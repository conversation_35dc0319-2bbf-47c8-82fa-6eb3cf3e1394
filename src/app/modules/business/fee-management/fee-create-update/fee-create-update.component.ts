import { <PERSON>mpo<PERSON>, HostListener, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  CONFIGURATION_FEE_TYPE_MAP,
  ENTITY_STATUS,
  TRANCSACTION_FEE_CONST,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IFee } from '@shared/models/IFee.model';
import { IMerchantTopup } from '@shared/models/merchant-topup.model';
import { TransactionFeeTypeSearch } from '@shared/models/request/transaction-fee-type.search';
import { ITransactionFeeType } from '@shared/models/transaction-fee-type.model';
import { FeeService } from '@shared/services/fee.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { MerchantService } from '@shared/services/merchant.service';
import { TransactionFeeTypeService } from '@shared/services/transaction-fee-type.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-fee-create-update',
  templateUrl: './fee-create-update.component.html',
  styleUrls: ['./fee-create-update.component.scss'],
})
export class FeeCreateUpdateComponent implements OnInit, OnDestroy {
  transactionFeeTypes: ITransactionFeeType[] = [];

  transactionFeeSearch: TransactionFeeTypeSearch = {};

  // form control data
  formFeeCreateUpdate: FormGroup = new FormGroup({});

  fee: IFee = {};

  feeId: number | undefined;

  isUpdate = false;

  isDetail = false;

  isTopup = false;

  action = '';

  hasFilter = false;

  configurationFeeType: string | undefined;

  transactionFeeTypeId: number | undefined;

  merchantTopup: IMerchantTopup[] = [];

  ENTITY_STATUS = ENTITY_STATUS;
  VALIDATORS = VALIDATORS;
  SYSTEM_RULES = SYSTEM_RULES;
  ROUTER_ACTIONS = ROUTER_ACTIONS;

  constructor(
    private router: Router,
    private formBuilder: FormBuilder,
    private routerActive: ActivatedRoute,
    private toastService: ToastrCustomService,
    private activatedRoute: ActivatedRoute,
    private feeService: FeeService,
    private merchantService: MerchantService,
    private translateService: TranslateService,
    private transactionFeeTypeService: TransactionFeeTypeService
  ) {
    this.activatedRoute.data.subscribe((res) => {
      this.action = res.action;
      if (this.action === ROUTER_ACTIONS.detail) {
        this.isDetail = true;
      }
      if (this.action === ROUTER_ACTIONS.update) {
        this.isUpdate = true;
      }
      if (this.action === ROUTER_ACTIONS.create) {
        this.isUpdate = false;
      }
    });
  }

  /**
   * remove storage
   */
  ngOnDestroy(): void {
    if (!this.hasFilter) {
      sessionStorage.removeItem(STORAGE_APP.FEE);
    }
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.hasFilter = true;
  }

  ngOnInit(): void {
    this.routerActive.paramMap.subscribe((res) => {
      const idParam = res.get('configurationFeeType');
      if (idParam) {
        this.configurationFeeType = idParam + '';
      }
    });

    this.routerActive.paramMap.subscribe((res) => {
      const idParam = res.get('feeId');
      if (idParam) {
        this.feeId = +idParam;
        this.isUpdate = true;
      }
    });

    this.initForm();
    if (this.isUpdate) {
      this.getDetail();
    }

    this.getTransactionFeeType();
  }

  getConfigurationFeeType(configurationType: any) {
    if (configurationType) {
      return CONFIGURATION_FEE_TYPE_MAP[configurationType];
    }
    return '';
  }

  /**
   * Update: check validate file and data input if valid then call api create
   */
  onUpdate() {
    // touched form
    if (this.formFeeCreateUpdate.invalid) {
      CommonUtils.markFormGroupTouched(this.formFeeCreateUpdate);
      return;
    }
    const data = this.formFeeCreateUpdate.getRawValue();

    if (this.formFeeCreateUpdate.valid) {
      this.feeService.update(data).subscribe((res): void => {
        this.router.navigate([ROUTER_UTILS.fee.root]);
        this.toastService.success('common.action.updateSuccess');
      });
    }
  }

  onCreate() {
    // touched form
    if (this.formFeeCreateUpdate.invalid) {
      CommonUtils.markFormGroupTouched(this.formFeeCreateUpdate);
      return;
    }
    const data = this.formFeeCreateUpdate.getRawValue();

    this.feeService.create(data).subscribe((res): void => {
      this.router.navigate([ROUTER_UTILS.fee.root]);
      this.toastService.success('common.action.createSuccess');
    });
  }

  /**
   * init form
   *
   * @param fee IFee
   */
  initForm(fee?: IFee): void {
    this.formFeeCreateUpdate = this.formBuilder.group({
      feeId: [fee?.feeId || null],
      feeName: [
        fee?.feeName || '',
        [
          Validators.required,
          Validators.minLength(VALIDATORS.LENGTH.FEE_RATE_NAME_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
        ],
      ],
      createdBy: [fee?.createdBy || ''],
      createdDate: [fee?.createDateTime || ''],
      configurationFeeType: [
        fee?.configurationFeeType || this.configurationFeeType,
      ],
      transactionFeeTypeId: [
        fee?.transactionFeeTypeId || null,
        [Validators.required],
      ],
      status: [fee?.status || 1, [Validators.required]],
      feeType: [
        fee?.feeType || this.translateService.instant('fee.feeTypeDefault'),
      ],
      createDateTime: [fee?.createDateTime || null],
      merchantId: [fee?.merchantId || null],
    });
  }

  patchValueToForm(fee: IFee) {
    if (fee?.transactionFeeTypeId === TRANCSACTION_FEE_CONST.TOPUP.value) {
      this.merchantService.getMerchantList().subscribe((res: any) => {
        this.merchantTopup = res.body;
        this.formFeeCreateUpdate.patchValue({
          feeId: fee.feeId,
          feeName: fee?.feeName,
          createdBy: fee?.createdBy || '',
          createdDate: CommonUtils.reverseDate(fee.createDateTime + ''),
          configurationFeeType:
            fee?.configurationFeeType || this.configurationFeeType,
          transactionFeeTypeId: fee?.transactionFeeTypeId,
          status: fee?.status || 0,
          feeType:
            fee?.feeType || this.translateService.instant('fee.feeTypeDefault'),
          createDateTime: fee?.createDateTime || null,
          merchantId: fee?.merchantId || null,
        });
      });
    } else {
      this.merchantTopup = [];
      this.formFeeCreateUpdate.patchValue({
        feeId: fee.feeId,
        feeName: fee?.feeName,
        createdBy: fee?.createdBy || '',
        createdDate: CommonUtils.reverseDate(fee.createDateTime + ''),
        configurationFeeType:
          fee?.configurationFeeType || this.configurationFeeType,
        transactionFeeTypeId: fee?.transactionFeeTypeId,
        status: fee?.status || 0,
        feeType:
          fee?.feeType || this.translateService.instant('fee.feeTypeDefault'),
        createDateTime: fee?.createDateTime || null,
        merchantId: fee?.merchantId || null,
      });
    }
  }

  /**
   * get detail event
   */
  getDetail(): void {
    this.feeService.detail({ id: this.feeId }).subscribe((res: any) => {
      this.fee = res.body;
      const data = res.body || undefined;
      this.configurationFeeType = this.fee.configurationFeeType;
      this.transactionFeeTypeId = this.fee.transactionFeeTypeId;
      this.patchValueToForm(this.fee);
      if (
        this.fee.transactionFeeTypeId === TRANCSACTION_FEE_CONST.TOPUP.value
      ) {
        this.isTopup = true;
      } else {
        this.isTopup = false;
      }
    });
  }

  /**
   * back to monitor list
   */
  backToList(): void {
    this.hasFilter = true;
    this.router.navigate([ROUTER_UTILS.fee.root]);
  }

  /**
   * get transaction fee type
   */
  getTransactionFeeType() {
    this.transactionFeeTypeService
      .searchAutoComplete(this.transactionFeeSearch)
      .subscribe((res: any) => {
        if (res.ok) {
          this.transactionFeeTypes = res.body?.content;
        }
      });
  }

  getTransactionFee(event: any): void {
    this.transactionFeeTypeId = event;

    if (event === TRANCSACTION_FEE_CONST.TOPUP.value) {
      this.isTopup = true;
      this.merchantService.getMerchantList().subscribe((res: any) => {
        this.merchantTopup = res.body;
      });
      this.formFeeCreateUpdate.controls.merchantId.setValidators([
        Validators.required,
      ]);
      this.formFeeCreateUpdate.controls.merchantId.updateValueAndValidity();
    } else {
      this.isTopup = false;
      this.merchantTopup = [];
      this.formFeeCreateUpdate.controls.merchantId.reset();
      this.formFeeCreateUpdate.controls.merchantId.clearValidators();
      this.formFeeCreateUpdate.controls.merchantId.updateValueAndValidity();
    }
  }

  checkMerchantInvalid(): void {
    if (this.fee.transactionFeeTypeId === TRANCSACTION_FEE_CONST.TOPUP.value) {
      this.formFeeCreateUpdate.controls.merchantId.setValidators([
        Validators.required,
      ]);
      this.formFeeCreateUpdate.controls.merchantId.updateValueAndValidity();
    } else {
      this.formFeeCreateUpdate.controls.merchantId.clearValidators();
      this.formFeeCreateUpdate.controls.merchantId.updateValueAndValidity();
    }
  }
}
