<div class="modal-content">
  <div class="modal-header">
    <h5 class="modal-title">
      {{ "common.action.uploadFiles" | translate }}
    </h5>
    <h5 class="modal-title"></h5>
    <button
      type="button"
      class="close"
      data-dismiss="modal"
      aria-label="Close"
      (click)="activeModal.close()"
    >
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="formImport">
      <div class="col-md-12 mb-2">
        <label class="mb-3">{{
          "common.action.download-template" | translate
        }}</label>
        <button
          class="btn btn-white download"
          type="button"
          (click)="downloadTemplateImport()"
        >
          <i class="fa fa-download"> </i>
          {{ "common.action.download" | translate }}
        </button>
        <br />
      </div>
      <div class="col-md-12 mb-2">
        <label>{{ "common.action.uploadFiles" | translate }}</label>
        <div class="row">
          <div class="file-upload">
            <label for="file-input">
              <i class="fas fa-cloud-upload-alt fa-style"></i
              >{{ "common.chooseFile" | translate }}
            </label>
            <div class="file-input-container">
              <input
                id="file-input"
                type="file"
                (change)="onFileSelected($event)"
                formControlName="uploadFile"
                accept=".xlsx, .xls"
              />
              <div id="file-name" [class.active]="selectedFileName">
                {{ selectedFileName }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="text-center pb-4">
    <button
      type="button"
      class="btn mb-btn-outline-color mr-3"
      (click)="activeModal.close()"
    >
      {{ "common.action.close" | translate }}
    </button>
    <ng-container>
      <button
        type="button"
        class="btn mb-btn-color"
        [disabled]="formImport.invalid"
        (click)="importFile()"
        *hasPrivileges="SYSTEM_RULES.REFERRAL_IMPORT"
      >
        {{ "common.action.uploadFile" | translate }}
      </button>
    </ng-container>
  </div>
</div>
