<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formDepartmentSearch" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-md-4 col-lg-3">
            <div class="form-group">
              <label>{{ "common.action.searchKeyword" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="keyword"
                class="w-100"
                class="form-control"
                placeholder="{{ 'common.action.searchKeyword' | placeholder }}"
              />
            </div>
          </div>
          <div class="col-md-4 col-lg-3">
            <div class="form-group">
              <label>{{ "common.status" | translate }}</label>
              <ng-select
                appearance="outline"
                [searchable]="false"
                placeholder="{{ 'common.appSelectOption.status' | translate }}"
                [clearable]="true"
                formControlName="status"
              >
                <ng-option
                  [value]="item.code"
                  *ngFor="let item of ENTITY_STATUS"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
            >
              <div class="btn-reset">
                <i
                  class="bi bi-arrow-clockwise"
                  type="button"
                  (click)="onReset()"
                >
                </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" (click)="onSearch()">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-3 mt-4">
          <button
            class="btn btn-red"
            type="button"
            (click)="onCreate()"
            *hasPrivileges="SYSTEM_RULES.DEPARTMENT_CREATE"
          >
            {{ "common.action.create" | translate }}
          </button>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th class="text-center" scope="col">
                {{ "common.no" | translate }}
              </th>
              <th scope="col">
                {{ "model.department.code" | translate }}
              </th>
              <th scope="col">
                {{ "model.department.name" | translate }}
              </th>
              <th scope="col">
                {{ "model.department.shortName" | translate }}
              </th>
              <th scope="col">
                {{ "model.department.description" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "model.department.status" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of departments; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td>
                <span title="{{ dataItem?.departmentCode }}">{{
                  dataItem?.departmentCode | limitWord
                }}</span>
              </td>
              <td>
                <span title="{{ dataItem?.departmentName }}">{{
                  dataItem?.departmentName | limitWord
                }}</span>
              </td>
              <td>
                <span title="{{ dataItem?.shortName }}">{{
                  dataItem?.shortName | limitWord
                }}</span>
              </td>
              <td>
                <span title="{{ dataItem?.description }}">{{
                  dataItem?.description | limitWord
                }}</span>
              </td>
              <td class="text-center">
                <span
                  class="badge"
                  [ngClass]="ENTITY_STATUS_MAP[dataItem.status || 0].style"
                  >{{
                    ENTITY_STATUS_MAP[dataItem.status || 0].label | translate
                  }}</span
                >
              </td>
              <td class="text-center">
                <button
                  ngbTooltip="{{ 'common.action.detail' | translate }}"
                  class="btn px-1 py-0"
                  (click)="onDetail(dataItem)"
                >
                  <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                </button>
                <button
                  ngbTooltip="{{ 'common.action.update' | translate }}"
                  class="btn px-1 py-0"
                  data-toggle="modal"
                  (click)="onEdit(dataItem)"
                  *hasPrivileges="SYSTEM_RULES.DEPARTMENT_WRITE"
                >
                  <i class="fa fa-edit mb-color" aria-hidden="true"></i>
                </button>
                <button
                  [ngbTooltip]="
                    (dataItem.status === ENTITY_STATUS_CONST.ACTIVE.code
                      ? 'common.action.lock'
                      : 'common.action.unlock'
                    ) | translate
                  "
                  class="btn px-1 py-0"
                  (click)="lockAndUnlock(dataItem)"
                  *hasPrivileges="
                    dataItem.status === ENTITY_STATUS_CONST.ACTIVE.code
                      ? SYSTEM_RULES.DEPARTMENT_LOCK
                      : SYSTEM_RULES.DEPARTMENT_UNLOCK
                  "
                >
                  <i
                    [className]="
                      dataItem.status === ENTITY_STATUS_CONST.ACTIVE.code
                        ? 'fa fa-lock mb-color'
                        : 'fa fa-unlock mb-color'
                    "
                    aria-hidden="true"
                  ></i>
                </button>

                <!--*ngIf="dataItem.approvalStatus === 1 && (dataItem.approvalType === 1 || dataItem.approvalType === 2)"-->
                <button
                  ngbTooltip="{{ 'common.action.delete' | translate }}"
                  class="btn px-1 py-0"
                  *hasPrivileges="SYSTEM_RULES.DEPARTMENT_DELETE"
                >
                  <i
                    class="fa fa-trash mb-color"
                    aria-hidden="true"
                    (click)="delete(dataItem)"
                  ></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        <div
          class="row d-block text-center m-0"
          *ngIf="departments?.length === 0"
        >
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
        <div *ngIf="departments.length">
          <mat-paginator
            [length]="formDepartmentSearch.value.length"
            [pageSize]="formDepartmentSearch.value.pageSize"
            [pageIndex]="formDepartmentSearch.value.pageIndex"
            [pageSizeOptions]="pageSizeOptions"
            (page)="onChangePage($event)"
            aria-label="Select page"
          >
          </mat-paginator>
        </div>
      </div>
    </div>
  </div>
</section>
