import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import {
  STORAGE_APP,
  STORAGE_TOKEN,
} from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ICaptcha } from '@shared/models/captcha.model';
import { AuthProviderService } from '@shared/services/auth/auth-provider.service';
import { AuthenticationService } from '@shared/services/auth/authentication.service';
import { CaptchaService } from '@shared/services/captcha.service';
import { encrypt, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import { LocalStorageService } from 'ngx-webstorage';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit {
  @ViewChild('username') usernameElement: ElementRef | undefined;
  @ViewChild('password') passwordElement: ElementRef | undefined;

  passwordVisible = false;
  captchaImageSource = '';
  transactionId = '';

  ROUTER_UTILS = ROUTER_UTILS;

  loginForm = this.fb.group({
    username: [
      '',
      [
        Validators.required,
        Validators.maxLength(VALIDATORS.LENGTH.USERNAME_MAX_LENGTH),
      ],
    ],
    password: [
      '',
      [
        Validators.required,
        Validators.maxLength(VALIDATORS.LENGTH.SMALL_TEXT_MAX_LENGTH),
      ],
    ],
    captcha: [''],
  });

  constructor(
    private authProviderService: AuthProviderService,
    private authenticationService: AuthenticationService,
    private fb: FormBuilder,
    private captchaService: CaptchaService,
    private localStorage: LocalStorageService,
    private router: Router
  ) {}

  ngOnInit(): void {}

  login() {
    const { username, password, captcha } = this.loginForm.value;

    if (this.loginForm.invalid) {
      CommonUtils.markFormGroupTouched(this.loginForm);
      if (username.toString().trim().length === 0) {
        this.usernameElement?.nativeElement.focus();
      } else if (password.toString().trim().length === 0) {
        this.passwordElement?.nativeElement.focus();
      }
      return;
    }

    const deviceToken = this.localStorage.retrieve(STORAGE_TOKEN.DEVICE_TOKEN);

    this.authProviderService
      .login({
        username,
        password: encrypt(password) + '',
        captcha,
        transactionId: this.transactionId,
        deviceToken,
      })
      .subscribe(
        (resp) => {
          if (resp?.changePwRequired) {
            this.router.navigate([ROUTER_UTILS.auth.changePasswordExpire]);
          } else {
            this.authenticationService.fetch().subscribe((res) => {
              this.localStorage.store(STORAGE_APP.USER_INFO, res.body);
              location.assign(ROUTER_UTILS.base.home);
            });
          }
        },
        (err) => {
          if (err?.error?.captchaRequired) {
            this.loginForm.controls['captcha'].setValidators(
              Validators.required
            );
            this.loginForm.controls['captcha'].setValue('');
            this.captchaImageSource = err.error?.captcha;
            this.transactionId = err.error?.transactionId;
          }
        }
      );
  }

  getCaptcha(): void {
    this.captchaService.getCaptcha().subscribe((rs) => {
      const captcha = rs?.body as ICaptcha;
      this.captchaImageSource = captcha?.captcha || '';
      this.transactionId = captcha?.transactionId || '';
    });
  }

  /**
   * Change type view password
   */
  onChangPasswordVisible(): void {
    this.passwordVisible = !this.passwordVisible;
  }
}
