import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  FILE_EXTENSION,
  MAX_DATE_CONST,
  MODAL_ACTION,
  PAGINATION,
  SCREEN_SHOW_BANNER,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { ICampaign } from '@shared/models/campaign.model';
import { CampaignService } from '@shared/services/campaign.service';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-campaign-management',
  templateUrl: './campaign-management.component.html',
  styleUrls: ['./campaign-management.component.scss'],
})
export class CampaignManagementComponent implements OnInit, OnDestroy {
  campaigns: ICampaign[] = [];

  data: any[] = [];

  maxDate = new Date();
  MAX_DATE_CONST = MAX_DATE_CONST;

  ROUTER_UTILS = ROUTER_UTILS;
  ENTITY_STATUS = ENTITY_STATUS;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  pageSizeOptions = PAGINATION.OPTIONS;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  SYSTEM_RULES = SYSTEM_RULES;
  SCREEN_SHOW_BANNER = SCREEN_SHOW_BANNER;
  FILE_EXTENSION = FILE_EXTENSION;

  // default form search
  formCampaignSearch = this.fb.group({
    status: null,
    campaignName: null,
    startDateFrom: null,
    startDateTo: null,
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    hasPageable: true,
    // previousPageIndex: 0,
  });

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private translateService: TranslateService,
    private modalService: ModalService,
    private toastService: ToastrCustomService,
    private campaignService: CampaignService,
    private ngb: NgbModal,
    private downloadService: DownloadService
  ) {}

  ngOnInit(): void {
    this.onSearch();
  }

  /**
   *
   * @param isUpdate
   * @param isDetail
   * @param departmentId
   * @returns
   */

  onChangePage(page: PageEvent): void {
    this.formCampaignSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formCampaignSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  onSearch(): void {
    const body = this.formCampaignSearch.value;
    this.campaignService.search(body).subscribe((res: any): void => {
      this.data = res.body.content;
      this.formCampaignSearch.controls.length.setValue(res.body.totalElements);
    });
  }

  ngOnDestroy(): void {
    this.modalService.dismissAll();
  }

  validateDate(): void {
    if (this.formCampaignSearch.controls['startDateTo'].value) {
      this.maxDate = this.formCampaignSearch.controls['startDateTo'].value;
    }
  }

  onReset(): void {
    // reset data
    // this.formCampaignSearch.reset();
    // this.formBankSearch.controls.status.setValue('');
    this.formCampaignSearch.controls.status.reset();
    this.formCampaignSearch.controls.campaignName.reset();
    this.formCampaignSearch.controls.startDateFrom.reset();
    this.formCampaignSearch.controls.startDateTo.reset();
  }

  detail(campaignId?: number): void {
    this.router.navigate([
      ROUTER_UTILS.sysManage.campaign.root,
      campaignId,
      ROUTER_ACTIONS.detail,
    ]);
  }

  update(campaignId?: number): void {
    this.router.navigate([
      ROUTER_UTILS.sysManage.campaign.root,
      campaignId,
      ROUTER_ACTIONS.update,
    ]);
  }

  onExport(): void {
    const formSearch = this.formCampaignSearch.value;
    if (this.formCampaignSearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formCampaignSearch);
      return;
    }
    formSearch.hasPageable = false;
    const fileName = this.translateService.instant('template.campaign');
    const obFile = this.campaignService.exportCampaign(formSearch);
    this.downloadService.downloadFileWithObservableAndName(
      obFile,
      fileName,
      FILE_EXTENSION.XLSX
    );
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formCampaignSearch.value.pageIndex,
      this.formCampaignSearch.value.pageSize
    );
  }

  lockAndUnlock(campaign: ICampaign): void {
    if (campaign.status === ENTITY_STATUS_CONST.INACTIVE.code) {
      this.unlock(campaign);
    } else {
      this.lock(campaign);
    }
  }

  lock(campaign: ICampaign): boolean {
    let rs = false;

    const modalData = {
      title: 'model.campaign.lockTitle',
      content: 'model.campaign.lockContent',
      interpolateParams: {
        campaignName: `<b>${campaign?.campaignName || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { campaignId: campaign.campaignId };
        this.campaignService.lock(params).subscribe((res) => {
          this.toastService.success('common.action.lockSuccess');
          this.onSearch();
          rs = true;
        });
      }
    });

    return rs;
  }

  delete(campaign: ICampaign): void {
    const modalData = {
      title: 'model.campaign.deleteTitle',
      content: 'model.campaign.deleteContent',
      interpolateParams: {
        campaignName: `<b>${campaign?.campaignName || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { campaignId: campaign.campaignId };
        this.campaignService.deleteCampaign(params).subscribe((res) => {
          this.toastService.success('common.action.deleteSuccess');
          this.onSearch();
        });
      }
    });
  }

  unlock(campaign: ICampaign): boolean {
    let rs = false;

    const modalData = {
      title: 'model.campaign.unlockTitle',
      content: 'model.campaign.unlockContent',
      interpolateParams: {
        campaignName: `<b>${campaign?.campaignName || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { campaignId: campaign.campaignId };
        this.campaignService.unlock(params).subscribe((res) => {
          this.toastService.success('common.action.unlockSuccess');
          this.onSearch();
          rs = true;
        });
      }
    });

    return rs;
  }
}
