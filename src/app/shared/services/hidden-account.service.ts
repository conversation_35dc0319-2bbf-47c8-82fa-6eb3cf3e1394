import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { INumberStructure } from '@shared/models/premium-account-number.modal';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { AbstractDomainService } from './common/abstract.domain.service';

@Injectable({
  providedIn: 'root',
})
export class HiddenAccountService extends AbstractDomainService<INumberStructure> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.HIDDEN_ACCOUNT;
}
