import { Directive, ElementRef, HostListener } from '@angular/core';

@Directive({
  selector: 'input[separator]',
})
export class SeperatorDirective {
  constructor(private _inputEl: ElementRef) {}

  @HostListener('input', ['$event'])
  onInput(event: any) {
    if (this._inputEl.nativeElement.value === '') {
      return;
    }
    const commasRemoved = this._inputEl.nativeElement.value
      .toString()
      .replace(/\D/g, '')
      .replace(/\B(?=(\d{3})+(?!\d))/g, '.');
    this._inputEl.nativeElement.value = commasRemoved;
  }
}
