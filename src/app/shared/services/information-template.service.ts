import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IInformationTemplate } from '@shared/models/information-template.model';
import { IInformationTemplateSearch } from '@shared/models/request/information-template.search';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({ providedIn: 'root' })
export class InformationTemplateService extends AbstractService {
  resourceUrl = `/information-template`;

  constructor(private httpClient: HttpClient) {
    super(httpClient);
  }

  /**
   *
   * @param request
   * @returns
   */
  search(
    request: IInformationTemplateSearch
  ): Observable<EntityResponseType<IInformationTemplate>> {
    return super.post<EntityResponseType<IInformationTemplate[]>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  /**
   *
   * @param request
   * @returns
   */
  searchByKeyWord(
    request: IInformationTemplateSearch
  ): Observable<EntityResponseType<IInformationTemplate>> {
    return super.post<EntityResponseType<IInformationTemplate[]>>(
      `${this.resourceUrl}/search-by-keyword`,
      request
    );
  }

  /**
   *
   * @param request
   * @returns
   */
  create(
    request: IInformationTemplate
  ): Observable<EntityResponseType<IInformationTemplate>> {
    return super.post<EntityResponseType<IInformationTemplate>>(
      `${this.resourceUrl}/create`,
      request
    );
  }

  /**
   *
   * @param request
   * @returns
   */
  unlock(
    request: IInformationTemplate
  ): Observable<EntityResponseType<boolean>> {
    return super.post<EntityResponseType<boolean>>(
      `${this.resourceUrl}/unlock`,
      request
    );
  }

  /**
   *
   * @param request
   * @returns
   */
  lock(request: IInformationTemplate): Observable<EntityResponseType<boolean>> {
    return super.post<EntityResponseType<boolean>>(
      `${this.resourceUrl}/lock`,
      request
    );
  }

  /**
   *
   * @param request
   * @returns
   */
  deleteDepartment(
    request: IInformationTemplate
  ): Observable<EntityResponseType<IInformationTemplate>> {
    return super.post<EntityResponseType<IInformationTemplate>>(
      `${this.resourceUrl}/delete`,
      request
    );
  }

  /**
   *
   * @param request
   * @returns
   */
  detail(
    request: IInformationTemplate
  ): Observable<EntityResponseType<IInformationTemplate>> {
    return super.post<IInformationTemplate>(
      `${this.resourceUrl}/detail`,
      request
    );
  }

  /**
   *
   * @param request
   * @returns
   */
  update(
    request: IInformationTemplate
  ): Observable<EntityResponseType<IInformationTemplate>> {
    return super.post<IInformationTemplate>(
      `${this.resourceUrl}/update`,
      request
    );
  }
}
