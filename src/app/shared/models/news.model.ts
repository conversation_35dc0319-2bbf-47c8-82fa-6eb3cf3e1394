export interface INews {
  newsId?: number;
  categoryId?: number;
  title?: string;
  content?: string;
  createDate?: string;
  status?: number;
  fromTime?: string;
  toTime?: string;
  newsContents?: any;
  iconUrL?: any;
  language?: string;
  newsContentId?: number;
  category?: any;
  length?: number;
  newsContent?: any;
  lastModifiedDate?: string;
  createdDate?: string;
}
export class News implements INews {
  constructor(
    public newsId?: number,
    public categoryId?: number,
    public title?: string,
    public content?: string,
    public createDate?: string,
    public status?: number,
    public fromTime?: string,
    public toTime?: string,
    public iconUrL?: any,
    public language?: string,
    public newsContentId?: number,
    public category?: any,
    public newsContents?: any,
    public length?: number,
    public newsContent?: any
  ) {}
}
