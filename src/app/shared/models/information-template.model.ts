export interface IInformationTemplate {
  informationTemplateId?: number;
  informationTemplateName?: string;
  informationTemplateCode?: string;
  displayName?: string;
  createdBy?: string;
  createdDate?: string;
  status?: number;
  template?: string;
  createdByFullname?: string;
  lastModifiedByFullname?: string;
  lastModifiedBy?: string;
  lastModifiedDate?: string;
  informationTemplateContentId?: number;
  language?: string;
  content?: string;
  informationTemplateContents?: IInformationTemplateContent;
  informationTemplateContent?: IInformationTemplateContent;
}

export interface IInformationTemplateContent {
  informationTemplateContentId?: number;
  informationTemplateId?: number;
  language?: string;
  content?: string;
  createdBy?: string;
  createdDate?: string;
  lastModifiedBy?: string;
  lastModifiedDate?: string;
  informationTemplateName?: string;
  informationTemplateCode?: string;
  displayName?: string;
}
