.table-customer {
    min-width: 1440px;
}

.award-icon {
    color: var(--mb-color);
    font-size: 24px;
}

.input-row {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.array-input {
    margin-top: 30px;
}

:host ::ng-deep .custom-display-select .ng-dropdown-panel.ng-select-bottom {
    display: none;
}

:host ::ng-deep .custom-display-select .ng-dropdown-panel.ng-select-bottom .ng-dropdown-panel-items .ng-option:last-child {
    display: none;
}

:host ::ng-deep .custom-display-select .ng-dropdown-panel .scroll-host {
    display: none;
}

:host ::ng-deep .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value {
    height: 40px;
    align-content: center;
    border-radius: 10px;
}

.item-hidden {
    color: #CECECE;
}

.item-special {
    color: #FF9900;
}

.item-blue {
    color: var(--mb-color);
    cursor: pointer;
}

:host .bi-chevron-double-right {
    margin-left: 15%;
}