input[type="checkbox"] {
  display: none;
}
:host {
  display: block;
  position: relative;
  width: 170px;
  height: 36px;
}

.toggle-button-switch {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 86px;
  height: 36px;
  cursor: pointer;
}
.toggle-button-text {
  overflow: hidden;
  border-radius: 9px;
  transition: background-color 0.3s;
  box-shadow: 0px 0px 3.5px 0px #00000040 inset;
  background: #f3f3f3;
  padding: 3px;
}

.toggle-button-text-on,
.toggle-button-text-off {
  float: left;
  width: 50%;
  height: 100%;
  line-height: 35px;
  color: #575758;
  text-align: center;
}

input[type="checkbox"]:checked ~ .toggle-button-switch {
  left: 82px;
  cursor: pointer;
}
.active {
  background: linear-gradient(172.44deg, #3641ff 11.52%, #141ed2 81.46%);
  border-radius: 6px;
  color: white;
  box-shadow: 4px 0px 3px 0px #00000033;
}
