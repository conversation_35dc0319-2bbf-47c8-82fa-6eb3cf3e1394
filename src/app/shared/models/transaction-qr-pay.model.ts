export interface ITransactionQrPay {
  ewalletTransferId?: number;
  accountNumber?: string;
  merchantService?: string;
  amount?: string;
  bankAccount?: string;
  bankTransId?: string;
  content?: string;
  transId?: string;
  transDate?: string;
  transactionStatus?: string;
  errorCode?: string;
  errorDetail?: string;
  partnerId?: string;
  status?: number;
  createdDate?: string;
  lastModifiedDate?: string;
  createdDateStr?: string;
  fromDate?: string;
  toDate?: string;
  keyword?: string;
  hasPageable?: boolean;
}

export class TransactionQrPay implements ITransactionQrPay {
  constructor(data: ITransactionQrPay) {
    Object.assign(this, data);
  }
}
