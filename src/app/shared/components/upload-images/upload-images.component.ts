import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { MAX_FILE_SIZE_CONST } from '@shared/constants/app.constants';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ImageViewerService } from '@shared/services/image-viewer.service';
import { ROUTER_ACTIONS } from '@shared/utils';
import { NzImagePreviewRef } from 'ng-zorro-antd/image';
import { NzUploadFile } from 'ng-zorro-antd/upload';
import { of } from 'rxjs';

export type Image = {
  src: string | null;
  name: string;
  file?: File;
};

@Component({
  selector: 'app-upload-images',
  templateUrl: './upload-images.component.html',
  styleUrls: ['./upload-images.component.scss'],
})
export class UploadImagesComponent implements OnInit, OnChanges {
  @Input() fileRequired: string[] = [];
  @Input() checkDisabled = false;
  @Input() fileExtension: string[] = [];
  @Input() imageUrls: any[] = [];
  @Input() type: any;

  @Output() imageUploaded: EventEmitter<any> = new EventEmitter();
  @Output() imageRemoved: EventEmitter<any> = new EventEmitter();

  @ViewChild(NzImagePreviewRef) imagePreviewRef!: NzImagePreviewRef;

  // fileList: any[] = [];

  getFileList: any[] = [];

  showUploadList = {
    showPreviewIcon: false,
    showRemoveIcon: false,
  };

  constructor(
    private imagePreviewService: ImageViewerService,
    private toast: ToastrCustomService
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (
      this.type === ROUTER_ACTIONS.create ||
      this.type === ROUTER_ACTIONS.update
    ) {
      if (
        this.fileRequired &&
        this.fileRequired.length > 0 &&
        this.imageUrls.length < this.fileRequired.length
      ) {
        for (const item of this.fileRequired) {
          this.imageUrls.push({
            src: null,
            name: item,
          });
        }
      }
    }
  }

  ngOnInit(): void {}

  // for test
  beforeUpload = (file: NzUploadFile, _fileList: NzUploadFile[]): boolean => {
    const isLessThan = file.size! < MAX_FILE_SIZE_CONST['10MB'].BYTE;
    if (!isLessThan) {
      this.toast.error({
        message: 'common.maxFileSize',
        params: { size: MAX_FILE_SIZE_CONST['10MB'].MB },
      });
      return false;
    }
    if (!this.fileExtension?.includes(file?.type || '')) {
      this.toast.error({
        message: 'common.fileUploadInvalid',
      });
      return false;
    }
    return true;
  };

  handlePreview = (event: any, index: number) => {
    event.stopPropagation();
    const rs: any[] = [];

    for (let i = 0; i < this.imageUrls.length; i++) {
      if (i === index) {
        rs.push(this.imageUrls[i].src);
      }
    }
    // for (const item of this.fileList) {
    //   if (item.src) {
    //     rs.push(item.src);
    //   }
    // }

    this.imagePreviewService.previewImages(rs);
  };

  onRemovePic(event: any, index: number) {
    event.stopPropagation();
    if (this.type !== ROUTER_ACTIONS.create) {
      this.imageRemoved.emit(this.imageUrls[index]);
    }

    this.imageUrls[index].file = undefined;
    this.imageUrls[index].name = null;
    this.imageUrls[index].src = null;
    this.imageUrls[index].id = null;
  }

  // for test
  handleChange(info: { file: NzUploadFile }, index: number): void {
    const fileReader: FileReader = new FileReader();

    fileReader.onload = (ev: ProgressEvent<FileReader>) => {
      const rs = ev.target?.result ? (ev.target.result as string) : '';

      this.imageUrls[index].src = rs;
    };

    if (info.file.originFileObj) {
      fileReader.readAsDataURL(info.file.originFileObj);
      this.imageUrls[index].file = info.file.originFileObj;
    } else {
      fileReader.readAsDataURL(info.file as any);
      this.imageUrls[index].file = info.file as any;
    }

    this.imageUploaded.emit(this.imageUrls);
  }

  handleCustomUploadReq = (item: any) => {
    return of(item).subscribe(
      (next) => {},
      (err) => {
        console.log(err);
      }
    );
  };
}
