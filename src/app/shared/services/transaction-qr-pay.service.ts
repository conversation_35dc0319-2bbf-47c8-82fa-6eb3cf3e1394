import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { ITransactionQrPay } from '@shared/models/transaction-qr-pay.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { Observable } from 'rxjs';
import { AbstractDomainService } from './common/abstract.domain.service';

@Injectable({
  providedIn: 'root',
})
export class TransactionQrPayService extends AbstractDomainService<ITransactionQrPay> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.TRANSACTION_QR_PAY;

  export(request: ITransactionQrPay): Observable<any> {
    return super.postFile(`/export`, request);
  }
}
