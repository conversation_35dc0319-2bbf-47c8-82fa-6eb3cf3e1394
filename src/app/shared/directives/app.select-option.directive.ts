import { Directive, ElementRef, EventEmitter, Output } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

/**
 * Add default option to select
 */
@Directive({
  selector: '[appSelectOption]',
})
export class AppSelectOptionDirective {
  @Output() clickOutside = new EventEmitter<void>();

  constructor(
    private elementRef: ElementRef,
    private translateService: TranslateService
  ) {
    this.elementRef.nativeElement.innerHTML =
      '<option value="">' +
      this.translateService.instant(`common.appSelectOption.select`) +
      '</option>';
  }
}
