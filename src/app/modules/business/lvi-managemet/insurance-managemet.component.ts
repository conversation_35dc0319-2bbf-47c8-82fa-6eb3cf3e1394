import { Component, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { TranslateService } from '@ngx-translate/core';
import {
  FILE_EXTENSION,
  MOMENT_CONST,
  PACKAGE_INSURANCE_HEALTH_MAP,
  PACKAGE_INSURANCE_VEHICLE_MAP,
  PAGINATION,
  PRODUCT_TYPE_VALUE,
  PRODUCT_TYPE_VALUE_CONST,
  VEHICLE_TYPE_MAP,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { LANGUAGES } from '@shared/constants/language.constants';
import { STORAGE_LANGUAGES } from '@shared/constants/storage.constants';
import {
  IHealthPackage,
  IInsurance,
  IVehiclePackage,
  IVehicleType,
} from '@shared/models/insurance.model';
import { DownloadService } from '@shared/services/helpers/download.service';
import { InsuranceService } from '@shared/services/insurance.service';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';

@Component({
  selector: 'app-lvi-managemet',
  templateUrl: './insurance-managemet.component.html',
  styleUrls: ['./insurance-managemet.component.scss'],
})
export class InsuranceManagemetComponent implements OnInit {
  formSearch: FormGroup = new FormGroup({});
  vehiclePackage: IVehiclePackage[] = [];
  vehicleType: IVehicleType[] = [];
  healthPackage: IHealthPackage[] = [];
  insuranceData: IInsurance[] = [];
  insuranceType: any = [];
  maxDate = new Date();
  local_lang: any = '';

  pageSizeOptions = PAGINATION.OPTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  LANGUAGES = LANGUAGES;
  MOMENT_CONST = MOMENT_CONST;
  PRODUCT_TYPE_VALUE_CONST = PRODUCT_TYPE_VALUE_CONST;
  PRODUCT_TYPE_VALUE = PRODUCT_TYPE_VALUE;
  VEHICLE_TYPE_MAP = VEHICLE_TYPE_MAP;
  PACKAGE_INSURANCE_HEALTH_MAP = PACKAGE_INSURANCE_HEALTH_MAP;
  PACKAGE_INSURANCE_VEHICLE_MAP = PACKAGE_INSURANCE_VEHICLE_MAP;

  constructor(
    private fb: FormBuilder,
    private insuranceService: InsuranceService,
    private downloadService: DownloadService,
    private translate: TranslateService
  ) {
    const endDate = moment(new Date()).format(MOMENT_CONST.FORMAT_DEFAULT);
    const date = new Date();
    const startDate = moment(date.setFullYear(date.getFullYear() - 1)).format(
      MOMENT_CONST.FORMAT_DEFAULT
    );
    this.formSearch = this.fb.group({
      keyword: '',
      status: null,
      fromDate: [
        CommonUtils.reverseDate(startDate),
        [Validators.required, this.fromDateInvalid],
      ],
      toDate: [
        CommonUtils.reverseDate(endDate),
        [Validators.required, this.toDateInvalid],
      ],
      pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
      pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
      length: 0,
      insuranceType: null,
      vehicleCode: null,
      packageCode: null,
      hasPageable: true,
    });
    this.formSearch.get('vehicleCode')?.disable();
    this.formSearch.get('packageCode')?.disable();
    this.insuranceService.getInsuranceType().subscribe((res) => {
      this.insuranceType = res.body;
    });
    const localFromLocal: any = localStorage.getItem(STORAGE_LANGUAGES);
    this.local_lang = JSON.parse(localFromLocal);
  }

  ngOnInit(): void {
    this.onSearch();
  }

  onSelectType(event: any) {
    if (event === PRODUCT_TYPE_VALUE_CONST.HEALTH.value) {
      this.formSearch.controls.packageCode.reset();
      this.formSearch.controls.vehicleCode.reset();
      this.vehiclePackage = [];
      this.vehicleType = [];
      this.insuranceService.getHealthPackage().subscribe((res: any) => {
        this.healthPackage = res.body.healthPackages;
        this.formSearch.get('packageCode')?.enable();
        this.formSearch.get('vehicleCode')?.disable();
      });
    }
    if (event === PRODUCT_TYPE_VALUE_CONST.VEHICLE.value) {
      this.formSearch.controls.packageCode.reset();
      this.formSearch.controls.vehicleCode.reset();
      this.healthPackage = [];
      this.insuranceService.getVehiclePackage().subscribe((res: any) => {
        this.vehiclePackage = res.body.vehiclePackages;
      });
      this.insuranceService.getVehicleType().subscribe((res: any) => {
        this.vehicleType = res.body.vehicleTypes;
        this.formSearch.get('packageCode')?.enable();
        this.formSearch.get('vehicleCode')?.enable();
      });
    }
    if (!event) {
      this.healthPackage = [];
      this.vehiclePackage = [];
      this.vehicleType = [];
      this.formSearch.get('packageCode')?.disable();
      this.formSearch.get('vehicleCode')?.disable();
      this.formSearch.controls.packageCode.reset();
      this.formSearch.controls.vehicleCode.reset();
    }
  }

  onsearchSubmit() {
    this.formSearch.controls.pageIndex.setValue(PAGINATION.PAGE_NUMBER_DEFAULT);
    this.formSearch.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    this.onSearch();
  }

  onSearch() {
    const params = this.formSearch.value;
    if (this.formSearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formSearch);
      return;
    }
    this.insuranceService.searchInsurance(params).subscribe((res: any) => {
      this.insuranceData = res.body.content;
      this.formSearch.controls.length.setValue(res.body.totalElements);
    });
  }

  exportFile() {
    const params = this.formSearch.value;
    if (this.formSearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formSearch);
      return;
    }
    const obFile = this.insuranceService.exportInsurance(params);
    const fileName = this.translate.instant('template.insurance');
    this.downloadService.downloadFileWithObservableAndName(
      obFile,
      fileName,
      FILE_EXTENSION.XLSX
    );
  }

  onReset() {
    this.formSearch.controls.keyword.reset();
    this.formSearch.controls.insuranceType.reset();
    this.formSearch.controls.packageCode.reset();
    this.formSearch.controls.vehicleCode.reset();
    const endDate = moment(new Date()).format(MOMENT_CONST.FORMAT_DEFAULT);
    const date = new Date();
    const startDate = moment(date.setFullYear(date.getFullYear() - 1)).format(
      MOMENT_CONST.FORMAT_DEFAULT
    );
    this.formSearch.controls.fromDate.setValue(
      CommonUtils.reverseDate(startDate)
    );
    this.formSearch.controls.fromDate.updateValueAndValidity();
    this.formSearch.controls.toDate.setValue(CommonUtils.reverseDate(endDate));
    this.formSearch.controls.toDate.updateValueAndValidity();
    this.vehiclePackage = [];
    this.healthPackage = [];
    this.vehicleType = [];
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formSearch.value.pageIndex,
      this.formSearch.value.pageSize
    );
  }

  onChangePage(page: PageEvent) {
    this.formSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  fromDateInvalid = (control: AbstractControl): ValidationErrors | null => {
    if (control?.value) {
      if (new Date(control.value) > new Date()) {
        return { invalidFromDateCurrent: true };
      }
    }
    return null;
  };

  toDateInvalid = (control: AbstractControl): ValidationErrors | null => {
    const fromDate = this.formSearch.get('fromDate')?.value;
    if (fromDate && control.value) {
      if (new Date(fromDate) > new Date(control.value)) {
        return { invalidToDate: true };
      }
      if (new Date(control.value) > new Date()) {
        return { invalidToDateCurrent: true };
      }
    }
    if (!fromDate && control.value) {
      if (new Date(control.value) > new Date()) {
        return { invalidToDateCurrent: true };
      }
    }
    return null;
  };
}
