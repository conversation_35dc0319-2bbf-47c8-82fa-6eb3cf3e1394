import { Component, OnInit } from '@angular/core';
import {
  <PERSON>bstractControl,
  FormArray,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import {
  CONFIG_AUTO_REPORT_CONST,
  CONFIG_AUTO_TYPE_CONST,
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  MOMENT_CONST,
  TRANSACTION_ENTITY_STATUS,
  TRANSACTION_UMONEY_ENTITY_TYPE,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { SELECTED_CONST } from '@shared/constants/user.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IConfigAuto } from '@shared/models/config-auto-transaction.model';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { UmoneyLapnetService } from '@shared/services/umoney-lapnet.service';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';

@Component({
  selector: 'app-config-auto-umoney-lapnet',
  templateUrl: './config-auto-report-umoney-lapnet.component.html',
  styleUrls: ['./config-auto-report-umoney-lapnet.component.scss'],
})
export class ConFigAutoReportUmoneyLapnetComponent implements OnInit {
  SYSTEM_RULES = SYSTEM_RULES;
  ENTITY_STATUS = ENTITY_STATUS;
  TRANSACTION_ENTITY_STATUS = TRANSACTION_ENTITY_STATUS;
  TRANSACTION_UMONEY_ENTITY_TYPE = TRANSACTION_UMONEY_ENTITY_TYPE;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  SELECTED_CONST = SELECTED_CONST;
  CONFIG_AUTO_REPORT_CONST = CONFIG_AUTO_REPORT_CONST;
  VALIDATORS = VALIDATORS;
  formCreate: FormGroup = new FormGroup({});
  emails: FormArray = new FormArray([]);
  isShowDeleteMail: boolean[] = [];
  data: IConfigAuto = {};
  isDataAvailable: boolean = false;

  constructor(
    private fb: FormBuilder,
    public activeModal: NgbActiveModal,
    private configAutoService: UmoneyLapnetService,
    private toastService: ToastrCustomService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.configAutoService
      .info({ type: CONFIG_AUTO_TYPE_CONST.TYPE_UMONEY_ON_BEHALF })
      .subscribe((res: any) => {
        if (res.body !== null) {
          this.data = res.body;
          this.initForm(res.body);
          this.getMail(res.body);
          this.isDataAvailable = true;
        } else {
          this.addEmail();
          this.isShowDeleteMail[CONFIG_AUTO_REPORT_CONST.MAIL_DEFAULT] = false;
        }
      });
  }

  initForm(data?: IConfigAuto) {
    this.formCreate = this.fb.group({
      title: [data?.title || '', [Validators.required]],
      content: [data?.content || '', [Validators.required]],
      status: [
        data?.status || ENTITY_STATUS_CONST.INACTIVE.code,
        [Validators.required],
      ],
      sendTime: [data ? this.getTime(data) : null, [Validators.required]],
      emails: new FormArray([], this.duplicateEmailValidator),
      type: CONFIG_AUTO_TYPE_CONST.TYPE_UMONEY_ON_BEHALF,
      configureAutomaticReportId: data?.configureAutomaticReportId,
    });
  }

  duplicateEmailValidator: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (!control || !(control instanceof Array || control instanceof Object))
      return null;

    const formArray = control as any;
    if (!formArray || formArray.length === 0) return null;

    const emailList: string[] = formArray.controls
      .map((group: FormGroup) => group.controls.email?.value?.trim())
      .filter((email: string) => !!email);

    const duplicates = emailList.filter(
      (email, index, self) => self.indexOf(email) !== index
    );

    formArray.controls.forEach((group: FormGroup) => {
      const email = group.controls.email.value?.trim();
      if (duplicates.includes(email)) {
        group.controls.email?.setErrors({
          ...group.controls.email?.errors,
          duplicate: true,
        });
      } else {
        const errors = { ...group.controls.email?.errors };
        if (errors?.duplicate) {
          delete errors.duplicate;
          group.controls.email?.setErrors(
            Object.keys(errors).length ? errors : null
          );
        }
      }
    });

    return null;
  };

  getMail(data?: IConfigAuto) {
    this.emails = this.formCreate.get('emails') as FormArray;
    data?.emailRecipients.forEach((item: any) => {
      this.emails.push(this.createEmail(item.email));
    });
    if (
      data?.emailRecipients.length ===
      CONFIG_AUTO_REPORT_CONST.MAIL_DEFAULT_LENGTH
    ) {
      this.isShowDeleteMail[CONFIG_AUTO_REPORT_CONST.MAIL_DEFAULT] = false;
    } else {
      this.onShowDeleteMail();
    }
  }

  getTime(data?: IConfigAuto) {
    const [hours, minutes, seconds] = data?.sendTime.split(':');
    return moment(
      new Date(
        new Date().getDay(),
        new Date().getMonth(),
        new Date().getDay(),
        +hours,
        +minutes,
        +seconds
      )
    ).format(MOMENT_CONST.DATE_TIME_MOMENT_FORMAT);
  }

  onShowDeleteMail() {
    this.emails.controls.forEach((item, i: number) => {
      this.isShowDeleteMail[i] = true;
    });
  }

  addEmail(): void {
    this.emails = this.formCreate.get('emails') as FormArray;
    this.emails.push(this.createEmail());
    this.onShowDeleteMail();
  }

  createEmail(mail?: any) {
    return this.fb.group({
      email: [
        mail || '',
        [Validators.pattern(VALIDATORS.PATTERN.EMAIL), Validators.required],
      ],
    });
  }

  onDeleteEmail(index: number) {
    this.emails.removeAt(index);
    this.isShowDeleteMail[CONFIG_AUTO_REPORT_CONST.MAIL_DEFAULT] =
      this.emails.controls.length ===
      CONFIG_AUTO_REPORT_CONST.MAIL_DEFAULT_LENGTH
        ? false
        : true;
  }

  sendMail() {
    if (this.formCreate.invalid) {
      CommonUtils.markFormGroupTouched(this.formCreate);
      return;
    }
    const emails: string[] = [];
    this.emails.controls.forEach((element) => {
      emails.push(element.value.email);
    });
    const body = {
      ...this.formCreate.value,
      sendTime: moment(this.formCreate.value.sendTime).format(
        MOMENT_CONST.FORMAT_TIME
      ),
      emails,
    };
    const serviceEvent =
      Object.keys(this.data).length === 0
        ? this.configAutoService.create(body)
        : this.configAutoService.update(body);
    serviceEvent.subscribe((res: any) => {
      this.toastService.success(
        this.data
          ? 'common.action.updateSuccess'
          : 'common.action.createSuccess'
      );
      this.activeModal.close();
    });
  }
}
