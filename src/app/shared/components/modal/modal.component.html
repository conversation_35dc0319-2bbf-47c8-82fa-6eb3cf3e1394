<div class="modal-header" [ngClass]="isHiddenBtnClose ? 'border-0' : ''">
  <button
    type="button"
    class="close"
    data-dismiss="modal"
    aria-label="Close"
    (click)="activeModal.close('close')"
  >
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body">
  <h3>{{ title | translate }}</h3>
  <p class="preventLongTextFix" [innerHTML]="showContent() | translate"></p>
</div>
<div class="text-center pb-5">
  <button
    type="button"
    [hidden]="isHiddenBtnClose"
    class="btn btn-white mr-3"
    (click)="activeModal.close('close')"
  >
    {{ "common.action.close" | translate }}
  </button>
  <button
    type="button"
    class="btn btn-red"
    (click)="activeModal.close('confirm')"
  >
    {{ confirmButton | translate }}
  </button>
</div>
