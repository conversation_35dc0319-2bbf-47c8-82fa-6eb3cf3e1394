import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import {
  ISearch,
  ISearchWithPagination,
} from '@shared/models/base/base-request.model';
import { Observable } from 'rxjs';
import { EntityResponseType } from './abstract.service';
import { BaseService } from './base.service';

@Injectable({
  providedIn: 'root',
})
export abstract class AbstractDomainService<T> extends BaseService {
  /**
   * abstract search
   *
   * @returns HttpResponse<T>
   */
  search(
    params: ISearchWithPagination,
    loading = true
  ): Observable<EntityResponseType<T[]>> {
    return super.post<T[]>(RESOURCE_CONST.COMMON.API.SEARCH, params, loading);
  }

  /**
   * abstract search
   *
   * @returns HttpResponse<T>
   */
  searchByKeyWord(
    params: ISearchWithPagination,
    loading = true
  ): Observable<EntityResponseType<T[]>> {
    return super.post<T[]>(
      RESOURCE_CONST.COMMON.API.SEARCH_BY_KEYWORD,
      params,
      loading
    );
  }

  /**
   * abstract search auto complete
   *
   * @returns HttpResponse<T>
   */
  searchAutoComplete(
    search: ISearch | any
  ): Observable<EntityResponseType<T[]>> {
    return super.post<T[]>(
      RESOURCE_CONST.COMMON.API.SEARCH_AUTO_COMPLETE,
      search,
      { loading: false }
    );
  }

  /**
   * abstract find all
   *
   * @returns HttpResponse<T>
   */
  findAll(): Observable<EntityResponseType<T[]>> {
    return super.post<T[]>(RESOURCE_CONST.COMMON.API.FIND_ALL);
  }

  /**
   * abstract create
   *
   * @param data any
   * @returns Observable<EntityResponseType<T>>
   */
  create(data: any): Observable<EntityResponseType<T>> {
    return super.post<T>(RESOURCE_CONST.COMMON.API.CREATE, data);
  }

  /**
   * abstract create
   *
   * @param data any
   * @returns Observable<EntityResponseType<T>>
   */
  detail(data: any): Observable<EntityResponseType<T>> {
    return super.post<T>(RESOURCE_CONST.COMMON.API.DETAIL, data);
  }

  /**
   * abstract update
   *
   * @param data any
   * @returns Observable<EntityResponseType<T>>
   */
  update(data: any): Observable<EntityResponseType<T>> {
    return super.post<T>(RESOURCE_CONST.COMMON.API.UPDATE, data);
  }

  /**
   * abstract active
   *
   * @param domainId number
   * @returns Observable<EntityResponseType<T>>
   */
  active(data: any): Observable<EntityResponseType<T>> {
    return super.post<T>(RESOURCE_CONST.COMMON.API.ACTIVE, data);
  }

  /**
   * abstract inactive
   *
   * @param domainId number
   * @returns Observable<EntityResponseType<T>>
   */
  inactive(data: any): Observable<EntityResponseType<T>> {
    return super.post<T>(RESOURCE_CONST.COMMON.API.INACTIVE, data);
  }

  /**
   * abstract active
   *
   * @param domainId number
   * @returns Observable<EntityResponseType<T>>
   */
  lock(data: any): Observable<EntityResponseType<T>> {
    return super.post<T>(RESOURCE_CONST.COMMON.API.LOCK, data);
  }

  /**
   * abstract inactive
   *
   * @param domainId number
   * @returns Observable<EntityResponseType<T>>
   */
  unlock(data: any): Observable<EntityResponseType<T>> {
    return super.post<T>(RESOURCE_CONST.COMMON.API.UNLOCK, data);
  }

  /**
   * abstract delete
   *
   * @param domainId number
   * @returns Observable<EntityResponseType<T>>
   */
  delete(data: any): Observable<EntityResponseType<T>> {
    return super.post<T>(RESOURCE_CONST.COMMON.API.DELETE, data);
  }
}
