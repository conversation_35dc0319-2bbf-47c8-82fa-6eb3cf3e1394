import { Injectable } from '@angular/core';
import { IPosition } from '@shared/models/position.model';
import { IPositionSearch } from '@shared/models/request/position.search';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';
@Injectable({
  providedIn: 'root',
})
export class PositionService extends AbstractService {
  public resourceUrl = '/position';

  /**
   * search
   *
   * @param request IPosition
   * @returns Observable<EntityResponseType<IPosition[]>>
   */
  search(
    request: IPositionSearch
  ): Observable<EntityResponseType<IPosition[]>> {
    return super.post<EntityResponseType<IPosition[]>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  /**
   * getAll
   *
   * @returns Observable<EntityResponseType<IPosition[]>>
   */
  getAll(): Observable<EntityResponseType<IPosition[]>> {
    return super.post(`${this.resourceUrl}/all`);
  }

  /**
   * API create
   *
   * @param request IPosition
   * @returns Observable<EntityResponseType<IPosition>>
   */
  create(request: IPosition): Observable<EntityResponseType<IPosition>> {
    return super.post<EntityResponseType<IPosition>>(
      `${this.resourceUrl}/create`,
      request
    );
  }

  /**
   * API update
   *
   * @param request IPosition
   * @returns Observable<EntityResponseType<IPosition>>
   */
  update(request: IPosition): Observable<EntityResponseType<IPosition>> {
    return super.post<EntityResponseType<IPosition>>(
      `${this.resourceUrl}/update`,
      request
    );
  }

  /**
   * API detail
   *
   * @param request IPosition
   * @returns Observable<EntityResponseType<ILoanOnline>>
   */
  detail(request: IPosition): Observable<EntityResponseType<IPosition>> {
    return super.post<EntityResponseType<IPosition>>(
      `${this.resourceUrl}/info`,
      request
    );
  }

  /**
   * API lock
   *
   * @param request IPosition
   * @returns Observable<EntityResponseType<IPosition>>
   */
  lock(request: IPosition): Observable<EntityResponseType<IPosition>> {
    return super.post<EntityResponseType<IPosition>>(
      `${this.resourceUrl}/lock`,
      request
    );
  }

  /**
   * API unlock
   *
   * @param request IPosition
   * @returns Observable<EntityResponseType<IPosition>>
   */
  unlock(request: IPosition): Observable<EntityResponseType<IPosition>> {
    return super.post<EntityResponseType<IPosition>>(
      `${this.resourceUrl}/unlock`,
      request
    );
  }

  /**
   * API delete
   *
   * @param request IPosition
   * @returns Observable<EntityResponseType<ILoanOnline>>
   */
  deletePosition(
    request: IPosition
  ): Observable<EntityResponseType<IPosition>> {
    return super.post<EntityResponseType<IPosition>>(
      `${this.resourceUrl}/delete`,
      request
    );
  }
}
