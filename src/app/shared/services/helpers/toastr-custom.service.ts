/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { IndividualConfig, ToastrService } from 'ngx-toastr';

@Injectable({ providedIn: 'root' })
export class ToastrCustomService {
  constructor(
    private toastr: ToastrService,
    private translate: TranslateService,
  ) { }

  show(
    message: string,
    title?: string,
    override?: Partial<IndividualConfig>,
    type?: string,
  ): void {
    this.translate.get(message).subscribe(result => {
      this.toastr.show(result, title, override, type);
    },
      () => {
        this.toastr.show(message, title, override, type);
      },
    );
  }

  success(
    message: string,
    title?: string,
    override?: Partial<IndividualConfig>,
  ): void {
    this.translate.get(message).subscribe(result => {
      this.toastr.success(result, title, override);
    },
      () => {
        this.toastr.success(message, title, override);
      },
    );

    // this.translate.get(message).subscribe({
    //   next: (result) => this.toastr.success(result, title, override),
    //   error: () => this.toastr.success(message, title, override)
    // });
  }

  errorCustom(
    message: string,
    param?: any,
    title?: string,
    override?: Partial<IndividualConfig>,
  ): void {
    this.translate.get(message, param).subscribe(result => {
      this.toastr.error(result, title, override);
    },
      () => {
        this.toastr.error(message, title, override);
      },
    );
  }

  successCustom(
    message: string,
    param?: any,
    title?: string,
    override?: Partial<IndividualConfig>,
  ): void {
    this.translate.get(message, param).subscribe(result => {
      this.toastr.success(result, title, override);
    },
      () => {
        this.toastr.success(message, title, override);
      },
    );
  }

  error(error: any, title?: string): void {
    const _error: any = {};
    if (typeof error === 'string') {
      _error.key = error;
    } else {
      _error.key = error.message;
      _error.interpolateParams = error.params;
    }
    this.translate.get(_error.key, _error.interpolateParams).subscribe(result => {
      this.toastr.error(result, title);
    },
      () => {
        this.toastr.error(_error.key, title);
      },
    );
  }

  info(
    message: string,
    title?: string,
    override?: Partial<IndividualConfig>,
  ): void {
    this.translate.get(message).subscribe(result => {
      this.toastr.info(result, title, override);
    },
      () => {
        this.toastr.info(message, title, override);
      },
    );
  }

  warning(
    message: string,
    title?: string,
    override?: Partial<IndividualConfig>,
  ): void {
    this.translate.get(message).subscribe(result => {
      this.toastr.warning(result, title, override);
    },
      () => {
        this.toastr.warning(message, title, override);
      },
    );
  }

  // @TODO: impl tiếp theo type error response của back end trả về
  handlerError(err: any): void {
    if (err && err.error) {
      const error = err.error;
      if (error.message) {
        this.error(error);
      } else if (error.errorKey && error.title) {
        this.error(err.error, err.title);
      } else {
        this.error('error.common');
      }
    } else {
      this.error('error.common');
    }
  }
}
