import { Component, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import {
  CURRENCY_CONST,
  DATE_CONSTANT,
  ENTITY_STATUS_CONST,
  INTERNATIONAL_TRANSACTION_TYPE,
  MODAL_ACTION,
  MOMENT_CONST,
  PAGINATION,
  SERVICE_TYPE,
  SERVICE_TYPE_CONST,
  SERVICE_TYPE_MAP,
  INTER_TRANSACTION_TYPE,
  INTERNAL_TRANSACTION_TYPE,
  OTHER_TRANSACTION_TYPE,
  TRANSACTION_SECTOR,
  TRANSACTION_SECTOR_CONST,
  TRANSACTION_SECTOR_MAP,
  TRANSACTION_TYPE,
  TRANSACTION_TYPE_CONST,
  TRANSACTION_TYPE_MAP,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ICurrency } from '@shared/models/currency.model';
import { MODEL_MAP_ITEM_COMMON } from '@shared/models/model.type';
import { ConfigTransactionService } from '@shared/services/config-transaction.service';
import { CurrencyService } from '@shared/services/currency.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';
@Component({
  selector: 'app-create-config',
  templateUrl: './create-config.component.html',
  styleUrls: ['./create-config.component.scss'],
})
export class CreateConfigComponent implements OnInit {
  formCreate: FormGroup = new FormGroup({});
  formArray: FormGroup = new FormGroup({});

  minDateStart = new Date();
  currentDate = new Date();
  minDateEnd = new Date();
  maxDateStart = '';
  currencies: ICurrency[] = [];
  currency = '';
  moneyDay = 0;
  arrayTransaction: any;
  startDateMin: any = '';

  isSector1890 = false;
  isSector1891 = false;
  isSector1740 = false;
  isSectorSelected = true;

  SECTOR_1890 = TRANSACTION_SECTOR_CONST.SECTOR_1890.value;
  SECTOR_1891 = TRANSACTION_SECTOR_CONST.SECTOR_1891.value;
  SECTOR_1740 = TRANSACTION_SECTOR_CONST.SECTOR_1740.value;
  pageIndex = PAGINATION.PAGE_NUMBER_DEFAULT;
  pageSize = PAGINATION.PAGE_SIZE_DEFAULT;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  VALIDATORS = VALIDATORS;
  SYSTEM_RULES = SYSTEM_RULES;
  SERVICE_TYPE_CONST = SERVICE_TYPE_CONST;
  SERVICE_TYPE = SERVICE_TYPE;
  SERVICE_TYPE_MAP = SERVICE_TYPE_MAP;
  TRANSACTION_TYPE_CONST = TRANSACTION_TYPE_CONST;
  TRANSACTION_TYPE = TRANSACTION_TYPE;
  INTERNATIONAL_TRANSACTION_TYPE = INTERNATIONAL_TRANSACTION_TYPE;
  INTERNAL_TRANSACTION_TYPE = INTERNAL_TRANSACTION_TYPE;
  OTHER_TRANSACTION_TYPE = OTHER_TRANSACTION_TYPE;
  INTER_TRANSACTION_TYPE = INTER_TRANSACTION_TYPE;
  TRANSACTION_TYPE_MAP = TRANSACTION_TYPE_MAP;
  TRANSACTION_SECTOR_CONST = TRANSACTION_SECTOR_CONST;
  CURRENCY_CONST = CURRENCY_CONST;
  TRANSACTION_SECTOR = TRANSACTION_SECTOR;
  TRANSACTION_SECTOR_MAP = TRANSACTION_SECTOR_MAP;
  actionConfirm!: MODEL_MAP_ITEM_COMMON;
  MODAL_ACTION = MODAL_ACTION;
  DATE_CONSTANT = DATE_CONSTANT;

  constructor(
    private fb: FormBuilder,
    private toastrService: ToastrCustomService,
    private router: Router,
    private configTransService: ConfigTransactionService,
    private currencyService: CurrencyService
  ) {
    this.formArray = this.fb.group({
      arrayForm: this.fb.array([...this.createLinkFormGroups()]),
    });
    this.validateTimeStart = this.validateTimeStart.bind(this);
    this.validateTimeEnd = this.validateTimeEnd.bind(this);
  }

  ngOnInit(): void {
    this.getCurrencies();
    this.initForm();
  }

  getCurrencies(): void {
    this.currencyService
      .search({
        hasPageable: false,
        status: Number(ENTITY_STATUS_CONST.ACTIVE.code),
      })
      .subscribe((res: any) => {
        if (res.body.content.length) {
          this.currencies = res.body.content;
        }
      });
  }

  initForm(): void {
    this.formCreate = this.fb.group({
      serviceName: [
        null,
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.NAME_NO_SPECIAL),
          Validators.pattern(VALIDATORS.PATTERN.NAME_NO_ASCII),
        ],
      ],
      sectorId: [null, [Validators.required]],
      currency: [null, [Validators.required]],
      maxTransferAmtOfDay: [null, [this.moneyDayInvalid]],
      maxTransferAmtOfMonth: [null, [this.moneyMonthInvalid]],
      maxTransferAmtOfYear: [null, [this.moneyYearInvalid]],
      startDate: [null, [Validators.required, this.validateTimeStart]],
      endDate: [null, [Validators.required, this.validateTimeEnd]],
      transactionLimitDetails: [[]],
    });
  }

  private changeInputWithCurrency(currency: string): void {
    (this.formArray.get('arrayForm') as FormArray).clear();
    this.formCreate.get('transactionLimitDetails')?.setValue([]);
    const arr = [];
    let transactionTypes;

    if (!currency) {
      transactionTypes = this.TRANSACTION_TYPE;
    } else if (currency === CURRENCY_CONST.LAK.value) {
      transactionTypes = this.INTERNAL_TRANSACTION_TYPE;
    } else if (
      currency === CURRENCY_CONST.THB.value ||
      currency === CURRENCY_CONST.USD.value
    ) {
      transactionTypes = this.INTER_TRANSACTION_TYPE;
    } else if (
      currency === CURRENCY_CONST.VND.value ||
      currency === CURRENCY_CONST.KHR.value
    ) {
      transactionTypes = this.INTERNATIONAL_TRANSACTION_TYPE;
    } else {
      transactionTypes = this.OTHER_TRANSACTION_TYPE;
    }

    for (const item of transactionTypes) {
      const form = this.fb.group({
        label: [item.label],
        transferType: [item.formName],
        maxTransferAmount: ['', this.moneyTransInvalidNone],
      });
      arr.push(form);
    }
    arr.forEach((form) => {
      (this.formArray.get('arrayForm') as FormArray).push(form);
    });
  }

  private createLinkFormGroups(currency?: string): FormGroup[] {
    const arr = [];
    if (currency === undefined || currency === null) {
      for (const item of this.TRANSACTION_TYPE) {
        const form = this.fb.group({
          label: [item.label],
          transferType: [item.formName],
          maxTransferAmount: ['', this.moneyTransInvalidNone],
        });
        arr.push(form);
      }
    }
    return arr;
  }

  public getFormGroupArray(): FormArray {
    return this.formArray.get('arrayForm') as FormArray;
  }

  onCreate(): void {
    const data = { ...this.formCreate.value };
    data.transactionLimitDetails = [];

    for (const item of this.getFormGroupArray().controls) {
      if (item.invalid && this.formCreate.invalid) {
        CommonUtils.markFormGroupTouched(this.formCreate);
        CommonUtils.markFormGroupTouchedArray(this.getFormGroupArray());
        return;
      }
      if (item.invalid) {
        CommonUtils.markFormGroupTouchedArray(this.getFormGroupArray());
        return;
      }
      if (this.formCreate.invalid) {
        CommonUtils.markFormGroupTouched(this.formCreate);
        return;
      }
    }

    if (
      !this.checkFormGroupInvalid(this.getFormGroupArray()) &&
      !this.checkFormGroupRequired(this.getFormGroupArray())
    ) {
      for (const item of this.getFormGroupArray().controls) {
        const dataTransaction = { ...item.value };
        delete dataTransaction.label;
        dataTransaction.maxTransferAmount = Number(
          dataTransaction?.maxTransferAmount?.split('.').join('')
        );
        data.transactionLimitDetails.push(dataTransaction);
      }
    }

    data.maxTransferAmtOfDay = Number(
      data.maxTransferAmtOfDay?.split('.').join('')
    );
    if (data.maxTransferAmtOfMonth) {
      data.maxTransferAmtOfMonth = Number(
        data.maxTransferAmtOfMonth?.split('.').join('')
      );
    }
    if (data.maxTransferAmtOfYear) {
      data.maxTransferAmtOfYear = Number(
        data.maxTransferAmtOfYear?.split('.').join('')
      );
    }
    const endDate = new Date(this.formCreate?.get('endDate')?.value);
    const startDate = new Date(this.formCreate?.get('startDate')?.value);
    data.endDate = endDate.toISOString();
    data.startDate = startDate.toISOString();
    this.configTransService.create(data).subscribe((res) => {
      this.router.navigate([ROUTER_UTILS.sysManage.configTransaction.root]);
      this.toastrService.success('model.configTransaction.createConfig');
    });
  }

  back(): void {
    this.router.navigate([ROUTER_UTILS.sysManage.configTransaction.root]);
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(index, this.pageIndex, this.pageSize);
  }

  checkFormGroupInvalid(formArray: FormArray) {
    return formArray.controls.find((item) => {
      return !item.get('maxTransferAmount')?.errors?.required && item.invalid;
    });
  }

  checkFormGroupRequired(formArray: FormArray) {
    return formArray.controls.find((item) => {
      return item.get('maxTransferAmount')?.errors?.required;
    });
  }

  getCurrency(currency: any) {
    this.currency = currency;
    this.formCreate.get('currency')?.setValue(currency);
    this.changeInputWithCurrency(currency);
  }

  getSector(event: any): void {
    const sector = event.target.value;
    if (Number(sector) === this.TRANSACTION_SECTOR_CONST.SECTOR_1890.value) {
      this.isSector1890 = true;
      this.isSector1740 = false;
      this.isSector1891 = false;
      this.isSectorSelected = false;
      this.formCreate
        .get('sectorId')
        ?.setValue(this.TRANSACTION_SECTOR_CONST.SECTOR_1890.value);

      this.formCreate.controls.maxTransferAmtOfMonth.clearValidators();
      this.formCreate.controls.maxTransferAmtOfMonth.setValidators([
        this.moneyMonthInvalid,
      ]);
      this.formCreate.controls.maxTransferAmtOfMonth.updateValueAndValidity();
    }

    if (Number(sector) === this.TRANSACTION_SECTOR_CONST.SECTOR_1740.value) {
      this.isSector1740 = true;
      this.isSector1890 = false;
      this.isSector1891 = false;
      this.isSectorSelected = false;
      this.formCreate
        .get('sectorId')
        ?.setValue(this.TRANSACTION_SECTOR_CONST.SECTOR_1740.value);

      this.formCreate.controls.maxTransferAmtOfMonth.clearValidators();
      this.formCreate.controls.maxTransferAmtOfMonth.setValidators([
        this.moneyMonthInvalid,
      ]);
      this.formCreate.controls.maxTransferAmtOfMonth.updateValueAndValidity();
    }

    if (Number(sector) === this.TRANSACTION_SECTOR_CONST.SECTOR_1891.value) {
      this.formCreate
        .get('sectorId')
        ?.setValue(this.TRANSACTION_SECTOR_CONST.SECTOR_1891.value);
      this.isSector1891 = true;
      this.isSector1890 = false;
      this.isSector1740 = false;
      this.isSectorSelected = false;
      this.formCreate.controls.maxTransferAmtOfDay.updateValueAndValidity();
      this.formCreate.controls.maxTransferAmtOfMonth.clearValidators();
      this.formCreate.controls.maxTransferAmtOfMonth.setValidators([
        this.moneyMonthSectorInvalid,
      ]);
      this.formCreate.controls.maxTransferAmtOfMonth.updateValueAndValidity();
    }
    if (!event) {
      this.isSector1891 = false;
      this.isSector1890 = false;
      this.isSectorSelected = true;
      this.formCreate.controls.maxTransferAmtOfMonth.reset();
      this.formCreate.controls.maxTransferAmtOfDay.reset();
      this.formCreate.controls.maxTransferAmtOfYear.reset();
      this.formCreate.controls.maxTransferAmtOfDay.updateValueAndValidity();
      this.formCreate.controls.maxTransferAmtOfMonth.updateValueAndValidity();
      this.formCreate.controls.maxTransferAmtOfYear.updateValueAndValidity();
    }
  }

  //// -----------custom validate form--------
  moneyDayInvalid = (control: AbstractControl): ValidationErrors | null => {
    if (control?.value) {
      const money = Number(control?.value?.split('.').join(''));
      this.moneyDay = money;
      if (money > 0) {
        for (const item of this.getFormGroupArray().controls) {
          item
            .get('maxTransferAmount')
            ?.setValidators([
              this.moneyTransInvalid,
              this.moneyTransInvalidNone,
            ]);
          item.get('maxTransferAmount')?.updateValueAndValidity();
        }
        return null; // passes the test, return null to clear any errors
      }
      if (money === 0) {
        return { invalidMoneyDay: true };
      }
    }
    if (!control?.value) {
      return { invalidMoneyDay: true };
    }
    return null;
  };

  moneyYearInvalid = (control: AbstractControl): ValidationErrors | null => {
    if (control?.value) {
      const money = Number(control?.value?.split('.').join(''));
      if (money > 0) {
        return null; // passes the test, return null to clear any errors
      }
      if (money === 0) {
        return { invalidMoneyYear: true };
      }
    }
    if (!control?.value) {
      return null;
    }
    return null;
  };

  moneyMonthInvalid = (control: AbstractControl): ValidationErrors | null => {
    if (control?.value) {
      const money = Number(control?.value?.split('.').join(''));
      if (money > 0) {
        return null; // passes the test, return null to clear any errors
      }
      if (money === 0) {
        return { invalidMoneyMonth: true };
      }
    }
    if (!control?.value) {
      return null;
    }
    return null;
  };

  moneyMonthSectorInvalid = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (control?.value) {
      const money = Number(control?.value?.split('.').join(''));

      if (money > 0) {
        return null; // passes the test, return null to clear any errors
      }
      if (money === 0) {
        return { invalidMoneyMonth: true };
      }
    }
    if (!control?.value) {
      return { invalidMoneyMonth: true };
    }
    return null;
  };

  moneyTransInvalid = (control: AbstractControl): ValidationErrors | null => {
    if (control?.value) {
      const money = Number(control?.value?.split('.').join(''));
      if (money <= this.moneyDay) {
        return null;
      }
    }
    if (!control?.value) {
      return null;
    }
    return { invalidMoneyTrans: true };
  };

  moneyTransInvalidNone = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (control?.value) {
      const money = Number(control?.value?.split('.').join(''));
      if (money > 0) {
        return null;
      }
      if (money === 0) {
        return { invalidMoneyTransNone: true };
      }
    }
    if (!control?.value) {
      return { invalidMoneyTransNone: true };
    }
    return null;
  };

  validateTimeStart(control: AbstractControl): ValidationErrors | null {
    if (control.value) {
      const startDate = new Date(control.value);
      const date = new Date();
      const startDateCheck = new Date(control.value);
      this.minDateEnd = new Date(startDate.setDate(startDate.getDate() + 1));
      const endDate = new Date(this.formCreate.get('endDate')?.value);
      const startDateMoment = moment(control.value).format(
        MOMENT_CONST.FORMAT_YEAR
      );
      const dateCurrentMoment = moment(new Date()).format(
        MOMENT_CONST.FORMAT_YEAR
      );
      if (
        endDate.getMonth() === startDateCheck.getMonth() &&
        endDate.getDate() === startDateCheck.getDate()
      ) {
        return { dateEqual: true };
      }
      if (startDateMoment === dateCurrentMoment) {
        if (startDateCheck.getHours() < date.getHours()) {
          return { isValidStartHour: true };
        }
        if (startDateCheck.getHours() === date.getHours()) {
          if (startDateCheck.getMinutes() < date.getMinutes() + 2) {
            return { isValidStartTime: true };
          } else {
            return null;
          }
        }
      }
    }
    return null;
  }

  validateTimeEnd(control: AbstractControl): ValidationErrors | null {
    if (control.value) {
      this.maxDateStart = control.value;
      const startDate = this.formCreate.get('startDate')?.value;
      const date = moment(new Date()).format(MOMENT_CONST.FORMAT_YEAR);
      const endDateCheck = moment(control.value).format(
        MOMENT_CONST.FORMAT_YEAR
      );
      const startDateCheck = moment(startDate).format(MOMENT_CONST.FORMAT_YEAR);
      if (!startDate) {
        if (date === endDateCheck) {
          return { inValidEndDateEqual: true };
        } else {
          return null;
        }
      } else {
        if (endDateCheck < startDateCheck || endDateCheck === startDateCheck) {
          return { isValidaEndDate: true };
        } else {
          return null;
        }
      }
    }
    return null;
  }

  onChangeStartTime(event: any): void {
    const startDate = new Date(event.value);
    startDate.setSeconds(0);
    this.formCreate.get('startDate')?.setValue(startDate);
  }

  onChangeEndTime(event: any): void {
    const endTime = new Date(event.value);
    endTime.setSeconds(0);
    this.formCreate.get('endDate')?.setValue(endTime);
  }
}
