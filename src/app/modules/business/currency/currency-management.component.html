<div class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="searchForm" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-lg-4">
            <div class="form-group">
              <label>{{ "common.action.searchKeyword" | translate }}</label>
              <input trim class="form-control w-100" formControlName="keyword" type="text"
                placeholder="{{ 'common.action.searchKeyword' | translate }}"
                [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH" />
            </div>
          </div>
          <div class="col-lg-3">
            <div class="form-group">
              <label>{{ "model.manageSaving.status" | translate }}</label>
              <ng-select placeholder="{{ 'model.manageSaving.status' | translate }}" [searchable]="false"
                formControlName="status" [clearable]="true">
                <ng-option [value]="item.code" *ngFor="let item of ENTITY_STATUS">
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 mt-4 d-flex">
            <div class="col-btn-reset" ngbTooltip="{{ 'common.action.reset' | translate }}" (click)="onReset()">
              <div class="btn-reset">
                <i class="bi bi-arrow-clockwise" type="button"> </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" type="submit">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-flex justify-content-between align-items-end text-right mb-3 mt-4">
          <h5 class="title-table text-uppercase mb-4">
            {{ "currency.list" | translate }}
          </h5>
          <div class="d-block text-right mb-3 mt-4">

            <button class="btn btn-red mr-2" type="button" (click)="create()"
              *hasPrivileges="SYSTEM_RULES.CURRENCY_CREATE">
              {{ "common.action.create" | translate }}
            </button>
          </div>
        </div>
      </form>

      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th [width]="'50px'" class="text-center">
                {{ "model.manageSaving.no" | translate }}
              </th>
              <th class="text-left" [width]="'250px'">
                {{ "currency.code" | translate }}
              </th>
              <th class="text-left" [width]="'250px'">
                {{ "currency.name" | translate }}
              </th>
              <th class="text-left" [width]="'250px'">
                {{ "currency.value" | translate }}
              </th>
              <th [width]="'200px'" class="text-left">
                {{ "model.role.updater" | translate }}
              </th>
              <th [width]="'200px'" class="text-center">
                {{ "model.role.lastModifiedDate" | translate }}
              </th>
              <th [width]="'200px'" class="text-left">
                {{ "model.role.creator" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.status" | translate }}
              </th>
              <th [width]="'200px'" class="text-center">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of data; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td class="text-left">{{ item?.code }}</td>
              <td class="text-left" title="{{ item?.name }}">{{ item?.name | limitWord : 30 }}</td>
              <td class="text-left">{{ item?.value }}</td>
              <td class="text-left">{{ item?.lastModifiedBy }}</td>
              <td class="text-center">{{ item?.lastModifiedDate }}</td>
              <td class="text-left">{{ item?.createdBy }}</td>
              <td class="text-center">
                <span class="badge" [ngClass]="ENTITY_STATUS_MAP[item.status || 0].style">{{
                  ENTITY_STATUS_MAP[item.status || 0].label | translate
                  }}</span>
              </td>
              <td class="text-center">
                <button ngbTooltip="{{ 'common.action.detail' | translate }}" class="btn px-1 py-0"
                  (click)="detail(item?.currencyId)">
                  <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                </button>
                <button ngbTooltip="{{ 'common.action.update' | translate }}" class="btn px-1 py-0"
                  (click)="update(item?.currencyId)" data-toggle="modal" *hasPrivileges="SYSTEM_RULES.CURRENCY_WRITE">
                  <i class="fa fa-edit mb-color" aria-hidden="true"></i>
                </button>
                <button class="btn px-1 py-0" [ngbTooltip]="
                    item.status === ENTITY_STATUS_CONST.INACTIVE.code
                      ? ('common.action.unlock' | translate)
                      : ('common.action.lock' | translate)
                  " (click)="activeAndInactive(item)" *hasPrivileges="
                    item.status === ENTITY_STATUS_CONST.ACTIVE.code
                      ? SYSTEM_RULES.CURRENCY_LOCK
                      : SYSTEM_RULES.CURRENCY_UNLOCK
                  ">
                  <i [className]="
                      item.status === ENTITY_STATUS_CONST.ACTIVE.code
                        ? 'fa fa-lock mb-color'
                        : 'fa fa-unlock mb-color'
                    " aria-hidden="true"></i>
                </button>
                <ng-container *hasPrivileges="SYSTEM_RULES.CURRENCY_DELETE">
                  <button ngbTooltip="{{ 'common.action.delete' | translate }}" class="btn px-1 py-0"
                    (click)="delete(item)">
                    <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                  </button>
                </ng-container>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="row d-block text-center m-0 no-search-result-wrapper" *ngIf="data?.length === 0">
          <img src="/assets/dist/img/icon/empty.svg" height="120" alt="no_search_result" />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
      </div>
      <div *ngIf="data?.length">
        <mat-paginator [length]="searchForm.value.length" [pageSize]="searchForm.value.pageSize"
          [pageIndex]="searchForm.value.pageIndex" [pageSizeOptions]="pageSizeOptions" (page)="onChangePage($event)"
          aria-label="Select page">
        </mat-paginator>
      </div>
    </div>
  </div>
</div>