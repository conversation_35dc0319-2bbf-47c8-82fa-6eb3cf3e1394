import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { MODAL_ACTION } from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import {
  APPROVAL_STATUS_CONST,
  APPROVAL_TYPE_CONST,
} from '@shared/constants/customer.constants';
import { Customer } from '@shared/models/customer.model';
import { CustomerService } from '@shared/services/customer.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_UTILS } from '@shared/utils';
import { CustomerApprovalModalComponent } from '../customer-approval-modal/customer-approval-modal.component';

@Component({
  selector: 'app-customer-approval-update',
  templateUrl: './customer-approval-update.component.html',
  styleUrls: ['./customer-approval-update.component.scss'],
})
export class CustomerApprovalUpdateComponent implements OnInit {
  customerInfo: Customer = new Customer();

  approvalButton = [
    {
      type: APPROVAL_TYPE_CONST.UPDATE.value,
      privilege: SYSTEM_RULES.CUSTOMER_UPDATE_APPROVAL,
    },
    {
      type: APPROVAL_TYPE_CONST.LOCK.value,
      privilege: SYSTEM_RULES.CUSTOMER_LOCK_APPROVAL,
    },
    {
      type: APPROVAL_TYPE_CONST.UNLOCK.value,
      privilege: SYSTEM_RULES.CUSTOMER_UNLOCK_APPROVAL,
    },
    {
      type: APPROVAL_TYPE_CONST.DELETE.value,
      privilege: SYSTEM_RULES.CUSTOMER_CANCEL_APPROVAL,
    },
    {
      type: APPROVAL_TYPE_CONST.UNCLOSED.value,
      privilege: SYSTEM_RULES.CUSTOMER_UNCLOSED_APPROVAL,
    },
  ];

  APPROVAL_STATUS_CONST = APPROVAL_STATUS_CONST;
  SYSTEM_RULES = SYSTEM_RULES;

  constructor(
    private customerService: CustomerService,
    private router: Router,
    private toastService: ToastrCustomService,
    private modalServiceOpen: NgbModal,
    private routerActive: ActivatedRoute
  ) {
    this.routerActive.paramMap.subscribe((res) => {
      const idParam = res.get('customerId');
      if (idParam) {
        this.customerInfo.customerId = +idParam;
      }
    });
  }

  ngOnInit(): void {
    this.getDetail();
  }

  /**
   * get detail customer
   */
  getDetail(): void {
    if (this.customerInfo.customerId) {
      this.customerService.info(this.customerInfo).subscribe((res: any) => {
        this.customerInfo = res.body;
      });
    }
  }

  /**
   * approval create: check approval status and approval type then call api
   */
  approval(approvalType: number): void {
    let title = '';
    let content = '';
    let successContent = '';

    switch (approvalType) {
      case APPROVAL_TYPE_CONST.LOCK.value:
        title = 'customerRegisterManagement.customerApproval.lockApproval';
        content =
          'customerRegisterManagement.customerApproval.lockApprovalContent';
        successContent =
          'customerRegisterManagement.customerApproval.success.lockApproval';
        break;
      case APPROVAL_TYPE_CONST.UNLOCK.value:
        title = 'customerRegisterManagement.customerApproval.unlockApproval';
        content =
          'customerRegisterManagement.customerApproval.unlockApprovalContent';
        successContent =
          'customerRegisterManagement.customerApproval.success.unlockApproval';
        break;
      case APPROVAL_TYPE_CONST.DELETE.value:
        title = 'customerRegisterManagement.customerApproval.deleteApproval';
        content =
          'customerRegisterManagement.customerApproval.deleteApprovalContent';
        successContent =
          'customerRegisterManagement.customerApproval.success.deleteApproval';
        break;
      case APPROVAL_TYPE_CONST.UPDATE.value:
        title = 'customerRegisterManagement.customerApproval.updateApproval';
        content =
          'customerRegisterManagement.customerApproval.updateApprovalContent';
        successContent =
          'customerRegisterManagement.customerApproval.success.updateApproval';
        break;
      case APPROVAL_TYPE_CONST.UNCLOSED.value:
        title = 'customerRegisterManagement.customerApproval.unclosedApproval';
        content =
          'customerRegisterManagement.customerApproval.unclosedApprovalContent';
        successContent =
          'customerRegisterManagement.customerApproval.success.unclosedApproval';
    }

    // if (
    //   (this.customerInfo.approvalStatus ===
    //     APPROVAL_STATUS_CONST.PENDING.value ||
    //     this.customerInfo.approvalStatus ===
    //       APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
    //   this.customerInfo.approvalType === APPROVAL_TYPE_CONST.LOCK.value
    // ) {
    //   title = 'customerRegisterManagement.customerApproval.lockApproval';
    //   content =
    //     'customerRegisterManagement.customerApproval.lockApprovalContent';
    //   successContent =
    //     'customerRegisterManagement.customerApproval.success.lockApproval';
    // } else if (
    //   (this.customerInfo.approvalStatus ===
    //     APPROVAL_STATUS_CONST.PENDING.value ||
    //     this.customerInfo.approvalStatus ===
    //       APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
    //   this.customerInfo.approvalType === APPROVAL_TYPE_CONST.UNLOCK.value
    // ) {
    //   title = 'customerRegisterManagement.customerApproval.unlockApproval';
    //   content =
    //     'customerRegisterManagement.customerApproval.unlockApprovalContent';
    //   successContent =
    //     'customerRegisterManagement.customerApproval.success.unlockApproval';
    // } else if (
    //   (this.customerInfo.approvalStatus ===
    //     APPROVAL_STATUS_CONST.PENDING.value ||
    //     this.customerInfo.approvalStatus ===
    //       APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
    //   this.customerInfo.approvalType === APPROVAL_TYPE_CONST.DELETE.value
    // ) {
    //   title = 'customerRegisterManagement.customerApproval.deleteApproval';
    //   content =
    //     'customerRegisterManagement.customerApproval.deleteApprovalContent';
    //   successContent =
    //     'customerRegisterManagement.customerApproval.success.deleteApproval';
    // } else if (
    //   (this.customerInfo.approvalStatus ===
    //     APPROVAL_STATUS_CONST.PENDING.value ||
    //     this.customerInfo.approvalStatus ===
    //       APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
    //   this.customerInfo.approvalType === APPROVAL_TYPE_CONST.UPDATE.value
    // ) {
    //   title = 'customerRegisterManagement.customerApproval.updateApproval';
    //   content =
    //     'customerRegisterManagement.customerApproval.updateApprovalContent';
    //   successContent =
    //     'customerRegisterManagement.customerApproval.success.updateApproval';
    // }

    const modalRef = this.modalServiceOpen.open(
      CustomerApprovalModalComponent,
      {
        backdrop: 'static',
        size: 'lg',
        centered: true,
        keyboard: false,
      }
    );
    modalRef.componentInstance.action = MODAL_ACTION.CONFIRM;
    modalRef.componentInstance.title = title;
    modalRef.componentInstance.content = content;
    modalRef.componentInstance.customer = this.customerInfo;
    modalRef.componentInstance.isDeny = false;
    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.toastService.success(successContent);
        this.router.navigate([
          ROUTER_UTILS.customer.root,
          ROUTER_UTILS.customer.approve.root,
        ]);
      }
    });
  }

  /**
   * deny create: check approval status and approval type then call api
   *
   * @param approvalType number
   */
  deny(approvalType: number): void {
    let title = '';
    let content = '';
    let successContent = '';

    switch (approvalType) {
      case APPROVAL_TYPE_CONST.LOCK.value:
        title = 'customerRegisterManagement.customerApproval.lockDeny';
        content = 'customerRegisterManagement.customerApproval.lockDenyContent';
        successContent =
          'customerRegisterManagement.customerApproval.success.deny.lockDeny';
        break;
      case APPROVAL_TYPE_CONST.UNLOCK.value:
        title = 'customerRegisterManagement.customerApproval.unlockDeny';
        content =
          'customerRegisterManagement.customerApproval.unlockDenyContent';
        successContent =
          'customerRegisterManagement.customerApproval.success.deny.unlockDeny';
        break;
      case APPROVAL_TYPE_CONST.UNCLOSED.value:
        title = 'customerRegisterManagement.customerApproval.unclosedDeny';
        content =
          'customerRegisterManagement.customerApproval.unclosedDenyContent';
        successContent =
          'customerRegisterManagement.customerApproval.success.deny.unlockDeny';
        break;
      case APPROVAL_TYPE_CONST.DELETE.value:
        title = 'customerRegisterManagement.customerApproval.deleteDeny';
        content =
          'customerRegisterManagement.customerApproval.deleteDenyContent';
        successContent =
          'customerRegisterManagement.customerApproval.success.deny.deleteDeny';
        break;
      case APPROVAL_TYPE_CONST.UPDATE.value:
        title = 'customerRegisterManagement.customerApproval.updateDeny';
        content =
          'customerRegisterManagement.customerApproval.updateDenyContent';
        successContent =
          'customerRegisterManagement.customerApproval.success.deny.updateDeny';
        break;
    }

    // if (
    //   (this.customerInfo.approvalStatus ===
    //     APPROVAL_STATUS_CONST.PENDING.value ||
    //     this.customerInfo.approvalStatus ===
    //       APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
    //   this.customerInfo.approvalType === APPROVAL_TYPE_CONST.LOCK.value
    // ) {
    //   title = 'customerRegisterManagement.customerApproval.lockDeny';
    //   content = 'customerRegisterManagement.customerApproval.lockDenyContent';
    //   successContent =
    //     'customerRegisterManagement.customerApproval.success.deny.lockDeny';
    // } else if (
    //   (this.customerInfo.approvalStatus ===
    //     APPROVAL_STATUS_CONST.PENDING.value ||
    //     this.customerInfo.approvalStatus ===
    //       APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
    //   this.customerInfo.approvalType === APPROVAL_TYPE_CONST.UNLOCK.value
    // ) {
    //   title = 'customerRegisterManagement.customerApproval.unlockDeny';
    //   content = 'customerRegisterManagement.customerApproval.unlockDenyContent';
    //   successContent =
    //     'customerRegisterManagement.customerApproval.success.deny.unlockDeny';
    // } else if (
    //   (this.customerInfo.approvalStatus ===
    //     APPROVAL_STATUS_CONST.PENDING.value ||
    //     this.customerInfo.approvalStatus ===
    //       APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
    //   this.customerInfo.approvalType === APPROVAL_TYPE_CONST.DELETE.value
    // ) {
    //   title = 'customerRegisterManagement.customerApproval.deleteDeny';
    //   content = 'customerRegisterManagement.customerApproval.deleteDenyContent';
    //   successContent =
    //     'customerRegisterManagement.customerApproval.success.deny.deleteDeny';
    // } else if (
    //   (this.customerInfo.approvalStatus ===
    //     APPROVAL_STATUS_CONST.PENDING.value ||
    //     this.customerInfo.approvalStatus ===
    //       APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
    //   this.customerInfo.approvalType === APPROVAL_TYPE_CONST.UPDATE.value
    // ) {
    //   title = 'customerRegisterManagement.customerApproval.updateDeny';
    //   content = 'customerRegisterManagement.customerApproval.updateDenyContent';
    //   successContent =
    //     'customerRegisterManagement.customerApproval.success.deny.updateDeny';
    // }

    const modalRef = this.modalServiceOpen.open(
      CustomerApprovalModalComponent,
      {
        backdrop: 'static',
        size: 'lg',
        centered: true,
        keyboard: false,
      }
    );
    modalRef.componentInstance.action = MODAL_ACTION.CONFIRM;
    modalRef.componentInstance.title = title;
    modalRef.componentInstance.content = content;
    modalRef.componentInstance.customer = this.customerInfo;
    modalRef.componentInstance.isDeny = true;
    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.toastService.success(successContent);
        this.router.navigate([
          ROUTER_UTILS.customer.root,
          ROUTER_UTILS.customer.approve.root,
        ]);
      }
    });
  }

  /**
   * back
   */
  onBack(): void {
    window.history.back();
  }
}
