import {
  Directive,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  Output,
} from '@angular/core';

@Directive({ selector: '[appNumberMaxMin]' })
export class MaxMinNumberDirective {
  private el: any;
  @Input() maxValue = 99;
  @Input() minValue = 0;
  @Output() onMaxValue: EventEmitter<any> = new EventEmitter();

  constructor(private elementRef: ElementRef) {
    this.el = this.elementRef.nativeElement;
  }

  @HostListener('keypress', ['$event'])
  validateNumericKeyPress(event: any): boolean {
    // tslint:disable-next-line:prefer-conditional-expression
    if (this.maxValue < this.el.value || this.minValue > this.el.value) {
      event.preventDefault();
      return false;
    }
    return true;
  }

  @HostListener('keyup', ['$event'])
  onKeyUp(event: any): void {
    // tslint:disable-next-line:prefer-conditional-expression
    if (this.maxValue < this.el.value || this.minValue > this.el.value) {
      event.preventDefault();
    }
  }
}
