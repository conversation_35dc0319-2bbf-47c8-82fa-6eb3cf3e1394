import { Moment } from 'moment';
import { ICustomer } from './customer.model';
import { IDocument } from './file.model';
import { INotification } from './notification.model';

export interface IEvent {
  eventId?: number;
  title?: string;
  description?: string;
  content?: string;
  eventStatus?: string;
  expectedNotificationAt?: Moment;
  numberSent?: Moment;
  announcementTypeId?: number;
  receiverType?: string;
  announcementTypeName?: string;
  senderUserId?: number;
  customerDTOS?: ICustomer[];
  totalSuccess?: number;
  totalFail?: number;
  createdBy?: string;
  notificationDTOS?: INotification[];
  icon?: IDocument;
  files?: any;
  totalNotification?: number;
}
