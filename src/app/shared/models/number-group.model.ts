import { INumberStructure } from './premium-account-number.modal';

export interface INumberGroup {
  numberGroupId?: number;
  code?: number;
  label?: string;
  numberGroupCode?: string;
  minPrice?: number;
  maxPrice?: number;
  description?: string;
  status?: number;
  createdDate?: string;
  lastModifiedDate?: string;
  lastModifiedBy?: string;
  name?: string;
  id?: number;
  createdBy?: string;
  keyword?: string;
  timeZoneStr?: string;
  structures?: INumberStructure[];
}

export class NumberGroup implements INumberGroup {
  constructor(data: INumberGroup) {
    Object.assign(this, data);
  }
}