import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IIdCardType } from '@shared/models/id-card-type.model';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({ providedIn: 'root' })
export class IdCardTypeService extends AbstractService {
  public resourceUrl = '/public/id-card-type/all';

  constructor(protected http: HttpClient) {
    super(http);
  }

  /**
   * API get all id-card-type
   *
   * @returns Observable<EntityResponseType<IIdCardType[]>>
   */
  public getAll(): Observable<EntityResponseType<IIdCardType[]>> {
    return super.post(this.resourceUrl);
  }
}
