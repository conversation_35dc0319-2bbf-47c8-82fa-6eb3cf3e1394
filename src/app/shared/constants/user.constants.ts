export const ACTION_TOKEN_CONST = {
  ACTIVATED: 'ACTIVATED',
  SEND_LINK_ACTIVATE: 'SEND_LINK_ACTIVATE',
  RESEND_LINK_ACTIVATE: 'RESEND_LINK_ACTIVATE',
  EXPIRE_TOKEN_CHANGE_PW: 'EXPIRE_TOKEN_CHANGE_PW',
  CHANGE_PW_SUCCESS: 'CHANGE_PW_SUCCESS',
};

export const SELECTED_CONST = {
  POSITION: 'POSITION',
  DEPARTMENT: 'DEPARTMENT',
  STATUS: 'STATUS',
  TRANSACTION_STATUS: 'TRANSACTION_STATUS',
  TRANSACTION_TYPE: 'TRANSACTION_TYPE',
  SERVICE_PACK_TYPE: 'SERVICE_PACK_TYPE',
  CLIENT_ID: 'CLIENT_ID',
  NUMBER_GROUP: 'NUMBER_GROUP',
  STRUCTURE: 'STRUCTURE',
  REFERRAL_ID: 'REFERRAL_ID',
  LENGTH_ID: 'CODE',
  NUMBER_STRUCTURE: 'NUMBER_STRUCTURE',
  DR_CR: 'DR_CR',
  CROSS_BORDER_NATION_CODES: 'CROSS_BORDER_NATION_CODES'
};

export const ENTITY_ACTIVE_USER_STATUS_CONST = {
  ACTIVATED: {
    label: 'common.activated',
    value: true,
    code: 1,
    style: 'bg-primary',
  },
  NOT_ACTIVATED: {
    label: 'common.notActivated',
    value: false,
    code: 0,
    style: 'bg-secondary',
  },
};

export const TAB_CONST = {
  ALL: 'ALL',
  UNREAD: 'UNREAD',
  READ: 'READ',
};

export const ENTITY_ACTIVE_USER_STATUS = Object.values(
  ENTITY_ACTIVE_USER_STATUS_CONST
);

export const ENTITY_ACTIVE_USER_STATUS_MAP = ENTITY_ACTIVE_USER_STATUS.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.code]: {
      label: valueMap.label,
      style: valueMap.style,
      value: valueMap.value,
    },
  }),
  {}
) as any;

export const ENTITY_STATUS_USER_CONST = {
  NOT_ACTIVATED: {
    label: 'common.notActivated',
    value: false,
    code: 2,
    style: 'bg-secondary',
  },
  ACTIVE: {
    label: 'common.active',
    value: true,
    code: 1,
    style: 'badge-success',
  },
  INACTIVE: {
    label: 'common.inactive',
    value: true,
    code: 0,
    style: 'badge-danger',
  },
};

export const ENTITY_STATUS_USER = Object.values(ENTITY_STATUS_USER_CONST);
