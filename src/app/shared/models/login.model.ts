export interface ILogin {
  username: string;
  password: string;
  rememberMe?: boolean;
  captcha?: string;
  transactionId?: string;
  deviceToken?: string;
}

// export class Login implements ILogin {
//   constructor(
//     public username: string,
//     public password: string,
//     public rememberMe?: boolean,
//     public captcha?: string,
//     public transactionId?: string,
//   ) {}
// }
