import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { IClient, IServicePackType } from '@shared/models/service-pack.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { Observable } from 'rxjs';
import { AbstractDomainService } from './common/abstract.domain.service';
import { EntityResponseType } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class ClientService extends AbstractDomainService<IClient> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.CLIENT;

  getAllServicePackType(): Observable<EntityResponseType<IServicePackType[]>> {
    return super.post(RESOURCE_CONST.SERVICE_PACK.API.SERVICE_PACK_TYPE);
  }
}
