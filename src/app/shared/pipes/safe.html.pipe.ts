import { Pipe, PipeTransform } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
/**
 * @export
 * @class SafeHTML
 * @implements {PipeTransform}
 * @howToUse
 * ```
 *     <some-element>{{ data?.html | safeHTML }}</some-element>
 * ```
 */
@Pipe({ name: 'safeHTML' })
export class SafeHTML implements PipeTransform {
  constructor(private sanitizer: DomSanitizer) {}

  transform(str?: string): SafeHtml {
    return this.sanitizer.bypassSecurityTrustHtml(str || '');
  }
}
