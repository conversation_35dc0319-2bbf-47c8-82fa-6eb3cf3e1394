<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="backToList()" />
      <h5>
        {{
        action === ROUTER_ACTIONS.detail
        ? ("client.detailTitle" | translate)
        : action === ROUTER_ACTIONS.update
        ? ("client.updateTitle" | translate)
        : ("client.createTitle" | translate)
        }}
      </h5>
    </div>
    <!-- <form [formGroup]="form" *ngIf="customer?.customerId"></form> -->
    <form [formGroup]="form" *ngIf="client?.clientId || action === ROUTER_ACTIONS.create">
      <div class="col-md-12">
        <div class="row">
          <h5 class="modal-title" *ngIf="!(action === ROUTER_ACTIONS.detail)">
            {{
            (action === ROUTER_ACTIONS.update
            ? "client.updateTitle"
            : "client.createTitle"
            ) | translate
            }}
          </h5>
          <h5 class="modal-title" *ngIf="action === ROUTER_ACTIONS.detail">
            {{ "client.titleInforUser" | translate }}
          </h5>
        </div>
        <div class="row border-create">
          <h3>{{ "client.titleInforUser" | translate }}</h3>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label>{{ "client.clientId" | translate
                    }}<span class="text-danger">*</span></label>
                  <input trim formControlName="clientId" type="text" class="w-100" class="form-control"
                    placeholder="{{ 'client.clientId' | placeholder }}"
                    [maxLength]="VALIDATORS.LENGTH.USERNAME_MAX_LENGTH" />
                  <small class="form-text text-danger noti-small" *ngIf="
                      form.get('clientId')?.errors?.required &&
                      form.get('clientId')?.touched
                    ">
                    {{ "client.error.required.clientId" | translate }}
                  </small>
                  <small class="form-text text-danger noti-small" *ngIf="
                      form.get('clientId')?.errors?.pattern &&
                      form.get('clientId')?.touched
                    ">
                    {{ "client.error.pattern.clientId" | translate }}
                  </small>
                  <small class="form-text text-danger noti-small" *ngIf="
                      form.get('clientId')?.errors?.maxlength &&
                      form.get('clientId')?.touched
                    ">
                    {{
                    "client.error.maxLength.clientId"
                    | translate
                    : {
                    param: VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH
                    }
                    }}
                  </small>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group" *ngIf="action === ROUTER_ACTIONS.create">
                  <label>{{ "client.password" | translate
                    }}<span class="text-danger">*</span></label>
                  <input trim formControlName="password" type="text" class="w-100" class="form-control"
                    placeholder="{{ 'client.password' | placeholder }}"
                    [maxLength]="VALIDATORS.LENGTH.PASSWORD_MAX_LENGTH" />
                  <small class="form-text text-danger noti-small" *ngIf="
                      form.get('password')?.errors?.required &&
                      form.get('password')?.touched
                    ">
                    {{ "client.error.required.password" | translate }}
                  </small>
                  <small class="form-text text-danger noti-small" *ngIf="
                      form.get('password')?.errors?.pattern &&
                      form.get('password')?.touched
                    ">
                    {{ "client.error.pattern.password" | translate }}
                  </small>
                  <small class="form-text text-danger noti-small" *ngIf="
                      form.get('password')?.errors?.maxlength &&
                      form.get('password')?.touched
                    ">
                    {{
                    "client.error.maxLength.password"
                    | translate
                    : {
                    param: VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH
                    }
                    }}
                  </small>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label>{{ "client.name" | translate
                    }}<span class="text-danger">*</span></label>
                  <input trim formControlName="name" type="text" class="w-100" class="form-control"
                    placeholder="{{ 'client.name' | placeholder }}"
                    [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH" />
                  <small class="form-text text-danger noti-small" *ngIf="
                      form.get('name')?.errors?.required &&
                      form.get('name')?.touched
                    ">
                    {{ "client.error.required.name" | translate }}
                  </small>
                  <small class="form-text text-danger noti-small" *ngIf="
                      form.get('name')?.errors?.pattern &&
                      form.get('name')?.touched
                    ">
                    {{ "client.error.pattern.name" | translate }}
                  </small>
                  <small class="form-text text-danger noti-small" *ngIf="
                      form.get('name')?.errors?.maxlength &&
                      form.get('name')?.touched
                    ">
                    {{
                    "client.error.maxLength.name"
                    | translate
                    : {
                    param: VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH
                    }
                    }}
                  </small>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label>{{ "client.type" | translate
                    }}<span class="text-danger">*</span></label>
                  <ng-select formControlName="type" [clearable]="false" [searchable]="false" appearance="outline"
                    class="w-100" placeholder="{{ 'client.type' | placeholder : 'select' }}">
                    <ng-option [value]="item.code" *ngFor="let item of CLIENT_TYPE">
                      {{ item.label | translate }}
                    </ng-option>
                  </ng-select>
                  <small class="form-text text-danger noti-small" *ngIf="
                      form.get('type')?.errors?.required &&
                      form.get('type')?.touched
                    ">
                    {{ "client.error.required.type" | translate }}
                  </small>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12" *ngIf="action === ROUTER_ACTIONS.detail">
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.role.creator" | translate }}</label>
                  <input trim type="text" disabled class="w-100" class="form-control"
                    [value]="client?.createdBy || ''" />
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.role.fromDate" | translate }}</label>
                  <input trim type="text" [value]="client?.createdDate || ''" disabled class="w-100"
                    class="form-control" />
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.role.updater" | translate }}</label>
                  <input trim type="text" disabled class="w-100" class="form-control"
                    [value]="client?.lastModifiedBy || ''" />
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.role.lastModifiedDate" | translate }}</label>
                  <input trim type="text" disabled [value]="client?.lastModifiedDate || ''" class="w-100"
                    class="form-control" />
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-3" *ngIf="action === ROUTER_ACTIONS.detail">
                <div class="form-group">
                  <label>{{ "common.status" | translate
                    }}<span class="text-danger">*</span></label>
                  <ng-select appearance="outline" [searchable]="false" [clearable]="false" formControlName="status">
                    <ng-option [value]="item.code" *ngFor="let item of ENTITY_STATUS">
                      {{ item.label | translate }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row border-create mt-4" *ngIf="action === ROUTER_ACTIONS.detail">
          <h3>
            {{ "servicePack.list" | translate }}
          </h3>
          <div class="col-md-12" *hasPrivileges="SYSTEM_RULES.CLIENT_READ">
            <div class="col-12">
              <form [formGroup]="formServicePackSearch" (submit)="onSearchServicePackSubmit()">
                <div class="row">
                  <div class="col-md-3">
                    <div class="form-group">
                      <label>{{
                        "common.action.searchKeyword" | translate
                        }}</label>
                      <input trim type="text" formControlName="keyword" placeholder="{{
                          'common.action.searchKeyword' | placeholder
                        }}" class="w-100" class="form-control" />
                    </div>
                  </div>
                  <div class="col-md-2 col-lg-2 mt-4 d-flex">
                    <div class="col-btn-reset" ngbTooltip="{{ 'common.action.reset' | translate }}" (click)="onReset()">
                      <div class="btn-reset">
                        <i class="bi bi-arrow-clockwise" type="button"> </i>
                      </div>
                    </div>
                    <div class="">
                      <button class="btn btn-search mr-2" type="submit" *hasPrivileges="SYSTEM_RULES.CLIENT_READ">
                        {{ "common.action.search" | translate }}
                      </button>
                    </div>
                  </div>
                </div>
              </form>
            </div>
            <div class="col-12">
              <div class="table-responsive">
                <table class="table">
                  <thead>
                    <tr>
                      <th [width]="'50px'" class="text-center">{{ "common.no" | translate }}</th>
                      <th class="text-left" [width]="'250px'">
                        {{ "servicePack.code" | translate }}
                      </th>
                      <th class="text-left" [width]="'250px'">
                        {{ "servicePack.name" | translate }}
                      </th>
                      <th [width]="'180px'" class="text-left" [width]="'250px'">
                        {{ "servicePack.type" | translate }}
                      </th>
                      <th [width]="'200px'" class="text-center">
                        {{ "common.status" | translate }}
                      </th>
                      <th [width]="'200px'" class="text-center" *hasPrivileges="SYSTEM_RULES.SERVICE_PACK_READ">
                        {{ "common.action.label" | translate }}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let item of servicePackTypes; let i = index">
                      <td class="text-center">{{ fillIndexItem(i) }}</td>
                      <td class="text-left">{{ item?.code }}</td>
                      <td class="text-left">{{ item?.name }}</td>
                      <td class="text-left">{{ item?.typeStr }}</td>
                      <td class="text-center">
                        <span class="badge" [ngClass]="ENTITY_STATUS_MAP[item?.status || 0].style">
                          {{ ENTITY_STATUS_MAP[item?.status || 0].label | translate }}
                        </span>
                      </td>
                      <td class="text-center" *hasPrivileges="SYSTEM_RULES.SERVICE_PACK_READ">
                        <button ngbTooltip="{{ 'common.action.detail' | translate }}" class="btn px-1 py-0"
                          (click)="detail(item?.servicePackId)">
                          <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                        </button>
                        <button ngbTooltip="{{ 'common.action.update' | translate }}" class="btn px-1 py-0"
                          (click)="edit(item?.servicePackId)" data-toggle="modal"
                          *hasPrivileges="SYSTEM_RULES.SERVICE_PACK_UPDATE">
                          <i class="fa fa-edit mb-color" aria-hidden="true"></i>
                        </button>
                        <ng-container *hasPrivileges="
                            item?.status === 1
                              ? SYSTEM_RULES.SERVICE_PACK_LOCK
                              : SYSTEM_RULES.SERVICE_PACK_UNLOCK
                          ">
                          <button [ngbTooltip]="
                              (item?.status === ENTITY_STATUS_CONST.ACTIVE.code
                                ? 'common.action.lock'
                                : 'common.action.unlock'
                              ) | translate
                            " class="btn px-1 py-0" (click)="lockAndUnlock(item)">
                            <i [className]="
                                item?.status === ENTITY_STATUS_CONST.ACTIVE.code
                                  ? 'fa fa-lock mb-color'
                                  : 'fa fa-unlock mb-color'
                              " aria-hidden="true"></i>
                          </button>
                        </ng-container>
                        <ng-container *hasPrivileges="SYSTEM_RULES.SERVICE_PACK_DELETE">
                          <button *ngIf="
                              item?.clientId !== 'umoney' && item?.clientId !== 'mbbank'
                            " ngbTooltip="{{ 'common.action.delete' | translate }}" class="btn px-1 py-0"
                            (click)="delete(item)">
                            <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                          </button>
                        </ng-container>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <div class="row d-block text-center m-0" *ngIf="servicePackTypes?.length === 0">
                  <img src="/assets/dist/img/icon/empty.svg" height="120" alt="no_search_result" />
                  <p class="text-center mb-5">
                    {{ "common.no_search_result" | translate }}
                  </p>
                </div>
                <div *ngIf="servicePackTypes.length">
                  <mat-paginator [length]="formServicePackSearch.value.length"
                    [pageSize]="formServicePackSearch.value.pageSize"
                    [pageIndex]="formServicePackSearch.value.pageIndex" [pageSizeOptions]="pageSizeOptions"
                    (page)="onChangePage($event)" aria-label="Select page">
                  </mat-paginator>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="d-block text-center mb-5 mt-4">
          <button type="button" class="btn btn-white mr-2" data-toggle="modal" (click)="backToList()">
            {{ "common.action.back" | translate }}
          </button>
          <ng-container *ngIf="action === ROUTER_ACTIONS.detail">
            <button class="btn btn-red mr-2" (click)="onEdit()" *hasPrivileges="SYSTEM_RULES.CLIENT_UPDATE">
              {{ "common.action.update" | translate }}
            </button>
          </ng-container>
          <ng-container *hasPrivileges="
              action === ROUTER_ACTIONS.update
                ? SYSTEM_RULES.CLIENT_UPDATE
                : SYSTEM_RULES.CLIENT_CREATE
            ">
            <button *ngIf="!(action === ROUTER_ACTIONS.detail)" class="btn btn-red" (click)="
                action === ROUTER_ACTIONS.update ? onUpdate() : onCreate()
              ">
              {{
              (action === ROUTER_ACTIONS.update
              ? "common.action.update"
              : "common.action.create"
              ) | translate
              }}
            </button>
          </ng-container>
        </div>
      </div>
    </form>
  </div>
</section>