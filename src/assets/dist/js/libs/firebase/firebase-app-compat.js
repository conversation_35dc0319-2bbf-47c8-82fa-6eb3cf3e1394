!(function (e, t) {
  "object" == typeof exports && "undefined" != typeof module
    ? (module.exports = t())
    : "function" == typeof define && define.amd
    ? define(t)
    : ((e =
        "undefined" != typeof globalThis ? globalThis : e || self).firebase =
        t());
})(this, function () {
  "use strict";
  function a(e, t) {
    if (!(t instanceof Object)) return t;
    switch (t.constructor) {
      case Date:
        const n = t;
        return new Date(n.getTime());
      case Object:
        void 0 === e && (e = {});
        break;
      case Array:
        e = [];
        break;
      default:
        return t;
    }
    for (const r in t)
      t.hasOwnProperty(r) && "__proto__" !== r && (e[r] = a(e[r], t[r]));
    return e;
  }
  class r {
    constructor() {
      (this.reject = () => {}),
        (this.resolve = () => {}),
        (this.promise = new Promise((e, t) => {
          (this.resolve = e), (this.reject = t);
        }));
    }
    wrapCallback(n) {
      return (e, t) => {
        e ? this.reject(e) : this.resolve(t),
          "function" == typeof n &&
            (this.promise.catch(() => {}), 1 === n.length ? n(e) : n(e, t));
      };
    }
  }
  class i extends Error {
    constructor(e, t, n) {
      super(t),
        (this.code = e),
        (this.customData = n),
        (this.name = "FirebaseError"),
        Object.setPrototypeOf(this, i.prototype),
        Error.captureStackTrace &&
          Error.captureStackTrace(this, o.prototype.create);
    }
  }
  class o {
    constructor(e, t, n) {
      (this.service = e), (this.serviceName = t), (this.errors = n);
    }
    create(e, ...t) {
      var r,
        n = t[0] || {},
        t = `${this.service}/${e}`,
        e = this.errors[e],
        e = e
          ? ((r = n),
            e.replace(s, (e, t) => {
              var n = r[t];
              return null != n ? String(n) : `<${t}?>`;
            }))
          : "Error",
        e = `${this.serviceName}: ${e} (${t}).`;
      return new i(t, e, n);
    }
  }
  const s = /\{\$([^}]+)}/g;
  function c(e, t) {
    return Object.prototype.hasOwnProperty.call(e, t);
  }
  function l(e, t) {
    if (e === t) return 1;
    const n = Object.keys(e),
      r = Object.keys(t);
    for (const s of n) {
      if (!r.includes(s)) return;
      var i = e[s],
        o = t[s];
      if (p(i) && p(o)) {
        if (!l(i, o)) return;
      } else if (i !== o) return;
    }
    for (const a of r) if (!n.includes(a)) return;
    return 1;
  }
  function p(e) {
    return null !== e && "object" == typeof e;
  }
  function n(e, t) {
    const n = new u(e, t);
    return n.subscribe.bind(n);
  }
  class u {
    constructor(e, t) {
      (this.observers = []),
        (this.unsubscribes = []),
        (this.observerCount = 0),
        (this.task = Promise.resolve()),
        (this.finalized = !1),
        (this.onNoObservers = t),
        this.task
          .then(() => {
            e(this);
          })
          .catch((e) => {
            this.error(e);
          });
    }
    next(t) {
      this.forEachObserver((e) => {
        e.next(t);
      });
    }
    error(t) {
      this.forEachObserver((e) => {
        e.error(t);
      }),
        this.close(t);
    }
    complete() {
      this.forEachObserver((e) => {
        e.complete();
      }),
        this.close();
    }
    subscribe(e, t, n) {
      let r;
      if (void 0 === e && void 0 === t && void 0 === n)
        throw new Error("Missing Observer.");
      (r = (function (e, t) {
        if ("object" != typeof e || null === e) return !1;
        for (const n of t) if (n in e && "function" == typeof e[n]) return !0;
        return !1;
      })(e, ["next", "error", "complete"])
        ? e
        : { next: e, error: t, complete: n }),
        void 0 === r.next && (r.next = f),
        void 0 === r.error && (r.error = f),
        void 0 === r.complete && (r.complete = f);
      n = this.unsubscribeOne.bind(this, this.observers.length);
      return (
        this.finalized &&
          this.task.then(() => {
            try {
              this.finalError ? r.error(this.finalError) : r.complete();
            } catch (e) {}
          }),
        this.observers.push(r),
        n
      );
    }
    unsubscribeOne(e) {
      void 0 !== this.observers &&
        void 0 !== this.observers[e] &&
        (delete this.observers[e],
        --this.observerCount,
        0 === this.observerCount &&
          void 0 !== this.onNoObservers &&
          this.onNoObservers(this));
    }
    forEachObserver(t) {
      if (!this.finalized)
        for (let e = 0; e < this.observers.length; e++) this.sendOne(e, t);
    }
    sendOne(e, t) {
      this.task.then(() => {
        if (void 0 !== this.observers && void 0 !== this.observers[e])
          try {
            t(this.observers[e]);
          } catch (e) {
            "undefined" != typeof console && console.error && console.error(e);
          }
      });
    }
    close(e) {
      this.finalized ||
        ((this.finalized = !0),
        void 0 !== e && (this.finalError = e),
        this.task.then(() => {
          (this.observers = void 0), (this.onNoObservers = void 0);
        }));
    }
  }
  function f() {}
  function e(e, s, a, c) {
    return new (a = a || Promise)(function (n, t) {
      function r(e) {
        try {
          o(c.next(e));
        } catch (e) {
          t(e);
        }
      }
      function i(e) {
        try {
          o(c.throw(e));
        } catch (e) {
          t(e);
        }
      }
      function o(e) {
        var t;
        e.done
          ? n(e.value)
          : ((t = e.value) instanceof a
              ? t
              : new a(function (e) {
                  e(t);
                })
            ).then(r, i);
      }
      o((c = c.apply(e, s || [])).next());
    });
  }
  function h(n, r) {
    var i,
      o,
      s,
      a = {
        label: 0,
        sent: function () {
          if (1 & s[0]) throw s[1];
          return s[1];
        },
        trys: [],
        ops: [],
      },
      e = { next: t(0), throw: t(1), return: t(2) };
    return (
      "function" == typeof Symbol &&
        (e[Symbol.iterator] = function () {
          return this;
        }),
      e
    );
    function t(t) {
      return function (e) {
        return (function (t) {
          if (i) throw new TypeError("Generator is already executing.");
          for (; a; )
            try {
              if (
                ((i = 1),
                o &&
                  (s =
                    2 & t[0]
                      ? o.return
                      : t[0]
                      ? o.throw || ((s = o.return) && s.call(o), 0)
                      : o.next) &&
                  !(s = s.call(o, t[1])).done)
              )
                return s;
              switch (((o = 0), (t = s ? [2 & t[0], s.value] : t)[0])) {
                case 0:
                case 1:
                  s = t;
                  break;
                case 4:
                  return a.label++, { value: t[1], done: !1 };
                case 5:
                  a.label++, (o = t[1]), (t = [0]);
                  continue;
                case 7:
                  (t = a.ops.pop()), a.trys.pop();
                  continue;
                default:
                  if (
                    !(s = 0 < (s = a.trys).length && s[s.length - 1]) &&
                    (6 === t[0] || 2 === t[0])
                  ) {
                    a = 0;
                    continue;
                  }
                  if (3 === t[0] && (!s || (t[1] > s[0] && t[1] < s[3]))) {
                    a.label = t[1];
                    break;
                  }
                  if (6 === t[0] && a.label < s[1]) {
                    (a.label = s[1]), (s = t);
                    break;
                  }
                  if (s && a.label < s[2]) {
                    (a.label = s[2]), a.ops.push(t);
                    break;
                  }
                  s[2] && a.ops.pop(), a.trys.pop();
                  continue;
              }
              t = r.call(n, a);
            } catch (e) {
              (t = [6, e]), (o = 0);
            } finally {
              i = s = 0;
            }
          if (5 & t[0]) throw t[1];
          return { value: t[0] ? t[1] : void 0, done: !0 };
        })([t, e]);
      };
    }
  }
  function d(e) {
    var t = "function" == typeof Symbol && Symbol.iterator,
      n = t && e[t],
      r = 0;
    if (n) return n.call(e);
    if (e && "number" == typeof e.length)
      return {
        next: function () {
          return {
            value: (e = e && r >= e.length ? void 0 : e) && e[r++],
            done: !e,
          };
        },
      };
    throw new TypeError(
      t ? "Object is not iterable." : "Symbol.iterator is not defined."
    );
  }
  function m(e, t) {
    var n = "function" == typeof Symbol && e[Symbol.iterator];
    if (!n) return e;
    var r,
      i,
      o = n.call(e),
      s = [];
    try {
      for (; (void 0 === t || 0 < t--) && !(r = o.next()).done; )
        s.push(r.value);
    } catch (e) {
      i = { error: e };
    } finally {
      try {
        r && !r.done && (n = o.return) && n.call(o);
      } finally {
        if (i) throw i.error;
      }
    }
    return s;
  }
  function v(e, t, n) {
    if (n || 2 === arguments.length)
      for (var r, i = 0, o = t.length; i < o; i++)
        (!r && i in t) ||
          ((r = r || Array.prototype.slice.call(t, 0, i))[i] = t[i]);
    return e.concat(r || Array.prototype.slice.call(t));
  }
  var g =
    ((t.prototype.setInstantiationMode = function (e) {
      return (this.instantiationMode = e), this;
    }),
    (t.prototype.setMultipleInstances = function (e) {
      return (this.multipleInstances = e), this;
    }),
    (t.prototype.setServiceProps = function (e) {
      return (this.serviceProps = e), this;
    }),
    (t.prototype.setInstanceCreatedCallback = function (e) {
      return (this.onInstanceCreated = e), this;
    }),
    t);
  function t(e, t, n) {
    (this.name = e),
      (this.instanceFactory = t),
      (this.type = n),
      (this.multipleInstances = !1),
      (this.serviceProps = {}),
      (this.instantiationMode = "LAZY"),
      (this.onInstanceCreated = null);
  }
  var b = "[DEFAULT]",
    y =
      ((I.prototype.get = function (e) {
        var t = this.normalizeInstanceIdentifier(e);
        if (!this.instancesDeferred.has(t)) {
          e = new r();
          if (
            (this.instancesDeferred.set(t, e),
            this.isInitialized(t) || this.shouldAutoInitialize())
          )
            try {
              var n = this.getOrInitializeService({ instanceIdentifier: t });
              n && e.resolve(n);
            } catch (e) {}
        }
        return this.instancesDeferred.get(t).promise;
      }),
      (I.prototype.getImmediate = function (t) {
        var e = this.normalizeInstanceIdentifier(
            null == t ? void 0 : t.identifier
          ),
          t =
            null !== (t = null == t ? void 0 : t.optional) && void 0 !== t && t;
        if (!this.isInitialized(e) && !this.shouldAutoInitialize()) {
          if (t) return null;
          throw Error("Service " + this.name + " is not available");
        }
        try {
          return this.getOrInitializeService({ instanceIdentifier: e });
        } catch (e) {
          if (t) return null;
          throw e;
        }
      }),
      (I.prototype.getComponent = function () {
        return this.component;
      }),
      (I.prototype.setComponent = function (e) {
        var t, n;
        if (e.name !== this.name)
          throw Error(
            "Mismatching Component " +
              e.name +
              " for Provider " +
              this.name +
              "."
          );
        if (this.component)
          throw Error(
            "Component for " + this.name + " has already been provided"
          );
        if (((this.component = e), this.shouldAutoInitialize())) {
          if ("EAGER" === e.instantiationMode)
            try {
              this.getOrInitializeService({ instanceIdentifier: b });
            } catch (e) {}
          try {
            for (
              var r = d(this.instancesDeferred.entries()), i = r.next();
              !i.done;
              i = r.next()
            ) {
              var o = m(i.value, 2),
                s = o[0],
                a = o[1],
                c = this.normalizeInstanceIdentifier(s);
              try {
                var l = this.getOrInitializeService({ instanceIdentifier: c });
                a.resolve(l);
              } catch (e) {}
            }
          } catch (e) {
            t = { error: e };
          } finally {
            try {
              i && !i.done && (n = r.return) && n.call(r);
            } finally {
              if (t) throw t.error;
            }
          }
        }
      }),
      (I.prototype.clearInstance = function (e) {
        this.instancesDeferred.delete((e = void 0 === e ? b : e)),
          this.instancesOptions.delete(e),
          this.instances.delete(e);
      }),
      (I.prototype.delete = function () {
        return e(this, void 0, void 0, function () {
          var t;
          return h(this, function (e) {
            switch (e.label) {
              case 0:
                return (
                  (t = Array.from(this.instances.values())),
                  [
                    4,
                    Promise.all(
                      v(
                        v(
                          [],
                          m(
                            t
                              .filter(function (e) {
                                return "INTERNAL" in e;
                              })
                              .map(function (e) {
                                return e.INTERNAL.delete();
                              })
                          )
                        ),
                        m(
                          t
                            .filter(function (e) {
                              return "_delete" in e;
                            })
                            .map(function (e) {
                              return e._delete();
                            })
                        )
                      )
                    ),
                  ]
                );
              case 1:
                return e.sent(), [2];
            }
          });
        });
      }),
      (I.prototype.isComponentSet = function () {
        return null != this.component;
      }),
      (I.prototype.isInitialized = function (e) {
        return this.instances.has((e = void 0 === e ? b : e));
      }),
      (I.prototype.getOptions = function (e) {
        return this.instancesOptions.get((e = void 0 === e ? b : e)) || {};
      }),
      (I.prototype.initialize = function (e) {
        var t,
          n,
          r = (e = void 0 === e ? {} : e).options,
          r = void 0 === r ? {} : r,
          i = this.normalizeInstanceIdentifier(e.instanceIdentifier);
        if (this.isInitialized(i))
          throw Error(this.name + "(" + i + ") has already been initialized");
        if (!this.isComponentSet())
          throw Error(
            "Component " + this.name + " has not been registered yet"
          );
        var o = this.getOrInitializeService({
          instanceIdentifier: i,
          options: r,
        });
        try {
          for (
            var s = d(this.instancesDeferred.entries()), a = s.next();
            !a.done;
            a = s.next()
          ) {
            var c = m(a.value, 2),
              l = c[0],
              p = c[1];
            i === this.normalizeInstanceIdentifier(l) && p.resolve(o);
          }
        } catch (e) {
          t = { error: e };
        } finally {
          try {
            a && !a.done && (n = s.return) && n.call(s);
          } finally {
            if (t) throw t.error;
          }
        }
        return o;
      }),
      (I.prototype.onInit = function (e, t) {
        var n = this.normalizeInstanceIdentifier(t),
          r =
            null !== (t = this.onInitCallbacks.get(n)) && void 0 !== t
              ? t
              : new Set();
        r.add(e), this.onInitCallbacks.set(n, r);
        t = this.instances.get(n);
        return (
          t && e(t, n),
          function () {
            r.delete(e);
          }
        );
      }),
      (I.prototype.invokeOnInitCallbacks = function (e, t) {
        var n,
          r,
          i = this.onInitCallbacks.get(t);
        if (i)
          try {
            for (var o = d(i), s = o.next(); !s.done; s = o.next()) {
              var a = s.value;
              try {
                a(e, t);
              } catch (e) {}
            }
          } catch (e) {
            n = { error: e };
          } finally {
            try {
              s && !s.done && (r = o.return) && r.call(o);
            } finally {
              if (n) throw n.error;
            }
          }
      }),
      (I.prototype.getOrInitializeService = function (e) {
        var t = e.instanceIdentifier,
          n = e.options,
          r = void 0 === n ? {} : n,
          e = this.instances.get(t);
        if (
          !e &&
          this.component &&
          ((e = this.component.instanceFactory(this.container, {
            instanceIdentifier: (n = t) === b ? void 0 : n,
            options: r,
          })),
          this.instances.set(t, e),
          this.instancesOptions.set(t, r),
          this.invokeOnInitCallbacks(e, t),
          this.component.onInstanceCreated)
        )
          try {
            this.component.onInstanceCreated(this.container, t, e);
          } catch (e) {}
        return e || null;
      }),
      (I.prototype.normalizeInstanceIdentifier = function (e) {
        return (
          void 0 === e && (e = b),
          !this.component || this.component.multipleInstances ? e : b
        );
      }),
      (I.prototype.shouldAutoInitialize = function () {
        return (
          !!this.component && "EXPLICIT" !== this.component.instantiationMode
        );
      }),
      I);
  function I(e, t) {
    (this.name = e),
      (this.container = t),
      (this.component = null),
      (this.instances = new Map()),
      (this.instancesDeferred = new Map()),
      (this.instancesOptions = new Map()),
      (this.onInitCallbacks = new Map());
  }
  var w,
    E =
      ((_.prototype.addComponent = function (e) {
        var t = this.getProvider(e.name);
        if (t.isComponentSet())
          throw new Error(
            "Component " +
              e.name +
              " has already been registered with " +
              this.name
          );
        t.setComponent(e);
      }),
      (_.prototype.addOrOverwriteComponent = function (e) {
        this.getProvider(e.name).isComponentSet() &&
          this.providers.delete(e.name),
          this.addComponent(e);
      }),
      (_.prototype.getProvider = function (e) {
        if (this.providers.has(e)) return this.providers.get(e);
        var t = new y(e, this);
        return this.providers.set(e, t), t;
      }),
      (_.prototype.getProviders = function () {
        return Array.from(this.providers.values());
      }),
      _);
  function _(e) {
    (this.name = e), (this.providers = new Map());
  }
  const O = [];
  ((te = w = w || {})[(te.DEBUG = 0)] = "DEBUG"),
    (te[(te.VERBOSE = 1)] = "VERBOSE"),
    (te[(te.INFO = 2)] = "INFO"),
    (te[(te.WARN = 3)] = "WARN"),
    (te[(te.ERROR = 4)] = "ERROR"),
    (te[(te.SILENT = 5)] = "SILENT");
  const C = {
      debug: w.DEBUG,
      verbose: w.VERBOSE,
      info: w.INFO,
      warn: w.WARN,
      error: w.ERROR,
      silent: w.SILENT,
    },
    N = w.INFO,
    L = {
      [w.DEBUG]: "log",
      [w.VERBOSE]: "log",
      [w.INFO]: "info",
      [w.WARN]: "warn",
      [w.ERROR]: "error",
    },
    A = (e, t, ...n) => {
      if (!(t < e.logLevel)) {
        var r = new Date().toISOString(),
          i = L[t];
        if (!i)
          throw new Error(
            `Attempted to log a message with an invalid logType (value: ${t})`
          );
        console[i](`[${r}]  ${e.name}:`, ...n);
      }
    };
  class S {
    constructor(e) {
      (this.name = e),
        (this._logLevel = N),
        (this._logHandler = A),
        (this._userLogHandler = null),
        O.push(this);
    }
    get logLevel() {
      return this._logLevel;
    }
    set logLevel(e) {
      if (!(e in w))
        throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);
      this._logLevel = e;
    }
    setLogLevel(e) {
      this._logLevel = "string" == typeof e ? C[e] : e;
    }
    get logHandler() {
      return this._logHandler;
    }
    set logHandler(e) {
      if ("function" != typeof e)
        throw new TypeError(
          "Value assigned to `logHandler` must be a function"
        );
      this._logHandler = e;
    }
    get userLogHandler() {
      return this._userLogHandler;
    }
    set userLogHandler(e) {
      this._userLogHandler = e;
    }
    debug(...e) {
      this._userLogHandler && this._userLogHandler(this, w.DEBUG, ...e),
        this._logHandler(this, w.DEBUG, ...e);
    }
    log(...e) {
      this._userLogHandler && this._userLogHandler(this, w.VERBOSE, ...e),
        this._logHandler(this, w.VERBOSE, ...e);
    }
    info(...e) {
      this._userLogHandler && this._userLogHandler(this, w.INFO, ...e),
        this._logHandler(this, w.INFO, ...e);
    }
    warn(...e) {
      this._userLogHandler && this._userLogHandler(this, w.WARN, ...e),
        this._logHandler(this, w.WARN, ...e);
    }
    error(...e) {
      this._userLogHandler && this._userLogHandler(this, w.ERROR, ...e),
        this._logHandler(this, w.ERROR, ...e);
    }
  }
  class D {
    constructor(e) {
      this.container = e;
    }
    getPlatformInfoString() {
      const e = this.container.getProviders();
      return e
        .map((e) => {
          if (
            (function (e) {
              e = e.getComponent();
              return "VERSION" === (null == e ? void 0 : e.type);
            })(e)
          ) {
            e = e.getImmediate();
            return `${e.library}/${e.version}`;
          }
          return null;
        })
        .filter((e) => e)
        .join(" ");
    }
  }
  const R = "@firebase/app",
    k = "0.7.4",
    P = new S("@firebase/app");
  var z;
  const F = "[DEFAULT]",
    j = {
      "@firebase/app": "fire-core",
      "@firebase/app-compat": "fire-core-compat",
      "@firebase/analytics": "fire-analytics",
      "@firebase/analytics-compat": "fire-analytics-compat",
      "@firebase/app-check": "fire-app-check",
      "@firebase/app-check-compat": "fire-app-check-compat",
      "@firebase/auth": "fire-auth",
      "@firebase/auth-compat": "fire-auth-compat",
      "@firebase/database": "fire-rtdb",
      "@firebase/database-compat": "fire-rtdb-compat",
      "@firebase/functions": "fire-fn",
      "@firebase/functions-compat": "fire-fn-compat",
      "@firebase/installations": "fire-iid",
      "@firebase/installations-compat": "fire-iid-compat",
      "@firebase/messaging": "fire-fcm",
      "@firebase/messaging-compat": "fire-fcm-compat",
      "@firebase/performance": "fire-perf",
      "@firebase/performance-compat": "fire-perf-compat",
      "@firebase/remote-config": "fire-rc",
      "@firebase/remote-config-compat": "fire-rc-compat",
      "@firebase/storage": "fire-gcs",
      "@firebase/storage-compat": "fire-gcs-compat",
      "@firebase/firestore": "fire-fst",
      "@firebase/firestore-compat": "fire-fst-compat",
      "fire-js": "fire-js",
      firebase: "fire-js-all",
    },
    $ = new Map(),
    T = new Map();
  function H(t, n) {
    try {
      t.container.addComponent(n);
    } catch (e) {
      P.debug(
        `Component ${n.name} failed to register with FirebaseApp ${t.name}`,
        e
      );
    }
  }
  function x(e, t) {
    e.container.addOrOverwriteComponent(t);
  }
  function M(e) {
    var t = e.name;
    if (T.has(t))
      return (
        P.debug(`There were multiple attempts to register component ${t}.`), !1
      );
    T.set(t, e);
    for (const n of $.values()) H(n, e);
    return !0;
  }
  function B(e, t) {
    return e.container.getProvider(t);
  }
  const V = new o("app", "Firebase", {
    "no-app":
      "No Firebase App '{$appName}' has been created - call Firebase App.initializeApp()",
    "bad-app-name": "Illegal App name: '{$appName}",
    "duplicate-app":
      "Firebase App named '{$appName}' already exists with different options or config",
    "app-deleted": "Firebase App named '{$appName}' already deleted",
    "invalid-app-argument":
      "firebase.{$appName}() takes either no argument or a Firebase App instance.",
    "invalid-log-argument":
      "First argument to `onLog` must be null or a function.",
  });
  class U {
    constructor(e, t, n) {
      (this._isDeleted = !1),
        (this._options = Object.assign({}, e)),
        (this._config = Object.assign({}, t)),
        (this._name = t.name),
        (this._automaticDataCollectionEnabled =
          t.automaticDataCollectionEnabled),
        (this._container = n),
        this.container.addComponent(new g("app", () => this, "PUBLIC"));
    }
    get automaticDataCollectionEnabled() {
      return this.checkDestroyed(), this._automaticDataCollectionEnabled;
    }
    set automaticDataCollectionEnabled(e) {
      this.checkDestroyed(), (this._automaticDataCollectionEnabled = e);
    }
    get name() {
      return this.checkDestroyed(), this._name;
    }
    get options() {
      return this.checkDestroyed(), this._options;
    }
    get config() {
      return this.checkDestroyed(), this._config;
    }
    get container() {
      return this._container;
    }
    get isDeleted() {
      return this._isDeleted;
    }
    set isDeleted(e) {
      this._isDeleted = e;
    }
    checkDestroyed() {
      if (this.isDeleted)
        throw V.create("app-deleted", { appName: this._name });
    }
  }
  const G = "9.1.3";
  function W(e, t = {}) {
    if ("object" != typeof t) {
      const r = t;
      t = { name: r };
    }
    var n = Object.assign({ name: F, automaticDataCollectionEnabled: !1 }, t);
    const r = n.name;
    if ("string" != typeof r || !r)
      throw V.create("bad-app-name", { appName: String(r) });
    t = $.get(r);
    if (t) {
      if (l(e, t.options) && l(n, t.config)) return t;
      throw V.create("duplicate-app", { appName: r });
    }
    const i = new E(r);
    for (const o of T.values()) i.addComponent(o);
    n = new U(e, n, i);
    return $.set(r, n), n;
  }
  async function Y(e) {
    var t = e.name;
    $.has(t) &&
      ($.delete(t),
      await Promise.all(e.container.getProviders().map((e) => e.delete())),
      (e.isDeleted = !0));
  }
  function K(e, t, n) {
    var r;
    let i = null !== (r = j[e]) && void 0 !== r ? r : e;
    n && (i += `-${n}`);
    (e = i.match(/\s|\//)), (n = t.match(/\s|\//));
    if (e || n) {
      const o = [`Unable to register library "${i}" with version "${t}":`];
      return (
        e &&
          o.push(
            `library name "${i}" contains illegal characters (whitespace or "/")`
          ),
        e && n && o.push("and"),
        n &&
          o.push(
            `version name "${t}" contains illegal characters (whitespace or "/")`
          ),
        void P.warn(o.join(" "))
      );
    }
    M(new g(`${i}-version`, () => ({ library: i, version: t }), "VERSION"));
  }
  function J(e, t) {
    if (null !== e && "function" != typeof e)
      throw V.create("invalid-log-argument");
    !(function (o, e) {
      for (const t of O) {
        let i = null;
        e && e.level && (i = C[e.level]),
          (t.userLogHandler =
            null === o
              ? null
              : (e, t, ...n) => {
                  var r = n
                    .map((e) => {
                      if (null == e) return null;
                      if ("string" == typeof e) return e;
                      if ("number" == typeof e || "boolean" == typeof e)
                        return e.toString();
                      if (e instanceof Error) return e.message;
                      try {
                        return JSON.stringify(e);
                      } catch (e) {
                        return null;
                      }
                    })
                    .filter((e) => e)
                    .join(" ");
                  t >= (null !== i && void 0 !== i ? i : e.logLevel) &&
                    o({
                      level: w[t].toLowerCase(),
                      message: r,
                      args: n,
                      type: e.name,
                    });
                });
      }
    })(e, t);
  }
  function X(e) {
    var t;
    (t = e),
      O.forEach((e) => {
        e.setLogLevel(t);
      });
  }
  (z = ""),
    M(new g("platform-logger", (e) => new D(e), "PRIVATE")),
    K(R, k, z),
    K(R, k, "esm2017"),
    K("fire-js", "");
  var Z = Object.freeze({
    __proto__: null,
    SDK_VERSION: G,
    _DEFAULT_ENTRY_NAME: F,
    _addComponent: H,
    _addOrOverwriteComponent: x,
    _apps: $,
    _clearComponents: function () {
      T.clear();
    },
    _components: T,
    _getProvider: B,
    _registerComponent: M,
    _removeServiceInstance: function (e, t, n = F) {
      B(e, t).clearInstance(n);
    },
    deleteApp: Y,
    getApp: function (e = F) {
      var t = $.get(e);
      if (!t) throw V.create("no-app", { appName: e });
      return t;
    },
    getApps: function () {
      return Array.from($.values());
    },
    initializeApp: W,
    onLog: J,
    registerVersion: K,
    setLogLevel: X,
    FirebaseError: i,
  });
  class q {
    constructor(e, t) {
      (this._delegate = e),
        (this.firebase = t),
        H(e, new g("app-compat", () => this, "PUBLIC")),
        (this.container = e.container);
    }
    get automaticDataCollectionEnabled() {
      return this._delegate.automaticDataCollectionEnabled;
    }
    set automaticDataCollectionEnabled(e) {
      this._delegate.automaticDataCollectionEnabled = e;
    }
    get name() {
      return this._delegate.name;
    }
    get options() {
      return this._delegate.options;
    }
    delete() {
      return new Promise((e) => {
        this._delegate.checkDestroyed(), e();
      }).then(
        () => (this.firebase.INTERNAL.removeApp(this.name), Y(this._delegate))
      );
    }
    _getService(e, t = F) {
      this._delegate.checkDestroyed();
      const n = this._delegate.container.getProvider(e);
      return (
        n.isInitialized() ||
          "EXPLICIT" !==
            (null === (e = n.getComponent()) || void 0 === e
              ? void 0
              : e.instantiationMode) ||
          n.initialize(),
        n.getImmediate({ identifier: t })
      );
    }
    _removeServiceInstance(e, t = F) {
      this._delegate.container.getProvider(e).clearInstance(t);
    }
    _addComponent(e) {
      H(this._delegate, e);
    }
    _addOrOverwriteComponent(e) {
      x(this._delegate, e);
    }
    toJSON() {
      return {
        name: this.name,
        automaticDataCollectionEnabled: this.automaticDataCollectionEnabled,
        options: this.options,
      };
    }
  }
  const Q = new o("app-compat", "Firebase", {
    "no-app":
      "No Firebase App '{$appName}' has been created - call Firebase App.initializeApp()",
    "invalid-app-argument":
      "firebase.{$appName}() takes either no argument or a Firebase App instance.",
  });
  function ee(i) {
    const n = {},
      o = {
        __esModule: !0,
        initializeApp: function (e, t = {}) {
          e = W(e, t);
          if (c(n, e.name)) return n[e.name];
          t = new i(e, o);
          return (n[e.name] = t);
        },
        app: s,
        registerVersion: K,
        setLogLevel: X,
        onLog: J,
        apps: null,
        SDK_VERSION: G,
        INTERNAL: {
          registerComponent: function (n) {
            const r = n.name,
              t = r.replace("-compat", "");
            {
              var e;
              M(n) &&
                "PUBLIC" === n.type &&
                ((e = (e = s()) => {
                  if ("function" != typeof e[t])
                    throw Q.create("invalid-app-argument", { appName: r });
                  return e[t]();
                }),
                void 0 !== n.serviceProps && a(e, n.serviceProps),
                (o[t] = e),
                (i.prototype[t] = function (...e) {
                  const t = this._getService.bind(this, r);
                  return t.apply(this, n.multipleInstances ? e : []);
                }));
            }
            return "PUBLIC" === n.type ? o[t] : null;
          },
          removeApp: function (e) {
            delete n[e];
          },
          useAsService: function (e, t) {
            if ("serverAuth" === t) return null;
            return t;
          },
          modularAPIs: Z,
        },
      };
    function s(e) {
      if (((e = e || F), !c(n, e))) throw Q.create("no-app", { appName: e });
      return n[e];
    }
    return (
      (o.default = o),
      Object.defineProperty(o, "apps", {
        get: function () {
          return Object.keys(n).map((e) => n[e]);
        },
      }),
      (s.App = i),
      o
    );
  }
  var te = (function e() {
    const t = ee(q);
    return (
      (t.INTERNAL = Object.assign(Object.assign({}, t.INTERNAL), {
        createFirebaseNamespace: e,
        extendNamespace: function (e) {
          a(t, e);
        },
        createSubscribe: n,
        ErrorFactory: o,
        deepExtend: a,
      })),
      t
    );
  })();
  const ne = new S("@firebase/app-compat");
  if (
    "object" == typeof self &&
    self.self === self &&
    void 0 !== self.firebase
  ) {
    ne.warn(`
    Warning: Firebase is already defined in the global scope. Please make sure
    Firebase library is only loaded once.
  `);
    const ie = self.firebase.SDK_VERSION;
    ie &&
      0 <= ie.indexOf("LITE") &&
      ne.warn(`
    Warning: You are trying to load Firebase while using Firebase Performance standalone script.
    You should load Firebase Performance with this instance of Firebase to avoid loading duplicate code.
    `);
  }
  const re = te;
  K("@firebase/app-compat", "0.1.5", void 0);
  return re.registerVersion("firebase", "9.1.3", "app-compat-cdn"), re;
});
//# sourceMappingURL=firebase-app-compat.js.map
