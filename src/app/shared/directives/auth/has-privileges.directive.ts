import { Directive, Input, TemplateRef, ViewContainerRef } from '@angular/core';
import { AuthenticationService } from '@shared/services/auth/authentication.service';

/**
 * Check rules
 *
 * <AUTHOR>
 * @date 2021-12-24
 * @export HasPrivilegesDirective
 * @class HasPrivilegesDirective
 * @howToUse
 * ```
 *     <some-element *hasPrivileges="'USER_READ'">...</some-element>
 *
 *     <some-element *hasPrivileges="['USER_READ', 'USER_CREATE']">...</some-element>
 * ```
 */
@Directive({
  selector: '[hasPrivileges]',
})
export class HasPrivilegesDirective {
  private privileges: string[] = [];

  constructor(
    private authenticationService: AuthenticationService,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    private templateRef: TemplateRef<any>,
    public viewContainerRef: ViewContainerRef
  ) {}

  @Input() set hasPrivileges(value: string | string[]) {
    this.privileges = Array.isArray(value) ? value : [value];
    this.updateView();
    // Get notified each time authentication state changes.
    this.authenticationService
      .getAuthenticationState()
      .subscribe(() => this.updateView());
  }

  private updateView(): void {
    if (this.privileges?.length > 0) {
      const hasPrivilege = this.authenticationService.hasAnyPrivileges(
        this.privileges
      );
      this.viewContainerRef.clear();
      if (hasPrivilege) {
        this.viewContainerRef.createEmbeddedView(this.templateRef);
      }
    } else {
      this.viewContainerRef.clear();
      this.viewContainerRef.createEmbeddedView(this.templateRef);
    }
  }
}
