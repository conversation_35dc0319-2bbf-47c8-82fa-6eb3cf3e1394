import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ICashoutCreate } from '@shared/models/cashout.model';
import { MODEL_MAP_ITEM_COMMON } from '@shared/models/model.type';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { PaymentService } from '@shared/services/payment.service';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-payment-service-create',
  templateUrl: './payment-service-create.component.html',
  styleUrls: ['./payment-service-create.component.scss'],
})
export class PaymentServiceCreateComponent implements OnInit {
  formCreate: FormGroup = new FormGroup({});

  SYSTEM_RULES = SYSTEM_RULES;
  VALIDATORS = VALIDATORS;
  actionConfirm!: MODEL_MAP_ITEM_COMMON;
  constructor(
    public activeModal: NgbActiveModal,
    private fb: FormBuilder,
    private paymentService: PaymentService,
    private toastService: ToastrCustomService
  ) {
    this.formCreate = this.fb.group({
      accountNumber: [
        '',
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.MERCHANT_NUMBER),
          Validators.maxLength(
            VALIDATORS.LENGTH.CASHOUT_ACCOUNT_NUMBER_MAX_LENGTH
          ),
        ],
      ],
      accountName: ['', [Validators.required]],
      transactionLimit: [
        '',
        [
          Validators.required,
          Validators.maxLength(VALIDATORS.LENGTH.TRANSACTION_LIMIT_MAX_LENGTH),
        ],
      ],
      cif: [
        '',
        [Validators.required, Validators.pattern(VALIDATORS.PATTERN.NUMBER)],
      ],
    });
  }

  ngOnInit(): void {}

  onCreate() {
    const body = {
      ...this.formCreate.value,
      transactionLimit: this.formCreate.value.transactionLimit
        ?.split('.')
        .join(''),
    } as ICashoutCreate;
    if (this.formCreate.invalid) {
      CommonUtils.markFormGroupTouched(this.formCreate);
      return;
    }
    this.paymentService.create(body).subscribe((res) => {
      this.toastService.success('common.action.createSuccess');
      this.activeModal.close(this.actionConfirm.code);
    });
  }
}
