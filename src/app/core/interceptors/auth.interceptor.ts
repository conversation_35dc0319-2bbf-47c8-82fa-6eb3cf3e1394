import {
  <PERSON>tt<PERSON><PERSON><PERSON>,
  <PERSON>tt<PERSON><PERSON><PERSON><PERSON>,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { HTTP_HEADERS } from '@shared/constants/http.constants';
import { LANGUAGES } from '@shared/constants/language.constants';
import {
  STORAGE_LANGUAGES,
  STORAGE_TOKEN,
} from '@shared/constants/storage.constants';
import { CookieService } from 'ngx-cookie-service';
import { LocalStorageService } from 'ngx-webstorage';
import { Observable } from 'rxjs';
import {
  DEVICE_TOKEN_CONST,
  SERVER_API_URL,
} from 'src/app/shared/constants/app.constants';
import { v4 as uuIdv4 } from 'uuid';
@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(
    private $cookie: CookieService,
    private localStorage: LocalStorageService
  ) {}

  intercept(
    request: HttpRequest<any>,
    next: <PERSON>ttpHand<PERSON>
  ): Observable<HttpEvent<any>> {
    if (
      !request ||
      !request.url ||
      (/^http/.test(request.url) &&
        !(SERVER_API_URL && request.url.startsWith(SERVER_API_URL)))
    ) {
      return next.handle(request);
    }

    const csrfToken = this.$cookie.get(STORAGE_TOKEN.CSRF_TOKEN);

    // const headers = new HttpHeaders({
    //   ...request.headers,
    //   [HTTP_HEADERS.AUTHORIZATION]: `${HTTP_HEADERS.AUTHORIZATION_TYPE}${csrfToken}`,
    // });

    request = request.clone({
      headers: request.headers
        .set(
          HTTP_HEADERS.AUTHORIZATION,
          `${HTTP_HEADERS.AUTHORIZATION_TYPE}${csrfToken}`
        )
        .set(
          HTTP_HEADERS.LOCALE,
          this.localStorage.retrieve(STORAGE_LANGUAGES) || LANGUAGES.VI.code
        )
        .set(HTTP_HEADERS.DEVICE_TOKEN, DEVICE_TOKEN_CONST)
        .set(HTTP_HEADERS.CLIENT_MESSAGE_ID, uuIdv4())
        .set(
          HTTP_HEADERS.TIME_ZONE,
          Intl.DateTimeFormat().resolvedOptions().timeZone
        ),
      withCredentials: true,
    });

    return next.handle(request);
  }
}
