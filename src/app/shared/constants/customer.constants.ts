export const APPROVAL_TYPE_CONST = {
  CREATE: {
    label: 'common.action.create',
    value: 1,
  },
  UPDATE: {
    label: 'common.action.update',
    value: 2,
  },
  LOCK: {
    label: 'common.action.lock',
    value: 3,
  },
  UNLOCK: {
    label: 'common.action.unlock',
    value: 4,
  },
  DELETE: {
    label: 'common.action.close',
    value: 5,
  },
  UNCLOSED: {
    label: 'common.action.unclosed',
    value: 7,
  },
};

export const APPROVAL_TYPE = Object.values(APPROVAL_TYPE_CONST);

export const APPROVAL_TYPE_MAP = APPROVAL_TYPE.reduce(
  (data, valueMap) => ({ ...data, [valueMap.value]: valueMap.label }),
  {}
) as any;

export const APPROVAL_STATUS_CONST = {
  // DRAFT: {
  //   label: 'common.action.draft',
  //   value: 0,
  // },
  PENDING: {
    label: 'common.action.pending',
    value: 1,
  },
  DENY: {
    label: 'common.action.deny',
    value: 2,
  },
  APPROVAL: {
    label: 'common.action.approved',
    value: 3,
  },
  CANCEL: {
    label: 'common.action.cancelApproval',
    value: 4,
  },
  EXPIRED: {
    label: 'common.action.approved',
    value: 5,
  },
  APPROVAL_ERROR: {
    label: 'common.action.approvalError',
    value: 6,
  },
};

export const APPROVAL_STATUS = Object.values(APPROVAL_STATUS_CONST);

export const APPROVAL_STATUS_MAP = APPROVAL_STATUS.reduce(
  (data, valueMap) => ({ ...data, [valueMap.value]: valueMap.label }),
  {}
) as any;

export const CUSTOMER_STATUS_CONST = {
  ACTIVE: {
    label: 'common.active',
    value: 1,
    style: 'badge-success',
  },
  INACTIVE: {
    label: 'common.inactive',
    value: 0,
    style: 'badge-danger',
  },
  CANCEL: {
    label: 'common.closed',
    value: 4,
    style: 'badge-secondary',
  },
};

export const CUSTOMER_STATUS = Object.values(CUSTOMER_STATUS_CONST);

export const CUSTOMER_STATUS_MAP = CUSTOMER_STATUS.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.value]: { label: valueMap.label, style: valueMap.style },
  }),
  {}
) as any;

export const MARITAL_STATUS_CONST = {
  SINGLE: {
    label: 'model.customer.maritalStatusOption.single',
    value: 0,
  },
  MARRIED: {
    label: 'model.customer.maritalStatusOption.married',
    value: 1,
  },
  OTHER: {
    label: 'model.customer.maritalStatusOption.other',
    value: 2,
  },
};

export const MARITAL_STATUS = Object.values(MARITAL_STATUS_CONST);

export const MARITAL_STATUS_MAP = MARITAL_STATUS.reduce(
  (data, valueMap) => ({ ...data, [valueMap.value]: valueMap.label }),
  {}
) as any;

export const RESIDENT_STATUS_CONST = {
  TEMPORARY: {
    label: 'model.customer.residentStatusOption.temporary',
    value: 0,
  },
  PERMANENT: {
    label: 'model.customer.residentStatusOption.permanent',
    value: 1,
  },
};

export const RESIDENT_STATUS = Object.values(RESIDENT_STATUS_CONST);

export const RESIDENT_STATUS_MAP = RESIDENT_STATUS.reduce(
  (data, valueMap) => ({ ...data, [valueMap.value]: valueMap.label }),
  {}
) as any;

export const QUANTITY_FILE_ID_CARD_CUSTOMER_UPLOAD = 1;

export const MINUS_YEAR = {
  DAY_OF_BIRTH: 18,
  ISSUE_DATE: 5,
  ISSUE_DATE_PASSPORT: 10,
};

export const ID_CARD_TYPE = {
  CMND: 'CMND',
  HO_CHIEU: 'HO.CHIEU',
};

export const CUSTOMER_SECTOR_ID_CONST = {
  SECTOR_1740: {
    label: 'model.customer.customerSector.customerId1740',
    value: 1740,
  },
  SECTOR_1700: {
    label: 'model.customer.customerSector.customerId1700',
    value: 1700,
  },
  SECTOR_1890: {
    label: 'model.customer.customerSector.customerId1890',
    value: 1890,
  },
  SECTOR_1891: {
    label: 'model.customer.customerSector.customerId1891',
    value: 1891,
  },
  SECTOR_1742: {
    label: 'model.customer.customerSector.customerId1742',
    value: 1742,
  },
};

export const CUSTOMER_SECTOR_ID = Object.values(CUSTOMER_SECTOR_ID_CONST);

export const CUSTOMER_SECTOR_ID_MAP = CUSTOMER_SECTOR_ID.reduce(
  (data, valueMap) => ({ ...data, [valueMap.value]: valueMap.label }),
  {}
) as any;

export const CUSTOMER_TYPE_CONST = {
  MB: {
    value: 0,
    label: 'model.customer.customerType.customerMB',
  },
  Umoney: {
    value: 1,
    label: 'model.customer.customerType.customerUmoney',
  },
};

export const CUSTOMER_TYPE = Object.values(CUSTOMER_TYPE_CONST);

export const CUSTOMER_TYPE_MAP = CUSTOMER_TYPE.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.value]: valueMap.label,
  }),
  {}
) as any;

export const CUSTOMER_ACTIVE_STATUS_CONST = {
  ACTIVATED: {
    label: 'common.activated',
    value: 1,
    style: 'badge-success',
  },
  INACTIVATED: {
    label: 'common.notActivated',
    value: 0,
    style: 'badge-secondary',
  },
};

export const CUSTOMER_ACTIVE_STATUS = Object.values(
  CUSTOMER_ACTIVE_STATUS_CONST
);

export const CUSTOMER_ACTIVE_STATUS_MAP = CUSTOMER_ACTIVE_STATUS.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.value]: { label: valueMap.label, style: valueMap.style },
  }),
  {}
) as any;

export const ACCOUNT_TYPE_CONST = {
  ACTIVATED: {
    label: 'customerRegisterManagement.paymentAccount',
    value: 1001,
    style: 'badge-success',
  },
  ACTIVATE: {
    label: 'customerRegisterManagement.overdraftAccount',
    value: 1005,
    style: 'badge-success',
  },
  INACTIVATED: {
    label: '',
    value: 0,
    style: 'badge-secondary',
  },
};

export const ACCOUNT_TYPE = Object.values(ACCOUNT_TYPE_CONST);

export const ACCOUNT_TYPE_MAP = ACCOUNT_TYPE.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.value]: { label: valueMap.label, style: valueMap.style },
  }),
  {}
) as any;

export const CUSTOMER_SECTOR_CONST = {
  SECTOR_1890: {
    label: 'model.customer.customerSector.customerSectorId1890',
    value: 1890,
  },
  SECTOR_1891: {
    label: 'model.customer.customerSector.customerSectorId1891',
    value: 1891,
  },
  SECTOR_1740: {
    label: 'model.customer.customerSector.customerId1740',
    value: 1740,
  },
};

export const CUSTOMER_SECTOR = Object.values(CUSTOMER_SECTOR_CONST);

export const CUSTOMER_SECTOR_MAP = CUSTOMER_SECTOR.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.value]: { label: valueMap.label },
  }),
  {}
) as any;

export const NO_TRANSACTION_STATUS_CONST = {
  ACTIVE: {
    label: 'common.active',
    value: 1,
    style: 'badge-success',
  },
  CANCEL: {
    label: 'common.closed',
    value: 4,
    style: 'badge-danger',
  },
};

export const NO_TRANSACTION_STATUS = Object.values(NO_TRANSACTION_STATUS_CONST);

export const NO_TRANSACTION_STATUS_MAP = NO_TRANSACTION_STATUS.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.value]: { label: valueMap.label, style: valueMap.style },
  }),
  {}
) as any;

export const PREMIUM_ACCOUNT_NUMBER_OBJECT = {
  ACCOUNT_NUMBER: 'accountNumber',
  PRICE: 'price',
  DISCOUNT: 'discount',
  TOTAL_PRICE: 'totalPrice',
  STRUCTURE: 'structure',
  IS_SPECICAL: 'isSpecial'
};
