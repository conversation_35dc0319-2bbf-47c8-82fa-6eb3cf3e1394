<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formLapnetReportSearch" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-md-6 col-lg-3">
            <div class="form-group">
              <label>{{ "common.action.searchKeyword" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="keyword"
                class="w-100"
                class="form-control"
                [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
                placeholder="{{ 'common.action.searchKeyword' | placeholder }}"
              />
            </div>
          </div>
          <div class="col-md-8 col-lg-4">
            <div class="date-picker-container form-group row">
              <div class="col-md-6">
                <label
                  >{{ "common.action.fromDate" | translate
                  }}<span class="text-danger">*</span></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="fromDate"
                    formControlName="fromDate"
                    placeholder="DD/MM/YYYY"
                    [max]="maxDate"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="fromDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #fromDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formLapnetReportSearch.get('fromDate')?.errors?.required &&
                    formLapnetReportSearch.get('fromDate')?.touched
                  "
                >
                  {{ "error.required.fromDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formLapnetReportSearch.get('fromDate')?.errors
                      ?.invalidDate &&
                    formLapnetReportSearch.get('fromDate')?.touched
                  "
                >
                  {{ "error.required.inValidDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formLapnetReportSearch.get('fromDate')?.errors
                      ?.invalidMaxDate &&
                    formLapnetReportSearch.get('fromDate')?.touched
                  "
                >
                  {{
                    "error.maxDateCurrent"
                      | translate
                        : {
                            param: "common.action.fromDate" | translate
                          }
                  }}
                </small>
              </div>
              <div class="col-md-6">
                <label
                  >{{ "common.action.toDate" | translate
                  }}<span class="text-danger">*</span></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="toDate"
                    formControlName="toDate"
                    placeholder="DD/MM/YYYY"
                    min="{{
                      formLapnetReportSearch.controls['fromDate'].value
                        | date : 'yyyy-MM-dd'
                    }}"
                    [max]="maxToDate"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="toDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #toDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formLapnetReportSearch.get('toDate')?.errors?.required &&
                    formLapnetReportSearch.get('toDate')?.touched
                  "
                >
                  {{ "error.required.toDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formLapnetReportSearch.get('toDate')?.errors
                      ?.invalidMaxDate &&
                    formLapnetReportSearch.get('toDate')?.touched
                  "
                >
                  {{
                    "error.maxDateCurrent"
                      | translate
                        : {
                            param: "common.action.toDate" | translate
                          }
                  }}
                </small>
              </div>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
            >
              <div class="btn-reset">
                <i
                  class="bi bi-arrow-clockwise"
                  type="button"
                  (click)="onReset()"
                >
                </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" type="submit">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-3 mt-4">
          <button class="btn btn-red mr-2 w-auto" (click)="onAsyncDateReport()">
            {{ "common.action.syncReport" | translate }}
          </button>
          <button
            class="btn btn-red mr-2 w-auto"
            (click)="onConfigAutoReportLapNet()"
            *hasPrivileges="SYSTEM_RULES.LAPNET_AUTOMATIC_REPORT_EXPORT"
          >
            {{ "model.report.transaction.configAutoButton" | translate }}
          </button>
          <button
            class="btn btn-red mr-2"
            type="button"
            (click)="exportFile()"
            *hasPrivileges="SYSTEM_RULES.DEBIT_DEPOSIT_ASYNC_EXPORT"
          >
            {{ "common.action.export" | translate }}
          </button>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table table-lapnet">
          <thead>
            <tr>
              <th class="text-center" scope="col">
                {{ "common.no" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "model.report.lapnet.id" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "model.report.lapnet.time" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.report.lapnet.referenceNumber" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.report.lapnet.fromMember" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.report.lapnet.fromUser" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{ "model.report.lapnet.fromAccount" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "model.report.lapnet.toType" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{ "model.report.lapnet.toAccount" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.report.lapnet.toMember" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{ "model.report.lapnet.amount" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "model.report.lapnet.currency" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.report.lapnet.purpose" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{ "model.report.lapnet.fee" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of lapnetReport; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td class="text-center">{{ dataItem.transactionId }}</td>
              <td class="text-center">
                {{ dataItem.time }}
              </td>
              <td class="text-left">{{ dataItem.referenceNumber }}</td>
              <td class="text-left">{{ dataItem.fromMember }}</td>
              <td class="text-left">{{ dataItem.fromUser }}</td>
              <td class="text-right">{{ dataItem.fromAccount }}</td>
              <td class="text-center" [title]="dataItem.toType">
                {{ dataItem.toType | limitWord }}
              </td>
              <td class="text-right" [title]="dataItem.toAccount">
                {{ dataItem.toAccount | limitWord : 20 }}
              </td>
              <td class="text-left">{{ dataItem.toMember }}</td>
              <td class="text-right">{{ dataItem.amount | currencyLak }}</td>
              <td class="text-center">{{ dataItem.currency }}</td>
              <td class="text-left">
                <span ngbTooltip="{{ dataItem.purpose }}">
                  {{ dataItem.purpose | limitWord : 30 }}</span
                >
              </td>
              <td class="text-right">{{ dataItem.fee | currencyLak }}</td>
            </tr>
          </tbody>
        </table>
        <div
          class="row d-block text-center m-0 no-search-result-wrapper"
          *ngIf="lapnetReport?.length === 0"
        >
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
      </div>
      <div *ngIf="lapnetReport.length" class="mat-paginator-wrapper">
        <mat-paginator
          [length]="formLapnetReportSearch.value.length"
          [pageSize]="formLapnetReportSearch.value.pageSize"
          [pageIndex]="formLapnetReportSearch.value.pageIndex"
          [pageSizeOptions]="pageSizeOptions"
          (page)="onChangePage($event)"
          aria-label="Select page"
        >
        </mat-paginator>
      </div>
    </div>
  </div>
</section>
