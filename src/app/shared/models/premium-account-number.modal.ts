export interface INumberStructure {
  premiumAccountNumberStructureId?: number;
  numberGroup?: any;
  numberGroupId?: number;
  numberStructure?: any;
  price?: string;
  name?: string;
  discount?: number;
  content?: number;
  createdDate?: string;
  createdBy?: string;
  lastModifiedBy?: string;
  lastModifiedDate?: string;
  description?: string;
  paymentPrice?: string;
  status?: number;
  pattern?: string;
  length?: number;
  minPrice?: string;
  maxPrice?: string;
  totalPrice?: string;
  code?: string;
  lengths?: string [];
}

export class NumberStructure implements INumberStructure {
  constructor(data: INumberStructure) {
    Object.assign(this, data);
  }
}

export interface ISpecialPremiumAccountNumber {
  specialPremiumAccountNumberId?: number;
  specialPremiumAccountNumber?: string;
  discount?: number;
  price?: number;
  paymentPrice?: number;
  hided?: any;
  status?: number;
  createdDate?: string;
  createdBy?: string;
  lastModifiedBy?: string;
  lastModifiedDate?: string;
  totalPrice?: number;
}

export class SpecialPremiumAccountNumber
  implements ISpecialPremiumAccountNumber
{
  constructor(data: ISpecialPremiumAccountNumber) {
    Object.assign(this, data);
  }
}
