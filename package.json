{"name": "mb-lao", "version": "0.0.1", "scripts": {"postinstall": "npx husky install", "ng": "ng", "start": "ng serve", "qa": "ng serve -c qa", "dev": "ng serve -c dev", "build": "ng build", "build:qa": "ng build -c qa", "build:dev": "ng build -c dev", "build:prod": "ng build -c production", "build:live": "ng build -c live", "build:stats": "ng build --stats-json", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "tslint --fix -p tsconfig.json -e src/**/*.js", "tslint-fix": "tslint --fix -p tsconfig.json -e src/**/*.js", "analyze": "webpack-bundle-analyzer dist/mb-lao/stats.json", "sonar": "sonar-scanner"}, "private": true, "dependencies": {"@angular-material-components/datetime-picker": "^6.0.3", "@angular/animations": "^12.2.15", "@angular/cdk": "^12.2.13", "@angular/common": "~12.2.15", "@angular/compiler": "~12.2.15", "@angular/core": "~12.2.15", "@angular/forms": "~12.2.15", "@angular/material": "^12.2.13", "@angular/material-moment-adapter": "^12.2.13", "@angular/platform-browser": "~12.2.15", "@angular/platform-browser-dynamic": "~12.2.15", "@angular/router": "~12.2.15", "@ng-bootstrap/ng-bootstrap": "^10.0.0", "@ng-select/ng-select": "^7.0.1", "@ngx-translate/core": "^13.0.0", "@ngx-translate/http-loader": "^6.0.0", "@ngxs/logger-plugin": "^3.7.3", "@ngxs/storage-plugin": "^3.7.3", "@ngxs/store": "^3.7.3", "admin-lte": "^3.1.0", "angularx-qrcode": "^12.0.3", "bootstrap": "^5.1.3", "bootstrap-icons": "^1.9.1", "date-fns": "^2.30.0", "file-saver": "^2.0.5", "firebase": "^9.1.3", "jsencrypt": "^3.2.1", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "mb-lao": "file:", "moment": "^2.29.1", "moment-timezone": "^0.5.43", "ng-zorro-antd": "^12.1.1", "ngx-bootstrap": "^7.1.2", "ngx-cookie-service": "^12.0.3", "ngx-spinner": "^12.0.0", "ngx-toastr": "^14.2.1", "ngx-webstorage": "^8.0.0", "pdfmake": "^0.2.5", "rxjs": "~6.6.7", "sockjs-client": "^1.3.0", "stompjs": "^2.3.3", "tslib": "^2.3.0", "uuid": "^8.3.2", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-builders/jest": "^12.1.2", "@angular-devkit/build-angular": "~12.2.15", "@angular/cli": "~12.2.15", "@angular/compiler-cli": "~12.2.15", "@commitlint/cli": "16.0.2", "@commitlint/config-conventional": "16.0.0", "@ngxs/devtools-plugin": "^3.7.1", "@types/file-saver": "^2.0.4", "@types/jasmine": "~3.10.0", "@types/jest": "27.0.3", "@types/lodash": "^4.14.192", "@types/node": "^12.11.1", "@types/pdfmake": "^0.2.0", "@types/sockjs-client": "^1.5.1", "@types/stompjs": "^2.3.5", "@types/uuid": "^8.3.4", "@types/ws": "^8.2.2", "codelyzer": "^6.0.2", "husky": "7.0.2", "jasmine-core": "~3.10.0", "jasmine-spec-reporter": "7.0.0", "jest": "^27.2.4", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "net": "^1.0.2", "npm-run-all": "^4.1.5", "sass": "^1.44.0", "sass-loader": "^12.4.0", "sonar-scanner": "^3.1.0", "ts-loader": "^4.4.1", "ts-node": "10.2.1", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "typescript": "^4.3.5", "webpack": "^5.65.0", "webpack-bundle-analyzer": "4.4.2"}}