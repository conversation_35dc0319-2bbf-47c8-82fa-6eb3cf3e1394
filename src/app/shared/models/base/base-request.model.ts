
export interface IBaseRequestModel {
  keyword?: string;
  pageIndex?: number;
  pageSize?: number;
  sortBy?: string;
}

export interface IPagination {
  pageIndex?: number;
  pageSize?: number;
  orderByType?: string;
  orderByColumn?: string;
}

export interface ISearch {
  keyword?: string;
  sortBy?: string;
  status?: number;
  hasPageable?: boolean;
}

export interface ISearchWithPagination extends ISearch, IPagination {}
