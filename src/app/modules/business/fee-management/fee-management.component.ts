import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import {
  CONFIGURATION_FEE_TYPE,
  CONFIGURATION_FEE_TYPE_CONST,
  CONFIGURATION_FEE_TYPE_MAP,
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  MODAL_ACTION,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { IFee } from '@shared/models/IFee.model';
import { IFeeSearch } from '@shared/models/request/fee.search';
import { FeeService } from '@shared/services/fee.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-fee-management',
  templateUrl: './fee-management.component.html',
  styleUrls: ['./fee-management.component.scss'],
})
export class FeeManagementComponent implements OnInit {
  fees: IFee[] = [];
  feeSearch: IFeeSearch = {};

  action: any = '';
  recordSelected: any = [];
  storage: any;

  pageSizeOptions = PAGINATION.OPTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  ROUTER_UTILS = ROUTER_UTILS;
  ENTITY_STATUS = ENTITY_STATUS;
  CONFIGURATION_FEE_TYPE = CONFIGURATION_FEE_TYPE;
  CONFIGURATION_FEE_TYPE_MAP = CONFIGURATION_FEE_TYPE_MAP;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;

  // default form search
  formFeeSearch = this.fb.group({
    keyword: '',
    configurationFeeType: null,
    status: null,
    length: 0,
    isSystem: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
  });

  constructor(
    private feeService: FeeService,
    private fb: FormBuilder,
    private router: Router,
    private modalService: ModalService,
    private toastService: ToastrCustomService
  ) {
    this.storage = sessionStorage.getItem(STORAGE_APP.FEE);
  }

  /**
   * Init data
   */
  ngOnInit(): void {
    // this.loadData(this.pageEvent);
    if (this.storage) {
      // get session storage and parse json
      const filter = JSON.parse(this.storage) as IFee;
      // set value form control
      if (filter.status) {
        const status = +filter.status;
        this.formFeeSearch.controls.status.setValue(status);
      }
      this.formFeeSearch.controls.configurationFeeType.setValue(
        filter.configurationFeeType
      );
      this.formFeeSearch.controls.keyword.setValue(filter.keyword);
      this.onSearch();
    } else {
      // set default value start date and end date
      this.onSearch();
    }
    // click form clear storage
    sessionStorage.removeItem(STORAGE_APP.FEE);
  }

  // /**
  //  * load list data Loan Online
  //  *
  //  * @param page PageEvent
  //  */
  // loadData(page: PageEvent): void {
  //   this.feeSearch.pageIndex = page.pageIndex;
  //   this.feeSearch.pageSize = page.pageSize;
  //   this.feeService.search(this.feeSearch).subscribe((res: any): void => {
  //     this.fees = res.body.content;
  //     this.totalRecord = res.body.totalElements;
  //   });
  // }

  /**
   * Change page: next page and preview page
   *
   * @param page PageEvent
   */
  onChangePage(page: PageEvent) {
    this.formFeeSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formFeeSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
    // this.loadData(page);
  }

  getConfigurationFeeType(configurationType: any) {
    if (configurationType) {
      return CONFIGURATION_FEE_TYPE_MAP[configurationType];
    }
    return '';
  }

  onSearchSubmit() {
    this.formFeeSearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.formFeeSearch.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    this.onSearch();
  }

  /**
   * search form
   */
  onSearch(): void {
    // this.loadData(this.pageEvent);
    this.feeService
      .search(this.formFeeSearch.value)
      .subscribe((res: any): void => {
        this.fees = res.body.content;
        this.formFeeSearch.controls.length.setValue(res.body.totalElements);
      });
  }

  /**
   * index on list event
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formFeeSearch.value.pageIndex,
      this.formFeeSearch.value.pageSize
    );
  }

  /**
   * button click detail
   *
   * @param feeId number
   */
  detail(feeId?: number): void {
    sessionStorage.setItem(
      STORAGE_APP.FEE,
      JSON.stringify(this.formFeeSearch.value)
    );
    this.router.navigate([ROUTER_UTILS.fee.root, feeId, ROUTER_ACTIONS.detail]);
  }

  /**
   * Cập nhật cấu hình phí
   *
   * @param feeId
   */
  update(feeId?: number): void {
    sessionStorage.setItem(
      STORAGE_APP.FEE,
      JSON.stringify(this.formFeeSearch.value)
    );
    this.router.navigate([ROUTER_UTILS.fee.root, feeId, ROUTER_ACTIONS.update]);
  }

  /**
   *
   *
   * @param fee
   */
  activeAndInactive(fee: IFee) {
    if (fee.status === ENTITY_STATUS_CONST.INACTIVE.code) {
      this.active(fee);
    } else {
      this.inactive(fee);
    }
  }

  /**
   * Tạo mới cấu hình phí
   *
   */
  onCreate() {
    const modalData = {
      title: 'fee.createFee',
      content: '',
    };

    this.modalService.createFee(modalData).then((configurationFeeType) => {
      sessionStorage.setItem(
        STORAGE_APP.FEE,
        JSON.stringify(this.formFeeSearch.value)
      );
      if (configurationFeeType === CONFIGURATION_FEE_TYPE_CONST.FEE.code) {
        this.router.navigate([
          ROUTER_UTILS.fee.root,
          CONFIGURATION_FEE_TYPE_CONST.FEE.code,
          ROUTER_ACTIONS.create,
        ]);
      } else if (
        configurationFeeType === CONFIGURATION_FEE_TYPE_CONST.DISCOUNT.code
      ) {
        this.router.navigate([
          ROUTER_UTILS.fee.root,
          CONFIGURATION_FEE_TYPE_CONST.DISCOUNT.code,
          ROUTER_ACTIONS.create,
        ]);
      }
    });
  }

  /**
   * Lock fee
   *
   * @param fee IFee
   */
  private inactive(fee: IFee) {
    const modalData = {
      title: 'fee.inactive',
      content: 'fee.inactiveFeeContent',
      interpolateParams: { feeName: `<b>${fee?.feeName || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { id: fee?.feeId };
        this.feeService.lock(params).subscribe((res) => {
          this.toastService.success('common.action.lockSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * UnLock Fee
   *
   * @param bank: IFee
   */
  private active(fee: IFee) {
    const modalData = {
      title: 'fee.active',
      content: 'fee.activeFeeContent',
      interpolateParams: { feeName: `<b>${fee?.feeName || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { id: fee?.feeId };
        this.feeService.unlock(params).subscribe((res) => {
          this.toastService.success('common.action.unlockSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * reset form
   */
  onReset(): void {
    this.formFeeSearch.controls.status.reset();
    this.formFeeSearch.controls.configurationFeeType.reset();
    this.formFeeSearch.controls.keyword.reset();
  }

  /**
   * delete
   *
   * @param IFee
   */
  delete(fee: IFee): void {
    // open modal
    const modalData = {
      title: 'fee.delete',
      content: 'fee.deleteContent',
      interpolateParams: { feeName: `<b>${fee?.feeName || ''}</b>` },
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.feeService.deleteFee({ id: fee.feeId }).subscribe((res: any) => {
          this.toastService.success('common.action.deleteSuccess');
          this.onSearch();
        });
      }
    });
  }
}
