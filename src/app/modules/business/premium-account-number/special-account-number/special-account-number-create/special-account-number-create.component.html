<div class="modal-content">
  <div class="modal-header">
    <h5 class="modal-title" *ngIf="!(action === ROUTER_ACTIONS.detail)">
      {{
      (action === ROUTER_ACTIONS.update
      ? "model.specialAccountNumber.updateTitle"
      : "model.specialAccountNumber.createTitle"
      ) | translate
      }}
    </h5>
    <h5 class="modal-title" *ngIf="action === ROUTER_ACTIONS.detail">
      {{ "model.specialAccountNumber.detailTitle" | translate }}
    </h5>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="activeModal.close()">
      <span aria-hidden="true"><i class="bi bi-x"></i></span>
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="formCreate" *ngIf="formCreate" (keydown.enter)="$event.preventDefault()">
      <div class="col-md-12">
        <div class="row" *ngIf="
            specialPremiumAccNumber?.specialPremiumAccountNumber != null ||
            action == ROUTER_ACTIONS.create
          ">
          <div class="table-responsive" [ngClass]="
              specialAccountNumbers.controls.length <=
              CONFIG_ACCOUNT_CONFIG_CONST.MAIL_SCROLL_LENGTH
                ? ''
                : 'scroll-list-mail'
            ">
            <table class="table table-customer">
              <thead>
                <tr>
                  <th class="text-left" [width]="'230px'">
                    {{ "common.accountNumber" | translate }} <span class="text-danger">*</span>
                  </th>
                  <th class="text-left">
                    {{ "model.structureAccount.listPrice" | translate }} <span class="text-danger">*</span>
                  </th>
                  <th class="text-left">
                    {{ "fee.discount" | translate }} <span class="text-danger">*</span>
                  </th>
                  <th class="text-left">
                    {{ "model.structureAccount.paymentPrice" | translate }}
                  </th>
                  <th class="text-center" [width]="'120px'">
                    {{ "model.specialAccountNumber.hiddenNumber" | translate }}
                  </th>
                  <th></th>
                </tr>
              </thead>
              <tbody formArrayName="specialAccountNumbers">
                <tr *ngFor="let item of specialAccountNumbers.controls; let i = index" [formGroupName]="i">
                  <td>
                    <input trim type="text" class="w-100" class="form-control" formControlName="accountNumber" numbersOnly/>
                    <small class="form-text text-danger noti-small" *ngIf="
                        item.get('accountNumber')?.errors?.required &&
                        item.get('accountNumber')?.touched
                      ">
                      {{
                      "model.specialAccountNumber.error.accountNumber"
                      | translate
                      }}
                    </small>
                    <small class="text-danger" *ngIf="
                        item.get('accountNumber')?.errors?.pattern &&
                        item.get('accountNumber')?.touched
                      ">
                      {{
                      "model.specialAccountNumber.pattern.accountNumber"
                      | translate
                      }}</small>
                  </td>
                  <td>
                    <div class="text-unit">
                      <input type="text" class="form-control w-100" [maxLength]="VALIDATORS.LENGTH.NUMBER_MAX_LENGTH"
                        (input)="calculatePaymentPrice(i)" formControlName="listPrice" numbersOnly appCurrencyFormat />
                      <div class="text-unit-right">
                        <span>{{ "loanOnline.unit.lak" | translate }}</span>
                      </div>
                      <small class="form-text text-danger noti-small" *ngIf="
                          item.get('listPrice')?.errors?.required &&
                          item.get('listPrice')?.touched
                        ">
                        {{
                        "model.specialAccountNumber.error.listPrice"
                        | translate
                        }}
                      </small>
                      <small class="form-text text-danger noti-small" *ngIf="
                          item.get('listPrice')?.errors?.invalidMax &&
                          item.get('listPrice')?.touched
                        ">
                        {{ "appError.listPrice" | translate }}
                      </small>
                    </div>
                  </td>
                  <td>
                    <div class="text-unit">
                      <input type="text" class="form-control w-100" formControlName="discount" appNumberInput
                        (input)="calculatePaymentPrice(i)" />
                      <div class="text-unit-right">
                        <span>{{ "common.percent" | translate }}</span>
                      </div>
                      <small class="form-text text-danger noti-small" *ngIf="
                          item.get('discount')?.errors?.required &&
                          item.get('discount')?.touched
                        ">
                        {{
                        "model.specialAccountNumber.error.discount"
                        | translate
                        }}
                      </small>
                    </div>
                  </td>
                  <td>
                    <div class="text-unit">
                      <input type="text" class="form-control w-100" formControlName="paymentPrice" appCurrencyFormat />
                      <div class="text-unit-right">
                        <span>{{ "loanOnline.unit.lak" | translate }}</span>
                      </div>
                    </div>
                  </td>
                  <td class="text-center">
                    <div class="form-group">
                      <mat-checkbox formControlName="hided" class="px-1 py-0 mt-2">
                      </mat-checkbox>
                    </div>
                  </td>
                  <td>
                    <button ngbTooltip="{{ 'common.action.delete' | translate }}" class="btn px-1 py-0 mt-1"
                      (click)="removeRow(i)" *ngIf="isShowDelete[i]">
                      <i class="fa fa-trash mb-color-red" aria-hidden="true"></i>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div *ngIf="action === ROUTER_ACTIONS.create">
            <button class="btn px-1 py-0 mt-1 add-mail" (click)="addRow()">
              <i class="fa fa-plus-circle" aria-hidden="true"></i>
            </button>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="text-center pb-4">
    <button type="button" class="btn mb-btn-outline-color mr-3 flex-fill" (click)="activeModal.close()">
      {{ "common.action.cancel" | translate }}
    </button>
    <ng-container *ngIf="!(action === ROUTER_ACTIONS.detail)">
      <button type="button" class="btn mb-btn-color flex-fill"
        (click)="action === ROUTER_ACTIONS.update ? onUpdate() : onCreate()" *hasPrivileges="action === ROUTER_ACTIONS.update
          ? SYSTEM_RULES.SPECIAL_PREMIUM_ACCOUNT_NUMBER_UPDATE
          : SYSTEM_RULES.SPECIAL_PREMIUM_ACCOUNT_NUMBER_CREATE">
        {{
        (action === ROUTER_ACTIONS.update
        ? "common.action.update"
        : "common.action.add"
        ) | translate
        }}
      </button>
    </ng-container>
  </div>
</div>
