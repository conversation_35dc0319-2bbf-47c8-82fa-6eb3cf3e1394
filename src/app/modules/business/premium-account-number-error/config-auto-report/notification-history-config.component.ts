import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import {
  CONFIG_AUTO_REPORT_CONST,
  CONFIG_AUTO_TYPE_CONST,
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  MOMENT_CONST,
  TRANSACTION_ENTITY_STATUS,
  TRANSACTION_UMONEY_ENTITY_TYPE,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { SELECTED_CONST } from '@shared/constants/user.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IConfigAuto } from '@shared/models/config-auto-transaction.model';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { NotificationHistoryService } from '@shared/services/notification-history.service';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';

@Component({
  selector: 'app-notification-history-config',
  templateUrl: './notification-history-config.component.html',
  styleUrls: ['./notification-history-config.component.scss'],
})
export class NotificationHistoryConFigComponent implements OnInit {
  ENTITY_STATUS = ENTITY_STATUS;
  TRANSACTION_ENTITY_STATUS = TRANSACTION_ENTITY_STATUS;
  TRANSACTION_UMONEY_ENTITY_TYPE = TRANSACTION_UMONEY_ENTITY_TYPE;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  SELECTED_CONST = SELECTED_CONST;
  CONFIG_AUTO_REPORT_CONST = CONFIG_AUTO_REPORT_CONST;
  VALIDATORS = VALIDATORS;
  SYSTEM_RULES = SYSTEM_RULES;

  formCreate: FormGroup = new FormGroup({});
  emails: FormArray = new FormArray([]);
  isShowDeleteMail: boolean[] = [];
  data: IConfigAuto = {};
  constructor(
    private fb: FormBuilder,
    public activeModal: NgbActiveModal,
    private configAutoService: NotificationHistoryService,
    private toastService: ToastrCustomService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.configAutoService
      .detailConfig({ type: CONFIG_AUTO_TYPE_CONST.TYPE_NOTIFICATION_HISTORY })
      .subscribe((res: any) => {
        if (res.body !== null) {
          this.data = res.body;
          this.initForm(res.body);
          this.getMail(res.body);
        } else {
          this.addEmail();
          this.isShowDeleteMail[CONFIG_AUTO_REPORT_CONST.MAIL_DEFAULT] = false;
        }
      });
  }

  initForm(data?: IConfigAuto) {
    this.formCreate = this.fb.group({
      title: [data?.title || '', [Validators.required]],
      content: [data?.content || '', [Validators.required]],
      emails: new FormArray([], this.duplicateEmailValidator),
      intervalMinute: [data?.intervalMinute || '', [Validators.required]],
      type: CONFIG_AUTO_TYPE_CONST.TYPE_NOTIFICATION_HISTORY,
      configureAutomaticReportId: data?.configureAutomaticReportId,
    });
  }

  getMail(data?: IConfigAuto) {
    this.emails = this.formCreate.get('emails') as FormArray;
    data?.emailRecipients.forEach((item: any) => {
      this.emails.push(this.createEmail(item.email));
    });
    if (
      data?.emailRecipients.length ===
      CONFIG_AUTO_REPORT_CONST.MAIL_DEFAULT_LENGTH
    ) {
      this.isShowDeleteMail[CONFIG_AUTO_REPORT_CONST.MAIL_DEFAULT] = false;
    } else {
      this.onShowDeleteMail();
    }
  }

  onShowDeleteMail() {
    this.emails.controls.forEach((item, i: number) => {
      this.isShowDeleteMail[i] = true;
    });
  }

  onSelectAll() {
    this.formCreate
      .get('transactionStatuses')
      ?.patchValue(this.TRANSACTION_ENTITY_STATUS.map((item) => item.code));
  }

  onClearAll() {
    this.formCreate.get('transactionStatuses')?.patchValue([]);
  }

  addEmail(): void {
    this.emails = this.formCreate.get('emails') as FormArray;
    this.emails.push(this.createEmail());
    this.onShowDeleteMail();
  }

  createEmail(mail?: any) {
    return this.fb.group({
      email: [
        mail || '',
        [Validators.pattern(VALIDATORS.PATTERN.EMAIL), Validators.required],
      ],
    });
  }

  duplicateEmailValidator: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (!control || !(control instanceof Array || control instanceof Object))
      return null;

    const formArray = control as any;
    if (!formArray || formArray.length === 0) return null;

    const emailList: string[] = formArray.controls
      .map((group: FormGroup) => group.controls.email?.value?.trim())
      .filter((email: string) => !!email);

    const duplicates = emailList.filter(
      (email, index, self) => self.indexOf(email) !== index
    );

    formArray.controls.forEach((group: FormGroup) => {
      const email = group.controls.email.value?.trim();
      if (duplicates.includes(email)) {
        group.controls.email?.setErrors({
          ...group.controls.email?.errors,
          duplicate: true,
        });
      } else {
        const errors = { ...group.controls.email?.errors };
        if (errors?.duplicate) {
          delete errors.duplicate;
          group.controls.email?.setErrors(
            Object.keys(errors).length ? errors : null
          );
        }
      }
    });

    return null;
  };

  onDeleteEmail(index: number) {
    this.emails.removeAt(index);
    this.isShowDeleteMail[CONFIG_AUTO_REPORT_CONST.MAIL_DEFAULT] =
      this.emails.controls.length ===
      CONFIG_AUTO_REPORT_CONST.MAIL_DEFAULT_LENGTH
        ? false
        : true;
  }

  sendMail() {
    if (this.formCreate.invalid) {
      CommonUtils.markFormGroupTouched(this.formCreate);
      return;
    }
    const emails: string[] = [];
    this.emails.controls.forEach((element) => {
      emails.push(element.value.email);
    });
    const body = {
      ...this.formCreate.value,
      intervalMinute: this.formCreate.controls.intervalMinute.value,
      emails,
    };
    const serviceEvent =
      Object.keys(this.data).length === 0
        ? this.configAutoService.createConfig(body)
        : this.configAutoService.updateConfig(body);
    serviceEvent.subscribe((res: any) => {
      this.toastService.success(
        this.data
          ? 'common.action.updateSuccess'
          : 'common.action.createSuccess'
      );
      this.activeModal.close();
    });
  }
}
