import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { MatMenuModule } from '@angular/material/menu';
import { BreadcrumbComponent } from '@core/layouts/breadcrumb/breadcrumb.component';
import { FooterComponent } from '@core/layouts/main-layout/footer/footer.component';
import { HeaderComponent } from '@core/layouts/main-layout/header/header.component';
import { MainLayoutComponent } from '@core/layouts/main-layout/main-layout.component';
import { NavbarComponent } from '@core/layouts/main-layout/navbar/navbar.component';
import { SidebarComponent } from '@core/layouts/main-layout/sidebar/sidebar.component';
import { SharedModule } from '@shared/shared.module';

@NgModule({
  declarations: [
    BreadcrumbComponent,
    MainLayoutComponent,
    HeaderComponent,
    FooterComponent,
    SidebarComponent,
    NavbarComponent,
  ],
  imports: [CommonModule, HttpClientModule, SharedModule, MatMenuModule],
  providers: [],
})
export class CoreModule {}
