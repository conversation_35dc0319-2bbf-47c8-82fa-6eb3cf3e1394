import { Component, HostListener, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { APPROVAL_STATUS_CONST, CUSTOMER_STATUS_CONST } from '@shared/constants/customer.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { Customer } from '@shared/models/customer.model';
import { CustomerService } from '@shared/services/customer.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';

@Component({
  selector: 'app-detail-customer',
  templateUrl: './detail-customer.component.html',
  styleUrls: ['./detail-customer.component.scss'],
})
export class DetailCustomerComponent implements OnInit, OnDestroy {
  customer: Customer = new Customer();

  hasFilter = false;

  SYSTEM_RULES = SYSTEM_RULES;
  APPROVAL_STATUS_CONST = APPROVAL_STATUS_CONST;
  CUSTOMER_STATUS_CONST = CUSTOMER_STATUS_CONST;

  constructor(
    private customerService: CustomerService,
    private router: Router,
    private routerActive: ActivatedRoute
  ) {
    // get param from url
    this.routerActive.paramMap.subscribe((res) => {
      const idParam = res.get('customerId');
      if (idParam) {
        this.customer.customerId = +idParam;
      }
    });
  }

  /**
   * remove storage
   */
   ngOnDestroy(): void {
    if (!this.hasFilter) {
      sessionStorage.removeItem(STORAGE_APP.CUSTOMER_REGISTER);
    }
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.hasFilter = true;
  }

  /**
   * init data call api get detail and get idCardType
   */
  ngOnInit(): void {
    this.getDetail();
  }

  /**
   * get detail customer
   */
  getDetail(): void {
    if (this.customer.customerId) {
      this.customerService.info(this.customer).subscribe((res: any) => {
        this.customer = res.body;
      });
    }
  }

  /**
   * back to list form registration
   */
  onBackToList(): void {
    this.router.navigate([
      ROUTER_UTILS.customer.root,
      ROUTER_UTILS.customer.registration.root,
    ]);
    this.hasFilter = true;
  }

  /**
   * enabled and disable form
   */
  onOpenUpdate(): void {
    this.router.navigate([
      ROUTER_UTILS.customer.root,
      ROUTER_UTILS.customer.registration.root,
      this.customer.customerId,
      ROUTER_ACTIONS.update,
    ]);
  }
}
