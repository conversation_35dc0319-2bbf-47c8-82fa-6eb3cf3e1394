import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ROUTER_UTILS } from '@shared/utils';

export const APP_ROUTES: Routes = [
  {
    path: ROUTER_UTILS.auth.root,
    loadChildren: () => import('@auth/auth.module').then((m) => m.AuthModule),
  },
  {
    path: '',
    loadChildren: () =>
      import('@business/business.module').then((m) => m.BusinessModule),
  },
  {
    path: '',
    loadChildren: () =>
      import('@portal/portal.module').then((m) => m.PortalModule),
  },
];

@NgModule({
  imports: [RouterModule.forRoot(APP_ROUTES)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
