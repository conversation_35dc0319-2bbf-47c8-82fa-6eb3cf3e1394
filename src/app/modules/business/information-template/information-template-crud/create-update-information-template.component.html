<section class="box-content">
  <div class="container-fluid">
    <div class="back-container">
      <div class="col-md-6 back-container">
        <img src="assets/dist/img/icon/back.png" (click)="backToList()" />
        <h5>
          {{
            action === ROUTER_ACTIONS.create
              ? ("informationTemplate.create" | translate)
              : action === ROUTER_ACTIONS.update
              ? ("informationTemplate.update" | translate)
              : ("informationTemplate.detail" | translate)
          }}
        </h5>
      </div>
      <div class="col-md-6">
        <div class="tab-list">
          <div class="col-3 text-labels">
            {{ "common.textLanguage" | translate }}
          </div>
          <div class="tab-labels">
            <div class="" *ngFor="let lang of LANGUAGES">
              <div
                [ngClass]="
                  language === lang.code ? 'tab-group action' : 'tab-group'
                "
                (click)="onChangeLang(lang.code)"
              >
                {{ lang.label | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-12">
      <div class="row">
        <h2 class="title-create">
          {{ "informationTemplate.infor" | translate }}
        </h2>
      </div>
      <div class="row border-ckeditor">
        <h3 class="border-ckeditor-h3">
          {{ "informationTemplate.infor" | translate }}
        </h3>
        <div class="w-100">
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-12">
                <form
                  [formGroup]="formCreate"
                  *ngIf="
                    this.action === ROUTER_ACTIONS.create
                      ? formCreate
                      : data?.informationTemplateId
                  "
                >
                  <div class="col-md-12">
                    <div class="row">
                      <div class="form-group col-md-6">
                        <label
                          >{{
                            "model.informationTemplate.informationTemplateCode"
                              | translate
                          }}
                          <span
                            class="text-danger"
                            *ngIf="action !== ROUTER_ACTIONS.detail"
                            >*</span
                          ></label
                        >
                        <input
                          trim
                          type="text"
                          formControlName="informationTemplateCode"
                          class="w-100"
                          class="form-control"
                          [maxLength]="
                            VALIDATORS.LENGTH
                              .INFORMATION_TEMPLATE_CODE_MAX_LENGTH
                          "
                          placeholder="{{
                            'model.informationTemplate.informationTemplateCode'
                              | placeholder
                          }}"
                        />
                        <small
                          class="form-text text-danger noti-small"
                          *ngIf="
                            formCreate.get('informationTemplateCode')?.errors
                              ?.required &&
                            formCreate.get('informationTemplateCode')?.touched
                          "
                        >
                          {{
                            "informationTemplate.error.required.informationTemplateCode"
                              | translate
                          }}
                        </small>
                        <small
                          class="form-text text-danger noti-small"
                          *ngIf="
                            formCreate.get('informationTemplateCode')?.errors
                              ?.pattern &&
                            formCreate.get('informationTemplateCode')?.touched
                          "
                        >
                          {{
                            "informationTemplate.error.pattern.informationTemplateCode"
                              | translate
                          }}
                        </small>
                        <small
                          class="form-text text-danger noti-small"
                          *ngIf="
                            formCreate.get('informationTemplateCode')?.errors
                              ?.maxlength &&
                            formCreate.get('informationTemplateCode')?.touched
                          "
                        >
                          {{
                            "informationTemplate.error.maxLength.informationTemplateCode"
                              | translate
                                : {
                                    param:
                                      VALIDATORS.LENGTH
                                        .INFORMATION_TEMPLATE_CODE_MAX_LENGTH
                                  }
                          }}
                        </small>
                      </div>
                      <div class="form-group col-md-6">
                        <label
                          >{{
                            "model.informationTemplate.informationTemplateName"
                              | translate
                          }}<span
                            class="text-danger"
                            *ngIf="action !== ROUTER_ACTIONS.detail"
                            >*</span
                          ></label
                        >
                        <input
                          trim
                          type="text"
                          formControlName="informationTemplateName"
                          class="w-100"
                          class="form-control"
                          [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
                          placeholder="{{
                            'model.informationTemplate.informationTemplateName'
                              | placeholder
                          }}"
                        />
                        <small
                          class="form-text text-danger noti-small"
                          *ngIf="
                            formCreate.get('informationTemplateName')?.errors
                              ?.required &&
                            formCreate.get('informationTemplateName')?.touched
                          "
                        >
                          {{
                            "informationTemplate.error.required.informationTemplateName"
                              | translate
                          }}
                        </small>
                        <small
                          class="form-text text-danger noti-small"
                          *ngIf="
                            formCreate.get('informationTemplateName')?.errors
                              ?.pattern &&
                            formCreate.get('informationTemplateName')?.touched
                          "
                        >
                          {{
                            "informationTemplate.error.pattern.informationTemplateName"
                              | translate
                          }}
                        </small>
                        <small
                          class="form-text text-danger noti-small"
                          *ngIf="
                            formCreate.get('informationTemplateName')?.errors
                              ?.maxlength &&
                            formCreate.get('informationTemplateName')?.touched
                          "
                        >
                          {{
                            "informationTemplate.error.maxLength.informationTemplateName"
                              | translate
                                : {
                                    param:
                                      VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH
                                  }
                          }}
                        </small>
                      </div>
                      <div class="form-group col-md-6">
                        <label
                          >{{
                            "model.informationTemplate.displayName" | translate
                          }}<span
                            class="text-danger"
                            *ngIf="action !== ROUTER_ACTIONS.detail"
                            >*</span
                          ></label
                        >
                        <input
                          trim
                          type="text"
                          formControlName="displayName"
                          class="w-100"
                          class="form-control"
                          [maxLength]="
                            VALIDATORS.LENGTH
                              .INFORMATION_TEMPLATE_DISPLAY_NAME_MAX_LENGTH
                          "
                          placeholder="{{
                            'model.informationTemplate.displayName'
                              | placeholder
                          }}"
                        />
                        <small
                          class="form-text text-danger noti-small"
                          *ngIf="
                            formCreate.get('displayName')?.errors?.required &&
                            formCreate.get('displayName')?.touched
                          "
                        >
                          {{
                            "informationTemplate.error.required.informationTemplateDisplayName"
                              | translate
                          }}
                        </small>
                        <small
                          class="form-text text-danger noti-small"
                          *ngIf="
                            formCreate.get('displayName')?.errors?.maxlength &&
                            formCreate.get('displayName')?.touched
                          "
                        >
                          {{
                            "informationTemplate.error.maxLength.informationTemplateDisplayName"
                              | translate
                                : {
                                    param:
                                      VALIDATORS.LENGTH
                                        .INFORMATION_TEMPLATE_DISPLAY_NAME_MAX_LENGTH
                                  }
                          }}
                        </small>
                        <small
                          class="form-text text-danger noti-small"
                          *ngIf="
                            formCreate.get('displayName')?.errors?.pattern &&
                            formCreate.get('displayName')?.touched
                          "
                        >
                          {{
                            "informationTemplate.error.pattern.informationTemplateDisplayName"
                              | translate
                          }}
                        </small>
                      </div>
                      <div class="col-12">
                        <div class="form-group">
                          <label
                            >{{ "model.event.content" | translate
                            }}<span class="text-danger">*</span></label
                          >
                          <mb-editor
                            #content
                            [placeholder]="'model.event.content' | translate"
                            [isReadOnly]="action === ROUTER_ACTIONS.detail"
                            [value]="formCreate?.get('content')?.value || ''"
                            [maxLength]="
                              VALIDATORS.LENGTH.INFORMATION_TEMPLATE_MAX_LENGTH
                            "
                            (HTMLdata)="getTemplate($event)"
                            (data)="onChangeData('content', $event)"
                            trim
                          >
                          </mb-editor>
                          <small
                            class="form-text text-danger noti-small"
                            *ngIf="
                              formCreate.get('content')?.errors?.required &&
                              formCreate.get('content')?.touched
                            "
                          >
                            {{ "error.event.required.content" | translate }}
                          </small>
                          <small
                            class="form-text text-danger noti-small"
                            *ngIf="
                              formCreate.get('content')?.errors?.maxlength &&
                              formCreate.get('content')?.touched
                            "
                          >
                            {{ "model.news.error.content" | translate }}
                          </small>
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
            <div class="d-block text-center mb-5 mt-4">
              <ng-container>
                <button class="btn btn-white mr-2" (click)="backToList()">
                  {{ "common.action.back" | translate }}
                </button>
                <button
                  class="btn btn-red"
                  (click)="onSubmit()"
                  *ngIf="action !== ROUTER_ACTIONS.detail"
                >
                  {{
                    (action === ROUTER_ACTIONS.update
                      ? "common.action.update"
                      : "common.action.create"
                    ) | translate
                  }}
                </button>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
