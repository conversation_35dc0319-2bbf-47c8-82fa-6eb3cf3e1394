import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { LoginComponent } from '@auth/login/login.component';
import { RegisterComponent } from '@auth/register/register.component';
import { SharedModule } from '@shared/shared.module';
import { AuthRoutingModule } from './auth-routing.module';
import { ChangePasswordExpireComponent } from './change-password-expire/change-password-expire.component';
import { ResetPasswordComponent } from './reset-password/reset-password.component';

@NgModule({
  declarations: [
    LoginComponent,
    RegisterComponent,
    ResetPasswordComponent,
    ChangePasswordExpireComponent,
  ],
  imports: [CommonModule, AuthRoutingModule, SharedModule],
  providers: [],
  // schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AuthModule {}
