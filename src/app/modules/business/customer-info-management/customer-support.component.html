<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container mb-2">
      <h5>{{ "model.customerSupport.title" | translate }}</h5>
    </div>
    <form [formGroup]="formCreate" *ngIf="formCreate">
      <div class="col-md-12">
        <div class="row">
          <h2 class="title-create">
            {{ "model.customerSupport.title" | translate }}
          </h2>
        </div>
        <div class="row border-create">
          <h3>{{ "model.customerSupport.info" | translate }}</h3>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-12">
                <div class="form-group">
                  <label
                    >{{ "model.customerSupport.address" | translate
                    }}<span class="text-danger small-text"> *</span></label
                  >
                  <textarea
                    class="w-100 form-control"
                    rows="5"
                    formControlName="address"
                    placeholder="{{
                      'model.customerSupport.address' | placeholder
                    }}"
                    [maxLength]="VALIDATORS.LENGTH.MEDIUM_TEXT_MAX_LENGTH"
                  ></textarea>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formCreate.get('address')?.errors?.required &&
                      formCreate.get('address')?.touched
                    "
                  >
                    {{ "model.customerSupport.error.address" | translate }}
                  </small>
                </div>
                <div class="form-group">
                  <label>
                    {{ "model.customerSupport.mail" | translate
                    }}<span class="text-danger small-text"> *</span></label
                  >
                  <input
                    trim
                    placeholder="{{
                      'model.customerSupport.mail' | placeholder
                    }}"
                    type="text"
                    class="form-control w-100"
                    formControlName="email"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formCreate.get('email')?.errors?.required &&
                      formCreate.get('email')?.touched
                    "
                  >
                    {{ "model.customerSupport.error.mail" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formCreate.get('email')?.errors?.pattern &&
                      formCreate.get('email')?.touched
                    "
                  >
                    {{ "model.customerSupport.pattern.mail" | translate }}
                  </small>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <label>
              {{ "model.customerSupport.phone" | translate
              }}<span class="text-danger small-text"> *</span></label
            >
            <div>
              <div
                formArrayName="phoneNumbers"
                class="form-group"
                *ngFor="let group of phoneNumbers.controls; index as i"
              >
                <div class="row" [formGroupName]="i">
                  <div class="col-md-6">
                    <input
                      placeholder="{{
                        'model.customerSupport.phone' | placeholder
                      }}"
                      type="text"
                      numbersOnly
                      trim
                      [maxLength]="
                        VALIDATORS.LENGTH
                          .PHONE_NUMBER_CUSTOMER_SUPPORT_MAX_LENGTH
                      "
                      class="form-control w-100"
                      formControlName="phone"
                    />
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="
                        group.get('phone')?.errors?.required &&
                        group.get('phone')?.touched
                      "
                    >
                      {{ "model.customerSupport.error.phone" | translate }}
                    </small>
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="
                        group.get('phone')?.errors?.pattern &&
                        group.get('phone')?.touched
                      "
                    >
                      {{ "model.customerSupport.pattern.phone" | translate }}
                    </small>
                  </div>
                  <div
                    class="col-md-2"
                    *hasPrivileges="
                      SYSTEM_RULES.REQUEST_CUSTOMER_SUPPORT_UPDATE
                    "
                  >
                    <button
                      ngbTooltip="{{ 'common.action.delete' | translate }}"
                      class="btn px-1 py-0 mt-1"
                      (click)="onDelete(i, group)"
                      *ngIf="isShowDelete[i]"
                    >
                      <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div
              class=""
              *ngIf="
                phoneNumbers.controls.length <
                CONFIG_PHONE_CONST.PHONE_MAX_LENGTH
              "
            >
              <button
                class="btn px-1 py-0 mt-1 add-mail"
                (click)="addPhone()"
                *hasPrivileges="SYSTEM_RULES.REQUEST_CUSTOMER_SUPPORT_UPDATE"
              >
                <i class="fa fa-plus-circle" aria-hidden="true"></i>
                {{ "model.customerSupport.add" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="d-block text-center mb-5 mt-4">
          <button
            class="btn btn-red"
            (click)="onSubmit()"
            *hasPrivileges="SYSTEM_RULES.REQUEST_CUSTOMER_SUPPORT_UPDATE"
          >
            {{ "common.action.update" | translate }}
          </button>
        </div>
      </div>
    </form>
  </div>
</section>
