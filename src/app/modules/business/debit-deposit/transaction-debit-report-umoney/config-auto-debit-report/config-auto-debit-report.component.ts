import { Component, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import {
  CONFIG_AUTO_REPORT_CONST,
  CONFIG_AUTO_TYPE_CONST,
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  ENTITY_TRANSACTION_STATUS_CONST,
  MOMENT_CONST,
  TRANSACTION_ENTITY_STATUS,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IConfigAuto } from '@shared/models/config-auto-transaction.model';
import { ConfigAutoService } from '@shared/services/config-auto.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';

@Component({
  selector: 'app-config-auto-debit',
  templateUrl: './config-auto-debit-report.component.html',
  styleUrls: ['./config-auto-debit-report.component.scss'],
})
export class ConFigAutoDebitReportComponent implements OnInit {
  SYSTEM_RULES = SYSTEM_RULES;
  VALIDATORS = VALIDATORS;
  formCreate: FormGroup = new FormGroup({});
  emails: FormArray = new FormArray([]);
  data: IConfigAuto = {};
  CONFIG_AUTO_REPORT_CONST = CONFIG_AUTO_REPORT_CONST;
  TRANSACTION_ENTITY_STATUS = TRANSACTION_ENTITY_STATUS;
  ENTITY_STATUS = ENTITY_STATUS;
  isShowDeleteMail: boolean[] = [];

  constructor(
    private fb: FormBuilder,
    private toastService: ToastrCustomService,
    private configAutoService: ConfigAutoService,
    public activeModal: NgbActiveModal
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.configAutoService
      .info({ type: CONFIG_AUTO_TYPE_CONST.ENTRUSTED_HISTORY })
      .subscribe((res: any) => {
        if (res.body !== null) {
          this.data = res.body;
          this.getMail(res.body);
        } else {
          this.addEmail();
          this.isShowDeleteMail[CONFIG_AUTO_REPORT_CONST.MAIL_DEFAULT] = false;
        }
      });
  }

  getMail(data?: IConfigAuto) {
    this.formCreate.controls.title.setValue(data?.title);
    this.formCreate.controls.content.setValue(data?.content);
    this.formCreate.controls.transactionStatuses.setValue(
      data?.transactionStatuses
    );
    this.formCreate.controls.sendTime.setValue(this.getTime(data));
    this.formCreate.controls.status.setValue(data?.status);
    this.formCreate.controls.configureAutomaticReportId.setValue(
      data?.configureAutomaticReportId
    );

    this.emails = this.formCreate.get('emails') as FormArray;

    this.data?.emailRecipients?.forEach((item: any) => {
      this.emails.push(this.createEmail(item.email));
    });
    if (
      data?.emailRecipients.length ===
      CONFIG_AUTO_REPORT_CONST.MAIL_DEFAULT_LENGTH
    ) {
      this.isShowDeleteMail[CONFIG_AUTO_REPORT_CONST.MAIL_DEFAULT] = false;
    } else {
      this.onShowDeleteMail();
    }
  }

  initForm() {
    this.formCreate = this.fb.group({
      title: ['', [Validators.required]],
      content: ['', [Validators.required]],
      transactionStatuses: [
        [ENTITY_TRANSACTION_STATUS_CONST.SUCCESS.code],
        [Validators.required],
      ],
      status: [ENTITY_STATUS_CONST.INACTIVE.code, [Validators.required]],
      sendTime: [null, [Validators.required]],
      type: CONFIG_AUTO_TYPE_CONST.ENTRUSTED_HISTORY,
      configureAutomaticReportId: '',
      emails: new FormArray([], this.duplicateEmailValidator),
    });
  }

  createEmail(email?: any) {
    return this.fb.group({
      email: [
        email,
        [Validators.required, Validators.pattern(VALIDATORS.PATTERN.EMAIL)],
      ],
    });
  }

  duplicateEmailValidator: ValidatorFn = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (!control || !(control instanceof Array || control instanceof Object))
      return null;

    const formArray = control as any;
    if (!formArray || formArray.length === 0) return null;

    const emailList: string[] = formArray.controls
      .map((group: FormGroup) => group.controls.email?.value?.trim())
      .filter((email: string) => !!email);

    const duplicates = emailList.filter(
      (email, index, self) => self.indexOf(email) !== index
    );

    formArray.controls.forEach((group: FormGroup) => {
      const email = group.controls.email.value?.trim();
      if (duplicates.includes(email)) {
        group.controls.email?.setErrors({
          ...group.controls.email?.errors,
          duplicate: true,
        });
      } else {
        const errors = { ...group.controls.email?.errors };
        if (errors?.duplicate) {
          delete errors.duplicate;
          group.controls.email?.setErrors(
            Object.keys(errors).length ? errors : null
          );
        }
      }
    });

    return null;
  };

  onSelectAll() {
    this.formCreate.controls.transactionStatuses?.patchValue(
      this.TRANSACTION_ENTITY_STATUS.map((item) => item.code)
    );
  }

  onClearAll() {
    this.formCreate.controls.transactionStatuses?.patchValue([]);
  }

  addEmail(): void {
    this.emails = this.formCreate.get('emails') as FormArray;
    this.emails.push(this.createEmail());
    this.onShowDeleteMail();
  }

  onDeleteEmail(index: number) {
    this.emails.removeAt(index);
    this.isShowDeleteMail[CONFIG_AUTO_REPORT_CONST.MAIL_DEFAULT] =
      this.emails.controls.length ===
      CONFIG_AUTO_REPORT_CONST.MAIL_DEFAULT_LENGTH
        ? false
        : true;
  }

  onShowDeleteMail() {
    this.emails.controls.forEach((item, i: number) => {
      this.isShowDeleteMail[i] = true;
    });
  }

  getTime(data?: IConfigAuto) {
    const [hours, minutes, seconds] = data?.sendTime.split(':');
    return moment(
      new Date(
        new Date().getDay(),
        new Date().getMonth(),
        new Date().getDay(),
        +hours,
        +minutes,
        +seconds
      )
    ).format(MOMENT_CONST.DATE_TIME_MOMENT_FORMAT);
  }

  sendMail() {
    if (this.formCreate.invalid) {
      CommonUtils.markFormGroupTouched(this.formCreate);
      return;
    }
    const emails: string[] = [];
    this.emails.controls.forEach((element) => {
      emails.push(element.value.email);
    });
    const body = {
      ...this.formCreate.value,
      sendTime: moment(this.formCreate.value.sendTime).format(
        MOMENT_CONST.FORMAT_TIME
      ),
      emails,
    };
    const serviceEvent =
      Object.keys(this.data).length === 0
        ? this.configAutoService.create(body)
        : this.configAutoService.update(body);
    serviceEvent.subscribe((res: any) => {
      this.toastService.success(
        this.data
          ? 'common.action.updateSuccess'
          : 'common.action.createSuccess'
      );
      this.activeModal.close();
    });
  }
}
