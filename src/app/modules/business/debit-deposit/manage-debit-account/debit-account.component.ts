import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import {
  CASHOUT_CONFIGURATION_TYPE_CONST,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  MODAL_ACTION,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { IDebitAccount } from '@shared/models/debit-account.model';
import { DebitAccountService } from '@shared/services/debit-account.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import { DebitAccountCreateComponent } from './debit-account-create/debit-account-create.component';
import { DebitAccountDetailUpdateComponent } from './debit-account-detail-update/debit-account-detail-update.component';

@Component({
  selector: 'app-debit-account',
  templateUrl: './debit-account.component.html',
  styleUrls: ['./debit-account.component.scss'],
})
export class ManageDebitAccountComponent implements OnInit {
  paymentData: IDebitAccount[] = [];
  formSearch: FormGroup = new FormGroup({});
  pageSizeOptions = PAGINATION.OPTIONS;

  PAGINATION = PAGINATION;
  SYSTEM_RULES = SYSTEM_RULES;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  CASHOUT_CONFIGURATION_TYPE_CONST = CASHOUT_CONFIGURATION_TYPE_CONST;
  isChecked = true;
  checked?: boolean;
  constructor(
    private debitAccountService: DebitAccountService,
    private fb: FormBuilder,
    private modal: NgbModal,
    private modalService: ModalService,
    private toastService: ToastrCustomService
  ) {
    this.formSearch = this.fb.group({
      keyword: '',
      status: null,
      pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
      pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
      length: 0,
      hasPageable: true,
    });
  }

  ngOnInit(): void {
    this.onSearch();
  }

  onSearch() {
    this.debitAccountService.getAll().subscribe((res: any) => {
      this.paymentData = res.body;
      const data = this.paymentData.map(
        (item: IDebitAccount) => item.configurationType
      );
    });
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formSearch.value.pageIndex,
      this.formSearch.value.pageSize
    );
  }

  onsearchSubmit() {
    this.formSearch.controls.pageIndex.setValue(PAGINATION.PAGE_NUMBER_DEFAULT);
    this.formSearch.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    this.onSearch();
  }

  onChangePage(page: PageEvent) {
    this.formSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  openCreateModal() {
    const modalRef = this.modal.open(DebitAccountCreateComponent, {
      size: 'lg',
      keyboard: true,
      backdrop: 'static',
      centered: true,
    });
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;
    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }

  openUpdateDetailModal(action: string, id: number) {
    const modalRef = this.modal.open(DebitAccountDetailUpdateComponent, {
      size: 'lg',
      keyboard: true,
      backdrop: 'static',
      centered: true,
    });
    if (action === ROUTER_ACTIONS.update) {
      modalRef.componentInstance.id = id;
      modalRef.componentInstance.action = action;
      modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;
      modalRef.result.then((result) => {
        if (result === MODAL_ACTION.CONFIRM.code) {
          this.onSearch();
        }
      });
      return;
    }
    if (action === ROUTER_ACTIONS.detail) {
      modalRef.componentInstance.id = id;
      modalRef.componentInstance.action = action;
      return;
    }
  }

  lockAndUnlock(item: IDebitAccount) {
    if (item.status === ENTITY_STATUS_CONST.INACTIVE.code) {
      this.unLockCashout(item);
    } else {
      this.lockCashout(item);
    }
  }

  private lockCashout(item: IDebitAccount) {
    const modalData = {
      title: 'managePayment.msg.lock',
      content: 'managePayment.msg.lockContent',
      interpolateParams: {
        name: `<b>${item.accountName || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.debitAccountService.lock(item.id).subscribe((res) => {
          this.toastService.success('common.action.lockSuccess');
          this.onSearch();
        });
      }
    });
  }

  private unLockCashout(item: IDebitAccount) {
    const modalData = {
      title: 'managePayment.msg.unlock',
      content: 'managePayment.msg.unlockContent',
      interpolateParams: {
        name: `<b>${item.accountName || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.debitAccountService.unLock(item.id).subscribe((res) => {
          this.toastService.success('common.action.unlockSuccess');
          this.onSearch();
        });
      }
    });
  }

  delete(item: IDebitAccount) {
    const modalData = {
      title: 'managePayment.msg.delete',
      content: 'managePayment.msg.deleteContent',
      interpolateParams: {
        name: `<b>${item.accountName || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.debitAccountService.deletePayment(item.id).subscribe((res) => {
          this.toastService.success('common.action.deleteSuccess');
          this.onSearch();
        });
      }
    });
  }
}
