import { Component, HostListener, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { MODAL_ACTION } from '@shared/constants/app.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { MODEL_MAP_ITEM_COMMON } from '@shared/models/model.type';
import { AccountService } from '@shared/services/account.service';
import { AuthProviderService } from '@shared/services/auth/auth-provider.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { encrypt, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import Validation from '@shared/validators/confirmed-password.validator';

@Component({
  selector: 'app-change-password',
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.scss'],
})
export class ChangePasswordComponent implements OnInit {
  VALIDATORS = VALIDATORS;

  passwordVisible = false;

  confirmPasswordVisible = false;

  currentPasswordVisible = false;

  userChangePasswordForm = this.fb.group(
    {
      currentPassword: ['', [Validators.required]],
      password: [
        '',
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.PASSWORD),
          Validators.minLength(VALIDATORS.LENGTH.PASSWORD_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.PASSWORD_MAX_LENGTH),
        ],
      ],
      confirmPassword: [
        '',
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.PASSWORD),
          Validators.minLength(VALIDATORS.LENGTH.PASSWORD_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.PASSWORD_MAX_LENGTH),
        ],
      ],
      token: [''],
    },
    {
      validators: [Validation.match('password', 'confirmPassword')],
    }
  );

  // Kiểm tra mật khẩu khác nhau hay không
  get f(): { [key: string]: AbstractControl } {
    return this.userChangePasswordForm.controls;
  }

  public title = 'common.action.confirm';
  public content = '';
  public interpolateParams: object = {};
  public isHiddenBtnClose = false;
  public action!: MODEL_MAP_ITEM_COMMON;

  MODAL_ACTION = MODAL_ACTION;

  constructor(
    public activeModal: NgbActiveModal,
    public translateService: TranslateService,
    private fb: FormBuilder,
    private translate: TranslateService,
    private accountService: AccountService,
    private toastService: ToastrCustomService,
    private authProviderService: AuthProviderService,
    private router: Router,
  ) {}

  ngOnInit(): void {}

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.activeModal.close();
  }

  /**
   * displayContent: display content in file i18n
   *
   * @param content string
   * @returns string
   */
  displayContent(content: string): string {
    return this.translate.instant(content);
  }

  changePassword(): void {
    const { currentPassword, password, confirmPassword } =
      this.userChangePasswordForm.value;
    if (this.userChangePasswordForm.invalid) {
      CommonUtils.markFormGroupTouched(this.userChangePasswordForm);
    }
    const params = this.userChangePasswordForm.getRawValue();
    if (this.userChangePasswordForm.valid) {
      // encrypt password
      params.currentPassword = encrypt(currentPassword) + '';
      params.password = encrypt(password) + '';
      params.confirmPassword = encrypt(confirmPassword) + '';
      this.accountService.changePasswordUser(params).subscribe((res: any) => {
        this.toastService.success('common.action.changePasswordSuccess');
        this.activeModal.close();
        this.authProviderService.logout();
        this.router.navigate([ROUTER_UTILS.auth.login]);
      });
    }
  }

  /**
   * Change type view password
   */
  onChangPasswordVisible(): void {
    this.passwordVisible = !this.passwordVisible;
  }

  /**
   * Change type view password
   */
  onChangConfirmPasswordVisible(): void {
    this.confirmPasswordVisible = !this.confirmPasswordVisible;
  }

  /**
   * Change type view password
   */
  onChangCurrentPasswordVisible(): void {
    this.currentPasswordVisible = !this.currentPasswordVisible;
  }
}
