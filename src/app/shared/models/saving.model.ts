export interface ISaving {
  savingAccountId?: number;
  receivingAccountNumber?: string;
  savingAccountNumber?: string;
  startTime?: string;
  settlementDueTime?: string;
  interestRate?: number;
  tenor?: number;
  savingAmount?: number;
  finalizationMethod?: string;
  type?: string;
  status?: number;
  fullname?: string;
  idCardNumber?: string;
  phoneNumber?: string;
  cif?: string;
  amtAfterSettlement?: number;
  interestAmtEnd?: number;
  interestAmt?: number;
  rmCode?: string;
}

export interface ISavingTransaction {
  description?: string;
  status?: number;
  transactionTime?: string;
  transactionAmount?: string;
  savingAccountNumber?: string;
  transactionCode?: string;
  transferType?: string;
  customerAccNumber?: string;
  id?: number;
}

export class Saving implements ISaving {
  constructor(
    public savingAccountId?: number,
    public name?: string,
    public receivingAccountNumber?: string,
    public savingAccountNumber?: string,
    public startTime?: string,
    public settlementDueTime?: string,
    public interestRate?: number,
    public tenor?: number,
    public savingAmount?: number,
    public finalizationMethod?: string,
    public type?: string,
    public status?: number,
    public fullname?: string,
    public idCardNumber?: string,
    public phoneNumber?: string,
    public cif?: string,
    public amtAfterSettlement?: number,
    public interestAmtEnd?: number,
    public interestAmt?: number,
    public customerAccNumber?: string,
    public rmCode?: string
  ) {
    this.savingAccountId = savingAccountId;
    this.name = name;
    this.receivingAccountNumber = receivingAccountNumber;
    this.savingAccountNumber = savingAccountNumber;
    this.startTime = startTime;
    this.settlementDueTime = settlementDueTime;
    this.interestRate = interestRate;
    this.tenor = tenor;
    this.savingAmount = savingAmount;
    this.finalizationMethod = finalizationMethod;
    this.type = type;
    this.status = status;
    this.fullname = fullname;
    this.idCardNumber = idCardNumber;
    this.phoneNumber = phoneNumber;
    this.cif = cif;
    this.amtAfterSettlement = amtAfterSettlement;
    this.interestAmtEnd = interestAmtEnd;
    this.interestAmt = interestAmt;
    this.customerAccNumber = customerAccNumber;
    this.rmCode = rmCode;
  }
}
