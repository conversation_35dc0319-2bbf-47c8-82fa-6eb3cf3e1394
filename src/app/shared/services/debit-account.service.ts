import { Injectable } from '@angular/core';
import {
  IDebitAccount,
  IDebitAccountCreate,
} from '@shared/models/debit-account.model';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';
@Injectable({
  providedIn: 'root',
})
export class DebitAccountService extends AbstractService {
  public resourceUrl = '/debit-account';

  /**
   * search
   *
   * @param request IPosition
   * @returns Observable<EntityResponseType<IPosition[]>>
   */
  getAll(): Observable<EntityResponseType<IDebitAccount>> {
    return super.get<EntityResponseType<IDebitAccount[]>>(
      `${this.resourceUrl}`
    );
  }
  /**
   * API create
   *
   * @param request IPosition
   * @returns Observable<EntityResponseType<IPosition>>
   */
  create(
    request: IDebitAccountCreate
  ): Observable<EntityResponseType<IDebitAccount>> {
    return super.post<EntityResponseType<IDebitAccount>>(
      `${this.resourceUrl}/create`,
      request
    );
  }

  /**
   * API update
   *
   * @param request IPosition
   * @returns Observable<EntityResponseType<IPosition>>
   */
  update(
    request: IDebitAccountCreate,
    id: number
  ): Observable<EntityResponseType<IDebitAccount>> {
    return super.post<EntityResponseType<IDebitAccount>>(
      `${this.resourceUrl}/update/${id}`,
      request
    );
  }

  /**
   * API detail
   *
   * @param request IPosition
   * @returns Observable<EntityResponseType<ILoanOnline>>
   */
  detail(id: number): Observable<EntityResponseType<IDebitAccount>> {
    return super.get<EntityResponseType<IDebitAccount>>(
      `${this.resourceUrl}/${id}`
    );
  }

  unLock(id: number): Observable<EntityResponseType<IDebitAccount>> {
    return super.post<EntityResponseType<IDebitAccount>>(
      `${this.resourceUrl}/activate/${id}`
    );
  }

  lock(id: number): Observable<EntityResponseType<IDebitAccount>> {
    return super.post<EntityResponseType<IDebitAccount>>(
      `${this.resourceUrl}/deactivate/${id}`
    );
  }

  deletePayment(id: number): Observable<EntityResponseType<IDebitAccount>> {
    return super.post<EntityResponseType<IDebitAccount>>(
      `${this.resourceUrl}/delete/${id}`
    );
  }

  manualAuto(
    request?: IDebitAccountCreate
  ): Observable<EntityResponseType<IDebitAccount>> {
    return super.post<EntityResponseType<IDebitAccount>>(
      `${this.resourceUrl}/manual-auto`,
      request
    );
  }
}
