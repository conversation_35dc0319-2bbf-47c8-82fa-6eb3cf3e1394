body {
  font-family: "<PERSON><PERSON>", sans-serif;
  min-height: 100vh;
}

.bg-login {
  background-image: url('../../../../assets/dist/img/bglogin.png');
  background-size: cover;
}

.bg-login {
  justify-content: center;
}

.wrap-login {
  width: 630px;
  background: rgba(255, 255, 255, 0.829);
  border-radius: 24px;
}

.icon-eye {
  position: absolute;
  right: 15px;
  top: 42px;
  font-size: 20px;
}

.btn-refresh {
  position: absolute;
  right: 1px;
  top: 18px;
}

.btn-refresh img {
  padding-top: 19px;
}

.brand-wrapper {
  margin-bottom: 50px;
}

.brand-wrapper .logo {
  height: 40px;
}

.login-card .card-body {
  padding: 78px 77px 60px;
}

@media (max-width: 422px) {
  .login-card .card-body {
    padding: 35px 24px;
  }
}

.login-card-description {
  font-size: 20px;
  color: #000;
  font-weight: normal;
  margin-bottom: 23px;
}

label {
  font-size: 16px;
}

.form-control {
  border-radius: 12px;
  height: 48px;
}

.login-card .login-btn {
  padding: 17px 20px 12px;
  background-color: #EB2D4B;
  border-radius: 12px;
  font-size: 17px;
  color: #fff;
  margin-bottom: 24px;
}

.login-card .login-btn:hover {
  background-color: #EB2D4B;
  color: white;
}

.login-card .forgot-password-link {
  font-size: 14px;
  color: #919aa3;
  margin-bottom: 12px;
}

.login-card-footer-text {
  font-size: 16px;
  color: #0d2366;
  margin-bottom: 60px;
}

@media (max-width: 767px) {
  .login-card-footer-text {
    margin-bottom: 24px;
  }
}

.login-card-footer-nav a {
  font-size: 14px;
  color: #919aa3;
}

.text-primary-red {
  color: red !important;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.title-link {
  font-size: 16px;
  font-weight: bold;
  box-shadow: 0 3px rgb(71, 151, 255);
}

.form-group {
  margin-bottom: 0rem !important;
}
