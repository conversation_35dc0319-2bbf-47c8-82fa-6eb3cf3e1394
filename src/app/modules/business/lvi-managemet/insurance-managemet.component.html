<div class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formSearch" (submit)="onsearchSubmit()">
        <div class="row">
          <div class="col-lg-2">
            <div class="form-group">
              <label>{{ "common.action.searchKeyword" | translate }}</label>
              <input
                trim
                class="form-control w-100"
                formControlName="keyword"
                type="text"
                placeholder="{{ 'common.action.searchKeyword' | placeholder }}"
              />
            </div>
          </div>
          <div class="col-lg-4">
            <div class="form-group row">
              <div class="col-md-6">
                <label class=""
                  >{{ "model.insuranceManage.fromDate" | translate
                  }}<small class="text-danger">*</small></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="fromDate"
                    formControlName="fromDate"
                    [placeholder]="MOMENT_CONST.FORMAT_DATE_PLACEHOLDER"
                    max="{{
                      formSearch.controls['toDate'].value
                        ? (formSearch.controls['toDate'].value
                          | date : MOMENT_CONST.FORMAT_DATE_MAT_PICKER)
                        : (maxDate | date : MOMENT_CONST.FORMAT_DATE_MAT_PICKER)
                    }}"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="fromDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #fromDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="text-danger"
                  *ngIf="
                    formSearch.get('fromDate')?.errors?.required &&
                    formSearch.get('fromDate')?.touched
                  "
                >
                  {{
                    "error.insuranceManage.required.fromDate" | translate
                  }}</small
                >
                <small
                  class="text-danger"
                  *ngIf="
                    formSearch.get('fromDate')?.errors
                      ?.invalidFromDateCurrent &&
                    formSearch.get('fromDate')?.touched
                  "
                >
                  {{
                    "error.insuranceManage.dateTime.invalidFromDateCurrent"
                      | translate
                  }}</small
                >
              </div>
              <div class="col-md-6">
                <label class=""
                  >{{ "model.insuranceManage.toDate" | translate
                  }}<small class="text-danger">*</small></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="toDate"
                    formControlName="toDate"
                    [placeholder]="MOMENT_CONST.FORMAT_DATE_PLACEHOLDER"
                    dateTransform
                    [max]="maxDate"
                    min="{{
                      formSearch.controls['fromDate'].value
                        | date : MOMENT_CONST.FORMAT_DATE_MAT_PICKER
                    }}"
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="toDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #toDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="text-danger"
                  *ngIf="
                    formSearch.get('toDate')?.errors?.required &&
                    formSearch.get('toDate')?.touched
                  "
                >
                  {{
                    "error.insuranceManage.required.toDate" | translate
                  }}</small
                >
                <small
                  class="text-danger"
                  *ngIf="
                    formSearch.get('toDate')?.errors?.invalidToDate &&
                    formSearch.get('toDate')?.touched
                  "
                >
                  {{
                    "error.insuranceManage.dateTime.invalidToDate" | translate
                  }}</small
                >
                <small
                  class="text-danger"
                  *ngIf="
                    formSearch.get('toDate')?.errors?.invalidToDateCurrent &&
                    formSearch.get('toDate')?.touched
                  "
                >
                  {{
                    "error.insuranceManage.dateTime.invalidToDateCurrent"
                      | translate
                  }}</small
                >
              </div>
            </div>
          </div>
          <div class="col-lg-2">
            <div class="form-group">
              <label>{{ "model.insuranceManage.type" | translate }}</label>
              <ng-select
                appearance="outline"
                [searchable]="false"
                placeholder="{{
                  'model.insuranceManage.placeholder.type'
                    | placeholder : 'select'
                }}"
                [clearable]="true"
                formControlName="insuranceType"
                (change)="onSelectType($event)"
              >
                <ng-option *ngFor="let item of insuranceType" [value]="item">
                  {{
                    item === PRODUCT_TYPE_VALUE_CONST.HEALTH.value
                      ? ("model.insuranceManage.health" | translate)
                      : ("model.insuranceManage.vehicle" | translate)
                  }}
                </ng-option></ng-select
              >
            </div>
          </div>
          <div class="col-lg-2">
            <div class="form-group">
              <label>{{
                "model.insuranceManage.productPackage" | translate
              }}</label>
              <ng-select
                appearance="outline"
                [searchable]="false"
                placeholder="{{
                  'model.insuranceManage.placeholder.productPackage'
                    | placeholder : 'select'
                }}"
                [clearable]="true"
                formControlName="packageCode"
              >
                <ng-container *ngIf="healthPackage.length !== 0">
                  <ng-option
                    *ngFor="let item of healthPackage"
                    [value]="item.code"
                  >
                    <span
                      [title]="item.nameVn"
                      *ngIf="local_lang === LANGUAGES.VI.code"
                      >{{ item.nameVn }}</span
                    >
                    <span
                      [title]="item.nameEn"
                      *ngIf="local_lang === LANGUAGES.EN.code"
                      >{{ item.nameEn }}</span
                    >
                    <span
                      [title]="item.nameLa"
                      *ngIf="local_lang === LANGUAGES.LAO.code"
                      >{{ item.nameLa }}</span
                    >
                  </ng-option>
                </ng-container>
                <ng-container *ngIf="vehiclePackage.length !== 0">
                  <ng-option
                    *ngFor="let item of vehiclePackage"
                    [value]="item.code"
                  >
                    <span
                      [title]="item.nameVn"
                      *ngIf="local_lang === LANGUAGES.VI.code"
                      >{{ item.nameVn }}</span
                    >
                    <span
                      [title]="item.nameEn"
                      *ngIf="local_lang === LANGUAGES.EN.code"
                      >{{ item.nameEn }}</span
                    >
                    <span
                      [title]="item.nameLa"
                      *ngIf="local_lang === LANGUAGES.LAO.code"
                      >{{ item.nameLa }}</span
                    >
                  </ng-option>
                </ng-container>
              </ng-select>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-2">
            <div class="form-group">
              <label>{{
                "model.insuranceManage.vehicleType" | translate
              }}</label>
              <ng-select
                appearance="outline"
                [searchable]="false"
                placeholder="{{
                  'model.insuranceManage.placeholder.vehicleType'
                    | placeholder : 'select'
                }}"
                [clearable]="true"
                formControlName="vehicleCode"
              >
                <ng-option *ngFor="let item of vehicleType" [value]="item.code">
                  <span
                    [title]="item.nameVn"
                    *ngIf="local_lang === LANGUAGES.VI.code"
                    >{{ item.nameVn }}</span
                  >
                  <span
                    [title]="item.nameEn"
                    *ngIf="local_lang === LANGUAGES.EN.code"
                    >{{ item.nameEn }}</span
                  >
                  <span
                    [title]="item.nameLa"
                    *ngIf="local_lang === LANGUAGES.LAO.code"
                    >{{ item.nameLa }}</span
                  >
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
              (click)="onReset()"
            >
              <div class="btn-reset">
                <i class="bi bi-arrow-clockwise" type="button"> </i>
              </div>
            </div>
            <div class="">
              <button
                class="btn btn-search mr-2"
                type="submit"
                *hasPrivileges="SYSTEM_RULES.INSURANCE_READ"
              >
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div
          class="d-flex justify-content-between align-items-end text-right mb-3 mt-4"
        >
          <h5 class="title-table">
            {{ "model.insuranceManage.title" | translate }}
          </h5>
          <button
            class="btn btn-red"
            type="button"
            *hasPrivileges="SYSTEM_RULES.INSURANCE_EXPORT"
            (click)="exportFile()"
          >
            {{ "common.action.export" | translate }}
          </button>
        </div>
      </form>

      <div class="table-responsive">
        <table class="table table-insurance">
          <thead>
            <tr>
              <th [width]="'50px'" class="text-center">
                {{ "model.insuranceManage.no" | translate }}
              </th>
              <th [width]="'180px'" class="text-right">
                {{ "model.insuranceManage.cifNumber" | translate }}
              </th>
              <th class="text-left" [width]="'250px'">
                {{ "model.insuranceManage.customerName" | translate }}
              </th>
              <th [width]="'130px'" class="text-right">
                {{ "model.insuranceManage.phoneNumber" | translate }}
              </th>
              <th [width]="'220px'" class="text-right">
                {{ "model.insuranceManage.insurancePolicy" | translate }}
              </th>
              <th [width]="'220px'" class="text-left">
                {{ "model.insuranceManage.type" | translate }}
              </th>
              <th [width]="'300px'" class="text-left">
                {{ "model.insuranceManage.productPackage" | translate }}
              </th>
              <th [width]="'190px'" class="text-left">
                {{ "model.insuranceManage.vehicleType" | translate }}
              </th>
              <th [width]="'200px'" class="text-right">
                {{ "model.insuranceManage.discount" | translate }}
              </th>
              <th [width]="'200px'" class="text-right">
                {{ "model.insuranceManage.moneyFromLvi" | translate }}
              </th>
              <th [width]="'200px'" class="text-right">
                {{ "model.insuranceManage.discountMB" | translate }}
              </th>
              <th [width]="'200px'" class="text-right">
                {{ "model.insuranceManage.moneyFromMb" | translate }}
              </th>
              <th [width]="'200px'" class="text-right">
                {{ "model.insuranceManage.totalAmount" | translate }}
              </th>
              <th [width]="'180px'" class="text-left">
                {{ "model.insuranceManage.transactionCode" | translate }}
              </th>
              <th [width]="'200px'" class="text-right">
                {{ "model.insuranceManage.transactionTime" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of insuranceData; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td class="text-right">{{ item.cif }}</td>
              <td class="text-left">
                {{ item.insuredName }}
              </td>
              <td class="text-right">{{ item.insuredPhone }}</td>
              <td class="text-right">{{ item.identificationNo }}</td>
              <td class="text-left">
                {{
                  item.type === PRODUCT_TYPE_VALUE_CONST.HEALTH.value
                    ? ("model.insuranceManage.health" | translate)
                    : ("model.insuranceManage.vehicle" | translate)
                }}
              </td>
              <td class="text-left">
                <span *ngIf="local_lang === LANGUAGES.VI.code">
                  {{
                    PACKAGE_INSURANCE_HEALTH_MAP[item?.packageCode]?.nameVn
                      ? PACKAGE_INSURANCE_HEALTH_MAP[item?.packageCode]?.nameVn
                      : PACKAGE_INSURANCE_VEHICLE_MAP[item?.packageCode]?.nameVn
                  }}
                </span>
                <span *ngIf="local_lang === LANGUAGES.EN.code">
                  {{
                    PACKAGE_INSURANCE_HEALTH_MAP[item?.packageCode]?.nameEn
                      ? PACKAGE_INSURANCE_HEALTH_MAP[item?.packageCode]?.nameEn
                      : PACKAGE_INSURANCE_VEHICLE_MAP[item?.packageCode]?.nameEn
                  }}
                </span>

                <span *ngIf="local_lang === LANGUAGES.LAO.code">
                  {{
                    PACKAGE_INSURANCE_HEALTH_MAP[item?.packageCode]?.nameLa
                      ? PACKAGE_INSURANCE_HEALTH_MAP[item?.packageCode]?.nameLa
                      : PACKAGE_INSURANCE_VEHICLE_MAP[item?.packageCode]?.nameLa
                  }}
                </span>
              </td>
              <td class="text-left">
                <span *ngIf="local_lang === LANGUAGES.VI.code">
                  {{
                    VEHICLE_TYPE_MAP[item?.vehicleCode]?.nameVn
                      ? VEHICLE_TYPE_MAP[item?.vehicleCode]?.nameVn
                      : ""
                  }}
                </span>
                <span *ngIf="local_lang === LANGUAGES.EN.code">
                  {{
                    VEHICLE_TYPE_MAP[item?.vehicleCode]?.nameEn
                      ? VEHICLE_TYPE_MAP[item?.vehicleCode]?.nameEn
                      : ""
                  }}
                </span>

                <span *ngIf="local_lang === LANGUAGES.LAO.code">
                  {{
                    VEHICLE_TYPE_MAP[item?.vehicleCode]?.nameLa
                      ? VEHICLE_TYPE_MAP[item?.vehicleCode]?.nameLa
                      : ""
                  }}
                </span>
              </td>
              <td class="text-right">
                {{ item.discount ? item.discount : "" }}
              </td>
              <td class="text-right">
                {{ item.payTotal ? (item.payTotal | currencyLak) : "" }}
              </td>
              <td class="text-right">
                {{ item.mbDiscount ? item.mbDiscount : "" }}
              </td>
              <td class="text-right">
                {{ item.mbFee ? (item.mbFee | currencyLak) : "" }}
              </td>
              <td class="text-right">
                {{ item.mbPayTotal ? (item.mbPayTotal | currencyLak) : "" }}
              </td>
              <td class="bg-white" class="text-left">
                {{ item.transactionCode }}
              </td>
              <td class="bg-white" class="text-right" [width]="'150px'">
                {{ item.transactionTime }}
              </td>
            </tr>
          </tbody>
        </table>
        <div
          class="row d-block text-center m-0 no-search-result-wrapper"
          *ngIf="insuranceData?.length === 0"
        >
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
      </div>
      <div *ngIf="insuranceData.length">
        <mat-paginator
          [length]="formSearch.value.length"
          [pageSize]="formSearch.value.pageSize"
          [pageIndex]="formSearch.value.pageIndex"
          [pageSizeOptions]="pageSizeOptions"
          (page)="onChangePage($event)"
          aria-label="Select page"
        >
        </mat-paginator>
      </div>
    </div>
  </div>
</div>
