import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { ACTION_TOKEN_CONST } from '@shared/constants/user.constants';
import { ActiveToken } from '@shared/models/active-token.model';
import { ActiveTokenService } from '@shared/services/active-token.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_UTILS } from '@shared/utils';

@Component({
  selector: 'app-active-user',
  templateUrl: './active-user.component.html',
  styleUrls: ['./active-user.component.scss'],
})
export class ActiveUserComponent implements OnInit {
  activeTokenDTO = new ActiveToken();

  activeToken = new ActiveToken();

  // check expire active token 24h
  // expire24Hour = false;

  // check user active or not
  active = false;

  action = '';

  ACTION_TOKEN_CONST = ACTION_TOKEN_CONST;

  isResendSucces = false;

  constructor(
    private routerActive: ActivatedRoute,
    private router: Router,
    private activeTokenServive: ActiveTokenService,
    private toastService: ToastrCustomService,
    private translate: TranslateService
  ) {}

  ngOnInit(): void {
    // get token from paramater
    this.routerActive.paramMap.subscribe((res) => {
      const token = res.get('token');
      if (token) {
        this.activeTokenDTO.token = token;

        this.verify(token);
      }
    });
  }

  /**
   * get detail loan online
   */
  verify(token: string): void {
    if (token) {
      const params = { token };
      this.activeTokenServive.verify(params).subscribe((res: any) => {
        this.activeToken = res.body;
        const data = (res.body || undefined) as ActiveToken;
        if (data.user?.active) {
          this.active = true;
        }
        if (data.tokenAction) {
          this.action = data.tokenAction;
        }
      });
    }
  }

  /**
   * onReSend
   */
  onReSend(): void {
    const params = {
      token: this.activeTokenDTO.token,
    };
    this.activeTokenServive.resend(params).subscribe((res: any) => {
      this.toastService.success('common.success');
      this.isResendSucces = true;
    });
  }

  displayContent(content: string): string {
    return this.translate.instant(content);
  }

  goToLogin(): void {
    this.router.navigate([ROUTER_UTILS.auth.login]);
  }
}
