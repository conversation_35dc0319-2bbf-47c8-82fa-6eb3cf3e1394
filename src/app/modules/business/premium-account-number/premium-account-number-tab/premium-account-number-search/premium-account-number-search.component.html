<div class="">
  <div class="col-md-12">
    <form [formGroup]="formSearch">
      <div class="row">
        <div class="col-md-8 col-lg-8">
          <div class="form-group">
            <label>{{ "model.premiumAccountNumber.series" | translate }}</label>
            <input trim numbersOnly type="text" placeholder="{{
                'model.premiumAccountNumber.seriesInput' | translate
              }}" formControlName="expectedString" class="w-100" class="form-control"
              [maxLength]="VALIDATORS.LENGTH.MINI_SMALL_TEXT_MAX_LENGTH" />
          </div>
        </div>
        <div class="col-md-2 col-lg-2">
          <div class="form-group">
            <label>{{ "model.structureAccount.length" | translate }}</label>
            <ng-select placeholder="{{
                'model.structureAccount.length' | placeholder : 'select'
              }}" [searchable]="false" formControlName="accountLengths" [multiple]="true" [clearable]="true">
              <ng-option [value]="item.code" *ngFor="let item of numberGroups">
                {{ item.label }}
              </ng-option>
              <ng-template ng-header-tmp>
                <div>
                  <button class="btn btn-link" type="button" (click)="onSelectAll(SELECTED_CONST.NUMBER_GROUP)">
                    {{ "common.action.selectAll" | translate }}
                  </button>
                  <button class="btn btn-link" type="button" (click)="onClearAll(SELECTED_CONST.NUMBER_GROUP)">
                    {{ "common.action.clearAll" | translate }}
                  </button>
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="col-md-2 col-lg-2">
          <div class="form-group">
            <label>{{
              "model.specialAccountNumber.numberType" | translate
              }}</label>
            <ng-select placeholder="{{
                'model.specialAccountNumber.numberType' | placeholder : 'select'
              }}" [searchable]="false" formControlName="numberTypes" [clearable]="true">
              <ng-option [value]="item.code" *ngFor="let item of NUMBER_TYPE_STATUS">
                {{ item.label | translate }}
              </ng-option>
            </ng-select>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-lg-2">
          <div class="form-group">
            <label>{{
              "model.structureAccount.hideAccountNumber" | translate
              }}</label>
            <ng-select [searchFn]="customSpecialAccNumberSearchFn" [items]="specialPremiumAccNumbers"
              bindLabel="specialPremiumAccountNumber" bindValue="specialPremiumAccountNumber" [multiple]="false"
              appearance="outline" [searchable]="true" [clearable]="true" placeholder="{{
                'model.structureAccount.hideAccountNumberPlaceHolder'
                  | placeholder : 'select'
              }}" formControlName="hidedAccount" (change)="selectAddAvoidNumber($event)">
              <ng-option [value]="item.specialPremiumAccountNumber" *ngFor="let item of specialPremiumAccNumbers">
                {{ item.specialPremiumAccountNumber }}
              </ng-option>
            </ng-select>
          </div>
        </div>
        <div class="col-lg-2">
          <div class="form-group">
            <label>{{
              "model.structureAccount.addHideAccNumber" | translate
              }}</label>
            <input numbersOnly trim type="text" placeholder="{{
                'model.structureAccount.accountNumber' | placeholder
              }}" formControlName="accountNumber" class="w-100" class="form-control"
              [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH" />
          </div>
        </div>
        <div class="col-lg-2 col-md-2 mt-4 d-flex">
          <div>
            <button class="btn px-1 py-0 mt-2 add-mail" (click)="enterAddAvoidNumber()" type="button">
              <i class="fa fa-plus-circle" aria-hidden="true"></i>
            </button>
          </div>
        </div>
      </div>
      <div class="col-lg-12">
        <div class="form-group">
          <ng-select [hideSelected]="true" [multiple]="true" appearance="outline" [searchable]="false"
            [clearable]="true" formControlName="hideAccountNumbers" [clearable]="false" class="custom-display-select"
            (ngModelChange)="onAvoidNumber($event)">
            <ng-option *ngFor="let item of itemSelect" [value]="item">
              {{ item }}
            </ng-option>
          </ng-select>
        </div>
      </div>
      <div class="container d-flex justify-content-center">
        <div class="col-lg-2 col-md-2 mt-4 d-flex">
          <div class="col-btn-reset text-center" ngbTooltip="{{ 'common.action.reset' | translate }}">
            <div class="btn-reset text-center">
              <i class="bi bi-arrow-clockwise" type="button" (click)="onReset()">
              </i>
            </div>
          </div>
          <div>
            <button class="btn btn-search mr-2" (click)="onSearch()">
              {{ "common.action.search" | translate }}
            </button>
          </div>
        </div>
      </div>
      <div class="border-bottom-search"></div>
      <div class="d-flex justify-content-between align-items-end text-right mb-3 mt-4">
        <h5 class="title-table text-uppercase mb-4">
          {{ "model.premiumAccountNumber.list" | translate }}
        </h5>
        <div class="d-block text-right mb-3 mt-4">
          <button type="button" class="btn btn-red" (click)="onExport()"
            *hasPrivileges="SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_EXPORT">
            {{ "common.action.export" | translate }}
          </button>
        </div>
      </div>
    </form>
    <div class="table-responsive">
      <table class="table">
        <thead>
          <tr>
            <th [width]="'100px'" class="text-center">
              {{ "model.rate.no" | translate }}
            </th>
            <th class="text-center">
              {{ "common.accountNumber" | translate }}
            </th>
            <th class="text-center">
              {{ "model.structureAccount.listPrice" | translate }}
            </th>
            <th class="text-center">
              {{ "model.structureAccount.discount" | translate }}
            </th>
            <th class="text-center">
              {{ "model.structureAccount.paymentPrice" | translate }}
            </th>
            <th class="text-center" scope="col">
              {{ "model.structureAccount.length" | translate }}
            </th>
            <th class="text-left" scope="col">
              {{ "model.structureAccount.numberStructure" | translate }}
            </th>
            <th [width]="'100px'" class="text-center" scope="col">
              {{ "model.specialAccountNumber.numberType" | translate }}
            </th>
            <th class="text-center" scope="col">
              {{ "common.action.label" | translate }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of data; let i = index">
            <td class="text-center">{{ fillIndexItem(i) }}</td>
            <td class="text-center">
              {{ item?.premiumAccNumber }}
            </td>
            <td class="text-center">
              {{ item?.originalPrice | currencyLak }}
            </td>
            <td class="text-center">
              {{ item?.discount }}
            </td>
            <td class="text-center">
              {{ item?.totalPrice | currencyLak }}
            </td>
            <td class="text-center">{{ item?.numberGroupStr }}</td>
            <td class="text-left">{{ item?.numberStructure }}</td>
            <td class="text-center freezer-td">
              <i *ngIf="item.numberType === 0"></i>
              <i class="item-hidden bi bi-award-fill h4" *ngIf="item.numberType === 2"></i>
              <i class="item-special bi bi-award-fill h4" *ngIf="item.numberType === 1"></i>
            </td>
            <td class="text-center">
              <ng-container *ngIf="item.numberType !== 2">
                <i title="{{
                    'sidebar.customer.registration.register' | translate
                  }}" class="item-blue bi bi-person-fill-up h4" (click)="onNegativeCustomer(item)"
                   *hasPrivileges="SYSTEM_RULES.CUSTOMER_CREATE"></i>
                <i title="{{ 'model.premiumAccountNumber.register' | translate }}"
                   class="ms-5 item-blue bi bi-chevron-double-right h4" (click)="onCreatePremiumAccNumber(item)"
                   *hasPrivileges="SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_CREATE"></i>
              </ng-container>
            </td>
          </tr>
        </tbody>
      </table>
      <div class="row d-block text-center m-0 no-search-result-wrapper" *ngIf="data?.length === 0">
        <img src="/assets/dist/img/icon/empty.svg" height="120" alt="no_search_result" />
        <p class="text-center mb-5">
          {{ "common.no_search_result" | translate }}
        </p>
      </div>
    </div>
    <!-- <div *ngIf="data?.length" class="paginator col-md-12">
      <mat-paginator [length]="formSearch.value.length" [pageSize]="formSearch.value.pageSize"
        [pageIndex]="formSearch.value.pageIndex" [pageSizeOptions]="pageSizeOptions" (page)="onChangePage($event)"
        aria-label="Select page">
      </mat-paginator>
    </div> -->
  </div>
</div>
