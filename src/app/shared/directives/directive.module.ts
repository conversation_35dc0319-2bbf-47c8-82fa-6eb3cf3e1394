import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { AppCleanAccentsDirective } from './app.clean-accents.directive';
import { DateTransformDirective } from './app.mask-date.directive';
import { AppSelectOptionDirective } from './app.select-option.directive';
import { AppTrimDirective } from './app.trim.directive';
import { ExcludeRolesDirective } from './auth/exclude-roles.directive';
import { HasPrivilegesDirective } from './auth/has-privileges.directive';
import { HasRolesDirective } from './auth/has-roles.directive';
import { AppCleanSpecialDirective } from './clean-special-character.directive';
import { ClickOutsideDirective } from './click-outside.directive';
import { CurrencyFormatterDirective } from './currency.directive';
import { DebounceClickDirective } from './debounce-click.directive';
import { DebounceKeyupDirective } from './debounce-keyup.directive';
import { DiscountDirective } from './discount.directive';
import { MoneyFormatDirective } from './money-format.directive';
import { MoneyInputDirective } from './money-input.directive';
import { NumberFormatterDirective } from './number-format.directive';
import { NumberInputDecimalDirective } from './number-input-decimal.directive';
import { NumberInputDirective } from './number-input.directive';
import { MaxMinNumberDirective } from './number-max-min.directive';
import { SeperatorDirective } from './number-seperator.directive';
import { NumbersOnlyDirective } from './numbers-only.directive';

@NgModule({
  imports: [CommonModule],
  declarations: [
    CurrencyFormatterDirective,
    DebounceClickDirective,
    ClickOutsideDirective,
    ExcludeRolesDirective,
    HasRolesDirective,
    HasPrivilegesDirective,
    NumberFormatterDirective,
    DebounceKeyupDirective,
    AppSelectOptionDirective,
    AppTrimDirective,
    AppCleanAccentsDirective,
    NumbersOnlyDirective,
    MaxMinNumberDirective,
    DateTransformDirective,
    SeperatorDirective,
    AppCleanSpecialDirective,
    DiscountDirective,
    NumberInputDirective,
    MoneyFormatDirective,
    MoneyInputDirective,
    NumberInputDecimalDirective,
  ],
  exports: [
    CommonModule,
    CurrencyFormatterDirective,
    DebounceClickDirective,
    ClickOutsideDirective,
    ExcludeRolesDirective,
    HasRolesDirective,
    HasPrivilegesDirective,
    NumberFormatterDirective,
    DebounceKeyupDirective,
    AppSelectOptionDirective,
    AppTrimDirective,
    AppCleanAccentsDirective,
    NumbersOnlyDirective,
    MaxMinNumberDirective,
    DateTransformDirective,
    SeperatorDirective,
    AppCleanSpecialDirective,
    DiscountDirective,
    NumberInputDirective,
    MoneyFormatDirective,
    MoneyInputDirective,
    NumberInputDecimalDirective,
  ],
})
export class DirectiveModule {}
