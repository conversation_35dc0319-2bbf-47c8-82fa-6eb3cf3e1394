import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { INumberGroup } from '@shared/models/number-group.model';
import { INumberStructure } from '@shared/models/premium-account-number.modal';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { Observable } from 'rxjs';
import { AbstractDomainService } from './common/abstract.domain.service';
import { EntityResponseType } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class NumberStructureService extends AbstractDomainService<INumberStructure> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.PREMIUM_ACCOUNT_STRUCTURE;
  numberGroupResource: RESOURCE_TYPE = RESOURCE_CONST.NUMBER_GROUP;

  downloadTemplate() {
    return super.postFile(`/download-template`, {});
  }

  import(file: File, loading = false): Observable<EntityResponseType<INumberStructure[]>> {
    const formData = new FormData();
    formData.append('file', file);
    return super.post(`/import-structure`, formData, {
      loading,
    });
  }

  getStructureCode(request: INumberStructure): Observable<EntityResponseType<INumberStructure[]>> {
    return super.post<EntityResponseType<INumberStructure[]>>(`/structure-code`, request);
  }

  getLength(): Observable<INumberGroup[]> {
    return super.post<EntityResponseType<INumberGroup[]>>(`/number-group`);
  }

  export(request: INumberStructure): Observable<EntityResponseType<INumberStructure>> {
    return super.postFile(RESOURCE_CONST.PREMIUM_ACCOUNT_STRUCTURE.API.EXPORT, request);
  }
}
