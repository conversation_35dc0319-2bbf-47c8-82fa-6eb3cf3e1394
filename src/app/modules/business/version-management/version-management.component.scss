.select-export {
  text-align: left !important;
  height: 41px !important;
}
.copy-text {
  cursor: pointer;
}
.dynamic-link {
  color: blue;
}
.copy-text {
  color: blue;
}
.format-button {
  margin-right: 4px;
  margin-left: 4px;
}
input[type="checkbox"] {
  position: relative;
  pointer-events: none;
}
// .wrapper-datepicker {
//   border: 1px solid #ccc;
//   border-radius: 0.25rem;
// }
// .date-picker-custom {
//   border: 1px solid white;
//   border-radius: 0.25rem;
// }
// .date-picker-custom:focus-within {
//   border-color: var(--mb-color) !important;
//   border-radius: 0.25rem;
// }
