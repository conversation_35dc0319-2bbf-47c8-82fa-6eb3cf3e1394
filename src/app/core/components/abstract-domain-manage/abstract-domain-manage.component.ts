import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ServiceLocator } from '@core/components/service-locator/service-locator.service';
import {
  ENTITY_STATUS,
  ENTITY_STATUS_MAP,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IBaseResponseModel } from '@shared/models/base/base.model';
import { IPagination } from '@shared/models/request/base.request.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { AbstractDomainService } from '@shared/services/common/abstract.domain.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import CommonUtils from '@shared/utils/common-utils';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';

@Component({ template: '' })
export abstract class AbstractDomainManageComponent<T>
  implements OnInit, OnDestroy
{
  abstract resource: RESOURCE_TYPE;
  abstract searchForm: FormGroup;
  abstract searchResults: IBaseResponseModel<T[]>;
  protected paramsAfterBack = {};
  protected modalService: ModalService;
  protected router: Router;
  protected activatedRoute: ActivatedRoute;
  protected _unsubscribeAll$ = new Subject();
  protected searchOnChange = false;
  protected defaultQueryParams = {};

  SYSTEM_RULES = SYSTEM_RULES;
  PAGINATION = PAGINATION;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  ENTITY_STATUS = ENTITY_STATUS;

  // tslint:disable-next-line: ban-types
  constructor(
    private abstractService: AbstractDomainService<T>,
    // tslint:disable-next-line:ban-types
    ignoreAutoSearch?: Boolean
  ) {
    this.modalService = ServiceLocator.injector.get(ModalService);
    this.router = ServiceLocator.injector.get(Router);
    this.activatedRoute = ServiceLocator.injector.get(ActivatedRoute);

    // if (!ignoreAutoSearch) {
    //   const path = this.router.url;
    //   const sessionItem = sessionStorage.getItem(path) || '';
    //   if (sessionItem !== '') {
    //     const _searchForm = JSON.parse(sessionItem) as any;
    //     const params = {} as any;
    //     Object.keys(_searchForm).forEach((key) => {
    //       params[key] = _searchForm[key];
    //     });
    //     this.paramsAfterBack = params;
    //     this.abstractService.search(params).subscribe((res: any) => {
    //       this.searchResults = res.body;
    //     });
    //   } else {
    //     const baseSearch: IPagination = {
    //       pageIndex: PAGINATION.PAGE_DEFAULT,
    //       pageSize: PAGINATION.SIZE_DEFAULT,
    //     };
    //     this.abstractService.search(baseSearch).subscribe((res: any) => {
    //       this.searchResults = res.body;
    //     });
    //   }
    //   sessionStorage.removeItem(path);
    // }
  }

  ngOnInit(): void {
    this.onParseValueSearchForm();
    this.searchFormChange();
    this.onSearch();
  }

  ngOnDestroy(): void {
    this._unsubscribeAll$.next();
    this._unsubscribeAll$.complete();
  }

  /**
   * onSearch
   *
   * <AUTHOR>
   * @date 03/08/2022
   * @abstract
   * @memberof AbstractDomainManageComponent
   */
  abstract onSearch(): void;

  public onClickToDetail(path: string, dataObject?: any): void {
    sessionStorage.setItem(
      this.router.url,
      JSON.stringify(this.searchForm.value)
    );

    this.router.navigate([path], dataObject);
  }

  /**
   * fillIndexItem
   *
   * <AUTHOR>
   * @date 03/08/2022
   * @param {number} index
   * @return {*} {number}
   * @memberof AbstractDomainManageComponent
   */
  public fillIndexItem(index: number): number {
    return CommonUtils.getIndex(
      index,
      this.searchForm.value.pageIndex,
      this.searchForm.value.pageSize
    );
  }

  /**
   * onChangePage
   *
   * <AUTHOR>
   * @date 03/08/2022
   * @param {IPagination} page
   * @memberof AbstractDomainManageComponent
   */
  public onChangePage(page: IPagination): void {
    this.searchForm.controls.pageIndex.setValue(page.pageIndex || 0);
    this.searchForm.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  /**
   * onSearchSubmit
   *
   * <AUTHOR>
   * @date 03/08/2022
   * @memberof AbstractDomainManageComponent
   */
  public onSearchSubmit(event?: any): void {
    if (this.searchForm.valid) {
      if (event instanceof KeyboardEvent) {
        event = event as any;
        this.searchForm.controls.keyword.setValue(event.target.value);
      }
      this.searchForm.controls.pageIndex.setValue(
        PAGINATION.PAGE_NUMBER_DEFAULT
      );
      this.searchForm.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
      this.onSearch();
    }
  }

  /**
   * onParseValueSearchForm
   *
   */
  // get query param from url to search
  protected onParseValueSearchForm(): void {
    const queryParams = this.activatedRoute.snapshot.queryParamMap;
    queryParams.keys.forEach((key) => {
      const control = this.searchForm.get(key);
      if (Array.isArray(control?.value)) {
        const values = queryParams.getAll(key);
        control?.setValue(values);
      } else {
        const value = queryParams.get(key) || '';
        if (
          value &&
          typeof queryParams.get(key) === 'string' &&
          VALIDATORS.PATTERN.LOCALE_DATE_TIME.test(value)
        ) {
          control?.setValue(CommonUtils.reverseDate(value));
        } else if (
          value &&
          typeof queryParams.get(key) === 'string' &&
          VALIDATORS.PATTERN.NUMBER.test(value) &&
          !VALIDATORS.PATTERN.PHONE_NUMBER_START_ZERO.test(value)
        ) {
          control?.setValue(+value);
        } else {
          control?.setValue(value);
        }
      }
    });
  }

  protected searchFormChange(): void {
    this.searchForm?.valueChanges
      ?.pipe(
        debounceTime(500),
        distinctUntilChanged(),
        takeUntil(this._unsubscribeAll$)
      )
      ?.subscribe((value) => {
        if (this.searchOnChange) {
          this.searchForm
            .get('pageIndex')
            ?.setValue(PAGINATION.PAGE_NUMBER_DEFAULT, { emitEvent: false });
          this.onSearch();
        }
        this.changeQueryParams({
          ...value,
          pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
        });
      });
  }

  protected changeQueryParams(params: any): void {
    const queryParams = CommonUtils.resolveRequestBody(
      CommonUtils.optimalObjectParams({
        ...params,
        ...this.defaultQueryParams,
      })
    );
    delete queryParams.total;
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams,
      // queryParamsHandling: 'preserve'
    });
  }
}
