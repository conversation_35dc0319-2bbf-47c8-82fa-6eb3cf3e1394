import { Component, OnInit } from '@angular/core';
import { Form<PERSON><PERSON>er, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import {
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  FILE_EXTENSION,
  MODAL_ACTION,
  MOMENT_CONST,
  PAGINATION
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { LANGUAGES } from '@shared/constants/language.constants';

import { Router } from '@angular/router';
import { AbstractDomainManageComponent } from '@core/components/abstract-domain-manage/abstract-domain-manage.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { SELECTED_CONST } from '@shared/constants/user.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IBaseResponseModel } from '@shared/models/base/base.model';
import { INumberGroup } from '@shared/models/number-group.model';
import { TransactionFeeTypeSearch } from '@shared/models/request/transaction-fee-type.search';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { NumberGroupService } from '@shared/services/number-group.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import { NumberGroupCreateUpdateComponent } from './number-group-crud/number-group-create-update.component';
import { NumberGroupDeleteComponent } from './number-group-delete/number-group-delete.component';

@Component({
  selector: 'app-number-group-management',
  templateUrl: './number-group-management.component.html',
  styleUrls: ['./number-group-management.component.scss'],
})
export class NumberGroupManagementComponent
  extends AbstractDomainManageComponent<any>
  implements OnInit
{
  resource: RESOURCE_TYPE = RESOURCE_CONST.NUMBER_GROUP;
  searchForm: FormGroup = new FormGroup({});
  searchResults: IBaseResponseModel<INumberGroup[]> = {};
  transactionFeeSearch: TransactionFeeTypeSearch = {};
  data: INumberGroup[] = [];

  pageSizeOptions = PAGINATION.OPTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  LANGUAGES = LANGUAGES;
  MOMENT_CONST = MOMENT_CONST;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  SELECTED_CONST = SELECTED_CONST;
  VALIDATORS = VALIDATORS;

  constructor(
    private fb: FormBuilder,
    protected router: Router,
    private numberGroupService: NumberGroupService,
    private translateService: TranslateService,
    private toastService: ToastrCustomService,
    private modal: NgbModal,
    private downloadService: DownloadService,
  ) {
    super(numberGroupService);
    this.searchForm = this.fb.group({
      keyword: '',
      price: '',
      minPrice: '',
      pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
      pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
      length: 0,
      hasPageable: true,
    });
  }

  ngOnInit(): void {
    super.ngOnInit();
  }

  onParseValueSearchForm(): void {
    super.onParseValueSearchForm();
  }

  onSearch() {
    if (this.searchForm.invalid) {
      CommonUtils.markFormGroupTouched(this.searchForm);
      return;
    }
    const data = this.searchForm.value;
    data.minPrice = data.minPrice.toString().split('.').join('');
    this.numberGroupService
      .search(this.searchForm.value)
      .subscribe((res: any) => {
        this.data = res.body.content;
        this.searchForm.controls.length.setValue(res.body.totalElements, {
          emitEvent: false,
        });
      });
  }

  onReset() {
    this.searchForm.controls.keyword.reset();
    this.searchForm.controls.minPrice.reset();
  }

  /**
   * delete
   *
   * @param user IUser
   */
  delete(numberGroup?: INumberGroup): void {
    // open modal
    if (numberGroup?.structures) {
      const modalRef = this.modal.open(NumberGroupDeleteComponent, {
        backdrop: 'static',
        centered: true,
        keyboard: false,
      });
      modalRef.componentInstance.action = ROUTER_ACTIONS.create;
      modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;
      modalRef.componentInstance.numberGroup = numberGroup;

      modalRef.result.then((result) => {
        if (result === MODAL_ACTION.CONFIRM.code) {
          sessionStorage.setItem(
            STORAGE_APP.NUMBER_GROUP,
            JSON.stringify(numberGroup)
          );

          this.router.navigate([
            ROUTER_UTILS.premiumAccountNumber.root,
            ROUTER_UTILS.premiumAccountNumber.numberStructure.root,
          ]);
        }
      });
    } else {
      const modalData = {
        title: 'numberGroup.delete',
        content: 'numberGroup.deleteContent',
        interpolateParams: { name: `<b>${numberGroup?.name || ''}</b>` },
        confirmButtonText: 'common.action.delete', // Đổi text nút xác nhận thành "Xóa"
        cancelButtonText: 'common.action.cancel', // Đổi text nút hủy nếu cần
        buttonOrder: 'cancel-first', // Hoặc 'confirm-first' tùy theo thứ tự bạn muốn
        confirmBtnClass: 'btn-danger',
      };
      this.modalService.confirm(modalData).then((result) => {
        if (result === MODAL_ACTION.CONFIRM.code) {
          const params = { id: numberGroup?.numberGroupId };
          this.numberGroupService.delete(params).subscribe((res: any) => {
            this.toastService.success('common.action.deleteSuccess');
            if (this.data?.length === 1) {
              this.searchForm.controls.pageIndex.setValue(
                this.searchForm.controls.pageIndex.value === 0 ? '' : Number(this.searchForm.controls.pageIndex.value) - 1
              );
            }
            this.onSearch();
          });
        }
      });
    }
  }

  create(id?: number): void {
    const modalRef = this.modal.open(NumberGroupCreateUpdateComponent, {
      backdrop: 'static',
      size: 'xl',
      centered: true,
      keyboard: false,
    });
    modalRef.componentInstance.action = ROUTER_ACTIONS.create;
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;

    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }

  detail(id?: number): void {
    const modalRef = this.modal.open(NumberGroupCreateUpdateComponent, {
      backdrop: 'static',
      size: 'xl',
      centered: true,
      keyboard: false,
    });
    modalRef.componentInstance.numberGroupId = id;
    modalRef.componentInstance.action = ROUTER_ACTIONS.detail;
  }

  update(id?: number): void {
    const modalRef = this.modal.open(NumberGroupCreateUpdateComponent, {
      backdrop: 'static',
      size: 'xl',
      centered: true,
      keyboard: false,
    });
    modalRef.componentInstance.numberGroupId = id;
    modalRef.componentInstance.action = ROUTER_ACTIONS.update;
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;

    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }

  onExport(): void {
    const bodySearch = this.searchForm.getRawValue();
    if (this.searchForm.invalid) {
      CommonUtils.markFormGroupTouched(this.searchForm);
    } else {
      // get file name
      const fileName = this.translateService.instant(
        'template.numberGroupReport'
      );
      const obFile = this.numberGroupService.export({
        keyword: bodySearch.keyword,
        minPrice: bodySearch.minPrice.toString().split('.').join(''),
        timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
      });

      this.downloadService.downloadFileWithObservableAndName(
        obFile,
        fileName,
        FILE_EXTENSION.XLSX
      );
    }
  }
}
