/* fallback */
@font-face {
  font-family: "Material Icons";
  font-style: normal;
  font-weight: 400;
  src: url(../font/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format("woff2");
}

@font-face {
  font-family: "Font Laos";
  font-style: normal;
  font-weight: 400;
  src: url(../font/NotoSansLao-VariableFont_wdth,wght.ttf) format("woff2");
}

@font-face {
  font-family: "Font MB";
  font-style: normal;
  font-weight: 400;
  src: url(../font/IntelligentDesign-AvertaStdCY-Regular_3.otf) format("woff2");
}

:root {
  --mb-color: #141ed2;
  --mb-bg-color: #141ed2;
  --mg-bg-color-deny: #eb2d4b;
  --mb-disabled-color: #F8F8F8;
}

.mb-color {
  color: var(--mb-color);
}

.mb-color-red {
  color: var(--mg-bg-color-deny);
}

.mb-bg-color {
  background-color: var(--mb-bg-color);
  color: white;
}

.mb-btn-color {
  background-color: var(--mb-bg-color);
  border-radius: 8px;
  color: white;
  min-width: 100px;
  font-size: 14px;
}

.mb-btn-color:hover {
  background-color: var(--mb-bg-color);
  border-radius: 8px;
  color: white;
}

.mb-btn-outline-color {
  border: 1px solid var(--mb-bg-color);
  border-radius: 8px;
  font-size: 14px;
  min-width: 100px;
  color: var(--mb-bg-color);
}

.mb-btn-outline-color:hover {
  color: var(--mb-bg-color);
}

.mb-bg-color:hover {
  color: white;
}

.material-icons {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.main-sidebar .brand-text,
.main-sidebar .logo-xl,
.main-sidebar .logo-xs,
.sidebar .nav-link p,
.sidebar .user-panel .info {
  transition: margin-left 0.3s linear, opacity 0.3s ease, visibility 0.3s ease;
}

.card {
  margin-bottom: 12px;
  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03) !important;
}

.card-header {
  border-bottom: none !important;
}

.content-wrapper {
  background: #f0f3fc !important;
}

.card-title {
  font-size: 15px !important;
  margin: 0 0 7px;
  font-weight: 600;
}

.main-header {
  border-bottom: none !important;
  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03) !important;
}

.nav-sidebar .nav-link>p>.right {
  right: 12px;
  top: 1.1rem;
}

.modal-content {
  border-radius: 8px;
  box-shadow: unset;
  border-width: 0;
}

.btn-red {
  background: var(--mb-color);
  border-radius: 8px;
  width: 200px;
  color: #fff !important;
  height: 40px;
  font-size: 16px;
  font-weight: 400;
  margin-left: 10px;
}

.btn-deny {
  background: var(--mg-bg-color-deny);
  border-radius: 8px;
  width: 200px;
  color: #fff !important;
  height: 40px;
  font-size: 16px;
  font-weight: 400;
  margin-left: 10px;
}

.btn-search {
  background: var(--mb-color);
  border-radius: 8px;
  width: 90px !important;
  color: #fff !important;
  height: 40px;
  font-size: 14px;
  font-weight: 400;
}

.btn-search-child {
  margin-top: 26px;
}

.col-btn-reset {
  margin-right: 15px;
}

.btn-reset {
  display: flex;
  align-items: center;
  padding: 0 10px;
  position: relative;
  top: 3px;
}

.btn-reset>i {
  font-size: 25px;
}

.btn-reset:hover>i {
  color: var(--mb-color);
}

.btn-reset:hover {
  background-color: #f1f1f1 !important;
  border-radius: 50%;
}

.btn-white {
  background: #fff;
  border-radius: 8px;
  width: 200px;
  height: 40px;
  color: var(--mb-color) !important;
  font-size: 16px;
  font-weight: 400;
  margin-left: 10px;
  border: 1px solid var(--mb-color);
}

#btn-red-sm {
  background: #eb2d4b;
  border-radius: 8px;
  width: 100px;
  color: #fff !important;
  height: 40px;
  font-size: 14px !important;
  font-weight: 400;
}

#btn-white-sm {
  background: #fff;
  border-radius: 8px;
  width: 100px;
  height: 40px;
  color: #eb2d4b !important;
  font-size: 14px !important;
  font-weight: 400;
  border: 1px solid #eb2d4b;
}

.form-control {
  font-size: 14px;
  height: 2.2rem;
  /* border-radius: 0px;
    border: 1.5px solid #CECECE;
    padding: 3px 15px!important; */
}

.container-fluid label {
  font-size: 12px;
}

.no-search-result-wrapper {
  position: sticky;
  left: 0;
}

.table th {
  background: rgb(243, 245, 255);
  font-size: 14px;
  padding: 9px;
  font-weight: 400;
  color: var(--mb-color);
}

.table thead th {
  border-bottom: 0px solid #dee2e6;
  border-top: 0px solid #dee2e6;
}

.table td {
  font-size: 14px;
  padding: 10px;
  border-bottom: 1px solid #e2e8f0;
}

.box-content {
  padding: 16px;
  /* padding: 16px 15px 16px 28px; */
}

.box-content .container-fluid {
  padding: 15px 15px 25px 15px;
  background: #fff;
  border-radius: 10px;
}

.title-create {
  font-weight: 600;
  font-size: 20px;
  color: #282828;
  color: white;
  line-height: 23px;
}

.border-create {
  border: 1px solid #dcdcdc;
  border-radius: 10px;
  padding: 24px;
  position: relative;
}

.border-create h3 {
  position: absolute;
  top: -9px;
  background: white;
  color: #282828;
  font-size: 14px;
  left: 20px;
}

.border-create h4 {
  font-weight: 500;
  font-size: 14px;
  line-height: 32px;
  color: #282828;
}

.border-ckeditor {
  border: 1px solid #dcdcdc;
  border-radius: 10px;
  padding: 24px;
  position: relative;
}

.border-ckeditor h4 {
  font-weight: 500;
  font-size: 14px;
  line-height: 32px;
  color: #282828;
}

.border-ckeditor-h3 {
  position: absolute;
  top: -9px;
  background: white;
  color: #282828;
  font-size: 14px;
  left: 20px;
}

.color-primary {
  color: #eb2d4b;
}

.color-second {
  color: #282828;
}

[class*="sidebar-dark-"] {
  background: var(--mb-color) !important;
}

[class*="sidebar-dark"] .brand-link {
  background-color: #50a5f1 !important;
  border-bottom: none !important;
}

[class*="sidebar-dark"] .nav-sidebar>p {
  color: #000000 !important;
}

[class*="sidebar-dark-"] .sidebar a {
  color: #000000 !important;
}

[class*="sidebar-dark-"] .nav-sidebar>.nav-item>.nav-link.active {
  color: #fff;
  box-shadow: 0 1px 3px rgb(0 0 0 / 6%), 0 1px 1px rgb(0 0 0 / 14%) !important;
}

.sidebar-dark-primary .nav-sidebar>.nav-item>.nav-link.active,
.sidebar-light-primary .nav-sidebar>.nav-item>.nav-link.active {
  background-color: #f2f7ff !important;
  /* background: linear-gradient( 133deg , #253494,#2C7FB8,#41B6C4) !important; */
  color: #178cc6 !important;
}

.elevation-4 {
  box-shadow: 0 14px 28px rgba(0, 0, 0, 0.1) !important;
}

.l .nav-sidebar>.nav-item .nav-icon {
  color: #7e84a3 !important;
}

.nav-sidebar>.nav-item .nav-icon .active {
  color: whitesmoke !important;
}

.nav-link:focus,
.nav-link:hover {
  /*color: whitesmoke !important;*/
}

.nav-sidebar .nav-header {
  font-size: 0.7rem !important;
  font-weight: 500 !important;
  /* font-family: "Roboto" !important; */
}

.nav-pills .nav-link {
  /* font-family: "Roboto" !important; */
  font-size: 0.85rem !important;
  margin-left: 0.65em !important;
}

.nav-sidebar>.nav-item .nav-icon.fa,
.nav-sidebar>.nav-item .nav-icon.fab,
.nav-sidebar>.nav-item .nav-icon.far,
.nav-sidebar>.nav-item .nav-icon.fas,
.nav-sidebar>.nav-item .nav-icon.glyphicon,
.nav-sidebar>.nav-item .nav-icon.ion {
  font-size: 1rem !important;
}

.content-header h1 {
  font-size: 16px !important;
  font-family: SFbold !important;
  text-transform: uppercase;
  color: #495057;
}

/* .table td,
.table th {
    padding: 0.35rem !important;
    line-height: 2rem;
    vertical-align: middle;
    font-size: 0.9rem !important;
    color: #7e7e7e;
    border-top: 0px solid #dee2e6 !important;
}
.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgb(240 243 252) !important;
} */

.media {
  display: flex;
  align-items: flex-start;
}

.media-body {
  flex: 1;
}

.mini-stats-wid .mini-stat-icon {
  overflow: hidden;
  position: relative;
}

.avatar-title {
  align-items: center;
  /* background-color: #556ee6; */
  color: #fff;
  display: flex;
  font-weight: 500;
  height: 40px;
  justify-content: center;
  width: 40px;
}

.bx {
  align-items: center;
  /* background-color: #556ee6; */
  color: #fff;
  display: flex;
  font-weight: 500;
  height: 50px;
  width: 50px;
  justify-content: center;
  width: 50px;
}

.font-size-24 {
  font-size: 24px !important;
}

.badge {
  text-transform: uppercase !important;
  font-size: 10px !important;
  font-weight: 400 !important;
  line-height: 1.5 !important;
}

/**avatar**/
.person-circle {
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.87);
  font-size: 20px;
  font-weight: bold;
  height: 30px;
  line-height: 30px;
  text-align: center;
  width: 30px;
  position: relative;
}

.preson-circle-avatar {
  height: 28px;
  width: 28px;
  line-height: 30px;
}

.person-circle.chr-A {
  background-color: #ef5350;
}

.person-circle.chr-B {
  background-color: #ec407a;
}

.person-circle.chr-C {
  background-color: #7e57c2;
}

.person-circle.chr-E {
  background-color: #5c6bc0;
}

.person-circle.chr-F {
  background-color: #0288d1;
}

.person-circle.chr-G {
  background-color: #0288d1;
}

.person-circle.chr-H {
  background-color: #0097a7;
}

.person-circle.chr-D {
  background-color: #7e57c2;
}

.person-circle.chr-Đ {
  background-color: #7e57c2;
}

.person-circle.chr-I {
  background-color: #43a047;
}

.person-circle.chr-J {
  background-color: #27792b;
}

.person-circle.chr-K {
  background-color: #7cb342;
}

.person-circle.chr-L {
  background-color: #827717;
}

.person-circle.chr-M {
  background-color: #fdd835;
}

.person-circle.chr-N {
  background-color: #ffb300;
}

.person-circle.chr-O {
  background-color: #ff5722;
}

.person-circle.chr-P {
  background-color: #7e57c2;
}

.person-circle.chr-Q {
  background-color: #2a2239;
}

.person-circle.chr-R {
  background-color: #1e585f;
}

.person-circle.chr-S {
  background-color: #511dac;
}

.person-circle.chr-T {
  background-color: #7e57c2;
}

.person-circle.chr-U {
  background-color: #3f3b47;
}

.person-circle.chr-V {
  background-color: #651d72;
}

.person-circle.chr-w {
  background-color: #7132e0;
}

.person-circle.chr-X {
  background-color: #880967;
}

.person-circle.chr-Y {
  background-color: #7126f5;
}

.person-circle.chr-Z {
  background-color: #147959;
}

.person-circle.chr-Ă {
  background-color: #201398;
}

.person-circle.chr-Â {
  background-color: #69b515;
}

.person-circle.chr-Ê {
  background-color: #560b32;
}

.person-circle.chr-Ô {
  background-color: #4f9680;
}

.person-circle.chr-Ơ {
  background-color: #07a7ea;
}

.person-circle.chr-Ư {
  background-color: #124c3a;
}

.person-circle.chr-0 {
  background-color: #202d53;
}

.person-circle.chr-1 {
  background-color: #2b413a;
}

.person-circle.chr-2 {
  background-color: #033022;
}

.person-circle.chr-3 {
  background-color: #7c5e5e;
}

.person-circle.chr-4 {
  background-color: #4a3e21;
}

.person-circle.chr-5 {
  background-color: #5d0d2e;
}

.person-circle.chr-6 {
  background-color: #49085b;
}

.person-circle.chr-7 {
  background-color: #334c44;
}

.person-circle.chr-8 {
  background-color: #147959;
}

.person-circle.chr-9 {
  background-color: #146a79;
}

/*first character name of list in page config*/
.person-circle-config {
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.87);
  font-size: 20px;
  font-weight: bold;
  height: 30px;
  line-height: 30px;
  text-align: center;
  width: 30px;
  position: relative;
  padding: 4px 4px 4px 8px;
}

.person-circle-config.chr-A {
  background-color: #ef5350;
}

.person-circle-config.chr-B {
  background-color: #ec407a;
}

.person-circle-config.chr-C {
  background-color: #7e57c2;
}

.person-circle-config.chr-E {
  background-color: #5c6bc0;
}

.person-circle-config.chr-F {
  background-color: #0288d1;
}

.person-circle-config.chr-G {
  background-color: #0288d1;
}

.person-circle-config.chr-H {
  background-color: #0097a7;
}

.person-circle-config.chr-D {
  background-color: #7e57c2;
}

.person-circle-config.chr-Đ {
  background-color: #7e57c2;
}

.person-circle-config.chr-I {
  background-color: #43a047;
}

.person-circle-config.chr-J {
  background-color: #27792b;
}

.person-circle-config.chr-K {
  background-color: #7cb342;
}

.person-circle-config.chr-L {
  background-color: #827717;
}

.person-circle-config.chr-M {
  background-color: #fdd835;
}

.person-circle-config.chr-N {
  background-color: #ffb300;
}

.person-circle-config.chr-O {
  background-color: #ff5722;
}

.person-circle-config.chr-P {
  background-color: #7e57c2;
}

.person-circle-config.chr-Q {
  background-color: #2a2239;
}

.person-circle-config.chr-R {
  background-color: #1e585f;
}

.person-circle-config.chr-S {
  background-color: #511dac;
}

.person-circle-config.chr-T {
  background-color: #7e57c2;
}

.person-circle-config.chr-U {
  background-color: #3f3b47;
}

.person-circle-config.chr-V {
  background-color: #651d72;
}

.person-circle-config.chr-w {
  background-color: #7132e0;
}

.person-circle-config.chr-X {
  background-color: #880967;
}

.person-circle-config.chr-Y {
  background-color: #7126f5;
}

.person-circle-config.chr-Z {
  background-color: #147959;
}

.person-circle-config.chr-Ă {
  background-color: #201398;
}

.person-circle-config.chr-Â {
  background-color: #69b515;
}

.person-circle-config.chr-Ê {
  background-color: #560b32;
}

.person-circle-config.chr-Ô {
  background-color: #4f9680;
}

.person-circle-config.chr-Ơ {
  background-color: #07a7ea;
}

.person-circle-config.chr-Ư {
  background-color: #124c3a;
}

.person-circle-config.chr-0 {
  background-color: #202d53;
}

.person-circle-config.chr-1 {
  background-color: #2b413a;
}

.person-circle-config.chr-2 {
  background-color: #033022;
}

.person-circle-config.chr-3 {
  background-color: #7c5e5e;
}

.person-circle-config.chr-4 {
  background-color: #4a3e21;
}

.person-circle-config.chr-5 {
  background-color: #5d0d2e;
}

.person-circle-config.chr-6 {
  background-color: #49085b;
}

.person-circle-config.chr-7 {
  background-color: #334c44;
}

.person-circle-config.chr-8 {
  background-color: #147959;
}

.person-circle-config.chr-9 {
  background-color: #146a79;
}

.select2-container--default .select2-selection--multiple {
  background-color: white;
  border: 1px solid #ced4da !important;
  border-radius: 0px !important;
  cursor: text;
  height: calc(1.8125rem + 2px);
  line-height: 1.5;
  min-height: calc(1.8125rem + 2px);
}

.select2-container--default .select2-selection--single {
  background-color: #fff;
  border: 1px solid #ced4da !important;
  border-radius: 0px !important;
  cursor: text;
  height: calc(1.8125rem + 2px);
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #444;
  line-height: 20px !important;
}

.select2-container .select2-selection--single {
  box-sizing: border-box;
  cursor: pointer;
  display: block;
  height: 31px !important;
  user-select: none;
  -webkit-user-select: none;
}

label:not(.form-check-label):not(.custom-file-label) {
  font-weight: 500 !important;
  /* font-size: 0.875rem; */
}

label {
  /* font-size: 0.9rem !important; */
}

.nav-sidebar .nav-item>.nav-link {
  margin-bottom: 0.5rem !important;
}

/* .list-group-item {
  font-family: "Roboto" !important;
} */
b,
strong {
  font-weight: bolder;
  font-size: 0.85rem;
}

.card-header>.card-tools {
  float: left !important;
  margin-left: -12.625rem !important;
}

/* .table-style .today {background: #2A3F54; color: #ffffff;}
.table-style th:nth-of-type(7),td:nth-of-type(7) {color: blue;}
.table-style th:nth-of-type(1),td:nth-of-type(1) {color: red;}
.table-style tr:first-child th{background-color:#F6F6F6; text-align:center; font-size: 15px;} */
.panel-group .panel {
  border-radius: 0;
  box-shadow: none;
  border-color: #eeeeee;
}

.panel-default>.panel-heading {
  padding: 0;
  border-radius: 0;
  color: #212121;
  background-color: #fafafa;
  border-color: #eeeeee;
}

.panel-title {
  font-size: 14px;
}

.panel-title>a {
  display: block;
  padding: 15px;
  text-decoration: none;
}

/* .more-less {
  float: right;
  color: #212121;
} */

.panel-default>.panel-heading+.panel-collapse>.panel-body {
  border-top-color: #eeeeee;
}

.select2-container .select2-selection--single .select2-selection__rendered {
  padding-left: 0px !important;
}

.btn-group-sm>.btn,
.btn-sm {
  font-size: 0.75rem !important;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  font-size: 0.8rem !important;
}

.select2-results__options {
  font-size: 0.8rem !important;
}

.form-control {
  /* font-size: 0.8rem; */
}

.select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice,
.select2-container--default .select2-blue .select2-selection--multiple .select2-selection__choice {
  font-size: 0.8rem !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered li:first-child.select2-search.select2-search--inline .select2-search__field {
  font-size: 0.8rem;
}

@media screen and (min-height: 768px) {
  .height_auto {
    height: 50vh;
  }
}

@media screen and (min-height: 900px) {
  .height_auto {
    height: 55vh;
  }
}

@media screen and (min-height: 1080px) {
  .height_auto {
    height: 63vh;
  }
}

.max-table {
  max-height: 58vh;
}

.panel-title>a {
  padding-left: 0px !important;
}

.clearfix::after,
.calendar ol::after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

/* ================
Calendar Styling */
.calendar {
  border-radius: 10px;
}

.month {
  font-size: 2rem;
}

@media (min-width: 992px) {
  .month {
    font-size: 3.5rem;
  }
}

.calendar ol li {
  float: left;
  width: 14.28571%;
}

.calendar .day-names {
  border-bottom: 1px solid #eee;
}

.calendar .day-names li {
  /* text-transform: uppercase; */
  margin-bottom: 0.5rem;
}

.calendar .days li {
  border-bottom: 1px solid #eee;
  min-height: 5rem;
}

.calendar .days li .date {
  margin: 0.5rem 0;
}

.calendar .days li .event {
  font-size: 0.65rem;
  padding: 0.3rem;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-radius: 4rem;
  margin-bottom: 1px;
}

.calendar .days li .event.span-2 {
  width: 200%;
}

.calendar .days li .event.begin {
  border-radius: 1rem 0 0 1rem;
}

.calendar .days li .event.end {
  border-radius: 0 1rem 1rem 0;
}

.calendar .days li .event.clear {
  background: none;
}

.calendar .days li:nth-child(n + 29) {
  border-bottom: none;
}

.calendar .days li.outside .date {
  color: #ddd;
}

.calendar_disable {
  background-color: #dddddd;
}

.dropzone {
  border: 2px dotted rgba(0, 0, 0, 0.3) !important;
}

.checked {
  color: orange;
}

.custom-table {
  border-collapse: collapse;
  width: 100%;
  border: solid 1px #c0c0c0;
  font-family: open sans;
  font-size: 11px;
}

.custom-table th,
.custom-table td {
  text-align: left;
  padding: 8px;
  border: solid 1px #c0c0c0;
}

.custom-table th {
  color: #000080;
}

.custom-table tr:nth-child(odd) {
  background-color: #f7f7ff;
}

.custom-table>thead>tr {
  background-color: #dde8f7 !important;
}

.tbtn {
  border: 0;
  outline: 0;
  background-color: transparent;
  font-size: 13px;
  cursor: pointer;
}

.toggler {
  display: none;
}

.toggler1 {
  display: table-row;
}

.custom-table a {
  color: #0033cc;
}

.custom-table a:hover {
  color: #f00;
}

.page-header {
  background-color: #eee;
}

.panel-group .panel {
  border-radius: 0;
  box-shadow: none;
  border-color: #eeeeee;
}

.panel-default>.panel-heading {
  padding: 0;
  border-radius: 0;
  color: #212121;
  background-color: #fafafa;
  border-color: #eeeeee;
}

.panel-title {
  font-size: 14px;
}

.panel-title>a {
  display: block;
  padding: 15px;
  text-decoration: none;
}

/* .more-less {
  float: right;
  color: #212121;
} */

.panel-default>.panel-heading+.panel-collapse>.panel-body {
  border-top-color: #eeeeee;
}

.img_dep {
  width: 200px;
  height: 200px;
  /* border-radius: 50%; */
  border-style: solid;
  border-color: #ffffff;
  box-shadow: 0 0 8px 3px #b8b8b8;
  position: relative;
}

.form-inline label {
  font-size: 0.8rem !important;
}

.img_dep img {
  height: 100%;
  width: 100%;
  /* border-radius: 50%; */
}

.img_dep i {
  position: absolute;
  top: 20px;
  right: -7px;
  /* border: 1px solid; */
  border-radius: 50%;
  /* padding: 11px; */
  height: 50px;
  width: 50px;
  display: flex !important;
  align-items: center;
  justify-content: center;
  background-color: white;
  color: cornflowerblue;
  box-shadow: 0 0 8px 3px #b8b8b8;
}

.btn-hidden {
  display: none;
  font-size: 12px;
  padding-right: 10px;
  text-align: right;
}

.divbutton:hover .btn-hidden {
  display: block;
  cursor: pointer;
}

.i-12 {
  position: relative;
}

.i-12:hover {
  background-color: #ebeff3;
}

.i-13 {
  position: absolute;
  top: 10px;
  right: 0px;
}

.i-14 {
  position: absolute;
  top: 40px;
  right: 0px;
}

.i-15 {
  position: absolute;
  top: 10px;
  right: 0px;
}

.select2-container--default .select2-selection--single {
  padding: 0.46875rem 0.45rem !important;
}

/*=================left navibar begin=========================================*/

.gw-container.gw-main-container .gw-sidebar.gw-sidebar-fixed,
.gw-container.gw-main-container .gw-sidebar.gw-sidebar-fixed:before {
  left: auto;
}

.gw-sidebar {
  width: 100%;
  /* position: fixed; */
  border: 1px solid #e5e5e5;
  border-width: 0 1px 0 0;
  background-color: #f2f2f2;
  bottom: 0;
  top: 0;
  left: 0;
}

.gw-sidebar .nano-pane {
  background: rgba(255, 255, 255, 0);
  font-size: 15px;
}

.gw-sidebar .gw-nav-list {
  border-right: 1px solid #ccc;
}

.gw-sidebar .gw-nav-list li a {
  padding-left: 20px;
}

.gw-nav-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.gw-nav-list>li:first-child {
  border-top: 0;
}

.gw-nav-list>li {
  display: block;
  padding: 0;
  margin: 0;
  border: 0;
  border-top: 1px solid #fcfcfc;
  border-bottom: 1px solid #e5e5e5;
  position: relative;
  text-decoration: none;
}

@-webkit-keyframes arrow-slide {
  0% {}

  100% {
    -webkit-transform: rotate(225deg);
    z-index: 3;
  }
}

@-webkit-keyframes arrow-slide1 {
  0% {}

  100% {
    -webkit-transform: rotate(225deg);
    z-index: 3;
  }
}

.gw-nav-list>li.arrow-down:after {
  content: "";
  width: 7px;
  height: 7px;
  position: absolute;
  left: 5px;
  top: 15px;
  border-top: 1px solid #bababa;
  border-left: 1px solid #bababa;
  -webkit-transform: rotate(45deg);
  -webkit-animation: arrow-slide 0.5s 0s ease both;
}

.gw-nav-list>li.init-arrow-down:after {
  content: "";
  width: 7px;
  height: 7px;
  position: absolute;
  left: 5px;
  top: 15px;
  border-right: 1px solid #bababa;
  border-bottom: 1px solid #bababa;
  -webkit-transform: rotate(45deg);
}

.gw-nav-list>li.arrow-up:after {
  content: "";
  width: 7px;
  height: 7px;
  position: absolute;
  left: 5px;
  top: 15px;
  border-right: 1px solid #bababa;
  border-bottom: 1px solid #bababa;
  -webkit-transform: rotate(45deg);
  -webkit-animation: arrow-slide1 0.5s 0s ease both;
}

.gw-nav-list>li.init-arrow-up:after {
  content: "";
  width: 7px;
  height: 7px;
  position: absolute;
  right: 10px;
  top: 15px;
  border-top: 1px solid #bababa;
  border-left: 1px solid #bababa;
  -webkit-transform: rotate(45deg);
}

.gw-nav-list>li.active {
  background-color: #fff;
}

.gw-nav-list>li>a {
  display: block;
  height: 38px;
  line-height: 36px;
  padding: 0 16px 0 7px;
  background-color: #f9f9f9;
  color: #585858;
  text-shadow: none !important;
  font-size: 13px;
  text-decoration: none;
}

.gw-open>a {
  outline: 0;
}

.gw-nav-list>li.gw-open {
  border-bottom-color: #e5e5e5;
}

.gw-nav-list>li.gw-open>a {
  background-color: #fafafa;
  color: #1963aa;
}

.gw-nav-list .gw-open>a,
.gw-nav-list .gw-open>a:hover,
.gw-nav-list .gw-open>a:focus {
  background-color: #fafafa;
}

.gw-nav .gw-open>a,
.gw-nav .gw-open>a:hover,
.gw-nav .gw-open>a:focus {
  background-color: #eee;
  border-color: #428bca;
}

.gw-nav-list>li.active>a,
.gw-nav-list>li.active>a:hover,
.gw-nav-list>li.active>a:focus,
.gw-nav-list>li.active>a:active {
  background-color: #fff;
  color: #dd4814;
  font-weight: bold;
  font-size: 13px;
}

.gw-nav-list>li>a,
.gw-nav-list .gw-nav-header {
  margin: 0;
}

.gw-nav-list>li.active>a>[class*="icon-"] {
  font-weight: normal;
}

.gw-nav-list>li.active>a:hover:before {
  display: none;
}

.gw-nav-list>li.active:before {
  display: inline-block;
  content: "";
  position: absolute;
  right: -2px;
  top: -1px;
  bottom: 0;
  z-index: 1;
  border: 2px solid #dd4814;
  border-width: 0 2px 0 0;
}

.gw-nav-list li.gw-open>a:after {
  display: none;
}

.gw-nav-list>li a>.gw-arrow {
  display: inline-block;
  width: 14px !important;
  height: 14px;
  line-height: 14px;
  text-shadow: none;
  font-size: 18px;
  position: absolute;
  right: 11px;
  top: 11px;
  padding: 0;
  color: #666;
}

.gw-nav-list>li a:hover>.gw-arrow,
.gw-nav-list>li.active>a>.gw-arrow,
.gw-nav-list>li.gw-open>a>.gw-arrow {
  color: #1963aa;
}

.gw-nav-list>li>a>[class*="icon-"]:first-child {
  display: inline-block;
  vertical-align: middle;
  min-width: 30px;
  text-align: center;
  font-size: 18px;
  font-weight: normal;
  margin-right: 2px;
}

.gw-nav-list>li.active .gw-submenu {
  display: block;
  -webkit-box-shadow: inset 0px 0px 5px rgba(0, 0, 0, 0.13);
  -moz-box-shadow: inset 0px 0px 5px rgba(0, 0, 0, 0.13);
  box-shadow: inset 0px 0px 5px rgba(0, 0, 0, 0.13);
}

.gw-nav-list>li .gw-submenu {
  font-size: 13px;
  display: none;
  list-style: none;
  margin: 0;
  padding: 0;
  position: relative;
  background-color: #fff;
  border-top: 1px solid #e5e5e5;
}

.gw-nav-list>li .gw-submenu>li {
  margin-left: 0;
  position: relative;
}

.gw-nav-list>li .gw-submenu>li>a {
  display: block;
  position: relative;
  color: #616161;
  padding: 7px 0 9px 43px;
  margin: 0;
  border-top: 1px dotted #e4e4e4;
  font-size: 0.8rem;
  text-decoration: none;
}

.gw-nav-list>li .gw-submenu>li>a:focus {
  text-decoration: none;
}

.gw-nav-list>li .gw-submenu>li>a:hover {
  text-decoration: none;
  color: #dd4814;
  background-color: rgba(25, 25, 50, 0.1);
}

.gw-nav-list>li .gw-submenu>li.active:after {
  display: inline-block;
  content: "";
  position: absolute;
  right: -1px;
  top: -1px;
  bottom: 0;
  z-index: 1;
  border: 2px solid #dd4814;
}

.gw-nav-list>li .gw-submenu>li.active>a {
  color: #dd4814;
}

.gw-nav-list>li .gw-submenu>li a>[class*="icon-"]:first-child {
  display: none;
  font-size: 12px;
  font-weight: normal;
  width: 18px;
  height: auto;
  line-height: 12px;
  text-align: center;
  position: absolute;
  left: 10px;
  top: 11px;
  z-index: 1;
  background-color: #fff;
}

.gw-nav-list>li .gw-submenu>li.active>a>[class*="icon-"]:first-child,
.gw-nav-list>li .gw-submenu>li:hover>a>[class*="icon-"]:first-child {
  display: inline-block;
}

.gw-nav-list>li .gw-submenu>li.active>a>[class*="icon-"]:first-child {
  color: #c86139;
}

.gw-nav-list>li>.gw-submenu>li:first-child>a {
  border-top: 0px;
}

.gw-nav-list li .gw-submenu {
  overflow: hidden;
}

.gw-nav-list li.active.gw-open>.gw-submenu>li.active.gw-open>a.dropdown-toggle:after {
  display: none;
}

.gw-nav-list li.active>.gw-submenu>li.active>a:after {
  display: none;
}

.gw-nav-list li.active.gw-open>.gw-submenu>li.active>a:after {
  display: block;
}

.gw-nav-tabs li[class*=" icon-"],
.nav-tabs li[class^="icon-"] {
  width: 1.25em;
  display: inline-block;
  text-align: center;
}

.w-70 {
  width: 73.5% !important;
}

/*=================left navibar end=========================================*/

.child-menu {
  display: none;
}

.child-menu a {
  display: block;
  position: relative;
  color: #616161;
  padding: 7px 0 9px 55px !important;
  margin: 0;
  border-top: 1px dotted #e4e4e4;
  font-size: 14px;
  text-decoration: none;
}

.child-menu a:hover {
  text-decoration: none;
  color: #dd4814;
  background-color: rgba(25, 25, 50, 0.1);
}

.child-menu a:after {
  display: none;
}

.icon_dv {
  color: #666;
  font-size: 7rem;
}

.dropdown-tree>ul {
  overflow-y: auto;
  overflow-x: hidden;
  white-space: nowrap;
}

.dropdown-tree li {
  list-style: none;
}

.dropdown-tree li>i {
  margin-left: 10px;
  width: 20px;
  height: 20px;
  text-align: center;
  position: relative;
  z-index: 1;
  font-size: 20px;
  top: 3px;
}

.dropdown-tree li:hover {
  background: #eee;
}

.dropdown-tree li:hover ul {
  background: white;
}

.dropdown-tree li:hover ul li:hover {
  background: #eee;
}

.dropdown-tree a {
  display: inline-block !important;
  padding: 3px 20px;
  clear: both;
  font-weight: 400;
  line-height: 1.42857143;
  color: #333;
  white-space: nowrap;
  text-decoration: none;
  background: transparent !important;
  position: relative;
}

.dropdown-tree .arrow {
  position: absolute;
  margin-left: -15px;
  top: 50%;
  transform: translateY(-50%);
}

/*RTL CSS*/
.rtl-dropdown-tree {
  direction: rtl !important;
}

.rtl-dropdown-tree>ul {
  right: 0;
  left: unset;
  text-align: right;
}

.rtl-dropdown-tree .arrow {
  right: 6px;
}

.rtl-dropdown-tree li>i {
  margin-left: 0;
  margin-right: 10px;
}

/* #view .modal-footer {
  justify-content: space-between;
} */
a:hover {
  color: red;
}

.has-search .form-control {
  padding-left: 2.375rem;
}

.has-search .form-control-feedback {
  position: absolute;
  z-index: 2;
  display: block;
  width: 2.375rem;
  height: 2.375rem;
  line-height: 2.375rem;
  text-align: center;
  pointer-events: none;
  color: #aaa;
}

button.close {
  outline: none;
  color: white;
}

.breadcrumb-item.active {
  font-size: 0.85rem;
}

.morning {
  font-size: 0.65rem !important;
  padding: 0.3rem !important;
  color: #fff !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  border-radius: 4rem !important;
  background-color: #34c38f !important;
  margin-bottom: 1px !important;
  border: none !important;
}

.afternoon {
  font-size: 0.65rem !important;
  padding: 0.3rem !important;
  color: #1f2d3d !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  border-radius: 4rem !important;
  background-color: #ffc107 !important;
  margin-bottom: 1px !important;
  border: none !important;
}

a.afternoon .fc-sticky {
  color: #1f2d3d !important;
}

.full-time {
  font-size: 0.65rem !important;
  padding: 0.3rem !important;
  color: #fff !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  border-radius: 4rem !important;
  background-color: #556ee6 !important;
  margin-bottom: 1px !important;
  border: none !important;
}

.holiday {
  font-size: 0.65rem !important;
  padding: 0.3rem !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  border-radius: 4rem !important;
  background-color: #fff !important;
  margin-bottom: 1px !important;
  border: #f46a6a 1px solid !important;
}

.holiday-day-number .fc-daygrid-day-number {
  color: #f46a6a !important;
}

.holiday-day-number .fc-event-title {
  color: #f46a6a !important;
}

.waiting-days {
  font-size: 0.65rem !important;
  padding: 0.3rem !important;
  color: #fff !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  border-radius: 4rem !important;
  /*background-color: #50a5f1 !important;*/
  background-color: #f46a6a !important;
  margin-bottom: 1px !important;
  border: none !important;
}

.day-off {
  background-color: #dddddd !important;
  border: 1px solid #dddddd !important;
}

.not-found-record {
  padding: 0.35rem !important;
  line-height: 2rem;
  vertical-align: middle;
  font-size: 0.85rem !important;
  color: #7e7e7e;
  border-top: 0 solid #dee2e6 !important;
  background-color: rgba(0, 0, 0, 0.05);
}

.nowrap {
  white-space: nowrap;
}

.text-nowrap {
  white-space: nowrap;
  max-width: 20vh;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-height {
  height: calc(100% - 12px);
}

.breadcrumb-custom {
  display: flex;
  flex-wrap: wrap;
  padding: 0.75rem 1rem;
  margin-bottom: 0rem;
  list-style: none;
}

table.table thead>tr>td[sortable],
table.table thead>tr>td[sortable].asc,
table.table thead>tr>td[sortable].desc,
table.table thead>tr>th[sortable],
table.table thead>tr>th[sortable].asc,
table.table thead>tr>th[sortable].desc {
  padding-right: 30px;
}

table.table thead>tr>td:active,
table.table thead>tr>th:active {
  outline: 0;
}

table.table thead th[sortable],
table.table thead th[sortable].asc,
table.table thead th[sortable].desc {
  cursor: pointer;
  position: relative;
  padding-right: 2em !important;
}

table.table thead th[sortable]:after,
table.table thead th[sortable]:before,
table.table thead th[sortable].asc:after,
table.table thead th[sortable].asc:before,
table.table thead th[sortable].desc:after,
table.table thead th[sortable].desc:before {
  position: absolute;
  bottom: 0.3em;
  display: block;
  opacity: 0.3;
}

table.table thead th[sortable]:before,
table.table thead th[sortable].asc:before,
table.table thead th[sortable].desc:before {
  right: 0.5em;
  content: "\f0de";
  font-family: "Font Awesome\ 5 Free";
  font-weight: 900;
  font-size: 1rem;
}

table.table thead th[sortable]:after,
table.table thead th[sortable].asc:after,
table.table thead th[sortable].desc:after {
  content: "\f0dd";
  font-family: "Font Awesome\ 5 Free";
  font-weight: 900;
  right: 0.5em;
  font-size: 1rem;
}

table.table thead th[sortable].asc:before,
table.table thead th[sortable].desc:after {
  opacity: 1;
}

/* .table-responsive {
    overflow: scroll;
} */

table.table-fixed {
  /* border-collapse: separate; */
  border: 1px solid #dee2e6;
  border-collapse: collapse;
}

table.table-fixed thead tr:nth-child(1) th,
table.table-fixed thead tr:nth-child(1) th[sortable] {
  background-color: #f8f9fa !important;
  position: sticky;
  position: -webkit-sticky;
  top: 0;
  z-index: 10;
  border-top: none !important;
  border-bottom: none !important;
  box-shadow: inset 0 1px 0 #dee2e6, inset 0 -1px 0 #dee2e6;
  padding: 2px 0;
  background-clip: padding-box;
}

.ng-select.ng-select-single .ng-select-container {
  height: 2.2 rem !important;
}

.ng-select.ng-select-focused:not(.ng-select-opened)>.ng-select-container {
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075),
    0 0 0 3px rgba(0, 126, 255, 0.1);
  border-color: var(--mb-color);
}

#info-block section {
  border-radius: 15px;
  border: 1px solid #d3d3d3;
  padding: 25px;
  padding-top: 0px;
}

.file-marker>div {
  padding: 0 1px;
  margin-top: -0.8em;
}

.box-title {
  background: white none repeat scroll 0 0;
  display: inline-block;
  padding: 0 2px;
}

.cursor-pointer {
  cursor: pointer;
}

body {
  width: auto !important;
}

.cdk-overlay-container {
  z-index: 2039 !important;
}

.border-input {
  border-top: 1.5px solid #a9a9ab !important;
}

.divider {
  border-top: 1px solid #cecece;
  height: 1px;
  width: 100%;
}

.btn-secondary-white {
  border: 1px solid #eb2d4b;
  background: white;
  color: #eb2d4b;
  border-radius: 8px;
  font-size: 14px;
  padding: 8px 25px;
}

.btn-secondary-white:hover {
  border: 1px solid #eb2d4b;
  background: white;
  color: #eb2d4b;
  border-radius: 8px;
  font-size: 14px;
  padding: 8px 25px;
}

.btn-danger-red {
  background: #eb2d4b;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  padding: 8px 25px;
}

.btn-danger-red:hover {
  background: #eb2d4b;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  padding: 8px 25px;
}

/* edit datepicker */
.date-picker.mat-form-field {
  display: block !important;
}

.date-picker .mat-form-field-flex {
  background-color: #fff !important;
  border: 1px solid #ced4da !important;
  border-radius: 0.25rem !important;
  padding: 0px 10px !important;
}

.mat-form-field-appearance-fill.mat-form-field-disabled .mat-form-field-flex {
  background-color: var(--mb-disabled-color) !important;
}

.date-picker .mat-form-field-flex:focus-within {
  background-color: #fff !important;
  border-color: var(--mb-color) !important;
  border-radius: 0.25rem !important;
  padding: 0px 10px !important;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075),
    0 0 0 3px rgba(0, 126, 255, 0.1);
  transition: all 0.3s ease;
}

.date-picker .mat-form-field-underline {
  display: none !important;
}

.date-picker .mat-form-field-appearance-fill .mat-form-field-infix {
  padding: 0px !important;
}

.date-picker .mat-form-field-suffix:focus-visible,
.date-picker .mat-datepicker-toggle:focus-visible,
.date-picker .mat-datepicker-toggle button:focus {
  outline: unset !important;
}

.date-picker.mat-form-field input.mat-input-element {
  position: absolute !important;
  top: -2px !important;
}

.date-picker .mat-form-field-wrapper {
  padding-bottom: 0 !important;
}

.mat-input-element:disabled,
.mat-form-field-type-mat-native-select.mat-form-field-disabled .mat-form-field-infix::after {
  color: black !important;
}

/* --------------- */

.sidebar-mini.sidebar-collapse .main-sidebar {
  padding-right: 15px !important;
}

::-webkit-scrollbar {
  width: 6px;
  background-color: #f5f5f5;
}

::-webkit-scrollbar-thumb {
  background-color: #423f3f;
  border-radius: 10px;
}

::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #f5f5f5;
}

/* tab custom */
.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: var(--mb-color) !important;
}

.ant-tabs-tab-active,
.ant-tabs-tab-active a {
  color: var(--mb-color) !important;
}

a:hover {
  color: var(--mb-color) !important;
  text-decoration: underline;
}

.ant-tabs-ink-bar {
  position: absolute;
  background: var(--mb-bg-color) !important;
  pointer-events: none;
}

.ant-tabs-tab:hover {
  color: var(--mb-color) !important;
}

/* mat-input custom*/
.mat-input-element:disabled {
  color: black !important;
}

/* form-check custom */
.form-check-input:disabled~.form-check-label,
.form-check-input[disabled]~.form-check-label {
  color: black !important;
}

.form-check-input:checked:disabled {
  background-color: var(--mb-disabled-color) !important;
}

/* date-picker-custom */

.date-picker-custom-fromdate .mat-form-field-flex:focus-within {
  background-color: #fff !important;
  border: 1px solid #ced4da !important;
  border-radius: 0.25rem !important;
  padding: 0px 10px !important;
  border-color: var(--mb-color) !important;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075),
    0 0 0 3px rgba(0, 126, 255, 0.1);
}

.date-picker-custom-todate .mat-form-field-flex:focus-within {
  background-color: #fff !important;
  border: 1px solid #ced4da !important;
  border-radius: 0.25rem !important;
  padding: 0px 10px !important;
  border-color: var(--mb-color) !important;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075),
    0 0 0 3px rgba(0, 126, 255, 0.1);
}

.date-picker-custom .mat-form-field-flex {
  margin-bottom: 10px !important;
}

/* .date-picker-custom {
  border: 1px solid #ccc;
  border-radius: 0.25rem;
} */

.border-bottom-search {
  border: 1px solid #ced4da;
  width: 100%;
  margin-top: 15px;
  margin-bottom: 15px;
}

.back-container {
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 5px;
}

.back-container>img {
  position: relative;
  top: -3px;
  cursor: pointer;
}

.form-container-money {
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  box-shadow: inset 0 0 0 transparent;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
}

.form-container-money:focus-within {
  color: #495057;
  background-color: #fff;
  border-color: var(--mb-color);
  outline: 0;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075),
    0 0 0 3px rgba(0, 126, 255, 0.1);
}

.form-control-money {
  font-size: 14px;
  height: 2.2rem;
  outline: none;
  border: none;
  min-width: 250px;
  padding-left: 10px;
}

.freezer {
  position: sticky;
  right: 0;
  width: 135px;
}

.status-freezer {
  position: sticky;
  right: 130px;
  width: 90px;
}

.hidden-freezer {
  position: sticky;
  right: 220px;
  width: 105px;
}

.hidden-freezer-td {
  position: sticky;
  right: 220px;
  width: 80px;
  background-color: white;
}

.status-freezer-td {
  position: sticky;
  right: 135px; 
  background-color: #fff;
  z-index: 1;
}

.freezer-td {
  position: sticky;
  right: 0;
  background-color: #fff;
  z-index: 2;
}

.bold-text {
  font-weight: bold;
  color: var(--mb-color);
}


/* text bold  level*/