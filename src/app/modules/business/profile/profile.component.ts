import { Component, OnInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { IUser } from '@shared/models/user.model';
import { AccountService } from '@shared/services/account.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ChangePasswordComponent } from './change-password/change-password.component';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss'],
})
export class ProfileComponent implements OnInit {
  user: IUser = {};

  constructor(
    private accountService: AccountService,
    private modalService: ModalService,
    private modalServiceOpen: NgbModal
  ) {
    this.onProfile();
  }

  ngOnInit(): void {}

  /**
   * onVerify: call api Verify
   */
  onProfile(): void {
    const params = {};
    this.accountService.profile(params).subscribe((res: any) => {
      this.user = res.body as IUser;
    });
  }

  onChangePassword(): void {
    // const modalData = {
    //   title: 'loanOnline.delete',
    //   content: 'loanOnline.deleteLoanOnlineContent',
    //   interpolateParams: { fullname: `<b></b>` },
    // };
    // this.modalService.confirm(modalData).then((result) => {});
    const modalRef = this.modalServiceOpen.open(ChangePasswordComponent, {
      backdrop: 'static',
      size: 'lg',
      centered: true,
      keyboard: false,
    });
  }
}
