import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { IRate } from '@shared/models/rate.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { AbstractDomainService } from './common/abstract.domain.service';

@Injectable({
  providedIn: 'root',
})
export class RateService extends AbstractDomainService<IRate> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.RATE;
}
