import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import {
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  MODAL_ACTION,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { IPosition } from '@shared/models/position.model';
import { IPositionSearch } from '@shared/models/request/position.search';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { PositionService } from '@shared/services/position.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import { PositionCreateUpdateComponent } from './position-create-update/position-create-update.component';

@Component({
  selector: 'app-position-management',
  templateUrl: './position-management.component.html',
  styleUrls: ['./position-management.component.scss'],
})
export class PositionManagementComponent implements OnInit, OnDestroy {
  positions: IPosition[] = [];
  recordSelected: any = [];
  storage: any;
  action: any = '';

  ENTITY_STATUS = ENTITY_STATUS;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  ROUTER_UTILS = ROUTER_UTILS;
  pageSizeOptions = PAGINATION.OPTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;

  // default form search
  formPositionSearch = this.fb.group({
    // positionName: '',
    // shortName: '',
    // positionCode: '',
    keyword: '',
    status: null,
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    // previousPageIndex: 0,
  });

  constructor(
    private fb: FormBuilder,
    private modalService: ModalService,
    private toastService: ToastrCustomService,
    private positionService: PositionService,
    private modalServiceOpen: NgbModal
  ) {
    // get session storage
    this.storage = sessionStorage.getItem(STORAGE_APP.POSITION);
  }

  /**
   * Init data
   */
  ngOnInit(): void {
    // check storage
    // (when click back button or back website)
    if (this.storage) {
      // get session storage and parse json
      const filter = JSON.parse(this.storage) as IPositionSearch;
      // set value form control
      if (filter.status) {
        const status = filter.status;
        this.formPositionSearch.controls.status.setValue(status);
      }
      this.formPositionSearch.controls.keyword.setValue(filter.keyword);
      // this.formPositionSearch.controls.positionName.setValue(
      //   filter.positionName
      // );
      // this.formPositionSearch.controls.shortName.setValue(filter.shortName);
      // this.formPositionSearch.controls.positionCode.setValue(
      //   filter.positionCode
      // );
      this.onSearch();
    } else {
      // set default value start date and end date
      this.onSearch();
    }
    // click form clear storage
    sessionStorage.removeItem(STORAGE_APP.POSITION);
  }

  ngOnDestroy(): void {
    this.modalService.dismissAll();
  }

  /**
   * Change page: next page and preview page
   *
   * @param page PageEvent
   */
  onChangePage(page: PageEvent) {
    this.formPositionSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formPositionSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  onSearchSubmit() {
    this.formPositionSearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.formPositionSearch.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    this.onSearch();
  }

  /**
   * search form
   */
  onSearch(): void {
    // this.positionSearch.positionName = body.positionName;
    // this.positionSearch.shortName = body.shortName;
    // this.positionSearch.positionCode = body.positionCode;
    // this.positionSearch.keyword = body.keyword;
    // this.positionSearch.status = body.status;

    this.positionService
      .search(this.formPositionSearch.value)
      .subscribe((res: any): void => {
        this.positions = res.body.content;
        this.formPositionSearch.controls.length.setValue(
          res.body.totalElements
        );
      });
  }

  /**
   * index on list loan online
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formPositionSearch.value.pageIndex,
      this.formPositionSearch.value.pageSize
    );
  }

  onCreate(): void {
    const modalRef = this.modalServiceOpen.open(PositionCreateUpdateComponent, {
      backdrop: 'static',
      size: 'lg',
      centered: true,
      keyboard: false,
    });
    modalRef.componentInstance.action = ROUTER_ACTIONS.create;
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;

    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }

  /**
   * button click detail
   *
   * @param positionId number
   */
  detail(positionId?: number): void {
    sessionStorage.setItem(
      STORAGE_APP.POSITION,
      JSON.stringify(this.formPositionSearch.value)
    );
    const modalRef = this.modalServiceOpen.open(PositionCreateUpdateComponent, {
      backdrop: 'static',
      size: 'lg',
      centered: true,
      keyboard: false,
    });
    modalRef.componentInstance.positionId = positionId;
    modalRef.componentInstance.action = ROUTER_ACTIONS.detail;
  }

  /**
   * button click edit
   *
   * @param positionId number
   */
  edit(positionId?: number): void {
    sessionStorage.setItem(
      STORAGE_APP.POSITION,
      JSON.stringify(this.formPositionSearch.value)
    );
    const modalRef = this.modalServiceOpen.open(PositionCreateUpdateComponent, {
      backdrop: 'static',
      size: 'lg',
      centered: true,
      keyboard: false,
    });
    modalRef.componentInstance.positionId = positionId;
    modalRef.componentInstance.action = ROUTER_ACTIONS.update;
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;

    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }

  /**
   * reset form
   */
  onReset(): void {
    // set default value date and reset data
    this.formPositionSearch.controls.keyword.reset();
    this.formPositionSearch.controls.status.reset();
  }

  /**
   * check lock and unlock and call api
   *
   * @param position IPosition
   */
  lockAndUnlock(position: IPosition): void {
    if (position.status === ENTITY_STATUS_CONST.INACTIVE.code) {
      this.unLockPosition(position);
    } else {
      this.lockPosition(position);
    }
  }

  /**
   * Lock position register
   *
   * @param position IPosition
   */
  private lockPosition(position: IPosition) {
    const modalData = {
      title: 'position.lock',
      content: 'position.lockPositionContent',
      interpolateParams: {
        positionName: `<b>${position?.positionName || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { positionId: position?.positionId };
        this.positionService.lock(params).subscribe((res) => {
          this.toastService.success('common.action.lockSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * UnLock position register
   *
   * @param position: IPosition
   */
  private unLockPosition(position: IPosition) {
    const modalData = {
      title: 'position.unlock',
      content: 'position.unlockPositionContent',
      interpolateParams: {
        positionName: `<b>${position?.positionName || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { positionId: position?.positionId };
        this.positionService.unlock(params).subscribe((res) => {
          this.toastService.success('common.action.unlockSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * delete
   *
   * @param position IPosition
   */
  delete(position: IPosition): void {
    // open modal
    const modalData = {
      title: 'position.delete',
      content: 'position.deletePositionContent',
      interpolateParams: {
        positionName: `<b>${position?.positionName || ''}</b>`,
      },
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { positionId: position?.positionId };
        this.positionService.deletePosition(params).subscribe((res: any) => {
          this.toastService.success('common.action.deleteSuccess');
          this.onSearch();
        });
      }
    });
  }
}
