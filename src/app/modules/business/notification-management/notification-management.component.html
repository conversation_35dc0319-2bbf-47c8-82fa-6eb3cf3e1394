<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formEventSearch" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-md-6 col-lg-3">
            <div class="form-group">
              <label>{{ "model.event.title" | translate }}</label>
              <input
                trim
                type="text"
                placeholder="{{ 'model.event.title' | translate }}"
                formControlName="title"
                class="w-100"
                class="form-control"
              />
            </div>
          </div>
          <div class="col-md-6 col-lg-3">
            <div class="form-group">
              <label>{{ "common.status" | translate }}</label>
              <ng-select
                appearance="outline"
                [searchable]="false"
                placeholder="{{ 'common.status' | translate }}"
                [clearable]="false"
                formControlName="eventStatus"
              >
                <ng-option
                  [value]="item?.code"
                  *ngFor="let item of EVENT_STATUS"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-8 col-lg-4">
            <div class="date-picker-container form-group row">
              <div class="col-md-6">
                <label
                  >{{ "common.action.fromDate" | translate
                  }}<span class="text-danger">*</span></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="fromTime"
                    formControlName="fromTime"
                    placeholder="DD/MM/YYYY"
                    [max]="maxDate | date : 'yyyy-MM-dd'"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="fromTime"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #fromTime></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formEventSearch.get('fromTime')?.errors?.required &&
                    formEventSearch.get('fromTime')?.touched
                  "
                >
                  {{ "error.required.fromDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="isErrorStartDateGreaterEndDate"
                >
                  {{ "error.toDateMustGreatherFromDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="formEventSearch.get('fromTime')?.errors?.invalidDate"
                >
                  {{ "error.required.inValidDate" | translate }}
                </small>
              </div>
              <div class="col-md-6">
                <label
                  >{{ "common.action.toDate" | translate
                  }}<span class="text-danger">*</span></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="toTime"
                    formControlName="toTime"
                    placeholder="DD/MM/YYYY"
                    min="{{
                      formEventSearch.controls['fromTime'].value
                        | date : 'yyyy-MM-dd'
                    }}"
                    [max]="MAX_DATE_CONST"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="toTime"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #toTime></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formEventSearch.get('toTime')?.errors?.required &&
                    formEventSearch.get('toTime')?.touched
                  "
                >
                  {{ "error.required.toDate" | translate }}
                </small>
              </div>
            </div>
          </div>
          <div class="col-lg-2 col-md-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
            >
              <div class="btn-reset">
                <i
                  class="bi bi-arrow-clockwise"
                  type="button"
                  (click)="onReset()"
                >
                </i>
              </div>
            </div>
            <div class="">
              <button
                class="btn btn-search mr-2"
                type="submit"
                *hasPrivileges="SYSTEM_RULES.NOTIFICATION_READ"
              >
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-3 mt-4">
          <button
            type="button"
            class="btn btn-red"
            [routerLink]="['create']"
            [routerLinkActive]="['active']"
            *hasPrivileges="SYSTEM_RULES.NOTIFICATION_CREATE"
          >
            {{ "common.action.create" | translate }}
          </button>
        </div>
      </form>
      <div></div>
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th class="text-center" scope="col">
                {{ "common.no" | translate }}
              </th>
              <th scope="col">{{ "model.event.eventType" | translate }}</th>
              <th scope="col">{{ "model.event.title" | translate }}</th>
              <th scope="col-3">
                {{ "model.event.content" | translate }}
              </th>
              <th scope="col">
                {{ "model.event.numberSent" | translate }}
              </th>
              <th scope="col">
                {{ "model.event.expectedNotificationAt" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.status" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of events; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td>{{ dataItem.announcementTypeName }}</td>
              <td>
                <span title="{{ dataItem.title }}">{{
                  dataItem.title | limitWord
                }}</span>
              </td>
              <td>
                <span
                  title="{{ getContentHTML(dataItem.content) }}"
                  [innerHTML]="
                    dataItem.content | limitWord : LIMIT_LENGTH_WORD_CONST.LARGE
                  "
                ></span>
              </td>
              <td *ngIf="dataItem.eventStatus !== EVENT_TYPE.DONE"></td>
              <td *ngIf="dataItem.eventStatus === EVENT_TYPE.DONE">
                {{ dataItem.totalSuccess + "/" }}
                <span [ngClass]="getEventBadge(dataItem.eventStatus) + ''">{{
                  dataItem?.totalNotification|| 0
                }}</span>
              </td>
              <td>{{ dataItem.expectedNotificationAt }}</td>
              <td class="text-center">
                <span [ngClass]="getEventBadge(dataItem.eventStatus) + ''">
                  {{ getEventStatus(dataItem.eventStatus) | translate }}
                </span>
              </td>

              <td class="text-center">
                <button
                  class="btn px-1 py-0"
                  (click)="detail(dataItem?.eventId)"
                  ngbTooltip="{{ 'common.action.detail' | translate }}"
                  *hasPrivileges="SYSTEM_RULES.NOTIFICATION_READ"
                >
                  <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                </button>
                <!--*ngIf="dataItem.approvalStatus === 1 && (dataItem.approvalType === 1 || dataItem.approvalType === 2)"-->
                <button
                  class="btn px-1 py-0"
                  (click)="delete(dataItem)"
                  ngbTooltip="{{ 'common.action.delete' | translate }}"
                  *hasPrivileges="SYSTEM_RULES.NOTIFICATION_DELETE"
                >
                  <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="row d-block text-center m-0" *ngIf="events?.length === 0">
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
        <div *ngIf="events.length">
          <mat-paginator
            [length]="formEventSearch.value.length"
            [pageSize]="formEventSearch.value.pageSize"
            [pageIndex]="formEventSearch.value.pageIndex"
            [pageSizeOptions]="pageSizeOptions"
            (page)="onChangePage($event)"
            aria-label="Select page"
          >
          </mat-paginator>
        </div>
      </div>
    </div>
  </div>
</section>
