import { Component, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  Validators,
} from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { TranslateService } from '@ngx-translate/core';
import {
  CALCULATE_MILLISECONDS_IN_A_YEAR,
  DATE_RANGE_STATISTICAL_LOAN_ONLINE,
  DRCR,
  FILE_EXTENSION,
  MOMENT_CONST,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { SELECTED_CONST } from '@shared/constants/user.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IReferral } from '@shared/models/referral.model';
import { ITransReport } from '@shared/models/trans-report.model';
import { DownloadService } from '@shared/services/helpers/download.service';
import { TransReportQuantityService } from '@shared/services/trans-report-quantitty.service';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';
@Component({
  selector: 'app-manage-transaction-quantity',
  templateUrl: './manage-transaction-quantity.component.html',
  styleUrls: ['./manage-transaction-quantity.component.scss'],
})
export class ManageTransactionQuantityComponent implements OnInit {
  maxDate = new Date();
  maxToDate = new Date();
  transactionReport: ITransReport[] = [];

  pageSizeOptions = PAGINATION.OPTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  SELECTED_CONST = SELECTED_CONST;
  PAGINATION = PAGINATION;
  VALIDATORS = VALIDATORS;
  MOMENT_CONST = MOMENT_CONST;
  DRCR = DRCR;

  isValidMaxDate = (control: FormControl) => {
    const dateOfBirth = new Date(control.value).getTime();
    const maxDateOfBirth = new Date().getTime();
    return dateOfBirth > maxDateOfBirth ? { invalidMaxDate: true } : null;
  };

  formTransactionReportSearch = this.fb.group({
    keyword: '',
    fromDate: [null, [Validators.required]],
    toDate: [null, [Validators.required, this.isValidMaxDate]],
    numberOfTransactionMin: [
      {
        value: '',
        disabled: true,
      },
    ],
    numberOfTransactionMax: [
      {
        value: '',
        disabled: true,
      },
    ],
    drCrs: [],
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    hasPageable: true,
    timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
  });

  constructor(
    private fb: FormBuilder,
    private translateService: TranslateService,
    private downloadService: DownloadService,
    private transReportQuantityService: TransReportQuantityService
  ) {}

  ngOnInit(): void {
    const startDate = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDate = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.formTransactionReportSearch.controls.fromDate.setValue(
      CommonUtils.reverseDate(startDate)
    );
    this.formTransactionReportSearch.controls.toDate.setValue(
      CommonUtils.reverseDate(endDate)
    );
    this.onSearch();
  }

  onSearchSubmit() {
    this.formTransactionReportSearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.formTransactionReportSearch.controls.length.setValue(
      PAGINATION.LENGTH_DEFAULT
    );
    this.onSearch();
  }

  onClearAll(value?: string) {
    switch (value) {
      case SELECTED_CONST.DR_CR:
        this.formTransactionReportSearch.get('drCrs')?.patchValue([]);
        this.formTransactionReportSearch
          .get('numberOfTransactionMin')
          ?.setValue(null);
        this.formTransactionReportSearch
          .get('numberOfTransactionMax')
          ?.setValue(null);
        this.formTransactionReportSearch
          .get('numberOfTransactionMin')
          ?.disable();
        this.formTransactionReportSearch
          .get('numberOfTransactionMax')
          ?.disable();
        break;
    }
  }

  onSelectAll(value?: string) {
    const selectedDrCr = this.DRCR.map((item) => item.code);
    switch (value) {
      case SELECTED_CONST.DR_CR:
        this.formTransactionReportSearch.get('drCrs')?.patchValue(selectedDrCr);
        this.formTransactionReportSearch.enable();
        break;
    }
  }

  dateValidator(value: number): any {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return value > DATE_RANGE_STATISTICAL_LOAN_ONLINE
        ? { invalidDate: true }
        : null;
    };
  }

  onChangeTransactionType(item: any) {
    if (item.length === 0) {
      this.formTransactionReportSearch
        .get('numberOfTransactionMin')
        ?.setValue(null);
      this.formTransactionReportSearch
        .get('numberOfTransactionMax')
        ?.setValue(null);
      this.formTransactionReportSearch.get('numberOfTransactionMin')?.disable();
      this.formTransactionReportSearch.get('numberOfTransactionMax')?.disable();
      this.formTransactionReportSearch.updateValueAndValidity();
    } else {
      if (this.formTransactionReportSearch.get('drCrs')?.value) {
        this.formTransactionReportSearch.enable();
      }
    }
  }

  changeValidDate(): void {
    if (
      this.formTransactionReportSearch.controls.fromDate.value &&
      this.formTransactionReportSearch.controls.toDate.value
    ) {
      if (this.formTransactionReportSearch.controls['toDate'].value) {
        this.maxDate =
          this.formTransactionReportSearch.controls['toDate'].value;
      }
      const startDate = CommonUtils.setStartTime(
        new Date(
          this.formTransactionReportSearch.controls.fromDate.value
        ).getTime()
      );
      const endDate = CommonUtils.setStartTime(
        new Date(
          this.formTransactionReportSearch.controls.toDate.value
        ).getTime()
      );
      const dayMax =
        (endDate - startDate) / CALCULATE_MILLISECONDS_IN_A_YEAR.DAY;
      if (dayMax > DATE_RANGE_STATISTICAL_LOAN_ONLINE) {
        this.formTransactionReportSearch.controls.fromDate.setValidators([
          Validators.required,
          this.dateValidator(dayMax),
          this.isValidMaxDate,
        ]);
        this.formTransactionReportSearch.controls.fromDate.updateValueAndValidity();
      } else {
        this.formTransactionReportSearch.controls.fromDate.clearValidators();
        this.formTransactionReportSearch.controls.fromDate.setValidators([
          Validators.required,
          this.isValidMaxDate,
        ]);
        this.formTransactionReportSearch.controls.fromDate.updateValueAndValidity();
      }
    }
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formTransactionReportSearch.value.pageIndex,
      this.formTransactionReportSearch.value.pageSize
    );
  }

  onSearch(): void {
    if (this.formTransactionReportSearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formTransactionReportSearch);
      return;
    }
    this.formTransactionReportSearch.controls.hasPageable.setValue(true);
    this.transReportQuantityService
      .search(this.formTransactionReportSearch.value)
      .subscribe((res: any): void => {
        this.transactionReport = res.body.content as ITransReport[];
        this.formTransactionReportSearch.controls.length.setValue(
          res.body.totalElements
        );
      });
  }

  onReset(): void {
    const startDateReset = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDateReset = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.formTransactionReportSearch.controls.keyword.reset();
    this.formTransactionReportSearch.controls.fromDate.setValue(
      CommonUtils.reverseDate(startDateReset)
    );
    this.formTransactionReportSearch.controls.toDate.setValue(
      CommonUtils.reverseDate(endDateReset)
    );
    this.formTransactionReportSearch.controls.fromDate.clearValidators();
    this.formTransactionReportSearch.controls.fromDate.setValidators([
      Validators.required,
    ]);
    this.formTransactionReportSearch.controls.fromDate.updateValueAndValidity();
    this.formTransactionReportSearch
      .get('timeZoneStr')
      ?.setValue(Intl.DateTimeFormat().resolvedOptions().timeZone);

    this.formTransactionReportSearch.controls.keyword.reset();
    this.formTransactionReportSearch.controls.numberOfTransactionMin.reset();
    this.formTransactionReportSearch.controls.numberOfTransactionMax.reset();
    this.formTransactionReportSearch.controls.drCrs.reset();
    this.formTransactionReportSearch.get('numberOfTransactionMin')?.disable();
    this.formTransactionReportSearch.get('numberOfTransactionMax')?.disable();
  }

  onChangePage(page: PageEvent) {
    this.formTransactionReportSearch.controls.pageIndex.setValue(
      page.pageIndex
    );
    this.formTransactionReportSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  exportFile() {
    const bodySearch = this.formTransactionReportSearch.getRawValue();
    bodySearch.hasPageable = false;
    if (this.formTransactionReportSearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formTransactionReportSearch);
    } else {
      // get file name
      const fileName = this.translateService.instant('template.transReport');
      const obFile = this.transReportQuantityService.export({
        fromDate: bodySearch.fromDate,
        toDate: bodySearch.toDate,
        keyword: bodySearch.keyword,
        numberOfTransactionMax: bodySearch.numberOfTransactionMax,
        numberOfTransactionMin: bodySearch.numberOfTransactionMin,
        timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
      });

      this.downloadService.downloadFileWithObservableAndName(
        obFile,
        fileName,
        FILE_EXTENSION.XLSX
      );
    }
  }
}
