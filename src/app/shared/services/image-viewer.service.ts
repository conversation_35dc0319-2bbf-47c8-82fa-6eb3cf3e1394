import { Injectable } from '@angular/core';
import {
  NzImage,
  NzImagePreviewOptions,
  NzImageService,
} from 'ng-zorro-antd/image';

@Injectable({ providedIn: 'root' })
export class ImageViewerService {
  imagePreviewOptionsDefault: NzImagePreviewOptions = {
    nzZoom: 2.0,
    nzRotate: 0,
  };

  constructor(private imageService: NzImageService) {}

  /**
   * preview image: display image
   *
   * @param fileSrcs string[]
   * @param options NzImagePreviewOptions
   */
  public previewImages(fileSrcs: string[], options?: NzImagePreviewOptions) {
    this.imageService.preview(
      this.tranform(fileSrcs),
      options ? options : this.imagePreviewOptionsDefault
    );
  }

  /**
   * tranform html
   *
   * @param srcs string[]
   * @returns NzImage[]
   */
  private tranform(srcs: string[]): NzImage[] {
    const result: NzImage[] = [];

    // set value and style html
    srcs.forEach((value) => {
      result.push({
        src: value,
        width: '300px',
        height: 'auto',
      });
    });
    return result;
  }
}
