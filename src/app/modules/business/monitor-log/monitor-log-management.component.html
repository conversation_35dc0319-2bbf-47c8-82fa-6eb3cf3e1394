<div class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="searchForm" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-lg-2">
            <div class="form-group">
              <label>{{
                "model.monitorLog.clientMessageId" | translate
              }}</label>
              <input
                trim
                class="form-control w-100"
                formControlName="clientMessageId"
                type="text"
                placeholder="{{
                  'model.monitorLog.clientMessageId' | translate
                }}"
                [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
              />
            </div>
          </div>
          <div class="col-lg-2">
            <div class="form-group">
              <label>{{ "model.monitorLog.serviceName" | translate }}</label>
              <input
                trim
                class="form-control w-100"
                formControlName="serviceName"
                type="text"
                placeholder="{{ 'model.monitorLog.serviceName' | translate }}"
                [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
              />
            </div>
          </div>
          <div class="col-lg-2">
            <div class="form-group">
              <label>{{ "model.monitorLog.requestUri" | translate }}</label>
              <input
                trim
                class="form-control w-100"
                formControlName="requestUri"
                type="text"
                placeholder="{{ 'model.monitorLog.requestUri' | translate }}"
                [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
              />
            </div>
          </div>
          <div class="col-lg-4">
            <div class="form-group row">
              <div class="col-md-6">
                <label
                  >{{ "common.action.fromDate" | translate
                  }}<span class="text-danger">*</span></label
                >

                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="fromDate"
                    formControlName="fromDate"
                    placeholder="DD/MM/YYYY"
                    [max]="maxDate"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="fromDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #fromDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    searchForm.get('fromDate')?.errors?.required &&
                    searchForm.get('fromDate')?.touched
                  "
                >
                  {{ "error.required.fromDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    searchForm.get('fromDate')?.errors?.invalidDate &&
                    searchForm.get('fromDate')?.touched
                  "
                >
                  {{ "error.required.inValidDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    searchForm.get('fromDate')?.errors?.invalidMaxDate &&
                    searchForm.get('fromDate')?.touched
                  "
                >
                  {{
                    "error.maxDateCurrent"
                      | translate
                        : {
                            param: "common.action.fromDate" | translate
                          }
                  }}
                </small>
              </div>
              <div class="col-md-6">
                <label
                  >{{ "common.action.toDate" | translate
                  }}<span class="text-danger">*</span></label
                >

                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="toDate"
                    formControlName="toDate"
                    placeholder="DD/MM/YYYY"
                    min="{{
                      searchForm.controls['fromDate'].value
                        | date : 'yyyy-MM-dd'
                    }}"
                    [max]="maxToDate"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="toDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #toDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    searchForm.get('toDate')?.errors?.required &&
                    searchForm.get('toDate')?.touched
                  "
                >
                  {{ "error.required.toDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    searchForm.get('toDate')?.errors?.invalidMaxDate &&
                    searchForm.get('toDate')?.touched
                  "
                >
                  {{
                    "error.maxDateCurrent"
                      | translate
                        : {
                            param: "common.action.toDate" | translate
                          }
                  }}
                </small>
              </div>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
              (click)="onReset()"
            >
              <div class="btn-reset">
                <i class="bi bi-arrow-clockwise" type="button"> </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" type="submit">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
      </form>

      <div class="table-responsive">
        <nz-table
          [nzData]="data"
          class="table-account-transaction"
          (nzQueryParams)="onChangePageSort($event)"
        >
          <thead>
            <tr>
              <th [width]="'50px'" class="text-center">
                {{ "model.manageSaving.no" | translate }}
              </th>
              <th [width]="'180px'" class="text-left">
                {{ "model.monitorLog.type" | translate }}
              </th>
              <th class="text-left" [width]="'250px'">
                {{ "model.monitorLog.clientMessageId" | translate }}
              </th>
              <th class="text-left" [width]="'250px'">
                {{ "model.monitorLog.method" | translate }}
              </th>
              <th class="text-left" [width]="'250px'">
                {{ "model.monitorLog.requestUri" | translate }}
              </th>
              <th class="text-left" [width]="'250px'">
                {{ "model.monitorLog.serviceName" | translate }}
              </th>
              <th
                class="text-left"
                [width]="'250px'"
                nzColumnKey="duration"
                [nzSortFn]="true"
              >
                {{ "model.monitorLog.duration" | translate }}
              </th>
              <th class="text-left" [width]="'250px'">
                {{ "model.monitorLog.httpStatus" | translate }}
              </th>
              <th class="text-left" [width]="'250px'">
                {{ "model.monitorLog.requestTime" | translate }}
              </th>
              <th class="text-left" [width]="'250px'">
                {{ "model.monitorLog.responseTime" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of data; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td class="text-left">{{ item.type }}</td>
              <td class="text-left">{{ item.clientMessageId }}</td>
              <td class="text-left">{{ item.method }}</td>
              <td class="text-left">{{ item.requestUri }}</td>
              <td class="text-left">{{ item.serviceName }}</td>
              <td class="text-left">{{ item.duration }}</td>
              <td class="text-left">{{ item.httpStatus }}</td>
              <td class="text-left">{{ item.createdDate }}</td>
              <td class="text-left">{{ item.lastModifiedDate }}</td>
            </tr>
          </tbody>
        </nz-table>
        <div
          class="row d-block text-center m-0 no-search-result-wrapper"
          *ngIf="data?.length === 0"
        >
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
      </div>
      <div *ngIf="data?.length">
        <mat-paginator
          [length]="searchForm.value.length"
          [pageSize]="searchForm.value.pageSize"
          [pageIndex]="searchForm.value.pageIndex"
          [pageSizeOptions]="pageSizeOptions"
          (page)="onChangePage($event)"
          aria-label="Select page"
        >
        </mat-paginator>
      </div>
    </div>
  </div>
</div>
