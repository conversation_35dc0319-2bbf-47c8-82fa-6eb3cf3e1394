import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LoginComponent } from '@auth/login/login.component';
import { RegisterComponent } from '@auth/register/register.component';
import { AuthGuard, LoggedInGuard } from '@core/guards';
import { FREE_PRIVILEGE } from '@shared/constants/authority.constants';
import { ROUTER_UTILS } from '@shared/utils';

import { ChangePasswordExpireComponent } from './change-password-expire/change-password-expire.component';

export const AUTH_ROUTES: Routes = [
  {
    path: ROUTER_UTILS.auth.login,
    component: LoginComponent,
    canActivate: [LoggedInGuard],
  },
  {
    path: ROUTER_UTILS.auth.register,
    component: RegisterComponent,
    canActivate: [LoggedInGuard],
  },
  {
    path: ROUTER_UTILS.auth.changePasswordExpire,
    canActivate: [AuthGuard],
    data: {
      privilege: FREE_PRIVILEGE,
      title: 'sidebar.changePassword',
    },
    component: ChangePasswordExpireComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(AUTH_ROUTES)],
  exports: [RouterModule],
})
export class AuthRoutingModule {}
