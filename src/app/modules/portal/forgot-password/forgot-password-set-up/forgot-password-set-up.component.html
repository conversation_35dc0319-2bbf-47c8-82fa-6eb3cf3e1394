<!-- <div *ngIf="this.activeToken.token">
  <p *ngIf="!activeToken.user?.active && !expire24Hour">
    active-user works! check mail send user and password
  </p>
  <p *ngIf="!active && !expire24Hour">
    active-user works! check mail send user and password
  </p>
  <p *ngIf="activeToken.user?.active && !expire24Hour">
    user already is active
  </p>
  <p *ngIf="active && !expire24Hour">user already is active</p>
  <div class="d-block text-center mb-3 mt-4" *ngIf="expire24Hour && !active">
    <button class="btn btn-white" (click)="onReSend()">
      {{ "public.resend" | translate }}
    </button>
  </div>
</div> -->

<main class="bg-login d-flex align-items-center min-vh-100 py-3 py-md-0">
  <app-change-language></app-change-language>
  <div class="wrap-login">
    <div class="login-card">
      <div class="row no-gutters">
        <div class="col-lg-12 col-md-12">
          <div class="card-body">
            <div class="brand-wrapper text-center">
              <strong class="title">{{
                "public.forgotPassword.titleChangePassword" | translate
              }}</strong>
            </div>
            <form [formGroup]="userForgotPasswordForm">
              <div class="input mb-3">
                <div class="form-group">
                  <label
                    >{{ "public.forgotPassword.email" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    trim
                    type="text"
                    autocomplete="off"
                    class="w-100"
                    formControlName="email"
                    class="form-control"
                    placeholder="{{
                      'public.forgotPassword.placeholder.email' | translate
                    }}"
                  />
                </div>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userForgotPasswordForm.get('email')?.errors?.required &&
                    userForgotPasswordForm.get('email')?.touched
                  "
                >
                  {{ "public.forgotPassword.error.required.email" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userForgotPasswordForm.get('email')?.errors?.maxlength &&
                    userForgotPasswordForm.get('email')?.touched
                  "
                >
                  {{
                    "public.forgotPassword.error.maxLength.email"
                      | translate
                        : {
                            param: VALIDATORS.LENGTH.EMAIL_MAX_LENGTH
                          }
                  }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userForgotPasswordForm.get('email')?.errors?.pattern &&
                    userForgotPasswordForm.get('email')?.touched
                  "
                >
                  {{ "public.forgotPassword.error.pattern.email" | translate }}
                </small>
              </div>
              <div class="input mb-3">
                <div class="form-group position-relative">
                  <label
                    >{{ "public.forgotPassword.password" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    trim
                    #password
                    [type]="passwordVisible ? 'text' : 'password'"
                    autocomplete="new-password"
                    class="w-100"
                    formControlName="password"
                    class="form-control"
                    placeholder="{{
                      'public.forgotPassword.password' | translate
                    }}"
                  />
                  <span
                    class="icon-eye cursor-pointer"
                    (click)="onChangPasswordVisible()"
                  >
                    <i
                      [classList]="
                        passwordVisible ? 'bi-eye-slash' : 'bi bi-eye'
                      "
                    ></i>
                  </span>
                </div>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userForgotPasswordForm.get('password')?.errors?.required &&
                    userForgotPasswordForm.get('password')?.touched
                  "
                >
                  {{
                    "public.forgotPassword.error.required.password" | translate
                  }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userForgotPasswordForm.get('password')?.errors?.minlength &&
                    userForgotPasswordForm.get('password')?.touched
                  "
                >
                  {{
                    "public.forgotPassword.error.minLength.password"
                      | translate
                        : {
                            param: VALIDATORS.LENGTH.PASSWORD_MIN_LENGTH
                          }
                  }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userForgotPasswordForm.get('password')?.errors?.maxlength &&
                    userForgotPasswordForm.get('password')?.touched
                  "
                >
                  {{
                    "public.forgotPassword.error.maxLength.password"
                      | translate
                        : {
                            param: VALIDATORS.LENGTH.PASSWORD_MAX_LENGTH
                          }
                  }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userForgotPasswordForm.get('password')?.errors?.pattern &&
                    userForgotPasswordForm.get('password')?.touched
                  "
                >
                  {{
                    "public.forgotPassword.error.pattern.password" | translate
                  }}
                </small>
              </div>
              <div class="input">
                <div class="form-group position-relative">
                  <label
                    >{{ "public.forgotPassword.confirmPassword" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    trim
                    #confirmPassword
                    [type]="confirmPasswordVisible ? 'text' : 'password'"
                    autocomplete="new-password"
                    class="w-100"
                    formControlName="confirmPassword"
                    class="form-control"
                    placeholder="{{
                      'public.forgotPassword.confirmPassword' | translate
                    }}"
                  />
                  <span
                    class="icon-eye cursor-pointer"
                    (click)="onChangConfirmPasswordVisible()"
                  >
                    <i
                      [classList]="
                        confirmPasswordVisible ? 'bi-eye-slash' : 'bi bi-eye'
                      "
                    ></i>
                  </span>
                </div>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userForgotPasswordForm.get('confirmPassword')?.errors
                      ?.required &&
                    userForgotPasswordForm.get('confirmPassword')?.touched
                  "
                >
                  {{
                    "public.forgotPassword.error.required.confirmPassword"
                      | translate
                  }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userForgotPasswordForm.get('confirmPassword')?.errors
                      ?.minlength &&
                    userForgotPasswordForm.get('confirmPassword')?.touched
                  "
                >
                  {{
                    "public.forgotPassword.error.minLength.confirmPassword"
                      | translate
                        : {
                            param: VALIDATORS.LENGTH.PASSWORD_MIN_LENGTH
                          }
                  }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userForgotPasswordForm.get('confirmPassword')?.errors
                      ?.maxlength &&
                    userForgotPasswordForm.get('confirmPassword')?.touched
                  "
                >
                  {{
                    "public.forgotPassword.error.maxLength.confirmPassword"
                      | translate
                        : {
                            param: VALIDATORS.LENGTH.PASSWORD_MAX_LENGTH
                          }
                  }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userForgotPasswordForm.get('confirmPassword')?.errors
                      ?.pattern &&
                    userForgotPasswordForm.get('confirmPassword')?.touched
                  "
                >
                  {{
                    "public.forgotPassword.error.pattern.confirmPassword"
                      | translate
                  }}
                </small>
                <span
                  class="text-danger"
                  *ngIf="f.confirmPassword.errors?.matching"
                >
                  <small>{{
                    "public.forgotPassword.error.notMatch" | translate
                  }}</small>
                </span>
              </div>
              <div class="callout callout-warning text-left mb-5 mt-3">
                <div
                  class="footer"
                  [innerHTML]="
                    displayContent('public.forgotPassword.footerChangePassword')
                  "
                ></div>
              </div>
              <button
                class="btn btn-block login-btn mb-4"
                (click)="onSetUpPassword()"
              >
                {{ "common.action.next" | translate }}
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>
