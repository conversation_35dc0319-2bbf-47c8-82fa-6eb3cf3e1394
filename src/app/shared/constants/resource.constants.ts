import { FeeSchedule } from '@shared/models/FeeSchedule.model';
import { BgLogin } from '@shared/models/bg-login.model';
import { ConfigAuto } from '@shared/models/config-auto-transaction.model';
import { ConfigSaving } from '@shared/models/config-saving.model';
import { Currency } from '@shared/models/currency.model';
import { CustomerSupport } from '@shared/models/customer-support.model';
import { Dotp } from '@shared/models/dotp.model';
import { LoanOnline } from '@shared/models/loan-online.models';
import { MonitorLog } from '@shared/models/monitor-log.model';
import { News } from '@shared/models/news.model';
import { NotificationHistoryTransaction } from '@shared/models/notification-history-transaction.model';
import { NumberGroup } from '@shared/models/number-group.model';
import { PremiumAccNumber } from '@shared/models/premium-acc-number.modal';
import {
  NumberStructure,
  SpecialPremiumAccountNumber,
} from '@shared/models/premium-account-number.modal';
import { Rate } from '@shared/models/rate.model';
import { Referral } from '@shared/models/referral.model';
import { Saving } from '@shared/models/saving.model';
import { Client, ServicePack } from '@shared/models/service-pack.model';
import { TransactionQrPay } from '@shared/models/transaction-qr-pay.model';
import { User } from '@shared/models/user.model';

export const SERVICE = {
  IAM: '/iam/api',
  CATALOG: '/catalog',
  CUSTOMER: '/customer',
  AUTH: '/auth',
  PROFILE: '/profile',
  REFERRAL: '/referral',
  LOAN_ONLINE: '/loan-online',
  SAVING: '/saving-account',
  RATE: '/interest-rate',
  CONFISCATING: '/saving-configuration',
  NEWS: '/news',
  FEE_SCHEDULE: '/fee-schedule',
  CONFIG_AUTO: '/configure-automatic',
  BG_LOGIN_APP: '/image-login-app',
  CUSTOMER_SUPPORT: '/customer-support',
  PREMIUM_ACCOUNT_STRUCTURE: '/premium-account-number-structure',
  NUMBER_GROUP: '/number-group',
  HIDDEN_ACCOUNT: '/premium-acc-number/hidden',
  SPECIAL_ACCOUNT: '/premium-acc-number/special',
  MONITOR_LOG: '/monitor-log',
  TRANSACTION_QR_PAY: '/transaction-qrpay',
  SERVICE_PACK: '/service-pack',
  CLIENT: '/client',
  SMS_BALANCE: '/sms-balance',
  PREMIUM_ACCOUNT_NUMBER: '/premium-account-number',
  CURRENCY: '/currency',
  NOTIFICATION_HISTORY: '/notification-history',
  DOTP: '/dotp',
};

export const DOMAIN_CONST = {
  PUBLIC: 'public',
  USER: SERVICE.CATALOG + '/user',
  ROLE: SERVICE.CATALOG + '/role',
  CUSTOMER: SERVICE.CATALOG + '/customer',
  PRODUCT: SERVICE.CATALOG + '/product',
  PACKAGE: SERVICE.CATALOG + '/package',
  SMART_BANK: SERVICE.CATALOG + '/smart-bank',
  BRANCH: SERVICE.CATALOG + '/branch',
  DEVICE_TYPE: SERVICE.CATALOG + '/device-type',
  DEVICE: SERVICE.CATALOG + '/device',
  SHIFT_SETTING: SERVICE.CATALOG + '/shift-setting',
  SHIFT_ASSIGN: SERVICE.CATALOG + '/shift-assign',
  SHIFT_HISTORY: SERVICE.CUSTOMER + '/shift-history',
  MB_AREA: SERVICE.CATALOG + '/mb-area',
  RM_INFO: SERVICE.CATALOG + '/rm-info',
  RESOURCE_FILE: SERVICE.PROFILE,
  COLLABORATOR: SERVICE.CATALOG + '/collaborator',
  SALE_RESULTS: SERVICE.CUSTOMER + '/sale-results',
  DASHBOARD: SERVICE.CUSTOMER + '/dashboard',
  REFERRAL: SERVICE.REFERRAL,
  LOAN_ONLINE: SERVICE.LOAN_ONLINE,
  SAVING: SERVICE.SAVING,
  RATE: SERVICE.RATE,
  CONFIGURING: SERVICE.CONFISCATING,
  NEWS: SERVICE.NEWS,
  FEE_SCHEDULE: SERVICE.FEE_SCHEDULE,
  CONFIG_AUTO: SERVICE.CONFIG_AUTO,
  BG_LOGIN_APP: SERVICE.BG_LOGIN_APP,
  CUSTOMER_SUPPORT: SERVICE.CUSTOMER_SUPPORT,
  PREMIUM_ACCOUNT_STRUCTURE: SERVICE.PREMIUM_ACCOUNT_STRUCTURE,
  NUMBER_GROUP: SERVICE.NUMBER_GROUP,
  PREMIUM_ACCOUNT_NUMBER: SERVICE.PREMIUM_ACCOUNT_NUMBER,
  HIDDEN_ACCOUNT: SERVICE.HIDDEN_ACCOUNT,
  SPECIAL_ACCOUNT: SERVICE.SPECIAL_ACCOUNT,
  MONITOR_LOG: SERVICE.MONITOR_LOG,
  TRANSACTION_QR_PAY: SERVICE.TRANSACTION_QR_PAY,
  SERVICE_PACK: SERVICE.SERVICE_PACK,
  CLIENT: SERVICE.CLIENT,
  SMS_BALANCE: SERVICE.SMS_BALANCE,
  CURRENCY: SERVICE.CURRENCY,
  NOTIFICATION_HISTORY: SERVICE.SMS_BALANCE,
  DOTP: SERVICE.DOTP,
};
export const RESOURCE_CONST = {
  COMMON: {
    MODEL: {},
    DOMAIN_NAME: (domain: string) => domain,
    API: {
      SEARCH: '/search',
      SEARCH_BY_KEYWORD: '/search-by-keyword',
      SEARCH_AUTO_COMPLETE: '/auto-complete',
      FIND_ALL: '/find-all',
      CREATE: '/create',
      DETAIL: '/detail',
      UPDATE: '/update',
      DELETE: '/delete',
      ACTIVE: '/active',
      INACTIVE: '/inactive',
      LOCK: '/lock',
      UNLOCK: '/unlock',
      HIDE: '/hide',
      UNHIDE: '/unhide',
      INFO: '/info',
    },
  },
  USER: {
    MODEL: User,
    DOMAIN_NAME: DOMAIN_CONST.USER,
    API: {},
  },
  RESOURCE_FILE: {
    MODEL: {},
    DOMAIN_NAME: DOMAIN_CONST.RESOURCE_FILE,
    API: {
      VIEW_IMG: '/image',
      VIEW_DOCUMENT: '/document',
      DELETE_PRODUCT_FILE: '/delete-product-file',
      DOWNLOAD_PRODUCT_DOCUMENT_FILE: '/product-document',
    },
  },
  COLLABORATOR: {
    MODEL: User,
    DOMAIN_NAME: DOMAIN_CONST.COLLABORATOR,
    API: {},
  },
  DASHBOARD: {
    MODEL: Object,
    DOMAIN_NAME: DOMAIN_CONST.DASHBOARD,
    API: {
      STATISTICAL: '/statistical',
      STATISTICAL_PRODUCT: '/product-statistical',
      STATISTICAL_BY_AREA: '/statistical-by-area',
    },
  },
  LOAN_ONLINE: {
    MODEL: LoanOnline,
    DOMAIN_NAME: DOMAIN_CONST.LOAN_ONLINE,
    API: {
      SEARCH_BY_KEYWORD: '/search-by-keyword',
      EXPORT: `/export`,
    },
  },
  REFERRAL: {
    MODEL: Referral,
    DOMAIN_NAME: DOMAIN_CONST.REFERRAL,
    API: {
      SEARCH: '/search',
      SEARCH_AUTO_COMPLETE: '/auto-complete',
      FIND_ALL: '/find-all',
      CREATE: '/create',
      DETAIL: (domainId: string) => `/${domainId}/detail`,
      UPDATE: (domainId: string) => `/${domainId}/update`,
      DELETE: (domainId: string) => `/${domainId}/delete`,
      ACTIVE: (domainId: string) => `/${domainId}/active`,
      INACTIVE: (domainId: string) => `/${domainId}/inactive`,
    },
  },
  SAVING: {
    MODEL: Saving,
    DOMAIN_NAME: DOMAIN_CONST.SAVING,
    API: {
      SEARCH: '/search',
      EXPORT: '/export',
      TRANSACTION: '/transaction',
    },
  },
  RATE: {
    MODEL: Rate,
    DOMAIN_NAME: DOMAIN_CONST.RATE,
    API: {
      SEARCH: '/search',
      CREATE: '/create',
      UPDATE: '/update',
      DELETE: '/delete',
    },
  },
  FEE_SCHEDULE: {
    MODEL: FeeSchedule,
    DOMAIN_NAME: DOMAIN_CONST.FEE_SCHEDULE,
    API: {
      SEARCH: '/search',
      CREATE: '/create',
      UPDATE: '/update',
      DELETE: '/delete',
    },
  },
  CONFIGURING: {
    MODEL: ConfigSaving,
    DOMAIN_NAME: DOMAIN_CONST.CONFIGURING,
    API: {
      SEARCH: '/search',
      CREATE: '/create',
      UPDATE: '/update',
      DELETE: '/delete',
    },
  },
  NEWS: {
    MODEL: News,
    DOMAIN_NAME: DOMAIN_CONST.NEWS,
    API: {
      SEARCH: '/search',
      CREATE: '/create',
      UPDATE: '/update',
      DELETE: '/delete',
    },
  },
  CONFIG_AUTO: {
    MODEL: ConfigAuto,
    DOMAIN_NAME: DOMAIN_CONST.CONFIG_AUTO,
    API: {
      CREATE: '/create',
      UPDATE: '/update',
      DELETE: '/info',
    },
  },
  BG_LOGIN_APP: {
    MODEL: BgLogin,
    DOMAIN_NAME: DOMAIN_CONST.BG_LOGIN_APP,
    API: {
      UPDATE: '/update',
      DELETE: '/info',
    },
  },
  CUSTOMER_SUPPORT: {
    MODEL: CustomerSupport,
    DOMAIN_NAME: DOMAIN_CONST.CUSTOMER_SUPPORT,
    API: {
      CREATE: '/create',
      UPDATE: '/update',
      DETAIL: '/detail',
    },
  },
  PREMIUM_ACCOUNT_STRUCTURE: {
    MODEL: NumberStructure,
    DOMAIN_NAME: DOMAIN_CONST.PREMIUM_ACCOUNT_STRUCTURE,
    API: {
      UPDATE: '/update',
      DELETE: '/info',
      EXPORT: '/export',
    },
  },
  SPECIAL_ACCOUNT: {
    MODEL: NumberStructure,
    DOMAIN_NAME: DOMAIN_CONST.SPECIAL_ACCOUNT,
    API: {
      UPDATE: '/update',
      CREATE: '/create',
    },
  },
  HIDDEN_ACCOUNT: {
    MODEL: NumberStructure,
    DOMAIN_NAME: DOMAIN_CONST.HIDDEN_ACCOUNT,
    API: {
      UPDATE: '/update',
      CREATE: '/create',
    },
  },
  MONITOR_LOG: {
    MODEL: MonitorLog,
    DOMAIN_NAME: DOMAIN_CONST.MONITOR_LOG,
    API: {
      SEARCH: '/search',
    },
  },
  TRANSACTION_QR_PAY: {
    MODEL: TransactionQrPay,
    DOMAIN_NAME: DOMAIN_CONST.TRANSACTION_QR_PAY,
    API: {
      SEARCH: '/search',
      EXPORT: '/export',
      TRANSACTION: '/transaction',
    },
  },
  SERVICE_PACK: {
    MODEL: ServicePack,
    DOMAIN_NAME: DOMAIN_CONST.SERVICE_PACK,
    API: {
      SEARCH: '/search',
      EXPORT: '/export',
      SERVICE_PACK_TYPE: '/search-type-service-pack',
    },
  },
  CLIENT: {
    MODEL: Client,
    DOMAIN_NAME: DOMAIN_CONST.CLIENT,
    API: {
      SEARCH: '/search',
      EXPORT: '/export',
    },
  },
  SMS_BALANCE: {
    MODEL: Client,
    DOMAIN_NAME: DOMAIN_CONST.SMS_BALANCE,
    API: {
      SEARCH: '/search',
      EXPORT: '/export',
    },
  },
  PREMIUM_ACCOUNT_NUMBER: {
    MODEL: PremiumAccNumber,
    DOMAIN_NAME: DOMAIN_CONST.PREMIUM_ACCOUNT_NUMBER,
    API: {
      SEARCH_SOLD: '/search-sold-acc-number',
      SEARCH_ACC_NUMBER: '/search-acc-number',
      SEARCH_ACC_NUMBER_CUSTOMER: '/search-acc-number-customer',
      EXPORT_SOLD: '/export-sold-acc-number',
      NOTIFICATION: '/notification-sold-account-number',
      EXPORT_PREMIUM_ACC_NUMBER: '/export-acc-number',
      CHECK_AVAIABLE: '/check-available',
      REQUEST_OTP: '/request-otp',
      CONFIRM_OTP: '/confirm-otp',
    },
  },
  SPECIAL_PREMIUM_ACCOUNT_NUMBER: {
    MODEL: SpecialPremiumAccountNumber,
    DOMAIN_NAME: DOMAIN_CONST.SPECIAL_ACCOUNT,
    API: {
      EXPORT_SPECIAL_ACCOUNT: '/export-special-account',
    },
  },
  NUMBER_GROUP: {
    MODEL: NumberGroup,
    DOMAIN_NAME: DOMAIN_CONST.NUMBER_GROUP,
    API: {
      SEARCH: '/search',
      EXPORT: '/export',
      SERVICE_PACK_TYPE: '/search-type-service-pack',
      INFO: '/info',
    },
  },
  CURRENCY: {
    MODEL: Currency,
    DOMAIN_NAME: DOMAIN_CONST.CURRENCY,
    API: {
      SEARCH: '/search',
      EXPORT: '/export',
      SERVICE_PACK_TYPE: '/search-type-service-pack',
      INFO: '/info',
    },
  },
  NOTIFICATION_HISTORY: {
    MODEL: NotificationHistoryTransaction,
    DOMAIN_NAME: DOMAIN_CONST.NOTIFICATION_HISTORY,
    API: {
      SEARCH: '/search',
      CONTRACT: '/contacted',
      NOT_CONTRACT: '/not-contacted',
      EXPORT: '/export',
    },
  },
  DOTP: {
    MODEL: Dotp,
    DOMAIN_NAME: DOMAIN_CONST.DOTP,
    API: {
      SEARCH: '/search',
      EXPORT: '/export',
      DELETE: '/cancel',
    },
  },
};
