import { Injectable } from '@angular/core';
import { ICategory } from '@shared/models/category.model';
import { Observable } from 'rxjs/internal/Observable';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class CategoryService extends AbstractService {
  public resourceUrl = '/category';
  category(): Observable<EntityResponseType<ICategory>> {
    return super.post(`${this.resourceUrl}/all`);
  }
}
