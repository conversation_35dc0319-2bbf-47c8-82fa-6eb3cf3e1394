import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  CONFIGURATION_FEE_TYPE_RADIO,
  MODAL_ACTION,
} from '@shared/constants/app.constants';
import { MODEL_MAP_ITEM_COMMON } from '@shared/models/model.type';
import { CustomerService } from '@shared/services/customer.service';

@Component({
  selector: 'app-configuration-fee-type-popup',
  templateUrl: './configuration-fee-type-popup.component.html',
  styleUrls: ['./configuration-fee-type-popup.component.scss'],
})
export class ConfigurationFeeTypePopupComponent implements OnInit {
  @Output() data: EventEmitter<any> = new EventEmitter();

  CONFIGURATION_FEE_TYPE_RADIO = CONFIGURATION_FEE_TYPE_RADIO;

  public item = '';

  public title = 'common.action.confirm';

  public action!: MODEL_MAP_ITEM_COMMON;

  MODAL_ACTION = MODAL_ACTION;

  formFeeType = this.fb.group({
    configurationFeeType: CONFIGURATION_FEE_TYPE_RADIO[0].value,
  });

  constructor(
    public activeModal: NgbActiveModal,
    public translateService: TranslateService,
    public customerService: CustomerService,
    private fb: FormBuilder
  ) {}

  ngOnInit(): void {}
}
