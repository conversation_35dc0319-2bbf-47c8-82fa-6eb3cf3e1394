import { Pipe, PipeTransform } from '@angular/core';

/**
 * Display VND
 *
 * <AUTHOR>
 * @date 2021-12-24
 * @export
 * @class CurrencyLakPipe
 * @implements {PipeTransform}
 * @howToUse
 * ```
 *     <some-element>{{ product?.price | currencyLak }}</some-element>
 * ```
 */
@Pipe({ name: 'currencyLak' })
export class CurrencyLakPipe implements PipeTransform {
  symbol = '';

  transform(value?: any): string {
    if (value) {
      if (this.isDecimal(value)) {
        value = value.toFixed(2);
      }
      return (
        value?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.') + this.symbol
      );
    }
    if (value === 0) {
      return value;
    }
    return '';
  }

  isDecimal(num: number) {
    return +num !== Math.floor(num);
  }
}
