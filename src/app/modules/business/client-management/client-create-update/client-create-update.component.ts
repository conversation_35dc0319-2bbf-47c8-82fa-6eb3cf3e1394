import { Component, HostListener, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { ActivatedRoute, Router } from '@angular/router';
import {
  CLIENT_TYPE,
  CLIENT_TYPE_CONST,
  CLIENT_TYPE_MAP,
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  FILE_UPLOAD_EXTENSIONS,
  MODAL_ACTION,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IClient, IServicePack } from '@shared/models/service-pack.model';
import { AuthenticationService } from '@shared/services/auth/authentication.service';
import { ClientService } from '@shared/services/client.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ServicePackService } from '@shared/services/service-pack.service';
import { encrypt, ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-client-create-update',
  templateUrl: './client-create-update.component.html',
  styleUrls: ['./client-create-update.component.scss'],
})
export class ClientCreateUpdateComponent implements OnInit, OnDestroy {
  form: FormGroup = new FormGroup({});

  // input call api
  client: IClient = {};
  reader: FileReader = new FileReader();
  action = '';
  hasFilter = false;
  maxDateOfBirth = new Date();
  servicePackTypes: IServicePack[] = [];

  ROUTER_UTILS = ROUTER_UTILS;
  ENTITY_STATUS = ENTITY_STATUS;
  VALIDATORS = VALIDATORS;
  FILE_UPLOAD_EXTENSIONS = FILE_UPLOAD_EXTENSIONS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  CLIENT_TYPE_CONST = CLIENT_TYPE_CONST;
  CLIENT_TYPE_MAP = CLIENT_TYPE_MAP;
  CLIENT_TYPE = CLIENT_TYPE;
  pageSizeOptions = PAGINATION.OPTIONS;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;

  formServicePackSearch = this.formBuilder.group({
    keyword: '',
    length: 0,
    clientIds: [],
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
  });

  constructor(
    private formBuilder: FormBuilder,
    private clientService: ClientService,
    private toastService: ToastrCustomService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private authenticationService: AuthenticationService,
    private servicePackService: ServicePackService,
    private modalService: ModalService,
  ) {
    this.activatedRoute.data.subscribe((res) => {
      this.action = res.action;
    });
    // get id form paramater
    this.activatedRoute.paramMap.subscribe((res) => {
      const idParam = res.get('id');
      if (idParam) {
        this.client.clientId = idParam;
      }
    });
  }

  /**
   * remove storage
   */
  ngOnDestroy(): void {
    if (!this.hasFilter) {
      sessionStorage.removeItem(STORAGE_APP.USER);
    }
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.hasFilter = true;
  }

  ngOnInit(): void {
    // this.getServicePackTypes();
    this.initForm();
    if (this.action === ROUTER_ACTIONS.create) {
      this.initForm();
    } else {
      this.getDetail();
    }
  }

  /**
   * init form
   *
   * @param user User
   */
  initForm(client?: IClient): void {
    this.form = this.formBuilder.group({
      clientId: [
        {
          value: client?.clientId || '',
          disabled: this.action !== ROUTER_ACTIONS.create,
        },
        [Validators.required, Validators.pattern(VALIDATORS.PATTERN.USERNAME)],
      ],
      password: [
        {
          value: '',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [Validators.required, Validators.pattern(VALIDATORS.PATTERN.PASSWORD)],
      ],
      name: [
        {
          value: client?.name || '',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.NO_SPECIAL_CHARACTERS),
        ],
      ],
      type: [
        {
          value: client?.type || null,
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [Validators.required],
      ],
      status: [
        {
          value: client?.status || 0,
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
      ],
    });

    if (this.action !== ROUTER_ACTIONS.create) {
      this.validateForm();
    }
  }

  validateForm(): void {
    this.form.get('password')?.setValidators([]);
    this.form.get('password')?.updateValueAndValidity();
  }

  /**
   * back to monitor list
   */
  backToList(): void {
    this.hasFilter = true;
    this.router.navigate([ROUTER_UTILS.client.root]);
  }

  /**
   * get detail user
   */
  getDetail(): void {
    if (this.client?.clientId) {
      this.clientService.detail(this.client).subscribe((res: any) => {
        this.client = res.body;
        const data = res.body || undefined;
        this.initForm(data);
        this.onSearchServicePackSubmit();
      });
    }
  }

  /**
   * Create: check validate file and data input if valid then call api create
   */
  onCreate(): void {
    const { password } = this.form.value;

    // touched form
    if (this.form.invalid) {
      CommonUtils.markFormGroupTouched(this.form);
    }
    const data = this.form.getRawValue();
    data.password = encrypt(password) + '';
    if (this.form.valid) {
      this.clientService.create({ ...data }).subscribe((res): void => {
        this.router.navigate([ROUTER_UTILS.client.root]);
        this.toastService.success('common.action.createSuccess');
      });
    }
  }

  /**
   * Update: check validate file and data input if valid then call api create
   */
  onUpdate(): void {
    // touched form
    if (this.form.invalid) {
      CommonUtils.markFormGroupTouched(this.form);
    }
    const data = this.form.getRawValue();
    data.userId = this.client?.clientId;
    if (this.form.valid) {
      this.clientService.update(data).subscribe((res): void => {
        this.router.navigate([ROUTER_UTILS.client.root]);
        this.toastService.success('common.action.updateSuccess');
      });
    }
  }

  onSearchServicePackSubmit() {
    this.formServicePackSearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.formServicePackSearch.controls.length.setValue(
      PAGINATION.LENGTH_DEFAULT
    );
    this.onSearchServicePack();
  }

  onSearchServicePack(): void {
    if (
      this.authenticationService.hasAnyPrivileges(
        SYSTEM_RULES.CLIENT_READ
      )
    ) {
      const body = this.formServicePackSearch.value;
      body.clientIds = [this.client.clientId];
      const params = { ...body, keyword: body.keyword };
      if (this.formServicePackSearch.invalid) {
        CommonUtils.markFormGroupTouched(this.formServicePackSearch);
      } else {
        this.servicePackService
          .search(params)
          .subscribe((res: any): void => {
            this.servicePackTypes = res.body.content;
            this.formServicePackSearch.controls.length.setValue(
              res.body.totalElements
            );
          });
      }
    }
  }

  /**
   * index on list
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formServicePackSearch.value.pageIndex,
      this.formServicePackSearch.value.pageSize
    );
  }

  /**
   * Change page: next page and preview page
   *
   * @param page PageEvent
   */
  onChangePage(page: PageEvent) {
    this.formServicePackSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formServicePackSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearchServicePack();
  }

  /**
   * check lock and unlock and call api
   *
   * @param customer ICustomer
   */
  lockAndUnlock(servicePack?: IServicePack): void {
    if (servicePack) {
      if (servicePack.status === ENTITY_STATUS_CONST.INACTIVE.code) {
        this.unLock(servicePack);
      } else {
        this.lock(servicePack);
      }
    }
  }

  /**
   * button click edit
   *
   * @param userId number
   */
  edit(id?: number): void {
    this.router.navigate([
      ROUTER_UTILS.servicePack.root,
      id,
      ROUTER_ACTIONS.update,
    ]);
  }

  /**
   * Lock user register
   *
   * @param user IUser
   */
  private lock(servicePack?: IServicePack) {
    const modalData = {
      title: 'servicePack.lock',
      content: 'servicePack.lockContent',
      interpolateParams: { name: `<b>${servicePack?.name || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { servicePackId: servicePack?.servicePackId };
        this.servicePackService.lock(params).subscribe((res) => {
          this.toastService.success('common.action.lockSuccess');
          this.onSearchServicePack();
        });
      }
    });
  }

  /**
   * UnLock user register
   *
   * @param user: IUser
   */
  private unLock(servicePack?: IServicePack) {
    const modalData = {
      title: 'servicePack.unlock',
      content: 'servicePack.unlockContent',
      interpolateParams: { name: `<b>${servicePack?.name || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { servicePackId: servicePack?.servicePackId };
        this.servicePackService.unlock(params).subscribe((res) => {
          this.toastService.success('common.action.unlockSuccess');
          this.onSearchServicePack();
        });
      }
    });
  }

  /**
   * delete
   *
   * @param user IUser
   */
  delete(servicePack?: IServicePack): void {
    // open modal
    const modalData = {
      title: 'servicePack.delete',
      content: 'servicePack.deleteContent',
      interpolateParams: { name: `<b>${servicePack?.name || ''}</b>` },
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { servicePackId: servicePack?.servicePackId };
        this.servicePackService.delete(params).subscribe((res: any) => {
          this.toastService.success('common.action.deleteSuccess');
          this.onSearchServicePack();
        });
      }
    });
  }

  detail(id?: number) {
    this.router.navigate([
      ROUTER_UTILS.servicePack.root,
      id,
      ROUTER_ACTIONS.detail,
    ]);
  }

  onReset() {
    this.formServicePackSearch.controls.keyword.reset();
  }

  onEdit() {
    this.router.navigate([
      ROUTER_UTILS.client.root,
      this.client.clientId,
      ROUTER_ACTIONS.update,
    ]);
  }
}
