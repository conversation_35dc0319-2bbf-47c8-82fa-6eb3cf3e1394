export type MODEL_MAP_ITEM_COMMON = {
  label: string;
  code: string | number;
  style?: string;
};

export type MODEL_MAP_COMMON = {
  [name: string]: MODEL_MAP_ITEM_COMMON;
};

export interface ModelDialog {
  title?: string;
  content?: string;
  action?: MODEL_MAP_ITEM_COMMON;
  interpolateParams?: any;
  dialogSize?: 'sm' | 'md' | 'lg' | 'xl' | 'xxl' | string;
  component?: any;
  dataComponent?: any;
}

export enum ScreenType {
  CREATE,
  UPDATE,
  DETAIL,
}

export enum ModalResult {
  OK = 'ok',
}

export enum ReferralType {
  CUSTOMER,
  COLLABORATORS,
  STAFF,
}
