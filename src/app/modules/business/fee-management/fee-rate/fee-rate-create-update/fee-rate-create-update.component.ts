import { Component, Input, OnInit } from '@angular/core';
import {
  <PERSON><PERSON><PERSON>er,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  CONFIGURATION_FEE_TYPE_CONST,
  CURRENCY_CONST,
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  MAX_DATE_CONST,
  MAX_MIN_VALUE_CONST,
  MODAL_ACTION,
  MOMENT_CONST,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ICurrency } from '@shared/models/currency.model';
import { IFeeRate } from '@shared/models/IFeeRate.model';
import { MODEL_MAP_ITEM_COMMON } from '@shared/models/model.type';
import { C<PERSON>rencyService } from '@shared/services/currency.service';
import { FeeRateService } from '@shared/services/fee-rate.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import CommonUtils from '@shared/utils/common-utils';
import { ROUTER_ACTIONS } from '@shared/utils/router.utils';
import * as moment from 'moment';

@Component({
  selector: 'app-fee-rate-create-update',
  templateUrl: './fee-rate-create-update.component.html',
  styleUrls: ['./fee-rate-create-update.component.scss'],
})
export class FeeRateCreateUpdateComponent implements OnInit {
  @Input() feeId = 0;

  @Input() configurationFeeType = '';

  @Input() transactionFeeTypeId = 0;

  feeRateForm: FormGroup = new FormGroup({});
  feeRate: IFeeRate = {};
  currencies: ICurrency[] = [];

  public actionConfirm!: MODEL_MAP_ITEM_COMMON;

  action = '';
  feeRateId: any;

  VALIDATORS = VALIDATORS;
  CONFIGURATION_FEE_TYPE = CONFIGURATION_FEE_TYPE_CONST;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  ENTITY_STATUS = ENTITY_STATUS;
  SYSTEM_RULES = SYSTEM_RULES;
  MAX_DATE_CONST = MAX_DATE_CONST;
  MAX_MIN_VALUE_CONST = MAX_MIN_VALUE_CONST;

  public title = 'common.action.confirm';
  public content = '';
  public interpolateParams: object = {};
  public isHiddenBtnClose = false;

  MODAL_ACTION = MODAL_ACTION;

  isErrorAmountMinMustSmallerAmountMax = false;
  isErrorAmountFeeMustSmallerAmountMax = false;
  isErrorDiscountPercent = false;

  initForm(iFeeRate?: IFeeRate): void {
    this.feeRateForm = this.fb.group({
      feeRateId: iFeeRate?.feeRateId,
      feeId: this.feeId,
      feeRateName: [
        iFeeRate?.feeRateName || '',
        [
          Validators.required,
          Validators.minLength(VALIDATORS.LENGTH.FEE_RATE_NAME_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
        ],
      ],
      feeAmount: [
        CommonUtils.formatWithThousandSeparator(iFeeRate?.feeAmount || 0),
        [
          // Validators.minLength(VALIDATORS.LENGTH.TRANSACTION_AMOUNT_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.TRANSACTION_AMOUNT_MAX_LENGTH),
        ],
      ],
      vat: [CommonUtils.formatWithThousandSeparator(iFeeRate?.vat || 0)],
      transactionAmountMin: [
        CommonUtils.formatWithThousandSeparator(
          iFeeRate?.transactionAmountMin || 0
        ),
        [
          Validators.required,
          Validators.maxLength(VALIDATORS.LENGTH.TRANSACTION_AMOUNT_MAX_LENGTH),
        ],
      ],
      transactionAmountMax: [
        CommonUtils.formatWithThousandSeparator(
          iFeeRate?.transactionAmountMax || 0
        ),
        [
          Validators.required,
          Validators.maxLength(VALIDATORS.LENGTH.TRANSACTION_AMOUNT_MAX_LENGTH),
          Validators.min(VALIDATORS.LENGTH.TRANSACTION_AMOUNT_MIN),
        ],
      ],
      discountPercent: [
        CommonUtils.formatWithThousandSeparator(
          iFeeRate?.discountPercent || null
        ),
      ],
      discountFixed: [
        CommonUtils.formatWithThousandSeparator(
          iFeeRate?.discountFixed || null
        ),
      ],
      effectiveAt: [
        iFeeRate?.effectiveAt
          ? this.getDateString(
              moment(
                iFeeRate?.effectiveAt,
                MOMENT_CONST.TIMEZONE_FORMAT
              ).format(MOMENT_CONST.LOCAL_DATE_TIME_FORMAT)
            )
          : '',
        [Validators.required],
      ],
      expiredAt: [
        iFeeRate?.expiredAt
          ? this.getDateString(
              moment(iFeeRate?.expiredAt, MOMENT_CONST.TIMEZONE_FORMAT).format(
                MOMENT_CONST.LOCAL_DATE_TIME_FORMAT
              )
            )
          : '',
        [Validators.required],
      ],
      status: ENTITY_STATUS_CONST.ACTIVE.code,
      moneyMinusAccountMin: ['0'],
      moneyMinusAccountMax: ['0'],
      currency: [iFeeRate?.currency || null, [Validators.required]],
    });
    if (this.action === ROUTER_ACTIONS.detail) {
      this.feeRateForm.disable();
    }
    this.procesAccountAmount('');
  }

  constructor(
    public activeModal: NgbActiveModal,
    public translateService: TranslateService,
    private fb: FormBuilder,
    private translate: TranslateService,
    private toastService: ToastrCustomService,
    private feeRateService: FeeRateService,
    private currencyService: CurrencyService
  ) {}

  ngOnInit(): void {
    this.getCurrency();
    if (this.action === ROUTER_ACTIONS.create) {
      this.initForm();
    } else {
      this.initForm(this.feeRate);
    }
  }

  isValidMaxDate = (control: FormControl) => {
    return { invalidMaxDate: true };
  };

  changeValidDate() {
    if (
      this.feeRateForm.controls.effectiveAt.value &&
      this.feeRateForm.controls.expiredAt.value
    ) {
      const effectiveAt = new Date(
        this.feeRateForm.controls.effectiveAt.value
      ).getTime();
      const expiredAt = new Date(
        this.feeRateForm.controls.expiredAt.value
      ).getTime();
      if (effectiveAt > expiredAt) {
        this.feeRateForm.controls.effectiveAt.clearValidators();
        this.feeRateForm.controls.effectiveAt.setValidators([
          Validators.required,
          this.isValidMaxDate,
        ]);
        this.feeRateForm.controls.effectiveAt.updateValueAndValidity();
      } else {
        this.feeRateForm.controls.effectiveAt.clearValidators();
        this.feeRateForm.controls.effectiveAt.setValidators([
          Validators.required,
        ]);
        this.feeRateForm.controls.effectiveAt.updateValueAndValidity();
      }
    } else {
      this.feeRateForm.controls.effectiveAt.clearValidators();
      this.feeRateForm.controls.effectiveAt.setValidators([
        Validators.required,
      ]);
      this.feeRateForm.controls.effectiveAt.updateValueAndValidity();
    }
  }

  /**
   * displayContent: display content in file i18n
   *
   * @param content string
   * @returns string
   */
  displayContent(content: string): string {
    return this.translate.instant(content);
  }

  onCreate(): void {
    if (this.feeRateForm.invalid) {
      CommonUtils.markFormGroupTouched(this.feeRateForm);
      return;
    }

    const params = this.feeRateForm.getRawValue();
    // validate
    // this.validateFeeRate(params);

    params.transactionAmountMin = CommonUtils.parseNumberFromFormattedString(
      params.transactionAmountMin
    );
    params.transactionAmountMax = CommonUtils.parseNumberFromFormattedString(
      params.transactionAmountMax
    );
    params.moneyMinusAccountMin = CommonUtils.parseNumberFromFormattedString(
      params.moneyMinusAccountMin
    );
    params.moneyMinusAccountMax = CommonUtils.parseNumberFromFormattedString(
      params.moneyMinusAccountMax
    );
    params.feeAmount = CommonUtils.parseNumberFromFormattedString(
      params.feeAmount
    );
    params.discountPercent = CommonUtils.parseNumberFromFormattedString(
      params.discountPercent
    );
    params.discountFixed = CommonUtils.parseNumberFromFormattedString(
      params.discountFixed
    );
    params.vat = CommonUtils.parseNumberFromFormattedString(params.vat);
    params.effectiveAt = moment(new Date(params.effectiveAt)).format(
      MOMENT_CONST.LOCAL_DATE_TIME_FORMAT
    );
    params.expiredAt = moment(new Date(params.expiredAt)).format(
      MOMENT_CONST.LOCAL_DATE_TIME_FORMAT
    );

    if (this.feeRateForm.controls.currency.value === CURRENCY_CONST.LAK.value) {
      params.transactionAmountMin = Math.ceil(params.transactionAmountMin);
      params.transactionAmountMax = Math.ceil(params.transactionAmountMax);
      params.feeAmount = Math.ceil(params.feeAmount);
    }

    if (
      this.feeRateForm.valid &&
      !this.isErrorAmountMinMustSmallerAmountMax &&
      !this.isErrorAmountFeeMustSmallerAmountMax &&
      !this.isErrorDiscountPercent &&
      params.vat >= MAX_MIN_VALUE_CONST.MIN &&
      params.vat <= MAX_MIN_VALUE_CONST.MAX
    ) {
      this.feeRateService.create(params).subscribe((res: any) => {
        this.toastService.success('common.action.createSuccess');
        this.activeModal.close(this.actionConfirm.code);
      });
    }
  }

  /**
   * validate fee rate
   *
   */
  validateFeeRate() {
    const transactionAmountMin = CommonUtils.parseNumberFromFormattedString(
      this.feeRateForm.get('transactionAmountMin')?.value
    );
    const transactionAmountMax = CommonUtils.parseNumberFromFormattedString(
      this.feeRateForm.get('transactionAmountMax')?.value
    );
    const feeAmount = CommonUtils.parseNumberFromFormattedString(
      this.feeRateForm.get('feeAmount')?.value
    );
    const vat = CommonUtils.parseNumberFromFormattedString(
      this.feeRateForm.get('vat')?.value
    );

    const discountPercent = CommonUtils.parseNumberFromFormattedString(
      this.feeRateForm.get('discountPercent')?.value
    );

    if (
      transactionAmountMin &&
      transactionAmountMax &&
      +transactionAmountMin >= +transactionAmountMax
    ) {
      this.isErrorAmountMinMustSmallerAmountMax = true;
      return;
    } else {
      this.isErrorAmountMinMustSmallerAmountMax = false;
    }

    if (this.configurationFeeType === this.CONFIGURATION_FEE_TYPE.FEE.code) {
      if (vat >= MAX_MIN_VALUE_CONST.MIN && vat <= MAX_MIN_VALUE_CONST.MAX) {
        if (
          +feeAmount + (+feeAmount * vat) / MAX_MIN_VALUE_CONST.MAX >
          +transactionAmountMax
        ) {
          this.isErrorAmountFeeMustSmallerAmountMax = true;
          return;
        } else {
          this.isErrorAmountFeeMustSmallerAmountMax = false;
          return;
        }
      }
    } else if (!discountPercent || discountPercent < 1) {
      this.isErrorDiscountPercent = true;
    } else {
      this.isErrorDiscountPercent = false;
    }
  }

  procesAccountAmount(event: any) {
    const transactionAmountMin = CommonUtils.parseNumberFromFormattedString(
      this.feeRateForm.get('transactionAmountMin')?.value
    );
    const transactionAmountMax = CommonUtils.parseNumberFromFormattedString(
      this.feeRateForm.get('transactionAmountMax')?.value
    );
    const vat = CommonUtils.parseNumberFromFormattedString(
      this.feeRateForm.get('vat')?.value
    );
    const feeAmount = CommonUtils.parseNumberFromFormattedString(
      this.feeRateForm.get('feeAmount')?.value || 0
    );
    const discountPercent = CommonUtils.parseNumberFromFormattedString(
      this.feeRateForm.get('discountPercent')?.value || 0
    );
    const discountFixed = CommonUtils.parseNumberFromFormattedString(
      this.feeRateForm.get('discountFixed')?.value || 0
    );

    if (this.configurationFeeType === this.CONFIGURATION_FEE_TYPE.FEE.code) {
      if (feeAmount) {
        if (vat >= MAX_MIN_VALUE_CONST.MIN && vat <= MAX_MIN_VALUE_CONST.MAX) {
          if (transactionAmountMin) {
            const moneyMinusAccountMin =
              +transactionAmountMin +
              +feeAmount +
              (+feeAmount * vat) / MAX_MIN_VALUE_CONST.MAX;
            this.feeRateForm.controls.moneyMinusAccountMin.setValue(
              CommonUtils.formatWithThousandSeparator(
                moneyMinusAccountMin.toFixed(2)
              )
            );
          } else {
            const moneyMinusAccountMin =
              +feeAmount + (+feeAmount * vat) / MAX_MIN_VALUE_CONST.MAX;
            this.feeRateForm.controls.moneyMinusAccountMin.setValue(
              CommonUtils.formatWithThousandSeparator(
                moneyMinusAccountMin.toFixed(2)
              )
            );
          }
          if (transactionAmountMax) {
            const moneyMinusAccountMax =
              +transactionAmountMax +
              +feeAmount +
              (+feeAmount * vat) / MAX_MIN_VALUE_CONST.MAX;
            this.feeRateForm.controls.moneyMinusAccountMax.setValue(
              CommonUtils.formatWithThousandSeparator(
                moneyMinusAccountMax.toFixed(2)
              )
            );
          }
        } else {
          if (transactionAmountMin) {
            const moneyMinusAccountMin = +transactionAmountMin + +feeAmount;
            this.feeRateForm.controls.moneyMinusAccountMin.setValue(
              CommonUtils.formatWithThousandSeparator(
                moneyMinusAccountMin.toFixed(2)
              )
            );
          }
          if (transactionAmountMax) {
            const moneyMinusAccountMax = +transactionAmountMax + +feeAmount;
            this.feeRateForm.controls.moneyMinusAccountMax.setValue(
              CommonUtils.formatWithThousandSeparator(
                moneyMinusAccountMax.toFixed(2)
              )
            );
          }
        }
      } else {
        this.feeRateForm.controls.moneyMinusAccountMin.setValue(
          CommonUtils.formatWithThousandSeparator(transactionAmountMin || 0)
        );
        this.feeRateForm.controls.moneyMinusAccountMax.setValue(
          CommonUtils.formatWithThousandSeparator(transactionAmountMax || 0)
        );
      }
    }

    if (
      this.configurationFeeType === this.CONFIGURATION_FEE_TYPE.DISCOUNT.code
    ) {
      if (
        discountPercent >= MAX_MIN_VALUE_CONST.MIN &&
        discountPercent <= MAX_MIN_VALUE_CONST.MAX
      ) {
        if (transactionAmountMin) {
          const moneyMinusAccountMin =
            +transactionAmountMin -
            +transactionAmountMin *
              (discountPercent / MAX_MIN_VALUE_CONST.MAX) -
            discountFixed;
          this.feeRateForm.controls.moneyMinusAccountMin.setValue(
            CommonUtils.formatWithThousandSeparator(
              moneyMinusAccountMin.toFixed(2)
            )
          );
        } else {
          const moneyMinusAccountMin =
            discountFixed > 0 ? -discountFixed.toFixed(2) : 0;
          this.feeRateForm.controls.moneyMinusAccountMin.setValue(
            CommonUtils.formatWithThousandSeparator(moneyMinusAccountMin)
          );
        }
        if (transactionAmountMax) {
          const moneyMinusAccountMax =
            +transactionAmountMax -
            +transactionAmountMax *
              (discountPercent / MAX_MIN_VALUE_CONST.MAX) -
            discountFixed;
          this.feeRateForm.controls.moneyMinusAccountMax.setValue(
            CommonUtils.formatWithThousandSeparator(
              moneyMinusAccountMax.toFixed(2)
            )
          );
        } else {
          const moneyMinusAccountMax =
            discountFixed > 0 ? -discountFixed.toFixed(2) : 0;
          this.feeRateForm.controls.moneyMinusAccountMax.setValue(
            CommonUtils.formatWithThousandSeparator(moneyMinusAccountMax)
          );
        }
      } else {
        this.feeRateForm.controls.moneyMinusAccountMin.setValue(
          // transactionAmountMin || 0
          MAX_MIN_VALUE_CONST.MIN
        );
        this.feeRateForm.controls.moneyMinusAccountMax.setValue(
          // transactionAmountMax || 0
          MAX_MIN_VALUE_CONST.MIN
        );
      }
    }
  }

  onUpdate(): void {
    if (this.feeRateForm.invalid) {
      CommonUtils.markFormGroupTouched(this.feeRateForm);
    }
    const params = this.feeRateForm.getRawValue();

    params.transactionAmountMin = CommonUtils.parseNumberFromFormattedString(
      params.transactionAmountMin
    );
    params.transactionAmountMax = CommonUtils.parseNumberFromFormattedString(
      params.transactionAmountMax
    );
    params.moneyMinusAccountMin = CommonUtils.parseNumberFromFormattedString(
      params.moneyMinusAccountMin
    );
    params.moneyMinusAccountMax = CommonUtils.parseNumberFromFormattedString(
      params.moneyMinusAccountMax
    );
    params.feeAmount = CommonUtils.parseNumberFromFormattedString(
      params.feeAmount
    );
    params.discountPercent = CommonUtils.parseNumberFromFormattedString(
      params.discountPercent
    );
    params.discountFixed = CommonUtils.parseNumberFromFormattedString(
      params.discountFixed
    );

    params.vat = CommonUtils.parseNumberFromFormattedString(params.vat);

    // this.validateFeeRate(params);
    params.effectiveAt = moment(new Date(params.effectiveAt)).format(
      MOMENT_CONST.LOCAL_DATE_TIME_FORMAT
    );

    params.expiredAt = moment(new Date(params.expiredAt)).format(
      MOMENT_CONST.LOCAL_DATE_TIME_FORMAT
    );

    if (this.feeRateForm.controls.currency.value === CURRENCY_CONST.LAK.value) {
      params.transactionAmountMin = Math.ceil(params.transactionAmountMin);
      params.transactionAmountMax = Math.ceil(params.transactionAmountMax);
      params.feeAmount = Math.ceil(params.feeAmount);
    }

    if (
      this.feeRateForm.valid &&
      !this.isErrorAmountMinMustSmallerAmountMax &&
      !this.isErrorAmountFeeMustSmallerAmountMax &&
      !this.isErrorDiscountPercent &&
      params.vat >= MAX_MIN_VALUE_CONST.MIN &&
      params.vat <= MAX_MIN_VALUE_CONST.MAX
    ) {
      params.feeId = this.feeRate.feeId;
      this.feeRateService.update(params).subscribe((res: any) => {
        this.toastService.success('common.action.updateSuccess');
        this.activeModal.close(this.actionConfirm.code);
      });
    }
  }

  getDateString(dateTime: string) {
    const [dateValues, timeValues] = dateTime.split(' ');
    const [day, month, year] = dateValues.split('-');
    const [hours, minutes, seconds] = timeValues.split(':');

    return moment(
      new Date(+year, +month - 1, +day, +hours, +minutes, +seconds)
    ).format(MOMENT_CONST.DATE_TIME_MOMENT_FORMAT);
  }

  getCurrency() {
    this.currencyService
      .search({ hasPageable: false, status: +ENTITY_STATUS_CONST.ACTIVE.code })
      .subscribe((res: any) => {
        if (res.body.content.length) {
          this.currencies = res.body.content;
        }
      });
  }
}
