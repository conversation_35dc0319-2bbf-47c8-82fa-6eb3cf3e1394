import {
  AfterViewChecked,
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import * as ClassicEditor from '@shared/ckeditor5';
import { LANGUAGE_CONST } from '@shared/constants/app.constants';
import { MAX_LENGTH_EDITOR_DEFAULT } from '@shared/constants/ckeditor.constant';
import { LANGUAGES } from '@shared/constants/language.constants';
import { STORAGE_LANGUAGES } from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import CommonUtils from '@shared/utils/common-utils';
import { LocalStorageService } from 'ngx-webstorage';

@Component({
  selector: 'mb-editor',
  templateUrl: './editor.component.html',
  styleUrls: ['editor.component.scss'],
})
export class EditorComponent
  implements OnInit, AfterViewInit, AfterViewChecked
{
  constructor(private localStorage: LocalStorageService) {}

  public Editor = ClassicEditor;
  public editor: any;
  public idEditor = EditorComponent.generateId();

  isLoadCKeditor = false;

  @Input() placeholder = 'model.event.content';
  @Input() maxLength = MAX_LENGTH_EDITOR_DEFAULT;
  @Input() value = '';
  @Input() isReadOnly = false;

  @Output() data: EventEmitter<any> = new EventEmitter();
  @Output() HTMLdata: EventEmitter<any> = new EventEmitter();
  @Output() inputInvalid: EventEmitter<any> = new EventEmitter();

  private static generateId(): string {
    return (
      'editor' + Math.ceil(Math.random() * 100) + Math.ceil(Math.random() * 100)
    );
  }

  ngOnInit(): void {}

  async ngAfterViewInit(): Promise<void> {
    await this.afterView();
  }

  setValue(content?: string) {
    content ? this.editor?.setData(content) : this.editor?.setData('');
  }

  async ngAfterViewChecked(): Promise<void> {
    // this.editor.setData('<p>Some text.</p>');
  }

  async afterView(): Promise<void> {
    const currentLanguage =
      this.localStorage.retrieve(STORAGE_LANGUAGES) || LANGUAGES.VI.code;
    setTimeout(async () => {
      if (document.querySelector(`#${this.idEditor}`) && !this.isLoadCKeditor) {
        this.isLoadCKeditor = true;
        ClassicEditor.builtinPlugins.map((plugin: any) => {});
        await ClassicEditor.create(
          document.querySelector(`#${this.idEditor}`),
          {
            toolbar: {
              items: [
                'heading',
                'bold',
                'italic',
                'underline',
                'alignment',
                'fontFamily',
                'fontColor',
                'fontSize',
                'highlight',
                'link',
                'strikethrough',
                'bulletedList',
                'numberedList',
                'blockQuote',
                'outdent',
                'indent',
                'code',
                'insertTable',
                // "paragraph",
                // 'insertImage',
              ],
            },
            image: {
              // toolbar: ['imageStyle:full', 'imageStyle:side'],
              toolbar: [
                // 'imageTextAlternative',
                '|',
                'imageStyle:full',
                'imageStyle:alignLeft',
                'imageStyle:alignCenter',
                'imageStyle:alignRight',
              ],
              styles: ['full', 'alignLeft', 'alignCenter', 'alignRight'],
            },
            placeholder: this.placeholder,
            extraPlugins: [MyCustomUploadAdapterPlugin],
            fontFamily: {
              options: [
                'Font Laos',
                'Font MB',
                'Arial, Helvetica, sans-serif',
                'Courier New, Courier, monospace',
                'Georgia, serif',
                'Lucida Sans Unicode, Lucida Grande, sans-serif',
                'Tahoma, Geneva, sans-serif',
                'Times New Roman, Times, serif',
                'Trebuchet MS, Helvetica, sans-serif',
                'Verdana, Geneva, sans-serif',
              ],
              supportAllValues: true,
            },
          }
        )
          .then((editor: any) => {
            this.editor = editor;
            editor.isReadOnly = this.isReadOnly;
            if (currentLanguage === LANGUAGE_CONST.LO.code) {
              editor.execute( 'fontFamily', { value: 'Font Laos' } );
            } else if (currentLanguage === LANGUAGE_CONST.EN.code) {
              editor.execute( 'fontFamily', { value: 'Font MB' } );
            } else {
              editor.execute( 'fontFamily', { value: 'Font MB' } );
            }
            editor.model.document.on('change:data', (evt: any, data: any) => {
              this.data.emit(
                editor.getData().replace(VALIDATORS.PATTERN.ESCAPE_HTML, '')
              );

              this.HTMLdata.emit(editor.getData());
            });

          })
          .catch((error: any) => {});
      }

      function MyCustomUploadAdapterPlugin(editor: any): void {
        editor.plugins.get('FileRepository').createUploadAdapter = (
          loader: any
        ) => {
          // Configure the URL to the upload script in your back-end here!
          return new MyUploadAdapter(loader);
        };

        class MyUploadAdapter {
          loader: any;
          xhr = new XMLHttpRequest();

          constructor(loader: any) {
            // The file loader instance to use during the upload.
            this.loader = loader;
          }

          upload() {
            return this.loader.file.then(
              (file: any) =>
                new Promise((resolve, reject) => {
                  this._initRequest();
                  this._initListeners(resolve, reject, file);
                  this._sendRequest(file);
                })
            );
          }

          abort() {
            if (this.xhr) {
              this.xhr.abort();
            }
          }

          _initRequest() {
            const xhr = (this.xhr = new XMLHttpRequest());
            // xhr.open('POST', API_STORAGE + '/file/...', true);
            xhr.responseType = 'json';
          }

          _initListeners(resolve: any, reject: any, file: any) {
            const xhr = this.xhr;
            const loader = this.loader;
            const genericErrorText = `Couldn't upload file: ${file.name}.`;

            xhr.addEventListener('error', () => reject(genericErrorText));
            xhr.addEventListener('abort', () => reject());
            xhr.addEventListener('load', () => {
              const response = xhr.response;
              if (!response || response.error) {
                return reject(
                  response && response.error
                    ? response.error.message
                    : genericErrorText
                );
              }
              resolve({
                default: response.url,
              });
            });
            if (xhr.upload) {
              xhr.upload.addEventListener('progress', (evt) => {
                if (evt.lengthComputable) {
                  loader.uploadTotal = evt.total;
                  loader.uploaded = evt.loaded;
                }
              });
            }
          }

          _sendRequest(file: any) {
            // Prepare the form data.
            const data = new FormData();

            data.append('file', file);
            this.xhr.send(data);
          }
        }
      }
    }, 300);
  }

  getLengthStripHTML(value: string): number {
    return CommonUtils.stripHTML(value).length;
  }

  getClass() {
    if (this.getLengthStripHTML(this.value) > this.maxLength) {
      this.inputInvalid.emit(true);
      return 'text-danger';
    } else {
      this.inputInvalid.emit(false);
      return '';
    }
  }
}
