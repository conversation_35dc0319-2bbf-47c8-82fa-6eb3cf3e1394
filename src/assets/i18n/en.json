{"common": {"unit": "LAK", "title": "MB Laos", "spinningTip": "System is working ...", "success": "Success", "fail": "Fail", "info": "Information", "warning": "Warning", "used": "Active", "deleted": "Deleted", "stop": "Inactive", "error": "Error", "blank": "", "unread": "Unread", "read": "Read", "percent": "%", "notification": "Notice", "nation": "Nation", "notificationContent": "Notification content", "note": "Note", "day": "Day", "month": "Month", "year": "Year", "number": "numbers", "minute": "Minute", "status": "Status", "accountNumber": "Account number", "activeStatus": "Activation status", "required": "The field cannot be empty", "active": "Active", "revert": "<PERSON><PERSON>", "inactive": "Inactive", "sold": "Sold", "suspended": "Suspended", "failed": "Failed", "processing": "Processing", "timeout": "Time out", "closed": "Closed", "pattern": "Invalid format", "hello": "Hello, ", "vi": "Vietnamese", "en": "English", "uploadFile": "Upload", "lo": "Laos", "uploadText": "Click or drag the file into this area to upload", "uploadHint": "Supports single or multiple file uploads.", "maxFileSize": "The file size must not exceed {{size}} MB.", "fileAccept": "You can only upload files {{type}}!", "fileTypeError": "The file is not in the correct format", "maxUploadFile": "Upload {{count}} files", "attachedFiles": "Attached Files", "chooseFile": "Select file", "maxSizeFile": "XLSX or XLS file size must not exceed 10MB", "maxSize": "File size must not exceed 10MB", "selectFileXlsx": "Select file or drag and drop here", "building": "building", "floor": "Floor", "company": "Company", "customer": "Customer", "email": "Email", "phoneNumber": "Phone number", "buildingCode": "Court code", "floorCode": "Floor Code", "unitCode": "Plan Code", "image": "Image", "sender": "Sender", "show": "Show", "confirmSave": "Do you want to save this information?", "all": "All", "noDataResult": "No records available", "notBlank": "cannot be blank", "advancedSearch": "Advanced Search", "uploadImage": "Add your photo here", "maxImage": "Select up to 5 images", "male": "Male", "female": "Female", "other": "Other", "identityCard": "Identity card", "passport": "Passport", "no_search_result": "No matching results", "no_search_result_error": "No log, there may be system error, please contact system administrator", "no_list_account_result": "No account list", "no_list_history_active_result": "Account is not activated", "no_list_history_update_result": "No service update history", "no_history_update_result": "No update history", "married": "Married", "unmarried": "Unmarried", "no": "No.", "stt": "No.", "not": "No", "login": "<PERSON><PERSON>", "logout": "Logout", "fileUploadInvalid": "File upload is invalid", "upload": "Upload", "uploading": "Uploading...", "myInfo": "My info", "myProfile": "My profile", "searchEmpty": "Please enter search criteria", "reason": "Reason", "activated": "Activated", "activate": "Activated", "notActivated": "Not activated", "no-rotation": "No ration", "root-rotation": "Root ration", "rotation-of-principal-and-interest": "Rotation of principal and interest", "deposit-more": "Deposit more", "settlement": "Full Settlement", "saving-account": "Saving Account", "settled": "Settled", "fixed": "Fixed", "accumulated": "Accumulated", "createdDate": "Created date", "lastModifiedDate": "Last modified date", "textLanguage": "Language selection", "contacted": "Contacted", "notContacted": "Not yet contacted", "addMore": "See more", "appSelectOption": {"select": "Select", "approvalType": "Select approval type", "approvalStatus": "Select approval status", "status": "Select status", "idCardType": "Select Id card type"}, "action": {"searchNumber": "the search number", "createVersion": "Confirm create", "label": "Action", "save": "Save", "create": "Add", "update": "Update", "delete": "Delete", "import": "Upload from Excel", "export": "Export file", "print": "Print", "print_code": "Print code", "filter": "Filter", "fromDate": "From date", "toDate": "To Date", "confirm": "Confirm", "agree": "Agree", "cancel": "Cancel", "close": "Close", "uploadFile": "Upload file", "detail": "Detail", "send": "Send", "back": "Back", "sync": "Synchronize information", "syncReport": "Synchronize report", "updateSector": "Update sectors", "searchOption": "Select search form", "createInternal": "Create a new internal account", "createLdap": "Create new customer account", "permission": "Decentralization", "outputFile": "Export file", "evaluationDate": "Evaluation Date", "syncDate": "Synchronous date", "link": "Link user accounts", "lock": "Lock", "unlock": "Unlock", "hide": "<PERSON>de", "unhide": "UnHide", "unclosed": "Unclosed", "show": "Show", "download": "Download", "active": "Active", "inactive": "Deactivated", "addRequest": "Create a new request", "confirmDelete": "Are you sure to delete this floor?", "reset": "Reset", "draft": "Draft", "pending": "Pending", "deny": "<PERSON><PERSON>", "approval": "Approval", "approved": "Approved", "sendApproval": "Send Approval", "cancelApproval": "<PERSON>cel <PERSON>", "approvalError": "Approval error", "add": "Add", "sendOtp": "Send otp", "resendCode": "Resend code OTP", "confirmOtp": "ConfirmOtp", "search": "Search", "searchKeyword": "Search keyword", "dismiss": "<PERSON><PERSON><PERSON>", "research": "Research", "researchAgain": "Research again", "createSuccess": "Create successful", "updateSuccess": "Update successful", "sendApprovalSuccess": "Send approval successful", "deleteSuccess": "Delete successful", "notification": "Send notification", "notificationSuccess": "Notification sent successfully", "syncReportSuccess": "Synchronize report successfully", "syncReportFails": "Report sync failed", "syncSuccess": "Customer information {{name}} synced successfully", "syncCustomerSuccess": "Customer information synced successfully", "syncFails": "Syncing customer information {{name}} failed", "lockSuccess": "Lock successful", "unlockSuccess": "Unlock successful", "hideSuccess": "Hide successful", "unhideSuccess": "Unhide successful", "requestSuccess": "Request sent successfully", "activateSuccess": "Account activation successful", "updateSectorSuccess": "Customer sector {{name}} updated successfully", "updateSectorFails": "Update of customer sector {{name}} failed", "next": "Next", "changePassword": "Change password", "changePasswordSuccess": "Change Password Successful", "download-template": "Download template file", "uploadSuccess": "Upload file Successful", "selectAll": "Select all", "clearAll": "Clear all", "backup": "Make a copy", "resendLinkSuccess": "Send account activation link successful", "addBanner": "Add banner", "deleteBanner": "Delete banner", "downloadTemplate": "Download template file", "uploadFiles": "Upload File", "auto": "Auto", "manual": "Manual", "contactedSuccess": "Change the status to successfully contacted", "notContactedSuccess": "Successfully changed the status to not yet contacted", "perform": "Perform"}, "confirm": {"reset": "Confirm reset", "resetContent": "Data will be lost, are you sure you want to reset it?", "exitScreen": "Confirm exit from current screen", "exitScreenContent": "Data will be lost, are you sure you want to exit?"}, "placeholder": {"gender": "Select gender", "idCardTypeId": "Select Id card type", "residentStatus": "Select residency status", "maritalStatus": "Select marital status", "select": "Select {{params}}", "input": "Enter {{params}}"}}, "appError": {"msg": "Error", "formInvalid": "Form invalid", "email": "Email invalidate", "phoneNumber": "Phone number invalidate", "login": "Authentication failed, please try again", "editorMaxLength": "The length of {{fieldName}} must be less than {{length}} characters", "required": "{{fieldName}} is required", "minlength": "{{fieldName}} must be greater than {{param}} characters", "maxlength": "{{fieldName}} must be less than {{param}} characters", "pattern": "{{fieldName}} Invalid pattern", "min": "{{fieldName}} must be greater than {{param}}", "max": "{{fieldName}} must be less than {{param}}", "warningChoiceFee": "Please select tenant information!", "auth": "You do not have permission to access this screen", "minPrice": "The minium price must be less than 99.999.999.999.999", "maxPrice": "The maximum price must be less than 99.999.999.999.999", "listPrice": "List price must be less than 99.999.999.999.999"}, "public": {"resend": "Resend link active", "resendLink": "Resend Link", "titleResend": "<p class=\"title\">Activation link is already <bold class=\"text-primary-red\" style=\"color: red\" >expired</bold> please click 'Resend link' to get new account activation link. </p>", "titleResendPw": "<p class=\"title\">Activation link is already <bold class=\"text-primary-red\" style=\"color: red\" >expired</bold> please click 'Resend link' to get activation link. </p>", "titleResendSuccess": "Resend link for account activation successful \n Please check your email.", "titleActive": "You have successful activated your account, please check the information sent to your Email.", "forgotPassword": {"title": "Forgot password", "titleChangePassword": "Change password", "footerChangePassword": "<i>New password contains 8 to 20 characters</i><p><i>Include uppercase, lowercase letters, numbers and special characters</i></p>", "confirmPassword": "Confirm new password", "password": "new Password", "email": "Email", "placeholder": {"email": "Enter email in correct format @mbbank.com.vn"}, "success": {"title": "Please check the information that has been sent to the Email. Link expire in", "titleChangePassword": "You have successful changed your password \n Please login again with new password"}, "error": {"titleChangePassword": "An error occurred during execution. Please try again", "titleExpireSetupPw": "Password expired", "contentExpireSetupPw": "The link has expired, please click confirm to retry.", "notMatch": "Confirm Password does not match", "required": {"email": "Email cannot be empty", "password": "Password cannot be empty", "confirmPassword": "Confirm password cannot be empty"}, "pattern": {"email": "Email is malformed", "password": "Password is malformed", "confirmPassword": "Confirm password is malformed"}, "minLength": {"password": "Password at least {{param}} characters", "confirmPassword": "Confirm password at least {{param}} characters"}, "maxLength": {"email": "Email has at most {{param}} characters", "password": "Password has at most {{param}} characters", "confirmPassword": "Confirm password has at most {{param}} characters"}}}}, "login": {"loginButton": "<PERSON><PERSON>", "userName": "Username", "password": "Password", "captcha": "Verification code", "error": {"userNameEmpty": "Username cannot be empty", "userNameMaxLength": "Username cannot be longer than 75 characters", "userNameMinLength": "<PERSON><PERSON><PERSON> at least 3 characters", "passwordEmpty": "Password cannot be empty", "passwordInvalid": "Invalid password", "captchaEmpty": "Verification code cannot be empty"}}, "sidebar": {"manageSaving": {"root": "Savings management", "detail": "Savings account details", "tab": {"detailSaving": "Savings account details", "detailTransaciton": "Transaction history  "}}, "configSaving": {"root": "List of savings terms"}, "news": {"root": "News management"}, "feeSchedule": {"root": "Fee Schedule management"}, "manageTransQuantity": {"root": "Manage transaction quantity"}, "managePayment": {"root": "Manage household payment services"}, "debitAccount": {"root": "Manage Umoney collection and payment", "debit": "Manage Umoney specialized payment account", "historyTransfer": "Manage Umoney collection and payment transaction history", "report": "Manage Umoney Lapnet report"}, "structureAccount": {"root": "Lucky number structure"}, "premiumAccountNumberInterest": {"root": "Lucky account number", "create": "Open a lucky account number"}, "managementServiceFeeFailed": {"root": "Service Fee Management Failed", "title": "SMS Balance Change Service Fee Management Failed"}, "premiumAccountNumberHasPaymentAccount": {"root": "Lucky account number has payment account", "create": "Open lucky account number already has a payment account."}, "premiumAccountNumber": {"root": "Lucky number management", "title": "Lucky account number", "sold": "Lucky number sold", "search": "Look up lucky number"}, "specialAccountNumber": {"root": "Special lucky number"}, "smsManage": {"root": "Manage SMS reports"}, "configTransaction": {"root": "Manage transaction limit configuration"}, "insuranceManage": {"root": "Insurance management"}, "versionManage": {"root": "Manage Version", "sub-tab": {"android": {"root": "Android"}, "ios": {"root": "IOS"}}}, "dashboard": "Home", "sysManage": {"root": "System Management", "setting": "System Configuration", "backgroundLogin": "Manage login wallpaper", "customerSupport": "Manage customer support information", "role": {"root": "Role Management", "create": "Create new role", "update": "Update role", "detail": "Role details"}, "errorCode": {"root": "Error Code Management", "create": "Create a new error code", "update": "Update error code", "detail": "Error code details"}}, "fee": {"root": "Configuring transaction fees", "fee-rate": "Configuring transaction fee schedule"}, "smsBalance": {"root": "Manage on/off sms service balance fluctuations", "list": "List of SMS service balance fluctuations"}, "department": {"root": "Department management", "create": "Create a department", "update": "Update a department", "detail": "Department detail"}, "informationTemplate": {"root": "Support information management"}, "customer": {"root": "Customer Management", "registration": {"root": "Manage subscriptions", "register": "Register a customer", "update": "Update customer information", "detail": "Customer details"}, "approve": {"root": "Manage approvals", "manage": "Manage customer information approval", "view": "Customer information approval"}, "accountNoTransaction": {"root": "Management does not generate transactions", "title": "Management does not generate transactions", "view": "Detail"}, "activateAccountManual": {"root": "Manage account activation manually", "title": "Manage account activation manually"}, "dotpManagement": {"root": "DOTP Customer Registration Management"}}, "loanOnline": {"root": "Loan Online Management", "detail": "Loan Online registration detail"}, "bank": {"root": "Beneficiary bank Management", "create": "Create beneficiary bank", "update": "Update beneficiary bank", "detail": "Beneficiary bank detail"}, "user": {"root": "User Management"}, "campaign": {"root": "Campaign management", "create": "Create a campaign", "update": "Update campaign's information", "detail": "Campaign's detail", "addBanner": "Add banner", "campaignInfoTitle": "Campaign information"}, "notification": {"root": "Manage notifications"}, "profile": "Profile information", "position": "Manage position", "merchant": {"root": "Manage merchant", "create": "Create new merchant", "update": "Update merchant", "detail": "merchant details", "infor": "Merchant information", "inforMasterMerchant": "Master Merchant Information", "listChilMerchant": "Child Merchant List", "transactionHistory": {"root": "Manage transaction history", "code": "Transaction code", "name": "Account Name", "transactionDate": "Transaction Date", "transactionOther": "Manage other transactions", "transactionUmoney": "Manage Umoney transactions", "transactionMMoney": "Manage electricity and water payments", "internationalTransaction": "Manage internalational transactions"}, "masterMerchant": {"root": "Manage master merchant", "create": "Create a new master merchant", "update": "Update master merchant", "detail": "Details of master merchant"}}, "lapnet": {"root": "Manage Lapnet Report"}, "referral": {"root": "Manage referral codes"}, "rate": {"root": "Interest rate management"}, "transactionExceedLimit": {"root": "Manage transactions exceeding limits"}, "changePassword": "Change password", "monitorLog": "Transaction Monitor system", "transactionQrPay": "Manage Transaction QRPAY", "numberGroup": "Number Group", "currency": "Currency Management"}, "error": {"manageTransQuantity": {"required": {"fromDate": "Start date cannot be empty", "toDate": "End date cannot be empty"}}, "managePayment": {"required": {"accountNumber": "Account number cannot be empty", "accountName": "Account name cannot be empty"}, "pattern": {"accountNumber": "Account numbers can only be entered in letters and numbers, minimum 4 characters, maximum 20 characters and do not include special characters"}, "length": {"accountNumber": "The account number has a maximum length of 20 characters"}}, "sms": {"dateTime": {"fromDateCurrentInvalid": "From date cannot be greater than the current date", "toDateCurrentInvalid": "To date cannot be greater than current date", "toDateInvalid": "To date must not be less than from date"}}, "configTransaction": {"required": {"servicePackage": "Service cannot be empty", "transType": "Transaction type cannot be empty", "sectorType": "Sector type cannot be empty", "currency": "Currency cannot be empty", "transMoney": "Please enter the full limit/transaction for all types of transactions", "startDate": "Start date cannot be empty", "endDate": "End date cannot be empty", "transDay1890": "The limit/day for sector 1890 cannot be empty", "transMonth1891": "The limit/month for sector 1891 cannot be empty", "transDay1891": "The limit/day for sector 1891 cannot be empty", "reason": "Reason cannot be empty"}, "dateTime": {"startDate": "Start date have to greater than or equal current date", "startDateTime": "Start time have to greater 2 minutes than current time", "endDate": "End date have to greater than start date", "DateEqual": "The start date cannot be the same as the end date"}, "invalidMoney": {"transDay": "Limit/day cannot be zero or blank", "transMonth": "Limit/month cannot be zero or blank", "transYear": "Limit/year cannot be equal to 0", "transMoney": "All limit/transaction does not exceed the daily sector limit", "transMoneyNone": "All limit/transaction cannot be equal to 0 or empty"}, "pattern": {"servicePackage": "Service type names cannot begin with the characters -, +, =, @ or the strings 0x09 and 0x0D"}}, "insuranceManage": {"dateTime": {"invalidFromDateCurrent": "From date cannot be greater than the current date", "invalidToDateCurrent": "To date cannot be greater than the current date", "invalidToDate": "To date must not be less than from date"}, "required": {"date": "Search date cannot be empty", "fromDate": "Start date cannot be empty", "toDate": "End date cannot be empty"}}, "versionManage": {"required": {"versionName": "Version name cannot be empyt", "versionCode": "Version code cannot be empty", "updateType": "Update type cannot be empty, please select atleast one update type", "timeUpdate": "Update time cannot be emmty", "dateUpdate": "Update time cannot be empty", "contentUpdate": "Content update can not be empty", "urlStore": "Url store can not be empty"}, "length": {"versionName": "Version name only up to 10 characters", "versionCode": "Version code only up to 10 characters", "versionContent": "Version content only up tp 500 characters"}, "pattern": {"versionName": "Version name just contain only dot and number character, have pattern: 1.1, 1.2, 2.1.2 ...", "versionCode": "Version code just contain only number", "updateTime": "Update time have to greater or equal than current time", "updateDate": "Update date have to greater or euqal to current date"}, "dateTime": {"invalidTime": "Release time have to greater at least 2 mininutes with current time", "invalidDate": "Release date have to greater current date", "invalidHours": "Release hour have to greater or equal current time", "invalidDateSearch": "From date cannot be  greater than to date", "invalidFromDate": "From date cannot be greater than current date", "invalidToDate": "To date cannot be greater than current date"}}, "label": "Error", "common": "An unknown error has occurred, please contact your administrator.", "formInvalid": "Invalid form data", "email": "Invalid email", "phoneNumber": "Invalid phone number", "cif": "Invalid CIF number", "idCardNumber": "Invalid id card number", "login": "Authentication failed, please try again", "maxDateCurrent": "{{param}} cannot be greater than the current date", "minDateCurrent": "{{param}} cannot be less than current date", "editorMaxLength": "The length of {{fieldName}} must be less than {{length}} characters", "required": {"fromDate": "The start date cannot be empty", "toDate": "The end date cannot be empty", "fromDateExcel": "The start date when exporting excel cannot be empty", "toDateExcel": "The start date when exporting excel cannot be empty", "inValidDate": "Search interval up to 90 days", "inValidDateExcel": "Search interval when exporting excel 90 days"}, "minLength": "minimum length {{param}} characters", "maxLength": "max length {{param}} characters", "pattern": "malformed", "min": " at least {{param}}", "toDateMustGreatherFromDate": "The start date cannot be greater than the end date", "startTimeMustGreatherEndTime": "The effective start time cannot be greater than the effective end time", "max": " the maximum is {{param}}", "role": {"maxLength": {"description": "Description length must be less than {{length}} characters", "name": "Role name length must be less than {{length}} characters"}, "required": {"name": "Role name cannot be empty"}, "pattern": {"name": "The role name cannot contain special characters"}}, "customerCreate": {"required": {"idCardType": "Type of ID card can not be empty", "idCardNumber": "ID number card can not be empty", "phoneNumber": "Phone number can not be empty", "fullname": "Full name can not be empty", "dob": "Date of birth can not be empty", "placeOfOrigin": "Place of birth can not be empty", "gender": "Gender can not be empty", "nationCode": "Nation can not be empty", "issueDate": "Issue date can not be empty", "issuePlace": "Issue place can not be empty", "placeOfResidence": "Place of residence can not be empty", "currentAddress": "Contact address can not be empty", "phoneContact": "Contact phonenumber can not be empty", "customerSectorId": "Customer profile status can not be empty", "staffCode": "Staff code can not be empty", "shopCode": "Shop code can not be empty", "idCardPic": "Id Card pictures can not be empty", "picture": "Photo ID can not be empty"}, "minLength": {"idCardNumber": "Id card number at least {{param}} characters", "phoneNumber": "Phone number at least {{param}} characters", "fullname": "Fullname at least {{param}} characters", "placeOfOrigin": "Place Of birth at {{param}} characters", "statusOfResidence": "Status of residence at least {{param}} ", "nationCode": "Nationality at least {{param}} characters", "nationCodeOther": "Other nationlity at least {{param}} characters", "issuePlace": "Issue place at least {{param}} characters", "placeOfResidence": "Place of residence at least {{param}} characters", "placeOfResidenceOutCountry": "Place of residence abroad at least {{param}} characters", "currentAddress": "Contact address at least {{param}} characters", "phoneContact": "Contact phone number at least {{param}} characters", "email": "Email at least {{param}} characters", "maritalStatus": "Martial status at least {{param}} characters", "job": "Job at least {{param}} characters", "position": "Duty at least {{param}} characters", "staffCode": "Staff code at least {{param}} characters", "shopCode": "Shop code at least {{param}} characters"}, "maxLength": {"idCardNumber": "Id Card number has at most {{param}} characters", "phoneNumber": "Phone number has at most {{param}} characters", "fullname": "Fullname has at most {{param}} characters", "placeOfOrigin": "Place of birth has at most {{param}} characters", "statusOfResidence": "Status of residence has at most {{param}} characters", "nationCode": "Nationality has at most {{param}} characters", "nationCodeOther": "Other nationlity has at most {{param}} characters", "issuePlace": "Issue place has at most {{param}} characters", "placeOfResidence": "Place of residence has at most {{param}} characters", "placeOfResidenceOutCountry": "Place of residence abroad has at most {{param}} characters", "currentAddress": "Contact address has at most {{param}} characters", "phoneContact": "Contact phone number has at most {{param}} characters", "email": "Email has at most {{param}} characters", "maritalStatus": "Martial status has at most {{param}} characters", "job": "Job has at most {{param}} characters", "position": "Duty has at most {{param}} characters", "staffCode": "Staff code has at most {{param}} characters", "shopCode": "Shop code has at most {{param}} characters", "workplace": "Maximum work place {{param}} characters"}, "pattern": {"idCardNumber": "Id card number can not contain special characters", "phoneNumber": "Phone number can only contain number characters, start with 0302, 0304,... or 0202, 0205,...", "fullname": "Fullname is malformed", "dob": "Date of birth is malformed", "nationCode": "Nationality can not contain special characters", "nationCodeOther": "Other nationlity canot contain special characters", "issueDate": "Issue date is malformed", "phoneContact": "Contact phone number can only contain number characters, start with 0302, 0304,... or 0202, 0205,...", "email": "Email is malformed", "staffCode": "Staff code can not contain special characters", "shopCode": "Shop code can not contain special characters", "dateOfBirth": "Customer date of birth is at least 18 years old", "issueDateMin": "The date of issuance of the identification document must not be overdue"}}, "customerDetail": {"required": {"username": "Username can not be empty", "fullname": "Full name can not be empty", "dateOfBirth": "Date of birth can not be empty", "email": "Email of birth can not be empty", "phoneNumber": "Phone number can not be empty", "status": "Status can not be empty", "gender": "Gender can not be empty", "idCardType": "Type of ID card can not be empty", "idCardNumber": "ID number card can not be empty", "issueDate": "Issue date can not be empty", "issuePlace": "Issue place can not be empty", "placeOfOrigin": "Place of origin can not be empty", "placeOfResidence": "Place of residence can not be empty", "nationCode": "Nation code can not be empty", "customerSectorId": "Customer's sector can not be empty", "cif": "Customer's CIF can not be empty", "limitPerTransaction": "Limit per transaction can not be empty", "limitPerDay": "Limit per day can not be empty", "currentAddress": "Current address can not be empty"}, "minLength": {"username": "Username at least {{param}} characters", "fullname": "Fullname at least {{param}} characters", "email": "Email at least {{param}} characters", "phoneNumber": "Phone number at least {{param}} characters", "staffCode": "Staff code at least {{param}} characters", "idCardNumber": "ID card number at least {{param}} characters", "issuePlace": "Issue place at least {{param}} characters", "placeOfOrigin": "Place of origin at least {{param}} characters", "placeOfResidence": "Place of residence at least {{param}} characters", "cif": "CIF at least {{param}} characters", "currentAddress": "Current address at least {{param}} characters"}, "maxLength": {"username": "Username has {{param}} characters at most", "fullname": "Fullname has {{param}} characters at most", "email": "Email has {{param}} characters at most", "phoneNumber": "Phone number has {{param}} characters at most", "staffCode": "Staff code has {{param}} characters at most", "description": "Description has {{param}} characters at most", "idCardNumber": "Id card number has {{param}} characters at most", "issuePlace": "Issue place has {{param}} characters at most", "placeOfOrigin": "Place of origin has {{param}} characters at most", "placeOfResidence": "Place of residence has {{param}} characters at most", "cif": "CIF has {{param}} characters at most", "currentAddress": "Current address has {{param}} characters at most"}, "pattern": {"username": "Username is malformed", "fullname": "Fullname is malformed", "dob": "Date of birth is malformed", "email": "Email is malformed", "phoneNumber": "Phone number can only contain number characters, start with 0302, 0304,... or 0202, 0205,...", "staffCode": "Staff code can only contain number characters, start with 0302, 0304,... or 0202, 0205,...", "idCardNumber": "Id card number can not contain special characters"}}, "bank": {"required": {"bankCodeNumber": "Bank code number can not be empty", "bankCode": "Bank code can not be empty", "order": "Order can not be empty", "files": "Files can not be empty", "status": "Status can not be empty", "bankName": "Bank name can not be empty"}, "maxLength": {"bankCodeNumber": "Bank code number has {{param}} characters at most", "bankCode": "Bank code has {{param}} characters at most", "bankName": "Bank name has {{param}} characters at most", "order": "Bank sort order largest is {{param}}"}, "minLength": {"bankCodeNumber": "Bank code number at least {{param}} characters", "bankCode": "Bank code at least  {{param}} characters", "bankName": "Bank name at least {{param}} characters", "order": "Smallest sort order {{param}}"}, "pattern": {"bankCodeNumber": "Invalid bank code", "bankCode": "Invalid bank code"}}, "campaign": {"required": {"campaignName": "<PERSON>'s name can not be empty", "embedLinkImage": "Please upload the full attachment link and banner", "embedLink": "The attached link cannot be empty when the banner is uploaded", "banners": "Files can not be empty", "startDateFrom": "Start date must not be empty", "endDateTo": "End date must not be empty", "screenShow": "Display position must not be empty", "bannerScreen": "Please upload at least 1 banner for the campaign", "description": "Campaign description cannot be empty", "position": "Required to choose 1 of 2 locations to display the campaign"}, "maxLength": {"campaignName": "<PERSON>'s name has {{param}} characters at most", "description": "Description has {{param}} characters at most", "embedLink": "Embedded link has {{param}} characters at most"}, "pattern": {"campaignName": "Campaign names cannot start with the characters -, +, =, @ or the strings 0x09 and 0x0D", "embedLink": "Attached links must be in the format http://abc., https://abc."}}, "event": {"required": {"title": "Can not leave the title blank", "content": "Content cannot be left blank", "announcementTypeId": "Notification category cannot be empty", "expectedNotificationAt": "Send time cannot be left blank"}, "maxLength": {"title": "Title has {{param}} characters at most", "content": "Content cannot exceed 1500 characters"}}, "user": {"required": {"phoneNumber": "Phone number cannot be empty", "fullname": "Full Name cannot be empty", "dob": "Date of birth cannot be empty", "gender": "Gender cannot be blank", "department": "Department cannot be empty", "position": "Position cannot be empty", "role": "Role cannot be empty", "email": "Email cannot be empty", "currentPassword": "Current password cannot be empty", "newPassword": "New password cannot be empty", "confirmPassword": "Confirm Password cannot be empty", "reason": "Reason cannot be empty", "phoneOrFullName": "Phone number or full name must not be empty"}, "minLength": {"phoneNumber": "Phone number must contain at least {{param}} characters", "fullname": "User name at least {{param}} characters", "email": "Email at least {{param}} characters", "newPassword": "New password at least {{param}} characters", "confirmPassword": "New password confirmation at least {{param}} characters"}, "maxLength": {"phoneNumber": "Phone number with at most {{param}} characters", "fullname": "Full name with at most {{param}} characters", "email": "Email with at most {{param}} characters", "newPassword": "New password with at most {{param}} characters", "confirmPassword": "Confirm new password with at most {{param}} characters", "reason": "Reason with at most {{param}} characters"}, "pattern": {"phoneNumber": "Phone numbers can only contain numeric characters, starting 030 or 020", "fullname": "<PERSON><PERSON><PERSON> full name", "dob": "Invalid date of birth", "email": "Invalid email", "newPassword": "The new password is malformed", "confirmPassword": "Confirm New Password Invalid Format"}}, "position": {"required": {"positionName": "Position name cannot be empty", "shortName": "Short name cannot be empty", "positionCode": "Position code cannot be empty"}, "minLength": {"shortName": "Position name at least {{param}} characters"}, "maxLength": {"positionName": "Position name has at most {{param}} characters", "shortName": "Short name with at most {{param}} characters", "positionCode": "Position code with at most {{param}} characters", "description": "Description has at most {{param}} characters"}, "pattern": {"positionName": "Position name must only include letters, numbers and spaces", "shortName": "Position short name must only include letters, numbers and spaces"}}, "referral": {"required": {"userFullName": "Fullname cannot be empty", "phoneNumber": "Phone number cannot be empty", "type": "Select cannot be empty", "userCode": "Staff code can not be empty", "rmCode": "RM code cannot be empty"}, "minLength": {"phoneNumber": "Phone number at least {{param}} characters", "userCode": "Staff code at least {{param}} characters", "userFullName": "Full name at least {{param}} characters", "rmCode": "RM code at least {{param}} characters"}, "maxLength": {"userFullName": "Fullname has at most {{param}} characters", "phoneNumber": "Phone number has at most {{param}} characters", "type": "Type has at most {{param}} characters", "userCode": "Staff code has at most {{param}} characters", "rmCode": "RM code code has at most {{param}} characters"}, "pattern": {"userFullName": "Fullname name must only include letters, numbers and spaces", "phoneNumber": "Phone number can only contain number characters, start with 0302, 0304,... or 0202, 0205,..."}}, "merchant": {"required": {"merchantName": "Merchant name cannot be empty", "merchantCode": "Merchant Code cannot be empty", "masterMerchantCode": "Master Merchant Code cannot be empty", "masterMerchantName": "Master Merchant Name cannot be empty", "merchantAccountNumber": "Account number cannot be empty", "serviceType": "Service type cannot be empty"}, "minLength": {"merchantCode": "Minimum Merchant Code {{param}} characters", "masterMerchantCode": "Minimum Master Merchant Code {{param}} characters"}, "maxLength": {"merchantCode": "Maximum Merchant Code {{param}} characters", "merchantName": "Maximum Merchant Name {{param}} characters", "description": "Description up to {{param}} characters", "masterMerchantCode": "Maximum Master Merchant Code {{param}} characters", "masterMerchantName": "Maximum Master Merchant Name {{param}} characters", "merchantAccountNumber": "Account not maximum {{param}} characters"}, "pattern": {"merchantCode": "Merchant name must only include letters, numbers and '_' character", "masterMerchantCode": "Merchant name must only include letters, numbers and '_' character", "merchantName": "Merchant name must only include letters, numbers and spaces", "masterMerchantName": "Master Merchant name must only include letters, numbers and spaces", "merchantAccountNumber": "Account number can only enter letters, numbers, minimum 4 characters, maximum 50 characters and do not include special characters"}}, "feeRate": {"required": {"feeRateName": "Fee schedule name cannot be empty", "transactionAmountMin": "Minimum transaction amount cannot be empty", "transactionAmountMax": "Maximum transaction amount cannot be empty", "discountPercent": "% discount cannot be empty and must be greater than 0", "vat": "VAT cannot be left blank", "effectiveAt": "The effective start time cannot be blank", "expiredAt": "The validity period cannot be left blank", "feeAmount": "Fee amount cannot be empty", "transactionAmountMinLength": "The minimum transaction amount should not exceed 999.999.999.999.999", "transactionAmountMaxLength": "The maximum transaction amount cannot exceed 999.999.999.999.999", "amountMinMustSmallerAmountMax": "Minimum transaction amount must be less than maximum transaction amount", "amountFeeMustSmallerAmountMax": "The amount of the fee after VAT must be less than the maximum transaction amount", "vatMinLength": "VAT up to 100%", "vatMin": "VAT minimum 0%", "discountPercentMinLength": "Percent discount must be less than 100"}, "maxLength": {"feeRateName": "Fee schedule name cannot exceed {{param}} characters"}, "min": {"transactionAmountMax": "Transaction amount max must be greater than 0"}}, "fee": {"required": {"feeName": "Fee schedule name cannot be empty", "status": "You have to choose the status", "transactionFeeType": "You must select the service type", "merchantFeeType": "You must select the merchant type"}, "maxLength": {"feeName": "Maximum charge configuration name {{param}} characters"}}}, "customerRegisterManagement": {"createTitle": "Create new customer", "detailTitle": "Customer detail", "updateTitle": "Update customer information", "no": "No.", "cif": "CIF number", "cifNumber": "CIF number", "fullname": "Full name", "username": "Username", "phoneNumber": "Phone number", "email": "Email", "nationCode": "Nation code", "issueDate": "Issue date", "status": "Status", "phoneNumberSearch": "Phone number", "identifyNumber": "Identify Number", "fromDate": "From Date", "toDate": "To Date", "addButton": "Add", "searchButton": "Search", "cancelButton": "Cancel", "deleteButton": "Delete", "lockButton": "Lock", "idCardNumber": "Identify Number", "dateOfBirth": "Date of birth", "lock": "Confirm customer account lock", "unlock": "Confirm customer account unlock", "deleteApproval": "Confirm deletion of browsing request", "cancel": "Confirmation of closing customer account", "unclosed": "Confirm opening customer account", "update": "Confirm customer account update", "create": "Confirm new customer account creation", "lockCustomerRegisterContent": "Are you sure you want to submit a request to lock the customer account {{fullname}}?", "unlockCustomerRegisterContent": "Are you sure you want to submit a request to unlock customer account {{fullname}}?", "deleteCustomerApproval": "Are you sure you want to delete request {{approvalType}} customer account {{fullname}}?", "cancelCustomerRegisterContent": "Are you sure you want to submit a request to close customer account {{fullname}}?", "unclosedCustomerRegisterContent": "Are you sure you want to submit a request to open customer account {{fullname}}?", "createCustomerRegisterContent": "Are you sure you want to submit a request to create a new customer account {{fullname}}?", "updateCustomerRegisterContent": "Are you sure you want to submit a request to update customer account {{fullname}}?", "resetPassword": "Confirm change of customer account password", "resetPasswordCustomerContent": "Are you sure you want to change the password of the customer account {{fullname}}?", "currency": "<PERSON><PERSON><PERSON><PERSON>", "paymentAccount": "Payment account", "overdraftAccount": "Overdraft account", "sector": "Sector", "customerCreate": {"title": "Create a new customer", "info": "Customer Information", "search": "Search", "researchConfirmTitle": "Confirm research again", "researchConfirmContent": "The customer information you enter may not be saved, are you sure you want to research again?", "alertDataNotExisted": "Customer information has not been used", "existedT24Warning": "Customer already has an account on T24 system", "warningPremiumAcc": "<span class=\"text-primary-color\">Note: Customers need to enter OTP SMS when opening a nice number account</span>"}, "customerDetail": {"title": "Customer detail information", "info": "Customer information", "default": "<PERSON><PERSON><PERSON>", "editButton": "Edit", "backButton": "Back"}, "customerApproval": {"approvalTitle": "Approve customer", "detailTitle": "Customer detail", "approvalStatus": "Status", "approvalType": "Classified", "lockApproval": "Confirm customer account to lock approval", "unlockApproval": "Confirm customer account to unlock approval", "deleteApproval": "Confirm approval to delete a customer account", "createApproval": "Confirm approval to create a new customer account", "updateApproval": "Confirm customer account to update approval", "lockDeny": "Confirm customer to lock refuse approval", "unlockDeny": "Confirm customer account to unlock refuse approval", "unclosedDeny": "Confirm customer account to unclosed refuse approval", "deleteDeny": "Confirm customer account to delete refuse approval", "createDeny": "Confirm customer account to create refuse approval", "updateDeny": "Confirm customer account to update refuse approval", "lockApprovalContent": "Are you sure you want to approve the customer key {{fullname}}?", "unlockApprovalContent": "Are you sure you want to approve unlocking customer {{fullname}}?", "deleteApprovalContent": "Are you sure you want to approve the deletion of customer account {{fullname}}?", "createApprovalContent": "Are you sure you want to approve new customer account {{fullname}}?", "updateApprovalContent": "Are you sure you want to approve the update of customer {{fullname}}?", "lockDenyContent": "Are you sure you want to refuse to approve the customer key {{fullname}}?", "unlockDenyContent": "Are you sure you want to refuse approval to unlock customer account {{fullname}}?", "unclosedDenyContent": "Are you sure you want to refuse approval to unclosed customer account {{fullname}}?", "deleteDenyContent": "Are you sure you want to refuse approval to delete customer account {{fullname}}?", "createDenyContent": "Are you sure you want to refuse approval to create new customer account {{fullname}}?", "updateDenyContent": "Are you sure you want to refuse approval to update customer account {{fullname}}?", "success": {"lockApproval": "Approve to lock customer account successful", "unlockApproval": "Approve to unlock customer account successful", "deleteApproval": "Approve to delete customer account successful", "createApproval": "Approve to create customer account successful", "unclosedApproval": "Approve to unclosed customer account successful", "updateApproval": "Approve to update customer account successful", "deny": {"lockDeny": "Refuse request for lock successful", "unlockDeny": "Refuse request for unlock successful", "unclosedDeny": "Refuse request for unclosed successful", "deleteDeny": "Refuse request for delete successful", "createDeny": "Refuse request for registration successful", "updateDeny": "Refuse request for update successful"}}, "request": {"title": "Customer information request approval", "info": "Customer Information", "approvalType": "Approval type"}, "detail": {"customerDetail": "Customer detail", "editInfomation": "Edit infomation", "customerInfomation": "Customer's infomation", "accountType": "Account type", "customerType": "Customer type", "accountsList": "Accounts list", "accountName": "Account name", "accountNumber": "Account number", "imei": "IMEI", "deviceId": "Device ID", "operatingSystem": "Operating system", "performer": "Performer", "approver": "Approver", "requestTime": "Request time", "approvalTime": "Approval time", "first": "First", "last": "Last", "handle": "<PERSON><PERSON>", "status": "Status"}}, "placeholder": {"cifNumber": "Enter cif number", "idCardNumber": "Enter GTTT number", "phoneNumberOtp": "Enter phone number to receive OTP", "email": "Enter email"}}, "role": {"title": "Create role", "titleUpdate": "Update role", "titleDetail": "Detail role information", "deleteRoleContent": "Are you delete role {{roleName}}?", "lockRoleContent": "Are you lock role {{roleName}}?", "unlockRoleContent": "Are you unlock role {{roleName}}?", "infor": "Role information", "createRole": "Save", "deleteTitle": "Delete Role", "backRole": "Roll back", "lock": "Confirm lock Role", "unLock": "Confirm unlock", "role-name-has-existed": "Role name has existed", "status": "Status", "searchButton": "Search", "addRole": "Add"}, "error-code-management": {"errorCode": "Error code", "nameError": "Error name", "status": "Status", "search": "Search", "addError": "Add", "manipulation": "Manipulation", "contentError": "Error content"}, "loanOnline": {"title": "Detail loan amount information", "titleInforBrrower": "Borrower information", "titleInforLoanAmount": "Loan amount information", "loanProduct": "Loan product", "search": "Search", "unit": {"month": "Months", "lak": "LAK", "lak-month": "LAK/Month"}, "delete": "Confirm deletion of loan registration", "deleteLoanOnlineContent": "Are you sure you want to delete customer loan registration {{fullname}}?"}, "referral": {"titleDetail": "Referral code details", "titleInfoDetail": "Referral code information", "lock": "Confirm lock referral", "unlock": "Confirm unlock referral", "delete": "Confirm delete referral", "lockReferralContent": "Are you lock referral {{referralCode}}?", "unlockReferralContent": "Are you lock referral {{referralCode}}?", "deleteReferralContent": "Are you delete referral {{referralCode}}?"}, "bank": {"root": "Beneficiary bank management", "detailTitle": "Detail beneficiary bank", "createTitle": "Create beneficiary bank", "updateTitle": "Update beneficiary bank", "titleInforBank": "Bank information", "titleDetailInforBank": "Bank detail information", "lock": "Confirm lock bank", "unlock": "Confirm unlock bank", "delete": "Confirm delete bank", "create": "Confirm new bank creation", "update": "Confirm bank update", "createBankContent": "Are you sure you want to create a new bank {{bankCode}}??", "updateBankContent": "Are you sure you want to update bank {{bankCode}}??", "lockBankContent": "Are you lock bank {{bankCode}}?", "unlockBankContent": "Are you unlock bank {{bankCode}}?", "deleteBankContent": "Are you delete bank {{bankCode}}?"}, "fee": {"title": "Create new fee configuration", "titleUpdate": "Update configuration fee", "titleDetail": "Fee configuration detail", "keyword": "Keyword", "configurationFeeType": "Configuration fee type", "status": "Status", "fee": "Fee", "discount": "Discount", "active": "Confirm the activation of the fee configuration", "inactive": "Charge configuration key confirmation", "activeFeeContent": "Do you want to enable the fee profile {{feeName}} ?", "inactiveFeeContent": "Do you want to lock the configuration {{feeName}}?", "createFee": "Create cost configuration", "feeInformationTitle": "Transaction fee information", "nameFeeDetail": "Name of transaction fee", "detailFeeRate": "Fee schedule details", "feeTypeDefault": "The money of default", "delete": "Confirm delete fee configuration", "deleteContent": "Are you sure you want to delete the {{feeName}} fee configuration?"}, "feeRate": {"feeRateName": "Name of fee schedule", "transactionAmount": "Transaction amount", "transactionAmountMin": "Transaction amount min", "transactionAmountMax": "Transaction amount max", "amountFee": "Fee amount", "feeAmount": "Fee amount", "VAT": "VAT (%)", "effectiveDate": "Effective date", "expiredDate": "Expiration date", "titleFeeRate": "Fee schedule details", "percentCk": "% transfer", "ckMin": "Minimum deduction amount", "ckMax": "Maximum deduction amount", "createTitle": "Create fee schedule", "updateTitle": "Update fee schedule", "detailTitle": "Fee schedule details", "transactionAmountLak": "Transaction Amount (LAK)", "transactionAmountLakMin": "Max Transaction Amount", "transactionAmountLakMax": "Min Transaction Amount", "from": "From", "to": "To", "percentDiscount": "% discount", "fixedDiscount": "Fixed discount", "moneyMinusAccount": "Amount deducted from bank account (LAK)", "moneyMinusAccountMin": "Minimum amount deducted from bank account (LAK)", "moneyMinusAccountMax": "Maximum amount deducted from bank account (LAK)", "effectTime": "Valid time of day", "startEffectTime": "Start time effective", "endEffectTime": "End time effective", "delete": "Clear fee schedule", "deleteContent": "Are you sure you want to remove the {{feeRateName}} fee schedule?", "active": "Active Fee Rate", "inactive": "Inactive Fee Rate"}, "event": {"sent": "<PERSON><PERSON>", "draft": "Draft", "waiting": "Waiting", "cancel": "Cancel", "receiver": "Receiver", "deleteEvent": "Confirm delete notification", "deleteContent": "Are you sure you want to delete the notification {{title}}? ", "createTitle": "Create Notification", "backUpTitle": "Make a copy of the notification", "detailTitle": "Detail Notification", "information": "Information of notification", "receiverList": "Receiver list", "addCustomerTitle": "Add customer", "usernameOrPhoneNumber": "Username or phonumber", "delete": "Delete Notification", "numberOfReceiver": "Number of receiver", "numberOfCustomer": "Number of customer", "downloadFileEvent": "Download file", "addNew": "Add new", "deleteReceiverContent": "Are you sure you want to delete recipient {{username}}?", "deleteReceiver": "Delete recipient", "sender": "Sender"}, "user": {"root": "User management", "createTitle": "Create User", "updateTitle": "Update User", "detailTitle": "Detail user", "titleInforUser": "User information", "lock": "Confirm lock user", "unlock": "Confirm unlock user", "delete": "Confirm delete user", "lockUserContent": "Are you lock user {{fullname}}?", "unlockUserContent": "Are you unlock user {{fullname}}?", "deleteUserContent": "Are you delete user {{fullname}}?", "resend": "Confirm resend link active user", "resetPassword": "Confirm reset password user", "resendUserContent": "Are you resend link active user {{fullname}}?", "resetPasswordUserContent": "Are you reset password user {{fullname}}?"}, "position": {"root": "Manage position", "createTitle": "Create position", "updateTitle": "Update position", "titleInforPosition": "Detail information position", "lock": "Confirm lock position", "unlock": "Confirm unlock position", "delete": "Confirm delete position", "lockPositionContent": "Are you sure you want to lock the position {{positionName}}?", "unlockPositionContent": "Are you sure you want to unlock the position {{positionName}}?", "deletePositionContent": "Are you sure you want to delete the position {{positionName}}?"}, "payment": {"lock": "Confirm lock this payment service", "unlock": "Confirm unlock this payment service", "delete": "Confirm delete this payment service", "lockPaymentContent": "Are you sure you can lock this payment service?", "unlockPaymentContent": "Are you sure you want to unlock this payment service?", "deletePaymentContent": "Are you sure you want to delete this payment service?", "auto": "Confirm automatic mode change", "manual": "Confirm manual mode switch", "autoContent": "Are you sure you want to switch your entire account to automatic mode?", "manualContent": "Are you sure you want to switch your entire account to manual mode?", "labelToggle": "Implementation mechanism"}, "merchant": {"root": "Manage Merchant", "createTitle": "Create new Merchant", "updateTitle": "Update Merchant", "titleInforMerchant": "Merchant details", "lock": "Confirm lock Merchant", "unlock": "Confirm unlock Merchant", "delete": "Confirm delete Merchant", "merchantCodeSync": "Merchant code sync", "merchantCodeSyncSuccess": "Merchant code sync successful", "lockMerchantContent": "Are you sure you want to lock Merchant {{merchantName}}?", "unlockMerchantContent": "Are you sure you want to unlock Merchant {{merchantName}}?", "deleteMerchantContent": "Are you sure you want to delete Merchant {{merchantName}}?", "totalAmount": "Total Payments", "totalQuantity": "Total Transactions", "masterMerchant": {"root": "Manage Master Merchant ", "createTitle": "Create a new Master Merchant", "updateTitle": "Update Master Merchant", "titleInforMerchant": "Master Merchant details", "lock": "Confirm lock Master Merchant", "unlock": "Confirm unlock Master Merchant", "delete": "Confirm delete Master Merchant", "lockMerchantContent": "Are you sure you want to lock Master Merchant {{merchantName}}?", "unlockMerchantContent": "Are you sure you want to unlock Master Merchant {{merchantName}}?", "deleteMerchantContent": "Are you sure you want to delete Master Merchant {{merchantName}}?"}}, "matPaginator": {"itemsPerPage": "Items per page", "of": "of", "firstPage": "First Page", "lastPage": "Last Page", "nextPage": "Next Page", "previousPage": "Previous Page"}, "model": {"manageSaving": {"month": "Months", "unit": "LAK", "ratePercent": "%/year", "money": "Deposits", "fixed": "Fixed savings", "accumulated": "Accumulated savings", "no_term": "Unlimited savings", "tabDetailSaving": "Savings account details", "tabDetailTrans": "Transaction history", "manageSavingTitle": "LIST OF SAVINGS ACCOUNTS", "keyword": "Keyword", "savingType": "Saving type", "status": "Status", "fromDate": "From date", "toDate": "To date", "no": "No.", "accountNumber": "Account number", "accountOwner": "Account owner", "cif": "CIF number", "gttt": "Identification number", "phoneNumber": "Phone number", "startTime": "Start time", "savingInfo": {"savingDetail": {"startTime": "Account opening time", "settlementDueTime": "Payment due date", "rate": "Interest rate", "frequencyPayment": "Frequency of interest payments", "rateCurrent": "Interest rate as of current date", "expectedRate": "Expected interest rate until settlement date", "moneyDueDate": "Total amount up to expiration date", "dueDateType": "Due form"}, "transactionHistory": {"no": "STT", "accountSource": "Source account", "transactionTime": "Transaction time", "transactionCode": "Trasaction code", "money": "Amount of money", "type": "Form", "content": "Content"}}}, "nationCode": {"VN": "Vietnam", "KH": "Cambodia", "LA": "Laos", "TH": "Thailand", "CN": "China"}, "news": {"no": "NO", "title": "Title", "content": "Content", "status": "Status", "categories": "Categories", "newsInternal": "Internal news", "newsInter": "News", "create": "Create new news", "update": "Update news", "detail": "News details", "information": "News information", "lock": "Confirm news key", "unlock": "Confirm unlock news", "lockContent": "Are you sure you want to block this news?", "unlockContent": "Are you sure you want to unlock this news?", "required": {"categories": "Category cannot be left blank"}, "modal": {"title": "Notification", "content": "You have unsaved changes. Do you want to continue switching to another language?", "delete": "Confirm deletion of news", "deleteNewsContent": "Are you sure you want to delete this news ?"}, "error": {"content": "Maximum content of 5000 characters"}}, "manageTransQuantity": {"keyword": "Keyword", "transQuantityGreater": "Largest number of transactions", "transQuantityLess": "Smallest number of transactions", "transQuantity": "Number of transactions", "fromDate": "From date", "toDate": "To date", "no": "No.", "userName": "Customer name", "cif": "Customer's code (CIF)", "transCreditSuccess": "Successful credit transactions count", "transDebitSuccess": "Successful debit transactions count", "transactionType": "Transaction type", "credit": "Credit", "debit": "Debit"}, "rate": {"title": "Title", "no": "No", "image": "Image", "createdDate": "Start time", "status": "Status", "lock": "Confirm interest rate lock", "lockContent": "Are you sure you want to lock the interest rate {{title}} ?", "unlock": "Confirm interest unlock", "unlockContent": "Are you sure you want to unlock interest {{title}} ?", "create": "Create a new interest rate", "detail": "Interest rate details", "update": "Update interest rates", "information": "Interest rate information", "modal": {"delete": "Confirm interest deletion", "deleteRateContent": "Are you sure you want to delete this interest ?"}}, "specialAccountNumber": {"list": "List of special lucky number", "createTitle": "Special lucky numbers create", "updateTitle": "Special lucky numbers update", "detailTitle": "Special lucky numbers detail", "numberType": "Number type", "hiddenNumber": "Hidden number", "lock": "Confirm hidden account number", "lockContent": "You want to hide account number {{title}} ?", "unlock": "Confirm opening hidden account number", "unlockContent": "You want to open hidden account number {{title}}", "delete": "Confirm account number deletion", "deleteConfig": "Do you want to remove account number {{title}} from the special numbers list?", "error": {"accountNumber": "Account number cannot be left blank", "listPrice": "List price cannot be blank", "discount": "Discount cannot be left blank", "maxLength": {"listPrice": "Invalid list price"}}, "pattern": {"accountNumber": "Account number must contain only numeric characters, be 4,5,9,10 in length and phone number"}}, "premiumAccountNumber": {"list": "List of lucky account number", "openDate": "openDate", "series": "Series", "seriesInput": "Enter search string", "avoidNumber": "Avoid numbers", "pending": "Pending", "otpExpried": "OTP code expires after:", "register": "Open a lucky account number", "recalled": "Revoked", "unKnown": "Unknown", "balance": "Account balance", "info": "Information on opening a lucky account number", "notification": "Confirm sending notification", "charge": "Lucky account number fee", "notificationContent": "Send notification to customer {{name}} owner of account number {{accountNumber}}, to remind about payment deadline for opening a nice account number transaction?", "accountType": {"phoneNumber": "Phone number", "interest": "Interest"}}, "structureAccount": {"titleStructure": "Structural information", "group4": "Number group 4", "group5": "Number group 5", "group6": "Number group 6", "group8": "Number group 8", "group9": "Number group 9", "name": "Structure name", "structureTitle": "Structure list", "numberStructure": "Structure", "listPrice": "List price (LAK)", "paymentPrice": "Payment price (LAK)", "numberGroup": "Number group", "originalPrice": "List Price", "minPrice": "<PERSON>", "uploadFile": "Import File", "maxPrice": "Max Price", "length": "Length", "price": "Price", "formatPattern": "Structural pattern", "totalPrice": "Total price", "discount": "Discount %", "btnHidden": "List of hidden numbers", "btnSpecial": "List of special numbers", "special": "Special number", "title": "Update number structure", "info": "Information", "lock": "Confirm structure lock", "lockContent": "Do you want to lock the {{title}} structure to {{length}} <b>character</b>?", "unlock": "Confirm structure unlock", "unlockContent": "Do you want to unlock the {{title}} structure of length {{length}} <b>character</b>?", "content": "Description", "addPremiumAccNumber": "Add number", "account": "Enter a series of numbers", "delete": "Confirm structure deletion", "deleteConfig": "Do you want to delete this {{title}} structure of length {{length}} <b>character</b>?", "addAccount": "Add", "titleConfirm": "Confirm deletion", "contentConfirm": "Do you want to delete this number?", "messageNotFound": "No matching results", "createTitle": "Create structure", "updateTitle": "Update structure", "detailTitle": "Structure details", "hideAccountNumber": "Avoid numbers", "hideAccountNumberPlaceHolder": "Numbers to avoid", "addHideAccNumber": "Enter more numbers to avoid", "accountNumber": "Account number", "error": {"name": "The numeric structure name cannot be left blank", "numberStructure": "The numeric structure code cannot be left blank", "numberGroup": "The number group cannot be left blank", "price": "List price cannot be blank", "discount": "Discounts cannot be left blank", "content": "Description cannot be left blank", "account": "The number range cannot be left blank", "structureAccount": "The entered number sequence is not in the correct format", "pattern": "The structure pattern cannot be left blank.", "length": "Length cannot be left blank", "errorPrice": "List price is not valid for group number range"}, "pattern": {"discount": "Discount is not formatted correctly"}}, "managePayment": {"no": "No.", "accountNumber": "Account number", "accountName": "Account name", "bankName": "Bank", "currency": "Currency unit", "balance": "Balance", "usageBalance": "Amount transferred during the day", "status": "Status", "action": "Action", "modal": {"create": "Create", "edit": "Update", "detail": "Detail", "accountName": "Account name", "bankBenificiary": "Beneficiary bank", "currency": "Currency unit", "currencyHolder": "Unit"}, "msg": {"manual": "Switching to manual mode successfully", "auto": "Switched to automatic mode successfully"}}, "smsManage": {"title": "LIST OF SMS REPORTS", "no": "No.", "fromDate": "From date", "toDate": "To date", "phoneSMS": "SMS receiving phone number", "smsType": "Message Type", "amountSendSucceed": "Number of successful submissions", "amountSendFailed": "Number of failed submissions", "amountSend": "Total amount sent"}, "configTransaction": {"title": "List of transaction limits", "denied": "Denied", "approved": "Approved", "waiting": "Waiting for approval", "create": "Create new config transaction", "detail": "Detail config transaction", "update": "Update config transaction", "tableCreateTrans": "Details of limit items of different types of transactions", "status": "Status", "fromDate": "From date", "toDate": "To date", "no": "No.", "packageName": "Service pack name", "serviceType": "Service type", "transactionType": "Transaction type", "sectorType": "Sector type", "maxMoneySend": "Maximum deposit amount", "maxMoneyTrans": "Maximum limit/1 transaction", "activeDate": "Effective date", "createdDate": "Created date", "approvedBy": "Approver", "approvedDate": "Approved date", "approvedStatus": "Approved status", "action": "Action", "reason": "Reason", "reasonApproval": "Approval reason", "reasonDeined": "Deined reason", "createConfig": "Create a new success limit", "denySuccess": "Deny the limit successfully", "approvalSuccess": "Approve the success limit", "statusCofig": {"waiting": "Pending", "approved": "Approved", "cancel": "Canceled"}, "placeholder": {"packageName": "Service pack name", "transactionDay": "Enter daily limit", "transactionMonthNo": "Unlimited", "transactionMonth": "Enter the limit by month", "transactionYear": "Unlimited"}, "modal": {"confirmDeny": "Confirm denial", "confirmApproval": "Confirm approval", "confirmReasonDeny": "Are you sure to refuse to approve this limit configuration ?", "confirmReasonApproval": "Are you sure you approve this limit configuration ?", "delete": "Confirm delete limit configuration", "deleteConfigTransContent": "Are you sure you want to delete this limit configuration ?"}, "form": {"servicePackage": "Service package", "transType": "Transaction type", "sectorType": "Limit by sector type", "currencyType": "Limit by currency", "sector": "Sector type", "maxMoney": "Limit", "trans": "LAK/ transaction", "transDay": "LAK/ day", "transMonth": "LAK/ month", "transYear": "LAK/ year", "startDate": "Start date", "endDate": "End date", "basicService": "Basic package", "advanceService": "Advance package", "internalTransfer": "Internal transfer", "interbankTransfer": "Interbank transfer", "internationalTransfer": "Cross-border payment", "topUp": "Mobile recharge", "billing": "Pay the bill", "insurance": "LVI insurance payment", "cashIn": "Top up Umoney wallet"}}, "configSaving": {"title": "List of savings terms", "type": "Type of savings", "tenor": "Period", "tenorPeriod": "Term type", "m": "Month", "create": "Create a new savings term", "update": "Update savings term", "view": "Saving term details", "modal": {"delete": "Confirm deletion of savings term configuration", "deleteConfigSavingContent": "Are you sure you want to delete this savings term configuration?"}, "error": {"type": "Savings type cannot be left blank", "tenor": "Term cannot be left blank"}, "pattern": {"tenor": "Term can only be entered in numbers"}}, "insuranceManage": {"title": "List of insurance purchase reports LVI", "fromDate": "From date", "toDate": "To date", "status": "Status", "no": "No", "cifNumber": "CIF number(CIF)", "customerName": "Customer name", "phoneNumber": "Phone number", "vehicleType": "Car type", "insurancePolicy": "Insurance policy code", "type": "Product type", "productPackage": "Product package", "costAfterDiscount": "Pre-tax fee (LAK)", "discount": "Discount from LVI (%)", "moneyFromLvi": "The amount LVI returns (LAK)", "discountMB": "Discount from MB (%)", "moneyFromMb": "MB transaction fee (LAK)", "totalAmount": "Payment (LAK)", "transactionCode": "Transaction code", "transactionTime": "Transaction time", "vehicle": "Vehicle insurance", "health": "Health insurance", "placeholder": {"type": "Product type", "productPackage": "Product package", "vehicleType": "Car type"}, "productSelect": {"motor": "Motor vehicle insurance", "health": "health insurance"}}, "customer": {"customerType": {"customer": "Customer Type", "customerMB": "Customers open accounts from MB system", "customerUmoney": "Customers open accounts from Umoney"}, "ekycImages": "Face's identification images", "idCardType": "Id card type", "idCardNumber": "Id card number", "phoneNumber": "Phone number", "fullname": "Full name", "dateOfBirth": "Date of birth", "manualActivationTime": "Manual activation time", "gender": "Gender", "statusOfResidence": "Status of residence", "nationCode": "Nationality", "nationCodeOther": "Other nationlity", "issueDate": "Issue date", "issuePlace": "Issue place", "placeOfResidence": "Place of residence", "placeOfResidenceOutCountry": "Place of residence abroad", "currentAddress": "Current address", "phoneContact": "Contact phone number", "email": "Email", "job": "Job", "position": "Duty", "identityCardImages": "Identity card images", "signatureImage": "Signature Image", "customerSectorId": "Customer profile status", "cif": "Cif", "accountCreatedTime": "Account created time", "staffCode": "Staff code", "oldSector": "Old sector", "accountsList": "Accounts list", "updateHistory": "Update history", "usageInformation": "Usage infomation", "approvedBy": "Approved by", "status": "Status", "approvedDate": "Approved date", "activatedHistory": "History of first activation on the app", "updateServiceHistory": "Update service history", "username": "Username", "accountNumber": "Account number", "accountType": "Account type", "activatedDate": "Activated date", "imei": "IMEI", "deviceId": "Device ID", "operatingSystem": "Operating system", "deviceName": "Device name", "action": "Action", "detail": "Detail", "editedBy": "Edited by", "requestedTime": "Requested time", "maritalStatus": "Martial Status", "residentStatus": "Resident Status", "createdDateAccountT24": "Created Date account T24", "createdDateAccount": "Registration time", "statusResidence": "Status of residence", "placeOfOrigin": "Place of origin", "shopCode": "Shop code", "default": "<PERSON><PERSON><PERSON>", "companyID": "Company ID", "lastModifiedDate": "Update time", "title": "Detail account number customer", "workplace": "Workplace", "staff": "Staff", "customer": "Customer", "collaborators": "Collaborators", "emptyListUpdateSector": "Please select customer", "premiumAccountNumber": "Premium Account Number", "paymentPrice": "Payment price (LAK)", "buttonSms": "Send sms", "otp": "Enter OTP code", "phoneOrFullName": "Phone number or Name", "maritalStatusOption": {"single": "Single", "married": "Married", "other": "Other"}, "residentStatusOption": {"temporary": "Temporary", "permanent": "Permanent"}, "customerSector": {"customerId1740": "The customer group only opens a payment account at the Transaction Counter", "customerId1700": "Customer group only opens payment accounts at Transaction Counters", "customerId1890": "The new customer group is Science and Technology who went to the account opening counter and opened the app", "customerId1891": "The customer group has just opened an online account and eKYC app 100%, not yet at the counter to add documents", "customerId1742": "The private customer group has profit", "customerSectorId1890": "New customer came to the counter to open an account and opened the app", "customerSectorId1891": "Customer has just opened a payment account and 100% online EKYC app, hasn't come to the counter to add documents yet"}, "accountNoTransaction": {"accountClose": "Close account", "requestTitle": "Confirm sending request to close account", "requestContent": "Are you sure you want to close this account?", "alertNotification": "You have not selected any account yet"}, "activateAccountManual": {"activateAccountTitle": "Confirm account activation", "activateAccountContent": "Are you sure you want to activate this account?", "alertNotification": "You have not selected any account yet"}}, "role": {"name": "Role name", "groupName": "Group role name", "editRole": "Edit role", "description": "Description", "creator": "Creator", "fromDate": "Create Date", "lastModifiedDate": "Last modified date", "updater": "Last modified by"}, "backgroundLogin": {"title": "Updated login background", "info": "Information", "colorCode": "Choose color code", "status": "Operating status", "bgImage": "Background image", "error": {"status": "Status Cannot be left blank"}}, "customerSupport": {"title": "Update customer support information", "titleConfirm": "Confirm deletion", "contentConfirm": "Do you want to delete this phone number?", "info": "Information", "address": "Bank address", "mail": "Mailing address", "phone": "Customer care phone number", "add": "Add new", "error": {"address": "Bank address cannot be left blank", "mail": "Mail address cannot be blank", "phone": "The customer care phone number cannot be left blank"}, "pattern": {"mail": "Mail address is not in correct format", "phone": "Minimum 8-digit customer care phone number"}}, "loanOnline": {"name": "name", "fromDate": "From Date", "toDate": "To Date", "status": "Status", "customerName": "Customer name", "phoneNumber": "Phone Number", "moneyLoanOnline": "Money loan online", "purposeLoan": "Purpose loan", "loanAmount": "Loan amount (LAK)", "loanTime": "Loan Time (month)", "collateral": "Collateral", "fullName": "Full Name", "dateOfBirth": "Date of birth", "gender": "Gender", "maritalStatus": "Marital status", "email": "Email", "address": "Address", "job": "Job", "position": "Position", "workplace": "Workplace", "income": "Income"}, "event": {"title": "Title", "eventType": "Type", "content": "Content", "contentNotification": "Notification content", "numberSent": "Number notification sent", "expectedNotificationAt": "Sent at", "allCustomer": "All customer", "eachCustomer": "Each customer", "announcementTypeId": "Type"}, "fee": {"configurationFeeTypeName": "Charge configuration name", "configurationFeeType": "Charge configuration type", "createDate": "Date created", "createBy": "Creator", "status": "Status", "feeType": "Type of fee", "transactionFeeType": "Type of service", "merchantFeeType": "Type of merchant"}, "bank": {"name": "Bank name", "code": "Bank code", "codeNumber": "Code number", "logo": "Logo", "order": "Order"}, "campaign": {"name": "Campaign's name", "position": "Display position", "description": "Campaign description", "embedLink": "Embedded link", "startDateFrom": "Start date", "endDateTo": "End date", "status": "Status", "createdDate": "Created date", "createdBy": "Created by", "lastModifiedDate": "Last modified date", "lastModifiedBy": "Last modified by", "banners": "Campaign's banner list", "lockTitle": "Confirm lock campaign", "unlockTitle": "Confirm unlock campaign", "deleteTitle": "Confirm delete campaign", "lockContent": "Are you sure you want to lock campaign {{campaignName}}?", "unlockContent": "Are you sure you want to unlock campaign {{campaignName}}?", "deleteContent": "Are you sure you want to delete campaign {{campaignName}}?", "deleteBannerContent": "Are you sure want to delete banner?", "campaignInfoTitle": "Campaign infomation", "loginScreen": "Login screen", "homepageScreen": "Homepage screen", "showScreen": "Display position", "linkBanner": "Attached link", "textBannersLogin": "Upload images in 1:1 ratio", "textBannersHomepage": "Upload images in 5:3 ratio, upload up to 5 photos", "textBannersOther": "Upload images in 9:16 ratio, upload up to 5 photos", "createSuccess": "Create new successful campaign", "updateSuccess": "Successful campaign update", "deleteBannerSuccess": "Delete successful campaign image", "addImage": "More photos", "create": "Create new campaign", "detail": "Campaign details", "update": "Campaign update", "titleImage": "Image", "other": "Other services"}, "user": {"username": "Username", "password": "Password", "fullname": "Full name", "dateOfBirth": "Date of birth", "email": "Email", "phoneNumber": "Phone number", "gender": "Gender", "description": "Description", "department": "Department", "position": "Position", "role": "Role", "currentPassword": "Current password", "newPassword": "New password", "confirmPassword": "Confirm new password"}, "position": {"positionName": "Position name", "positionCode": "Position code", "shortName": "Short name", "description": "Description", "createdDate": "Created date", "createdBy": "Created by", "lastModifiedDate": "Last modified date", "lastModifiedBy": "Last modified by"}, "department": {"name": "Department name", "shortName": "Short name", "code": "Department code", "description": "Description", "status": "Status", "time": "time", "createdDate": "Created date", "createdBy": "Created by", "lastModifiedDate": "Last modified date", "lastModifiedBy": "Last modified by"}, "informationTemplate": {"informationTemplateName": "Supporting information name", "informationTemplateCode": "Support Information Code", "displayName": "Display name", "template": "Content", "createdDate": "Created Date", "createdBy": "Creator", "lastModifiedBy": "Last Editor", "lastModifiedDate": "Last modified date", "status": "Status", "time": "time"}, "merchant": {"merchantName": "Merchant Name", "merchantCode": "Merchant Code", "merchantAccountNumber": "Account Number", "merchantBalanceAccount": "Topup account balance", "merchantBankCode": "Bank Code", "description": "Description", "infor": "Merchant Information", "inforMasterMerchant": "Master Merchant Information", "listChilMerchant": "List of Child Merchants", "origin": "Type of Merchant", "masterMerchant": {"merchantName": "Master Merchant Name", "merchantCode": "Master Merchant Code", "merchantAccountNumber": "Account Number", "masterMerchantBilling": "Billing", "masterMerchantTopup": "Topup", "masterMerchantOther": "Other", "serviceType": "Type of service", "origin": "Type of Master Merchant"}, "transactionHistory": {"name": "Account Name", "cif": "Customer Code (CIF)", "code": "Transaction code", "totalAmount": "Total transaction amount (Lak)", "date": "Transaction date", "fromDate": "Transaction from date", "discount": "Discount (%)"}, "monney": "Amount (Lak)", "fee": "Transaction fee (Lak)", "transferType": "Transfer type", "transactionAmount": "Amount ", "feeAmount": "Transaction fee ", "totalAmount": "Total transaction amount "}, "report": {"lapnet": {"id": "Id", "time": "Time", "referenceNumber": "Referencenumber", "fromMember": "Frommember", "fromUser": "<PERSON><PERSON>", "fromAccount": "Fromaccount", "toType": "Totype", "toAccount": "Toac<PERSON>unt", "toMember": "Tomember", "amount": "Amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "purpose": "Purpose", "fee": "Fee"}, "transaction": {"foreignCurrency": "Foreign currency", "foreignMoney": "Foreign transaction amount", "foreignFee": "Foreign transaction fee", "totalForeignAmount": "Total foreign transaction amount", "exchangeRate": "Exchange rate", "transferMoney": "Transfer money", "qrcode": "QR code", "saveTransaction": "Save transaction", "topup": "Top up", "cashIn": "Cash in", "billing": "Billing", "insurance": "LVI insurance payment", "internalBank": "Internal bank", "interBank": "Inter bank", "internationalBank": "Cross-border payment", "customerAccNumber": "Root account", "transID": "TransID", "clientMessageId": "Client message id", "customerAccountName": "Username", "target": "Receiving account", "beneficiaryCustomerName": "Recipient Name", "transactionCurrency": "<PERSON><PERSON><PERSON><PERSON>", "tradingResults": "Trading results", "transactionStatus": "Transaction status", "electric": "Electricity payment", "water": "Water payment", "invoiceNumber": "Invoice Number (AccNo)", "proCode": "Province code (ProCode)", "remark": "Payment content (Remark)", "titleLa": "Lao title (Title_LA)", "accName": "Account name (AccName)", "fixedDiscount": "Fixed discount (LAK)", "limitPerDay": "Limit per day", "limitPerMonth": "Limit per month", "limitPerYear": "Limit per year", "limitPerTransaction": "Transaction limit", "contactStatus": "Contact status", "configAutoButton": "Configure automatic report sending", "crossBorder": "Cross border", "configAutoReport": {"titleReport": "Configure <PERSON><PERSON> automatic report sending", "entrucstedHistoryTitle": "Configure automatic reporting of <PERSON><PERSON>'s revenue and expenditure history", "title": "Email subject", "content": "Email content", "time": "Sending time (Daily)", "status": "Operating status", "titleMail": "List of received emails", "addMail": "Add new Receiving Email", "placeholder": {"time": "Choose time"}, "error": {"required": {"title": "Can not leave the title blank", "content": "Content cannot be empty", "time": "Sending time cannot be blank", "syncDate": "Sync date cannot be empty", "mail": "Email cannot be blank", "emailDuplicate": "Email cannot be duplicated", "transactionStatuses": "Transaction status cannot be empty", "status": "Active status cannot be empty"}, "pattern": {"emailDuplicate": "Email cannot be duplicated"}}}, "configAutoReportLapNet": {"titleReport": "Configure LAPNET automatic report sending"}, "configAutoReportLapNetUmoney": {"titleReport": "Configure automatic report sending LAPNET UMONEY"}, "configAutoNotificationHistory": {"titleReport": "Configuration to send email notification of fee collection errors", "configAutoButton": "Configuration for sending mail"}}}, "referral": {"userFullName": "Full Name", "phoneNumber": "Phone number", "userCode": "Staff code", "rmCode": "RM code", "referralId": "Description Code", "rmLink": "Referral Link", "titleDetail": "Referral code details", "type": "Type", "import-export": "Import/export excel", "updateTitle": "Update Referral Code", "createTitle": "Create referral code", "qrCode": "QR Code", "quantity": "Quantity Referral customer", "referralCode": "Referral code", "referralCodeUser": "Referrer code", "referrerPhoneNumber": "Referrer phone number", "referrerName": "<PERSON><PERSON><PERSON>'s name", "referrerInformation": "Referrer Information"}, "versionManage": {"no": "STT", "versionName": "Version name", "versionCode": "Version code", "forceUpdate": "Forced to update", "noti": "Notification", "handUpdate": "Manual update", "installed": "Installed", "urlStore": "URL store", "createdDate": "Created date", "status": "Status", "action": "Action", "fromDate": "From date", "toDate": "To date", "detailIos": "Detail version IOS", "createIos": "Create new version for IOS", "detailAndroid": "Detail version Android", "createAndroid": "Create new version for Android", "updateIos": "Update version IOS system", "updateAndroid": "Update version Android system", "versionStatus": {"published": "Published", "unreleased": "Unreleased"}, "form": {"versionCode": "Version code", "versionName": "Version name", "versionContent": "Description update", "releaseDate": "Release date", "forceUpdate": "Forced to update", "manualUpdate": "Update by manual", "forceNotification": "Notification", "urlStore": "Url store"}, "placeholder": {"versionName": "Enter version name", "versionCode": "Enter verson code", "versionContent": "Enter content update", "urlStore": "Enter link update"}}, "feeSchedule": {"title": "Title", "no": "No", "image": "Image", "createdDate": "Start time", "status": "Status", "lock": "Confirm fee schedule lock", "lockContent": "Are you sure you want to lock the fee schedule {{title}} ?", "unlock": "Confirm fee schedule unlock", "unlockContent": "Are you sure you want to unlock fee schedule {{title}} ?", "create": "Create a new fee schedule", "detail": "fee schedule details", "update": "Update fee schedules", "information": "fee schedule information", "modal": {"delete": "Confirm fee schedule deletion", "deleteRateContent": "Are you sure you want to delete this fee schedule ?"}}, "notificationLimit": {"contacted": "Confirmed transition to contacted status", "notContacted": "Confirmed change to not contacted status", "contactedContent": "Are you sure you want to change your username {{fullname}}'s contact status?", "notContactedContent": "Are you sure you want to change the status of username {{fullname}}?", "title": "Notice of over-limit payment", "notiTabTitle": "Transaction limit"}, "monitorLog": {"type": "Type", "clientMessageId": "ClientMessageId", "method": "Method", "requestUri": "RequestUri", "serviceName": "ServiceName", "duration": "Duration", "httpStatus": "HttpStatus", "requestTime": "RequestTime", "responseTime": "ResponseTime"}, "transactionQrPay": {"bankTransId": "Transaction code", "partnerId": "Partner ID", "bankAccount": "Source account", "accountNumber": "Beneficiary account", "amount": "Amount", "content": "Content", "transDate": "Transaction time"}}, "template": {"loanOnline": "List_Of_Loan_Online_Applications", "merchantHistory": "Merchant_Transaction_History_{{param}}", "masterMerchantHistory": "Master_Merchant_Transaction_History_{{param}}", "customerEvent": "List_Customer", "eventReceiver": "List_Notification_Receiver", "transactionReport": "Report_Transaction_Other", "transactionNotifiLimitReport": "Report_Transactions_Exceeding_The_Limit", "transactionReportUmoney": "Report_Transaction_Umoney", "transactionReportDebitDeposit": "Report_Transaction_Debit_Deposit", "lapnetReport": "Report_Lapnet", "customerRegistration": "List_Customer_Register", "masterMerchant": "List_Master_Merchant", "merchant": "List_Merchant", "referralTemplate": "Referral_Template", "referral": "List_Referral", "android": "List_Version_Android", "ios": "List_Version_IOS", "insurance": "INSURANCE_LIST", "configTransLimit": "Transaction_Limit_Configuration_List", "campaign": "List_Campaign", "sms": "Report_Message_SMS", "customerTemplate": "List_Of_Customer", "errorImportCustomer": "List_Of_Errors_Importing_Customer_Information", "transReport": "List_Of_Transaction_Quantities", "savingAccount": "Saving_Account_List", "smsBalance": "Sms_Balance_List", "transactionMMoneyReport": "Report_Electricity_And_Water_Payment", "activateAccountManual": "Manual_Account_Activation_List", "accountNoTransaction": "List_Of_Accounts_With_No_Transactions", "errorImportPremiumAccount": "List_Of_Errors_Importing_Premium_Account_Structure", "transactionQrpay": "QRPAY_TRANSACTION_REPORT", "premiumAccountNumberSoldReport": "Report_Of_Beautiful_Numbers_Sold", "specialAccountNumber": "List_Of_Special_Beautiful_Account_Numbers", "premiumAccountNumberReport": "Report_Of_Nice_Number_Lookup", "numberGroupReport": "Report_Of_Number_Group", "luckyNumberAccountStructure": "List_Of_Lucky_Number_Account", "accountStructure": "Lucky_Number_Account_Structure", "errorImportSpecialAccount": "List_Of_Errors_Import_Special_Account_Number", "premiumAccountRevertReport": "List_Of_Premium_Account_Numbers_Recovered_Errors", "smsBalanceChargeFail": "List_Of_SMS_Balance_Charge_Failures", "internationalPaymentTemplate": "Report_Cross_Border_Payment"}, "department": {"lockTitle": "Confirm lock department", "unlockTitle": "Confirm unlock department", "deleteTitle": "Confirm delete department", "lockContent": "Are you sure you want to lock department {{departmentName}}?", "unlockContent": "Are you sure you want to unlock department {{departmentName}}?", "deleteContent": "Are you sure you want to delete department {{departmentName}}?", "create": "Create a department", "update": "Update a department", "detail": "Department's detail", "error": {"required": {"departmentCode": "Department code can not be empty", "departmentName": "Department name can not be empty", "status": "Department status can not be empty", "departmentShortName": "Department short name can not be empty"}, "maxLength": {"shortName": "Shortname with up to {{param}} characters", "description": "Description can be up to {{param}} characters", "departmentCode": "Department code up to {{param}} characters", "departmentName": "Department name has a maximum of {{param}} characters"}, "minLength": {"departmentCode": "Department code at least {{param}} characters", "departmentName": "Department name at least {{param}} characters"}, "pattern": {"departmentName": "Department name must only include letters, numbers and spaces", "departmentShortName": "Department short name must only include letters, numbers and spaces"}}}, "informationTemplate": {"lockTitle": "Confirm lock support information", "unlockTitle": "Confirm unlock support information", "deleteTitle": "Confirm delete support information", "lockContent": "Are you sure you want to lock the {{informationTemplateName}} support information?", "unlockContent": "Are you sure you want to unlock support information {{informationTemplateName}}?", "deleteContent": "Are you sure you want to delete support information {{informationTemplateName}}?", "create": "Create new support information", "update": "Update support information", "detail": "Detailed support information", "infor": "Support information", "error": {"required": {"informationTemplateCode": "Support information code cannot be empty", "informationTemplateName": "Supporting information name cannot be empty", "status": "Status cannot be empty", "informationTemplateDisplayName": "The display name cannot be empty", "template": "Contents of supporting information cannot be empty"}, "maxLength": {"displayName": "The display name can be up to {{param}} characters", "description": "Description can be up to {{param}} characters", "informationTemplateCode": "Supporting information codes can be up to {{param}} characters", "informationTemplateName": "Supporting information name can be up to {{param}} characters", "informationTemplateDisplayName": "Supporting information display name with up to {{param}} characters", "template": "Content can be up to {{param}} characters"}, "pattern": {"informationTemplateName": "The supporting information name must only include letters, numbers and spaces", "informationTemplateDisplayName": "The supporting information display name must only include letters, numbers and spaces", "informationTemplateCode": "Information template code is invalid"}}}, "smsBalance": {"root": "Manage on/off sms service balance fluctuations", "customerName": "Customer name", "cif": "CIF number", "customerAccountNumber": "Customer account number", "issueDate": "Issue date", "cancellationDate": "CancellationDate", "manageSmsBalanceTitle": "List of services on/off SMS balance fluctuations", "notificationFail": "SMS balance change fee", "expectedTime": "Sending Time (Minutes/time)", "chargeFee": "Account Charges", "error": {"required": {"expectedTime": "Send time cannot be blank"}}}, "servicePack": {"root": "Service Management", "createTitle": "Create new service", "updateTitle": "Service Update", "detailTitle": "Service details", "titleInforUser": "Service information", "lock": "Confirm service lock", "unlock": "Confirm service unlock", "delete": "Confirm deletion of service", "lockContent": "Are you sure you want to lock service {{name}}?", "unlockContent": "Are you sure you want to unlock the {{name}} service?", "deleteContent": "Are you sure you want to delete service {{name}}?", "deleteContentUrl": "Are you sure you want to delete this API {{name}} information?", "name": "Service name", "type": "Service type", "code": "Service code", "clientId": "ClientID", "url": "url address", "password": "Password", "listAPI": "List of APIs", "nameAPI": "API name", "carrieType": "Carrier type", "error": {"required": {"clientId": "ClientID cannot be empty", "password": "Password cannot be empty", "status": "Status cannot be empty", "type": "Service type cannot be blank", "name": "Service name cannot be empty", "url": "Url cannot be empty", "nameFunction": "Name cannot be empty", "nameAPI": "Api name cannot be empty"}, "minLength": {"clientId": "ClientID minimum {{param}} characters", "password": "Password minimum {{param}} characters"}, "pattern": {"clientId": "ClientID is malformed", "password": "Password is not in the correct format", "name": "Invalid service name", "nameAPI": "api name is invalid", "url": "URl is invalid "}}}, "premiumAccNumber": {"pattern": {"premiumAccNumber": "Lucky account number is not in correct format", "firstDigitZero": "Account does not exist", "expireOtp": "The OTP has expired. Please click Resend OTP to receive a new code"}, "success": {"requestOtp": "OTP request sent successfully", "confirmOtp": "Open a lucky account number successfully", "sendOtp": "OTP sent successfully"}, "error": {"maxLength": {"premiumAccNumber": "Lucky account number must be from 4 to 10 characters"}, "minLength": {"otp": "OTP code consists of 8 numeric characters"}, "required": {"premiumAccNumber": "Lucky account number cannot be left blank", "accountType": "Lucky account number type cannot be left blank", "otpValue": "OTP cannot be blank"}}}, "client": {"root": "Client management", "createTitle": "Create new client", "updateTitle": "Update client", "detailTitle": "Client details", "titleInforUser": "Client information", "lock": "Confirm client lock", "unlock": "Confirm client unlock", "delete": "Confirm deletion of client", "lockContent": "Are you sure you want to lock client {{name}}?", "unlockContent": "Are you sure you want to unlock client {{name}}?", "deleteContent": "Are you sure you want to delete client {{name}}?", "name": "Client name", "type": "Classification", "clientId": "ClientID", "password": "Password", "inter": "Outside the system", "internal": "Internal", "error": {"required": {"clientId": "ClientID cannot be empty", "password": "Password cannot be empty", "status": "Status cannot be empty", "type": "Classification cannot be blank", "name": "Client name cannot be empty"}, "minLength": {"clientId": "ClientID minimum {{param}} characters", "password": "Password minimum {{param}} characters"}, "pattern": {"clientId": "ClientID is malformed", "password": "Password is not in the correct format", "name": "Invalid client name"}}}, "numberGroup": {"root": "Number Group", "list": "Number Group List", "createTitle": "Create New Number Group", "updateTitle": "Update Number Group", "detailTitle": "Number Group Details", "titleInforUser": "Number Group Information", "lock": "Confirm Lock Number Group", "unlock": "Confirm Unlock Number Group", "delete": "Confirm Delete Number Group", "lockContent": "Are you sure you want to lock Number Group {{name}}?", "unlockContent": "Are you sure you want to unlock Number Group {{name}}?", "deleteContent": "Are you sure you want to delete Number Group {{name}}?", "deleteModalContent": "<b>{{name}}</b> is being applied to the <b>Number Structures</b>:", "performModalContent": "To successfully delete the number group, must you break the dependency of the Number Structures?", "name": "Number group name", "code": "Code", "minPrice": "Minimum price", "maxPrice": "Maximum price", "message": {"understand": "I understand", "warning": "Attention: ", "text": "Attention: <span class=\"text-primary-color\" style=\"color: red\">You have just changed the price range of <b>{{name}}</b>, this change may affect the price information of the dependent <b>Number Structures</b>!</span>"}, "error": {"required": {"password": "Password cannot be empty", "status": "Status cannot be empty", "type": "Category cannot be empty", "name": "Number Group Name cannot be empty", "minPrice": "min price cannot be empty", "maxPrice": "max price cannot be empty", "invalidNumber": "min price cannot be greater than maximum price"}, "minLength": {"password": "Password must be at least {{param}} characters"}, "maxLength": {"minPrice": "Invalid min price", "maxPrice": "Invalid max price"}, "pattern": {"GroupId": "GroupID is not in the correct format", "password": "Password is not in the correct format", "name": "Group Name is not valid"}}}, "currency": {"root": "<PERSON><PERSON><PERSON><PERSON>", "list": "List of currencies", "createTitle": "Create new currency", "updateTitle": "Update currency", "detailTitle": "Currency details", "titleInforUser": "Currency information", "lock": "Confirm locking currency", "unlock": "Confirm unlocking currency", "delete": "Confirm deleting currency", "lockContent": "Are you sure you want to lock currency {{name}}?", "unlockContent": "Are you sure you want to unlock currency {{name}}?", "deleteContent": "Do you want to delete currency {{name}} from the list?", "name": "Currency name", "code": "Currency code", "value": "Currency code when generating QR", "error": {"required": {"password": "Password cannot be empty", "status": "Status cannot be empty", "type": "Category cannot be empty", "name": "Currency name cannot be empty", "invalidNumber": "Low price cannot be greater than high price"}, "minLength": {"name": "Currency name minimum {{param}} characters", "code": "Currency code minimum {{param}} characters"}}}, "premiumAccountRevert": {"root": "Premium account number fee collection management failed", "list": "Premium account number failed", "date": "Fee Collection Error Date", "limit": "Notification of exceeding limit", "premium": "Notification of premium account number recovery error", "title": "Notification of failed collection of beautiful account number fees", "note1": "Collecting beautiful account number fees", "note2": " of customer", "note3": " failed", "contentContracted": "Are you sure you want to change the status of this customer {{name}} contacted?", "contentNotContracted": "Are you sure you want to change the status of this customer {{name}} not contacted?"}, "managementSMSBalanceFeeFailed": {"root": "SMS Balance Change Fee Management Failed", "list": "Balance Change Message Fee Management Failed"}, "managePayment": {"no": "STT", "accountNumber": "Account Number", "accountName": "Account Name", "bankName": "Bank", "currency": "<PERSON><PERSON><PERSON><PERSON>", "balance": "Balance", "usageBalance": "Amount transferred today", "status": "Status", "action": "Action", "cif": "Cif Number", "addedTransfer": "Amount added today", "modal": {"create": "Create new", "edit": "Update", "detail": "Detail", "accountName": "Account Name", "bankBenificiary": "Beneficiary Bank", "currency": "<PERSON><PERSON><PERSON><PERSON>", "currencyHolder": "Unit"}, "msg": {"lock": "Confirm account lock", "unlock": "Confirm account unlock", "delete": "Confirm account deletion", "lockContent": "Are you sure you want to lock account {{name}}?", "unlockContent": "Are you sure you want to unlock account {{name}}?", "deleteContent": "Do you want to remove account {{name}} from the list?"}, "bank": "Receiving bank", "bankCode": "Receiving bank code", "remark": "Transfer content", "nameTransfer": "Name of transferring customer", "account": "Payment account number of transferring customer", "amount": "Amount", "fee": "Fee amount", "remarkPayment": "Fee collection content", "fromUserFullName": "Customer name", "fromMember": "Bank code calling for accounting", "fromaccount": "Sending account phone number", "fromUser": "Wallet/bank account number", "debit": "Debit", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "accTransactionCode": "Accounting transaction code", "limit": "Limit/transaction"}, "dotp": {"title": "DOTP Customer Registration Management", "search": {"keyword": {"placeholder": "Enter search keyword (CIF, customer name, phone number)"}}, "table": {"cifNumber": "CIF Number", "customerName": "Customer Name", "phoneNumber": "Phone Number", "deviceId": "Device ID", "registrationDate": "Registration Date", "cancelDate": "Cancellation Date", "deviceName": "Device Name"}, "modal": {"cancel": {"title": "Confirm DOTP Cancellation", "content": "Are you sure you want to cancel DOTP for customer {{customerName}}?", "success": "DOTP cancellation successful"}}}}