<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formFeeSearch" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-md-6 col-lg-3">
            <div class="form-group">
              <label>{{ "fee.keyword" | translate }}</label>
              <input
                trim
                type="text"
                placeholder="{{ 'fee.keyword' | translate }}"
                formControlName="keyword"
                class="w-100"
                class="form-control"
              />
            </div>
          </div>
          <div class="col-md-6 col-lg-3">
            <div class="form-group">
              <label>{{ "fee.configurationFeeType" | translate }}</label>
              <ng-select
                appearance="outline"
                [searchable]="false"
                placeholder="{{ 'fee.configurationFeeType' | translate }}"
                [clearable]="false"
                formControlName="configurationFeeType"
              >
                <ng-option
                  [value]="item?.code"
                  *ngFor="let item of CONFIGURATION_FEE_TYPE"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-6 col-lg-3">
            <div class="form-group">
              <label>{{ "common.status" | translate }}</label>
              <ng-select
                appearance="outline"
                [searchable]="false"
                placeholder="{{ 'common.status' | translate }}"
                [clearable]="false"
                formControlName="status"
              >
                <ng-option
                  [value]="item?.code"
                  *ngFor="let item of ENTITY_STATUS"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
            >
              <div class="btn-reset">
                <i
                  class="bi bi-arrow-clockwise"
                  type="button"
                  (click)="onReset()"
                >
                </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" (click)="onSearch()">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-3 mt-4">
          <button
            type="button"
            class="btn btn-red"
            (click)="onCreate()"
            *hasPrivileges="SYSTEM_RULES.FEE_CREATE"
          >
            {{ "common.action.create" | translate }}
          </button>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th class="text-center" scope="col">
                {{ "common.no" | translate }}
              </th>
              <th scope="col">
                {{ "model.fee.configurationFeeTypeName" | translate }}
              </th>
              <th scope="col">
                {{ "model.fee.configurationFeeType" | translate }}
              </th>
              <th scope="col">
                {{ "model.fee.createDate" | translate }}
              </th>
              <th scope="col">
                {{ "model.fee.createBy" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.status" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of fees; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td>
                <span title="{{ dataItem.feeName }}">{{
                  dataItem.feeName | limitWord
                }}</span>
              </td>
              <td>
                {{
                  getConfigurationFeeType(dataItem?.configurationFeeType)
                    | translate
                }}
              </td>
              <td>{{ dataItem.createdDate }}</td>
              <td>{{ dataItem.createdBy }}</td>
              <td class="text-center">
                <!-- {{ ENTITY_STATUS_MAP[dataItem.status || 0] | translate }} -->
                <span
                  class="badge"
                  [ngClass]="ENTITY_STATUS_MAP[dataItem.status || 0].style"
                  >{{
                    ENTITY_STATUS_MAP[dataItem.status || 0].label | translate
                  }}</span
                >
              </td>

              <td class="text-center">
                <button
                  class="btn px-1 py-0"
                  ngbTooltip="{{ 'common.action.detail' | translate }}"
                  (click)="detail(dataItem?.feeId)"
                >
                  <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                </button>
                <button
                  class="btn px-1 py-0"
                  ngbTooltip="{{ 'common.action.update' | translate }}"
                  (click)="update(dataItem?.feeId)"
                  *hasPrivileges="SYSTEM_RULES.FEE_WRITE"
                >
                  <i class="fa fa-edit mb-color" aria-hidden="true"></i>
                </button>
                <button
                  class="btn px-1 py-0"
                  [ngbTooltip]="
                    dataItem.status === ENTITY_STATUS_CONST.INACTIVE.code
                      ? ('common.action.unlock' | translate)
                      : ('common.action.lock' | translate)
                  "
                  (click)="activeAndInactive(dataItem)"
                  *hasPrivileges="
                    dataItem.status === ENTITY_STATUS_CONST.ACTIVE.code
                      ? SYSTEM_RULES.FEE_LOCK
                      : SYSTEM_RULES.FEE_UNLOCK
                  "
                >
                  <i
                    [className]="
                      dataItem.status === ENTITY_STATUS_CONST.ACTIVE.code
                        ? 'fa fa-lock mb-color'
                        : 'fa fa-unlock mb-color'
                    "
                    aria-hidden="true"
                  ></i>
                </button>
                <!--*ngIf="dataItem.approvalStatus === 1 && (dataItem.approvalType === 1 || dataItem.approvalType === 2)"-->
                <button
                  class="btn px-1 py-0"
                  ngbTooltip="{{ 'common.action.delete' | translate }}"
                  (click)="delete(dataItem)"
                  *hasPrivileges="SYSTEM_RULES.FEE_DELETE"
                >
                  <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="row d-block text-center m-0" *ngIf="fees?.length === 0">
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
        <div *ngIf="fees.length">
          <mat-paginator
            [length]="this.formFeeSearch.value.length"
            [pageSize]="this.formFeeSearch.value.pageSize"
            [pageIndex]="this.formFeeSearch.value.pageIndex"
            [pageSizeOptions]="pageSizeOptions"
            (page)="onChangePage($event)"
            aria-label="Select page"
          >
          </mat-paginator>
        </div>
      </div>
    </div>
  </div>
</section>
