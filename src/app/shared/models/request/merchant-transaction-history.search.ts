import { SearchWithPagination } from '@shared/models/request/base.request.model';

export interface IMerchantTransactionHistorySearch
  extends SearchWithPagination {
  merchantId?: number;
  customerAccountNumber?: string;
  customerCif?: string;
  transactionId?: string;
  transactionDateStart?: string;
  transactionDateEnd?: string;
  merchantIds?: string;
  parentId?: number;
  status?: number;
  serviceType?: string;
  sourceZone?: string;
  timeZoneStr?: string;
  fromDate?: string;
  toDate?: string;
}
