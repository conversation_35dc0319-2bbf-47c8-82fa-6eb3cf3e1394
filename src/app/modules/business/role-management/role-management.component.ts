import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import {
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  MODAL_ACTION,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { IRole } from '@shared/models/role.model';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { RoleService } from '@shared/services/role.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
@Component({
  selector: 'app-role',
  templateUrl: './role-management.component.html',
  styleUrls: ['./role-management.component.scss'],
})
export class RoleManagementComponent implements OnInit, OnDestroy {
  listRole: IRole[] = [];

  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;

  ENTITY_STATUS = ENTITY_STATUS;

  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;

  searchForm = this.fb.group({
    name: '',
    status: null,
  });

  ROUTER_UTILS = ROUTER_UTILS;

  totalRecord = 0;
  pageSize = PAGINATION.PAGE_SIZE_DEFAULT;
  pageIndex = PAGINATION.PAGE_NUMBER_DEFAULT;
  pageSizeOptions = PAGINATION.OPTIONS;
  SYSTEM_RULES = SYSTEM_RULES;

  constructor(
    protected roleService: RoleService,
    // private modalService: NgbModal,
    private router: Router,
    private fb: FormBuilder,
    protected toastService: ToastrCustomService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.onSearch();
  }

  ngOnDestroy() {
    this.modalService.dismissAll();
  }

  paginateRole(roles: IRole[]) {
    this.listRole = roles;
  }

  /**
   * Delete role
   *
   * @param role IRole
   */
  deleteRole(role: IRole) {
    const modalData = {
      title: 'role.deleteTitle',
      content: 'role.deleteRoleContent',
      action: MODAL_ACTION.CONFIRM,
      interpolateParams: { roleName: `<b>${role?.name || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { roleId: role?.roleId };
        this.roleService.deleteRole(params).subscribe((res) => {
          this.toastService.success('common.success');
          this.onSearch();
        });
      }
    });
  }

  /**
   * Lock and unLock
   *
   * @param role IRole
   */
  lockAndUnlock(role: IRole) {
    if (role.status === ENTITY_STATUS_CONST.INACTIVE.code) {
      this.unLockRole(role);
    } else {
      this.lockRole(role);
    }
  }

  /**
   * Lock role
   *
   * @param role IRole
   */
  private lockRole(role: IRole) {
    const modalData = {
      title: 'role.lock',
      content: 'role.lockRoleContent',
      interpolateParams: { roleName: `<b>${role?.name || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { roleId: role?.roleId };
        this.roleService.lock(params).subscribe((res) => {
          this.toastService.success('common.success');
          this.onSearch();
        });
      }
    });
  }

  /**
   * UnLock role
   *
   * @param role IRole
   */
  private unLockRole(role: IRole) {
    const modalData = {
      title: 'role.unLock',
      content: 'role.unlockRoleContent',
      action: MODAL_ACTION.CONFIRM,
      interpolateParams: { roleName: `<b>${role?.name || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { roleId: role?.roleId };
        this.roleService.unlock(params).subscribe((res) => {
          this.toastService.success('common.success');
          this.onSearch();
        });
      }
    });
  }

  /**
   * index on list role
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(index, this.pageIndex, this.pageSize);
  }

  onChangePage(pe: PageEvent) {
    this.pageIndex = pe.pageIndex;
    this.pageSize = pe.pageSize;
    this.onSearch();
  }

  viewDetail(roleId?: number) {
    this.router.navigate([
      ROUTER_UTILS.sysManage.role.root,
      roleId,
      ROUTER_ACTIONS.detail,
    ]);
  }

  /**
   * Search
   */
  onSearch() {
    const searchParams = {
      name: this.searchForm.value.name,
      status: this.searchForm.value.status,
      pageIndex: this.pageIndex,
      pageSize: this.pageSize,
      total: this.totalRecord,
      sort: [],
    };

    this.roleService.search(searchParams).subscribe((response: any) => {
      this.paginateRole(response?.body?.content);
      this.totalRecord = response.body.totalElements;
    });
  }

  onReset(): void {
    this.searchForm.reset();
  }
}
