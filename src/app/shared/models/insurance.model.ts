export interface IInsurance {
  cif: string;
  insuredName: string;
  insuredPhone: string;
  identificationNo: string;
  type: string;
  packageCode: any;
  vehicleCode: any;
  payTotal?: number;
  discount?: number;
  premium?: number;
  payCurrency: string;
  transactionStatus: string;
  transactionCode: string;
  transactionTime: string;
  transactionTimeStr: null;
  mbDiscount?: string;
  mbFee?: string;
  mbPayTotal?: string;
}

export interface IVehiclePackage {
  id: number;
  code: string;
  nameLa?: string;
  nameEn?: string;
  nameVn?: string;
  nameCn?: string;
  brochure?: string;
  display?: string;
  note?: string;
  classId?: any;
  si?: string;
  order?: number;
  endorsment?: string;
  name: string;
}

export interface IVehicleType {
  id: number;
  code: string;
  nameLa?: string;
  nameEn?: string;
  nameVn?: string;
  nameCn?: string;
  typeId: number;
  name: string;
}

export interface IHealthPackage {
  id: number;
  code: string;
  nameLa?: string;
  nameEn?: string;
  nameVn?: string;
  nameCn?: string;
  premium?: number;
  paid?: number;
  discount?: number;
  display?: string;
  brochure?: string;
  yearOldMin?: number;
  yearOldMax?: number;
  name?: string;
  active?: number;
}
