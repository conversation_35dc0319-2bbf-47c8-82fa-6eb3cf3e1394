import { Injectable } from '@angular/core';
import { ICampaign } from '@shared/models/campaign.model';
import { ICampaignSearch } from '@shared/models/request/campaign.search';
import {
  IFileEntryDelete,
  IFileEntrySearch,
} from '@shared/models/request/file-entry.search';
import CommonUtils from '@shared/utils/common-utils';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({ providedIn: 'root' })
export class CampaignService extends AbstractService {
  public resourceUrl = '/campaign';

  search(request: ICampaignSearch): Observable<EntityResponseType<ICampaign>> {
    return super.post<EntityResponseType<ICampaign>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  detail(request: ICampaign): Observable<EntityResponseType<ICampaign>> {
    return super.post<EntityResponseType<ICampaign>>(
      `${this.resourceUrl}/detail`,
      request
    );
  }

  create(request: ICampaign): Observable<EntityResponseType<ICampaign>> {
    const body = CommonUtils.appendObjectToFormData(request, new FormData());
    return super.post<EntityResponseType<ICampaign>>(
      `${this.resourceUrl}/create`,
      body
    );
  }

  update(request: ICampaign): Observable<EntityResponseType<ICampaign>> {
    return super.post<EntityResponseType<ICampaign>>(
      `${this.resourceUrl}/update`,
      request
    );
  }

  deleteCampaign(
    request: ICampaign
  ): Observable<EntityResponseType<ICampaign>> {
    return super.post<EntityResponseType<ICampaign>>(
      `${this.resourceUrl}/delete`,
      request
    );
  }

  lock(request: ICampaign): Observable<EntityResponseType<ICampaign>> {
    return super.post<EntityResponseType<ICampaign>>(
      `${this.resourceUrl}/lock`,
      request
    );
  }

  unlock(request: ICampaign): Observable<EntityResponseType<ICampaign>> {
    return super.post<EntityResponseType<ICampaign>>(
      `${this.resourceUrl}/unlock`,
      request
    );
  }

  exportCampaign(
    request: ICampaign
  ): Observable<EntityResponseType<ICampaign>> {
    return super.postFile(`${this.resourceUrl}/export`, request);
  }

  getBanner(
    search: IFileEntrySearch,
    request: ICampaign,
    loading = false,
    ignoreError = true
  ): any {
    return super.postFile(
      `${this.resourceUrl}/banner/${search.fileEntryId}/${search.normalizeName}`,
      request,
      {
        loading,
        ignoreError,
      }
    );
  }

  createBanner(request: ICampaign): Observable<EntityResponseType<ICampaign>> {
    const body = CommonUtils.appendObjectToFormData(request, new FormData());
    return super.post<EntityResponseType<ICampaign>>(
      `${this.resourceUrl}/banner/create`,
      body
    );
  }

  deleteBanner(
    request: IFileEntryDelete
  ): Observable<EntityResponseType<IFileEntrySearch>> {
    return super.post<EntityResponseType<IFileEntrySearch>>(
      `${this.resourceUrl}/banner/delete`,
      request
    );
  }
}
