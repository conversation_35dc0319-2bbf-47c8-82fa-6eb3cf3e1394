import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IActiveToken } from '@shared/models/active-token.model';
import { IUser } from '@shared/models/user.model';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({ providedIn: 'root' })
export class AccountService extends AbstractService {
  public resourceUrlPublic = '/public/account';

  public resourceUrl = '/account';

  constructor(protected http: HttpClient) {
    super(http);
  }

  /**
   * profile
   *
   * @param params
   * @returns Observable<EntityResponseType<IUser>>
   */
  profile(params: any): Observable<EntityResponseType<IUser>> {
    return super.post<EntityResponseType<IUser>>(
      `${this.resourceUrl}/info/detail`,
      params
    );
  }

  /**
   * changePassword
   *
   * @param params
   * @returns Observable<EntityResponseType<IActiveToken>>
   */
  changePasswordUser(params: any): Observable<EntityResponseType<IUser>> {
    return super.post<EntityResponseType<IUser>>(
      `${this.resourceUrl}/change-pw`,
      params
    );
  }

  /**
   * send Email Verify
   *
   * @param params
   * @returns Observable<EntityResponseType<IActiveToken>>
   */
  sendEmailVerify(params: any): Observable<EntityResponseType<IActiveToken>> {
    return super.post<EntityResponseType<IActiveToken>>(
      `${this.resourceUrlPublic}/send-email-verify`,
      params
    );
  }

  /**
   * verify
   *
   * @param params
   * @returns Observable<EntityResponseType<IActiveToken>>
   */
  verify(params: any): Observable<EntityResponseType<IActiveToken>> {
    return super.post<EntityResponseType<IActiveToken>>(
      `${this.resourceUrlPublic}/verify`,
      params
    );
  }

  /**
   * changePassword
   *
   * @param params
   * @returns Observable<EntityResponseType<IActiveToken>>
   */
  changePassword(params: any): Observable<EntityResponseType<IActiveToken>> {
    return super.post<EntityResponseType<IActiveToken>>(
      `${this.resourceUrlPublic}/change-password`,
      params
    );
  }

  /**
   * resend
   *
   * @param params
   * @returns Observable<EntityResponseType<IActiveToken>>
   */
  resend(params: any): Observable<EntityResponseType<IActiveToken>> {
    return super.post<EntityResponseType<IActiveToken>>(
      `${this.resourceUrlPublic}/resend`,
      params
    );
  }
}
