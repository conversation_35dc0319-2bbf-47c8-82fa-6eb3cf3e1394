import { Clipboard } from '@angular/cdk/clipboard';
import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  ENTITY_TYPE_MAP,
  FILE_EXTENSION,
  IMPORT_OR_EXPORT,
  MODAL_ACTION,
  PAGINATION,
  RM_TYPE,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { IReferral } from '@shared/models/referral.model';
import { ReferralType } from '@shared/models/types/model.type';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ReferralService } from '@shared/services/referral.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import { ReferralCreateUpdateManagementComponent } from './referral-management-create-update/referral-management-create-update.component';
import { ReferralManagementImportComponent } from './referral-management-import/referral-management-immport.component';
@Component({
  selector: 'app-referral-management',
  templateUrl: './referral-management.component.html',
  styleUrls: ['./referral-management.component.scss'],
})
export class ReferralManagementComponent implements OnInit {
  ROUTER_UTILS = ROUTER_UTILS;
  IMPORT_OR_EXPORT = IMPORT_OR_EXPORT;
  ENTITY_STATUS = ENTITY_STATUS;
  pageSizeOptions = PAGINATION.OPTIONS;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  SYSTEM_RULES = SYSTEM_RULES;
  ENTITY_TYPE_MAP = ENTITY_TYPE_MAP;
  RM_TYPE = RM_TYPE;
  referralType = ReferralType;
  referrals: IReferral[] = [];

  ngOnInit(): void {
    this.onSearch();
  }
  constructor(
    private fb: FormBuilder,
    private modalServiceOpen: NgbModal,
    private referralService: ReferralService,
    private translateService: TranslateService,
    private downloadService: DownloadService,
    private modalService: ModalService,
    private router: Router,
    private toastService: ToastrCustomService,
    private clipboard: Clipboard
  ) {}

  formEventSearch = this.fb.group({
    keyword: '',
    status: null,
    length: 0,
    export: null,
    types: [],
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    hasPageable: true,
  });

  /**
   * search form
   */
  onSearch(): void {
    this.referralService
      .search(this.formEventSearch.value)
      .subscribe((res: any): void => {
        this.referrals = res.body.content;
        this.formEventSearch.controls.length.setValue(res.body.totalElements);
      });
  }

  /**
   * reset form
   */
  onReset(): void {
    // set default value date and reset data
    this.formEventSearch.controls.keyword.reset();
    this.formEventSearch.controls.status.reset();
    this.formEventSearch.controls.types.reset();
  }

  onCreate(): void {
    const modalRef = this.modalServiceOpen.open(
      ReferralCreateUpdateManagementComponent,
      {
        backdrop: 'static',
        size: 'lg',
        centered: true,
        keyboard: false,
      }
    );
    modalRef.componentInstance.action = ROUTER_ACTIONS.create;
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;
    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }

  onImportExcel(): void {
    const modalRef = this.modalServiceOpen.open(
      ReferralManagementImportComponent,
      {
        backdrop: 'static',
        size: 'lg',
        centered: true,
        keyboard: false,
      }
    );
    modalRef.componentInstance.action = ROUTER_ACTIONS.detail;
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;
    modalRef.result.then(() => {
      this.onSearch();
      this.formEventSearch.get('export')?.setValue(null);
    });
  }

  /**
   * button click edit
   *
   * @param referral number
   */
  edit(referralId?: number): void {
    sessionStorage.setItem(
      STORAGE_APP.REFERRAL,
      JSON.stringify(this.formEventSearch.value)
    );
    const modalRef = this.modalServiceOpen.open(
      ReferralCreateUpdateManagementComponent,
      {
        backdrop: 'static',
        size: 'lg',
        centered: true,
        keyboard: false,
      }
    );
    modalRef.componentInstance.referralId = referralId;
    modalRef.componentInstance.action = ROUTER_ACTIONS.update;
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;

    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }

  onChangePage(page: PageEvent) {
    this.formEventSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formEventSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  onSearchSubmit() {
    this.formEventSearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.formEventSearch.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    this.onSearch();
  }

  exportFile(): void {
    const bodySearch = this.formEventSearch.value;
    const fileName = this.translateService.instant('template.referral');
    const obFile = this.referralService.exportReferral({
      keyword: bodySearch.keyword,
      status: bodySearch.status,
    });
    this.downloadService.downloadFileWithObservableAndName(
      obFile,
      fileName,
      FILE_EXTENSION.XLSX
    );
  }

  downloadTemplateImport(): void {
    const fileName = this.translateService.instant('template.referralTemplate');
    const obFile = this.referralService.downloadTemplate();
    this.downloadService.downloadFileWithObservableAndName(
      obFile,
      fileName,
      FILE_EXTENSION.XLSX
    );
  }

  /**
   * index on list referral
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formEventSearch.value.pageIndex,
      this.formEventSearch.value.pageSize
    );
  }

  /**
   * check lock and unlock and call api
   *
   * @param referral IReferral
   */
  lockAndUnlock(referral: IReferral): void {
    if (referral.status === ENTITY_STATUS_CONST.INACTIVE.code) {
      this.unLockReferral(referral);
    } else {
      this.lockReferral(referral);
    }
  }

  /**
   * Lock referral register
   *
   * @param position IReferral
   */
  private lockReferral(referral: IReferral) {
    const modalData = {
      title: 'referral.lock',
      content: 'referral.lockReferralContent',
      interpolateParams: {
        referralCode: `<b>${referral?.referralCode || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { referralId: referral?.referralId };
        this.referralService.lock(params).subscribe((res) => {
          this.toastService.success('common.action.lockSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * UnLock referral register
   *
   * @param referral: IReferral
   */
  private unLockReferral(referral: IReferral) {
    const modalData = {
      title: 'referral.unlock',
      content: 'referral.unlockReferralContent',
      interpolateParams: {
        referralCode: `<b>${referral?.referralCode || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { referralId: referral?.referralId };
        this.referralService.unlock(params).subscribe((res) => {
          this.toastService.success('common.action.unlockSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * button click detail
   *
   * @param referralId number
   */
  detail(referralId?: number): void {
    sessionStorage.setItem(
      STORAGE_APP.REFERRAL,
      JSON.stringify(this.formEventSearch.value)
    );
    this.router.navigate([
      ROUTER_UTILS.referral.root,
      referralId,
      ROUTER_ACTIONS.detail,
    ]);
  }

  copyText(textToCopy: any) {
    this.clipboard.copy(textToCopy);
  }
}
