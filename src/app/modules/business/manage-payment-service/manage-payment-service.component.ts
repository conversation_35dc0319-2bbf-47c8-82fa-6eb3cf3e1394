import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import {
  CASHOUT_CONFIGURATION_TYPE_CONST,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  MODAL_ACTION,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { ICashout } from '@shared/models/cashout.model';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { PaymentService } from '@shared/services/payment.service';
import { ROUTER_ACTIONS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import { PaymentServiceCreateComponent } from './payment-service-create/payment-service-create.component';
import { PaymentServiceDetailUpdateComponent } from './payment-service-detail-update/payment-service-detail-update.component';

@Component({
  selector: 'app-manage-payment-service',
  templateUrl: './manage-payment-service.component.html',
  styleUrls: ['./manage-payment-service.component.scss'],
})
export class ManagePaymentServiceComponent implements OnInit {
  paymentData: ICashout[] = [];
  formSearch: FormGroup = new FormGroup({});
  pageSizeOptions = PAGINATION.OPTIONS;

  PAGINATION = PAGINATION;
  SYSTEM_RULES = SYSTEM_RULES;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  CASHOUT_CONFIGURATION_TYPE_CONST = CASHOUT_CONFIGURATION_TYPE_CONST;
  isChecked = true;
  checked?: boolean;
  constructor(
    private paymentService: PaymentService,
    private fb: FormBuilder,
    private modal: NgbModal,
    private modalService: ModalService,
    private toastService: ToastrCustomService
  ) {
    this.formSearch = this.fb.group({
      keyword: '',
      status: null,
      pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
      pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
      length: 0,
      hasPageable: true,
    });
  }

  ngOnInit(): void {
    this.onSearch();
  }

  onSearch() {
    this.paymentService.getAll().subscribe((res: any) => {
      this.paymentData = res.body;
      const data = this.paymentData.map(
        (item: ICashout) => item.configurationType
      );
      if (!!data) {
        this.isChecked =
          data[0] === CASHOUT_CONFIGURATION_TYPE_CONST.AUTO.code ? false : true;
      }
    });
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formSearch.value.pageIndex,
      this.formSearch.value.pageSize
    );
  }

  onsearchSubmit() {
    this.formSearch.controls.pageIndex.setValue(PAGINATION.PAGE_NUMBER_DEFAULT);
    this.formSearch.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    this.onSearch();
  }

  onChangePage(page: PageEvent) {
    this.formSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  openCreateModal() {
    const modalRef = this.modal.open(PaymentServiceCreateComponent, {
      size: 'lg',
      keyboard: true,
      backdrop: 'static',
      centered: true,
    });
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;
    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }

  openUpdateDetailModal(action: string, id: number) {
    const modalRef = this.modal.open(PaymentServiceDetailUpdateComponent, {
      size: 'lg',
      keyboard: true,
      backdrop: 'static',
      centered: true,
    });
    if (action === ROUTER_ACTIONS.update) {
      modalRef.componentInstance.id = id;
      modalRef.componentInstance.action = action;
      modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;
      modalRef.result.then((result) => {
        if (result === MODAL_ACTION.CONFIRM.code) {
          this.onSearch();
        }
      });
      return;
    }
    if (action === ROUTER_ACTIONS.detail) {
      modalRef.componentInstance.id = id;
      modalRef.componentInstance.action = action;
      return;
    }
  }

  openPopUpManualAuto($event: any) {
    const modalData = {
      title: $event ? 'payment.auto' : 'payment.manual',
      content: $event ? 'payment.autoContent' : 'payment.manualContent',
    };
    const data = this.paymentData.map(
      (item: ICashout) => item.configurationType
    );

    if (!!data) {
      const body = {
        type: data[0] || CASHOUT_CONFIGURATION_TYPE_CONST.MANUAL.code,
      } as any;

      this.modalService.confirm(modalData).then((result) => {
        if (result === MODAL_ACTION.CONFIRM.code) {
          this.paymentService.manualAuto(body).subscribe((res) => {
            this.toastService.success(
              data[0] !== CASHOUT_CONFIGURATION_TYPE_CONST.MANUAL.code
                ? 'model.managePayment.msg.manual'
                : 'model.managePayment.msg.auto'
            );
            this.onSearch();
          });
        }
      });
    }
  }

  lockAndUnlock(item: ICashout) {
    if (item.status === ENTITY_STATUS_CONST.INACTIVE.code) {
      this.unLockCashout(item);
    } else {
      this.lockCashout(item);
    }
  }

  private lockCashout(item: ICashout) {
    const modalData = {
      title: 'payment.lock',
      content: 'payment.lockPaymentContent',
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.paymentService.lock(item.id).subscribe((res) => {
          this.toastService.success('common.action.lockSuccess');
          this.onSearch();
        });
      }
    });
  }

  private unLockCashout(item: ICashout) {
    const modalData = {
      title: 'payment.unlock',
      content: 'payment.unlockPaymentContent',
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.paymentService.unLock(item.id).subscribe((res) => {
          this.toastService.success('common.action.unlockSuccess');
          this.onSearch();
        });
      }
    });
  }

  delete(item: ICashout) {
    const modalData = {
      title: 'payment.delete',
      content: 'payment.deletePaymentContent',
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.paymentService.deletePayment(item.id).subscribe((res) => {
          this.toastService.success('common.action.deleteSuccess');
          this.onSearch();
        });
      }
    });
  }
}
