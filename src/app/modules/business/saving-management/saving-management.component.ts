import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import {
  ENTITY_STATUS_MAP,
  FILE_EXTENSION,
  MOMENT_CONST,
  PAGINATION,
  SAVING_ACCOUNT_STATUS,
  SAVING_ACCOUNT_STATUS_MAP,
  SAVING_TYPE,
  SAVING_TYPE_CONST,
  SAVING_TYPE_MAP,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { LANGUAGES } from '@shared/constants/language.constants';

import { Router } from '@angular/router';
import { AbstractDomainManageComponent } from '@core/components/abstract-domain-manage/abstract-domain-manage.component';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { IBaseResponseModel } from '@shared/models/base/base.model';
import { ISaving } from '@shared/models/saving.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { DownloadService } from '@shared/services/helpers/download.service';
import { SavingService } from '@shared/services/saving.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-saving-management',
  templateUrl: './saving-management.component.html',
  styleUrls: ['./saving-management.component.scss'],
})
export class SavingManagementComponent
  extends AbstractDomainManageComponent<any>
  implements OnInit
{
  resource: RESOURCE_TYPE = RESOURCE_CONST.SAVING;
  searchForm: FormGroup = new FormGroup({});
  searchResults: IBaseResponseModel<ISaving[]> = {};
  maxDate = new Date();
  data: ISaving[] = [];

  pageSizeOptions = PAGINATION.OPTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  LANGUAGES = LANGUAGES;
  MOMENT_CONST = MOMENT_CONST;
  SAVING_TYPE = SAVING_TYPE;
  SAVING_TYPE_MAP = SAVING_TYPE_MAP;
  SAVING_TYPE_CONST = SAVING_TYPE_CONST;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  SAVING_ACCOUNT_STATUS_MAP = SAVING_ACCOUNT_STATUS_MAP;
  SAVING_ACCOUNT_STATUS = SAVING_ACCOUNT_STATUS;

  constructor(
    private fb: FormBuilder,
    protected router: Router,
    private downloadService: DownloadService,
    private savingAccountService: SavingService,
    private translateService: TranslateService
  ) {
    super(savingAccountService);
    this.searchForm = this.fb.group({
      keyword: '',
      types: [],
      status: null,
      fromDate: null,
      toDate: null,
      pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
      pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
      length: 0,
      hasPageable: true,
    });
  }

  ngOnInit(): void {
    super.ngOnInit();
  }

  onParseValueSearchForm(): void {
    super.onParseValueSearchForm();
  }

  onSearch() {
    if (typeof this.searchForm.get('types')?.value === 'string') {
      this.searchForm.get('types')?.setValue([]);
    }

    if (this.searchForm.invalid) {
      CommonUtils.markFormGroupTouched(this.searchForm);
      return;
    }
    this.savingAccountService
      .search(this.searchForm.value)
      .subscribe((res: any) => {
        this.data = res.body.content;
        this.searchForm.controls.length.setValue(res.body.totalElements, {
          emitEvent: false,
        });
      });
  }

  onReset() {
    this.searchForm.controls.keyword.reset();
    this.searchForm.controls.fromDate.reset();
    this.searchForm.controls.toDate.reset();
    this.searchForm.controls.types.setValue(null);
    this.searchForm.controls.status.reset();
  }

  exportFile(): void {
    const fileName = this.translateService.instant('template.savingAccount');
    const obFile = this.savingAccountService.export(this.searchForm.value);
    this.downloadService.downloadFileWithObservableAndName(
      obFile,
      fileName,
      FILE_EXTENSION.XLSX
    );
    return;
  }

  detail(savingId?: number) {
    this.router.navigate([
      ROUTER_UTILS.saving.root,
      savingId,
      ROUTER_ACTIONS.detail,
    ]);
  }

  onSelectAll() {
    const savingType = this.SAVING_TYPE.map((item) => item.value);
    this.searchForm.get('types')?.patchValue(savingType);
  }

  onClearAll() {
    this.searchForm.get('types')?.patchValue([]);
  }
}
