import {
  AfterViewChecked,
  ChangeDetectorRef,
  Component,
  OnInit,
} from '@angular/core';
import { NavigationError, NavigationStart, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { LANGUAGES } from '@shared/constants/language.constants';
import { STORAGE_LANGUAGES } from '@shared/constants/storage.constants';
import { LanguageService } from '@shared/services/helpers/language.service';
import { LoadingService } from '@shared/services/helpers/loading.service';
import { ROUTER_UTILS } from '@shared/utils';
import { LocalStorageService } from 'ngx-webstorage';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit, AfterViewChecked {
  currentUrl = '';
  defaultLanguage = LANGUAGES.VI.code;

  constructor(
    public router: Router,
    private translateService: TranslateService,
    private languageService: LanguageService,
    private localStorage: LocalStorageService,
    public loadingService: LoadingService,
    private cdr: ChangeDetectorRef
  ) {
    this.router.events.subscribe((routerEvent) => {
      let language = this.localStorage.retrieve(STORAGE_LANGUAGES);
      if (!language) {
        language = this.defaultLanguage;
        this.localStorage.store(STORAGE_LANGUAGES, this.defaultLanguage);
      }
      this.setLanguage(language);
      this.languageService.updateTitle(
        this.languageService.getPageTitle(this.router.routerState.snapshot.root)
      );
      if (routerEvent instanceof NavigationStart) {
        this.currentUrl = routerEvent.url.substring(
          routerEvent.url.lastIndexOf('/') + 1
        );
      }

      if (
        routerEvent instanceof NavigationError &&
        routerEvent.error.status === 404
      ) {
        this.router.navigate([ROUTER_UTILS.error.notFound]);
      }

      window.scrollTo(0, 0);
    });
  }

  private setLanguage(language: string): void {
    this.translateService.setDefaultLang(language);
    this.translateService.use(language);
  }

  ngOnInit(): void {
    // this.requestPermission();
    // this.runGlobalServices();
    // this.carService.getListCar().subscribe(res => {
    //   console.log(res);
    // });
  }

  // private runGlobalServices(): void {
  //   this.seoService.init();
  //   this.themeService.init();
  // }

  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  // requestPermission() {
  //   const messaging = getMessaging();

  //   getToken(messaging, { vapidKey: environment.firebase.vapidKey })
  //     .then((deviceToken) => {
  //       if (deviceToken) {
  //         this.localStorage.store(STORAGE_TOKEN.DEVICE_TOKEN, deviceToken);
  //       } else {
  //         // Show permission request UI
  //         console.log(
  //           'No registration token available. Request permission to generate one.'
  //         );
  //       }
  //     })
  //     .catch((err) => {
  //       console.log('An error occurred while retrieving token. ', err);
  //     });
  // }
}
