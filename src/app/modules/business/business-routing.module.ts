import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { FeeCreateUpdateComponent } from '@business/fee-management/fee-create-update/fee-create-update.component';
import { FeeManagementComponent } from '@business/fee-management/fee-management.component';
import { NotificationCreateUpdateComponent } from '@business/notification-management/notification-create-update/notification-create-update.component';
import { CreateRoleComponent } from '@business/role-management/create-role/create-role.component';
import { DetailRoleComponent } from '@business/role-management/detail-role/detail-role.component';
import { RoleManagementComponent } from '@business/role-management/role-management.component';
import { AuthGuard } from '@core/guards';
import { MainLayoutComponent } from '@core/layouts/main-layout/main-layout.component';
import {
  FREE_PRIVILEGE,
  SYSTEM_RULES,
} from '@shared/constants/authority.constants';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import { BackgroundLoginComponent } from './background-login-management/background-login.component';
import { BankCreateUpdateComponent } from './bank-management/bank-create-update/bank-create-update.component';
import { BankDetailComponent } from './bank-management/bank-detail/bank-detail.component';
import { BankComponent } from './bank-management/bank.component';
import { CampaignManagementComponent } from './campaign-management/campaign-management.component';
import { CreateCampaignComponent } from './campaign-management/create-campaign/create-campaign.component';
import { DetailUpdateCampaignComponent } from './campaign-management/detail-update-campaign/detail-update-campaign.component';
import { ClientCreateUpdateComponent } from './client-management/client-create-update/client-create-update.component';
import { ClientManagementComponent } from './client-management/client-management.component';
import { ConfigSavingManagementComponent } from './config-saving-management/config-saving-management.component';
import { ConfigTransactionManagementComponent } from './config-transaction-management/config-transaction-management.component';
import { CreateConfigComponent } from './config-transaction-management/create-config/create-config.component';
import { DetailUpdateConfigComponent } from './config-transaction-management/detail-update-config/detail-update.component';
import { CurrencyManagementComponent } from './currency/currency-management.component';
import { CustomerSupportComponent } from './customer-info-management/customer-support.component';
import { AccountNoTransactionComponent } from './customer-management/account-no-transaction/account-no-transaction.component';
import { DetailAccountTransactionComponent } from './customer-management/account-no-transaction/detail-account-transaction/detail-account-transaction.component';
import { ActiveAccountManualComponent } from './customer-management/activate-account-manual/activate-account-manual.component';
import { CustomerApprovalCreateComponent } from './customer-management/customer-approval-management/customer-approval-create/customer-approval-create.component';
import { CustomerApprovalManagementComponent } from './customer-management/customer-approval-management/customer-approval-management.component';
import { CustomerApprovalUpdateComponent } from './customer-management/customer-approval-management/customer-approval-update/customer-approval-update.component';
import { CreateCustomerComponent } from './customer-management/customer-register-management/create-customer/create-customer.component';
import { CustomerRegisterManagementComponent } from './customer-management/customer-register-management/customer-register-management.component';
import { DetailCustomerComponent } from './customer-management/customer-register-management/detail-customer/detail-customer.component';
import { DotpManagementComponent } from './customer-management/dotp-management/dotp-management.component';
import { UmoneyLapnetReportComponent } from './debit-deposit/lapnet-report/umoney-lapnet-report.component';
import { ManageDebitAccountComponent } from './debit-deposit/manage-debit-account/debit-account.component';
import { TransactionDebitReportUmoneyComponent } from './debit-deposit/transaction-debit-report-umoney/transaction-debit-report-umoney.component';
import { DepartmentManagementComponent } from './department-management/department-management.component';
import { FeeScheduleComponent } from './fee-schedule/fee-schedule-crud/fee-schedule.component';
import { FeeScheduleManagementComponent } from './fee-schedule/fee-schedule-management.component';
import { CreateUpdateInformationTemplateComponent } from './information-template/information-template-crud/create-update-information-template.component';
import { InformationTemplateComponent } from './information-template/information-template.component';
import { LapnetReportComponent } from './lapnet-report/lapnet-report.component';
import { LoanOnlineDetailComponent } from './loan-online-management/loan-online-detail/loan-online-detail.component';
import { LoanOnlineManagementComponent } from './loan-online-management/loan-online-management.component';
import { InsuranceManagemetComponent } from './lvi-managemet/insurance-managemet.component';
import { ManagePaymentServiceComponent } from './manage-payment-service/manage-payment-service.component';
import { CreateUpdateMasterMerchantComponent } from './master-merchant-management/create-update-master-merchant/create-update-master-merchant.component';
import { DetailMasterMerchantComponent } from './master-merchant-management/detail-master-merchant/detail-master-merchant.component';
import { MasterMerchantManagementComponent } from './master-merchant-management/master-merchant-management.component';
import { CreateUpdateMerchantComponent } from './merchant-management/create-update-merchant/create-update-merchant.component';
import { DetailMerchantComponent } from './merchant-management/detail-merchant/detail-merchant.component';
import { MerchantManagementComponent } from './merchant-management/merchant-management.component';
import { MonitorLogManagementComponent } from './monitor-log/monitor-log-management.component';
import { CreateNewsComponent } from './news-management/create-update-news/create-update-news.component';
import { NewsManagementComponent } from './news-management/news-management.component';
import { NotificationManagementComponent } from './notification-management/notification-management.component';
import { PositionManagementComponent } from './position-management/position-management.component';
import { PremiumAccountNumberErrorComponent } from './premium-account-number-error/premium-account-number-error.component';
import { CreatePremiumAccNumberComponent } from './premium-account-number/create-premium-acc-number/create-premium-acc-number.component';
import { NumberGroupManagementComponent } from './premium-account-number/number-group/number-group-management.component';
import { PremiumAccountNumberTabComponent } from './premium-account-number/premium-account-number-tab/premium-account-number-tab.component';
import { PremiumAccountNumberCreateComponent } from './premium-account-number/premium-account-structure/premium-account-number-create/premium-account-number-create.component';
import { PremiumAccountStructureComponent } from './premium-account-number/premium-account-structure/premium-account-structure.component';
import { SpecialAccountNumberComponent } from './premium-account-number/special-account-number/special-account-number.component';
import { ProfileComponent } from './profile/profile.component';
import { CreateRateComponent } from './rate-management/create-rate/create-rate.component';
import { RateManagementComponent } from './rate-management/rate-management.component';
import { ReferralDetailManagementComponent } from './referral-management/referral-management-detail/referral-management-detail.component';
import { ReferralManagementComponent } from './referral-management/referral-management.component';
import { SavingInfoComponent } from './saving-management/saving-info-management/saving-info.component';
import { SavingManagementComponent } from './saving-management/saving-management.component';
import { ServicePackCreateUpdateComponent } from './service-pack-management/service-pack-create-update/service-pack-create-update.component';
import { ServicePackManagementComponent } from './service-pack-management/service-pack-management.component';
import { SMSBalanceFeeFailedComponent } from './sms-balance-fee-failed/sms-balance-fee-failed.component';
import { SmsBalanceManagementComponent } from './sms-balance-management/sms-balance-management.component';
import { SmsManagementComponent } from './sms-management/sms-management.component';
import { TransactionQrPayManagementComponent } from './transaction-qr-pay/transaction-qr-pay-management.component';
import { InternationalTransactionReportComponent } from './transaction-report/international-transaction-report/international-transaction-report';
import { ManageTransactionQuantityComponent } from './transaction-report/manage-transaction-quantity/manage-transaction-quantity.component';
import { TransactionNotificationLimitComponent } from './transaction-report/transaction-notification-limit/transaction-notification-limit.component';
import { TransactionReportMMoneyComponent } from './transaction-report/transaction-report-mmoney/transaction-report-mmoney.component';
import { TransactionReportUmoneyComponent } from './transaction-report/transaction-report-umoney/transaction-report-umoney.component';
import { TransactionReportComponent } from './transaction-report/transaction-report.component';
import { UserCreateUpdateComponent } from './user-management/user-create-update/user-create-update.component';
import { UserManagementComponent } from './user-management/user-management.component';
import { VersionManagementComponent } from './version-management/version-management.component';
export const BUSINESS_ROUTES: Routes = [
  {
    path: ROUTER_UTILS.base.home,
    component: MainLayoutComponent,
    data: {
      privilege: FREE_PRIVILEGE,
    },
    children: [
      {
        path: `${ROUTER_UTILS.customer.root}/${ROUTER_UTILS.customer.registration.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.CUSTOMER_READ,
          title: 'sidebar.customer.approve.manage',
        },
        component: CustomerRegisterManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.customer.root}/${ROUTER_UTILS.customer.registration.root}/${ROUTER_UTILS.customer.registration.register}`,
        data: {
          privilege: SYSTEM_RULES.CUSTOMER_CREATE,
          title: 'sidebar.customer.approve.manage',
          action: ROUTER_ACTIONS.create,
        },
        component: CreateCustomerComponent,
      },
      {
        path: `${ROUTER_UTILS.customer.root}/${ROUTER_UTILS.customer.registration.root}/${ROUTER_UTILS.customer.registration.update}`,
        data: {
          privilege: SYSTEM_RULES.CUSTOMER_WRITE,
          title: 'sidebar.customer.approve.manage',
          action: ROUTER_ACTIONS.update,
        },
        component: CreateCustomerComponent,
      },
      {
        path: `${ROUTER_UTILS.customer.root}/${ROUTER_UTILS.customer.registration.root}/${ROUTER_UTILS.customer.registration.detail}`,
        data: {
          privilege: SYSTEM_RULES.CUSTOMER_READ,
          title: 'sidebar.customer.approve.manage',
          action: ROUTER_ACTIONS.detail,
        },
        component: DetailCustomerComponent,
      },
      {
        path: `${ROUTER_UTILS.customer.root}/${ROUTER_UTILS.customer.approve.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.CUSTOMER_READ,
          title: 'sidebar.customer.approve.root',
        },
        component: CustomerApprovalManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.customer.root}/${ROUTER_UTILS.customer.approve.root}/${ROUTER_UTILS.customer.approve.view}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.CUSTOMER_READ,
          title: 'sidebar.customer.approve.root',
          action: ROUTER_ACTIONS.view,
        },
        component: CustomerApprovalCreateComponent,
      },
      {
        path: `${ROUTER_UTILS.customer.root}/${ROUTER_UTILS.customer.approve.root}/${ROUTER_UTILS.customer.approve.update}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.CUSTOMER_READ,
          title: 'sidebar.customer.approve.root',
          action: ROUTER_ACTIONS.update,
        },
        component: CustomerApprovalUpdateComponent,
      },
      {
        path: `${ROUTER_UTILS.customer.root}/${ROUTER_UTILS.customer.accountNoTransaction.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.NO_TRANSACTION_READ,
          title: 'sidebar.customer.accountNoTransaction.root',
        },
        component: AccountNoTransactionComponent,
      },
      {
        path: `${ROUTER_UTILS.customer.root}/${ROUTER_UTILS.customer.accountNoTransaction.view}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.NO_TRANSACTION_READ,
          title: 'sidebar.customer.accountNoTransaction.root',
        },
        component: DetailAccountTransactionComponent,
      },
      {
        path: `${ROUTER_UTILS.customer.root}/${ROUTER_UTILS.customer.activateAccountManual.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.MANUAL_ACTIVATION_READ,
          title: 'sidebar.customer.activateAccountManual.root',
        },
        component: ActiveAccountManualComponent,
      },
      {
        path: `${ROUTER_UTILS.customer.root}/${ROUTER_UTILS.customer.activateAccountManual.root}/${ROUTER_UTILS.customer.activateAccountManual.view}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.MANUAL_ACTIVATION_READ,
          title: 'sidebar.customer.activateAccountManual.root',
        },
        component: DetailAccountTransactionComponent,
      },
      {
        path: `${ROUTER_UTILS.customer.root}/${ROUTER_UTILS.customer.dotpManagement.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.CUSTOMER_READ,
          title: 'sidebar.customer.dotpManagement.root',
        },
        component: DotpManagementComponent,
      },

      // {
      //   path: ROUTER_UTILS.sysManage.errorCode.root,
      //   component: ErrorCodeManagementComponent,
      // },
      // {
      //   path: ROUTER_UTILS.sysManage.errorCode.update,
      //   component: ErrorCodeDetailComponent,
      // },
      {
        path: ROUTER_UTILS.sysManage.backgroundLogin.root,
        component: BackgroundLoginComponent,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.IMAGE_LOGIN_APP_READ,
          title: 'sidebar.sysManage.backgroundLogin',
          parent: ROUTER_UTILS.sysManage.backgroundLogin.root,
        },
      },
      {
        path: ROUTER_UTILS.sysManage.customerSupport.root,
        component: CustomerSupportComponent,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.REQUEST_CUSTOMER_SUPPORT_READ,
          title: 'sidebar.sysManage.customerSupport',
          parent: ROUTER_UTILS.sysManage.customerSupport.root,
        },
      },
      {
        path: ROUTER_UTILS.sysManage.role.root,
        component: RoleManagementComponent,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.ROLE_READ,
          title: 'sidebar.sysManage.role.root',
          parent: ROUTER_UTILS.sysManage.role.root,
        },
      },
      {
        path: `${ROUTER_UTILS.sysManage.role.root}/${ROUTER_UTILS.sysManage.role.create}`,
        component: CreateRoleComponent,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.ROLE_CREATE,
          title: 'sidebar.sysManage.role.root',
          parent: ROUTER_UTILS.sysManage.role.root,
        },
      },
      {
        path: `${ROUTER_UTILS.sysManage.role.root}/${ROUTER_UTILS.sysManage.role.update}`,
        component: CreateRoleComponent,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.ROLE_WRITE,
          title: 'sidebar.sysManage.role.root',
        },
      },
      {
        path: `${ROUTER_UTILS.sysManage.role.root}/${ROUTER_UTILS.sysManage.role.detail}`,
        component: DetailRoleComponent,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.ROLE_READ,
          title: 'sidebar.sysManage.role.root',
        },
      },
      {
        path: `${ROUTER_UTILS.loanOnline.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.LOAN_ONLINE_READ,
          title: 'sidebar.loanOnline.root',
        },
        component: LoanOnlineManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.loanOnline.root}/${ROUTER_UTILS.loanOnline.detail}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.LOAN_ONLINE_READ,
          title: 'sidebar.loanOnline.root',
        },
        component: LoanOnlineDetailComponent,
      },
      {
        path: ROUTER_UTILS.fee.root,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.FEE_READ,
          title: 'sidebar.fee.root',
        },
        component: FeeManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.fee.root}/${ROUTER_UTILS.fee.create}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.FEE_CREATE,
          title: 'sidebar.fee.root',
          action: ROUTER_ACTIONS.create,
        },
        component: FeeCreateUpdateComponent,
      },
      {
        path: `${ROUTER_UTILS.fee.root}/${ROUTER_UTILS.fee.update}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.FEE_WRITE,
          title: 'sidebar.fee.root',
          action: ROUTER_ACTIONS.update,
        },
        component: FeeCreateUpdateComponent,
      },
      {
        path: `${ROUTER_UTILS.fee.root}/${ROUTER_UTILS.fee.detail}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.FEE_READ,
          title: 'sidebar.fee.root',
          action: ROUTER_ACTIONS.detail,
        },
        component: FeeCreateUpdateComponent,
      },
      {
        path: ROUTER_UTILS.notification.root,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.NOTIFICATION_READ,
          title: 'sidebar.notification.root',
        },
        component: NotificationManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.premiumAccountNumber.root}/${ROUTER_UTILS.premiumAccountNumber.numberStructure.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_STRUCTURE_READ,
          title: 'sidebar.structureAccount.root',
        },
        component: PremiumAccountStructureComponent,
      },
      {
        path: `${ROUTER_UTILS.premiumAccountNumber.root}/${ROUTER_UTILS.premiumAccountNumber.special.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.SPECIAL_PREMIUM_ACCOUNT_NUMBER_READ,
          title: 'sidebar.specialAccountNumber.root',
        },
        component: SpecialAccountNumberComponent,
      },
      {
        path: `${ROUTER_UTILS.premiumAccountNumber.root}/${ROUTER_UTILS.premiumAccountNumber.interest.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_READ,
          title: 'sidebar.premiumAccountNumber.title',
        },
        component: PremiumAccountNumberTabComponent,
      },
      {
        path: `${ROUTER_UTILS.premiumAccountNumber.root}/${ROUTER_UTILS.premiumAccountNumber.numberStructure.root}/${ROUTER_UTILS.premiumAccountNumber.numberStructure.update}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_STRUCTURE_UPDATE,
          title: 'sidebar.structureAccount.root',
          action: `${ROUTER_ACTIONS.update}`,
        },
        component: PremiumAccountNumberCreateComponent,
      },
      {
        path: `${ROUTER_UTILS.premiumAccountNumber.root}/${ROUTER_UTILS.premiumAccountNumber.numberStructure.root}/${ROUTER_UTILS.premiumAccountNumber.numberStructure.create}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_STRUCTURE_READ,
          title: 'sidebar.structureAccount.root',
          action: `${ROUTER_ACTIONS.create}`,
        },
        component: PremiumAccountNumberCreateComponent,
      },
      {
        path: `${ROUTER_UTILS.premiumAccountNumber.root}/${ROUTER_UTILS.premiumAccountNumber.numberStructure.root}/${ROUTER_UTILS.premiumAccountNumber.numberStructure.detail}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_STRUCTURE_READ,
          title: 'sidebar.structureAccount.root',
          action: `${ROUTER_ACTIONS.detail}`,
        },
        component: PremiumAccountNumberCreateComponent,
      },
      {
        path: `${ROUTER_UTILS.notification.root}/${ROUTER_UTILS.notification.create}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.NOTIFICATION_CREATE,
          title: 'sidebar.notification.root',
        },
        component: NotificationCreateUpdateComponent,
      },
      {
        path: `${ROUTER_UTILS.notification.root}/${ROUTER_UTILS.notification.detail}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.NOTIFICATION_READ,
          title: 'sidebar.notification.root',
        },
        component: NotificationCreateUpdateComponent,
      },
      {
        path: ROUTER_UTILS.news.root,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.NEWS_READ,
          title: 'sidebar.news.root',
        },
        component: NewsManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.news.root}/${ROUTER_UTILS.news.create}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.NEWS_CREATE,
          title: 'sidebar.news.root',
          action: `${ROUTER_ACTIONS.create}`,
        },
        component: CreateNewsComponent,
      },
      {
        path: `${ROUTER_UTILS.news.root}/${ROUTER_UTILS.news.detail}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.NEWS_READ,
          title: 'sidebar.news.root',
          action: `${ROUTER_ACTIONS.detail}`,
        },
        component: CreateNewsComponent,
      },
      {
        path: `${ROUTER_UTILS.news.root}/${ROUTER_UTILS.news.update}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.NEWS_WRITE,
          title: 'sidebar.news.root',
          action: `${ROUTER_ACTIONS.update}`,
        },
        component: CreateNewsComponent,
      },
      {
        path: ROUTER_UTILS.bank.root,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.BANK_READ,
          title: 'sidebar.bank.root',
        },
        component: BankComponent,
      },
      {
        path: ROUTER_UTILS.sysManage.campaign.root,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.CAMPAIGN_READ,
          title: 'sidebar.campaign.root',
        },
        component: CampaignManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.sysManage.campaign.root}/${ROUTER_UTILS.sysManage.campaign.create}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.CAMPAIGN_CREATE,
          title: 'sidebar.campaign.create',
          parent: `${ROUTER_UTILS.sysManage.campaign.root}`,
        },
        component: CreateCampaignComponent,
      },
      {
        path: `${ROUTER_UTILS.sysManage.campaign.root}/${ROUTER_UTILS.sysManage.campaign.detail}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.CAMPAIGN_READ,
          title: 'sidebar.campaign.detail',
          action: ROUTER_ACTIONS.detail,
          parent: `${ROUTER_UTILS.sysManage.campaign.root}`,
        },
        component: DetailUpdateCampaignComponent,
      },
      {
        path: `${ROUTER_UTILS.sysManage.campaign.root}/${ROUTER_UTILS.sysManage.campaign.update}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.CAMPAIGN_WRITE,
          title: 'sidebar.campaign.root',
          action: ROUTER_ACTIONS.update,
          parent: `${ROUTER_UTILS.sysManage.campaign.root}`,
        },
        component: DetailUpdateCampaignComponent,
      },

      {
        path: `${ROUTER_UTILS.bank.root}/${ROUTER_UTILS.bank.create}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.BANK_CREATE,
          title: 'sidebar.bank.root',
          action: ROUTER_ACTIONS.create,
        },
        component: BankCreateUpdateComponent,
      },
      {
        path: `${ROUTER_UTILS.bank.root}/${ROUTER_UTILS.bank.update}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.BANK_WRITE,
          title: 'sidebar.bank.root',
          action: ROUTER_ACTIONS.update,
        },
        component: BankCreateUpdateComponent,
      },
      {
        path: `${ROUTER_UTILS.bank.root}/${ROUTER_UTILS.bank.detail}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.BANK_READ,
          title: 'sidebar.bank.root',
          action: ROUTER_ACTIONS.detail,
        },
        component: BankDetailComponent,
      },
      {
        path: ROUTER_UTILS.user.root,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.USER_READ,
          title: 'sidebar.user.root',
        },
        component: UserManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.user.root}/${ROUTER_UTILS.user.create}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.USER_CREATE,
          title: 'sidebar.user.root',
          action: ROUTER_ACTIONS.create,
        },
        component: UserCreateUpdateComponent,
      },
      {
        path: `${ROUTER_UTILS.user.root}/${ROUTER_UTILS.user.update}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.USER_WRITE,
          title: 'sidebar.user.root',
          action: ROUTER_ACTIONS.update,
        },
        component: UserCreateUpdateComponent,
      },
      {
        path: `${ROUTER_UTILS.user.root}/${ROUTER_UTILS.user.detail}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.USER_READ,
          title: 'sidebar.user.root',
          action: ROUTER_ACTIONS.detail,
        },
        component: UserCreateUpdateComponent,
      },
      {
        path: ROUTER_UTILS.user.profile,
        canActivate: [AuthGuard],
        data: {
          privilege: FREE_PRIVILEGE,
          title: 'sidebar.profile',
        },
        component: ProfileComponent,
      },
      {
        path: ROUTER_UTILS.sysManage.position.root,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.POSITION_READ,
          title: 'sidebar.position',
        },
        component: PositionManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.sysManage.department.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.DEPARTMENT_READ,
          title: 'sidebar.department.root',
        },
        component: DepartmentManagementComponent,
      },
      {
        path: ROUTER_UTILS.merchant.root,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.MERCHANT_READ,
          title: 'sidebar.merchant.root',
        },
        component: MerchantManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.merchant.root}/${ROUTER_UTILS.merchant.create}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.MERCHANT_CREATE,
          title: 'sidebar.merchant.root',
          action: ROUTER_ACTIONS.create,
        },
        component: CreateUpdateMerchantComponent,
      },
      {
        path: `${ROUTER_UTILS.merchant.root}/${ROUTER_UTILS.merchant.update}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.MERCHANT_WRITE,
          title: 'sidebar.merchant.root',
          action: ROUTER_ACTIONS.update,
        },
        component: CreateUpdateMerchantComponent,
      },
      {
        path: `${ROUTER_UTILS.merchant.root}/${ROUTER_UTILS.merchant.detail}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.MERCHANT_READ,
          title: 'sidebar.merchant.root',
          action: ROUTER_ACTIONS.detail,
        },
        component: DetailMerchantComponent,
      },
      {
        path: ROUTER_UTILS.merchant.masterMerchant.root,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.MASTER_MERCHANT_READ,
          title: 'sidebar.merchant.masterMerchant.root',
        },
        component: MasterMerchantManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.merchant.masterMerchant.root}/${ROUTER_UTILS.merchant.create}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.MASTER_MERCHANT_CREATE,
          title: 'sidebar.merchant.masterMerchant.root',
          action: ROUTER_ACTIONS.create,
        },
        component: CreateUpdateMasterMerchantComponent,
      },
      {
        path: `${ROUTER_UTILS.merchant.masterMerchant.root}/${ROUTER_UTILS.merchant.update}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.MASTER_MERCHANT_WRITE,
          title: 'sidebar.merchant.masterMerchant.root',
          action: ROUTER_ACTIONS.update,
        },
        component: CreateUpdateMasterMerchantComponent,
      },
      {
        path: `${ROUTER_UTILS.merchant.masterMerchant.root}/${ROUTER_UTILS.merchant.detail}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.MASTER_MERCHANT_READ,
          title: 'sidebar.merchant.masterMerchant.root',
          action: ROUTER_ACTIONS.detail,
        },
        component: DetailMasterMerchantComponent,
      },
      {
        path: `${ROUTER_UTILS.payment.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.CASHOUT_ACCOUNT_READ,
          title: 'sidebar.managePayment.root',
        },
        component: ManagePaymentServiceComponent,
      },
      {
        path: `${ROUTER_UTILS.sysManage.informationTemplate.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.INFORMATION_TEMPLATE_READ,
          title: 'sidebar.informationTemplate.root',
        },
        component: InformationTemplateComponent,
      },
      {
        path: `${ROUTER_UTILS.transaction.transactionOther.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.TRANSACTION_READ,
          title: 'sidebar.merchant.transactionHistory.transactionOther',
        },
        component: TransactionReportComponent,
      },
      {
        path: `${ROUTER_UTILS.transaction.internationalTransaction.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.INTERNATIONAL_PAYMENT_READ,
          title: 'sidebar.merchant.transactionHistory.internationalTransaction',
        },
        component: InternationalTransactionReportComponent,
        // icon: 'bi bi-wallet',
      },
      {
        path: `${ROUTER_UTILS.transaction.transQuantity.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.REPORT_NUMBER_TRANSACTION_READ,
          title: 'sidebar.manageTransQuantity.root',
        },
        component: ManageTransactionQuantityComponent,
      },
      {
        path: `${ROUTER_UTILS.transaction.transactionUmoney.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.UMONEY_TRANSACTION_READ,
          title: 'sidebar.merchant.transactionHistory.transactionUmoney',
        },
        component: TransactionReportUmoneyComponent,
      },
      {
        path: `${ROUTER_UTILS.lapnet.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.LAPNET_REPORT_READ,
          title: 'sidebar.lapnet.root',
        },
        component: LapnetReportComponent,
      },

      {
        path: `${ROUTER_UTILS.referral.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.REFERRAL_READ,
          title: 'sidebar.referral.root',
        },
        component: ReferralManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.referral.root}/${ROUTER_UTILS.referral.detail}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.REFERRAL_READ,
          title: 'sidebar.referral.root',
          action: ROUTER_ACTIONS.detail,
        },
        component: ReferralDetailManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.version.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.VERSION_READ,
          title: 'sidebar.versionManage.root',
        },
        component: VersionManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.insurance.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.INSURANCE_READ,
          title: 'sidebar.insuranceManage.root',
        },
        component: InsuranceManagemetComponent,
      },
      {
        path: `${ROUTER_UTILS.sysManage.configTransaction.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.TRANSACTION_LIMIT_READ,
          title: 'sidebar.configTransaction.root',
        },
        component: ConfigTransactionManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.sysManage.configTransaction.root}/${ROUTER_UTILS.sysManage.configTransaction.create}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.TRANSACTION_LIMIT_CREATE,
          title: 'sidebar.configTransaction.root',
          action: `${ROUTER_ACTIONS.create}`,
        },
        component: CreateConfigComponent,
      },
      {
        path: `${ROUTER_UTILS.sysManage.configTransaction.root}/${ROUTER_UTILS.sysManage.configTransaction.detail}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.TRANSACTION_LIMIT_READ,
          title: 'sidebar.configTransaction.root',
          action: `${ROUTER_ACTIONS.detail}`,
        },
        component: DetailUpdateConfigComponent,
      },
      {
        path: `${ROUTER_UTILS.sysManage.configTransaction.root}/${ROUTER_UTILS.sysManage.configTransaction.update}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.TRANSACTION_LIMIT_UPDATE,
          title: 'sidebar.configTransaction.root',
          action: `${ROUTER_ACTIONS.update}`,
        },
        component: DetailUpdateConfigComponent,
      },
      {
        path: `${ROUTER_UTILS.sysManage.configSaving.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.SAVING_CONFIGURATION_READ,
          title: 'sidebar.configSaving.root',
        },
        component: ConfigSavingManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.smsManage.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.SMS_READ,
          title: 'sidebar.smsManage.root',
        },
        component: SmsManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.saving.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.SAVING_ACCOUNT_READ,
          title: 'sidebar.manageSaving.root',
        },
        component: SavingManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.saving.root}/${ROUTER_UTILS.saving.detail}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.SAVING_ACCOUNT_READ,
          title: 'sidebar.manageSaving.root',
        },
        component: SavingInfoComponent,
      },
      {
        path: `${ROUTER_UTILS.rate.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.INTEREST_RATE_READ,
          title: 'sidebar.rate.root',
        },
        component: RateManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.rate.root}/${ROUTER_UTILS.rate.create}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.INTEREST_RATE_CREATE,
          title: 'sidebar.rate.root',
          action: `${ROUTER_ACTIONS.create}`,
        },
        component: CreateRateComponent,
      },
      {
        path: `${ROUTER_UTILS.rate.root}/${ROUTER_UTILS.rate.update}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.INTEREST_RATE_UPDATE,
          title: 'sidebar.rate.root',
          action: `${ROUTER_ACTIONS.update}`,
        },
        component: CreateRateComponent,
      },
      {
        path: `${ROUTER_UTILS.rate.root}/${ROUTER_UTILS.rate.detail}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.INTEREST_RATE_READ,
          title: 'sidebar.rate.root',
          action: `${ROUTER_ACTIONS.detail}`,
        },
        component: CreateRateComponent,
      },
      {
        path: `${ROUTER_UTILS.transaction.transMMoney.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.UTILITY_TRANSACTION_READ,
          title: 'sidebar.merchant.transactionHistory.transactionMMoney',
        },
        component: TransactionReportMMoneyComponent,
      },
      {
        path: `${ROUTER_UTILS.feeSchedule.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.FEE_SCHEDULE_READ,
          title: 'sidebar.feeSchedule.root',
        },
        component: FeeScheduleManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.feeSchedule.root}/${ROUTER_UTILS.feeSchedule.create}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.FEE_SCHEDULE_CREATE,
          title: 'sidebar.feeSchedule.root',
          action: `${ROUTER_ACTIONS.create}`,
        },
        component: FeeScheduleComponent,
      },
      {
        path: `${ROUTER_UTILS.feeSchedule.root}/${ROUTER_UTILS.feeSchedule.update}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.FEE_SCHEDULE_UPDATE,
          title: 'sidebar.feeSchedule.root',
          action: `${ROUTER_ACTIONS.update}`,
        },
        component: FeeScheduleComponent,
      },
      {
        path: `${ROUTER_UTILS.feeSchedule.root}/${ROUTER_UTILS.feeSchedule.detail}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.FEE_SCHEDULE_READ,
          title: 'sidebar.feeSchedule.root',
          action: `${ROUTER_ACTIONS.detail}`,
        },
        component: FeeScheduleComponent,
      },
      {
        path: `${ROUTER_UTILS.transaction.transactionNotificationLimit.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.NOTIFICATION_LIMIT_READ,
          title: 'sidebar.transactionExceedLimit.root',
        },
        component: TransactionNotificationLimitComponent,
      },
      {
        path: `${ROUTER_UTILS.monitorLog.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.MONITOR_LOG_READ,
          title: 'sidebar.monitorLog',
        },
        component: MonitorLogManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.transaction.transactionQrPay.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.TRANSACTION_QRPAY_READ,
          title: 'sidebar.transactionQrPay',
        },
        component: TransactionQrPayManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.sysManage.informationTemplate.root}/${ROUTER_UTILS.sysManage.informationTemplate.detail}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.INFORMATION_TEMPLATE_READ,
          title: 'informationTemplate.detail',
          action: `${ROUTER_ACTIONS.detail}`,
        },
        component: CreateUpdateInformationTemplateComponent,
      },
      {
        path: `${ROUTER_UTILS.sysManage.informationTemplate.root}/${ROUTER_UTILS.sysManage.informationTemplate.create}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.INFORMATION_TEMPLATE_CREATE,
          title: 'informationTemplate.create',
          action: `${ROUTER_ACTIONS.create}`,
        },
        component: CreateUpdateInformationTemplateComponent,
      },
      {
        path: `${ROUTER_UTILS.sysManage.informationTemplate.root}/${ROUTER_UTILS.sysManage.informationTemplate.update}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.INFORMATION_TEMPLATE_WRITE,
          title: 'informationTemplate.update',
          action: `${ROUTER_ACTIONS.update}`,
        },
        component: CreateUpdateInformationTemplateComponent,
      },
      {
        path: `${ROUTER_UTILS.servicePack.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.SERVICE_PACK_READ,
          title: 'servicePack.root',
        },
        component: ServicePackManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.servicePack.root}/${ROUTER_UTILS.servicePack.create}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.SERVICE_PACK_CREATE,
          title: 'servicePack.root',
          action: `${ROUTER_ACTIONS.create}`,
        },
        component: ServicePackCreateUpdateComponent,
      },
      {
        path: `${ROUTER_UTILS.servicePack.root}/${ROUTER_UTILS.servicePack.update}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.SERVICE_PACK_UPDATE,
          title: 'servicePack.root',
          action: `${ROUTER_ACTIONS.update}`,
        },
        component: ServicePackCreateUpdateComponent,
      },
      {
        path: `${ROUTER_UTILS.servicePack.root}/${ROUTER_UTILS.servicePack.detail}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.SERVICE_PACK_READ,
          title: 'servicePack.root',
          action: `${ROUTER_ACTIONS.detail}`,
        },
        component: ServicePackCreateUpdateComponent,
      },
      {
        path: `${ROUTER_UTILS.client.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.CLIENT_READ,
          title: 'client.root',
        },
        component: ClientManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.client.root}/${ROUTER_UTILS.client.detail}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.CLIENT_READ,
          title: 'client.root',
          action: `${ROUTER_ACTIONS.detail}`,
        },
        component: ClientCreateUpdateComponent,
      },
      {
        path: `${ROUTER_UTILS.client.root}/${ROUTER_UTILS.client.create}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.CLIENT_CREATE,
          title: 'client.root',
          action: `${ROUTER_ACTIONS.create}`,
        },
        component: ClientCreateUpdateComponent,
      },
      {
        path: `${ROUTER_UTILS.client.root}/${ROUTER_UTILS.client.update}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.CLIENT_UPDATE,
          title: 'client.root',
          action: `${ROUTER_ACTIONS.update}`,
        },
        component: ClientCreateUpdateComponent,
      },
      {
        path: `${ROUTER_UTILS.smsBalance.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.SMS_BALANCE_READ,
          title: 'smsBalance.root',
          action: `${ROUTER_ACTIONS.detail}`,
        },
        component: SmsBalanceManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.premiumAccountNumber.root}/${ROUTER_UTILS.numberGroup.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.NUMBER_GROUP_READ,
          title: 'sidebar.numberGroup',
        },
        component: NumberGroupManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.currency.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.CURRENCY_READ,
          title: 'sidebar.currency',
        },
        component: CurrencyManagementComponent,
      },
      {
        path: `${ROUTER_UTILS.transaction.premiumAccountRevertNotificationLimit.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.NOTIFICATION_LIMIT_READ,
          title: 'premiumAccountRevert.list',
        },
        component: PremiumAccountNumberErrorComponent,
      },
      {
        path: `${ROUTER_UTILS.premiumAccountNumber.root}/${ROUTER_UTILS.premiumAccountNumber.create}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_CREATE,
          title: 'sidebar.premiumAccountNumberHasPaymentAccount.create',
        },
        component: CreatePremiumAccNumberComponent,
      },
      {
        path: `${ROUTER_UTILS.managementServiceFeeFailed.managementSMSBalanceFeeFailed.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_CREATE,
          title: 'managementSMSBalanceFeeFailed.list',
        },
        component: SMSBalanceFeeFailedComponent,
      },
      {
        path: `${ROUTER_UTILS.debitAccount.root}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.DEBIT_ACCOUNT_READ,
          title: 'sidebar.debitAccount.debit',
        },
        component: ManageDebitAccountComponent,
      },
      {
        path: `${ROUTER_UTILS.debitAccount.history}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.DEBIT_DEPOSIT_TRANSACTION_READ,
          title: 'sidebar.debitAccount.historyTransfer',
        },
        component: TransactionDebitReportUmoneyComponent,
      },
      {
        path: `${ROUTER_UTILS.debitAccount.report}`,
        canActivate: [AuthGuard],
        data: {
          privilege: SYSTEM_RULES.DEBIT_DEPOSIT_ASYNC_READ,
          title: 'sidebar.debitAccount.report',
        },
        component: UmoneyLapnetReportComponent,
      },
    ],
  },
  // {
  //   path: ROUTER_UTILS.base.freeRoute,
  //   redirectTo: ROUTER_UTILS.base.home,
  // },
];

@NgModule({
  imports: [RouterModule.forChild(BUSINESS_ROUTES)],
  exports: [RouterModule],
})
export class BusinessRoutingModule {}
