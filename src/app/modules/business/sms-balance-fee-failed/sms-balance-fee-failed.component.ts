import { Component, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  Validators,
} from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  CALCULATE_MILLISECONDS_IN_A_YEAR,
  DATE_RANGE_STATISTICAL_LOAN_ONLINE,
  ENTITY_NOTIFICATION_LIMIT_STATUS_CONST,
  ENTITY_STATUS,
  ENTITY_TRANSACTION_TYPE_CONST,
  FILE_EXTENSION,
  MODAL_ACTION,
  MOMENT_CONST,
  NOTIFICATION_LIMIT_ENTITY_STATUS,
  NOTIFICATION_LIMIT_ENTITY_STATUS_MAP,
  PAGINATION,
  TRANSACTION_NOTIFICATION_ENTITY_TYPE,
  TRANSACTION_NOTIFICATION_ENTITY_TYPE_MAP,
  TRANSACTION_TYPE_TRANSFER_MONEY_CONST,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { SELECTED_CONST } from '@shared/constants/user.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { INotificationHistoryTransaction } from '@shared/models/notification-history-transaction.model';
import { ITransactionSearch } from '@shared/models/request/transaction.search';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { NotificationHistoryService } from '@shared/services/notification-history.service';
import { ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';
import { NotificationHistoryEnum } from '../../../shared/models/request/notification-history.search';
import { NotificationSMSBalanceFeeFailedConfig } from './config-auto-report/notification-sms-balance-fee-failed-config.component';
import { NotificationLimitEnum } from '@shared/models/request/notification-limit.search';

@Component({
  selector: 'app-sms-balance-fee-failed',
  templateUrl: './sms-balance-fee-failed.component.html',
  styleUrls: ['./sms-balance-fee-failed.component.scss'],
})
export class SMSBalanceFeeFailedComponent implements OnInit {
  // transactionReport: ILoanOnline[] = [];
  transactionReport: INotificationHistoryTransaction[] = [];
  action: any = '';
  storage: any;
  maxDate = new Date();
  maxToDate = new Date();
  CONTACTED = ENTITY_NOTIFICATION_LIMIT_STATUS_CONST.CONTACTED.code;

  ROUTER_UTILS = ROUTER_UTILS;
  // input search
  pageSizeOptions = PAGINATION.OPTIONS;
  ENTITY_STATUS = ENTITY_STATUS;
  NOTIFICATION_LIMIT_ENTITY_STATUS_MAP = NOTIFICATION_LIMIT_ENTITY_STATUS_MAP;
  SYSTEM_RULES = SYSTEM_RULES;
  VALIDATORS = VALIDATORS;
  ENTITY_NOTIFICATION_LIMIT_STATUS_CONST =
    ENTITY_NOTIFICATION_LIMIT_STATUS_CONST;
  TRANSACTION_ENTITY_TYPE_MAP = TRANSACTION_NOTIFICATION_ENTITY_TYPE_MAP;
  ENTITY_TRANSACTION_TYPE_CONST = ENTITY_TRANSACTION_TYPE_CONST;
  TRANSACTION_ENTITY_TYPE = TRANSACTION_NOTIFICATION_ENTITY_TYPE;
  TRANSACTION_TYPE_TRANSFER_MONEY_CONST = TRANSACTION_TYPE_TRANSFER_MONEY_CONST;
  MOMENT_CONST = MOMENT_CONST;
  NOTIFICATION_LIMIT_ENTITY_STATUS = NOTIFICATION_LIMIT_ENTITY_STATUS;
  SELECTED_CONST = SELECTED_CONST;

  isValidMaxDate = (control: FormControl) => {
    const dateOfBirth = new Date(control.value).getTime();
    const maxDateOfBirth = new Date().getTime();
    return dateOfBirth > maxDateOfBirth ? { invalidMaxDate: true } : null;
  };

  // default form search
  formNotificationHistorySearch = this.fb.group({
    keyword: '',
    fromDate: [null, [Validators.required]],
    toDate: [null, [Validators.required, this.isValidMaxDate]],
    transferTypes: [],
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    notificationTypes: [[NotificationHistoryEnum.SMS_BALANCE_CHANGE_FAILED] as NotificationHistoryEnum[]],
    // bankcode: '',
    hasPageable: true,
    timeZoneStr: '',
    statuses: [],
    // previousPageIndex: 0,
  });

  constructor(
    private notificationHistoryService: NotificationHistoryService,
    private fb: FormBuilder,
    private downloadService: DownloadService,
    private translateService: TranslateService,
    private modalService: ModalService,
    private toastService: ToastrCustomService,
    private modal: NgbModal
  ) {
    // get session storage
    this.storage = sessionStorage.getItem(
      STORAGE_APP.TRANSACTION_NOTIFICATION_LIMIT_REPORT
    );
  }

  /**
   * Init data
   */
  ngOnInit(): void {
    // check storage
    // (when click back button or back website)
    this.formNotificationHistorySearch
      .get('timeZoneStr')
      ?.setValue(Intl.DateTimeFormat().resolvedOptions().timeZone);
    if (this.storage) {
      // get session storage and parse json
      const filter = JSON.parse(this.storage) as ITransactionSearch;
      // set value form control
      this.formNotificationHistorySearch.controls.keyword.setValue(
        filter.keyword
      );
      this.formNotificationHistorySearch.controls.fromDate.setValue(
        filter.fromDate
      );
      this.formNotificationHistorySearch.controls.toDate.setValue(
        filter.toDate
      );
      this.formNotificationHistorySearch.controls.transferTypes.setValue(
        filter.transferTypes
      );
      this.formNotificationHistorySearch.controls.statuses.setValue(
        filter.statuses
      );
      this.onSearch();
    } else {
      // set default value start date and end date
      const startDate = moment()
        .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
        .format(MOMENT_CONST.FORMAT_DEFAULT);
      const endDate = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
      // this.loanOnlineSearch.fromDate = startDate;
      // this.loanOnlineSearch.toDate = endDate;
      this.formNotificationHistorySearch.controls.fromDate.setValue(
        CommonUtils.reverseDate(startDate)
      );
      this.formNotificationHistorySearch.controls.toDate.setValue(
        CommonUtils.reverseDate(endDate)
      );
      this.onSearch();
    }
    // click form clear storage
    sessionStorage.removeItem(STORAGE_APP.TRANSACTION_REPORT);
  }

  /**
   * export file
   */
  exportFile(): void {
    // get value form control
    const bodySearch = this.formNotificationHistorySearch.getRawValue();
    if (this.formNotificationHistorySearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formNotificationHistorySearch);
    } else {
      // get file name
      const fileName = this.translateService.instant(
        'template.smsBalanceChargeFail'
      );
      const obFile = this.notificationHistoryService.export({
        fromDate: bodySearch.fromDate,
        toDate: bodySearch.toDate,
        keyword: bodySearch.keyword,
        transferTypes: bodySearch.transferTypes,
        statuses: bodySearch.statuses,
        timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
        notificationTypes: [NotificationLimitEnum.SMS_BALANCE_CHANGE_FAILED] ,
      })

      this.downloadService.downloadFileWithObservableAndName(
        obFile,
        fileName,
        FILE_EXTENSION.XLSX
      );
    }
  }

  /**
   * Change page: next page and preview page
   *
   * @param page PageEvent
   */
  onChangePage(page: PageEvent) {
    this.formNotificationHistorySearch.controls.pageIndex.setValue(
      page.pageIndex
    );
    this.formNotificationHistorySearch.controls.pageSize.setValue(
      page.pageSize
    );
    this.onSearch();
  }

  dateValidator(value: number): any {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return value > DATE_RANGE_STATISTICAL_LOAN_ONLINE
        ? { invalidDate: true }
        : null;
    };
  }

  changeValidDate() {
    if (
      this.formNotificationHistorySearch.controls.fromDate.value &&
      this.formNotificationHistorySearch.controls.toDate.value
    ) {
      if (this.formNotificationHistorySearch.controls['toDate'].value) {
        this.maxDate =
          this.formNotificationHistorySearch.controls['toDate'].value;
      }
      const startDate = CommonUtils.setStartTime(
        new Date(
          this.formNotificationHistorySearch.controls.fromDate.value
        ).getTime()
      );
      const endDate = CommonUtils.setStartTime(
        new Date(
          this.formNotificationHistorySearch.controls.toDate.value
        ).getTime()
      );
      const dayMax =
        (endDate - startDate) / CALCULATE_MILLISECONDS_IN_A_YEAR.DAY;
      if (dayMax > DATE_RANGE_STATISTICAL_LOAN_ONLINE) {
        this.formNotificationHistorySearch.controls.fromDate.setValidators([
          Validators.required,
          this.dateValidator(dayMax),
          this.isValidMaxDate,
        ]);
        this.formNotificationHistorySearch.controls.fromDate.updateValueAndValidity();
      } else {
        this.formNotificationHistorySearch.controls.fromDate.clearValidators();
        this.formNotificationHistorySearch.controls.fromDate.setValidators([
          Validators.required,
        ]);
        this.formNotificationHistorySearch.controls.fromDate.updateValueAndValidity();
      }
    }
  }

  onSearchSubmit() {
    this.formNotificationHistorySearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.formNotificationHistorySearch.controls.length.setValue(
      PAGINATION.LENGTH_DEFAULT
    );
    this.onSearch();
  }

  /**
   * search form
   */
  onSearch(): void {
    if (this.formNotificationHistorySearch.valid) {
      this.formNotificationHistorySearch.controls.hasPageable.setValue(true);
      this.notificationHistoryService
        .search(this.formNotificationHistorySearch.value)
        .subscribe((res: any): void => {
          this.transactionReport = res.body.content;
          this.formNotificationHistorySearch.controls.length.setValue(
            res.body.totalElements
          );
        });
    }
  }

  /**
   * index on list loan online
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formNotificationHistorySearch.value.pageIndex,
      this.formNotificationHistorySearch.value.pageSize
    );
  }

  // checkValidateDate() {
  //   this.maxDate = this.formNotificationHistorySearch.controls['toDate'].value;
  // }

  /**
   * reset form
   */
  onReset(): void {
    // set default value date and reset data
    const startDateReset = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDateReset = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.formNotificationHistorySearch.controls.keyword.reset();
    this.formNotificationHistorySearch.controls.fromDate.setValue(
      CommonUtils.reverseDate(startDateReset)
    );
    this.formNotificationHistorySearch.controls.toDate.setValue(
      CommonUtils.reverseDate(endDateReset)
    );
    this.formNotificationHistorySearch.controls.fromDate.clearValidators();
    this.formNotificationHistorySearch.controls.fromDate.setValidators([
      Validators.required,
    ]);
    this.formNotificationHistorySearch.controls.fromDate.updateValueAndValidity();
    this.formNotificationHistorySearch.controls.transferTypes.setValue(null);
    this.formNotificationHistorySearch.controls.statuses.setValue(null);
    this.formNotificationHistorySearch
      .get('timeZoneStr')
      ?.setValue(Intl.DateTimeFormat().resolvedOptions().timeZone);
  }

  totalAmount(value = 0, fee = 0): number {
    return +value + +fee;
  }

  onSelectAll(value?: string) {
    const selectedTransferTypes = this.TRANSACTION_ENTITY_TYPE.map(
      (item) => item.code
    );
    const selectedTransactionStatus = this.NOTIFICATION_LIMIT_ENTITY_STATUS.map(
      (item) => item.code
    );
    switch (value) {
      case SELECTED_CONST.TRANSACTION_TYPE:
        this.formNotificationHistorySearch
          .get('transferTypes')
          ?.patchValue(selectedTransferTypes);
        break;
      case SELECTED_CONST.TRANSACTION_STATUS:
        this.formNotificationHistorySearch
          .get('statuses')
          ?.patchValue(selectedTransactionStatus);
        break;
    }
  }

  onClearAll(value?: string) {
    switch (value) {
      case SELECTED_CONST.TRANSACTION_TYPE:
        this.formNotificationHistorySearch.get('transferTypes')?.patchValue([]);
        break;
      case SELECTED_CONST.TRANSACTION_STATUS:
        this.formNotificationHistorySearch.get('statuses')?.patchValue([]);
        break;
    }
  }

  toggleActive(item: INotificationHistoryTransaction): void {
    if (item.status === ENTITY_NOTIFICATION_LIMIT_STATUS_CONST.CONTACTED.code) {
      this.notContacted(item);
    } else {
      this.contacted(item);
    }
  }

  /**
   * Lock user register
   *
   * @param user IUser
   */
  private notContacted(item: INotificationHistoryTransaction) {
    const modalData = {
      title: 'model.notificationLimit.notContacted',
      content: 'premiumAccountRevert.contentNotContracted',
      interpolateParams: {
        name: `<b>${item.customerAccountName || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.notificationHistoryService
          .notContacted({ notificationHistoryId: item.notificationHistoryId })
          .subscribe((res) => {
            this.toastService.success('common.action.notContactedSuccess');
            this.onSearch();
          });
      }
    });
  }

  /**
   * UnLock user register
   *
   * @param user: IUser
   */
  private contacted(item: INotificationHistoryTransaction) {
    const modalData = {
      title: 'model.notificationLimit.contacted',
      content: 'premiumAccountRevert.contentContracted',
      interpolateParams: {
        name: `<b>${item.customerAccountName || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.notificationHistoryService
          .contacted({ notificationHistoryId: item.notificationHistoryId })
          .subscribe((res) => {
            this.toastService.success('common.action.contactedSuccess');
            this.onSearch();
          });
      }
    });
  }

  onConfigAutoReport() {
    const modalRef = this.modal.open(NotificationSMSBalanceFeeFailedConfig, {
      size: 'lg',
      keyboard: true,
      backdrop: 'static',
      centered: true,
    });
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;
    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }
}
