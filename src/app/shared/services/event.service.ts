import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ICustomer } from '@shared/models/customer.model';
import { IEvent } from '@shared/models/event.model';
import { IEventSearch } from '@shared/models/request/event.search';
import { IFileEntrySearch } from '@shared/models/request/file-entry.search';
import CommonUtils from '@shared/utils/common-utils';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class EventService extends AbstractService {
  public resourceUrl = '/events';

  constructor(protected http: HttpClient) {
    super(http);
  }

  search(request: IEventSearch): Observable<EntityResponseType<IEvent>> {
    return super.post<EntityResponseType<IEvent>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  /**
   * API delete
   *
   * @param request IEvent
   * @returns Observable<EntityResponseType<IEvent>>
   */
  deleteEvent(request: IEvent): Observable<EntityResponseType<IEvent>> {
    return super.post<EntityResponseType<IEvent>>(
      `${this.resourceUrl}/delete`,
      request
    );
  }

  /**
   * API create
   *
   * @param request IEvent
   * @returns Observable<EntityResponseType<IEvent>>
   */
  create(request: IEvent): Observable<EntityResponseType<IEvent>> {
    const body = CommonUtils.appendObjectToFormData(request, new FormData());
    return super.post<EntityResponseType<IEvent>>(
      `${this.resourceUrl}/create`,
      body
    );
  }

  /**
   * API create
   *
   * @param request IEvent
   * @returns Observable<EntityResponseType<IEvent>>
   */
  update(request: IEvent): Observable<EntityResponseType<IEvent>> {
    const body = CommonUtils.appendObjectToFormData(request, new FormData());
    return super.post<EntityResponseType<IEvent>>(
      `${this.resourceUrl}/update`,
      body
    );
  }

  /**
   * API get detail
   *
   * @param request
   */
  detail(request: any): Observable<EntityResponseType<IEvent>> {
    return super.post<EntityResponseType<IEvent>>(
      `${this.resourceUrl + '/detail'}`,
      request
    );
  }

  delete(request: any): Observable<EntityResponseType<IEvent>> {
    return super.post<EntityResponseType<IEvent>>(
      `${this.resourceUrl + '/delete'}`,
      request
    );
  }

  send(request: IEvent): Observable<EntityResponseType<IEvent>> {
    const body = CommonUtils.appendObjectToFormData(request, new FormData());
    return super.post<EntityResponseType<IEvent>>(
      `${this.resourceUrl}/send`,
      body
    );
  }

  import(
    file: File,
    loading = false
  ): Observable<EntityResponseType<ICustomer[]>> {
    const formData = new FormData();
    formData.append('file', file);
    return super.post(`${this.resourceUrl}/import-customers`, formData, {
      loading,
    });
  }

  /**
   * download file template
   *
   * @returns
   *
   */
  downloadTemplate() {
    return super.postFile(`${this.resourceUrl}/download-template`, {});
  }

  /**
   * API export
   *
   * @param request
   * @returns Observable<any>
   */
  export(request: any): Observable<any> {
    return super.postFile(`${this.resourceUrl}/export-receiver`, request);
  }

  /**
   * getIcon
   *
   * @param search IFileEntrySearch
   * @param loading boolean
   * @returns Observable<any>
   */
  getIcon(search: IFileEntrySearch, loading = false, ignoreError = true): any {
    return super.postFile(`${this.resourceUrl}/icon`, search, {
      loading,
      ignoreError,
    });
  }
}
