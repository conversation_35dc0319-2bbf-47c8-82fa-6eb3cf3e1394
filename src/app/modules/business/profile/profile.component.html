<section class="box-content-profile">
  <div class="container-fluid" *ngIf="user">
    <!-- <form [formGroup]="form" *ngIf="customer?.customerId"></form> -->
    <div class="row">
      <div class="col-md-12">
        <div class="text-center">
          <img src="../../assets/dist/img/profile.svg" alt="logo login" />
        </div>
        <div class="d-block text-center mb-5 mt-2">
          <button
            class="btn btn-red mr-2"
            type="button"
            (click)="onChangePassword()"
          >
            {{ "common.action.changePassword" | translate }}
          </button>
        </div>
        <div class="row tex-box">
          <div class="col-lg-4 text-left">
            <p>{{ "model.user.fullname" | translate }}</p>
            <hr />
          </div>
          <div class="col-lg-8 text-left">
            <p>
              <strong>{{ user.fullname }}</strong>
            </p>
          </div>
        </div>
        <div class="row tex-box">
          <div class="col-lg-4 text-left">
            <p>{{ "model.user.department" | translate }}</p>
            <hr />
          </div>
          <div class="col-lg-8 text-left">
            <p>
              <strong>{{ user?.departmentName }}</strong>
            </p>
          </div>
        </div>
        <div class="row tex-box">
          <div class="col-lg-4 text-left">
            <p>{{ "model.user.position" | translate }}</p>
            <hr />
          </div>
          <div class="col-lg-8 text-left">
            <p>
              <strong>{{ user?.positionName }}</strong>
            </p>
          </div>
        </div>
        <div class="row tex-box">
          <div class="col-lg-4 text-left">
            <p>{{ "model.user.email" | translate }}</p>
            <hr />
          </div>
          <div class="col-lg-8 text-left">
            <p>
              <strong>{{ user.email }}</strong>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
