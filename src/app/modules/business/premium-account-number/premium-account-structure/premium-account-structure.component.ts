import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  FILE_EXTENSION,
  MODAL_ACTION,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { SELECTED_CONST } from '@shared/constants/user.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { INumberGroup } from '@shared/models/number-group.model';
import { INumberStructure } from '@shared/models/premium-account-number.modal';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { NumberGroupService } from '@shared/services/number-group.service';
import { NumberStructureService } from '@shared/services/premium-account-number.serice';
import CommonUtils from '@shared/utils/common-utils';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils/router.utils';
import { ImportStructureAccountComponent } from './modal-import/modal-import-structure.component';

@Component({
  selector: 'app-premium-account-structure',
  templateUrl: './premium-account-structure.component.html',
  styleUrls: ['./premium-account-structure.component.scss'],
})
export class PremiumAccountStructureComponent implements OnInit {
  pageSizeOptions = PAGINATION.OPTIONS;
  ROUTER_UTILS = ROUTER_UTILS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  VALIDATORS = VALIDATORS;
  SELECTED_CONST = SELECTED_CONST;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  data: INumberStructure[] = [];
  lengths: INumberGroup[] = [];
  numberGroups: INumberGroup[] = [];
  listStructureCode: INumberStructure[] = [];
  numberStructures: string[] = [];
  storage: any;
  selectLengths: INumberGroup[] = [];
  firstLength: INumberGroup = {};
  selectStructures: INumberStructure[] = [];
  firstStructure: INumberStructure = {};

  constructor(
    private formBuilder: FormBuilder,
    private modalService: ModalService,
    private toastService: ToastrCustomService,
    private router: Router,
    private modal: NgbModal,
    private premiumAccountStructureService: NumberStructureService,
    private numberGroupService: NumberGroupService,
    private translateService: TranslateService,
    private downloadService: DownloadService
  ) {
    this.storage = sessionStorage.getItem(STORAGE_APP.NUMBER_GROUP);
  }

  formSearch = this.formBuilder.group({
    length: 0,
    name: '',
    structureLengths: [],
    numberStructures: [],
    // numberGroupIds: [],
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    hasPageable: true,
  });

  ngOnInit(): void {
    // if (this.storage) {
    //   const filter = JSON.parse(this.storage) as INumberStructure;
    //   this.formSearch.controls.numberGroupIds.setValue([filter.numberGroupId]);
    //   sessionStorage.removeItem(STORAGE_APP.NUMBER_GROUP);
    // }
    this.getLength();
    this.onSearch();
    // this.getNumberGroup();
    this.getStructure();
  }

  getLength() {
    this.premiumAccountStructureService.getLength().subscribe((res: any) => {
      if (res.body.length) {
        this.lengths = res.body;
      }
    });
  }

  onSearch(): void {
    if (this.formSearch.valid) {
      const params = this.formSearch.value;
      this.premiumAccountStructureService
        .search(params)
        .subscribe((res: any) => {
          this.data = res.body.content;
          this.formSearch.controls.length.setValue(res.body.totalElements);
        });
    }
  }

  getStructure(numberGroup?: INumberGroup[]) {
    this.numberStructures = [];

    numberGroup?.forEach((item) => {
      this.numberStructures.push(item?.code + '');
    });

    this.premiumAccountStructureService
      .getStructureCode({ lengths: this.numberStructures })
      .subscribe((res: any) => {
        this.listStructureCode = res.body;
      });
  }

  onSearchSubmit(): void {
    this.formSearch.controls.pageIndex.setValue(PAGINATION.PAGE_NUMBER_DEFAULT);
    this.formSearch.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    this.onSearch();
  }

  onChangePage(page: PageEvent) {
    this.formSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  onReset(): void {
    this.listStructureCode = [];
    this.formSearch.controls.name.reset();
    this.formSearch.controls.structureLengths.reset();
    this.formSearch.controls.numberStructures.reset();

    this.getStructure();
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formSearch.value.pageIndex,
      this.formSearch.value.pageSize
    );
  }

  update(id?: number) {
    this.router.navigate([
      ROUTER_UTILS.premiumAccountNumber.root,
      ROUTER_UTILS.premiumAccountNumber.numberStructure.root,
      id,
      ROUTER_ACTIONS.update,
    ]);
  }

  onDelete(data: INumberStructure) {
    const modalData = {
      title: 'model.structureAccount.delete',
      content: 'model.structureAccount.deleteConfig',
      interpolateParams: {
        title: `<b>${data?.numberStructure || ''}</b>`,
        length: `<b>${data?.length || ''}</b>`,
      },
      confirmButtonText: 'common.action.delete',
      cancelButtonText: 'common.action.cancel',
      buttonOrder: 'cancel-first',
      confirmBtnClass: 'btn-danger',
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = {
          id: data.premiumAccountNumberStructureId,
        };
        this.premiumAccountStructureService.delete(params).subscribe(() => {
          this.toastService.success('common.action.deleteSuccess');
          if (this.data?.length === 1) {
            this.formSearch.controls.pageIndex.setValue(
              this.formSearch.controls.pageIndex.value === 0
                ? ''
                : Number(this.formSearch.controls.pageIndex.value) - 1
            );
          }
          this.onSearch();
        });
      }
    });
  }

  uploadFile() {
    const modalRef = this.modal.open(ImportStructureAccountComponent, {
      backdrop: 'static',
      centered: true,
      size: 'lg',
      keyboard: true,
    });
    modalRef.componentInstance.action = MODAL_ACTION.CONFIRM;
    modalRef.result.then((res) => {
      if (res) {
        this.onSearch();
      }
    });
  }

  numberStructureSearchFn(term: string, item: INumberStructure) {
    if (item.numberStructure) {
      term = term.replace(/ + /g, ' ');
      term = term.trim();
      term = term.toLocaleLowerCase();
      return (
        item?.numberStructure.trim().toLocaleLowerCase().indexOf(term) > -1
      );
    }
    return '';
  }

  lockAndUnlock(data: INumberStructure): void {
    const modalData = {
      title:
        data.status === 1
          ? 'model.structureAccount.lock'
          : 'model.structureAccount.unlock',
      content:
        data.status === 1
          ? 'model.structureAccount.lockContent'
          : 'model.structureAccount.unlockContent',
      interpolateParams: {
        title: `<b>${data?.numberStructure || ''}</b>`,
        length: `<b>${data?.length || ''}</b>`,
      },
      confirmButtonText: 'common.action.agree',
      cancelButtonText: 'common.action.cancel',
      buttonOrder: 'cancel-first',
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = {
          id: data.premiumAccountNumberStructureId,
        };
        if (data.status === 1) {
          this.premiumAccountStructureService.lock(params).subscribe((res) => {
            this.toastService.success('common.action.lockSuccess');
            this.onSearch();
          });
        } else {
          this.premiumAccountStructureService
            .unlock(params)
            .subscribe((res) => {
              this.toastService.success('common.action.unlockSuccess');
              this.onSearch();
            });
        }
      }
    });
  }

  onCreate() {
    this.router.navigate([
      ROUTER_UTILS.premiumAccountNumber.root,
      ROUTER_UTILS.premiumAccountNumber.numberStructure.root,
      ROUTER_ACTIONS.create,
    ]);
  }

  detail(id?: number) {
    this.router.navigate([
      ROUTER_UTILS.premiumAccountNumber.root,
      ROUTER_UTILS.premiumAccountNumber.numberStructure.root,
      id,
      ROUTER_ACTIONS.detail,
    ]);
  }

  onChangeLength(item: INumberGroup[]) {
    if (this.formSearch.get('numberStructures')?.value) {
      this.selectStructures = [];
      this.formSearch.controls.numberStructures.reset();
    }

    this.selectLengths = [];
    this.selectLengths = item;
    if (this.selectLengths.length > 0) {
      this.firstLength = this.selectLengths[0];
    }

    this.getStructure(item);
  }

  onChangeStructure(item: INumberStructure[]) {
    this.selectStructures = [];
    this.selectStructures = item;

    if (this.selectStructures) {
      this.firstStructure = this.selectStructures[0];
    }
  }

  onExport() {
    const bodySearch = this.formSearch.getRawValue();
    if (this.formSearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formSearch);
    } else {
      // get file name
      const fileName = this.translateService.instant(
        'template.luckyNumberAccountStructure'
      );
      const params = this.formSearch.value;
      const obFile = this.premiumAccountStructureService.export(params);

      this.downloadService.downloadFileWithObservableAndName(
        obFile,
        fileName,
        FILE_EXTENSION.XLSX
      );
    }
  }

  onSelectAll(value?: string) {
    const lengthId = this.lengths.map((item) => item.code);
    const numberStructures = this.listStructureCode.map((item) => item.numberStructure);
    this.selectStructures = [];
    switch (value) {
      case SELECTED_CONST.LENGTH_ID:
        this.selectLengths = [];
        if (this.lengths.length > 0) {
          this.firstLength = this.lengths[0];
        }
        this.selectLengths = this.lengths;

        this.formSearch.get('numberStructures')?.patchValue([]);
        this.formSearch
          .get('structureLengths')
          ?.patchValue(lengthId);
        break;
      // case SELECTED_CONST.NUMBER_GROUP:
      //     this.formSearch
      //       .get('numberGroupIds')
      //       ?.patchValue(numberGroupIds);
      //     break;
      case SELECTED_CONST.NUMBER_STRUCTURE:
        if (this.listStructureCode.length > 0) {
          this.firstStructure = this.listStructureCode[0];
        }
        this.selectStructures = this.listStructureCode;
        this.formSearch
          .get('numberStructures')
          ?.patchValue(numberStructures);
        break;
    }
  }

  onClearAll(value?: string) {
    this.firstStructure = {};
    // this.selectLengths = [];
    this.selectStructures = [];

    switch (value) {
      case SELECTED_CONST.LENGTH_ID:
        this.formSearch.get('structureLengths')?.patchValue([]);
        this.formSearch.get('numberStructures')?.patchValue([]);
        this.getStructure();
        break;
      // case SELECTED_CONST.NUMBER_GROUP:
      //   this.formSearch.get('numberGroupIds')?.patchValue([]);
      //   break;
      case SELECTED_CONST.NUMBER_STRUCTURE:
        this.formSearch.get('numberStructures')?.patchValue([]);
        break;
    }
  }
}
