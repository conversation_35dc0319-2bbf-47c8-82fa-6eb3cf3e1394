import { Component, HostListener, Input, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
@Component({
  selector: 'app-delete-popup',
  templateUrl: './modal.component.html',
  styleUrls: ['./modal.component.scss'],
})
export class ModalComponent implements OnInit {
  @Input() title = '';
  @Input() body = '';
  @Input() confirmButton = 'common.action.confirm';
  @Input() btnConfirmClass = '';
  @Input() isHiddenBtnClose = false;
  @Input() interpolateParams: object = {};

  constructor(
    public activeModal: NgbActiveModal,
    public translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.title = this.title ? this.title : 'common.notification';
    this.body = this.body ? this.body : 'common.notificationContent';
    this.confirmButton = this.confirmButton
      ? this.confirmButton
      : 'common.action.confirm';
    this.btnConfirmClass = this.btnConfirmClass
      ? this.btnConfirmClass
      : 'btn btn-danger btn-sm rounded-0';
    this.isHiddenBtnClose = this.isHiddenBtnClose
      ? this.isHiddenBtnClose
      : false;
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.activeModal.close();
  }

  showContent(): string {
    return this.translateService.instant(this.body, this.interpolateParams);
  }
}
