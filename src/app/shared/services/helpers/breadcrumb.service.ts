import { Injectable } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { BehaviorSubject, Observable } from 'rxjs';
import { filter } from 'rxjs/operators';

@Injectable({ providedIn: 'root' })
export class BreadcrumbService {
  breadcrumbs: Observable<any[]>;

  private breadcrumbSubject: BehaviorSubject<any[]>;

  constructor(private router: Router, private route: ActivatedRoute) {
    this.breadcrumbSubject = new BehaviorSubject<any[]>(new Array<any>());

    this.breadcrumbs = this.breadcrumbSubject.asObservable();
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe((event) => {
        const breadcrumbs: any[] = [];
        let currentRoute = this.route.root;
        let url = '';
        do {
          const childrenRoutes = currentRoute.children;
          currentRoute = null as any;
          childrenRoutes.forEach((rout) => {
            if (rout.outlet === 'primary') {
              const routeSnapshot = rout.snapshot;
              url +=
                '/' +
                routeSnapshot.url.map((segment) => segment.path).join('/');
              if (url.match('/*/[0-9]+')) {
                const newUrl = url?.substring(0, url.lastIndexOf('/'));
                breadcrumbs.push({
                  label: rout.snapshot.data,
                  url: newUrl,
                });
              }

              breadcrumbs.push({
                label: rout.snapshot.data,
                url,
              });
              currentRoute = rout;
            }
          });
        } while (currentRoute);
        this.breadcrumbSubject.next({ ...[], ...breadcrumbs });
        return breadcrumbs;
      });
  }
}
