import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import {
  ENTITY_STATUS_MAP,
  MODAL_ACTION,
  NOTIFICATION_CUSTOMER_STATUS,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { ICustomer } from '@shared/models/customer.model';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';

@Component({
  selector: 'app-notification-customer',
  templateUrl: './notification-customer.component.html',
  styleUrls: ['./notification-customer.component.scss'],
})
export class NotificationCustomerComponent implements OnInit {
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  SYSTEM_RULES = SYSTEM_RULES;

  @Input()
  customers: ICustomer[] = [];

  @Input()
  isWaitingOrSend = false;

  @Output() customerEmit: EventEmitter<any> = new EventEmitter();

  constructor(
    private modalService: ModalService,
    private toastService: ToastrCustomService
  ) {}

  ngOnInit(): void {}

  delete(customer: ICustomer) {
    const modalData = {
      title: 'event.deleteReceiver',
      content: 'event.deleteReceiverContent',
      interpolateParams: { username: `<b>${customer.username || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.customers = this.customers.filter(
          (item) => item.customerId !== customer.customerId
        );
        this.customerEmit.emit(this.customers);

        this.toastService.success('common.success');
      }
    });
  }

  getEventStatus(notificationStatus: any): string {
    if (notificationStatus) {
      return NOTIFICATION_CUSTOMER_STATUS[notificationStatus]?.label;
    }
    return '';
  }

  getEventBadge(notificationStatus: any) {
    if (notificationStatus) {
      return NOTIFICATION_CUSTOMER_STATUS[notificationStatus]?.style;
    }
    return '';
  }
}
