import { Component, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  CALCULATE_MILLISECONDS_IN_A_YEAR,
  DATE_RANGE_STATISTICAL_LOAN_ONLINE,
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  FILE_EXTENSION,
  MAX_DATE_CONST,
  MODAL_ACTION,
  MOMENT_CONST,
  PAGINATION,
  PREMIUM_ACC_NUMBER_STATUS,
  PREMIUM_ACC_NUMBER_STATUS_MAP,
  RM_TYPE_CONST,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { SELECTED_CONST } from '@shared/constants/user.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ISearchWithPagination } from '@shared/models/base/base-request.model';
import { IPremiumAccNumber } from '@shared/models/premium-acc-number.modal';
import { IReferral } from '@shared/models/referral.model';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { PremiumAccNumberService } from '@shared/services/premium-acc-number.service';
import { ReferralService } from '@shared/services/referral.service';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';

@Component({
  selector: 'app-premium-account-number-sold',
  templateUrl: './premium-account-number-sold.component.html',
  styleUrls: ['./premium-account-number-sold.component.scss'],
})
export class PremiumAccountNumberSoldComponent implements OnInit {
  VALIDATORS = VALIDATORS;
  SYSTEM_RULES = SYSTEM_RULES;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  pageSizeOptions = PAGINATION.OPTIONS;
  ENTITY_STATUS = ENTITY_STATUS;
  SELECTED_CONST = SELECTED_CONST;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  PREMIUM_ACC_NUMBER_STATUS = PREMIUM_ACC_NUMBER_STATUS;
  PREMIUM_ACC_NUMBER_STATUS_MAP = PREMIUM_ACC_NUMBER_STATUS_MAP;
  MAX_DATE_CONST = MAX_DATE_CONST;

  data: IPremiumAccNumber[] = [];
  referrals: IReferral[] = [];
  maxToDate = new Date();
  isErrorStartDateGreaterEndDate = false;
  maxDate = new Date();
  formSearch: FormGroup = new FormGroup({});

  constructor(
    private formBuilder: FormBuilder,
    private modal: NgbModal,
    private modalService: ModalService,
    private toastService: ToastrCustomService,
    private premiumAccountNumberService: PremiumAccNumberService,
    private downloadService: DownloadService,
    private translateService: TranslateService,
    private referralService: ReferralService
  ) {
    const startDate = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDate = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.formSearch = this.formBuilder.group({
      keyword: '',
      status: null,
      referralIds: [],
      startDate: [CommonUtils.reverseDate(startDate), [Validators.required]],
      endDate: [
        CommonUtils.reverseDate(endDate),
        [Validators.required, this.isValidMaxDate],
      ],
      length: 0,
      timeZoneStr: '',
      pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
      pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
      hasPageable: true,
    });
  }

  ngOnInit(): void {
    this.onSearch();
    this.getReferral();
  }

  onReset() {
    this.formSearch.controls.keyword.reset();
    this.formSearch.controls.status.reset();
    this.formSearch.controls.referralIds.reset();

    const startDateReset = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDateReset = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.formSearch.controls.startDate.setValue(
      CommonUtils.reverseDate(startDateReset)
    );
    this.formSearch.controls.endDate.setValue(
      CommonUtils.reverseDate(endDateReset)
    );
    this.formSearch.controls.startDate.clearValidators();
    this.formSearch.controls.startDate.setValidators([Validators.required]);
    this.formSearch.controls.startDate.updateValueAndValidity();
  }

  onSearch(): void {
    this.formSearch
      .get('timeZoneStr')
      ?.setValue(Intl.DateTimeFormat().resolvedOptions().timeZone);
    if (this.formSearch.valid) {
      const params = this.formSearch.value;
      this.premiumAccountNumberService
        .searchSold(params)
        .subscribe((res: any) => {
          this.data = res.body.content;
          this.formSearch.controls.length.setValue(res.body.totalElements);
        });
    }
  }

  uploadFile(): void {}

  onSearchSubmit(): void {
    this.formSearch.controls.pageIndex.setValue(PAGINATION.PAGE_NUMBER_DEFAULT);
    this.formSearch.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    this.onSearch();
  }

  isValidMaxDate = (control: FormControl) => {
    const dateOfBirth = new Date(control.value).getTime();
    const maxDateOfBirth = new Date().getTime();
    return dateOfBirth > maxDateOfBirth ? { invalidMaxDate: true } : null;
  };

  dateValidator(value: number): any {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return value > DATE_RANGE_STATISTICAL_LOAN_ONLINE
        ? { invalidDate: true }
        : null;
    };
  }

  changeValidDate(): void {
    if (
      this.formSearch.controls.startDate.value &&
      this.formSearch.controls.endDate.value
    ) {
      if (this.formSearch.controls['endDate'].value) {
        this.maxDate = this.formSearch.controls['endDate'].value;
      }
      const startDate = CommonUtils.setStartTime(
        new Date(this.formSearch.controls.startDate.value).getTime()
      );
      const endDate = CommonUtils.setStartTime(
        new Date(this.formSearch.controls.endDate.value).getTime()
      );
      const dayMax =
        (endDate - startDate) / CALCULATE_MILLISECONDS_IN_A_YEAR.DAY;
      if (dayMax > DATE_RANGE_STATISTICAL_LOAN_ONLINE) {
        this.formSearch.controls.startDate.setValidators([
          Validators.required,
          this.dateValidator(dayMax),
          this.isValidMaxDate,
        ]);
        this.formSearch.controls.startDate.updateValueAndValidity();
      } else {
        this.formSearch.controls.startDate.clearValidators();
        this.formSearch.controls.startDate.setValidators([
          Validators.required,
          this.isValidMaxDate,
        ]);
        this.formSearch.controls.startDate.updateValueAndValidity();
      }
    }
  }

  onExport(): void {
    const bodySearch = this.formSearch.getRawValue();
    if (this.formSearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formSearch);
    } else {
      // get file name
      const fileName = this.translateService.instant(
        'template.premiumAccountNumberSoldReport'
      );
      const obFile = this.premiumAccountNumberService.exportSold({
        startDate: bodySearch.startDate,
        endDate: bodySearch.endDate,
        keyword: bodySearch.keyword,
        status: bodySearch.status,
        referralIds: bodySearch.referralIds,
        timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
      });

      this.downloadService.downloadFileWithObservableAndName(
        obFile,
        fileName,
        FILE_EXTENSION.XLSX
      );
    }
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formSearch.value.pageIndex,
      this.formSearch.value.pageSize
    );
  }

  onNotification(data?: IPremiumAccNumber) {
    const modalData = {
      title: 'model.premiumAccountNumber.notification',
      content: 'model.premiumAccountNumber.notificationContent',
      interpolateParams: {
        name: `<b>${data?.customer?.fullname || ''}</b>`,
        accountNumber: `<b>${data?.premiumAccNumber || ''}</b>`,
      },
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { premiumAccNumberId: data?.premiumAccNumberId };
        this.premiumAccountNumberService
          .notificationSold(params)
          .subscribe((res: any) => {
            this.toastService.success('common.action.notificationSuccess');
            this.onSearch();
          });
      }
    });
  }

  onChangePage(page: PageEvent) {
    this.formSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  getReferral(): void {
    const params = {
      hasPageable: false,
      types: [RM_TYPE_CONST.STAFF.code],
    };
    this.referralService
      .search(params as ISearchWithPagination)
      .subscribe((response: any) => {
        this.referrals = response.body.content;
        this.referrals = this.referrals
          .filter((item) => item.referralId != null)
          .map((value) => {
            if (
              value.rmCode === null ||
              value.rmCode === '' ||
              value.rmCode === undefined
            ) {
              return {
                referralId: value.referralId,
                referralCode: value.referralCode,
                rmCode: value.phoneNumber,
              };
            } else {
              return {
                referralId: value.referralId,
                referralCode: value.referralCode,
                rmCode: value.rmCode,
              };
            }
          });
      });
  }

  onSelectAll(value?: string) {
    const selectedReferralId = this.referrals.map((item) => item.referralId);
    switch (value) {
      case SELECTED_CONST.REFERRAL_ID:
        this.formSearch.get('referralIds')?.patchValue(selectedReferralId);
        break;
    }
  }

  onClearAll(value?: string) {
    switch (value) {
      case SELECTED_CONST.REFERRAL_ID:
        this.formSearch.get('referralIds')?.patchValue([]);
        break;
    }
  }

  customReferralSearchFn(term: string, item: IReferral) {
    if (item.rmCode) {
      term = term.replace(/ + /g, ' ');
      term = term.trim();
      term = term.toLocaleLowerCase();
      return item.rmCode.toLocaleLowerCase().indexOf(term) > -1;
    }
    return '';
  }
}
