import { Component, Input, OnInit } from '@angular/core';
import {
  <PERSON><PERSON><PERSON>er,
  FormGroup,
  Validators
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  ENTITY_STATUS,
  MODAL_ACTION
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ICurrency } from '@shared/models/currency.model';
import { ITransactionFeeType } from '@shared/models/transaction-fee-type.model';
import { CurrencyService } from '@shared/services/currency.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-currency-crud',
  templateUrl: './currency-crud.component.html',
  styleUrls: ['./currency-crud.component.scss'],
})
export class CurrencyCrudComponent implements OnInit {
  @Input() currencyId = 0;
  form: FormGroup = new FormGroup({});

  // input call api
  currency: ICurrency = {};
  reader: FileReader = new FileReader();
  action = '';
  hasFilter = false;
  isShowButton = false;
  servicePackTypes: ITransactionFeeType[] = [];

  ROUTER_UTILS = ROUTER_UTILS;
  ENTITY_STATUS = ENTITY_STATUS;
  VALIDATORS = VALIDATORS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  SYSTEM_RULES = SYSTEM_RULES;

  constructor(
    private formBuilder: FormBuilder,
    private currencyService: CurrencyService,
    private toastService: ToastrCustomService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private modalService: ModalService,
    public activeModal: NgbActiveModal,
    public translateService: TranslateService,
  ) {
  }

  ngOnInit(): void {
    if (this.action === ROUTER_ACTIONS.create) {
      this.initForm();
    } else {
      this.getDetail();
    }
  }

  /**
   * init form
   *
   * @param user User
   */
  initForm(currency?: ICurrency): void {
    this.form = this.formBuilder.group({
      code: [
        {
          value: currency?.code || '',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.POSITION_SHORT_NAME),
          Validators.minLength(VALIDATORS.LENGTH.CURRENCY_MIN_LENGTH),
          Validators.maxLength(
            VALIDATORS.LENGTH.TEXT_MAX_LENGTH
          ),
        ],
      ],
      name: [
        {
          value: currency?.name || '',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.NO_SPECIAL_CHARACTERS),
          Validators.minLength(VALIDATORS.LENGTH.CURRENCY_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.SMALL_TEXT_MAX_LENGTH),
        ],
      ],
      value: [
        {
          value: currency?.value || '',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [
          Validators.required,
          Validators.minLength(VALIDATORS.LENGTH.CURRENCY_VALUE_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.CURRENCY_VALUE_MAX_LENGTH),
        ],
      ]
    });
  }

  /**
   * back to monitor list
   */
  backToList(): void {
    this.activeModal.close();
  }

  /**
   * get detail user
   */
  getDetail(): void {
    if (this.currencyId) {
      this.currencyService.detail({currencyId: this.currencyId}).subscribe((res: any) => {
        this.currency = res.body;
        const data = res.body || undefined;
        this.initForm(data);
      });
    }
  }

  /**
   * Create: check validate file and data input if valid then call api create
   */
  onCreate(): void {
    // touched form
    if (this.form.invalid) {
      CommonUtils.markFormGroupTouched(this.form);
    }
    const data = this.form.getRawValue();
    if (this.form.valid) {
      this.currencyService.create({ ...data }).subscribe((res): void => {
        this.toastService.success('common.action.createSuccess');
        this.activeModal.close(MODAL_ACTION.CONFIRM.code);
      });
    }
  }

  /**
   * Update: check validate file and data input if valid then call api create
   */
  onUpdate(): void {
    // touched form
    if (this.form.invalid) {
      CommonUtils.markFormGroupTouched(this.form);
    }
    const data = this.form.getRawValue();
    data.currencyId = this.currency?.currencyId;
    if (this.form.valid) {
      this.currencyService.update(data).subscribe((res): void => {
        this.toastService.success('common.action.updateSuccess');
        this.activeModal.close(MODAL_ACTION.CONFIRM.code);
      });
    }
  }

  onEdit() {
    this.router.navigate([
      ROUTER_UTILS.currency.root,
      this.currency.currencyId,
      ROUTER_ACTIONS.update,
    ]);
  }

  actionOnUpdate(): void {
    this.action = ROUTER_ACTIONS.update;
    this.form.controls.name.enable();
    this.form.controls.minPrice.enable();
    this.form.controls.maxPrice.enable();
    this.form.controls.description.enable();
  }
}
