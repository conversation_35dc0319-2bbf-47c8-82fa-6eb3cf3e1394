import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>ener, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ni<PERSON> } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import {
  ENTITY_STATUS,
  EXTREME_EMAIL_EVOTEK,
  EXTREME_EMAIL_MBBANK,
  FILE_UPLOAD_EXTENSIONS,
  GENDER,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IDepartment } from '@shared/models/department.model';
import { IPosition } from '@shared/models/position.model';
import { IRole } from '@shared/models/role.model';
import { User } from '@shared/models/user.model';
import { DepartmentService } from '@shared/services/department.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { PositionService } from '@shared/services/position.service';
import { RoleService } from '@shared/services/role.service';
import { UserService } from '@shared/services/user.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-user-create-update',
  templateUrl: './user-create-update.component.html',
  styleUrls: ['./user-create-update.component.scss'],
})
export class UserCreateUpdateComponent implements OnInit, OnDestroy {
  formUser: FormGroup = new FormGroup({});

  // input call api
  userDTO: User = new User();
  // input data form control
  user: User = new User();
  reader: FileReader = new FileReader();
  roles: IRole[] = [];
  positions: IPosition[] = [];
  departments: IDepartment[] = [];
  // input: file upload to create
  fileUploads: any[] = [];
  eKycPicUrls: string[] = [];
  // default two file upload
  fileRequired: string[] = ['Logo'];
  action = '';
  currentValue = '';
  hasFilter = false;
  isUpdate = false;
  maxDateOfBirth = new Date();

  ROUTER_UTILS = ROUTER_UTILS;
  ENTITY_STATUS = ENTITY_STATUS;
  VALIDATORS = VALIDATORS;
  FILE_UPLOAD_EXTENSIONS = FILE_UPLOAD_EXTENSIONS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  GENDER_LIST = GENDER;
  SYSTEM_RULES = SYSTEM_RULES;

  constructor(
    private formBuilder: FormBuilder,
    private userService: UserService,
    private toastService: ToastrCustomService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private roleService: RoleService,
    private positionService: PositionService,
    private departmentService: DepartmentService
  ) {
    this.activatedRoute.data.subscribe((res) => {
      this.action = res.action;
    });
    // get id form paramater
    this.activatedRoute.paramMap.subscribe((res) => {
      const idParam = res.get('userId');
      if (idParam) {
        this.userDTO.userId = +idParam;
        this.isUpdate = true;
      }
    });
  }

  /**
   * remove storage
   */
  ngOnDestroy(): void {
    if (!this.hasFilter) {
      sessionStorage.removeItem(STORAGE_APP.USER);
    }
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.hasFilter = true;
  }

  ngOnInit(): void {
    this.getRoles();
    this.getDepartment();
    this.getPosition();
    if (this.isUpdate) {
      this.getDetail();
      // this.mappingRoles(this.roles);
    } else {
      this.initForm();
    }
  }

  /**
   * init form
   *
   * @param user User
   */
  initForm(user?: User): void {
    this.formUser = this.formBuilder.group({
      email: [
        {
          value: user?.email || '',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.EMAIL_MB),
          Validators.minLength(VALIDATORS.LENGTH.EMAIL_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.EMAIL_USER_MAX_LENGTH),
        ],
      ],
      fullname: [
        {
          value: user?.fullname || '',
          disabled:
            this.action === ROUTER_ACTIONS.detail ||
            this.action === ROUTER_ACTIONS.update,
        },
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.FULLNAME),
          Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
        ],
      ],
      username: [
        {
          value: user?.username || '',
          disabled: true,
        },
      ],
      dateOfBirth: [
        {
          value: CommonUtils.reverseDate(user?.dateOfBirth) || null,
          disabled:
            this.action === ROUTER_ACTIONS.detail ||
            this.action === ROUTER_ACTIONS.update,
        },
        [Validators.required, this.isValidMaxDate],
      ],
      gender: [
        {
          value: this.isUpdate ? user?.gender : null,
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
      ],
      phoneNumber: [
        {
          value: user?.phoneNumber || '',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.PHONE_MB),
          Validators.minLength(VALIDATORS.LENGTH.PHONE_NUMBER_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.PHONE_NUMBER_MAX_LENGTH),
        ],
      ],
      departmentId: [
        {
          value: user?.departmentId,
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [Validators.required],
      ],
      positionId: [
        {
          value: user?.positionId,
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [Validators.required],
      ],
      roleIds: [
        {
          value: user?.roleIds || [],
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [Validators.required],
      ],
      status: [
        {
          value: user?.status || 0,
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
      ],
    });
  }

  /**
   * back to monitor list
   */
  backToList(): void {
    this.hasFilter = true;
    this.router.navigate([ROUTER_UTILS.user.root]);
  }

  /**
   * get detail user
   */
  getDetail(): void {
    if (this.userDTO.userId) {
      this.userService.detail(this.userDTO).subscribe((res: any) => {
        this.user = res.body;
        const data = res.body || undefined;
        this.getRoles(this.user.roleIds);
        this.initForm(data);
      });
    }
  }

  onChangeGetUsername(value: any): void {
    const valueUsername = value.target.value.replace(EXTREME_EMAIL_MBBANK, '');

    if (this.action === ROUTER_ACTIONS.create) {
      this.formUser.controls.username.setValue(
        valueUsername.replace(EXTREME_EMAIL_EVOTEK, '')
      );
    }
  }

  /**
   * Create: check validate file and data input if valid then call api create
   */
  onCreate(): void {
    // check file, if is empty then set required file
    // if (this.fileUploads.length === 0) {
    //   this.formUser.controls.files.setValidators(Validators.required);
    //   this.formUser.controls.files.updateValueAndValidity();
    // }
    // this.validateFileLength(this.fileUploads);

    // touched form
    if (this.formUser.invalid) {
      CommonUtils.markFormGroupTouched(this.formUser);
    }
    const data = this.formUser.getRawValue();
    //  set data files = map fileUploads
    // data.files = this.fileUploads.map((fileX) => fileX.file);
    if (this.formUser.valid) {
      this.userService.create({ ...data }).subscribe((res): void => {
        this.router.navigate([ROUTER_UTILS.user.root]);
        this.toastService.success('common.action.createSuccess');
      });
    }
  }

  /**
   * Update: check validate file and data input if valid then call api create
   */
  onUpdate(): void {
    // check file, if is empty then set required file
    // if (this.fileUploads.length === 0) {
    //   this.formUser.controls.files.setValidators(Validators.required);
    //   this.formUser.controls.files.updateValueAndValidity();
    // }
    // this.validateFileLength(this.fileUploads);

    // touched form
    if (this.formUser.invalid) {
      CommonUtils.markFormGroupTouched(this.formUser);
    }
    const data = this.formUser.getRawValue();
    //  set data files = map fileUploads
    // data.files = this.fileUploads.map((fileX) => fileX.file);
    data.userId = this.user?.userId;
    if (this.formUser.valid) {
      this.userService.update(data).subscribe((res): void => {
        this.router.navigate([ROUTER_UTILS.user.root]);
        this.toastService.success('common.action.updateSuccess');
      });
    }
  }

  // /**
  //  * Upload picture
  //  * set file upload and check validate file
  //  *
  //  * @param fileSelected any
  //  */
  // onUploadPics(fileSelected: any): void {
  //   // set fileUpload
  //   this.fileUploads = fileSelected;
  //   // check file, if file have 1 then required file and check have 2 file
  //   this.validateFileLength(this.fileUploads);
  //   // const input: HTMLInputElement = fileSelected.target as HTMLInputElement;

  //   // if (input.files && input.files.length > 0) {
  //   //   for (const item of input.files) {
  //   //     try {
  //   //       this.reader.readAsDataURL(item);
  //   //       this.eKycPics.push(item);
  //   //     } catch (error) {
  //   //       this.toast.error('');
  //   //     }
  //   //   }
  //   // }
  // }

  getRoles(value?: any): void {
    this.roleService.getAll().subscribe((response: any) => {
      this.roles = response.body;
    });
  }

  getDepartment(): void {
    this.departmentService.getAll().subscribe((response: any) => {
      this.departments = response.body;
    });
  }

  getPosition(): void {
    this.positionService.getAll().subscribe((response: any) => {
      this.positions = response.body;
    });
  }

  isValidMaxDate = (control: FormControl) => {
    const dateOfBirth = new Date(control.value).getTime();
    const maxDateOfBirth = new Date(this.maxDateOfBirth).getTime();
    return dateOfBirth > maxDateOfBirth ? { invalidMaxDate: true } : null;
  };

  // /**
  //  * check validate file
  //  *
  //  * @param files any[]
  //  */
  // private validateFileLength(files: any[] = []): void {
  //   // check file, if file have 1 then required file and check have 2 file clear required
  //   const srcFile =
  //     files.map((file: any) => file.src).filter((src: any) => src != null) ||
  //     [];
  //   if (srcFile?.length < QUANTITY_UPLOAD.ICON_BANK) {
  //     this.formUser.controls.files.setValidators(Validators.required);
  //     this.formUser.controls.files.updateValueAndValidity();
  //   } else {
  //     this.formUser.controls.files.clearValidators();
  //     this.formUser.controls.files.updateValueAndValidity();
  //   }
  // }

  // /**
  //  * Change lang
  //  *
  //  * @param {string} lang
  //  * @memberof NavbarComponent
  //  */
  // onChangeLanguage(value: number[]): void {
  //   // if (value !== this.currentValue) {
  //   //   console.log(value);
  //   // }
  // }

  onSearchKeywordDepartment(term: string, item: IDepartment) {
    if (item.departmentName) {
      term = term.replace(/ + /g, ' ');
      term = term.trim();
      term = term.toLocaleLowerCase();
      return item.departmentName.toLocaleLowerCase().indexOf(term) > -1;
    }
    return '';
  }

  onSearchKeywordPosition(term: string, item: IPosition) {
    if (item.positionName) {
      term = term.replace(/ + /g, ' ');
      term = term.trim();
      term = term.toLocaleLowerCase();
      return item.positionName.toLocaleLowerCase().indexOf(term) > -1;
    }
    return '';
  }

  onSearchKeywordRole(term: string, item: IRole) {
    if (item.name) {
      term = term.replace(/ + /g, ' ');
      term = term.trim();
      term = term.toLocaleLowerCase();
      return item.name.toLocaleLowerCase().indexOf(term) > -1;
    }
    return '';
  }
}
