import { Injectable } from '@angular/core';
import { ICashout, ICashoutCreate } from '@shared/models/cashout.model';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';
@Injectable({
  providedIn: 'root',
})
export class PaymentService extends AbstractService {
  public resourceUrl = '/cashout-account';

  /**
   * search
   *
   * @param request IPosition
   * @returns Observable<EntityResponseType<IPosition[]>>
   */
  getAll(): Observable<EntityResponseType<ICashout>> {
    return super.get<EntityResponseType<ICashout[]>>(`${this.resourceUrl}`);
  }
  /**
   * API create
   *
   * @param request IPosition
   * @returns Observable<EntityResponseType<IPosition>>
   */
  create(request: ICashoutCreate): Observable<EntityResponseType<ICashout>> {
    return super.post<EntityResponseType<ICashout>>(
      `${this.resourceUrl}`,
      request
    );
  }

  /**
   * API update
   *
   * @param request IPosition
   * @returns Observable<EntityResponseType<IPosition>>
   */
  update(
    request: ICashoutCreate,
    id: number
  ): Observable<EntityResponseType<ICashout>> {
    return super.post<EntityResponseType<ICashout>>(
      `${this.resourceUrl}/update/${id}`,
      request
    );
  }

  /**
   * API detail
   *
   * @param request IPosition
   * @returns Observable<EntityResponseType<ILoanOnline>>
   */
  detail(id: number): Observable<EntityResponseType<ICashout>> {
    return super.get<EntityResponseType<ICashout>>(`${this.resourceUrl}/${id}`);
  }

  unLock(id: number): Observable<EntityResponseType<ICashout>> {
    return super.post<EntityResponseType<ICashout>>(
      `${this.resourceUrl}/activate/${id}`
    );
  }

  lock(id: number): Observable<EntityResponseType<ICashout>> {
    return super.post<EntityResponseType<ICashout>>(
      `${this.resourceUrl}/deactivate/${id}`
    );
  }

  deletePayment(id: number): Observable<EntityResponseType<ICashout>> {
    return super.post<EntityResponseType<ICashout>>(
      `${this.resourceUrl}/delete/${id}`
    );
  }

  manualAuto(
    request?: ICashoutCreate
  ): Observable<EntityResponseType<ICashout>> {
    return super.post<EntityResponseType<ICashout>>(
      `${this.resourceUrl}/manual-auto`,
      request
    );
  }
}
