<div class="row border-create">
  <h3>{{ "feeRate.titleFeeRate" | translate }}</h3>
  <div class="col-12 d-flex" [formGroup]="formFeeSearch">
    <div class="col-md-4">
      <div class="d-flex text-right mb-2 mt-1">
        <input
          trim
          formControlName="keyword"
          type="text"
          placeholder="{{ 'fee.keyword' | translate }}"
          class="form-control"
          [maxLength]="VALIDATORS.LENGTH.KEYWORD_MAX_LENGTH"
        />
        <button
          class="btn btn-red ml-2 btn-search text-center"
          (click)="onSearch()"
        >
          <i class="fa fa-search"></i>
        </button>
      </div>
    </div>
    <div class="col-md-8">
      <div class="d-block text-right mb-2 mt-1">
        <!-- <button class="btn btn-red mr-2" (click)="print()">{{ "common.action.print" | translate }}
        </button> -->
        <ng-container *ngIf="action !== ROUTER_ACTIONS.detail">
          <button
            class="btn btn-red mr-2"
            (click)="onCreate()"
            *hasPrivileges="SYSTEM_RULES.FEE_RATE_CREATE"
          >
            {{ "common.action.create" | translate }}
          </button>
        </ng-container>
      </div>
    </div>
  </div>
  <div class="table-responsive">
    <table class="table">
      <thead>
        <tr>
          <th class="text-center" scope="col">
            {{ "common.no" | translate }}
          </th>
          <th scope="col">{{ "feeRate.feeRateName" | translate }}</th>
          <th scope="col">{{ "feeRate.transactionAmount" | translate }}</th>
          <th
            *ngIf="configurationFeeType === CONFIGURATION_FEE_TYPE.FEE.code"
            scope="col"
            class="text-right"
          >
            {{ "feeRate.feeAmount" | translate }}
          </th>
          <th
            *ngIf="configurationFeeType === CONFIGURATION_FEE_TYPE.FEE.code"
            scope="col"
            class="text-right"
          >
            {{ "feeRate.VAT" | translate }}
          </th>
          <th
            *ngIf="
              configurationFeeType === CONFIGURATION_FEE_TYPE.DISCOUNT.code
            "
            scope="col"
            class="text-right"
          >
            {{ "feeRate.percentCk" | translate }}
          </th>
          <th
            *ngIf="
              configurationFeeType === CONFIGURATION_FEE_TYPE.DISCOUNT.code &&
              (transactionFeeTypeId === 5 || transactionFeeTypeId === 6)
            "
            scope="col"
            class="text-right"
          >
            {{ "feeRate.fixedDiscount" | translate }}
          </th>
          <th
            *ngIf="
              configurationFeeType === CONFIGURATION_FEE_TYPE.DISCOUNT.code
            "
            scope="col"
            class="text-right"
          >
            {{ "feeRate.ckMin" | translate }}
          </th>
          <th
            *ngIf="
              configurationFeeType === CONFIGURATION_FEE_TYPE.DISCOUNT.code
            "
            scope="col"
            class="text-right"
          >
            {{ "feeRate.ckMax" | translate }}
          </th>
          <th scope="col">{{ "currency.root" | translate }}</th>
          <th scope="col">
            {{ "feeRate.effectiveDate" | translate }}
          </th>
          <th scope="col">
            {{ "feeRate.expiredDate" | translate }}
          </th>
          <th class="text-center" scope="col">
            {{ "common.status" | translate }}
          </th>
          <th class="text-center" scope="col">
            {{ "common.action.label" | translate }}
          </th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let dataItem of feeRates; let i = index">
          <td class="text-center">{{ fillIndexItem(i) }}</td>
          <td>{{ dataItem.feeRateName }}</td>
          <td>
            {{
              dataItem.transactionAmountMin +
                " - " +
                dataItem.transactionAmountMax
            }}
          </td>
          <td
            *ngIf="configurationFeeType === CONFIGURATION_FEE_TYPE.FEE.code"
            class="text-right"
          >
            {{ dataItem.feeAmount }}
          </td>
          <td
            *ngIf="configurationFeeType === CONFIGURATION_FEE_TYPE.FEE.code"
            class="text-right"
          >
            {{ dataItem.vat }}
          </td>
          <td
            *ngIf="
              configurationFeeType === CONFIGURATION_FEE_TYPE.DISCOUNT.code
            "
            class="text-right"
          >
            {{ dataItem.discountPercent }}
          </td>
          <td
            *ngIf="
              configurationFeeType === CONFIGURATION_FEE_TYPE.DISCOUNT.code &&
              (transactionFeeTypeId === 5 || transactionFeeTypeId === 6)
            "
            class="text-right"
          >
            {{ dataItem.discountFixed }}
          </td>
          <td
            *ngIf="
              configurationFeeType === CONFIGURATION_FEE_TYPE.DISCOUNT.code
            "
            class="text-right"
          >
            {{
              dataItem.discountPercent && dataItem.transactionAmountMin
                ? dataItem.transactionAmountMin -
                  dataItem.transactionAmountMin *
                    (dataItem.discountPercent / 100) -
                  (dataItem?.discountFixed || 0)
                : dataItem?.transactionAmountMin
            }}
          </td>
          <td
            *ngIf="
              configurationFeeType === CONFIGURATION_FEE_TYPE.DISCOUNT.code
            "
            class="text-right"
          >
            {{
              dataItem.discountPercent && dataItem.transactionAmountMax
                ? dataItem.transactionAmountMax -
                  dataItem.transactionAmountMax *
                    (dataItem.discountPercent / 100) -
                  (dataItem?.discountFixed || 0)
                : dataItem?.transactionAmountMax
            }}
          </td>
          <td>{{ dataItem.currency }}</td>
          <td>{{ dataItem.effectiveAt }}</td>
          <td>{{ dataItem.expiredAt }}</td>
          <td class="text-center">
            <span
              class="badge"
              [ngClass]="ENTITY_STATUS_MAP[dataItem.status || 0].style"
              >{{
                ENTITY_STATUS_MAP[dataItem.status || 0].label | translate
              }}</span
            >
          </td>

          <td class="text-center">
            <button
              class="btn px-1 py-0"
              ngbTooltip="{{ 'common.action.detail' | translate }}"
              (click)="detail(dataItem)"
            >
              <i class="bi bi-eye mb-color" aria-hidden="true"></i>
            </button>
            <ng-container *ngIf="action !== ROUTER_ACTIONS.detail">
              <button
                class="btn px-1 py-0"
                ngbTooltip="{{ 'common.action.update' | translate }}"
                (click)="update(dataItem)"
                *hasPrivileges="SYSTEM_RULES.FEE_RATE_WRITE"
              >
                <i class="fa fa-edit mb-color" aria-hidden="true"></i>
              </button>
              <button
                class="btn px-1 py-0"
                [ngbTooltip]="
                  dataItem.status === ENTITY_STATUS_CONST.INACTIVE.code
                    ? ('common.action.unlock' | translate)
                    : ('common.action.lock' | translate)
                "
                (click)="activeAndInactive(dataItem)"
                *hasPrivileges="
                  dataItem.status === ENTITY_STATUS_CONST.ACTIVE.code
                    ? SYSTEM_RULES.FEE_RATE_LOCK
                    : SYSTEM_RULES.FEE_RATE_UNLOCK
                "
              >
                <i
                  [className]="
                    dataItem.status === ENTITY_STATUS_CONST.ACTIVE.code
                      ? 'fa fa-lock mb-color'
                      : 'fa fa-unlock mb-color'
                  "
                  aria-hidden="true"
                ></i>
              </button>
              <!--*ngIf="dataItem.approvalStatus === 1 && (dataItem.approvalType === 1 || dataItem.approvalType === 2)"-->
              <button
                class="btn px-1 py-0"
                ngbTooltip="{{ 'common.action.delete' | translate }}"
                (click)="delete(dataItem)"
                *hasPrivileges="SYSTEM_RULES.FEE_RATE_DELETE"
              >
                <i class="fa fa-trash mb-color" aria-hidden="true"></i>
              </button>
            </ng-container>
          </td>
        </tr>
      </tbody>
    </table>
    <div class="row d-block text-center m-0" *ngIf="feeRates?.length === 0">
      <img
        src="/assets/dist/img/icon/empty.svg"
        height="120"
        alt="no_search_result"
      />
      <p class="text-center mb-5">
        {{ "common.no_search_result" | translate }}
      </p>
    </div>
    <div *ngIf="feeRates.length">
      <mat-paginator
        [length]="totalRecord"
        [pageSize]="pageSize"
        [pageSizeOptions]="pageSizeOptions"
        (page)="onChangePage($event)"
        aria-label="Select page"
      >
      </mat-paginator>
    </div>
  </div>
</div>
