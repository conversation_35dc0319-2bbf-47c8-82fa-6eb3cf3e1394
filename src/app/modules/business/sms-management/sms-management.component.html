<div class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formSearch" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-lg-3">
            <div class="form-group">
              <label>{{ "model.smsManage.fromDate" | translate }}</label>
              <mat-form-field appearance="fill" class="date-picker">
                <input
                  (dateInput)="fromDateInput()"
                  matInput
                  [matDatepicker]="fromDate"
                  formControlName="fromDate"
                  [placeholder]="MOMENT_CONST.FORMAT_DATE_PLACEHOLDER"
                  max="{{
                    !formSearch.controls['toDate'].value
                      ? (maxDate | date : 'yyyy-MM-dd')
                      : (formSearch.controls['toDate'].value
                        | date : 'yyyy-MM-dd')
                  }}"
                  dateTransform
                />
                <mat-datepicker-toggle
                  matSuffix
                  [for]="fromDate"
                ></mat-datepicker-toggle>
                <mat-datepicker #fromDate></mat-datepicker>
              </mat-form-field>
              <small
                *ngIf="
                  formSearch.get('fromDate')?.errors?.invalidFromDate &&
                  formSearch.get('fromDate')?.touched
                "
                class="text-danger"
                >{{
                  "error.sms.dateTime.fromDateCurrentInvalid" | translate
                }}</small
              >
            </div>
          </div>
          <div class="col-lg-3">
            <div class="form-group">
              <label>{{ "model.smsManage.toDate" | translate }}</label>
              <mat-form-field appearance="fill" class="date-picker">
                <input
                  (dateInput)="toDateInput()"
                  matInput
                  [matDatepicker]="toDate"
                  formControlName="toDate"
                  [placeholder]="MOMENT_CONST.FORMAT_DATE_PLACEHOLDER"
                  [max]="maxDate"
                  min="{{
                    formSearch.controls['fromDate'].value | date : 'yyyy-MM-dd'
                  }}"
                  dateTransform
                />
                <mat-datepicker-toggle
                  matSuffix
                  [for]="toDate"
                ></mat-datepicker-toggle>
                <mat-datepicker #toDate></mat-datepicker>
              </mat-form-field>
              <small
                *ngIf="
                  formSearch.get('toDate')?.errors?.invalidToDate &&
                  formSearch.get('toDate')?.touched
                "
                class="text-danger"
                >{{ "error.sms.dateTime.toDateInvalid" | translate }}</small
              >
              <small
                *ngIf="
                  formSearch.get('toDate')?.errors?.invalidToDateCurrent &&
                  formSearch.get('toDate')?.touched
                "
                class="text-danger"
                >{{
                  "error.sms.dateTime.toDateCurrentInvalid" | translate
                }}</small
              >
            </div>
          </div>
          <div class="col-md-2 col-lg-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
              (click)="onReset()"
            >
              <div class="btn-reset">
                <i class="bi bi-arrow-clockwise" type="button"> </i>
              </div>
            </div>
            <div class="">
              <button
                class="btn btn-search mr-2"
                type="submit"
                *hasPrivileges="SYSTEM_RULES.SMS_READ"
              >
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div
          class="d-flex justify-content-between align-items-center text-right mb-2 mt-4"
        >
          <h5>{{ "model.smsManage.title" | translate }}</h5>
          <button
            class="btn btn-red mb-2"
            type="button"
            *hasPrivileges="SYSTEM_RULES.SMS_EXPORT"
            (click)="exportFile()"
          >
            {{ "common.action.export" | translate }}
          </button>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th class="text-center">
                {{ "model.smsManage.no" | translate }}
              </th>
              <th class="text-left">
                {{ "model.smsManage.smsType" | translate }}
              </th>
              <th class="text-right">
                {{ "model.smsManage.amountSendSucceed" | translate }}
              </th>
              <th class="text-right">
                {{ "model.smsManage.amountSendFailed" | translate }}
              </th>
              <th class="text-right">
                {{ "model.smsManage.amountSend" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of data; let i = index">
              <td class="text-center">
                {{ fillIndexItem(i) }}
              </td>
              <td class="text-left">
                {{ item.typeStr }}
              </td>
              <td class="text-right">
                {{ item.quantitySuccess }}
              </td>
              <td class="text-right">
                {{ item.quantityFail }}
              </td>
              <td class="text-right">
                {{ item.total }}
              </td>
            </tr>
          </tbody>
        </table>
        <div
          class="row d-block text-center m-0 no-search-result-container"
          *ngIf="data?.length === 0"
        >
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
