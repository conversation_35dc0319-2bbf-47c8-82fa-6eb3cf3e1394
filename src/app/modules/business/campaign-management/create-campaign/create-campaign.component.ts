import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDatepickerInputEvent } from '@angular/material/datepicker';
import { Router } from '@angular/router';
import {
  DATE_CONSTANT,
  FILE_UPLOAD_EXTENSIONS,
  MODAL_ACTION,
  SCREEN_SHOW_BANNER,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ICampaign } from '@shared/models/campaign.model';
import { IImageUrls } from '@shared/models/request/image-type.search';
import { CampaignService } from '@shared/services/campaign.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import { Subscription } from 'rxjs';
@Component({
  selector: 'app-create-update-container',
  templateUrl: './create-campaign.component.html',
  styleUrls: ['./create-campaign.component.scss'],
})
export class CreateCampaignComponent implements OnInit, OnDestroy {
  formCampaign: FormGroup = new FormGroup({});
  campaign: ICampaign = {};
  fileUploads: any[] = [];

  maxDate = new Date();
  startDateMin?: Date;
  campaignId = 0;
  position?: string;

  action = ROUTER_ACTIONS.create;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  FILE_UPLOAD_EXTENSIONS = FILE_UPLOAD_EXTENSIONS;
  VALIDATORS = VALIDATORS;
  SCREEN_SHOW_BANNER = SCREEN_SHOW_BANNER;
  MODAL_ACTION = MODAL_ACTION;
  DATE_CONSTANT = DATE_CONSTANT;

  constructor(
    private fb: FormBuilder,
    private toastService: ToastrCustomService,
    private campaignService: CampaignService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.formCampaign = this.fb.group({
      campaignName: [
        '',
        [
          Validators.required,
          Validators.maxLength(VALIDATORS.LENGTH.CAMPAIGN_NAME_MAX_LENGTH),
          Validators.pattern(VALIDATORS.PATTERN.CAMPAIGN_NAME),
          Validators.pattern(VALIDATORS.PATTERN.CAMPAIGN_NAME_ASCII),
        ],
      ],
      description: [
        '',
        [
          Validators.maxLength(
            VALIDATORS.LENGTH.CAMPAIGN_DESCRIPTION_MAX_LENGTH
          ),
        ],
      ],
      startDate: [null, [Validators.required]],
      endDate: [null, [Validators.required]],
      position: [null, [Validators.required]],
    });
  }

  ngOnDestroy(): void {}

  onUploadPics(fileSelected: FormGroup[]): void {
    // set fileUpload
    const fileArr = [];
    for (const item of fileSelected) {
      fileArr.push(item.value);
    }
    this.fileUploads = fileArr;
  }

  onRemovePics(event: any): void {
    this.fileUploads = event;
  }

  onAddImage(event: FormGroup[]): void {
    this.fileUploads = [];
    for (const item of event) {
      this.fileUploads.push(item.value);
    }
  }

  onSave(): void {
    const data = this.formCampaign.value;
    const fileUploadFilter = this.fileUploads.filter((item) => {
      return item.file || item.embedLink;
    });
    const bannerNull = [];
    for (const item of this.fileUploads) {
      if (!item.file && !item.embedLink) {
        bannerNull.push(item);
      }
    }
    if (this.formCampaign.invalid) {
      CommonUtils.markFormGroupTouched(this.formCampaign);
      return;
    }
    if (fileUploadFilter.length < 1) {
      this.toastService.error('error.campaign.required.bannerScreen');
      return;
    }
    if (fileUploadFilter.length > 1) {
      if (!this.checkImageValid(fileUploadFilter)) {
        this.toastService.error('error.campaign.required.bannerScreen');
        return;
      }
      if (this.checkEmbedLinkValid(fileUploadFilter)) {
        this.toastService.error('error.campaign.required.embedLinkImage');
        return;
      }
      if (this.checkEmbedPattern(fileUploadFilter)) {
        return;
      }
    }
    if (fileUploadFilter.length === 1) {
      if (!this.checkImageValid(fileUploadFilter)) {
        this.toastService.error('error.campaign.required.bannerScreen');
        return;
      }
      if (this.checkEmbedLinkValid(fileUploadFilter)) {
        this.toastService.error('error.campaign.required.embedLinkImage');
        return;
      }
      if (this.checkEmbedPattern(fileUploadFilter)) {
        return;
      }
    }
    if (bannerNull.length >= 1) {
      this.toastService.error('error.campaign.required.embedLinkImage');
      return;
    }
    const fileImage: IImageUrls[] = [];
    const embedLinkImage: string[] = [];

    for (const item of this.fileUploads) {
      if (item.file) {
        fileImage.push(item.file);
      }
      if (item.embedLink) {
        embedLinkImage.push(item.embedLink);
      }
    }

    data.files = fileImage;
    data.embedLink = embedLinkImage;
    this.campaignService
      .create(data)
      .subscribe(() => {
        this.router.navigate([ROUTER_UTILS.sysManage.campaign.root]);
        this.toastService.success('model.campaign.createSuccess');
      });
  }

  checkImageValid(files: IImageUrls[]): boolean {
    return files.some((item: IImageUrls) => {
      return item.file;
    });
  }

  checkEmbedLinkValid(files: IImageUrls[]): any {
    return files.find((item: IImageUrls) => {
      return (item.file && !item.embedLink) || (!item.src && item.embedLink);
    });
  }

  checkEmbedPattern(files: IImageUrls[]): any {
    const pattern = new RegExp(VALIDATORS.PATTERN.HTTP_PATTERN);
    return files.find((item: IImageUrls) => {
      if (item.embedLink) {
        return !pattern.test(item.embedLink);
      } else {
        return;
      }
    });
  }

  backToList(): void {
    this.router.navigate([ROUTER_UTILS.sysManage.campaign.root]);
  }

  getPlacesShowCampaign(event: Event): void {
    const target = event.target as HTMLInputElement;
    const valueScreen = target.value;
    if (valueScreen === this.SCREEN_SHOW_BANNER.LOGIN.label) {
      this.formCampaign
        .get('position')
        ?.setValue(this.SCREEN_SHOW_BANNER.LOGIN.label);
      this.position = this.SCREEN_SHOW_BANNER.LOGIN.label;
      this.fileUploads = [];
      return;
    }
    if (valueScreen === this.SCREEN_SHOW_BANNER.HOMEPAGE.label) {
      this.formCampaign
        .get('position')
        ?.setValue(this.SCREEN_SHOW_BANNER.HOMEPAGE.label);
      this.position = this.SCREEN_SHOW_BANNER.HOMEPAGE.label;
      this.fileUploads = [];
      return;
    }
    if (valueScreen === this.SCREEN_SHOW_BANNER.OTHER.label) {
      this.formCampaign
        .get('position')
        ?.setValue(this.SCREEN_SHOW_BANNER.OTHER.label);
      this.position = this.SCREEN_SHOW_BANNER.OTHER.label;
      this.fileUploads = [];
      return;
    }
  }

  validateTimeStart(event: MatDatepickerInputEvent<Date>): void {
    if (event.value) {
      const dateMin = new Date(event.value);
      this.startDateMin = new Date(dateMin.setDate(dateMin.getDate() + 1));
    }
  }
}
