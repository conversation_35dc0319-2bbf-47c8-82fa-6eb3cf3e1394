<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="backToList()" />
      <h5>{{ "role.titleDetail" | translate }}</h5>
    </div>
    <div class="col-md-12">
      <div class="row">
        <h2 class="title-create">{{ "role.titleDetail" | translate }}</h2>
      </div>
      <div class="row border-create">
        <h3>{{ "role.infor" | translate }}</h3>
        <div class="col-md-12">
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <label
                  >{{ "model.role.name" | translate
                  }}<span class="text-danger">*</span></label
                >
                <input
                  trim
                  type="text"
                  disabled
                  class="w-100"
                  class="form-control"
                  [value]="role?.name"
                />
              </div>
            </div>
            <div class="col-md-9">
              <div class="form-group">
                <label>{{ "model.role.description" | translate }}</label>
                <input
                  trim
                  type="text"
                  disabled
                  class="w-100"
                  class="form-control"
                  [value]="role?.description ? role?.description : ''"
                />
              </div>
            </div>
          </div>
          <div class="row mb-2">
            <div class="col-md-3">
              <div class="form-group">
                <label
                  >{{ "model.role.creator" | translate
                  }}<span class="text-danger">*</span></label
                >
                <input
                  trim
                  type="text"
                  disabled
                  class="w-100"
                  class="form-control"
                  [value]="role?.createdBy ? role.createdBy : ''"
                />
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label>{{ "model.role.fromDate" | translate }}</label>
                <input
                  trim
                  type="text"
                  [value]="role.createdDate"
                  readonly
                  class="w-100"
                  class="form-control"
                />
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label>{{ "model.role.updater" | translate }}</label>
                <input
                  trim
                  type="text"
                  disabled
                  class="w-100"
                  class="form-control"
                  [value]="role?.lastModifiedBy ? role.lastModifiedBy : ''"
                />
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label>{{ "model.role.lastModifiedDate" | translate }}</label>
                <input
                  trim
                  type="text"
                  readonly
                  [value]="role.lastModifiedDate"
                  class="w-100"
                  class="form-control"
                />
              </div>
            </div>
          </div>
          <div class="row mb-2">
            <div class="col-md-3">
              <div class="form-group">
                <label>{{ "common.status" | translate }}</label>
                <ng-select
                  appearance="outline"
                  [readonly]="roleStatus !== null || roleStatus !== ''"
                  [searchable]="false"
                  placeholder="{{
                    'common.appSelectOption.status' | translate
                  }}"
                  [clearable]="true"
                  [(ngModel)]="roleStatus"
                >
                  <ng-option
                    [value]="item.code"
                    *ngFor="let item of ENTITY_STATUS"
                  >
                    {{ item.label | translate }}
                  </ng-option>
                </ng-select>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-12">
          <cdk-accordion class="example-accordion">
            <cdk-accordion-item
              *ngFor="let item of groupPrivileges; let index = index"
              #accordionItem="cdkAccordionItem"
              class="example-accordion-item"
              role="button"
              tabindex="0"
              [attr.id]="'accordion-header-' + index"
              [attr.aria-expanded]="accordionItem.expanded"
              [attr.aria-controls]="'accordion-body-' + index"
            >
              <div
                class="example-accordion-item-header"
                (click)="accordionItem.toggle()"
              >
                <h5 class="m-0">{{ item?.name }}</h5>
                <span class="example-accordion-item-description">
                  <i
                    class="fas fa-{{
                      accordionItem.expanded ? 'angle-up' : 'angle-down'
                    }}"
                  ></i>
                </span>
              </div>
              <div
                class="example-accordion-item-body"
                role="region"
                [style.display]="accordionItem.expanded ? '' : 'none'"
                [attr.id]="'accordion-body-' + index"
                [attr.aria-labelledby]="'accordion-header-' + index"
              >
                <div class="row">
                  <div
                    class="col-md-3"
                    *ngFor="let privilege of item.privileges; let i = index"
                  >
                    <mat-checkbox
                      class="check-box-role"
                      [(ngModel)]="privilege.checked"
                      [disabled]="true"
                      class="example-margin"
                    >
                      {{ privilege?.description }}
                    </mat-checkbox>
                  </div>
                </div>
              </div>
            </cdk-accordion-item>
          </cdk-accordion>
        </div>
      </div>
      <div class="d-block text-center mb-5 mt-4">
        <button
          class="btn btn-white mr-2"
          (click)="editRole()"
          *hasPrivileges="SYSTEM_RULES.ROLE_WRITE"
        >
          {{ "common.action.update" | translate }}
        </button>
        <button class="btn btn-red" (click)="backToList()">
          {{ "common.action.back" | translate }}
        </button>
      </div>
    </div>
  </div>
</section>
