import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import {
  FILE_TYPE,
  FILE_UPLOAD_EXTENSIONS,
  MODAL_ACTION,
  RATE_TYPE,
} from '@shared/constants/app.constants';
import { LANGUAGES } from '@shared/constants/language.constants';
import { STORAGE_LANGUAGES } from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import {
  IFeeSchedule,
  IFeeScheduleDetail,
} from '@shared/models/FeeSchedule.model';
import { IRate } from '@shared/models/rate.model';
import { IFileEntrySearch } from '@shared/models/request/file-entry.search';
import { FeeScheduleService } from '@shared/services/FeeSchedule.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { IconService } from '@shared/services/icon.service';
import CommonUtils from '@shared/utils/common-utils';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils/router.utils';
import { LocalStorageService } from 'ngx-webstorage';

@Component({
  selector: 'app-fee-schedule',
  templateUrl: './fee-schedule.component.html',
  styleUrls: ['./fee-schedule.component.scss'],
})
export class FeeScheduleComponent implements OnInit {
  formCreate: FormGroup = new FormGroup({});
  reader: FileReader = new FileReader();
  fileUploads: any[] = [];
  fileRequired: string[] = ['Image'];
  fileDelete: any[] = [];
  imageUrls: any[] = [];
  FILE_UPLOAD_EXTENSIONS = FILE_UPLOAD_EXTENSIONS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  maxLengthContent = VALIDATORS.LENGTH.CONTENT_NOTIFICATION;
  VALIDATORS = VALIDATORS;
  ROUTER_UTILS = ROUTER_UTILS;
  RATE_TYPE = RATE_TYPE;
  LANGUAGES = Object.values(LANGUAGES);
  language = '';
  id?: number;
  action?: string;
  data?: IFeeSchedule[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private feeScheduleService: FeeScheduleService,
    private toastService: ToastrCustomService,
    private localStorage: LocalStorageService,
    private iconService: IconService,
    private modalService: ModalService
  ) {
    this.activatedRoute.paramMap.subscribe((res) => {
      const idParam = res.get('id');
      if (idParam) {
        this.id = +idParam;
      }
    });
    this.activatedRoute.data.subscribe((res) => {
      this.action = res.action;
    });
    this.language =
      this.router?.getCurrentNavigation()?.extras.state?.language ||
      this.localStorage.retrieve(STORAGE_LANGUAGES);
  }

  ngOnInit(): void {
    this.initForm();
    if (this.id) {
      this.feeScheduleService
        .detail({ feeScheduleId: this.id })
        .subscribe((res: any) => {
          this.data = res.body.details;
          this.getImages(res.body.details);
        });
    }
  }

  getData(data?: any) {
    return data?.find(
      (item: IFeeScheduleDetail) => item.language === this.language
    );
  }

  getImages(data?: any) {
    if (this.getData(data)) {
      const searchFile: IFileEntrySearch = {};
      searchFile.classPk = this.getData(data).feeScheduleDetailId;
      searchFile.fileEntryId = this.getData(data).icon?.id;
      searchFile.normalizeName = this.getData(data).icon?.normalizeName;
      searchFile.className = FILE_TYPE.FEE_SCHEDULE;
      searchFile.resource = FILE_TYPE.FEE_SCHEDULE;
      this.iconService.getIcon(searchFile).subscribe((responsive: any) => {
        CommonUtils.blobToBase64(responsive.body).then((base64) => {
          this.imageUrls = [
            { src: base64, name: null, id: this.getData(data)?.icon?.id },
          ];
        });
      });
    }
    this.initForm(this.getData(data));
  }

  initForm(data?: IRate): void {
    this.formCreate = this.formBuilder.group({
      title: [
        {
          value: data?.title || '',
          disabled: this.action === ROUTER_ACTIONS.detail ? true : false,
        },
        [
          Validators.required,
          Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
        ],
      ],
    });
  }

  onChangeLang(language: string) {
    if (
      this.action === ROUTER_ACTIONS.create &&
      (this.formCreate.value.title || this.fileUploads.length > 0) &&
      language !== this.language
    ) {
      const modalData = {
        title: 'model.news.modal.title',
        content: 'model.news.modal.content',
      };
      this.modalService.confirm(modalData).then((result) => {
        if (result === MODAL_ACTION.CONFIRM.code) {
          this.getDataLang(language);
        }
      });
    } else if (language !== this.language) {
      this.getDataLang(language);
    }
  }

  getDataLang(language: string) {
    this.language = language;
    this.formCreate.reset();
    this.fileUploads = [];
    this.imageUrls = [];
    this.getImages(this.data);
  }

  onUploadPics(fileSelected: any): void {
    this.fileUploads = fileSelected;
  }

  onRemoveImage(image: any): void {
    this.fileDelete.push(image.id);
  }

  backToList() {
    this.router.navigate([ROUTER_UTILS.feeSchedule.root]);
  }

  onSubmit() {
    if (this.formCreate.invalid) {
      CommonUtils.markFormGroupTouched(this.formCreate);
      return;
    }
    const body = this.formCreate.getRawValue();
    body.fileId =
      (this.fileUploads[0]?.src === null && this.fileDelete.length > 0) ||
      (this.fileUploads.length === 0 && this.fileDelete.length > 0)
        ? this.getData(this.data)?.icon?.id
        : '';
    body.file = this.fileUploads.map((fileX) => fileX.file);
    body.language = this.language;
    body.feeScheduleId = this.id || '';
    body.feeScheduleDetailId =
      this.getData(this.data)?.feeScheduleDetailId || '';
    const serviceEvent =
      this.action === ROUTER_ACTIONS.update
        ? this.feeScheduleService.update(body)
        : this.feeScheduleService.create(body);
    serviceEvent.subscribe((res: any) => {
      this.action === ROUTER_ACTIONS.create
        ? this.router.navigate(
            [
              ROUTER_UTILS.feeSchedule.root,
              res?.body?.feeScheduleId,
              ROUTER_ACTIONS.update,
            ],
            {
              state: {
                language: this.language,
              },
            }
          )
        : this.router.navigate([ROUTER_UTILS.feeSchedule.root]);

      this.toastService.success(
        this.action === ROUTER_ACTIONS.update
          ? 'common.action.updateSuccess'
          : 'common.action.createSuccess'
      );
    });
  }
}
