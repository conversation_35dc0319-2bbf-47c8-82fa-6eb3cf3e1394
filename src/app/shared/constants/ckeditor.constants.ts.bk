import { environment } from '../../../environments/environment';

const url = 'https://ckeditor.com/apps/ckfinder/3.4.5/ckfinder.html';
const urlCore = 'https://ckeditor.com/apps/ckfinder/3.4.5/core/connector/php/connector.php?command=QuickUpload';
// const urlCore = environment.apiUrl + '/api/public/file/upload';

export const CKEDITOR_TOOLBAR = {
  // toolbar: [
  //   {name: 'document', items: ['Source', '-', 'NewPage', 'Preview', '-', 'Templates']},
  //   {name: 'styles', items: ['Styles']},
  //   {name: 'styles', items: ['Format', 'Font', 'FontSize']},
  //   {name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText', 'PasteFromWord', '-', 'Undo', 'Redo']},
  //   {name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'RemoveFormat', 'CopyFormatting']},
  //   {name: 'colors', items: ['TextColor', 'BGColor']},
  //   {name: 'align', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock']},
  //   {name: 'links', items: ['Link', 'Unlink']},
  //   {name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', '-', 'Blockquote']},
  //   {name: 'insert', items: ['Image', 'Table']},
  //   {name: 'tools', items: ['Maximize']},
  //   {name: 'editing', items: ['Scayt']}
  // ],
  toolbar: [
    { name: 'document', groups: [ 'mode', 'document', 'doctools' ], items: [ 'Source', '-', 'Save', 'NewPage', 'ExportPdf', 'Preview', 'Print', '-', 'Templates' ] },
    { name: 'clipboard', groups: [ 'clipboard', 'undo' ], items: [ 'Cut', 'Copy', 'Paste', 'PasteText', 'PasteFromWord', '-', 'Undo', 'Redo' ] },
    { name: 'editing', groups: [ 'find', 'selection', 'spellchecker' ], items: [ 'Find', 'Replace', '-', 'SelectAll', '-', 'Scayt' ] },
    { name: 'forms', items: [ 'Form', 'Checkbox', 'Radio', 'TextField', 'Textarea', 'Select', 'Button', 'ImageButton', 'HiddenField' ] },
    '/',
    { name: 'basicstyles', groups: [ 'basicstyles', 'cleanup' ], items: [ 'Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript', '-', 'CopyFormatting', 'RemoveFormat' ] },
    { name: 'paragraph', groups: [ 'list', 'indent', 'blocks', 'align', 'bidi' ], items: [ 'NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', '-', 'Blockquote', 'CreateDiv', '-', 'JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock', '-', 'BidiLtr', 'BidiRtl', 'Language' ] },
    { name: 'links', items: [ 'Link', 'Unlink', 'Anchor' ] },
    { name: 'insert', items: [ 'Image', 'Flash', 'Table', 'HorizontalRule', 'Smiley', 'SpecialChar', 'PageBreak', 'Iframe' ] },
    '/',
    { name: 'styles', items: [ 'Styles', 'Format', 'Font', 'FontSize' ] },
    { name: 'colors', items: [ 'TextColor', 'BGColor' ] },
    { name: 'tools', items: [ 'Maximize', 'ShowBlocks' ] },
    { name: 'others', items: [ '-' ] },
    { name: 'about', items: [ 'saveButton', 'About' ] }
  ],
  customConfig: '',
  disallowedContent: 'img{width,height,float}',
  extraAllowedContent: 'img[width,height,align]',

  // Enabling extra plugins, available in the full-all preset: https://ckeditor.com/cke4/presets-all
  extraPlugins: 'emoji,pastetools,removeformat,colorbutton,font,justify,tableresize,uploadimage,divarea',
  // removeDialogTabs: 'image:advanced;link:advanced',
  // height: 800,
  contentsCss: ['https://cdn.ckeditor.com/4.8.0/full-all/contents.css'],
  bodyClass: 'document-editor',
  format_tags: 'p;h1;h2;h3;pre',
  allowedContent: true,
  withCredentials: true,
  forcePasteAsPlainText: true,
  withAccessControlAllowOrigin: '*',
  headers: {
    'X-CSRF-TOKEN': 'CSFR-Token',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Credentials': true,
    'Access-Control-Allow-Methods': 'POST',
    'Access-Control-Expose-Headers': 'Authorization,Link,X-Total-Count,X-Total-Active-Count',
    // 'Authorization': 'Bearer ' + token
  },
  // filebrowserBrowseUrl: url,
  // filebrowserImageBrowseUrl: url,
  filebrowserUploadUrl: urlCore + '&type=Files',
  // filebrowserImageUploadUrl: urlCore + '&type=Images',
  // filebrowserImageUploadUrl: urlCore,
  uploadUrl: urlCore + '&type=Files&responseType=json',
};
