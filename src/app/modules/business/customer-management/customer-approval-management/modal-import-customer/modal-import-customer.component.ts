import { HttpStatusCode } from '@angular/common/http';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  FILE_EXTENSION,
  customErrorCode,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { FILE_TEXT_PLAIN } from '@shared/constants/file.constant';
import { CustomerService } from '@shared/services/customer.service';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS } from '@shared/utils';
import { saveAs } from 'file-saver';

@Component({
  selector: 'app-modal-import-customer',
  templateUrl: './modal-import-customer.component.html',
  styleUrls: ['./modal-import-customer.component.scss'],
})
export class ModalImportCustomerComponent implements OnInit {
  formImport: FormGroup = new FormGroup({});
  selectedFileName = '';
  fileImport: any;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  @ViewChild('fileInput') fileInput: any;

  ngOnInit(): void {
    this.initForm();
  }

  constructor(
    private fb: FormBuilder,
    public translateService: TranslateService,
    public activeModal: NgbActiveModal,
    private customerService: CustomerService,
    private toastService: ToastrCustomService,
    private downloadService: DownloadService
  ) {}

  initForm() {
    this.formImport = this.fb.group({
      uploadFile: ['', Validators.required],
    });
  }

  downloadTemplateImport(): void {
    const fileName = this.translateService.instant('template.customerTemplate');
    const obFile = this.customerService.downloadTemplate();
    this.downloadService.downloadFileWithObservableAndName(
      obFile,
      fileName,
      FILE_EXTENSION.XLSX
    );
  }

  onFileSelected(event: any) {
    if (event?.target?.files && event?.target?.files.length > 0) {
      this.selectedFileName = event?.target?.files[0].name;
      this.fileImport = event?.target?.files;
    } else {
      return;
    }
  }

  importFile(): void {
    const [fileImport] = this.fileImport;
    if (!fileImport) {
      return;
    }
    this.customerService.importDataTemplate(fileImport).subscribe(
      (res) => {
        this.toastService.success('common.action.uploadSuccess');
        this.activeModal.close();
      },
      (err) => {
        if (
          err.status === HttpStatusCode.BadRequest &&
          err.error.errorCode === customErrorCode.errorCodeImportCustomer
        ) {
          this.saveStringToFile(
            err.error.title,
            'template.errorImportCustomer'
          );
        }
      }
    );
  }

  private saveStringToFile(content: string, fileName: string): void {
    const blob = new Blob([content], { type: FILE_TEXT_PLAIN });
    const fileNameTranslate = this.translateService.instant(fileName);
    saveAs(blob, fileNameTranslate);
    return;
  }

  deleteFile() {
    // Clear the file input
    this.fileInput.nativeElement.value = '';
    this.selectedFileName = '';
    this.fileImport = null;
  }
}
