<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="onBackToList()" />
      <h5>
        {{
          isUpdateScreen
            ? ("customerRegisterManagement.updateTitle" | translate)
            : ("customerRegisterManagement.createTitle" | translate)
        }}
      </h5>
    </div>
    <form [formGroup]="customerCreateForm" *ngIf="customerCreateForm">
      <div class="col-md-12">
        <div class="row">
          <h2 class="title-create">
            {{
              (routerAction === ROUTER_ACTIONS.create
                ? "customerRegisterManagement.customerCreate.title"
                : "customerRegisterManagement.customerApproval.detail.editInfomation"
              ) | translate
            }}
          </h2>
        </div>
        <div class="row border-create">
          <h3>
            {{ "customerRegisterManagement.customerCreate.info" | translate }}
          </h3>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label
                    >{{ "model.customer.idCardType" | translate
                    }}<span class="text-danger"> *</span></label
                  >
                  <!-- <select
                    formControlName="idCardTypeId"
                    class="w-100"
                    class="form-control"
                  >
                    <option
                      *ngFor="let item of idCardTypes"
                      [value]="item.idCardTypeId"
                    >
                      {{ item.name }}
                    </option>
                  </select> -->
                  <ng-select
                    appearance="outline"
                    [searchable]="false"
                    placeholder="{{
                      'common.appSelectOption.idCardType' | translate
                    }}"
                    [clearable]="true"
                    formControlName="idCardTypeId"
                  >
                    <ng-option
                      [value]="item.idCardTypeId"
                      *ngFor="let item of idCardTypes"
                    >
                      {{ item.name }}
                    </ng-option>
                  </ng-select>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('idCardTypeId')?.errors
                        ?.required &&
                      customerCreateForm.get('idCardTypeId')?.touched
                    "
                  >
                    {{ "error.customerCreate.required.idCardType" | translate }}
                  </small>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label
                    >{{ "model.customer.idCardNumber" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    trim
                    [maxLength]="VALIDATORS.LENGTH.IDENTIFICATION_MAX_LENGTH"
                    formControlName="idCardNumber"
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{
                      'model.customer.idCardNumber' | placeholder
                    }}"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('idCardNumber')?.errors
                        ?.required &&
                      customerCreateForm.get('idCardNumber')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.required.idCardNumber" | translate
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('idCardNumber')?.errors
                        ?.minlength &&
                      customerCreateForm.get('idCardNumber')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.minLength.idCardNumber"
                        | translate
                          : {
                              param: VALIDATORS.LENGTH.IDENTIFICATION_MIN_LENGTH
                            }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('idCardNumber')?.errors
                        ?.maxlength &&
                      customerCreateForm.get('idCardNumber')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.maxLength.idCardNumber"
                        | translate
                          : {
                              param: VALIDATORS.LENGTH.IDENTIFICATION_MAX_LENGTH
                            }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('idCardNumber')?.errors?.pattern &&
                      customerCreateForm.get('idCardNumber')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.pattern.idCardNumber" | translate
                    }}
                  </small>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label
                    >{{ "model.customer.phoneNumber" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    trim
                    [maxLength]="VALIDATORS.LENGTH.PHONE_NUMBER_MAX_LENGTH"
                    formControlName="phoneNumber"
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{
                      'model.customer.phoneNumber' | placeholder
                    }}"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('phoneNumber')?.errors?.required &&
                      customerCreateForm.get('phoneNumber')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.required.phoneNumber" | translate
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('phoneNumber')?.errors
                        ?.minlength &&
                      customerCreateForm.get('phoneNumber')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.minLength.phoneNumber"
                        | translate
                          : { param: VALIDATORS.LENGTH.PHONE_NUMBER_MIN_LENGTH }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('phoneNumber')?.errors
                        ?.maxlength &&
                      customerCreateForm.get('phoneNumber')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.minLength.phoneNumber"
                        | translate
                          : { param: VALIDATORS.LENGTH.PHONE_NUMBER_MAX_LENGTH }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('phoneNumber')?.errors?.pattern &&
                      customerCreateForm.get('phoneNumber')?.touched
                    "
                  >
                    {{ "error.customerCreate.pattern.phoneNumber" | translate }}
                  </small>
                </div>
              </div>
              <div class="col-md-3">
                <div
                  class="d-block text-center mt-4"
                  *ngIf="!checkConfirmSearch"
                >
                  <button
                    type="button"
                    style="height: 35px"
                    class="btn btn-red w-100 btn-sm"
                    (click)="identityVerify()"
                  >
                    {{ "common.action.research" | translate }}
                  </button>
                </div>
                <div
                  class="d-block text-center mt-4"
                  *ngIf="checkConfirmSearch"
                >
                  <button
                    type="button"
                    style="height: 35px"
                    class="btn btn-red w-100 btn-sm"
                    (click)="confirmSearch()"
                  >
                    {{ "common.action.researchAgain" | translate }}
                  </button>
                </div>
              </div>
            </div>
            <!-- <hr class="mt-0"/> -->
          </div>
          <div class="col-12" *ngIf="accountExistedT24">
            <div class="callout callout-warning text-left mt-2">
              <div
                class="footer"
                [innerHTML]="
                  displayContent(
                    'customerRegisterManagement.customerCreate.existedT24Warning'
                  )
                "
              ></div>
            </div>
          </div>
          <div class="col-md-12">
            <hr class="divider" />
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label
                    >{{ "model.customer.fullname" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    accents
                    trim
                    [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
                    formControlName="fullname"
                    type="text"
                    class="w-100"
                    class="form-control border-input"
                    placeholder="{{ 'model.customer.fullname' | placeholder }}"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('fullname')?.errors?.required &&
                      customerCreateForm.get('fullname')?.touched
                    "
                  >
                    {{ "error.customerCreate.required.fullname" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('fullname')?.errors?.minlength &&
                      customerCreateForm.get('fullname')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.minLength.fullname"
                        | translate
                          : { param: VALIDATORS.LENGTH.FULL_NAME_MIN_LENGTH }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('fullname')?.errors?.maxlength &&
                      customerCreateForm.get('fullname')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.maxLength.fullname"
                        | translate
                          : { param: VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('fullname')?.errors?.pattern &&
                      customerCreateForm.get('fullname')?.touched
                    "
                  >
                    {{ "error.customerCreate.pattern.fullname" | translate }}
                  </small>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label
                    >{{ "model.customer.dateOfBirth" | translate
                    }}<span class="text-danger" *ngIf="displayDanger()"
                      >*</span
                    ></label
                  >
                  <!-- <input
                    trim
                    formControlName="dateOfBirth"
                    type="date"
                    class="w-100"
                    class="form-control border-input"
                    max="{{ maxDateOfBirth | date: 'yyyy-MM-dd' }}"
                  /> -->
                  <mat-form-field appearance="fill" class="date-picker">
                    <input
                      matInput
                      [matDatepicker]="dateOfBirth"
                      formControlName="dateOfBirth"
                      placeholder="DD/MM/YYYY"
                      [max]="maxDateOfBirth"
                      (change)="changeValidDateMaxDateOfBirth()"
                      (dateInput)="changeValidDateMaxDateOfBirth()"
                      dateTransform
                    />
                    <mat-datepicker-toggle
                      matSuffix
                      [for]="dateOfBirth"
                    ></mat-datepicker-toggle>
                    <mat-datepicker #dateOfBirth></mat-datepicker>
                  </mat-form-field>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('dateOfBirth')?.errors?.required &&
                      customerCreateForm.get('dateOfBirth')?.touched
                    "
                  >
                    {{ "error.customerCreate.required.dob" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('dateOfBirth')?.errors
                        ?.invalidMaxDateOfBirth &&
                      customerCreateForm.get('dateOfBirth')?.touched
                    "
                  >
                    {{ "error.customerCreate.pattern.dateOfBirth" | translate }}
                  </small>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label
                    >{{ "model.customer.placeOfOrigin" | translate
                    }}<span class="text-danger" *ngIf="displayDanger()"
                      >*</span
                    ></label
                  >
                  <input
                    trim
                    [maxLength]="VALIDATORS.LENGTH.PLACE_OF_ORIGIN_MAX_LENGTH"
                    formControlName="placeOfOrigin"
                    type="text"
                    class="w-100"
                    class="form-control border-input"
                    placeholder="{{
                      'model.customer.placeOfOrigin' | placeholder
                    }}"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('placeOfOrigin')?.errors
                        ?.required &&
                      customerCreateForm.get('placeOfOrigin')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.required.placeOfOrigin" | translate
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('placeOfOrigin')?.errors
                        ?.maxlength &&
                      customerCreateForm.get('placeOfOrigin')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.maxLength.placeOfOrigin"
                        | translate
                          : {
                              param:
                                VALIDATORS.LENGTH.PLACE_OF_ORIGIN_MAX_LENGTH
                            }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('placeOfOrigin')?.errors
                        ?.minlength &&
                      customerCreateForm.get('placeOfOrigin')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.minLength.placeOfOrigin"
                        | translate
                          : { param: VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('placeOfOrigin')?.errors
                        ?.pattern &&
                      customerCreateForm.get('placeOfOrigin')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.pattern.placeOfOrigin" | translate
                    }}
                  </small>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label
                    >{{ "model.customer.gender" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <ng-select
                    appearance="outline"
                    [searchable]="false"
                    [clearable]="false"
                    placeholder="{{ 'common.placeholder.gender' | translate }}"
                    formControlName="gender"
                  >
                    <ng-option
                      [value]="item.code"
                      *ngFor="let item of GENDER_LIST"
                    >
                      {{ item.label | translate }}
                    </ng-option>
                  </ng-select>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('gender')?.errors?.required &&
                      customerCreateForm.get('gender')?.touched
                    "
                  >
                    {{ "error.customerCreate.required.gender" | translate }}
                  </small>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label
                    >{{ "model.customer.nationCode" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <!-- <input trim formControlName="nationCode" type="text" class="w-100" class="form-control border-input"
                    placeholder="{{
                      'model.customer.nationCode' | placeholder
                    }}" /> -->
                  <ng-select
                    appearance="outline"
                    [searchable]="false"
                    [clearable]="false"
                    placeholder="{{
                      'model.customer.nationCode' | placeholder : 'select'
                    }}"
                    formControlName="nationCode"
                  >
                    <ng-option
                      [value]="item.nationCode"
                      *ngFor="let item of nations"
                    >
                      {{ item.name }}
                    </ng-option>
                  </ng-select>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('nationCode')?.errors?.required &&
                      customerCreateForm.get('nationCode')?.touched
                    "
                  >
                    {{ "error.customerCreate.required.nationCode" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('nationCode')?.errors?.maxlength &&
                      customerCreateForm.get('nationCode')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.maxLength.nationCode"
                        | translate
                          : { param: VALIDATORS.LENGTH.ADDRESS_MAX_LENGTH }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('nationCode')?.errors?.minlength &&
                      customerCreateForm.get('nationCode')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.minLength.nationCode"
                        | translate
                          : { param: VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('nationCode')?.errors?.pattern &&
                      customerCreateForm.get('nationCode')?.touched
                    "
                  >
                    {{ "error.customerCreate.pattern.nationCode" | translate }}
                  </small>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{
                    "model.customer.nationCodeOther" | translate
                  }}</label>
                  <!-- <input trim formControlName="nationCodeOther" type="text" class="w-100"
                    class="form-control border-input" placeholder="{{
                      'model.customer.nationCodeOther' | placeholder
                    }}" /> -->
                  <ng-select
                    appearance="outline"
                    [searchable]="false"
                    [clearable]="false"
                    placeholder="{{
                      'model.customer.nationCodeOther' | placeholder : 'select'
                    }}"
                    formControlName="nationCodeOther"
                  >
                    <ng-option
                      [value]="item.nationCode"
                      *ngFor="let item of nations"
                    >
                      {{ item.name }}
                    </ng-option>
                  </ng-select>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('nationCodeOther')?.errors
                        ?.maxlength &&
                      customerCreateForm.get('nationCodeOther')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.maxLength.nationCodeOther"
                        | translate
                          : { param: VALIDATORS.LENGTH.ADDRESS_MAX_LENGTH }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('nationCodeOther')?.errors
                        ?.minlength &&
                      customerCreateForm.get('nationCodeOther')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.minLength.nationCodeOther"
                        | translate
                          : { param: VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('nationCodeOther')?.errors
                        ?.pattern &&
                      customerCreateForm.get('nationCodeOther')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.pattern.nationCodeOther" | translate
                    }}
                  </small>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label
                    >{{ "model.customer.issueDate" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <!-- <input
                    trim
                    formControlName="issueDate"
                    type="date"
                    class="w-100"
                    class="form-control border-input"
                    max="{{ maxIssueDate | date: 'yyyy-MM-dd' }}"
                    min="{{ minIssueDate | date: 'yyyy-MM-dd' }}"
                  /> -->
                  <mat-form-field appearance="fill" class="date-picker">
                    <input
                      matInput
                      [matDatepicker]="issueDate"
                      formControlName="issueDate"
                      placeholder="DD/MM/YYYY"
                      min="{{ minIssueDate | date : 'yyyy-MM-dd' }}"
                      [max]="maxIssueDate"
                      (change)="changeValidDateMin()"
                      (dateInput)="changeValidDateMin()"
                      dateTransform
                    />
                    <mat-datepicker-toggle
                      matSuffix
                      [for]="issueDate"
                    ></mat-datepicker-toggle>
                    <mat-datepicker #issueDate></mat-datepicker>
                  </mat-form-field>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('issueDate')?.errors?.required &&
                      customerCreateForm.get('issueDate')?.touched
                    "
                  >
                    {{ "error.customerCreate.required.issueDate" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('issueDate')?.errors
                        ?.invalidMinDate &&
                      customerCreateForm.get('issueDate')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.pattern.issueDateMin" | translate
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('issueDate')?.errors
                        ?.invalidMaxDate &&
                      customerCreateForm.get('issueDate')?.touched
                    "
                  >
                    {{
                      "error.maxDateCurrent"
                        | translate
                          : {
                              param: "model.customer.issueDate" | translate
                            }
                    }}
                  </small>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label
                    >{{ "model.customer.issuePlace" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    trim
                    [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
                    formControlName="issuePlace"
                    type="text"
                    class="w-100"
                    class="form-control border-input"
                    placeholder="{{
                      'model.customer.issuePlace' | placeholder
                    }}"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('issuePlace')?.errors?.required &&
                      customerCreateForm.get('issuePlace')?.touched
                    "
                  >
                    {{ "error.customerCreate.required.issuePlace" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('issuePlace')?.errors?.minlength &&
                      customerCreateForm.get('issuePlace')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.minLength.issuePlace"
                        | translate
                          : { param: VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('issuePlace')?.errors?.maxlength &&
                      customerCreateForm.get('issuePlace')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.minLength.issuePlace"
                        | translate
                          : { param: VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH }
                    }}
                  </small>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label
                    >{{ "model.customer.placeOfResidence" | translate
                    }}<span class="text-danger" *ngIf="displayDanger()"
                      >*</span
                    ></label
                  >
                  <input
                    trim
                    [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
                    formControlName="placeOfResidence"
                    type="text"
                    class="w-100"
                    class="form-control border-input"
                    placeholder="{{
                      'model.customer.placeOfResidence' | placeholder
                    }}"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('placeOfResidence')?.errors
                        ?.required &&
                      customerCreateForm.get('placeOfResidence')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.required.placeOfResidence"
                        | translate
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('placeOfResidence')?.errors
                        ?.minlength &&
                      customerCreateForm.get('placeOfResidence')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.minLength.placeOfResidence"
                        | translate
                          : { param: VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('placeOfResidence')?.errors
                        ?.maxlength &&
                      customerCreateForm.get('placeOfResidence')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.minLength.placeOfResidence"
                        | translate
                          : { param: VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH }
                    }}
                  </small>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label>{{
                    "model.customer.placeOfResidenceOutCountry" | translate
                  }}</label>
                  <input
                    trim
                    [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
                    formControlName="placeOfResidenceOutCountry"
                    type="text"
                    class="w-100"
                    class="form-control border-input"
                    placeholder="{{
                      'model.customer.placeOfResidenceOutCountry' | placeholder
                    }}"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('placeOfResidenceOutCountry')
                        ?.errors?.minlength &&
                      customerCreateForm.get('placeOfResidenceOutCountry')
                        ?.touched
                    "
                  >
                    {{
                      "error.customerCreate.minLength.placeOfResidenceOutCountry"
                        | translate
                          : { param: VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('placeOfResidenceOutCountry')
                        ?.errors?.maxlength &&
                      customerCreateForm.get('placeOfResidenceOutCountry')
                        ?.touched
                    "
                  >
                    {{
                      "error.customerCreate.minLength.placeOfResidenceOutCountry"
                        | translate
                          : { param: VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH }
                    }}
                  </small>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label>{{
                    "model.customer.currentAddress" | translate
                  }}</label>
                  <input
                    trim
                    [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
                    formControlName="currentAddress"
                    type="text"
                    class="w-100"
                    class="form-control border-input"
                    placeholder="{{
                      'model.customer.currentAddress' | placeholder
                    }}"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('currentAddress')?.errors
                        ?.required &&
                      customerCreateForm.get('currentAddress')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.required.currentAddress" | translate
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('currentAddress')?.errors
                        ?.minlength &&
                      customerCreateForm.get('currentAddress')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.minLength.currentAddress"
                        | translate
                          : { param: VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('currentAddress')?.errors
                        ?.maxlength &&
                      customerCreateForm.get('currentAddress')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.minLength.currentAddress"
                        | translate
                          : { param: VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH }
                    }}
                  </small>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.customer.phoneContact" | translate }}</label>
                  <input
                    trim
                    [maxLength]="VALIDATORS.LENGTH.PHONE_NUMBER_MAX_LENGTH"
                    formControlName="phoneContact"
                    type="text"
                    class="w-100"
                    class="form-control border-input"
                    placeholder="{{
                      'model.customer.phoneContact' | placeholder
                    }}"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('phoneContact')?.errors
                        ?.minlength &&
                      customerCreateForm.get('phoneContact')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.minLength.phoneContact"
                        | translate
                          : { param: VALIDATORS.LENGTH.PHONE_NUMBER_MIN_LENGTH }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('phoneContact')?.errors
                        ?.maxlength &&
                      customerCreateForm.get('phoneContact')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.maxLength.phoneContact"
                        | translate
                          : { param: VALIDATORS.LENGTH.PHONE_NUMBER_MAX_LENGTH }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('phoneContact')?.errors?.pattern &&
                      customerCreateForm.get('phoneContact')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.pattern.phoneContact" | translate
                    }}
                  </small>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.customer.email" | translate }}</label>
                  <input
                    trim
                    [maxLength]="VALIDATORS.LENGTH.EMAIL_MAX_LENGTH"
                    formControlName="email"
                    type="text"
                    class="w-100"
                    class="form-control border-input"
                    placeholder="{{ 'model.customer.email' | placeholder }}"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('email')?.errors?.pattern &&
                      customerCreateForm.get('email')?.touched
                    "
                  >
                    {{ "error.customerCreate.pattern.email" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('email')?.errors?.maxlength &&
                      customerCreateForm.get('email')?.touched
                    "
                  >
                    {{ "error.customerCreate.maxLength.email" | translate }}
                  </small>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-3">
            <div class="form-group">
              <label>{{ "model.customer.residentStatus" | translate }}</label>
              <ng-select
                appearance="outline"
                [searchable]="false"
                [clearable]="false"
                placeholder="{{
                  'common.placeholder.residentStatus' | translate
                }}"
                formControlName="residentStatus"
              >
                <ng-option
                  [value]="item.value"
                  *ngFor="let item of RESIDENT_STATUS"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label>{{ "model.customer.maritalStatus" | translate }}</label>
              <ng-select
                appearance="outline"
                [searchable]="false"
                [clearable]="false"
                placeholder="{{
                  'common.placeholder.maritalStatus' | translate
                }}"
                formControlName="maritalStatus"
              >
                <ng-option
                  [value]="item.value"
                  *ngFor="let item of MARITAL_STATUS"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label>{{ "model.customer.job" | translate }}</label>
              <input
                trim
                [maxLength]="VALIDATORS.LENGTH.JOB_MAX_LENGTH"
                formControlName="job"
                type="text"
                class="w-100"
                class="form-control border-input"
                placeholder="{{ 'model.customer.job' | placeholder }}"
              />
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  customerCreateForm.get('job')?.errors?.maxlength &&
                  customerCreateForm.get('job')?.touched
                "
              >
                {{
                  "error.customerCreate.maxLength.job"
                    | translate : { param: VALIDATORS.LENGTH.JOB_MAX_LENGTH }
                }}
              </small>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label>{{ "model.customer.position" | translate }}</label>
              <input
                trim
                [maxLength]="VALIDATORS.LENGTH.POSITION_MAX_LENGTH"
                formControlName="position"
                type="text"
                class="w-100"
                class="form-control border-input"
                placeholder="{{ 'model.customer.position' | placeholder }}"
              />
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  customerCreateForm.get('position')?.errors?.maxlength &&
                  customerCreateForm.get('position')?.touched
                "
              >
                {{
                  "error.customerCreate.minLength.position"
                    | translate
                      : { param: VALIDATORS.LENGTH.POSITION_MAX_LENGTH }
                }}
              </small>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label>{{ "model.customer.workplace" | translate }}</label>
              <input
                trim
                [maxLength]="VALIDATORS.LENGTH.SMALL_TEXT_MAX_LENGTH"
                formControlName="workplace"
                type="text"
                class="w-100"
                class="form-control border-input"
                placeholder="{{ 'model.customer.workplace' | placeholder }}"
              />
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  customerCreateForm.get('workplace')?.errors?.maxlength &&
                  customerCreateForm.get('workplace')?.touched
                "
              >
                {{
                  "error.customerCreate.maxLength.workplace"
                    | translate
                      : { param: VALIDATORS.LENGTH.SMALL_TEXT_MAX_LENGTH }
                }}
              </small>
            </div>
          </div>
          <div class="col-md-3" *ngIf="displayPremiumAccount()">
            <div class="form-group">
              <label>{{
                "model.customer.premiumAccountNumber" | translate
              }}</label>
              <input
                trim
                formControlName="premiumAccountNumber"
                numbersOnly
                type="text"
                class="w-100"
                class="form-control"
                appAutoValidate
                placeholder="{{
                  'model.customer.premiumAccountNumber' | placeholder
                }}"
              />
            </div>
          </div>
          <div class="col-md-3" *ngIf="displayPremiumAccount()">
            <div class="form-group">
              <label>{{ "model.customer.paymentPrice" | translate }}</label>
              <input
                trim
                formControlName="price"
                type="text"
                class="w-100"
                class="form-control bold-text"
                appAutoValidate
                placeholder="{{ 'model.customer.paymentPrice' | placeholder }}"
              />
            </div>
          </div>
          <div class="col-md-12">
            <label
              >{{ "model.customer.identityCardImages" | translate
              }}<span class="text-danger" *ngIf="displayDanger()"
                >*</span
              ></label
            >
            <app-upload-images
              (imageUploaded)="onUploadPics($event)"
              [fileExtension]="FILE_UPLOAD_EXTENSIONS.AVATAR"
              [fileRequired]="fileRequired"
              [checkDisabled]="disabledFile"
              [imageUrls]="imageUrls"
              (imageRemoved)="onRemoveImage($event)"
              [type]="routerAction"
            ></app-upload-images>
            <small
              class="form-text text-danger noti-small"
              *ngIf="
                customerCreateForm.get('files')?.errors?.required &&
                customerCreateForm.get('files')?.touched
              "
            >
              {{ "error.customerCreate.required.picture" | translate }}
            </small>
          </div>
          <div class="col-md-12">
            <div class="row">
              <!-- <div class="col-md-3">
                <div class="form-group">
                  <label
                    >{{ "model.customer.customerSectorId" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input trim
                    formControlName="customerSectorId"
                    type="text"
                    class="w-100"
                    class="form-control border-input"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('customerSectorId')?.errors
                        ?.required &&
                      customerCreateForm.get('customerSectorId')?.touched
                    "
                  >
                    {{
                      "error.customerCreate.required.customerSectorId"
                        | translate
                    }}
                  </small>
                </div>
              </div> -->
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.customer.cif" | translate }}</label>
                  <input
                    trim
                    formControlName="cif"
                    type="text"
                    class="w-100"
                    class="form-control border-input"
                    placeholder="{{ 'model.customer.cif' | placeholder }}"
                  />
                </div>
              </div>
              <div
                class="col-md-3"
                *ngIf="routerAction === ROUTER_ACTIONS.update"
              >
                <div class="form-group">
                  <label>{{
                    "customerRegisterManagement.sector" | translate
                  }}</label>
                  <ng-select
                    appearance="outline"
                    [searchable]="false"
                    [clearable]="false"
                    placeholder="{{
                      'customerRegisterManagement.sector' | translate
                    }}"
                    formControlName="customerSectorId"
                  >
                    <ng-option
                      [value]="item.value"
                      *ngFor="let item of CUSTOMER_SECTOR"
                    >
                      {{ item.value }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
              <div
                class="col-md-3"
                *ngIf="routerAction === ROUTER_ACTIONS.update"
              >
                <label>{{ "model.customer.oldSector" | translate }}</label>
                <input
                  type="text"
                  class="form-control"
                  formControlName="customerOldSectorId"
                />
              </div>
              <!-- <div class="col-md-3">
                <div class="form-group">
                  <label>{{ 'model.customer.shopCode' | translate}}<span class="text-danger">*</span></label>
                  <input trim formControlName="shopCode" type="text" class="w-100" class="form-control border-input">
                </div>
              </div> -->
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.customer.staffCode" | translate }}</label>
                  <input
                    trim
                    [maxLength]="VALIDATORS.LENGTH.PHONE_NUMBER_MAX_LENGTH"
                    formControlName="staffCode"
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{ 'model.customer.staffCode' | placeholder }}"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('staffCode')?.errors?.pattern &&
                      customerCreateForm.get('staffCode')?.touched
                    "
                  >
                    {{ "error.customerDetail.pattern.staffCode" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('staffCode')?.errors?.minlength &&
                      customerCreateForm.get('staffCode')?.touched
                    "
                  >
                    {{
                      "error.customerDetail.minLength.staffCode"
                        | translate
                          : { param: VALIDATORS.LENGTH.PHONE_NUMBER_MIN_LENGTH }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      customerCreateForm.get('staffCode')?.errors?.maxlength &&
                      customerCreateForm.get('staffCode')?.touched
                    "
                  >
                    {{
                      "error.customerDetail.maxLength.staffCode"
                        | translate
                          : { param: VALIDATORS.LENGTH.PHONE_NUMBER_MAX_LENGTH }
                    }}
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="d-block text-center mb-5 mt-4">
          <!-- <button type="button" class="btn btn-white mr-2" data-toggle="modal" (click)="onReset()">
            {{ "common.action.reset" | translate }}
          </button> -->
          <button
            type="button"
            class="btn btn-white mr-2"
            (click)="onBackToList()"
          >
            {{ "common.action.back" | translate }}
          </button>
          <button
            type="button"
            class="btn btn-red"
            (click)="onSenApproval()"
            *hasPrivileges="[
              SYSTEM_RULES.CUSTOMER_CREATE,
              SYSTEM_RULES.CUSTOMER_WRITE
            ]"
          >
            {{ "common.action.sendApproval" | translate }}
          </button>
        </div>
      </div>
    </form>
  </div>
</section>
