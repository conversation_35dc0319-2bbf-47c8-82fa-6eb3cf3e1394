import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  FINALIZATION_METHOD_CONST,
  MOMENT_CONST,
  SAVING_ACCOUNT_STATUS_CONST,
  SAVING_TYPE_CONST,
  SAVING_TYPE_MAP,
} from '@shared/constants/app.constants';
import { ISaving } from '@shared/models/saving.model';
import { SavingService } from '@shared/services/saving.service';
import { ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';

@Component({
  selector: 'app-saving-detail',
  templateUrl: './saving-detail.component.html',
  styleUrls: ['./saving-detail.component.scss'],
})
export class SavingDetailComponent implements OnInit, OnDestroy {
  savingAccountId = 0;
  formDetail: FormGroup = new FormGroup({});
  detailData: ISaving = {};

  SAVING_TYPE_CONST = SAVING_TYPE_CONST;
  SAVING_TYPE_MAP = SAVING_TYPE_MAP;
  ENTITY_STATUS = ENTITY_STATUS;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  FINALIZATION_METHOD_CONST = FINALIZATION_METHOD_CONST;
  SAVING_ACCOUNT_STATUS_CONST = SAVING_ACCOUNT_STATUS_CONST;
  constructor(
    private router: Router,
    private activeRoute: ActivatedRoute,
    private savingService: SavingService,
    private fb: FormBuilder,
    private translateService: TranslateService
  ) {
    this.activeRoute.params.subscribe((res) => {
      this.savingAccountId = res.savingId;
    });
  }

  ngOnInit(): void {
    this.getDetail();
  }

  initForm(item: ISaving) {
    this.formDetail = this.fb.group({
      receivingAccountNumber: [item?.receivingAccountNumber || ''],
      savingAccountNumber: [item?.savingAccountNumber || ''],
      startTime: [item?.startTime || ''],
      settlementDueTime: [item?.settlementDueTime || ''],
      interestRate: [this.transformTextRate(item?.interestRate)],
      tenor: [this.transformTextMonth(item?.tenor)],
      savingAmount: [this.transformTextMoney(item?.savingAmount)],
      finalizationMethod: [this.getFinalizationMethod(item.finalizationMethod)],
      type: [this.getSavingType(item?.type) || ''],
      status: [this.getStatusContent(item?.status) || ''],
      cif: [item?.cif || ''],
      fullname: [item?.fullname || ''],
      idCardNumber: [item?.idCardNumber || ''],
      phoneNumber: [item?.phoneNumber || ''],
      amtAfterSettlement: [this.transformTextMoney(item?.amtAfterSettlement)],
      interestAmtEnd: [this.transformTextMoney(item?.interestAmtEnd)],
      interestAmt: [this.transformTextMoney(item?.interestAmt)],
    });

    this.formDetail.disable();
  }

  getDetail(): void {
    this.savingService
      .detail({ id: this.savingAccountId })
      .subscribe((response) => {
        this.detailData = response.body as ISaving;
        if (this.detailData.startTime) {
          this.detailData.startTime = this.convertToMidnight(
            moment(
              this.detailData.startTime,
              MOMENT_CONST.LOCAL_DATE_TIME_FORMAT
            ).toDate()
          );
        }
        if (this.detailData.settlementDueTime) {
          this.detailData.settlementDueTime = this.convertToMidnight(
            moment(
              this.detailData.settlementDueTime,
              MOMENT_CONST.LOCAL_DATE_TIME_FORMAT
            ).toDate()
          );
        }
        this.initForm(this.detailData);
      });
  }

  getSavingType(type?: string): string {
    if (type === this.SAVING_TYPE_CONST.FIXED.value) {
      return this.translateService.instant(this.SAVING_TYPE_CONST.FIXED.label);
    }
    if (type === this.SAVING_TYPE_CONST.ACCUMULATED.value) {
      return this.translateService.instant(
        this.SAVING_TYPE_CONST.ACCUMULATED.label
      );
    }
    return '';
  }

  getFinalizationMethod(finalizationMethod?: string): string {
    if (
      finalizationMethod === this.FINALIZATION_METHOD_CONST.NO_ROTATION.value
    ) {
      return this.translateService.instant(
        this.FINALIZATION_METHOD_CONST.NO_ROTATION.label
      );
    }
    if (
      finalizationMethod === this.FINALIZATION_METHOD_CONST.ROOT_ROTATION.value
    ) {
      return this.translateService.instant(
        this.FINALIZATION_METHOD_CONST.ROOT_ROTATION.label
      );
    }
    if (
      finalizationMethod ===
      this.FINALIZATION_METHOD_CONST.ROTATION_OF_PRINCIPAL_AND_INTEREST.value
    ) {
      return this.translateService.instant(
        this.FINALIZATION_METHOD_CONST.ROTATION_OF_PRINCIPAL_AND_INTEREST.label
      );
    }
    return '';
  }

  getStatusContent(status?: number): string {
    if (status === this.SAVING_ACCOUNT_STATUS_CONST.ACTIVE.code) {
      return this.translateService.instant(
        this.SAVING_ACCOUNT_STATUS_CONST.ACTIVE.label
      );
    }
    if (status === this.SAVING_ACCOUNT_STATUS_CONST.INACTIVE.code) {
      return this.translateService.instant(
        this.SAVING_ACCOUNT_STATUS_CONST.INACTIVE.label
      );
    }
    return '';
  }

  transformTextRate(value?: number): string {
    if (value) {
      const rate = this.translateService.instant(
        'model.manageSaving.ratePercent'
      );
      return `${value} ${rate}`;
    }
    return '';
  }

  transformTextMonth(value?: number): string {
    if (value) {
      const month = this.translateService.instant('model.manageSaving.month');
      return `${value} ${month}`;
    }

    return '';
  }

  transformTextMoney(value?: number): string {
    if (value) {
      const money = CommonUtils.moneyFormat(String(value));
      const unit = this.translateService.instant('model.manageSaving.unit');
      return `${money} ${unit}`;
    }
    return '0';
  }

  backToList(): void {
    this.router.navigate([ROUTER_UTILS.saving.root]);
  }

  convertToMidnight(originalTime: Date): string {
    originalTime.setHours(0, 0, 0, 0);

    const formattedTime = `${originalTime.getFullYear()}-${this.pad(
      originalTime.getMonth() + 1
    )}-${this.pad(originalTime.getDate())} ${this.pad(
      originalTime.getHours()
    )}:${this.pad(originalTime.getMinutes())}:${this.pad(
      originalTime.getSeconds()
    )}`;

    return formattedTime;
  }

  private pad(num: number): string {
    return num < 10 ? `0${num}` : `${num}`;
  }

  ngOnDestroy(): void {}
}
