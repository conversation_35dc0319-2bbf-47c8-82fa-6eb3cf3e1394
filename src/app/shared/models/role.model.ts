import { Moment } from 'moment';

export interface IRole {
  roleId?: number;
  name?: string;
  description?: string;
  createdBy?: string;
  createdDate?: string;
  createDateTime?: Moment;
  lastModifiedDatetime?: Moment;
  lastModifiedBy?: string;
  immutable?: boolean;
  status?: number;
  privileges?: number[];
}

// export class Role {
//   constructor(
//     public id?: number,
//     public name?: string,
//     public description?: string,
//     public immutable?: boolean,
//     public status?: number,
//     public privileges?: Array<number>
//   ) {
//     this.id = id ?? null;
//     this.name = name ?? null;
//     this.description = description ?? null;
//     this.immutable = immutable ?? null;
//     this.status = status ?? null;
//     this.privileges = privileges ?? [];
//   }
// }
