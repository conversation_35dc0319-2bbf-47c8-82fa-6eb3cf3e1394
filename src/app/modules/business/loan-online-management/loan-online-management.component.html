<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="searchForm" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-lg-3 col-md-6">
            <div class="form-group">
              <label>{{ "common.action.searchKeyword" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="keyword"
                class="w-100"
                class="form-control"
                placeholder="{{ 'common.action.searchKeyword' | placeholder }}"
              />
            </div>
          </div>
          <div class="col-lg-2 col-md-6">
            <div class="form-group">
              <label>{{ "common.status" | translate }}</label>
              <ng-select
                placeholder="{{ 'common.appSelectOption.status' | translate }}"
                [searchable]="false"
                formControlName="status"
                [clearable]="true"
              >
                <ng-option
                  [value]="item.code"
                  *ngFor="let item of ENTITY_STATUS"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-lg-4 col-md-8">
            <div class="date-picker-container form-group row">
              <div class="col-md-6">
                <label
                  >{{ "common.action.fromDate" | translate
                  }}<span class="text-danger">*</span></label
                >

                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="createdDateStartAt"
                    formControlName="createdDateStartAt"
                    placeholder="DD/MM/YYYY"
                    [max]="maxDate"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="createdDateStartAt"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #createdDateStartAt></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    searchForm.get('createdDateStartAt')?.errors?.required &&
                    searchForm.get('createdDateStartAt')?.touched
                  "
                >
                  {{ "error.required.fromDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    searchForm.get('createdDateStartAt')?.errors?.invalidDate &&
                    searchForm.get('createdDateStartAt')?.touched
                  "
                >
                  {{ "error.required.inValidDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    searchForm.get('createdDateStartAt')?.errors
                      ?.invalidMaxDate &&
                    searchForm.get('createdDateStartAt')?.touched
                  "
                >
                  {{
                    "error.maxDateCurrent"
                      | translate
                        : {
                            param: "common.action.fromDate" | translate
                          }
                  }}
                </small>
              </div>
              <div class="col-md-6">
                <label
                  >{{ "common.action.toDate" | translate
                  }}<span class="text-danger">*</span></label
                >

                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="createdDateEndAt"
                    formControlName="createdDateEndAt"
                    placeholder="DD/MM/YYYY"
                    min="{{
                      searchForm.controls['createdDateStartAt'].value
                        | date : 'yyyy-MM-dd'
                    }}"
                    [max]="maxToDate"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="createdDateEndAt"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #createdDateEndAt></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    searchForm.get('createdDateEndAt')?.errors?.required &&
                    searchForm.get('createdDateEndAt')?.touched
                  "
                >
                  {{ "error.required.toDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    searchForm.get('createdDateEndAt')?.errors
                      ?.invalidMaxDate &&
                    searchForm.get('createdDateEndAt')?.touched
                  "
                >
                  {{
                    "error.maxDateCurrent"
                      | translate
                        : {
                            param: "common.action.toDate" | translate
                          }
                  }}
                </small>
              </div>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
            >
              <div class="btn-reset">
                <i
                  class="bi bi-arrow-clockwise"
                  type="button"
                  (click)="onReset()"
                >
                </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" type="submit">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-3 mt-4">
          <!-- <button class="btn btn-red mr-2" (click)="print()">{{ "common.action.print" | translate }}
          </button> -->
          <button
            class="btn btn-red mr-2"
            type="button"
            (click)="exportFile()"
            *hasPrivileges="SYSTEM_RULES.LOAN_ONLINE_EXPORT"
          >
            {{ "common.action.export" | translate }}
          </button>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th class="text-center" scope="col">
                {{ "common.no" | translate }}
              </th>
              <th scope="col">{{ "model.loanOnline.fullName" | translate }}</th>
              <th scope="col">
                {{ "model.loanOnline.phoneNumber" | translate }}
              </th>
              <th scope="col">
                {{ "model.loanOnline.loanAmount" | translate }}
              </th>
              <th scope="col">
                {{ "model.loanOnline.purposeLoan" | translate }}
              </th>
              <th scope="col">{{ "model.loanOnline.loanTime" | translate }}</th>
              <th class="text-center" scope="col">
                {{ "common.status" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of searchResults.content; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td>
                <span title="{{ dataItem.fullName }}">{{
                  dataItem.fullName | limitWord
                }}</span>
              </td>
              <td>{{ dataItem.phoneNumber }}</td>
              <td>{{ dataItem.loanAmount | currencyLak }}</td>
              <td>
                <span title="{{ dataItem.loanProductName }}">{{
                  dataItem.loanProductName | limitWord
                }}</span>
              </td>
              <td>{{ dataItem.loanTime }}</td>
              <td class="text-center">
                <!-- {{ ENTITY_STATUS_MAP[dataItem.status || 0] | translate }} -->
                <span
                  class="badge"
                  [ngClass]="ENTITY_STATUS_MAP[dataItem.status || 0].style"
                  >{{
                    ENTITY_STATUS_MAP[dataItem.status || 0].label | translate
                  }}</span
                >
              </td>
              <td class="text-center">
                <button
                  ngbTooltip="{{ 'common.action.detail' | translate }}"
                  class="btn px-1 py-0"
                  (click)="detail(dataItem.loanOnlineId)"
                >
                  <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                </button>
                <!--*ngIf="dataItem.approvalStatus === 1 && (dataItem.approvalType === 1 || dataItem.approvalType === 2)"-->
                <button
                  ngbTooltip="{{ 'common.action.delete' | translate }}"
                  class="btn px-1 py-0"
                  (click)="delete(dataItem)"
                  *hasPrivileges="SYSTEM_RULES.LOAN_ONLINE_DELETE"
                >
                  <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        <div
          class="row d-block text-center m-0"
          *ngIf="searchResults.content?.length === 0"
        >
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
        <div *ngIf="searchResults.content?.length">
          <mat-paginator
            [length]="searchForm.value.length"
            [pageSize]="searchForm.value.pageSize"
            [pageIndex]="searchForm.value.pageIndex"
            [pageSizeOptions]="pageSizeOptions"
            (page)="onChangePage($event)"
            aria-label="Select page"
          >
          </mat-paginator>
        </div>
      </div>
    </div>
  </div>
</section>
