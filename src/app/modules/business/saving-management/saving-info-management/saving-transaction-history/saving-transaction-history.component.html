<div class="col-md-12">
  <form [formGroup]="searchForm" (submit)="onSearchSubmit()">
    <div class="row">
      <div class="col-lg-2">
        <div class="form-group">
          <label>{{ "common.action.searchKeyword" | translate }}</label>
          <input
            trim
            class="form-control w-100"
            formControlName="keyword"
            type="text"
            placeholder="{{ 'common.action.searchKeyword' | translate }}"
          />
        </div>
      </div>
      <div class="col-lg-2">
        <div class="form-group">
          <label>{{ "model.manageSaving.savingInfo.transactionHistory.type" | translate }}</label>
          <ng-select
            placeholder="{{ 'model.manageSaving.savingInfo.transactionHistory.type' | translate }}"
            [searchable]="false"
            formControlName="transferType"
            [clearable]="true"
          >
            <ng-option [value]="item.code" *ngFor="let item of TRANSFER_TYPE">
              {{ item.label | translate }}
            </ng-option>
          </ng-select>
        </div>
      </div>
      <div class="col-lg-4">
        <div class="row form-group">
          <div class="col-md-6">
            <label class=""
              >{{ "model.insuranceManage.fromDate" | translate
              }}<small class="text-danger">*</small></label
            >
            <mat-form-field appearance="fill" class="date-picker">
              <input
                matInput
                [matDatepicker]="fromDate"
                formControlName="fromDate"
                [placeholder]="MOMENT_CONST.FORMAT_DATE_PLACEHOLDER"
                max="{{
                  searchForm.controls['toDate'].value
                    ? (searchForm.controls['toDate'].value
                      | date : MOMENT_CONST.FORMAT_DATE_MAT_PICKER)
                    : (maxDate | date : MOMENT_CONST.FORMAT_DATE_MAT_PICKER)
                }}"
                dateTransform
              />
              <mat-datepicker-toggle
                matSuffix
                [for]="fromDate"
              ></mat-datepicker-toggle>
              <mat-datepicker #fromDate></mat-datepicker>
            </mat-form-field>
            <small
              class="text-danger"
              *ngIf="
                searchForm.get('fromDate')?.errors?.required &&
                searchForm.get('fromDate')?.touched
              "
            >
              {{ "error.insuranceManage.required.fromDate" | translate }}</small
            >
            <small
              class="text-danger"
              *ngIf="
                searchForm.get('fromDate')?.errors?.invalidFromDateCurrent &&
                searchForm.get('fromDate')?.touched
              "
            >
              {{
                "error.insuranceManage.dateTime.invalidFromDateCurrent"
                  | translate
              }}</small
            >
          </div>
          <div class="col-md-6">
            <label class=""
              >{{ "model.insuranceManage.toDate" | translate
              }}<small class="text-danger">*</small></label
            >
            <mat-form-field appearance="fill" class="date-picker">
              <input
                matInput
                [matDatepicker]="toDate"
                formControlName="toDate"
                [placeholder]="MOMENT_CONST.FORMAT_DATE_PLACEHOLDER"
                dateTransform
                [max]="maxDate"
                min="{{
                  searchForm.controls['fromDate'].value
                    | date : MOMENT_CONST.FORMAT_DATE_MAT_PICKER
                }}"
              />
              <mat-datepicker-toggle
                matSuffix
                [for]="toDate"
              ></mat-datepicker-toggle>
              <mat-datepicker #toDate></mat-datepicker>
            </mat-form-field>
            <small
              class="text-danger"
              *ngIf="
                searchForm.get('toDate')?.errors?.required &&
                searchForm.get('toDate')?.touched
              "
            >
              {{ "error.insuranceManage.required.toDate" | translate }}</small
            >
            <small
              class="text-danger"
              *ngIf="
                searchForm.get('toDate')?.errors?.invalidToDate &&
                searchForm.get('toDate')?.touched
              "
            >
              {{
                "error.insuranceManage.dateTime.invalidToDate" | translate
              }}</small
            >
            <small
              class="text-danger"
              *ngIf="
                searchForm.get('toDate')?.errors?.invalidToDateCurrent &&
                searchForm.get('toDate')?.touched
              "
            >
              {{
                "error.insuranceManage.dateTime.invalidToDateCurrent"
                  | translate
              }}</small
            >
          </div>
        </div>
      </div>
      <div class="col-md-2 col-lg-2 mt-4 d-flex">
        <div
          class="col-btn-reset"
          ngbTooltip="{{ 'common.action.reset' | translate }}"
          (click)="onReset()"
        >
          <div class="btn-reset">
            <i class="bi bi-arrow-clockwise" type="button"> </i>
          </div>
        </div>
        <div class="">
          <button
            class="btn btn-search mr-2"
            type="submit"
            *hasPrivileges="SYSTEM_RULES.SAVING_ACCOUNT_READ"
          >
            {{ "common.action.search" | translate }}
          </button>
        </div>
      </div>
    </div>
  </form>
  <div class="table-responsive">
    <table class="table">
      <thead>
        <tr>
          <th class="text-center">
            {{ "model.manageSaving.no" | translate }}
          </th>
          <th class="text-right">
            {{
              "model.manageSaving.savingInfo.transactionHistory.accountSource"
                | translate
            }}
          </th>
          <th class="text-right">
            {{
              "model.manageSaving.savingInfo.transactionHistory.transactionTime"
                | translate
            }}
          </th>
          <th class="text-left">
            {{
              "model.manageSaving.savingInfo.transactionHistory.transactionCode"
                | translate
            }}
          </th>
          <th class="text-right">
            {{
              "model.manageSaving.savingInfo.transactionHistory.money"
                | translate
            }}
          </th>
          <th class="text-left">
            {{
              "model.manageSaving.savingInfo.transactionHistory.type"
                | translate
            }}
          </th>
          <th [width]="'350px'" class="text-left">
            {{
              "model.manageSaving.savingInfo.transactionHistory.content"
                | translate
            }}
          </th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of data; let i = index">
          <td class="text-center">{{ fillIndexItem(i) }}</td>
          <td class="text-right">{{ item.customerAccNumber }}</td>
          <td class="text-right">{{ item.transactionTime }}</td>
          <td class="text-left">{{ item.transactionCode }}</td>
          <td class="text-right">
            {{ item.transactionAmount | currencyLak }}
          </td>
          <td class="text-left">
            {{ TRANSFER_TYPE_MAP[item.transferType ?? ""].label | translate }}
          </td>
          <td class="text-left">{{ item.description }}</td>
        </tr>
      </tbody>
    </table>
    <div
      class="row d-block text-center m-0 no-search-result-wrapper"
      *ngIf="data?.length === 0"
    >
      <img
        src="/assets/dist/img/icon/empty.svg"
        height="120"
        alt="no_search_result"
      />
      <p class="text-center mb-5">
        {{ "common.no_search_result" | translate }}
      </p>
    </div>
  </div>
  <div *ngIf="data?.length">
    <mat-paginator
      [length]="searchForm.value.length"
      [pageSize]="searchForm.value.pageSize"
      [pageIndex]="searchForm.value.pageIndex"
      [pageSizeOptions]="pageSizeOptions"
      (page)="onChangePage($event)"
      aria-label="Select page"
    >
    </mat-paginator>
  </div>
</div>
