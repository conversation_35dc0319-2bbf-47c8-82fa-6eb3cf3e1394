import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { IConfigSaving } from '../models/config-saving.model';
import { AbstractDomainService } from './common/abstract.domain.service';

@Injectable({
  providedIn: 'root',
})
export class ConfigSavingService extends AbstractDomainService<IConfigSaving> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.CONFIGURING;
}
