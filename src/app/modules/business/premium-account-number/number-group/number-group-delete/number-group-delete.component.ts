import { Component, HostListener, Input, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { MODAL_ACTION } from '@shared/constants/app.constants';
import { MODEL_MAP_ITEM_COMMON } from '@shared/models/model.type';
import { INumberGroup } from '@shared/models/number-group.model';

@Component({
  selector: 'app-number-group-delete',
  templateUrl: './number-group-delete.component.html',
  styleUrls: ['./number-group-delete.component.scss'],
})
export class NumberGroupDeleteComponent implements OnInit {
  public title = 'numberGroup.delete';
  public content = 'numberGroup.deleteContent';
  public interpolateParams: object = {};
  public isHiddenBtnClose = false;
  public action!: MODEL_MAP_ITEM_COMMON;

  // Thêm các thuộc tính cho nút
  @Input() buttonOrder: 'confirm-first' | 'cancel-first' | undefined;
  @Input() confirmBtnClass: string | undefined; // Lớp CSS cho nút xác nhận
  @Input() numberGroup: INumberGroup = {};

  hasFilter = false;

  MODAL_ACTION = MODAL_ACTION;

  constructor(public activeModal: NgbActiveModal, public translateService: TranslateService) {
  }

  ngOnInit(): void {
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.activeModal.close();
  }

  showContent(): string {
    const interpolateParams = { name: this.numberGroup.name };
    return this.translateService.instant('numberGroup.deleteModalContent', interpolateParams);
  }

  onDelete(): void {
    this.activeModal.close(MODAL_ACTION.CONFIRM.code);
  }
}
