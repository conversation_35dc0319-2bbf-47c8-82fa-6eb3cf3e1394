import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IReferral } from '@shared/models/referral.model';
import { IReferralSearch } from '@shared/models/request/referral.search';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';
@Injectable({
  providedIn: 'root',
})
export class ReferralService extends AbstractService {
  public resourceUrl = '/referral';

  constructor(protected http: HttpClient) {
    super(http);
  }

  /**
   * API search
   *
   * @param request IPagination
   * @returns Observable<EntityResponseType<IReferral[]>>
   */
  search(request: IReferral): Observable<EntityResponseType<IReferral[]>> {
    return super.post<EntityResponseType<IReferral[]>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  /**
   * API export
   *
   * @param request IMerchantSearch
   * @returns Observable<any>
   */
  exportReferral(request: IReferralSearch): Observable<any> {
    return super.postFile(`${this.resourceUrl}/export`, request);
  }

  /**
   * API create
   *
   * @param request IReferral
   * @returns Observable<EntityResponseType<IReferral>>
   */
  create(request: IReferral): Observable<EntityResponseType<IReferral>> {
    return super.post<EntityResponseType<IReferral>>(
      `${this.resourceUrl}/create`,
      request
    );
  }

  /**
   * API update
   *
   * @param request IReferral
   * @returns Observable<EntityResponseType<IReferral>>
   */
  update(request: IReferral): Observable<EntityResponseType<IReferral>> {
    return super.post<EntityResponseType<IReferral>>(
      `${this.resourceUrl}/update`,
      request
    );
  }

  /**
   * API lock
   *
   * @param request IReferral
   * @returns Observable<EntityResponseType<IReferral>>
   */
  lock(request: IReferral): Observable<EntityResponseType<IReferral>> {
    return super.post<EntityResponseType<IReferral>>(
      `${this.resourceUrl}/lock`,
      request
    );
  }

  /**
   * API unlock
   *
   * @param request IReferral
   * @returns Observable<EntityResponseType<IReferral>>
   */
  unlock(request: IReferral): Observable<EntityResponseType<IReferral>> {
    return super.post<EntityResponseType<IReferral>>(
      `${this.resourceUrl}/unlock`,
      request
    );
  }

  /**
   * API delete
   *
   * @param request IReferral
   * @returns Observable<EntityResponseType<IReferral>>
   */
  deletePosition(
    request: IReferral
  ): Observable<EntityResponseType<IReferral>> {
    return super.post<EntityResponseType<IReferral>>(
      `${this.resourceUrl}/delete`,
      request
    );
  }

  /**
   * API export
   *
   * @param request IReferralSearch
   * @returns Observable<any>
   */
  downloadTemplate(): Observable<any> {
    return super.postFile(`${this.resourceUrl}/template-import`, null);
  }

  /**
   * API detail
   *
   * @param request IReferral
   * @returns Observable<EntityResponseType<IReferral>>
   */
  detail(request: IReferral): Observable<EntityResponseType<IReferral>> {
    return super.post<EntityResponseType<IReferral>>(
      `${this.resourceUrl}/detail`,
      request
    );
  }

  importDataTemplate(file: File): Observable<EntityResponseType<IReferral>> {
    const formData = new FormData();
    formData.append('file', file);
    return super.post(`${this.resourceUrl}/import`, formData);
  }
}
