import { Injectable } from '@angular/core';
import { IConfigTransaction } from '@shared/models/config-transaction.model';
import { ISearchConfigTrans } from '@shared/models/request/config-transaction.search';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({ providedIn: 'root' })
export class ConfigTransactionService extends AbstractService {
  public resourceUrl = '/transaction-limit';

  search(
    request: ISearchConfigTrans
  ): Observable<EntityResponseType<IConfigTransaction>> {
    return super.post<EntityResponseType<IConfigTransaction>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  detail(request: any): Observable<EntityResponseType<IConfigTransaction>> {
    return super.post<EntityResponseType<IConfigTransaction>>(
      `${this.resourceUrl}/detail`,
      request
    );
  }

  create(
    request: IConfigTransaction
  ): Observable<EntityResponseType<IConfigTransaction>> {
    return super.post<EntityResponseType<IConfigTransaction>>(
      `${this.resourceUrl}/create`,
      request
    );
  }

  update(
    request: IConfigTransaction
  ): Observable<EntityResponseType<IConfigTransaction>> {
    return super.post<EntityResponseType<IConfigTransaction>>(
      `${this.resourceUrl}/update`,
      request
    );
  }

  approve(request: any): Observable<EntityResponseType<any>> {
    return super.post<EntityResponseType<any>>(
      `${this.resourceUrl}/approve`,
      request
    );
  }

  export(
    request: ISearchConfigTrans
  ): Observable<EntityResponseType<IConfigTransaction>> {
    return super.postFile(`${this.resourceUrl}/export`, request);
  }

  delete(request: any): Observable<EntityResponseType<IConfigTransaction>> {
    return super.post<EntityResponseType<any>>(
      `${this.resourceUrl}/delete`,
      request
    );
  }
}
