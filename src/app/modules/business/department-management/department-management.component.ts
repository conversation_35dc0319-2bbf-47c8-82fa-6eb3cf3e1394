import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  MODAL_ACTION,
  PAGINATION
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { IDepartment } from '@shared/models/department.model';
import { DepartmentService } from '@shared/services/department.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import { DepartmentCreateUpdateComponent } from './department-create-update/department-create-update.component';

@Component({
  selector: 'app-department-management',
  templateUrl: './department-management.component.html',
  styleUrls: ['./department-management.component.scss'],
})
export class DepartmentManagementComponent implements OnInit {
  departments: IDepartment[] = [];

  pageSizeOptions = PAGINATION.OPTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  ENTITY_STATUS = ENTITY_STATUS;
  ROUTER_UTILS = ROUTER_UTILS;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;

  formDepartmentSearch = this.fb.group({
    keyword: '',
    status: null,
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    // previousPageIndex: 0,
  });

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private translateService: TranslateService,
    private modalService: ModalService,
    private toastService: ToastrCustomService,
    private departmentService: DepartmentService,
    private ngb: NgbModal
  ) {}

  ngOnInit(): void {
    // this.initForm();
    // this.loadData(this.pageEvent);
    this.onSearch();
  }

  // initForm() {
  //   this.formDepartmentSearch = this.fb.group({
  //     departmentName: [null],
  //     shortName: [null],
  //     status: [null],
  //     departmentCode: [null],
  //     keyword: [null],
  //   });
  // }

  // loadData(page: PageEvent): void {
  //   this.departmentSearch.pageIndex = page.pageIndex;
  //   this.departmentSearch.pageSize = page.pageSize;
  //   this.departmentSearch.keyword =
  //     this.formDepartmentSearch.get('keyword')?.value;
  //   this.departmentService
  //     .searchByKeyWord(this.departmentSearch)
  //     .subscribe((res: any): void => {
  //       this.departments = res.body.content;
  //       this.totalRecord = res.body.totalElements;
  //     });
  // }

  onCreate() {
    this.openDepartmentModal(false, false, true).then((result) => {
      if (result === MODAL_ACTION.RESET.code) {
        this.onSearch();
      }
    });
  }

  /**
   *
   * @param isUpdate
   * @param isDetail
   * @param departmentId
   * @returns
   */
  async openDepartmentModal(
    isUpdate: boolean,
    isDetail: boolean,
    isCreate: boolean,
    departmentId?: number
  ) {
    let department = null;

    if (departmentId) {
      await this.departmentService
        .detail({ departmentId })
        .toPromise()
        .then((result: any) => {
          department = result.body;
        });
    }

    const modalRef = this.ngb.open(DepartmentCreateUpdateComponent, {
      backdrop: 'static',
      size: 'xl',
      centered: true,
      keyboard: false,
    });

    modalRef.componentInstance.isUpdate = isUpdate;
    modalRef.componentInstance.isDetail = isDetail;
    modalRef.componentInstance.isCreate = isCreate;

    if (departmentId) {
      modalRef.componentInstance.department = department;
    }

    return modalRef.result;
  }

  onReset() {
    // this.formDepartmentSearch.reset();
    this.formDepartmentSearch.controls.keyword.reset();
    this.formDepartmentSearch.controls.status.reset();
  }

  onSearchSubmit() {
    this.formDepartmentSearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.formDepartmentSearch.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    this.onSearch();
  }

  onSearch() {
    // const body = this.formDepartmentSearch.getRawValue();
    // this.departmentSearch.departmentCode = body.departmentCode;
    // this.departmentSearch.status = body.status;
    // this.departmentSearch.departmentName = body.departmentName;
    // this.departmentSearch.shortName = body.shortName;

    // this.loadData(this.pageEvent);
    this.departmentService
      .searchByKeyWord(this.formDepartmentSearch.value)
      .subscribe((res: any): void => {
        this.departments = res.body.content;
        this.formDepartmentSearch.controls.length.setValue(res.body.totalElements);
      });
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formDepartmentSearch.value.pageIndex,
      this.formDepartmentSearch.value.pageSize
    );
  }

  onDetail(department: IDepartment) {
    if (department.departmentId) {
      this.openDepartmentModal(
        false,
        true,
        false,
        department.departmentId
      ).then((result) => {
        if (result === MODAL_ACTION.RESET.code) {
          this.onSearch();
        }
      });
    }
  }

  onEdit(department: IDepartment) {
    if (department.departmentId) {
      this.openDepartmentModal(
        true,
        false,
        false,
        department.departmentId
      ).then((result) => {
        if (result === MODAL_ACTION.RESET.code) {
          this.onSearch();
        }
      });
    }
  }

  onChangePage(page: PageEvent) {
    // this.pageIndex = page.pageIndex;
    // this.pageSize = page.pageSize;
    // this.loadData(page);
    this.formDepartmentSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formDepartmentSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  delete(department: IDepartment) {
    const modalData = {
      title: 'department.deleteTitle',
      content: 'department.deleteContent',
      interpolateParams: {
        departmentName: `<b>${department?.departmentName || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { departmentId: department.departmentId };
        this.departmentService.deleteDepartment(params).subscribe((res) => {
          this.toastService.success('common.action.deleteSuccess');
          this.onSearch();
        });
      }
    });
  }

  lockAndUnlock(department: IDepartment) {
    if (department.status === ENTITY_STATUS_CONST.INACTIVE.code) {
      this.unlock(department);
    } else {
      this.lock(department);
    }
  }

  lock(department: IDepartment): boolean {
    let rs = false;

    const modalData = {
      title: 'department.lockTitle',
      content: 'department.lockContent',
      interpolateParams: {
        departmentName: `<b>${department?.departmentName || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { departmentId: department.departmentId };
        this.departmentService.lock(params).subscribe((res) => {
          this.toastService.success('common.action.lockSuccess');
          this.onSearch();
          rs = true;
        });
      }
    });

    return rs;
  }

  unlock(department: IDepartment): boolean {
    let rs = false;

    const modalData = {
      title: 'department.unlockTitle',
      content: 'department.unlockContent',
      interpolateParams: {
        departmentName: `<b>${department?.departmentName || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { departmentId: department.departmentId };
        this.departmentService.unlock(params).subscribe((res) => {
          this.toastService.success('common.action.unlockSuccess');
          this.onSearch();
          rs = true;
        });
      }
    });

    return rs;
  }
}
