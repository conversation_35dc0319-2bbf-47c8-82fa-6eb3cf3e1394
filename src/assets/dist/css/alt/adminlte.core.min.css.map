{"version": 3, "sources": ["..\\..\\..\\build\\scss\\AdminLTE-core.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\bootstrap.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_root.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_reboot.scss", "..\\..\\..\\build\\scss\\_bootstrap-variables.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\vendor\\_rfs.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_variables.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_hover.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_type.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_lists.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_images.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_image.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_border-radius.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_box-shadow.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_code.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_grid.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_grid.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_breakpoints.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_grid-framework.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_tables.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_table-row.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_functions.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_forms.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_transition.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_forms.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_gradients.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_buttons.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_buttons.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_button-group.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_transitions.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_dropdown.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_caret.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_nav-divider.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_input-group.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_custom-forms.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_nav.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_navbar.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_card.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_breadcrumb.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_pagination.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_pagination.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_badge.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_badge.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_jumbotron.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_alert.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_alert.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_progress.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_media.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_list-group.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_list-group.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_close.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_toasts.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_modal.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_tooltip.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_reset-text.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_popover.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_carousel.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_clearfix.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_spinners.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\utilities\\_align.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_background-variant.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\utilities\\_background.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\utilities\\_borders.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\utilities\\_display.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\utilities\\_embed.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\utilities\\_flex.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\utilities\\_float.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\utilities\\_overflow.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\utilities\\_position.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\utilities\\_screenreaders.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_screen-reader.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\utilities\\_shadows.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\utilities\\_sizing.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\utilities\\_stretched-link.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\utilities\\_spacing.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\utilities\\_text.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_text-truncate.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_text-emphasis.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\mixins\\_text-hide.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\utilities\\_visibility.scss", "..\\..\\..\\node_modules\\bootstrap\\scss\\_print.scss", "..\\..\\..\\build\\scss\\_layout.scss", "..\\..\\..\\build\\scss\\_variables.scss", "..\\..\\..\\build\\scss\\_main-header.scss", "..\\..\\..\\build\\scss\\_brand.scss", "..\\..\\..\\build\\scss\\_main-sidebar.scss", "..\\..\\..\\build\\scss\\mixins\\_miscellaneous.scss", "..\\..\\..\\build\\scss\\mixins\\_sidebar.scss", "..\\..\\..\\build\\scss\\_sidebar-mini.scss", "..\\..\\..\\build\\scss\\_control-sidebar.scss", "..\\..\\..\\build\\scss\\_dropdown.scss", "..\\..\\..\\build\\scss\\_navs.scss", "..\\..\\..\\build\\scss\\_miscellaneous.scss", "..\\..\\..\\build\\scss\\_print.scss", "..\\..\\..\\build\\scss\\_text.scss", "..\\..\\..\\build\\scss\\_elevation.scss", "..\\..\\..\\build\\scss\\mixins\\_backgrounds.scss", "..\\..\\..\\build\\scss\\_colors.scss", "..\\..\\..\\build\\scss\\mixins\\_accent.scss"], "names": [], "mappings": "AAAA;;;;;;ACAA;;;;;ACCA,MAGI,OAAa,QAAb,SAAa,QAAb,SAAa,QAAb,OAAa,QAAb,MAAa,QAAb,SAAa,QAAb,SAAa,QAAb,QAAa,QAAb,OAAa,QAAb,OAAa,QAAb,QAAa,QAAb,OAAa,QAAb,YAAa,QAIb,UAAa,QAAb,YAAa,QAAb,UAAa,QAAb,OAAa,QAAb,UAAa,QAAb,SAAa,QAAb,QAAa,QAAb,OAAa,QAIb,gBAAgC,EAAhC,gBAAgC,MAAhC,gBAAgC,MAAhC,gBAAgC,MAAhC,gBAAgC,OAKlC,yBAAwB,iBAAA,CAAA,aAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,CAAA,mBAAA,CAAA,gBAAA,CAAA,kBACxB,wBAAuB,cAAA,CAAA,KAAA,CAAA,MAAA,CAAA,QAAA,CAAA,iBAAA,CAAA,aAAA,CAAA,UCAzB,EAEC,QADA,SAEC,WAAY,WAGd,KACE,YAAa,WACb,YAAa,KACb,yBAA0B,KAC1B,4BCPS,YDaX,QAAS,MAAO,WAAY,OAAQ,OAAQ,OAAQ,OAAQ,KAAM,IAAK,QACrE,QAAS,MAUX,KACE,OAAQ,EACR,YCyL4B,iBAAiB,CAAE,aAAa,CAAE,kBAAkB,CAAE,UAAU,CAAE,MAAM,CAAE,gBAAgB,CAAE,KAAK,CAAE,UAAU,CAAE,mBAAmB,CAAE,gBAAgB,CAAE,kBCzG9K,UAtCW,KFxCf,YCiM4B,IDhM5B,YCoM4B,IDnM5B,MC/BS,QDgCT,WAAY,KACZ,iBC1CS,KDsD6B,0CACtC,QAAS,YASX,GACE,WAAY,YACZ,OAAQ,EACR,SAAU,QAaZ,GAAI,GAAI,GAAI,GAAI,GAAI,GAClB,WAAY,EACZ,cCkK4B,MD3J9B,EACE,WAAY,EACZ,cC4D0B,KDhDvB,0BADA,YAEH,gBAAiB,UACjB,gBAAiB,UAAA,OACjB,OAAQ,KACR,cAAe,EACf,yBAA0B,KAG5B,QACE,cAAe,KACf,WAAY,OACZ,YAAa,QAKf,GAFA,GACA,GAEE,WAAY,EACZ,cAAe,KAGd,MAEA,MACA,MAFA,MAGD,cAAe,EAGjB,GACE,YCoG4B,IDjG9B,GACE,cAAe,MACf,YAAa,EAGf,WACE,OAAQ,EAAA,EAAA,KAGV,EACA,OACE,YGwI4B,OHrI9B,MExFI,UAAU,IFiGd,IACA,IACE,SAAU,SEnGR,UAAU,IFqGZ,YAAa,EACb,eAAgB,SAGlB,IAAM,OAAQ,OACd,IAAM,IAAK,MAOX,EACE,MCtJQ,QDuJR,gBCjC0B,KDkC1B,iBAAkB,YIhLjB,QJmLC,MCpCwB,QDqCxB,gBCpCwB,KD6CrB,cACL,MAAO,QACP,gBAAiB,KI/LhB,oBJkMC,MAAO,QACP,gBAAiB,KAUrB,KACA,IAFA,IAGA,KACE,YCa4B,cAAc,CAAE,KAAK,CAAE,MAAM,CAAE,QAAQ,CAAE,iBAAiB,CAAE,aAAa,CAAE,UCjKrG,UAAU,IFwJd,IAEE,WAAY,EAEZ,cAAe,KAEf,SAAU,KAQZ,OAEE,OAAQ,EAAA,EAAA,KAQV,IACE,eAAgB,OAChB,aAAc,KAGhB,IAGE,SAAU,OACV,eAAgB,OAQlB,MACE,gBAAiB,SAGnB,QACE,YCiC4B,ODhC5B,eCgC4B,OD/B5B,MC/PS,QDgQT,WAAY,KACZ,aAAc,OAGhB,GAGE,WAAY,QAQd,MAEE,QAAS,aACT,cGqKsC,MH/JxC,OAEE,cAAe,EAOX,aACJ,QAAS,IAAA,OACT,QAAS,IAAA,KAAA,yBAIX,OADA,MAGA,SADA,OAEA,SACE,OAAQ,EACR,YAAa,QErPX,UAAU,QFuPZ,YAAa,QAGf,OACA,MACE,SAAU,QAGZ,OACA,OACE,eAAgB,KAMlB,OACE,UAAW,OAQZ,cACA,aACA,cAHD,OAIE,mBAAoB,OASH,6BAAA,4BAAA,6BAAA,sBACb,OAAQ,QAOC,gCACD,+BACC,gCAHT,yBAIJ,QAAS,EACT,aAAc,KAIV,qBADA,kBAEJ,WAAY,WACZ,QAAS,EAIL,iBAEA,2BACA,kBAFA,iBAQJ,mBAAoB,QAGtB,SACE,SAAU,KAEV,OAAQ,SAGV,SAME,UAAW,EAEX,QAAS,EACT,OAAQ,EACR,OAAQ,EAKV,OACE,QAAS,MACT,MAAO,KACP,UAAW,KACX,QAAS,EACT,cAAe,MEjSX,UAtCW,OFyUf,YAAa,QACb,MAAO,QACP,YAAa,OAGf,SACE,eAAgB,SAIH,yCACA,yCACb,OAAQ,KAGT,cAKC,eAAgB,KAChB,mBAAoB,KAOP,yCACb,mBAAoB,KAQtB,6BACE,KAAM,QACN,mBAAoB,OAOtB,OACE,QAAS,aAGX,QACE,QAAS,UACT,OAAQ,QAGV,SACE,QAAS,KAKV,SACC,QAAS,eKzdX,IAAK,IAAK,IAAK,IAAK,IAAK,IADzB,GAAI,GAAI,GAAI,GAAI,GAAI,GAElB,cJuP4B,MItP5B,YJuP4B,QItP5B,YJuP4B,IItP5B,YJuP4B,IItP5B,MJuP4B,QIpP1B,IAAJ,GHgHM,UAtCW,OGzEb,IAAJ,GH+GM,UAtCW,KGxEb,IAAJ,GH8GM,UAtCW,QGvEb,IAAJ,GH6GM,UAtCW,OGtEb,IAAJ,GH4GM,UAtCW,QGrEb,IAAJ,GH2GM,UAtCW,KGnEjB,MHyGM,UAtCW,QGjEf,YJyP4B,IIrP9B,WHmGM,UAtCW,KG3Df,YJ4O4B,II3O5B,YJmO4B,IIjO9B,WH8FM,UAtCW,OGtDf,YJwO4B,IIvO5B,YJ8N4B,II5N9B,WHyFM,UAtCW,OGjDf,YJoO4B,IInO5B,YJyN4B,IIvN9B,WHoFM,UAtCW,OG5Cf,YJgO4B,II/N5B,YJoN4B,IDvL9B,GKpBE,WJkEO,KIjEP,cJiEO,KIhEP,OAAQ,EACR,WJwJ4B,IIxJC,MJrCpB,eI8CX,OADA,MHMI,UAAU,IGHZ,YJ6K4B,IIzK9B,MADA,KAEE,QJmN4B,KIlN5B,iBJ2N4B,QInN9B,eC/EE,aAAc,EACd,WAAY,KDmFd,aCpFE,aAAc,EACd,WAAY,KDsFd,kBACE,QAAS,aAEQ,mCACf,aJqM0B,MI3L9B,YHjCI,UAAU,IGmCZ,eAAgB,UAIlB,YACE,cJSO,KCMH,UAtCW,QG2BjB,mBACE,QAAS,MH7CP,UAAU,IG+CZ,MJtGS,QImGO,2BAMd,QAAS,aEnHb,WCIE,UAAW,KAGX,OAAQ,KDDV,eACE,QNqzBkC,OMpzBlC,iBNJS,KMKT,ONkM4B,IMlMI,MNFvB,QQTP,cRgN0B,OSrMxB,WTwzB8B,EAAE,IAAI,IAnzB/B,iBOVT,UAAW,KAGX,OAAQ,KDcV,QAEE,QAAS,aAGX,YACE,cAAe,MACf,YAAa,EAGf,gBLkCI,UAAU,IKhCZ,MNvBS,QUhBX,KTuEI,UAAU,MSrEZ,MVoCQ,QUnCR,UAAW,WAHb,OAOI,MAAO,QAKX,IACE,QVm3BkC,MACA,MC1zBhC,UAAU,MSxDZ,MVLS,KUMT,iBVGS,QQfP,cRkN0B,MSvMxB,WTiRwB,MAAM,EAAG,OAAM,EA5QlC,gBUAT,QACE,QAAS,ETkDT,UAAU,KShDV,YV2N0B,ISnOxB,WCSkB,KXuMxB,IWjME,QAAS,MTyCP,UAAU,MSvCZ,MVbS,QUgBT,SToCE,UAAU,QSlCV,MAAO,QACP,WAAY,OAKhB,gBACE,WV21BkC,MU11BlC,WAAY,OCxCZ,WCDA,MAAO,KACP,cAAe,MACf,aAAc,MACd,aAAc,KACd,YAAa,KCmDa,yBFtD1B,WCWI,UZ0KM,Oa/HgB,yBFtD1B,WCWI,UZ2KM,OahIgB,yBFtD1B,WCWI,UZ4KM,OajIiB,0BFtD3B,WCWI,UZ6KM,QWlLV,iBAME,cAAA,cAAA,cAAA,cCbF,MAAO,KACP,cAAe,MACf,aAAc,MACd,aAAc,KACd,YAAa,KCmDa,yBFrCtB,WALF,cAMI,UXmKI,Oa/HgB,yBFrCtB,WALF,cAAA,cAMI,UXoKI,OahIgB,yBFrCtB,WALF,cAAA,cAAA,cAMI,UXqKI,OajIiB,0BFrCvB,WALF,cAAA,cAAA,cAAA,cAMI,UXsKI,QWlJV,KCrBA,QAAS,KACT,UAAW,KACX,aAAc,OACd,YAAa,ODwBb,YACE,aAAc,EACd,YAAa,EAEX,iBACC,0BACD,cAAe,EACf,aAAc,EGlChB,KAhBF,OAYI,QAAA,QAAA,QAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAKF,UADA,QAJE,UAAA,WAAA,WAAA,WAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAKF,aADA,QAJE,UAAA,WAAA,WAAA,WAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAKF,aADA,QAJE,UAAA,WAAA,WAAA,WAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAKF,aADA,QAJE,UAAA,WAAA,WAAA,WAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAAA,UAKF,aAhBA,SAAU,SACV,MAAO,KACP,cAAe,MACf,aAAc,MAmBZ,KACE,WAAY,EACZ,UAAW,EACX,UAAW,KF+Bb,cACF,KAAM,EAAE,EAAE,KACV,UAAW,KAFT,cACF,KAAM,EAAE,EAAE,IACV,UAAW,IAFT,cACF,KAAM,EAAE,EAAE,WACV,UAAW,WAFT,cACF,KAAM,EAAE,EAAE,IACV,UAAW,IAFT,cACF,KAAM,EAAE,EAAE,IACV,UAAW,IAFT,cACF,KAAM,EAAE,EAAE,WACV,UAAW,WExBT,UFMJ,KAAM,EAAA,EAAA,KACN,MAAO,KACP,UAAW,KEHL,OFPN,KAAM,EAAE,EAAE,UAIV,UAAW,UEGL,OFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,OFPN,KAAM,EAAE,EAAE,IAIV,UAAW,IEGL,OFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,OFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,OFPN,KAAM,EAAE,EAAE,IAIV,UAAW,IEGL,OFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,OFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,OFPN,KAAM,EAAE,EAAE,IAIV,UAAW,IEGL,QFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,QFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,QFPN,KAAM,EAAE,EAAE,KAIV,UAAW,KEQP,aAAwB,MAAO,GAE/B,YAAuB,MdkJC,Gc/ItB,SAAwB,MADb,EACX,SAAwB,MADb,EACX,SAAwB,MADb,EACX,SAAwB,MADb,EACX,SAAwB,MADb,EACX,SAAwB,MADb,EACX,SAAwB,MADb,EACX,SAAwB,MADb,EACX,SAAwB,MADb,EACX,SAAwB,MADb,EACX,UAAwB,MADb,GACX,UAAwB,MADb,GACX,UAAwB,MADb,GAOT,UFRR,YAA8B,UEQtB,UFRR,YAA8B,WEQtB,UFRR,YAA8B,IEQtB,UFRR,YAA8B,WEQtB,UFRR,YAA8B,WEQtB,UFRR,YAA8B,IEQtB,UFRR,YAA8B,WEQtB,UFRR,YAA8B,WEQtB,UFRR,YAA8B,IEQtB,WFRR,YAA8B,WEQtB,WFRR,YAA8B,WCKJ,yBC9BtB,QACE,WAAY,EACZ,UAAW,EACX,UAAW,KF+Bb,iBACF,KAAM,EAAE,EAAE,KACV,UAAW,KAFT,iBACF,KAAM,EAAE,EAAE,IACV,UAAW,IAFT,iBACF,KAAM,EAAE,EAAE,WACV,UAAW,WAFT,iBACF,KAAM,EAAE,EAAE,IACV,UAAW,IAFT,iBACF,KAAM,EAAE,EAAE,IACV,UAAW,IAFT,iBACF,KAAM,EAAE,EAAE,WACV,UAAW,WExBT,aFMJ,KAAM,EAAA,EAAA,KACN,MAAO,KACP,UAAW,KEHL,UFPN,KAAM,EAAE,EAAE,UAIV,UAAW,UEGL,UFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,UFPN,KAAM,EAAE,EAAE,IAIV,UAAW,IEGL,UFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,UFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,UFPN,KAAM,EAAE,EAAE,IAIV,UAAW,IEGL,UFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,UFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,UFPN,KAAM,EAAE,EAAE,IAIV,UAAW,IEGL,WFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,WFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,WFPN,KAAM,EAAE,EAAE,KAIV,UAAW,KEQP,gBAAwB,MAAO,GAE/B,eAAuB,MdkJC,Gc/ItB,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,aAAwB,MADb,GACX,aAAwB,MADb,GACX,aAAwB,MADb,GAOT,aFRR,YAA2B,EEQnB,aFRR,YAA8B,UEQtB,aFRR,YAA8B,WEQtB,aFRR,YAA8B,IEQtB,aFRR,YAA8B,WEQtB,aFRR,YAA8B,WEQtB,aFRR,YAA8B,IEQtB,aFRR,YAA8B,WEQtB,aFRR,YAA8B,WEQtB,aFRR,YAA8B,IEQtB,cFRR,YAA8B,WEQtB,cFRR,YAA8B,YCKJ,yBC9BtB,QACE,WAAY,EACZ,UAAW,EACX,UAAW,KF+Bb,iBACF,KAAM,EAAE,EAAE,KACV,UAAW,KAFT,iBACF,KAAM,EAAE,EAAE,IACV,UAAW,IAFT,iBACF,KAAM,EAAE,EAAE,WACV,UAAW,WAFT,iBACF,KAAM,EAAE,EAAE,IACV,UAAW,IAFT,iBACF,KAAM,EAAE,EAAE,IACV,UAAW,IAFT,iBACF,KAAM,EAAE,EAAE,WACV,UAAW,WExBT,aFMJ,KAAM,EAAA,EAAA,KACN,MAAO,KACP,UAAW,KEHL,UFPN,KAAM,EAAE,EAAE,UAIV,UAAW,UEGL,UFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,UFPN,KAAM,EAAE,EAAE,IAIV,UAAW,IEGL,UFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,UFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,UFPN,KAAM,EAAE,EAAE,IAIV,UAAW,IEGL,UFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,UFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,UFPN,KAAM,EAAE,EAAE,IAIV,UAAW,IEGL,WFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,WFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,WFPN,KAAM,EAAE,EAAE,KAIV,UAAW,KEQP,gBAAwB,MAAO,GAE/B,eAAuB,MdkJC,Gc/ItB,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,aAAwB,MADb,GACX,aAAwB,MADb,GACX,aAAwB,MADb,GAOT,aFRR,YAA2B,EEQnB,aFRR,YAA8B,UEQtB,aFRR,YAA8B,WEQtB,aFRR,YAA8B,IEQtB,aFRR,YAA8B,WEQtB,aFRR,YAA8B,WEQtB,aFRR,YAA8B,IEQtB,aFRR,YAA8B,WEQtB,aFRR,YAA8B,WEQtB,aFRR,YAA8B,IEQtB,cFRR,YAA8B,WEQtB,cFRR,YAA8B,YCKJ,yBC9BtB,QACE,WAAY,EACZ,UAAW,EACX,UAAW,KF+Bb,iBACF,KAAM,EAAE,EAAE,KACV,UAAW,KAFT,iBACF,KAAM,EAAE,EAAE,IACV,UAAW,IAFT,iBACF,KAAM,EAAE,EAAE,WACV,UAAW,WAFT,iBACF,KAAM,EAAE,EAAE,IACV,UAAW,IAFT,iBACF,KAAM,EAAE,EAAE,IACV,UAAW,IAFT,iBACF,KAAM,EAAE,EAAE,WACV,UAAW,WExBT,aFMJ,KAAM,EAAA,EAAA,KACN,MAAO,KACP,UAAW,KEHL,UFPN,KAAM,EAAE,EAAE,UAIV,UAAW,UEGL,UFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,UFPN,KAAM,EAAE,EAAE,IAIV,UAAW,IEGL,UFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,UFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,UFPN,KAAM,EAAE,EAAE,IAIV,UAAW,IEGL,UFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,UFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,UFPN,KAAM,EAAE,EAAE,IAIV,UAAW,IEGL,WFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,WFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,WFPN,KAAM,EAAE,EAAE,KAIV,UAAW,KEQP,gBAAwB,MAAO,GAE/B,eAAuB,MdkJC,Gc/ItB,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,aAAwB,MADb,GACX,aAAwB,MADb,GACX,aAAwB,MADb,GAOT,aFRR,YAA2B,EEQnB,aFRR,YAA8B,UEQtB,aFRR,YAA8B,WEQtB,aFRR,YAA8B,IEQtB,aFRR,YAA8B,WEQtB,aFRR,YAA8B,WEQtB,aFRR,YAA8B,IEQtB,aFRR,YAA8B,WEQtB,aFRR,YAA8B,WEQtB,aFRR,YAA8B,IEQtB,cFRR,YAA8B,WEQtB,cFRR,YAA8B,YCKH,0BC9BvB,QACE,WAAY,EACZ,UAAW,EACX,UAAW,KF+Bb,iBACF,KAAM,EAAE,EAAE,KACV,UAAW,KAFT,iBACF,KAAM,EAAE,EAAE,IACV,UAAW,IAFT,iBACF,KAAM,EAAE,EAAE,WACV,UAAW,WAFT,iBACF,KAAM,EAAE,EAAE,IACV,UAAW,IAFT,iBACF,KAAM,EAAE,EAAE,IACV,UAAW,IAFT,iBACF,KAAM,EAAE,EAAE,WACV,UAAW,WExBT,aFMJ,KAAM,EAAA,EAAA,KACN,MAAO,KACP,UAAW,KEHL,UFPN,KAAM,EAAE,EAAE,UAIV,UAAW,UEGL,UFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,UFPN,KAAM,EAAE,EAAE,IAIV,UAAW,IEGL,UFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,UFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,UFPN,KAAM,EAAE,EAAE,IAIV,UAAW,IEGL,UFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,UFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,UFPN,KAAM,EAAE,EAAE,IAIV,UAAW,IEGL,WFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,WFPN,KAAM,EAAE,EAAE,WAIV,UAAW,WEGL,WFPN,KAAM,EAAE,EAAE,KAIV,UAAW,KEQP,gBAAwB,MAAO,GAE/B,eAAuB,MdkJC,Gc/ItB,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,YAAwB,MADb,EACX,aAAwB,MADb,GACX,aAAwB,MADb,GACX,aAAwB,MADb,GAOT,aFRR,YAA2B,EEQnB,aFRR,YAA8B,UEQtB,aFRR,YAA8B,WEQtB,aFRR,YAA8B,IEQtB,aFRR,YAA8B,WEQtB,aFRR,YAA8B,WEQtB,aFRR,YAA8B,IEQtB,aFRR,YAA8B,WEQtB,aFRR,YAA8B,WEQtB,aFRR,YAA8B,IEQtB,cFRR,YAA8B,WEQtB,cFRR,YAA8B,YGnDhC,OACE,MAAO,KACP,cfmHO,KelHP,MfaS,QeZT,iBf0S4B,YevS5B,UADA,UAEE,QfmS0B,OelS1B,eAAgB,IAChB,WfoM0B,IepMM,MfAzB,QeGH,gBACJ,eAAgB,OAChB,cAAe,IAA0B,MfLlC,QeQD,mBACN,WAAY,IAA0B,MfT/B,QeoBT,aADA,aAEE,Qf6Q0B,MepQ9B,gBACE,OfqK4B,IerKA,Mf/BnB,QekCT,mBADA,mBAEE,OfiK0B,IejKE,MfnCrB,QewCP,yBADA,yBAEE,oBAAqB,IASjB,8BAFR,qBADA,qBAEM,2BAEJ,OAAQ,EASc,yCACtB,iBftDO,gBGTR,4BY2EG,MfnEK,QeoEL,iBfnEK,iBgBhBT,eAGI,kBADA,kBAEA,iBCsFI,QD/EI,2BAFR,kBADA,kBAEM,wBAEJ,aC8EE,QdnFP,kCaiBK,iBAJe,QAMb,qCACA,qCACA,iBARa,QApBrB,iBAGI,oBADA,oBAEA,iBCsFI,QD/EI,6BAFR,oBADA,oBAEM,0BAEJ,aC8EE,QdnFP,oCaiBK,iBAJe,QAMb,uCACA,uCACA,iBARa,QApBrB,eAGI,kBADA,kBAEA,iBCsFI,QD/EI,2BAFR,kBADA,kBAEM,wBAEJ,aC8EE,QdnFP,kCaiBK,iBAJe,QAMb,qCACA,qCACA,iBARa,QApBrB,YAGI,eADA,eAEA,iBCsFI,QD/EI,wBAFR,eADA,eAEM,qBAEJ,aC8EE,QdnFP,+BaiBK,iBAJe,QAMb,kCACA,kCACA,iBARa,QApBrB,eAGI,kBADA,kBAEA,iBCsFI,QD/EI,2BAFR,kBADA,kBAEM,wBAEJ,aC8EE,QdnFP,kCaiBK,iBAJe,QAMb,qCACA,qCACA,iBARa,QApBrB,cAGI,iBADA,iBAEA,iBCsFI,QD/EI,0BAFR,iBADA,iBAEM,uBAEJ,aC8EE,QdnFP,iCaiBK,iBAJe,QAMb,oCACA,oCACA,iBARa,QApBrB,aAGI,gBADA,gBAEA,iBCsFI,QD/EI,yBAFR,gBADA,gBAEM,sBAEJ,aC8EE,QdnFP,gCaiBK,iBAJe,QAMb,mCACA,mCACA,iBARa,QApBrB,YAGI,eADA,eAEA,iBCsFI,QD/EI,wBAFR,eADA,eAEM,qBAEJ,aC8EE,QdnFP,+BaiBK,iBAJe,QAMb,kCACA,kCACA,iBARa,QApBrB,cAGI,iBADA,iBAEA,iBhBYK,iBGTR,iCaiBK,iBAJe,iBAMb,oCACA,oCACA,iBARa,iBDwFnB,sBACE,MfvGK,KewGL,iBf/FK,QegGL,af4MwB,QevM1B,uBACE,MfxGK,QeyGL,iBf9GK,Qe+GL,af9GK,QemHX,YACE,MfvHS,KewHT,iBf/GS,QekHT,eADA,eAEM,qBACJ,afwL0B,Qe/LnB,2BAWP,OAAQ,EAlEc,oDAuEpB,iBftIK,sBGCR,uCY4IK,Mf7IG,Ke8IH,iBf9IG,uBa8DoB,4BEiG3B,qBAEI,QAAS,MACT,MAAO,KACP,WAAY,KACZ,2BAA4B,MAG1B,qCACA,OAAQ,GF1Ga,4BEiG3B,qBAEI,QAAS,MACT,MAAO,KACP,WAAY,KACZ,2BAA4B,MAG1B,qCACA,OAAQ,GF1Ga,4BEiG3B,qBAEI,QAAS,MACT,MAAO,KACP,WAAY,KACZ,2BAA4B,MAG1B,qCACA,OAAQ,GF1Gc,6BEiG5B,qBAEI,QAAS,MACT,MAAO,KACP,WAAY,KACZ,2BAA4B,MAG1B,qCACA,OAAQ,GAdlB,kBAOQ,QAAS,MACT,MAAO,KACP,WAAY,KACZ,2BAA4B,MAG1B,kCACA,OAAQ,EG7KlB,cACE,QAAS,MACT,MAAO,KACP,OlB+ZsC,oBkB9ZtC,QlB8T4B,QACA,OC1MxB,UAtCW,KiB5Ef,YlBqO4B,IkBpO5B,YlBwO4B,IkBvO5B,MlBGS,QkBFT,iBlBLS,KkBMT,gBAAiB,YACjB,OlBgM4B,IkBhMA,MlBHnB,QQVP,cRgN0B,OSrMxB,WToYkC,MAAM,EAAE,EAAE,EA/XvC,YmBfL,WnB0akC,aAAa,KAAK,WAAW,CAAE,WAAW,KAAK,YmBra7C,uCDL1C,cCMM,WAAY,MDNL,0BAsBT,iBAAkB,YAClB,OAAQ,EAvBC,6BA4BT,MAAO,YACP,YAAa,EAAE,EAAE,ElBjBV,QoBLR,oBACC,MpBIO,QoBHP,iBpBJO,KoBKP,apB2YoC,QoB1YpC,QAAS,EAGP,WpBgYkC,MAAM,EAAE,EAAE,EA/XvC,WAAI,CAyYyB,KkBxZ3B,2BAqCT,MlBqXoC,QkBnXpC,QAAS,EAvCA,uBAgDT,wBACA,iBlB1CO,QkB4CP,QAAS,EAKJ,qCAML,MlBlDO,QkBmDP,iBlB1DO,KkB+DX,mBACA,oBACE,QAAS,MACT,MAAO,KAUT,gBACE,YDwBiC,oBCvBjC,eDuBiC,oBCtBjC,cAAe,EjBlBb,UAAU,QiBoBZ,YlB0J4B,IkBvJ9B,mBACE,YDgBiC,kBCfjC,eDeiC,kBhBe7B,UAtCW,QiBUf,YlB4G4B,IkBzG9B,mBACE,YDSiC,mBCRjC,eDQiC,mBhBe7B,UAtCW,QiBiBf,YlBsG4B,IkB7F9B,wBACE,QAAS,MACT,MAAO,KACP,QlBkN4B,QkBlNF,EAC1B,cAAe,EjBQX,UAtCW,KiBgCf,YlB6H4B,IkB5H5B,MlBtGS,QkBuGT,iBAAkB,YAClB,OAAQ,MAAA,YACR,alBqF4B,IkBrFM,EAVb,wCAAA,wCAcnB,cAAe,EACf,aAAc,EAYlB,iBACE,OlB4RsC,sBkB3RtC,QlBgM4B,OACA,MClNxB,UAtCW,QiByDf,YlB8D4B,IQ3M1B,cRkN0B,MkBjE9B,iBACE,OlBuRsC,qBkBtRtC,QlB4L4B,MACA,KCtNxB,UAtCW,QiBiEf,YlBqD4B,IQ1M1B,cRiN0B,MkBrD1B,8BADA,0BAEA,OAAQ,KAIJ,sBACN,OAAQ,KAQV,YACE,clB2QsC,KkBxQxC,WACE,QAAS,MACT,WlB6PsC,OkBrPxC,UACE,QAAS,KACT,UAAW,KACX,aAAc,KACd,YAAa,KAEX,eACC,wBACD,cAAe,IACf,aAAc,IASlB,YACE,SAAU,SACV,QAAS,MACT,alBkOsC,QkB/NxC,kBACE,SAAU,SACV,WlB8NsC,MkB7NtC,YlB4NsC,SkBxNzB,6CADC,8CAEZ,MlB5MO,QkBgNX,kBACE,cAAe,EAGjB,mBACE,QAAS,YACT,YAAa,OACb,aAAc,EACd,alB+MsC,OkB5MtC,qCACE,SAAU,OACV,WAAY,EACZ,alB0MoC,SkBzMpC,YAAa,EEpMf,gBACE,QAAS,KACT,MAAO,KACP,WpBmYoC,OC1WpC,UAAU,ImBvBV,MpBNM,QoBSR,eACE,SAAU,SACV,IAAK,KACL,QAAS,EACT,QAAS,KACT,UAAW,KACX,QpB2nB0B,OACA,MoB3nB1B,WAAY,MnBoEV,UAtCW,QmB5Bb,YpByL0B,IoBxL1B,MpBnDO,KoBoDP,iBpBpBM,mBQtCN,cRgN0B,OoBjJxB,0BACA,yBADA,sCACA,qCACA,QAAS,MArCV,uBADe,mCA4Cd,apBjCI,QoBoCF,cpBqVgC,QoBpVhC,iBHpCI,gQGqCJ,kBAAmB,UACnB,oBAAqB,MpBoVW,wBoBpVuB,OACvD,gBpBkVgC,sBAAA,sBoBpY1B,6BADY,yCAuDlB,apB5CE,QoB6CF,WAAY,EAAE,EAAE,EpBqUgB,EAlX9B,oBoBXU,2CACf,+BAgEG,cpBmUgC,QoBlUhC,oBAAqB,IpBoUW,wBoBpUqB,MpBoUrB,wBoBrYnC,wBADe,oCAyEd,apB9DI,QoBiEF,cHI2B,wBGH3B,WHjEI,uKfgiB6D,UAAU,MFzGhD,OEyG+E,MAAA,CAAA,IAAA,IAA+B,CehiBrI,gQjBjCD,KoBkGwE,UAAU,OAAA,MAAA,OAAA,CAAA,sBAAA,sBA5E/E,8BADY,0CAiFlB,apBtEE,QoBuEF,WAAY,EAAE,EAAE,EpB2SgB,EAlX9B,oBoB8EF,6CAAA,yDACA,MpB/EE,QoBkFF,2CACA,0CADA,uDACA,sDACA,QAAS,MAOT,qDAAA,iEACA,MpB5FE,QoB2FmB,6DAAA,yEAInB,apB/FA,QoBoGqB,qEAAA,iFACrB,aAAc,QC1IpB,iBD2I2B,QAKA,mEAAA,+EACrB,WAAY,EAAE,EAAE,EpBsQc,EAlX9B,oBoB+GqC,iFAAA,6FACrC,apBhHA,QoByHF,+CAAA,2DACA,apB1HE,QoB8HA,qDAAA,iEACA,apB/HA,QoBgIA,WAAY,EAAE,EAAE,EpBkPc,EAlX9B,oBoBCR,kBACE,QAAS,KACT,MAAO,KACP,WpBmYoC,OC1WpC,UAAU,ImBvBV,MpBTM,QoBYR,iBACE,SAAU,SACV,IAAK,KACL,QAAS,EACT,QAAS,KACT,UAAW,KACX,QpB2nB0B,OACA,MoB3nB1B,WAAY,MnBoEV,UAtCW,QmB5Bb,YpByL0B,IoBxL1B,MpBnDO,KoBoDP,iBpBvBM,mBQnCN,cRgN0B,OoBjJxB,8BACA,6BADA,0CACA,yCACA,QAAS,MArCV,yBADe,qCA4Cd,apBpCI,QoBuCF,cpBqVgC,QoBpVhC,iBHpCI,2TGqCJ,kBAAmB,UACnB,oBAAqB,MpBoVW,wBoBpVuB,OACvD,gBpBkVgC,sBAAA,sBoBpYxB,+BADY,2CAuDpB,apB/CE,QoBgDF,WAAY,EAAE,EAAE,EpBqUgB,EArX9B,oBoBRU,6CACf,iCAgEG,cpBmUgC,QoBlUhC,oBAAqB,IpBoUW,wBoBpUqB,MpBoUrB,wBoBrYnC,0BADe,sCAyEd,apBjEI,QoBoEF,cHI2B,wBGH3B,WHjEI,uKfgiB6D,UAAU,MFzGhD,OEyG+E,MAAA,CAAA,IAAA,IAA+B,CehiBrI,2TjBjCD,KoBkGwE,UAAU,OAAA,MAAA,OAAA,CAAA,sBAAA,sBA5E7E,gCADY,4CAiFpB,apBzEE,QoB0EF,WAAY,EAAE,EAAE,EpB2SgB,EArX9B,oBoBiFF,+CAAA,2DACA,MpBlFE,QoBqFF,+CACA,8CADA,2DACA,0DACA,QAAS,MAOT,uDAAA,mEACA,MpB/FE,QoB8FmB,+DAAA,2EAInB,apBlGA,QoBuGqB,uEAAA,mFACrB,aAAc,QC1IpB,iBD2I2B,QAKA,qEAAA,iFACrB,WAAY,EAAE,EAAE,EpBsQc,EArX9B,oBoBkHqC,mFAAA,+FACrC,apBnHA,QoB4HF,iDAAA,6DACA,apB7HE,QoBiIA,uDAAA,mEACA,apBlIA,QoBmIA,WAAY,EAAE,EAAE,EpBkPc,EArX9B,oBkBiOV,aACE,QAAS,KACT,UAAW,IAAA,KACX,YAAa,OAKb,yBACE,MAAO,KLtNiB,yBK2NxB,mBACE,QAAS,KACT,YAAa,OACb,gBAAiB,OACjB,cAAe,EAIjB,yBACE,QAAS,KACT,KAAM,EAAA,EAAA,KACN,UAAW,IAAA,KACX,YAAa,OACb,cAAe,EAIjB,2BACE,QAAS,aACT,MAAO,KACP,eAAgB,OAIlB,qCACE,QAAS,aAIX,4BADA,0BAEE,MAAO,KApCX,yBA0CI,QAAS,KACT,YAAa,OACb,gBAAiB,OACjB,MAAO,KACP,aAAc,EAEhB,+BACE,SAAU,SACV,YAAa,EACb,WAAY,EACZ,alBiHkC,OkBhHlC,YAAa,EAGf,6BACE,YAAa,OACb,gBAAiB,OAEnB,mCACE,cAAe,GIxUrB,KACE,QAAS,aAET,YtByO4B,IsBxO5B,MtBUS,QsBTT,WAAY,OAEZ,eAAgB,OAChB,OAA+C,QAC/C,YAAa,KACb,iBAAkB,YAClB,OtBiM4B,IsBjMF,MAAM,YCuFhC,QvBgO4B,QACA,OC1MxB,UAtCW,KsBiBf,YvB4I4B,IQjP1B,cRgN0B,OmB/MxB,WnBuXwB,MAAM,KAAK,WAAW,CAAE,iBAAiB,KAAK,WAAW,CAAE,aAAa,KAAK,WAAW,CAAE,WAAW,KAAK,YmBlX9F,uCGL1C,KHMM,WAAY,MhBAf,WmBUC,MtBFO,QsBGP,gBAAiB,KAjBjB,WAAA,WAsBA,QAAS,EACT,WtBmV0B,KsB1W1B,cAAA,cA6BA,QtB8U0B,ISjWxB,WaoBkB,KAIS,0CADA,0CbvB3B,WTkWwB,KsBhUzB,eACc,wBACjB,eAAgB,KAShB,aCvDA,MvBKS,KqBLP,iBrB8BM,QuB5BR,avB4BQ,QSpBJ,WT8VwB,KGlW3B,mBoBAC,MvBDO,KqBLP,iBED2D,QAS3D,aATqG,QAatG,mBADA,mBAEC,MvBRO,KqBLP,iBED2D,QAgB3D,aAhBqG,QAmBnG,WvBsVwB,IAAI,CuBtVC,EAAE,EAAE,EvBuVT,EuBvViC,oBAO5D,sBACA,sBACC,MvBtBO,KuBuBP,iBvBEM,QuBDN,avBCM,QuBOuB,kDADA,kDAEtB,mCACP,MvBlCO,KuBmCP,iBAzC+I,QA6C/I,aA7CyL,QAsCrJ,wDADA,wDAEb,yCAanB,WAAY,EAAE,EAAE,EvBsTM,EuBtTkB,oBDI9C,eCvDA,MvBKS,KqBLP,iBrBWO,QuBTT,avBSS,QSDL,WT8VwB,KGlW3B,qBoBAC,MvBDO,KqBLP,iBED2D,QAS3D,aATqG,QAatG,qBADA,qBAEC,MvBRO,KqBLP,iBED2D,QAgB3D,aAhBqG,QAmBnG,WvBsVwB,IAAI,CuBtVC,EAAE,EAAE,EvBuVT,EuBvViC,qBAO5D,wBACA,wBACC,MvBtBO,KuBuBP,iBvBjBO,QuBkBP,avBlBO,QuB0BsB,oDADA,oDAEtB,qCACP,MvBlCO,KuBmCP,iBAzC+I,QA6C/I,aA7CyL,QAsCrJ,0DADA,0DAEb,2CAanB,WAAY,EAAE,EAAE,EvBsTM,EuBtTkB,qBDI9C,aCvDA,MvBKS,KqBLP,iBrBqCM,QuBnCR,avBmCQ,QS3BJ,WT8VwB,KGlW3B,mBoBAC,MvBDO,KqBLP,iBED2D,QAS3D,aATqG,QAatG,mBADA,mBAEC,MvBRO,KqBLP,iBED2D,QAgB3D,aAhBqG,QAmBnG,WvBsVwB,IAAI,CuBtVC,EAAE,EAAE,EvBuVT,EuBvViC,mBAO5D,sBACA,sBACC,MvBtBO,KuBuBP,iBvBSM,QuBRN,avBQM,QuBAuB,kDADA,kDAEtB,mCACP,MvBlCO,KuBmCP,iBAzC+I,QA6C/I,aA7CyL,QAsCrJ,wDADA,wDAEb,yCAanB,WAAY,EAAE,EAAE,EvBsTM,EuBtTkB,mBDI9C,UCvDA,MvBKS,KqBLP,iBrBuCM,QuBrCR,avBqCQ,QS7BJ,WT8VwB,KGlW3B,gBoBAC,MvBDO,KqBLP,iBED2D,QAS3D,aATqG,QAatG,gBADA,gBAEC,MvBRO,KqBLP,iBED2D,QAgB3D,aAhBqG,QAmBnG,WvBsVwB,IAAI,CuBtVC,EAAE,EAAE,EvBuVT,EuBvViC,oBAO5D,mBACA,mBACC,MvBtBO,KuBuBP,iBvBWM,QuBVN,avBUM,QuBFuB,+CADA,+CAEtB,gCACP,MvBlCO,KuBmCP,iBAzC+I,QA6C/I,aA7CyL,QAsCrJ,qDADA,qDAEb,sCAanB,WAAY,EAAE,EAAE,EvBsTM,EuBtTkB,oBDI9C,aCvDA,MvBuFc,QqBvFZ,iBrBoCM,QuBlCR,avBkCQ,QS1BJ,WT8VwB,KGlW3B,mBoBAC,MvBiFY,QqBvFZ,iBED2D,QAS3D,aATqG,QAatG,mBADA,mBAEC,MvB0EY,QqBvFZ,iBED2D,QAgB3D,aAhBqG,QAmBnG,WvBsVwB,IAAI,CuBtVC,EAAE,EAAE,EvBuVT,EuBvViC,oBAO5D,sBACA,sBACC,MvB4DY,QuB3DZ,iBvBQM,QuBPN,avBOM,QuBCuB,kDADA,kDAEtB,mCACP,MvBgDY,QuB/CZ,iBAzC+I,QA6C/I,aA7CyL,QAsCrJ,wDADA,wDAEb,yCAanB,WAAY,EAAE,EAAE,EvBsTM,EuBtTkB,oBDI9C,YCvDA,MvBKS,KqBLP,iBrBkCM,QuBhCR,avBgCQ,QSxBJ,WT8VwB,KGlW3B,kBoBAC,MvBDO,KqBLP,iBED2D,QAS3D,aATqG,QAatG,kBADA,kBAEC,MvBRO,KqBLP,iBED2D,QAgB3D,aAhBqG,QAmBnG,WvBsVwB,IAAI,CuBtVC,EAAE,EAAE,EvBuVT,EuBvViC,mBAO5D,qBACA,qBACC,MvBtBO,KuBuBP,iBvBMM,QuBLN,avBKM,QuBGuB,iDADA,iDAEtB,kCACP,MvBlCO,KuBmCP,iBAzC+I,QA6C/I,aA7CyL,QAsCrJ,uDADA,uDAEb,wCAanB,WAAY,EAAE,EAAE,EvBsTM,EuBtTkB,mBDI9C,WCvDA,MvBuFc,QqBvFZ,iBrBMO,QuBJT,avBIS,QSIL,WT8VwB,KGlW3B,iBoBAC,MvBiFY,QqBvFZ,iBED2D,QAS3D,aATqG,QAatG,iBADA,iBAEC,MvB0EY,QqBvFZ,iBED2D,QAgB3D,aAhBqG,QAmBnG,WvBsVwB,IAAI,CuBtVC,EAAE,EAAE,EvBuVT,EuBvViC,qBAO5D,oBACA,oBACC,MvB4DY,QuB3DZ,iBvBtBO,QuBuBP,avBvBO,QuB+BsB,gDADA,gDAEtB,iCACP,MvBgDY,QuB/CZ,iBAzC+I,QA6C/I,aA7CyL,QAsCrJ,sDADA,sDAEb,uCAanB,WAAY,EAAE,EAAE,EvBsTM,EuBtTkB,qBDI9C,UCvDA,MvBKS,KqBLP,iBrBaO,QuBXT,avBWS,QSHL,WT8VwB,KGlW3B,gBoBAC,MvBDO,KqBLP,iBED2D,QAS3D,aATqG,QAatG,gBADA,gBAEC,MvBRO,KqBLP,iBED2D,QAgB3D,aAhBqG,QAmBnG,WvBsVwB,IAAI,CuBtVC,EAAE,EAAE,EvBuVT,EuBvViC,kBAO5D,mBACA,mBACC,MvBtBO,KuBuBP,iBvBfO,QuBgBP,avBhBO,QuBwBsB,+CADA,+CAEtB,gCACP,MvBlCO,KuBmCP,iBAzC+I,QA6C/I,aA7CyL,QAsCrJ,qDADA,qDAEb,sCAanB,WAAY,EAAE,EAAE,EvBsTM,EuBtTkB,kBDU9C,qBCHA,MvB5BQ,QuB6BR,avB7BQ,QGxBP,2BoBwDC,MvBzDO,KuB0DP,iBvBjCM,QuBkCN,avBlCM,QuBsCP,2BADA,2BAEC,WAAY,EAAE,EAAE,EvBoSU,EA3UpB,mBuB0CP,8BACA,8BACC,MvB5CM,QuB6CN,iBAAkB,YAIW,0DADA,0DAEtB,2CACP,MvB5EO,KuB6EP,iBvBpDM,QuBqDN,avBrDM,QuBiD8B,gEADA,gEAEb,iDAUnB,WAAY,EAAE,EAAE,EvB+QM,EA3UpB,mBsB+BR,uBCHA,MvB/CS,QuBgDT,avBhDS,QGLR,6BoBwDC,MvBzDO,KuB0DP,iBvBpDO,QuBqDP,avBrDO,QuByDR,6BADA,6BAEC,WAAY,EAAE,EAAE,EvBoSU,EA9VnB,qBuB6DR,gCACA,gCACC,MvB/DO,QuBgEP,iBAAkB,YAIW,4DADA,4DAEtB,6CACP,MvB5EO,KuB6EP,iBvBvEO,QuBwEP,avBxEO,QuBoE6B,kEADA,kEAEb,mDAUnB,WAAY,EAAE,EAAE,EvB+QM,EA9VnB,qBsBkDT,qBCHA,MvBrBQ,QuBsBR,avBtBQ,QG/BP,2BoBwDC,MvBzDO,KuB0DP,iBvB1BM,QuB2BN,avB3BM,QuB+BP,2BADA,2BAEC,WAAY,EAAE,EAAE,EvBoSU,EApUpB,mBuBmCP,8BACA,8BACC,MvBrCM,QuBsCN,iBAAkB,YAIW,0DADA,0DAEtB,2CACP,MvB5EO,KuB6EP,iBvB7CM,QuB8CN,avB9CM,QuB0C8B,gEADA,gEAEb,iDAUnB,WAAY,EAAE,EAAE,EvB+QM,EApUpB,mBsBwBR,kBCHA,MvBnBQ,QuBoBR,avBpBQ,QGjCP,wBoBwDC,MvBzDO,KuB0DP,iBvBxBM,QuByBN,avBzBM,QuB6BP,wBADA,wBAEC,WAAY,EAAE,EAAE,EvBoSU,EAlUpB,oBuBiCP,2BACA,2BACC,MvBnCM,QuBoCN,iBAAkB,YAIW,uDADA,uDAEtB,wCACP,MvB5EO,KuB6EP,iBvB3CM,QuB4CN,avB5CM,QuBwC8B,6DADA,6DAEb,8CAUnB,WAAY,EAAE,EAAE,EvB+QM,EAlUpB,oBsBsBR,qBCHA,MvBtBQ,QuBuBR,avBvBQ,QG9BP,2BoBwDC,MvByBY,QuBxBZ,iBvB3BM,QuB4BN,avB5BM,QuBgCP,2BADA,2BAEC,WAAY,EAAE,EAAE,EvBoSU,EArUpB,mBuBoCP,8BACA,8BACC,MvBtCM,QuBuCN,iBAAkB,YAIW,0DADA,0DAEtB,2CACP,MvBMY,QuBLZ,iBvB9CM,QuB+CN,avB/CM,QuB2C8B,gEADA,gEAEb,iDAUnB,WAAY,EAAE,EAAE,EvB+QM,EArUpB,mBsByBR,oBCHA,MvBxBQ,QuByBR,avBzBQ,QG5BP,0BoBwDC,MvBzDO,KuB0DP,iBvB7BM,QuB8BN,avB9BM,QuBkCP,0BADA,0BAEC,WAAY,EAAE,EAAE,EvBoSU,EAvUpB,mBuBsCP,6BACA,6BACC,MvBxCM,QuByCN,iBAAkB,YAIW,yDADA,yDAEtB,0CACP,MvB5EO,KuB6EP,iBvBhDM,QuBiDN,avBjDM,QuB6C8B,+DADA,+DAEb,gDAUnB,WAAY,EAAE,EAAE,EvB+QM,EAvUpB,mBsB2BR,mBCHA,MvBpDS,QuBqDT,avBrDS,QGAR,yBoBwDC,MvByBY,QuBxBZ,iBvBzDO,QuB0DP,avB1DO,QuB8DR,yBADA,yBAEC,WAAY,EAAE,EAAE,EvBoSU,EAnWnB,qBuBkER,4BACA,4BACC,MvBpEO,QuBqEP,iBAAkB,YAIW,wDADA,wDAEtB,yCACP,MvBMY,QuBLZ,iBvB5EO,QuB6EP,avB7EO,QuByE6B,8DADA,8DAEb,+CAUnB,WAAY,EAAE,EAAE,EvB+QM,EAnWnB,qBsBuDT,kBCHA,MvB7CS,QuB8CT,avB9CS,QGPR,wBoBwDC,MvBzDO,KuB0DP,iBvBlDO,QuBmDP,avBnDO,QuBuDR,wBADA,wBAEC,WAAY,EAAE,EAAE,EvBoSU,EA5VnB,kBuB2DR,2BACA,2BACC,MvB7DO,QuB8DP,iBAAkB,YAIW,uDADA,uDAEtB,wCACP,MvB5EO,KuB6EP,iBvBrEO,QuBsEP,avBtEO,QuBkE6B,6DADA,6DAEb,8CAUnB,WAAY,EAAE,EAAE,EvB+QM,EA5VnB,kBsB2DX,UACE,YtBmK4B,IsBlK5B,MtB5CQ,QsB6CR,gBtByE0B,KG9IzB,gBmBwEC,MtBuEwB,QsBtExB,gBtBuEwB,KsB9EnB,gBAAA,gBAYL,gBtBkEwB,KsBjExB,WAAY,KAbP,mBAAA,mBAkBL,MtB/EO,QsBgFP,eAAgB,KEpCJ,mBF+ChB,QCJE,QvB4O4B,MACA,KCtNxB,UAtCW,QsBiBf,YvBqG4B,IQ1M1B,cRiN0B,MwB1Jd,mBFoDhB,QCRE,QvBwO4B,OACA,MClNxB,UAtCW,QsBiBf,YvBsG4B,IQ3M1B,cRkN0B,MsB9F9B,WACE,QAAS,MACT,MAAO,KAGL,sBACA,WtBuP0B,MsB/O3B,6BAAA,4BAAA,6BACC,MAAO,KGxIX,MNMM,WnByNwB,QAAQ,KAAK,OmBpND,uCMX1C,MNYM,WAAY,MMTL,iBACT,QAAS,EADA,qBAOT,QAAS,KAIb,YACE,SAAU,SACV,OAAQ,EACR,SAAU,ONXN,WnB0NwB,OAAO,KAAK,KmBrNA,uCMG1C,YNFM,WAAY,MOTlB,UACA,UAFA,WADA,QAIE,SAAU,SAGZ,iBACE,YAAa,OCoBV,wBACC,QAAS,aACT,YzBkOwB,OyBjOxB,ezBgOwB,OyB/NxB,QAAS,GAhCb,W3B2N4B,K2B3NH,MACzB,a3B0N4B,K2B1ND,MAAM,YACjC,cAAe,EACf,Y3BwN4B,K2BxNF,MAAM,YAqDvB,8BACL,YAAa,ED1CnB,eACE,SAAU,SACV,IAAK,KACL,KAAM,EACN,Q1BsiBkC,K0BriBlC,QAAS,KACT,MAAO,KACP,U1BqgBkC,M0BpgBlC,Q1BqgBkC,M0BrgBL,EAC7B,O1BqgBkC,Q0BrgBT,EAAE,EzBsGvB,UAtCW,KyB9Df,M1BPS,Q0BQT,WAAY,KACZ,WAAY,KACZ,iB1BnBS,K0BoBT,gBAAiB,YACjB,O1BkL4B,I0BlLG,M1BXtB,gBQhBP,cRgN0B,OSrMxB,WTohB8B,EAAE,MAAM,KA/gBjC,iB0BoBP,oBACE,MAAO,KACP,KAAM,EAGR,qBACE,MAAO,EACP,KAAM,KbYgB,yBanBxB,uBACE,MAAO,KACP,KAAM,EAGR,wBACE,MAAO,EACP,KAAM,MbYgB,yBanBxB,uBACE,MAAO,KACP,KAAM,EAGR,wBACE,MAAO,EACP,KAAM,MbYgB,yBanBxB,uBACE,MAAO,KACP,KAAM,EAGR,wBACE,MAAO,EACP,KAAM,MbYiB,0BanBzB,uBACE,MAAO,KACP,KAAM,EAGR,wBACE,MAAO,EACP,KAAM,MAQV,uBACE,IAAK,KACL,OAAQ,KACR,WAAY,EACZ,c1BkegC,Q2BjgB/B,gCACC,QAAS,aACT,YzBkOwB,OyBjOxB,ezBgOwB,OyB/NxB,QAAS,GAzBb,WAAY,EACZ,a3BmN4B,K2BnND,MAAM,YACjC,c3BkN4B,K2BlNA,MAC5B,Y3BiN4B,K2BjNF,MAAM,YA8CvB,sCACL,YAAa,EDWjB,0BACE,IAAK,EACL,MAAO,KACP,KAAM,KACN,WAAY,EACZ,Y1BodgC,Q2BjgB/B,mCACC,QAAS,aACT,YzBkOwB,OyBjOxB,ezBgOwB,OyB/NxB,QAAS,GAlBb,W3B6M4B,K2B7MH,MAAM,YAC/B,aAAc,EACd,c3B2M4B,K2B3MA,MAAM,YAClC,Y3B0M4B,K2B1MF,MAuCjB,yCACL,YAAa,EA7Bd,mCDmDC,eAAgB,EAMpB,yBACE,IAAK,EACL,MAAO,KACP,KAAM,KACN,WAAY,EACZ,a1BmcgC,Q2BjgB/B,kCACC,QAAS,aACT,YzBkOwB,OyBjOxB,ezBgOwB,OyB/NxB,QAAS,GAJV,kCAgBG,QAAS,KAGV,mCACC,QAAS,aACT,azB+MsB,OyB9MtB,ezB6MsB,OyB5MtB,QAAS,GA9Bf,W3BsM4B,K2BtMH,MAAM,YAC/B,a3BqM4B,K2BrMD,MAC3B,c3BoM4B,K2BpMA,MAAM,YAiCzB,wCACL,YAAa,EAVZ,mCDiDD,eAAgB,EAUlB,oCACA,kCAFA,mCADA,iCAIA,MAAO,KACP,OAAQ,KAKZ,kBE9GE,OAAQ,EACR,O1BssBkC,M0BtsBhB,EAClB,SAAU,OACV,WAAY,IAAI,M5BKP,Q0B6GX,eACE,QAAS,MACT,MAAO,KACP,Q1BkbkC,OACA,K0BlblC,MAAO,KACP,Y1BmH4B,I0BlH5B,M1B5GS,Q0B6GT,WAAY,QACZ,YAAa,OACb,iBAAkB,YAClB,OAAQ,EvBnHP,qBADA,qBuBmIC,M1BoZgC,Q0BnZhC,gBAAiB,KL9IjB,iBrBMO,Q0B8GG,sBAAA,sBAgCV,M1B/IO,K0BgJP,gBAAiB,KLrJjB,iBrB8BM,Q0BsFI,wBAAA,wBAuCV,M1BhJO,Q0BiJP,eAAgB,KAChB,iBAAkB,YAQR,oBACZ,QAAS,MAIX,iBACE,QAAS,MACT,Q1B2WkC,MAmBA,K0B7XlC,cAAe,EzBpDX,UAtCW,QyB4Ff,M1BpKS,Q0BqKT,YAAa,OAIf,oBACE,QAAS,MACT,Q1BmXkC,OACA,K0BnXlC,M1BzKS,QwBjBX,WACA,oBACE,SAAU,SACV,QAAS,YACT,eAAgB,OAEd,yBAAA,gBACA,SAAU,SACV,KAAM,EAAA,EAAA,KrBCP,+BAAA,sBqBIG,QAAS,EAPP,gCAAA,gCAAA,+BAAA,uBAAA,uBAAA,sBAYF,QAAS,EAMf,aACE,QAAS,KACT,UAAW,KACX,gBAAiB,WAEjB,0BACE,MAAO,KAMc,wCAAA,kCAErB,YxByK0B,KwBpKI,4CEtClB,uDlBaZ,wBgB0B6B,EhBzB7B,2BgByB6B,EAIE,6CAZV,kChBJrB,uBgBiB4B,EhBhB5B,0BgBgB4B,EAgBhC,uBACE,cAAe,SACf,aAAc,SAFM,8BAAA,yCAAA,sCAOlB,YAAa,EAPK,yCAWlB,aAAc,EAIR,0CAAA,+BACR,cAAe,QACf,aAAc,QAGN,0CAAA,+BACR,cAAe,OACf,aAAc,OAMA,iCfhFV,WTkWwB,KwBlRE,0CfhF1B,WeqFkB,KASxB,oBACE,eAAgB,OAChB,YAAa,WACb,gBAAiB,OAEf,yBACA,+BACA,MAAO,KA9Ec,iDAAA,2CAmFrB,WxBwF0B,KwBnFI,qDEvHlB,gElBoBZ,2BgBoG8B,EhBnG9B,0BgBmG8B,EAIC,sDA7FV,2ChBzBrB,uBgBuH2B,EhBtH3B,wBgBsH2B,EAkB3B,uBACa,kCACb,cAAe,EAGT,4CADA,yCACA,uDADA,oDAEJ,SAAU,SACV,KAAM,cACN,eAAgB,KKzJtB,aACE,SAAU,SACV,QAAS,KACT,UAAW,KACX,YAAa,QACb,MAAO,KAKL,0BADA,4BAFA,2BACA,qCAGA,SAAU,SACV,KAAM,EAAA,EAAA,GACN,UAAW,EACX,cAAe,EAIb,uCADA,yCADA,wCAEA,yCADA,2CADA,0CAEA,wCADA,0CADA,yCAEA,kDADA,oDADA,mDAGA,Y7ByLwB,K6BlLc,sEAD1B,kCADD,iCAGb,QAAS,EAIsB,mDAC/B,QAAS,EzBwDM,6CAAA,4CIzEf,wBqBsBkD,ErBrBlD,2BqBqBkD,ELJ7B,8CAAA,6ChBJrB,uBqBSkD,ErBRlD,0BqBQkD,EAKlD,0BACA,QAAS,KACT,YAAa,OAEM,8DACkB,qErBjCrC,wBqBiC4E,ErBhC5E,2BqBgC4E,EACxD,+DrBpBpB,uBqBoBqE,ErBnBrE,0BqBmBqE,EAYzE,oBADA,qBAEE,QAAS,KAKT,yBAAA,0BACE,SAAU,SACV,QAAS,EAFP,+BAAA,gCAKA,QAAS,EAIN,8BACA,2CAEa,2CADA,wDAFb,+BACA,4CAEa,4CADA,yDAElB,Y7B4H0B,K6BxH9B,qBAAuB,a7BwHO,K6BvH9B,oBAAsB,Y7BuHQ,K6B/G9B,kBACE,QAAS,KACT,YAAa,OACb,Q7BkO4B,QACA,O6BlO5B,cAAe,E5BwBX,UAtCW,K4BgBf,Y7ByI4B,I6BxI5B,Y7B4I4B,I6B3I5B,M7BzFS,Q6B0FT,WAAY,OACZ,YAAa,OACb,iB7BjGS,Q6BkGT,O7BmG4B,I6BnGA,M7BhGnB,QQVP,cRgN0B,O6BjGtB,uCADA,oCAEJ,WAAY,EAWE,+B9BsQV,4C8BrQN,O7B6SsC,qB6BzStB,+BADA,8BAKsB,yCAFA,sDACC,0CAFA,uDAIvC,Q7B0M4B,MACA,KCtNxB,UAtCW,Q4BmDf,Y7BmE4B,IQ1M1B,cRiN0B,M6BrEZ,+B9BqPV,4C8BpPN,O7ByRsC,sB6BrRtB,+BADA,8BAKsB,yCAFA,sDACC,0CAFA,uDAIvC,Q7BqL4B,OACA,MClNxB,UAtCW,Q4BoEf,Y7BmD4B,IQ3M1B,cRkN0B,M6BtDZ,+BACA,+BAChB,cAAe,QH3JD,wFtBsFG,+EyBkFmC,uDACA,oEAHhB,uCACA,oDrB1JlC,wBqB+J2B,ErB9J3B,2BqB8J2B,EAGM,sCACA,mDLjJZ,qEAAA,kFKkJ+B,yDACA,sErBvJpD,uBqB0J0B,ErBzJ1B,0BqByJ0B,ECpL9B,gBACE,SAAU,SACV,QAAS,MACT,WAAY,OACZ,aAAc,OAGhB,uBACE,QAAS,YACT,a9BgbsC,K8B7axC,sBACE,SAAU,SACV,KAAM,EACN,QAAS,GACT,M9B2asC,K8B1atC,OAAQ,QACR,QAAS,EAEwB,4DAC/B,M9BnBO,K8BoBP,a9BKM,QqB9BN,iBrB8BM,QSpBJ,WT+b0C,K8B3af,0DAG3B,W9B6WkC,MAAM,EAAE,EAAE,EA/XvC,WAAI,CAyYyB,K8BjXO,wEAC3C,a9B6WoC,Q8B1WS,0EAC7C,M9BvCO,K8BwCP,iB9Bia4C,Q8Bha5C,a9Bga4C,QSpc1C,WTqc0C,K8B1Z1C,qDAAA,sDACA,M9B3CK,Q8B0CgB,6DAAA,8DAInB,iB9BlDG,Q8B4DX,sBACE,SAAU,SACV,cAAe,EAEf,eAAgB,IAJG,8BASjB,SAAU,SACV,IAAK,OACL,KAAQ,QACR,QAAS,MACT,M9B8WoC,K8B7WpC,O9B6WoC,K8B5WpC,eAAgB,KAChB,QAAS,GACT,iB9B5EO,Q8B6EP,O9B3EO,Q8B2EwC,M9BuHrB,ISlMxB,WTubkC,MAAM,EAAE,OAAO,OAlb5C,e8BoDU,6BAwBjB,SAAU,SACV,IAAK,OACL,KAAQ,QACR,QAAS,MACT,M9B+VoC,K8B9VpC,O9B8VoC,K8B7VpC,QAAS,GACT,WAAY,UAAU,GAAA,CAAA,IAAA,IAUH,+CtB7GnB,cRgN0B,O8B/FyB,4EAEjD,iBb5EM,4MagFiD,mFAEvD,a9B1FI,QqB9BN,iBrB8BM,QSpBJ,WT6c+C,K8BjWQ,kFAOvD,iBbvFM,yJa4FyB,sFAC/B,iB9BrGI,mB8BuGiC,4FACrC,iB9BxGI,mB8BkHa,4CAEnB,c9BuU4C,I8BpUO,yEAEjD,iBbjHM,sJasHyB,mFAC/B,iB9B/HI,mB8ByIV,eACE,aAAc,QAEO,6CAEjB,KAAQ,SACR,M5BsY0C,Q4BrY1C,eAAgB,IAEhB,c5BoY0C,M4B1YzB,4CAUjB,Ib1E6B,mBa2E7B,Kb3E6B,qBa4E7B,MbxD6B,iBayD7B,ObzD6B,iBa0D7B,iB9B9KK,Q8BgLL,c5B0X0C,MiBpjB1C,WW2LoB,UAAU,KAAK,WAAW,C5B2UZ,iBAAiB,KAAK,WAAW,CAAE,aAAa,KAAK,WAAW,CAAE,WAAW,KAAK,YiBjgBhF,uCWqKnB,4CXpKjB,WAAY,MWyLqC,0EAEjD,iB9BzLK,Q8B0LL,UAAW,mBAKoB,oFAC/B,iB9B1KI,mB8BsLV,eACE,QAAS,aACT,MAAO,KACP,O9B2MsC,oB8B1MtC,Q9BoQkC,Q8BpQA,Q9BoQA,QACD,OCpW7B,UAtCW,K6BwIf,Y9BiB4B,I8BhB5B,Y9BoB4B,I8BnB5B,M9BjNS,Q8BkNT,eAAgB,OAChB,W9B1NS,KiBiCC,uKfgiB6D,UAAU,MFzGhD,OEyG+E,MAAA,CAAA,IAAA,K4BtWhH,O9BpB4B,I8BoBQ,M9BvN3B,QQVP,cRgN0B,OSrMxB,WPqkB8B,MAAM,EAAE,IAAI,IFhkBrC,iB8BoNT,WAAY,KAfA,qBAkBV,a9B+KoC,Q8B9KpC,QAAS,EAEP,W5BsW8B,MAAM,EAAE,IAAI,IFhkBrC,gBAAI,CA8dqB,K8BxQ3B,gCAeH,M9BxOK,Q8ByOL,iB9BhPK,K8BoPP,yBACW,qCACX,OAAQ,KACR,c9BiO+B,O8BhO/B,iBAAkB,KAzCR,wBA6CV,M9BtPO,Q8BuPP,iB9B3PO,Q8B6MG,2BAmDV,QAAS,KAnDC,8BAwDV,MAAO,YACP,YAAa,EAAE,EAAE,E9BjQV,Q8BqQX,kBACE,O9BmJsC,sB8BlJtC,Y9BuD4B,O8BtD5B,e9BsD4B,O8BrD5B,a9BsD4B,MCzQ1B,UAAU,I6BuNd,kBACE,O9B8IsC,qB8B7ItC,Y9BmD4B,M8BlD5B,e9BkD4B,M8BjD5B,a9BkD4B,KC7Q1B,UAAU,K6BoOd,aACE,SAAU,SACV,QAAS,aACT,MAAO,KACP,O9BwHsC,oB8BvHtC,cAAe,EAGjB,mBACE,SAAU,SACV,QAAS,EACT,MAAO,KACP,O9BgHsC,oB8B/GtC,OAAQ,EACR,QAAS,EAEC,4CACR,a9B8FoC,Q8B7FpC,W9BqLgC,K8BhLrB,+CADC,gDAEZ,iB9BvTO,Q8B2TwB,sDAC7B,Q9BkMM,S8B9LuB,0DAC/B,QAAS,kBAIb,mBACE,SAAU,SACV,IAAK,EACL,MAAO,EACP,KAAM,EACN,QAAS,EACT,O9BgFsC,oB8B/EtC,Q9BjB4B,QACA,O8BkB5B,Y9BzG4B,I8B0G5B,Y9BtG4B,I8BuG5B,M9B3US,Q8B4UT,iB9BnVS,K8BoVT,O9B7I4B,I8B6IM,M9BhVzB,QQVP,cRgN0B,OSrMxB,WTme8B,K8BjKlB,0BAkBd,SAAU,SACV,IAAK,EACL,MAAO,EACP,OAAQ,EACR,QAAS,EACT,QAAS,MACT,O9B0DoC,Q8BzDpC,Q9BnC0B,QACA,O8BmC1B,Y9BtH0B,I8BuH1B,M9B3VO,Q8B4VP,QAAS,STxWT,iBrBOO,Q8BmWP,YAAa,QtB3Wb,csB4WuB,E9B5JG,OAAA,O8B4JqD,EAUnF,cACE,MAAO,KACP,ObhRU,KaiRV,QAAS,EACT,iBAAkB,YAClB,WAAY,KALD,oBAQT,QAAS,EADJ,0CAKqB,W9BuIe,EAAE,EAAE,EAAE,IAngBxC,IAAO,CAmUY,EAAE,EAAE,EAFJ,MAxSpB,oB8B8VD,sCAMqB,W9BsIe,EAAE,EAAE,EAAE,IAngBxC,IAAO,CAmUY,EAAE,EAAE,EAFJ,MAxSpB,oB8B8VD,+BAOqB,W9BqIe,EAAE,EAAE,EAAE,IAngBxC,IAAO,CAmUY,EAAE,EAAE,EAFJ,MAxSpB,oB8BuVG,gCAkBT,OAAQ,EAlBC,oCAsBT,M5B2NyC,K4B1NzC,O5B0NyC,K4BzNzC,WAAY,QT7YZ,iBrB8BM,Q8BiXN,O5B0NyC,EM1mBzC,cN2mByC,KOhmBvC,WPimBuC,EAAE,MAAM,OF5lB1C,emBfL,WjBsgBkC,iBAAiB,KAAK,WAAW,CAAE,aAAa,KAAK,WAAW,CAAE,WAAW,KAAK,Y4BnHtH,WAAY,KX9Y0B,uCWgX7B,oCX/WP,WAAY,MWoYO,2CT1YrB,iBnB8mByC,Q4BzPhC,6CAsCT,M5BoMgC,K4BnMhC,O5BoMgC,M4BnMhC,MAAO,YACP,O5BmMgC,Q4BlMhC,iB9BvZO,Q8BwZP,aAAc,YtBjad,cNomBgC,KOzlB9B,WP0lB8B,MAAM,EAAE,OAAO,OFrlBxC,e8BsWE,gCAiDT,M5BgMyC,K4B/LzC,O5B+LyC,KmBtmBzC,iBrB8BM,Q8B2YN,O5BgMyC,EM1mBzC,cN2mByC,KOhmBvC,WPimBuC,EAAE,MAAM,OF5lB1C,emBfL,WjBsgBkC,iBAAiB,KAAK,WAAW,CAAE,aAAa,KAAK,WAAW,CAAE,WAAW,KAAK,Y4BzFtH,WAAY,KXxa0B,uCWgX7B,gCX/WP,WAAY,MW+ZG,uCTrajB,iBnB8mByC,Q4BzPhC,gCAgET,M5B0KgC,K4BzKhC,O5B0KgC,M4BzKhC,MAAO,YACP,O5ByKgC,Q4BxKhC,iB9BjbO,Q8BkbP,aAAc,YtB3bd,cNomBgC,KOzlB9B,WP0lB8B,MAAM,EAAE,OAAO,OFrlBxC,e8BsWE,yBA2ET,M5BsKyC,K4BrKzC,O5BqKyC,K4BpKzC,WAAY,EACZ,a9B5CoC,E8B6CpC,Y9B7CoC,EqBvZpC,iBrB8BM,Q8BwaN,O5BmKyC,EM1mBzC,cN2mByC,KOhmBvC,WPimBuC,EAAE,MAAM,OF5lB1C,emBfL,WjBsgBkC,iBAAiB,KAAK,WAAW,CAAE,aAAa,KAAK,WAAW,CAAE,WAAW,KAAK,Y4B5DtH,WAAY,KXrc0B,uCWgX7B,yBX/WP,WAAY,MWybJ,gCT/bV,iBnB8mByC,Q4BzPhC,yBA6FT,M5B6IgC,K4B5IhC,O5B6IgC,M4B5IhC,MAAO,YACP,O5B4IgC,Q4B3IhC,iBAAkB,YAClB,aAAc,YACd,aAAc,MrB9cZ,WP0lB8B,MAAM,EAAE,OAAO,OFrlBxC,e8BsWE,8BAwGT,iB9BrdO,QQTP,cNomBgC,K4B9OvB,8BA6GT,aAAc,KACd,iB9B3dO,QQTP,cNomBgC,K4B5HxB,6CAEN,iB9B/dK,Q8B6dC,sDAMN,OAAQ,QANF,yCAUN,iB9BveK,Q8B6dC,yCAcN,OAAQ,QAdF,kCAkBN,iB9B/eK,Q8BofU,8BACrB,mBACA,eXhgBM,WjBsgBkC,iBAAiB,KAAK,WAAW,CAAE,aAAa,KAAK,WAAW,CAAE,WAAW,KAAK,YiBjgBhF,uCWyfrB,8BACrB,mBACA,eX1fM,WAAY,MYPlB,KACE,QAAS,KACT,UAAW,KACX,aAAc,EACd,cAAe,EACf,WAAY,KAGd,UACE,QAAS,MACT,Q/BqjBkC,MACA,KGpjBjC,gBADA,gB4BEC,gBAAiB,KALZ,mBAUL,M/BNO,Q+BOP,eAAgB,KAChB,OAAQ,QAQZ,UACE,c/BgL4B,I+BhLU,M/BpB7B,Q+BsBT,oBACE,c/B6K0B,K+B1K5B,oBACE,O/ByK0B,I+BzKK,MAAM,YvB3BrC,uBRuM0B,OQtM1B,wBRsM0B,OGpM3B,0BADA,0B4B6BG,a/BhCK,QAAA,QACA,Q+B0BA,6BASL,M/BhCK,Q+BiCL,iBAAkB,YAClB,aAAc,YAKH,mCADN,2BAEP,M/BvCO,Q+BwCP,iB/B/CO,K+BgDP,a/B7CO,QAAA,QAHA,K+BmDT,yBAEE,W/BkJ0B,KQpM1B,uBuBoD2B,EvBnD3B,wBuBmD2B,EAU7B,qBvBvEE,cRgN0B,O+BrInB,4BACD,2BACN,M/BvEO,K+BwEP,iB/B/CM,Q+ByDR,oBACE,KAAM,EAAA,EAAA,KACN,WAAY,OAKd,yBACE,WAAY,EACZ,UAAW,EACX,WAAY,OAUZ,uBACA,QAAS,KAET,qBACA,QAAS,MCpGb,QACE,SAAU,SACV,QAAS,KACT,UAAW,KACX,YAAa,OACb,gBAAiB,cACjB,QhC+jBkC,MACA,MgCrjBlC,mBACA,yBrBjBE,sBAAA,sBAAA,sBAAA,sBqBUA,QAAS,KACT,UAAW,KACX,YAAa,OACb,gBAAiB,cAoBrB,cACE,QAAS,aACT,YhC0iBkC,SgCziBlC,ehCyiBkC,SgCxiBlC,ahCgiBkC,MCxd9B,UAtCW,Q+BhCf,YAAa,QACb,YAAa,O7BzCZ,oBADA,oB6B6CC,gBAAiB,KASrB,YACE,QAAS,KACT,eAAgB,OAChB,aAAc,EACd,cAAe,EACf,WAAY,KAEZ,sBACE,cAAe,EACf,aAAc,EAGhB,2BACE,SAAU,OACV,MAAO,KASX,aACE,QAAS,aACT,YhCqekC,MgCpelC,ehCoekC,MgCxdpC,iBACE,WAAY,KACZ,UAAW,EAGX,YAAa,OAIf,gBACE,QhC2ekC,OACA,OCne9B,UAtCW,Q+B+Bf,YAAa,EACb,iBAAkB,YAClB,OhCwF4B,IgCxFN,MAAM,YxBrH1B,cRgN0B,OGpM3B,sBADA,sB6B8GC,gBAAiB,KAMrB,qBACE,QAAS,aACT,MAAO,MACP,OAAQ,MACR,eAAgB,OAChB,QAAS,GACT,WAAY,UAAA,OAAA,OACZ,gBAAiB,KAAA,KnBlEY,4BmBmFrB,6BACA,mCrB3IN,gCAAA,gCAAA,gCAAA,gCqBsIM,cAAe,EACf,aAAc,GnB7FI,yBmByFxB,kBAoBI,UAAW,IAAA,OACX,gBAAiB,WAEjB,8BACE,eAAgB,IAEhB,6CACE,SAAU,SAGZ,wCACE,chCqawB,KgCpaxB,ahCoawB,KgC7b1B,6BACA,mCrB3IN,gCAAA,gCAAA,gCAAA,gCqByKM,UAAW,OAcb,mCACE,QAAS,eAGT,WAAY,KAGd,kCACE,QAAS,MnBxIY,4BmBmFrB,6BACA,mCrB3IN,gCAAA,gCAAA,gCAAA,gCqBsIM,cAAe,EACf,aAAc,GnB7FI,yBmByFxB,kBAoBI,UAAW,IAAA,OACX,gBAAiB,WAEjB,8BACE,eAAgB,IAEhB,6CACE,SAAU,SAGZ,wCACE,chCqawB,KgCpaxB,ahCoawB,KgC7b1B,6BACA,mCrB3IN,gCAAA,gCAAA,gCAAA,gCqByKM,UAAW,OAcb,mCACE,QAAS,eAGT,WAAY,KAGd,kCACE,QAAS,MnBxIY,4BmBmFrB,6BACA,mCrB3IN,gCAAA,gCAAA,gCAAA,gCqBsIM,cAAe,EACf,aAAc,GnB7FI,yBmByFxB,kBAoBI,UAAW,IAAA,OACX,gBAAiB,WAEjB,8BACE,eAAgB,IAEhB,6CACE,SAAU,SAGZ,wCACE,chCqawB,KgCpaxB,ahCoawB,KgC7b1B,6BACA,mCrB3IN,gCAAA,gCAAA,gCAAA,gCqByKM,UAAW,OAcb,mCACE,QAAS,eAGT,WAAY,KAGd,kCACE,QAAS,MnBxIa,6BmBmFtB,6BACA,mCrB3IN,gCAAA,gCAAA,gCAAA,gCqBsIM,cAAe,EACf,aAAc,GnB7FK,0BmByFzB,kBAoBI,UAAW,IAAA,OACX,gBAAiB,WAEjB,8BACE,eAAgB,IAEhB,6CACE,SAAU,SAGZ,wCACE,chCqawB,KgCpaxB,ahCoawB,KgC7b1B,6BACA,mCrB3IN,gCAAA,gCAAA,gCAAA,gCqByKM,UAAW,OAcb,mCACE,QAAS,eAGT,WAAY,KAGd,kCACE,QAAS,MAjEnB,eAyBQ,UAAW,IAAA,OACX,gBAAiB,WAdf,0BACA,gCrB3IN,6BAAA,6BAAA,6BAAA,6BqBsIM,cAAe,EACf,aAAc,EAmBhB,2BACE,eAAgB,IAEhB,0CACE,SAAU,SAGZ,qCACE,chCqawB,KgCpaxB,ahCoawB,KgC7b1B,0BACA,gCrB3IN,6BAAA,6BAAA,6BAAA,6BqByKM,UAAW,OAcb,gCACE,QAAS,eAGT,WAAY,KAGd,+BACE,QAAS,KAcjB,4BACE,MhC3MO,eGJR,kCADA,kC6BmNG,MhC9MK,egCmNP,oCACE,MhCpNK,eGJR,0CADA,0C6B4NK,MhCvNG,egCmNE,6CAQL,MhC3NG,egCgOG,4CAED,2CADA,yCAFD,0CAIN,MhCnOK,egCuOT,8BACE,MhCxOO,egCyOP,ahCzOO,egC4OT,mCACE,iBhC+W4C,oPgC5W9C,2BACE,MhCjPO,egCkPP,6BACE,MhCnPK,eGJR,mCADA,mC6B2PK,MhCtPG,egC8PT,2BACE,MhCzQO,KGMR,iCADA,iC6BuQG,MhC5QK,KgCiRP,mCACE,MhClRK,sBGMR,yCADA,yC6BgRK,MhCrRG,KgCiRE,4CAQL,MhCzRG,sBgC8RG,2CAED,0CADA,wCAFD,yCAIN,MhCjSK,KgCqST,6BACE,MhCtSO,sBgCuSP,ahCvSO,qBgC0ST,kCACE,iBhCoT4C,2PgCjT9C,0BACE,MhC/SO,sBgCgTP,4BACE,MhCjTK,KGMR,kCADA,kC6B+SK,MhCpTG,KiCPX,MACE,SAAU,SACV,QAAS,KACT,eAAgB,OAChB,UAAW,EAEX,UAAW,WACX,iBjCAS,KiCCT,gBAAiB,WACjB,OjC+oBkC,EiC/oBP,MjCQlB,iBQhBP,cRgN0B,OiCrM1B,SACA,aAAc,EACd,YAAa,EAIG,2DzBRhB,uBRuM0B,OQtM1B,wBRsM0B,OiCzLV,yDzBAhB,2BRyL0B,OQxL1B,0BRwL0B,OiCnL9B,WAGE,KAAM,EAAA,EAAA,KAGN,WAAY,IACZ,QjCknBkC,QiC9mBpC,YACE,cjC4mBkC,OiCzmBpC,eACE,WAAY,SACZ,cAAe,EAGP,sBACR,cAAe,E9B3Cd,iB8BgDC,gBAAiB,KAGjB,sBACA,YjC2lBgC,QiCnlBpC,aACE,QjCilBkC,OACA,QiCjlBlC,cAAe,EAEf,iBjCvDS,gBiCwDT,cjC+kBkC,EiC/kBA,MjCxDzB,iBiCmDC,yBzBnER,cR0pBgC,iBAAA,iBiC/kB2C,EAAE,EAI7D,sDACd,WAAY,EAKlB,aACE,QjC+jBkC,OACA,QiC/jBlC,iBjCvES,gBiCwET,WjC+jBkC,EiC/jBH,MjCxEtB,iBiCqEC,wBzBrFR,cyB2FuB,EAAE,EjC+jBO,iBAAA,iBiCtjBpC,kBACE,aAAc,SACd,cjC+iBkC,QiC9iBlC,YAAa,SACb,cAAe,EAGjB,mBACE,aAAc,SACd,YAAa,SAIf,kBACE,SAAU,SACV,IAAK,EACL,MAAO,EACP,OAAQ,EACR,KAAM,EACN,QjCuiBkC,QiCpiBpC,UAEA,iBADA,cAEE,YAAa,EACb,MAAO,KAGT,UACA,czBzHI,uBRipBgC,iBQhpBhC,wBRgpBgC,iBiCphBpC,UACA,iBzBhHI,2BRmoBgC,iBQloBhC,0BRkoBgC,iBiC3gBlC,iBACE,cjCghBgC,MazmBR,yBoBuF5B,WAMI,QAAS,KACT,UAAW,IAAA,KACX,ajC0gBgC,OiCzgBhC,YjCygBgC,OiCjhBlC,iBAYI,KAAM,EAAA,EAAA,GACN,ajCogB8B,MiCngB9B,cAAe,EACf,YjCkgB8B,OiCrfhC,kBACA,cjCofgC,MazmBR,yBoBiH5B,YAQI,QAAS,KACT,UAAW,IAAA,KANX,kBAWE,KAAM,EAAA,EAAA,GACN,cAAe,EAEb,wBACA,YAAa,EACb,YAAa,E7BlGF,mCIzEf,wByBiLmC,EzBhLnC,2ByBgLmC,EAG7B,gDADA,iDAGE,wBAAyB,EAG3B,gDADA,oDAGE,2BAA4B,ETzKf,oChBJrB,uByBkLkC,EzBjLlC,0ByBiLkC,EAG5B,iDADA,kDAGE,uBAAwB,EAG1B,iDADA,qDAGE,0BAA2B,GAcrC,oBACE,cjC4agC,Oa9lBR,yBoBgL5B,cAMI,ajCsbgC,EiCrbhC,WjCsbgC,QiCrbhC,QAAS,EACT,OAAQ,EARV,oBAWI,QAAS,aACT,MAAO,MAWT,iBACA,SAAU,OAES,oCACjB,cAAe,EzB5OjB,2ByB6OgC,EzB5OhC,0ByB4OgC,EAGZ,qCzB9PpB,uByB+P6B,EzB9P7B,wByB8P6B,EAG3B,8BzB3QF,cyB4QyB,EACvB,cjC0Y8B,EkC5pBpC,YACE,QAAS,KACT,UAAW,KACX,QlCg1BkC,OACA,KkCh1BlC,clCm1BkC,KkCj1BlC,WAAY,KACZ,iBlCMS,QQRP,cRgN0B,OkCxM1B,kCACA,alCu0BgC,MkCx0BhB,0CAId,QAAS,aACT,clCm0B8B,MkCl0B9B,MlCFK,QkCGL,QlCw0B8B,IkC9zBV,gDACtB,gBAAiB,UADK,gDAKtB,gBAAiB,KAxBL,wBA4BZ,MlCtBO,QmCjBX,YACE,QAAS,K9BGT,aAAc,EACd,WAAY,KGAV,cRgN0B,OmC/M9B,WACE,SAAU,SACV,QAAS,MACT,QnC6mBkC,MACA,OmC7mBlC,YnCwM4B,KmCvM5B,YnCinBkC,KmChnBlC,MnCwBQ,QmCvBR,iBnCFS,KmCGT,OnCoM4B,ImCpMK,MnCAxB,QmCRD,iBAWN,QAAS,EACT,MnCyIwB,QmCxIxB,gBAAiB,KACjB,iBnCPO,QmCQP,anCPO,QmCRD,iBAmBN,QAAS,EACT,QjC4wBgC,EiC3wBhC,WnCmT0B,EAAE,EAAE,EAFJ,MAxSpB,oBmCHN,kCACE,YAAa,E3BCf,uBRkL0B,OQjL1B,0BRiL0B,OmC9K1B,iC3BlBA,wBRgM0B,OQ/L1B,2BR+L0B,OmCzKnB,6BACP,QAAS,EACT,MnCnCO,KmCoCP,iBnCXM,QmCYN,anCZM,QmCeG,+BACT,MnCnCO,QmCoCP,eAAgB,KAEhB,OAAQ,KACR,iBnC7CO,KmC8CP,anC3CO,QoCXT,0BACE,QpCsnBgC,OACA,OC5f9B,UAtCW,QmCnFb,YpCyM0B,IoCpMxB,iD5BwBF,uBRmL0B,MQlL1B,0BRkL0B,MoCtMxB,gD5BKF,wBRiM0B,MQhM1B,2BRgM0B,MoCnN5B,0BACE,QpConBgC,OACA,MC1f9B,UAtCW,QmCnFb,YpC0M0B,IoCrMxB,iD5BwBF,uBRoL0B,MQnL1B,0BRmL0B,MoCvMxB,gD5BKF,wBRkM0B,MQjM1B,2BRiM0B,MqClN9B,OACE,QAAS,aACT,QrC4tBkC,MACA,KC5pBhC,UAAU,IoC/DZ,YrC0O4B,IqCzO5B,YAAa,EACb,WAAY,OACZ,YAAa,OACb,eAAgB,S7BRd,cRgN0B,OmB/MxB,WnBuXwB,MAAM,KAAK,WAAW,CAAE,iBAAiB,KAAK,WAAW,CAAE,aAAa,KAAK,WAAW,CAAE,WAAW,KAAK,YmBlX9F,uCkBN1C,OlBOM,WAAY,MhBKf,cADA,ckCGG,gBAAiB,KAdjB,aAoBF,QAAS,KAKR,YACH,SAAU,SACV,IAAK,KAOP,YACE,crC+rBkC,KqC9rBlC,arC8rBkC,KQluBhC,cRquBgC,MqCxrBlC,eCjDA,MtCUS,KsCTT,iBtCkCQ,QGnBP,sBADA,sBmCVG,MtCKK,KsCJL,iBAAkB,QAHC,sBAAA,sBAQnB,QAAS,EACT,WAAY,EAAE,EAAE,EtC+TQ,MAxSpB,mBqCcR,iBCjDA,MtCUS,KsCTT,iBtCeS,QGAR,wBADA,wBmCVG,MtCKK,KsCJL,iBAAkB,QAHG,wBAAA,wBAQrB,QAAS,EACT,WAAY,EAAE,EAAE,EtC+TQ,MA3TnB,qBqCiCT,eCjDA,MtCUS,KsCTT,iBtCyCQ,QG1BP,sBADA,sBmCVG,MtCKK,KsCJL,iBAAkB,QAHC,sBAAA,sBAQnB,QAAS,EACT,WAAY,EAAE,EAAE,EtC+TQ,MAjSpB,mBqCOR,YCjDA,MtCUS,KsCTT,iBtC2CQ,QG5BP,mBADA,mBmCVG,MtCKK,KsCJL,iBAAkB,QAHF,mBAAA,mBAQhB,QAAS,EACT,WAAY,EAAE,EAAE,EtC+TQ,MA/RpB,oBqCKR,eCjDA,MtC4Fc,QsC3Fd,iBtCwCQ,QGzBP,sBADA,sBmCVG,MtCuFU,QsCtFV,iBAAkB,QAHC,sBAAA,sBAQnB,QAAS,EACT,WAAY,EAAE,EAAE,EtC+TQ,MAlSpB,mBqCQR,cCjDA,MtCUS,KsCTT,iBtCsCQ,QGvBP,qBADA,qBmCVG,MtCKK,KsCJL,iBAAkB,QAHA,qBAAA,qBAQlB,QAAS,EACT,WAAY,EAAE,EAAE,EtC+TQ,MApSpB,mBqCUR,aCjDA,MtC4Fc,QsC3Fd,iBtCUS,QGKR,oBADA,oBmCVG,MtCuFU,QsCtFV,iBAAkB,QAHD,oBAAA,oBAQjB,QAAS,EACT,WAAY,EAAE,EAAE,EtC+TQ,MAhUnB,qBqCsCT,YCjDA,MtCUS,KsCTT,iBtCiBS,QGFR,mBADA,mBmCVG,MtCKK,KsCJL,iBAAkB,QAHF,mBAAA,mBAQhB,QAAS,EACT,WAAY,EAAE,EAAE,EtC+TQ,MAzTnB,kBuCnBX,WACE,QvCmpBkC,KuCnpBN,KAC5B,cvCkpBkC,KuChpBlC,iBvCSS,QQRP,cRiN0B,Ma1JF,yB0B5D5B,WAQI,QAAS,KvC4oBuB,MuCxoBpC,iBACE,cAAe,EACf,aAAc,E/BTZ,c+BUqB,ECXzB,OACE,SAAU,SACV,QxC0wBkC,OACA,QwC1wBlC,cxC2wBkC,KwC1wBlC,OxC0M4B,IwC1MA,MAAM,YhCHhC,cRgN0B,OwCxM9B,eAEE,MAAO,QAIT,YACE,YxC+N4B,IwCvN9B,mBACE,cAAe,KAGf,0BACE,SAAU,SACV,IAAK,EACL,MAAO,EACP,QxC4uBgC,OACA,QwC5uBhC,MAAO,QAUT,eC9CA,MxB8FQ,QIzFN,iBJyFM,QwB5FR,axB4FQ,QwB1FR,kBACE,iBAAkB,QAGpB,2BACE,MAAO,QDqCT,iBC9CA,MxB8FQ,QIzFN,iBJyFM,QwB5FR,axB4FQ,QwB1FR,oBACE,iBAAkB,QAGpB,6BACE,MAAO,QDqCT,eC9CA,MxB8FQ,QIzFN,iBJyFM,QwB5FR,axB4FQ,QwB1FR,kBACE,iBAAkB,QAGpB,2BACE,MAAO,QDqCT,YC9CA,MxB8FQ,QIzFN,iBJyFM,QwB5FR,axB4FQ,QwB1FR,eACE,iBAAkB,QAGpB,wBACE,MAAO,QDqCT,eC9CA,MxB8FQ,QIzFN,iBJyFM,QwB5FR,axB4FQ,QwB1FR,kBACE,iBAAkB,QAGpB,2BACE,MAAO,QDqCT,cC9CA,MxB8FQ,QIzFN,iBJyFM,QwB5FR,axB4FQ,QwB1FR,iBACE,iBAAkB,QAGpB,0BACE,MAAO,QDqCT,aC9CA,MxB8FQ,QIzFN,iBJyFM,QwB5FR,axB4FQ,QwB1FR,gBACE,iBAAkB,QAGpB,yBACE,MAAO,QDqCT,YC9CA,MxB8FQ,QIzFN,iBJyFM,QwB5FR,axB4FQ,QwB1FR,eACE,iBAAkB,QAGpB,wBACE,MAAO,QCRT,gCACE,KAAO,oB1C2xByB,K0C3xBa,EAC7C,GAAK,oBAAqB,EAAA,GAI9B,UACE,QAAS,KACT,O1CoxBkC,K0CnxBlC,SAAU,OzCoHN,UAtCW,OyC5Ef,iB1CAS,QQRP,cRgN0B,OSrMxB,WTkxB8B,MAAM,EAAE,MAAM,MA7wBvC,e0CHX,cACE,QAAS,KACT,eAAgB,OAChB,gBAAiB,OACjB,SAAU,OACV,M1CZS,K0CaT,WAAY,OACZ,YAAa,OACb,iB1CUQ,QmB9BJ,WnBgyB8B,MAAM,IAAI,KmB3xBJ,uCuBO1C,cvBNM,WAAY,MuBkBlB,sBrBaE,iBAAkB,iKqBXlB,gB1C8vBkC,KAAA,K0C1vBlC,uBACE,UAAW,qB1CgwBqB,GAAG,OAAO,S0C7vBF,uCAJ1C,uBAKM,UAAW,MCzCnB,OACE,QAAS,KACT,YAAa,WAGf,YACE,KAAM,ECFR,YACE,QAAS,KACT,eAAgB,OAGhB,aAAc,EACd,cAAe,EASjB,wBACE,MAAO,KACP,M5CHS,Q4CIT,WAAY,QzCLX,8BADA,8ByCUC,QAAS,EACT,M5CTO,Q4CUP,gBAAiB,KACjB,iB5CjBO,Q4COY,+BAcnB,M5CbO,Q4CcP,iB5CrBO,Q4C8BX,iBACE,SAAU,SACV,QAAS,MACT,Q5CiwBkC,OACA,Q4ChwBlC,iB5CrCS,K4CsCT,O5CiK4B,I4CjKK,M5C5BxB,iB4CsBK,6BpC7BZ,uBRuM0B,OQtM1B,wBRsM0B,O4C1Kd,4BpCfZ,2BRyL0B,OQxL1B,0BRwL0B,O4C1Kd,0BAAA,0BAkBZ,M5C5CO,Q4C6CP,eAAgB,KAChB,iB5CpDO,K4CgCK,wBAyBZ,QAAS,EACT,M5C1DO,K4C2DP,iB5ClCM,Q4CmCN,a5CnCM,Q4COV,kCAgCI,iBAAkB,EAhCN,yCAmCV,W5CoIwB,K4CnIxB,iB5CmIwB,I4CrH1B,uBACE,eAAgB,IAEA,oDpCpClB,0BRyJ0B,OQrK1B,wBoCmDuC,EAHrB,mDpChDlB,wBRqK0B,OQzJ1B,0BoC4CyC,EARvB,+CAYZ,WAAY,EAGV,yDACF,iB5CkGoB,I4CjGpB,kBAAmB,EAFD,gEAKhB,Y5C8FkB,K4C7FlB,kB5C6FkB,IatJF,yB+BiCxB,0BACE,eAAgB,IAEA,uDpCpClB,0BRyJ0B,OQrK1B,wBoCmDuC,EAHrB,sDpChDlB,wBRqK0B,OQzJ1B,0BoC4CyC,EARvB,kDAYZ,WAAY,EAGV,4DACF,iB5CkGoB,I4CjGpB,kBAAmB,EAFD,mEAKhB,Y5C8FkB,K4C7FlB,kB5C6FkB,KatJF,yB+BiCxB,0BACE,eAAgB,IAEA,uDpCpClB,0BRyJ0B,OQrK1B,wBoCmDuC,EAHrB,sDpChDlB,wBRqK0B,OQzJ1B,0BoC4CyC,EARvB,kDAYZ,WAAY,EAGV,4DACF,iB5CkGoB,I4CjGpB,kBAAmB,EAFD,mEAKhB,Y5C8FkB,K4C7FlB,kB5C6FkB,KatJF,yB+BiCxB,0BACE,eAAgB,IAEA,uDpCpClB,0BRyJ0B,OQrK1B,wBoCmDuC,EAHrB,sDpChDlB,wBRqK0B,OQzJ1B,0BoC4CyC,EARvB,kDAYZ,WAAY,EAGV,4DACF,iB5CkGoB,I4CjGpB,kBAAmB,EAFD,mEAKhB,Y5C8FkB,K4C7FlB,kB5C6FkB,KatJD,0B+BiCzB,0BACE,eAAgB,IAEA,uDpCpClB,0BRyJ0B,OQrK1B,wBoCmDuC,EAHrB,sDpChDlB,wBRqK0B,OQzJ1B,0BoC4CyC,EARvB,kDAYZ,WAAY,EAGV,4DACF,iB5CkGoB,I4CjGpB,kBAAmB,EAFD,mEAKhB,Y5C8FkB,K4C7FlB,kB5C6FkB,K4C9E5B,mCACE,mBAAoB,EACpB,kBAAmB,EpCjInB,coCkIuB,EAHT,+CAMZ,iBAAkB,EAKJ,yDACd,oBAAqB,EC7IzB,yBACE,M5B2FM,Q4B1FN,iB5B0FM,Qd9EP,sDADA,sD0CPK,M5BsFE,Q4BrFF,iBAAkB,QAHE,uDAOpB,M7CHG,K6CIH,iB5BgFE,Q4B/EF,a5B+EE,Q4B5FR,2BACE,M5B2FM,Q4B1FN,iB5B0FM,Qd9EP,wDADA,wD0CPK,M5BsFE,Q4BrFF,iBAAkB,QAHE,yDAOpB,M7CHG,K6CIH,iB5BgFE,Q4B/EF,a5B+EE,Q4B5FR,yBACE,M5B2FM,Q4B1FN,iB5B0FM,Qd9EP,sDADA,sD0CPK,M5BsFE,Q4BrFF,iBAAkB,QAHE,uDAOpB,M7CHG,K6CIH,iB5BgFE,Q4B/EF,a5B+EE,Q4B5FR,sBACE,M5B2FM,Q4B1FN,iB5B0FM,Qd9EP,mDADA,mD0CPK,M5BsFE,Q4BrFF,iBAAkB,QAHE,oDAOpB,M7CHG,K6CIH,iB5BgFE,Q4B/EF,a5B+EE,Q4B5FR,yBACE,M5B2FM,Q4B1FN,iB5B0FM,Qd9EP,sDADA,sD0CPK,M5BsFE,Q4BrFF,iBAAkB,QAHE,uDAOpB,M7CHG,K6CIH,iB5BgFE,Q4B/EF,a5B+EE,Q4B5FR,wBACE,M5B2FM,Q4B1FN,iB5B0FM,Qd9EP,qDADA,qD0CPK,M5BsFE,Q4BrFF,iBAAkB,QAHE,sDAOpB,M7CHG,K6CIH,iB5BgFE,Q4B/EF,a5B+EE,Q4B5FR,uBACE,M5B2FM,Q4B1FN,iB5B0FM,Qd9EP,oDADA,oD0CPK,M5BsFE,Q4BrFF,iBAAkB,QAHE,qDAOpB,M7CHG,K6CIH,iB5BgFE,Q4B/EF,a5B+EE,Q4B5FR,sBACE,M5B2FM,Q4B1FN,iB5B0FM,Qd9EP,mDADA,mD0CPK,M5BsFE,Q4BrFF,iBAAkB,QAHE,oDAOpB,M7CHG,K6CIH,iB5BgFE,Q4B/EF,a5B+EE,Q6B/FV,OACE,MAAO,M7C8HH,UAtCW,O6CtFf,Y9CgP4B,I8C/O5B,YAAa,EACb,M9CgBS,K8CfT,Y9Co3BkC,EAAE,IAAI,EA/2B/B,K8CJT,QAAS,G3CKR,a2CDC,M9CUO,K8CTP,gBAAiB,K3CKlB,2CADA,2C2CCG,QAAS,IAWT,aACJ,QAAS,EACT,iBAAkB,YAClB,OAAQ,EACR,WAAY,KAMP,iBACL,eAAgB,KCvClB,OACE,U7Cy4BkC,M6Cx4BlC,SAAU,O9C6HN,UAtCW,Q8CpFf,iB/CMS,sB+CLT,gBAAiB,YACjB,O7Cy4BkC,I6Cz4BN,M7C04BM,e6Cz4BlC,W7C24BkC,EAAE,OAAO,OF93BlC,e+CZT,gBAAiB,WACjB,QAAS,EvCLP,cN64BgC,OEpzBjB,wB2ChFf,c7C63BgC,O6C34B9B,eAkBF,QAAS,EAlBP,YAsBF,QAAS,MACT,QAAS,EAvBP,YA2BF,QAAS,KAIb,cACE,QAAS,KACT,YAAa,OACb,Q7C02BkC,OADA,O6Cx2BlC,M/ClBS,Q+CmBT,iB/CzBS,sB+C0BT,gBAAiB,YACjB,c7C02BkC,I6C12BC,M7Ci3BD,gB6C92BpC,YACE,Q7Ci2BkC,O8Cr4BpC,YAEE,SAAU,OAEV,mBACE,WAAY,OACZ,WAAY,KAKhB,OACE,SAAU,MACV,IAAK,EACL,KAAM,EACN,QhDyiBkC,KgDxiBlC,QAAS,KACT,MAAO,KACP,OAAQ,KACR,SAAU,OAGV,QAAS,EAOX,cACE,SAAU,SACV,MAAO,KACP,OhD4sB4B,MgD1sB5B,eAAgB,KALlB,0B7B7BM,WnBmwB8B,UAAU,IAAI,SgD5tB9C,U9Cm6BgC,mBiBr8BM,uC6BwB1C,0B7BvBM,WAAY,M6BuBlB,0BAaI,U9Ci6BgC,K8C96BpC,kCAkBI,U9C85BgC,Y8C15BpC,yBACE,QAAS,KACT,W/ByEiC,kB+BvEjC,wCACE,W/BsE+B,mB+BrE/B,SAAU,OAIZ,uCADA,uCAEE,YAAa,EAGf,qCACE,WAAY,KAIhB,uBACE,QAAS,KACT,YAAa,OACb,W/BqDiC,kB+BxDb,+BAOlB,QAAS,MACT,O/BgD+B,mB+B/C/B,QAAS,GATS,+CAclB,eAAgB,OAChB,gBAAiB,OACjB,OAAQ,KAER,8DACE,WAAY,KANS,uDAUrB,QAAS,KAMf,eACE,SAAU,SACV,QAAS,KACT,eAAgB,OAChB,MAAO,KAGP,eAAgB,KAChB,iBhDtGS,KgDuGT,gBAAiB,YACjB,OhD+F4B,IgD/FQ,MhD9F3B,eQhBP,cRiN0B,MStMxB,WT0uB2B,EAAE,OAAO,MAruB/B,egDkGT,QAAS,EAIX,gBACE,SAAU,MACV,IAAK,EACL,KAAM,EACN,QhD8bkC,KgD7blC,MAAO,MACP,OAAQ,MACR,iBhD7GS,KgDsGI,qBAUJ,QAAS,EAVL,qBAWJ,QhDwnBmB,GgDnnB9B,cACE,QAAS,KACT,YAAa,WACb,gBAAiB,cACjB,QhDonB4B,KgDnnB5B,chDkE4B,IgDlEc,MhDnIjC,QQCP,uBSsH+B,kBTrH/B,wBSqH+B,kB+BejC,qBACE,QhD+mB0B,KgD7mB1B,O9CizBgC,MACA,MADA,M8CjzByD,KAK7F,aACE,cAAe,EACf,YhDyF4B,IgDpF9B,YACE,SAAU,SAGV,KAAM,EAAA,EAAA,KACN,QhDykB4B,KgDrkB9B,cACE,QAAS,KACT,UAAW,KACX,YAAa,OACb,gBAAiB,SACjB,QAAS,OACT,WhDiC4B,IgDjCW,MhDpK9B,QQeP,2BSwG+B,kBTvG/B,0BSuG+B,kB+BoD/B,gBACA,OAAQ,OAKZ,yBACE,SAAU,SACV,IAAK,QACL,MAAO,KACP,OAAQ,KACR,SAAU,OnCvIgB,yBmCzB5B,cAuKI,UhD4jBgC,MgD3jBhC,OhDwiB0B,QgDxiBW,KAlJzC,yBAsJI,W/B3E+B,oB+BvEjC,wCAqJI,W/B9E6B,qB+BxDnC,uBA2II,W/BnF+B,oB+BxDb,+BA8IhB,O/BtF6B,qB+B3BnC,evCzFM,WT2uB2B,EAAE,MAAM,KAtuB9B,egD6MT,UAAY,UhDqiBsB,Oa3sBR,yBmC0K1B,UACA,UACE,UhD6hBgC,OazsBP,0BmCiL3B,UAAY,U9C8tBsB,Q+C18BpC,SACE,SAAU,SACV,QjD6jBkC,KiD5jBlC,QAAS,MACT,OjDkrB4B,EkDtrB5B,YlDuO4B,iBAAiB,CAAE,aAAa,CAAE,kBAAkB,CAAE,UAAU,CAAE,MAAM,CAAE,gBAAgB,CAAE,KAAK,CAAE,UAAU,CAAE,mBAAmB,CAAE,gBAAgB,CAAE,kBkDrOlL,WAAY,OACZ,YlD8O4B,IkD7O5B,YlDiP4B,IkDhP5B,WAAY,KACZ,WAAY,MACZ,gBAAiB,KACjB,YAAa,KACb,eAAgB,KAChB,eAAgB,OAChB,WAAY,OACZ,aAAc,OACd,YAAa,OACb,WAAY,KjDgHR,UAtCW,QgD9Ef,UAAW,WACX,QAAS,EAXH,cAaG,QjDsqBmB,GiDpqB5B,gBACE,SAAU,SACV,QAAS,MACT,MjDsqB0B,MiDrqB1B,OjDsqB0B,MiD1qBtB,wBAOF,SAAU,SACV,QAAS,GACT,aAAc,YACd,aAAc,MAkEhB,mCA7DJ,gBACE,QjD0pB4B,MiD1pBG,EAE/B,0CAAA,uBACE,OAAQ,EADJ,kDAAA,+BAIF,IAAK,EACL,ajDmpBwB,MiDnpBY,MAA2B,EAC/D,iBjDnBK,KiD0EP,qCAlDJ,kBACE,QAAS,EjD4oBmB,MiD1oB5B,4CAAA,yBACE,KAAM,EACN,MjDwoB0B,MiDvoB1B,OjDsoB0B,MiDzoBtB,oDAAA,iCAMF,MAAO,EACP,aAAc,MjDmoBU,MiDnoBuC,MAA2B,EAC1F,mBjDnCK,KiD6EP,sCArCJ,mBACE,QjD4nB4B,MiD5nBG,EAE/B,6CAAA,0BACE,IAAK,EADD,qDAAA,kCAIF,OAAQ,EACR,aAAc,EAAE,MjDqnBQ,MiDpnBxB,oBjDjDK,KiDgFP,oCA1BJ,iBACE,QAAS,EjD8mBmB,MiD5mB5B,2CAAA,wBACE,MAAO,EACP,MjD0mB0B,MiDzmB1B,OjDwmB0B,MiD3mBtB,mDAAA,gCAMF,KAAM,EACN,aAAc,MAA2B,EAAE,MjDqmBnB,MiDpmBxB,kBjDjEK,KiDsFX,eACE,UjDokB4B,MiDnkB5B,QjDwkB4B,OACA,MiDxkB5B,MjDnGS,KiDoGT,WAAY,OACZ,iBjD3FS,KQhBP,cRgN0B,OmDrN9B,SACE,SAAU,SACV,IAAK,EACL,KAAM,EACN,QnD2jBkC,KmD1jBlC,QAAS,MACT,UnDmsBkC,MkDxsBlC,YlDuO4B,iBAAiB,CAAE,aAAa,CAAE,kBAAkB,CAAE,UAAU,CAAE,MAAM,CAAE,gBAAgB,CAAE,KAAK,CAAE,UAAU,CAAE,mBAAmB,CAAE,gBAAgB,CAAE,kBkDrOlL,WAAY,OACZ,YlD8O4B,IkD7O5B,YlDiP4B,IkDhP5B,WAAY,KACZ,WAAY,MACZ,gBAAiB,KACjB,YAAa,KACb,eAAgB,KAChB,eAAgB,OAChB,WAAY,OACZ,aAAc,OACd,YAAa,OACb,WAAY,KjDgHR,UAtCW,QkD7Ef,UAAW,WACX,iBnDFS,KmDGT,gBAAiB,YACjB,OnDmM4B,ImDnME,MnDMrB,eQhBP,cRiN0B,MStMxB,WT6rB8B,EAAE,OAAO,MAxrBlC,emDFT,gBACE,SAAU,SACV,QAAS,MACT,MnDksBgC,KmDjsBhC,OnDksBgC,MmDjsBhC,OAAQ,EnD8LkB,MmDnMtB,uBAAA,wBASF,SAAU,SACV,QAAS,MACT,QAAS,GACT,aAAc,YACd,aAAc,MAwGhB,mCAnGJ,gBACE,cnDmrBkC,MmDjrBhC,0CAAA,uBACA,OlC2F+B,mBkC5FzB,kDAAA,+BAIJ,OAAQ,EACR,anD4qB8B,MmD5qBM,MAA2B,EAC/D,iBnD8qB8B,gBmDprB1B,iDAAA,8BAUJ,OnDgKwB,ImD/JxB,anDsqB8B,MmDtqBM,MAA2B,EAC/D,iBnDzCK,KmDgIP,qCAlFJ,kBACE,YnD+pBkC,MmD7pBhC,4CAAA,yBACA,KlCuE+B,mBkCtE/B,MnD2pBgC,MmD1pBhC,OnDypBgC,KmDxpBhC,OnDsJ0B,MmDtJK,EAJzB,oDAAA,iCAOJ,KAAM,EACN,aAAc,MnDqpBgB,MmDrpBiC,MAA2B,EAC1F,mBnDupB8B,gBmDhqB1B,mDAAA,gCAaJ,KnDyIwB,ImDxIxB,aAAc,MnD+oBgB,MmD/oBiC,MAA2B,EAC1F,mBnDhEK,KmDmIP,sCA9DJ,mBACE,WnDwoBkC,MmDtoBhC,6CAAA,0BACA,IlCgD+B,mBkCjDzB,qDAAA,kCAIJ,IAAK,EACL,aAAc,EAAE,MnDioBc,MmDjoBmC,MACjE,oBnDmoB8B,gBmDzoB1B,oDAAA,iCAUJ,InDqHwB,ImDpHxB,aAAc,EAAE,MnD2nBc,MmD3nBmC,MACjE,oBnDpFK,KmDyFM,8DAAA,2CACb,SAAU,SACV,IAAK,EACL,KAAM,IACN,QAAS,MACT,MnD+mBgC,KmD9mBhC,YAAa,OACb,QAAS,GACT,cnDsG0B,ImDtGW,MnDmmBL,QmD9jBhC,oCAjCJ,iBACE,anDwmBkC,MmDtmBhC,2CAAA,wBACA,MlCgB+B,mBkCf/B,MnDomBgC,MmDnmBhC,OnDkmBgC,KmDjmBhC,OnD+F0B,MmD/FK,EAJzB,mDAAA,gCAOJ,MAAO,EACP,aAAc,MAA2B,EAAE,MnD8lBb,MmD7lB9B,kBnDgmB8B,gBmDzmB1B,kDAAA,+BAaJ,MnDkFwB,ImDjFxB,aAAc,MAA2B,EAAE,MnDwlBb,MmDvlB9B,kBnDvHK,KmD6IX,gBACE,QnDwjBkC,MACA,OmDxjBlC,cAAe,ElD3BX,UAtCW,KkDmEf,MnDuG4B,QmDtG5B,iBnDkjBkC,QmDjjBlC,cnDoD4B,ImDpDS,MAAM,Q3ChJzC,uBSsH+B,kBTrH/B,wBSqH+B,kBkCoBpB,sBAUX,QAAS,KAIb,cACE,QnD0iBkC,MACA,OmD1iBlC,MnDpJS,QoDPX,UACE,SAAU,SAGH,wBACP,aAAc,MAGhB,gBACE,SAAU,SACV,MAAO,KACP,SAAU,OCvBT,uBACC,QAAS,MACT,MAAO,KACP,QAAS,GDwBb,eACE,SAAU,SACV,QAAS,KACT,MAAO,KACP,MAAO,KACP,aAAc,MACd,oBAAqB,OjC5BjB,WnB42B8B,UAAU,IAAI,KmBv2BR,uCiCiB1C,ejChBM,WAAY,MiC2BlB,oBACA,oBAFc,sBAGZ,QAAS,MAIJ,4BADoC,6CAEzC,UAAW,iBAIN,2BADqC,8CAE1C,UAAW,kBASX,8BACE,QAAS,EACT,oBAAqB,QACrB,UAAW,KAIM,sDACA,uDAFL,qCAGZ,QAAS,EACT,QAAS,EAGJ,0CACA,2CACL,QAAS,EACT,QAAS,EjCtEP,WiCuEkB,QAAQ,GlDy/BK,IiB3jCK,uCiC8DjC,0CACA,2CjC9DH,WAAY,MiC2ElB,uBADA,uBAEE,SAAU,SACV,IAAK,EACL,OAAQ,EACR,QAAS,EAET,QAAS,KACT,YAAa,OACb,gBAAiB,OACjB,MpDkwBkC,IoDjwBlC,MpDtFS,KoDuFT,WAAY,OACZ,QpDgwBkC,GmB71B9B,WjB+iC+B,QAAQ,KAAK,KiB1iCR,uCiC4E1C,uBADA,uBjC1EM,WAAY,MhBKf,6BADA,6BACA,6BADA,6BiDwFC,MpD7FO,KoD8FP,gBAAiB,KACjB,QAAS,EACT,QlDy8BiC,GkDt8BrC,uBACE,KAAM,EAKR,uBACE,MAAO,EAQT,4BADA,4BAEE,QAAS,aACT,MpD8uBkC,KoD7uBlC,OpD6uBkC,KoD5uBlC,WAAY,UAAA,GAAA,CAAA,KAAA,KAEd,4BACE,iBnCxFU,kMmC0FZ,4BACE,iBnC3FU,kMmCoGZ,qBACE,SAAU,SACV,MAAO,EACP,OAAQ,EACR,KAAM,EACN,QAAS,GACT,QAAS,KACT,gBAAiB,OACjB,aAAc,EAEd,apDwsBkC,IoDvsBlC,YpDusBkC,IoDtsBlC,WAAY,KAEZ,wBACE,WAAY,YACZ,KAAM,EAAA,EAAA,KACN,MpDosBgC,KoDnsBhC,OpDosBgC,IoDnsBhC,apDosBgC,IoDnsBhC,YpDmsBgC,IoDlsBhC,YAAa,OACb,OAAQ,QACR,iBpD5JO,KoD6JP,gBAAiB,YAEjB,WlD+4BiC,KkD/4Be,MAAM,YACtD,clD84BiC,KkD94BkB,MAAM,YACzD,QAAS,GjCtKP,WjBsjC+B,QAAQ,IAAI,KiBjjCP,uCiCmJxC,wBjClJI,WAAY,MiCoKhB,6BACE,QAAS,EASb,kBACE,SAAU,SACV,MAAO,IACP,OAAQ,KACR,KAAM,IACN,QAAS,GACT,YAAa,KACb,eAAgB,KAChB,MpDvLS,KoDwLT,WAAY,OE/Ld,0BACE,GAAK,UAAW,gBAGlB,gBACE,QAAS,aACT,MpDkkCsB,KoDjkCtB,OpDikCsB,KoDhkCtB,eAAgB,YAChB,OpDikCsB,MoDjkCQ,MAAM,aACpC,mBAAoB,YAEpB,cAAe,IACf,UAAW,eAAA,KAAA,OAAA,SAGb,mBACE,MpD2jCwB,KoD1jCxB,OpD0jCwB,KoDzjCxB,apD2jCwB,KoDpjC1B,wBACE,GACE,UAAW,SAEb,IACE,QAAS,GAIb,cACE,QAAS,aACT,MpDmiCsB,KoDliCtB,OpDkiCsB,KoDjiCtB,eAAgB,YAChB,iBAAkB,aAElB,cAAe,IACf,QAAS,EACT,UAAW,aAAA,KAAA,OAAA,SAGb,iBACE,MpD4hCwB,KoD3hCxB,OpD2hCwB,KqD9kC1B,gBAAqB,eAAgB,mBACrC,WAAqB,eAAgB,cACrC,cAAqB,eAAgB,iBACrC,cAAqB,eAAgB,iBACrC,mBAAqB,eAAgB,sBACrC,gBAAqB,eAAgB,mBCFnC,YACE,iBxD8BM,kBGnBP,mBADA,mBACA,wBADA,wBqDLG,iBAAkB,kBANtB,cACE,iBxDWO,kBGAR,qBADA,qBACA,0BADA,0BqDLG,iBAAkB,kBANtB,YACE,iBxDqCM,kBG1BP,mBADA,mBACA,wBADA,wBqDLG,iBAAkB,kBANtB,SACE,iBxDuCM,kBG5BP,gBADA,gBACA,qBADA,qBqDLG,iBAAkB,kBANtB,YACE,iBxDoCM,kBGzBP,mBADA,mBACA,wBADA,wBqDLG,iBAAkB,kBANtB,WACE,iBxDkCM,kBGvBP,kBADA,kBACA,uBADA,uBqDLG,iBAAkB,kBANtB,UACE,iBxDMO,kBGKR,iBADA,iBACA,sBADA,sBqDLG,iBAAkB,kBANtB,SACE,iBxDaO,kBGFR,gBADA,gBACA,qBADA,qBqDLG,iBAAkB,kBCCxB,UACE,iBzDFS,eyDKX,gBACE,iBAAkB,sBCXpB,QAAkB,O1D4MY,I0D5MU,M1DQ7B,kB0DPX,YAAkB,W1D2MY,I0D3Mc,M1DOjC,kB0DNX,cAAkB,a1D0MY,I0D1MgB,M1DMnC,kB0DLX,eAAkB,c1DyMY,I0DzMiB,M1DKpC,kB0DJX,aAAkB,Y1DwMY,I0DxMe,M1DIlC,kB0DFX,UAAmB,OAAQ,YAC3B,cAAmB,WAAY,YAC/B,gBAAmB,aAAc,YACjC,iBAAmB,cAAe,YAClC,eAAmB,YAAa,YAG9B,gBACE,a1DgBM,kB0DjBR,kBACE,a1DHO,kB0DET,gBACE,a1DuBM,kB0DxBR,aACE,a1DyBM,kB0D1BR,gBACE,a1DsBM,kB0DvBR,eACE,a1DoBM,kB0DrBR,cACE,a1DRO,kB0DOT,aACE,a1DDO,kB0DKX,cACE,a1DdS,e0DqBX,YACE,c1DsL4B,gB0DnL9B,SACE,c1DgL4B,iB0D7K9B,aACE,uB1D4K4B,iB0D3K5B,wB1D2K4B,iB0DxK9B,eACE,wB1DuK4B,iB0DtK5B,2B1DsK4B,iB0DnK9B,gBACE,2B1DkK4B,iB0DjK5B,0B1DiK4B,iB0D9J9B,cACE,uB1D6J4B,iB0D5J5B,0B1D4J4B,iB0DzJ9B,YACE,c1DyJ4B,gB0DtJ9B,gBACE,cAAe,cAGjB,cACE,cxDiL4B,gBwD9K9B,WACE,cAAe,YLxEd,iBACC,QAAS,MACT,MAAO,KACP,QAAS,GMOP,QAAwB,QzDmmCnB,eyDnmCL,UAAwB,QzDmmCb,iByDnmCX,gBAAwB,QzDmmCL,uByDnmCnB,SAAwB,QzDmmCS,gByDnmCjC,SAAwB,QzDmmCgB,gByDnmCxC,aAAwB,QzDmmCuB,oByDnmC/C,cAAwB,QzDmmCkC,qByDnmC1D,QAAwB,QzDmmC8C,eyDnmCtE,eAAwB,QzDmmCoD,sBWljCtD,yB8CjDtB,WAAwB,QzDmmCnB,eyDnmCL,aAAwB,QzDmmCb,iByDnmCX,mBAAwB,QzDmmCL,uByDnmCnB,YAAwB,QzDmmCS,gByDnmCjC,YAAwB,QzDmmCgB,gByDnmCxC,gBAAwB,QzDmmCuB,oByDnmC/C,iBAAwB,QzDmmCkC,qByDnmC1D,WAAwB,QzDmmC8C,eyDnmCtE,kBAAwB,QzDmmCoD,uBWljCtD,yB8CjDtB,WAAwB,QzDmmCnB,eyDnmCL,aAAwB,QzDmmCb,iByDnmCX,mBAAwB,QzDmmCL,uByDnmCnB,YAAwB,QzDmmCS,gByDnmCjC,YAAwB,QzDmmCgB,gByDnmCxC,gBAAwB,QzDmmCuB,oByDnmC/C,iBAAwB,QzDmmCkC,qByDnmC1D,WAAwB,QzDmmC8C,eyDnmCtE,kBAAwB,QzDmmCoD,uBWljCtD,yB8CjDtB,WAAwB,QzDmmCnB,eyDnmCL,aAAwB,QzDmmCb,iByDnmCX,mBAAwB,QzDmmCL,uByDnmCnB,YAAwB,QzDmmCS,gByDnmCjC,YAAwB,QzDmmCgB,gByDnmCxC,gBAAwB,QzDmmCuB,oByDnmC/C,iBAAwB,QzDmmCkC,qByDnmC1D,WAAwB,QzDmmC8C,eyDnmCtE,kBAAwB,QzDmmCoD,uBWljCrD,0B8CjDvB,WAAwB,QzDmmCnB,eyDnmCL,aAAwB,QzDmmCb,iByDnmCX,mBAAwB,QzDmmCL,uByDnmCnB,YAAwB,QzDmmCS,gByDnmCjC,YAAwB,QzDmmCgB,gByDnmCxC,gBAAwB,QzDmmCuB,oByDnmC/C,iBAAwB,QzDmmCkC,qByDnmC1D,WAAwB,QzDmmC8C,eyDnmCtE,kBAAwB,QzDmmCoD,uByDzlC3E,aAEH,cAAqB,QzDulCd,eyDvlCP,gBAAqB,QzDulCR,iByDvlCb,sBAAqB,QzDulCA,uByDvlCrB,eAAqB,QzDulCc,gByDvlCnC,eAAqB,QzDulCqB,gByDvlC1C,mBAAqB,QzDulC4B,oByDvlCjD,oBAAqB,QzDulCuC,qByDvlC5D,cAAqB,QzDulCmD,eyDvlCxE,qBAAqB,QzDulCyD,uB0D5mClF,kBACE,SAAU,SACV,QAAS,MACT,MAAO,KACP,QAAS,EACT,SAAU,OALK,0BAQb,QAAS,MACT,QAAS,GAGX,yCAEA,wBADA,yBAEA,yBACA,wBACE,SAAU,SACV,IAAK,EACL,OAAQ,EACR,KAAM,EACN,MAAO,KACP,OAAQ,KACR,OAAQ,EAQa,gCAEnB,YAAa,WAFM,gCAEnB,YAAa,OAFK,+BAElB,YAAa,IAFK,+BAElB,YAAa,KCzBf,UAAgC,eAAgB,cAChD,aAAgC,eAAgB,iBAChD,kBAAgC,eAAgB,sBAChD,qBAAgC,eAAgB,yBAEhD,WAA8B,UAAW,eACzC,aAA8B,UAAW,iBACzC,mBAA8B,UAAW,uBACzC,WAA8B,KAAM,EAAA,EAAA,eACpC,aAA8B,UAAW,YACzC,aAA8B,UAAW,YACzC,eAA8B,YAAa,YAC3C,eAA8B,YAAa,YAE3C,uBAAoC,gBAAiB,qBACrD,qBAAoC,gBAAiB,mBACrD,wBAAoC,gBAAiB,iBACrD,yBAAoC,gBAAiB,wBACrD,wBAAoC,gBAAiB,uBAErD,mBAAiC,YAAa,qBAC9C,iBAAiC,YAAa,mBAC9C,oBAAiC,YAAa,iBAC9C,sBAAiC,YAAa,mBAC9C,qBAAiC,YAAa,kBAE9C,qBAAkC,cAAe,qBACjD,mBAAkC,cAAe,mBACjD,sBAAkC,cAAe,iBACjD,uBAAkC,cAAe,wBACjD,sBAAkC,cAAe,uBACjD,uBAAkC,cAAe,kBAEjD,iBAAgC,WAAY,eAC5C,kBAAgC,WAAY,qBAC5C,gBAAgC,WAAY,mBAC5C,mBAAgC,WAAY,iBAC5C,qBAAgC,WAAY,mBAC5C,oBAAgC,WAAY,kBhDYpB,yBgDlDxB,aAAgC,eAAgB,cAChD,gBAAgC,eAAgB,iBAChD,qBAAgC,eAAgB,sBAChD,wBAAgC,eAAgB,yBAEhD,cAA8B,UAAW,eACzC,gBAA8B,UAAW,iBACzC,sBAA8B,UAAW,uBACzC,cAA8B,KAAM,EAAA,EAAA,eACpC,gBAA8B,UAAW,YACzC,gBAA8B,UAAW,YACzC,kBAA8B,YAAa,YAC3C,kBAA8B,YAAa,YAE3C,0BAAoC,gBAAiB,qBACrD,wBAAoC,gBAAiB,mBACrD,2BAAoC,gBAAiB,iBACrD,4BAAoC,gBAAiB,wBACrD,2BAAoC,gBAAiB,uBAErD,sBAAiC,YAAa,qBAC9C,oBAAiC,YAAa,mBAC9C,uBAAiC,YAAa,iBAC9C,yBAAiC,YAAa,mBAC9C,wBAAiC,YAAa,kBAE9C,wBAAkC,cAAe,qBACjD,sBAAkC,cAAe,mBACjD,yBAAkC,cAAe,iBACjD,0BAAkC,cAAe,wBACjD,yBAAkC,cAAe,uBACjD,0BAAkC,cAAe,kBAEjD,oBAAgC,WAAY,eAC5C,qBAAgC,WAAY,qBAC5C,mBAAgC,WAAY,mBAC5C,sBAAgC,WAAY,iBAC5C,wBAAgC,WAAY,mBAC5C,uBAAgC,WAAY,mBhDYpB,yBgDlDxB,aAAgC,eAAgB,cAChD,gBAAgC,eAAgB,iBAChD,qBAAgC,eAAgB,sBAChD,wBAAgC,eAAgB,yBAEhD,cAA8B,UAAW,eACzC,gBAA8B,UAAW,iBACzC,sBAA8B,UAAW,uBACzC,cAA8B,KAAM,EAAA,EAAA,eACpC,gBAA8B,UAAW,YACzC,gBAA8B,UAAW,YACzC,kBAA8B,YAAa,YAC3C,kBAA8B,YAAa,YAE3C,0BAAoC,gBAAiB,qBACrD,wBAAoC,gBAAiB,mBACrD,2BAAoC,gBAAiB,iBACrD,4BAAoC,gBAAiB,wBACrD,2BAAoC,gBAAiB,uBAErD,sBAAiC,YAAa,qBAC9C,oBAAiC,YAAa,mBAC9C,uBAAiC,YAAa,iBAC9C,yBAAiC,YAAa,mBAC9C,wBAAiC,YAAa,kBAE9C,wBAAkC,cAAe,qBACjD,sBAAkC,cAAe,mBACjD,yBAAkC,cAAe,iBACjD,0BAAkC,cAAe,wBACjD,yBAAkC,cAAe,uBACjD,0BAAkC,cAAe,kBAEjD,oBAAgC,WAAY,eAC5C,qBAAgC,WAAY,qBAC5C,mBAAgC,WAAY,mBAC5C,sBAAgC,WAAY,iBAC5C,wBAAgC,WAAY,mBAC5C,uBAAgC,WAAY,mBhDYpB,yBgDlDxB,aAAgC,eAAgB,cAChD,gBAAgC,eAAgB,iBAChD,qBAAgC,eAAgB,sBAChD,wBAAgC,eAAgB,yBAEhD,cAA8B,UAAW,eACzC,gBAA8B,UAAW,iBACzC,sBAA8B,UAAW,uBACzC,cAA8B,KAAM,EAAA,EAAA,eACpC,gBAA8B,UAAW,YACzC,gBAA8B,UAAW,YACzC,kBAA8B,YAAa,YAC3C,kBAA8B,YAAa,YAE3C,0BAAoC,gBAAiB,qBACrD,wBAAoC,gBAAiB,mBACrD,2BAAoC,gBAAiB,iBACrD,4BAAoC,gBAAiB,wBACrD,2BAAoC,gBAAiB,uBAErD,sBAAiC,YAAa,qBAC9C,oBAAiC,YAAa,mBAC9C,uBAAiC,YAAa,iBAC9C,yBAAiC,YAAa,mBAC9C,wBAAiC,YAAa,kBAE9C,wBAAkC,cAAe,qBACjD,sBAAkC,cAAe,mBACjD,yBAAkC,cAAe,iBACjD,0BAAkC,cAAe,wBACjD,yBAAkC,cAAe,uBACjD,0BAAkC,cAAe,kBAEjD,oBAAgC,WAAY,eAC5C,qBAAgC,WAAY,qBAC5C,mBAAgC,WAAY,mBAC5C,sBAAgC,WAAY,iBAC5C,wBAAgC,WAAY,mBAC5C,uBAAgC,WAAY,mBhDYnB,0BgDlDzB,aAAgC,eAAgB,cAChD,gBAAgC,eAAgB,iBAChD,qBAAgC,eAAgB,sBAChD,wBAAgC,eAAgB,yBAEhD,cAA8B,UAAW,eACzC,gBAA8B,UAAW,iBACzC,sBAA8B,UAAW,uBACzC,cAA8B,KAAM,EAAA,EAAA,eACpC,gBAA8B,UAAW,YACzC,gBAA8B,UAAW,YACzC,kBAA8B,YAAa,YAC3C,kBAA8B,YAAa,YAE3C,0BAAoC,gBAAiB,qBACrD,wBAAoC,gBAAiB,mBACrD,2BAAoC,gBAAiB,iBACrD,4BAAoC,gBAAiB,wBACrD,2BAAoC,gBAAiB,uBAErD,sBAAiC,YAAa,qBAC9C,oBAAiC,YAAa,mBAC9C,uBAAiC,YAAa,iBAC9C,yBAAiC,YAAa,mBAC9C,wBAAiC,YAAa,kBAE9C,wBAAkC,cAAe,qBACjD,sBAAkC,cAAe,mBACjD,yBAAkC,cAAe,iBACjD,0BAAkC,cAAe,wBACjD,yBAAkC,cAAe,uBACjD,0BAAkC,cAAe,kBAEjD,oBAAgC,WAAY,eAC5C,qBAAgC,WAAY,qBAC5C,mBAAgC,WAAY,mBAC5C,sBAAgC,WAAY,iBAC5C,wBAAgC,WAAY,mBAC5C,uBAAgC,WAAY,mBC1C5C,YAAwB,MAAO,eAC/B,aAAwB,MAAO,gBAC/B,YAAwB,MAAO,ejDoDP,yBiDtDxB,eAAwB,MAAO,eAC/B,gBAAwB,MAAO,gBAC/B,eAAwB,MAAO,gBjDoDP,yBiDtDxB,eAAwB,MAAO,eAC/B,gBAAwB,MAAO,gBAC/B,eAAwB,MAAO,gBjDoDP,yBiDtDxB,eAAwB,MAAO,eAC/B,gBAAwB,MAAO,gBAC/B,eAAwB,MAAO,gBjDoDN,0BiDtDzB,eAAwB,MAAO,eAC/B,gBAAwB,MAAO,gBAC/B,eAAwB,MAAO,gBCLjC,eAAsB,S7D4mCZ,e6D5mCV,iBAAsB,S7D4mCN,iB8D3mChB,iBAAyB,S9D4mCf,iB8D5mCV,mBAAyB,S9D4mCP,mB8D5mClB,mBAAyB,S9D4mCG,mB8D5mC5B,gBAAyB,S9D4mCa,gB8D5mCtC,iBAAyB,S9D4mCoB,iB8DvmC/C,WACE,SAAU,MACV,IAAK,EACL,MAAO,EACP,KAAM,EACN,QhE8iBkC,KgE3iBpC,cACE,SAAU,MACV,MAAO,EACP,OAAQ,EACR,KAAM,EACN,QhEsiBkC,KgEliBP,4BAD7B,YAEI,SAAU,OACV,IAAK,EACL,QhE8hBgC,MiEvjBpC,SCEE,SAAU,SACV,MAAO,IACP,OAAQ,IACR,QAAS,EACT,OAAQ,KACR,SAAU,OACV,KAAM,cACN,YAAa,OACb,OAAQ,EAUP,0BACA,yBACC,SAAU,OACV,MAAO,KACP,OAAQ,KACR,SAAU,QACV,KAAM,KACN,YAAa,OC7BjB,WAAa,WjEsPiB,EAAE,QAAQ,OFnO7B,2BmElBX,QAAU,WjEsPoB,EAAE,MAAM,KFpO3B,0BmEjBX,WAAa,WjEsPiB,EAAE,KAAK,KFrO1B,2BmEhBX,aAAe,WAAY,eCCvB,MAAuB,MpEiIf,coEjIR,MAAuB,MpEkIf,coElIR,MAAuB,MpEmIf,coEnIR,OAAuB,MpEoId,eoEpIT,QAAuB,MlE0JjB,ekE1JN,MAAuB,OpEiIf,coEjIR,MAAuB,OpEkIf,coElIR,MAAuB,OpEmIf,coEnIR,OAAuB,OpEoId,eoEpIT,QAAuB,OlE0JjB,ekEtJV,QAAU,UAAW,eACrB,QAAU,WAAY,eAItB,YAAc,UAAW,gBACzB,YAAc,WAAY,gBAE1B,QAAU,MAAO,gBACjB,QAAU,OAAQ,gBCfH,uBAEX,SAAU,SACV,IAAK,EACL,MAAO,EACP,OAAQ,EACR,KAAM,EACN,QAAS,EAET,eAAgB,KAChB,QAAS,GAET,iBAAkB,cCNd,KAAgC,OtEkH7B,YsEjHH,MACA,MACE,WtE+GC,YsE7GH,MACA,MACE,atE2GC,YsEzGH,MACA,MACE,ctEuGC,YsErGH,MACA,MACE,YtEmGC,YsElHH,KAAgC,OtEmH7B,iBsElHH,MACA,MACE,WtEgHC,iBsE9GH,MACA,MACE,atE4GC,iBsE1GH,MACA,MACE,ctEwGC,iBsEtGH,MACA,MACE,YtEoGC,iBsEnHH,KAAgC,OtEoH7B,gBsEnHH,MACA,MACE,WtEiHC,gBsE/GH,MACA,MACE,atE6GC,gBsE3GH,MACA,MACE,ctEyGC,gBsEvGH,MACA,MACE,YtEqGC,gBsEpHH,KAAgC,OtE+G/B,esE9GD,MACA,MACE,WtE4GD,esE1GD,MACA,MACE,atEwGD,esEtGD,MACA,MACE,ctEoGD,esElGD,MACA,MACE,YtEgGD,esE/GD,KAAgC,OtEsH7B,iBsErHH,MACA,MACE,WtEmHC,iBsEjHH,MACA,MACE,atE+GC,iBsE7GH,MACA,MACE,ctE2GC,iBsEzGH,MACA,MACE,YtEuGC,iBsEtHH,KAAgC,OtEuH7B,esEtHH,MACA,MACE,WtEoHC,esElHH,MACA,MACE,atEgHC,esE9GH,MACA,MACE,ctE4GC,esE1GH,MACA,MACE,YtEwGC,esEvHH,KAAgC,QtEkH7B,YsEjHH,MACA,MACE,YtE+GC,YsE7GH,MACA,MACE,ctE2GC,YsEzGH,MACA,MACE,etEuGC,YsErGH,MACA,MACE,atEmGC,YsElHH,KAAgC,QtEmH7B,iBsElHH,MACA,MACE,YtEgHC,iBsE9GH,MACA,MACE,ctE4GC,iBsE1GH,MACA,MACE,etEwGC,iBsEtGH,MACA,MACE,atEoGC,iBsEnHH,KAAgC,QtEoH7B,gBsEnHH,MACA,MACE,YtEiHC,gBsE/GH,MACA,MACE,ctE6GC,gBsE3GH,MACA,MACE,etEyGC,gBsEvGH,MACA,MACE,atEqGC,gBsEpHH,KAAgC,QtE+G/B,esE9GD,MACA,MACE,YtE4GD,esE1GD,MACA,MACE,ctEwGD,esEtGD,MACA,MACE,etEoGD,esElGD,MACA,MACE,atEgGD,esE/GD,KAAgC,QtEsH7B,iBsErHH,MACA,MACE,YtEmHC,iBsEjHH,MACA,MACE,ctE+GC,iBsE7GH,MACA,MACE,etE2GC,iBsEzGH,MACA,MACE,atEuGC,iBsEtHH,KAAgC,QtEuH7B,esEtHH,MACA,MACE,YtEoHC,esElHH,MACA,MACE,ctEgHC,esE9GH,MACA,MACE,etE4GC,esE1GH,MACA,MACE,atEwGC,esEhGH,MAAwB,OtE4FrB,kBsE3FH,OACA,OACE,WtEyFC,kBsEvFH,OACA,OACE,atEqFC,kBsEnFH,OACA,OACE,ctEiFC,kBsE/EH,OACA,OACE,YtE6EC,kBsE5FH,MAAwB,OtE6FrB,iBsE5FH,OACA,OACE,WtE0FC,iBsExFH,OACA,OACE,atEsFC,iBsEpFH,OACA,OACE,ctEkFC,iBsEhFH,OACA,OACE,YtE8EC,iBsE7FH,MAAwB,OtEwFvB,gBsEvFD,OACA,OACE,WtEqFD,gBsEnFD,OACA,OACE,atEiFD,gBsE/ED,OACA,OACE,ctE6ED,gBsE3ED,OACA,OACE,YtEyED,gBsExFD,MAAwB,OtE+FrB,kBsE9FH,OACA,OACE,WtE4FC,kBsE1FH,OACA,OACE,atEwFC,kBsEtFH,OACA,OACE,ctEoFC,kBsElFH,OACA,OACE,YtEgFC,kBsE/FH,MAAwB,OtEgGrB,gBsE/FH,OACA,OACE,WtE6FC,gBsE3FH,OACA,OACE,atEyFC,gBsEvFH,OACA,OACE,ctEqFC,gBsEnFH,OACA,OACE,YtEiFC,gBsE3EP,QAAmB,OAAQ,eAC3B,SACA,SACE,WAAY,eAEd,SACA,SACE,aAAc,eAEhB,SACA,SACE,cAAe,eAEjB,SACA,SACE,YAAa,ezDTS,yByDlDpB,QAAgC,OtEkH7B,YsEjHH,SACA,SACE,WtE+GC,YsE7GH,SACA,SACE,atE2GC,YsEzGH,SACA,SACE,ctEuGC,YsErGH,SACA,SACE,YtEmGC,YsElHH,QAAgC,OtEmH7B,iBsElHH,SACA,SACE,WtEgHC,iBsE9GH,SACA,SACE,atE4GC,iBsE1GH,SACA,SACE,ctEwGC,iBsEtGH,SACA,SACE,YtEoGC,iBsEnHH,QAAgC,OtEoH7B,gBsEnHH,SACA,SACE,WtEiHC,gBsE/GH,SACA,SACE,atE6GC,gBsE3GH,SACA,SACE,ctEyGC,gBsEvGH,SACA,SACE,YtEqGC,gBsEpHH,QAAgC,OtE+G/B,esE9GD,SACA,SACE,WtE4GD,esE1GD,SACA,SACE,atEwGD,esEtGD,SACA,SACE,ctEoGD,esElGD,SACA,SACE,YtEgGD,esE/GD,QAAgC,OtEsH7B,iBsErHH,SACA,SACE,WtEmHC,iBsEjHH,SACA,SACE,atE+GC,iBsE7GH,SACA,SACE,ctE2GC,iBsEzGH,SACA,SACE,YtEuGC,iBsEtHH,QAAgC,OtEuH7B,esEtHH,SACA,SACE,WtEoHC,esElHH,SACA,SACE,atEgHC,esE9GH,SACA,SACE,ctE4GC,esE1GH,SACA,SACE,YtEwGC,esEvHH,QAAgC,QtEkH7B,YsEjHH,SACA,SACE,YtE+GC,YsE7GH,SACA,SACE,ctE2GC,YsEzGH,SACA,SACE,etEuGC,YsErGH,SACA,SACE,atEmGC,YsElHH,QAAgC,QtEmH7B,iBsElHH,SACA,SACE,YtEgHC,iBsE9GH,SACA,SACE,ctE4GC,iBsE1GH,SACA,SACE,etEwGC,iBsEtGH,SACA,SACE,atEoGC,iBsEnHH,QAAgC,QtEoH7B,gBsEnHH,SACA,SACE,YtEiHC,gBsE/GH,SACA,SACE,ctE6GC,gBsE3GH,SACA,SACE,etEyGC,gBsEvGH,SACA,SACE,atEqGC,gBsEpHH,QAAgC,QtE+G/B,esE9GD,SACA,SACE,YtE4GD,esE1GD,SACA,SACE,ctEwGD,esEtGD,SACA,SACE,etEoGD,esElGD,SACA,SACE,atEgGD,esE/GD,QAAgC,QtEsH7B,iBsErHH,SACA,SACE,YtEmHC,iBsEjHH,SACA,SACE,ctE+GC,iBsE7GH,SACA,SACE,etE2GC,iBsEzGH,SACA,SACE,atEuGC,iBsEtHH,QAAgC,QtEuH7B,esEtHH,SACA,SACE,YtEoHC,esElHH,SACA,SACE,ctEgHC,esE9GH,SACA,SACE,etE4GC,esE1GH,SACA,SACE,atEwGC,esEhGH,SAAwB,OtE4FrB,kBsE3FH,UACA,UACE,WtEyFC,kBsEvFH,UACA,UACE,atEqFC,kBsEnFH,UACA,UACE,ctEiFC,kBsE/EH,UACA,UACE,YtE6EC,kBsE5FH,SAAwB,OtE6FrB,iBsE5FH,UACA,UACE,WtE0FC,iBsExFH,UACA,UACE,atEsFC,iBsEpFH,UACA,UACE,ctEkFC,iBsEhFH,UACA,UACE,YtE8EC,iBsE7FH,SAAwB,OtEwFvB,gBsEvFD,UACA,UACE,WtEqFD,gBsEnFD,UACA,UACE,atEiFD,gBsE/ED,UACA,UACE,ctE6ED,gBsE3ED,UACA,UACE,YtEyED,gBsExFD,SAAwB,OtE+FrB,kBsE9FH,UACA,UACE,WtE4FC,kBsE1FH,UACA,UACE,atEwFC,kBsEtFH,UACA,UACE,ctEoFC,kBsElFH,UACA,UACE,YtEgFC,kBsE/FH,SAAwB,OtEgGrB,gBsE/FH,UACA,UACE,WtE6FC,gBsE3FH,UACA,UACE,atEyFC,gBsEvFH,UACA,UACE,ctEqFC,gBsEnFH,UACA,UACE,YtEiFC,gBsE3EP,WAAmB,OAAQ,eAC3B,YACA,YACE,WAAY,eAEd,YACA,YACE,aAAc,eAEhB,YACA,YACE,cAAe,eAEjB,YACA,YACE,YAAa,gBzDTS,yByDlDpB,QAAgC,OtEkH7B,YsEjHH,SACA,SACE,WtE+GC,YsE7GH,SACA,SACE,atE2GC,YsEzGH,SACA,SACE,ctEuGC,YsErGH,SACA,SACE,YtEmGC,YsElHH,QAAgC,OtEmH7B,iBsElHH,SACA,SACE,WtEgHC,iBsE9GH,SACA,SACE,atE4GC,iBsE1GH,SACA,SACE,ctEwGC,iBsEtGH,SACA,SACE,YtEoGC,iBsEnHH,QAAgC,OtEoH7B,gBsEnHH,SACA,SACE,WtEiHC,gBsE/GH,SACA,SACE,atE6GC,gBsE3GH,SACA,SACE,ctEyGC,gBsEvGH,SACA,SACE,YtEqGC,gBsEpHH,QAAgC,OtE+G/B,esE9GD,SACA,SACE,WtE4GD,esE1GD,SACA,SACE,atEwGD,esEtGD,SACA,SACE,ctEoGD,esElGD,SACA,SACE,YtEgGD,esE/GD,QAAgC,OtEsH7B,iBsErHH,SACA,SACE,WtEmHC,iBsEjHH,SACA,SACE,atE+GC,iBsE7GH,SACA,SACE,ctE2GC,iBsEzGH,SACA,SACE,YtEuGC,iBsEtHH,QAAgC,OtEuH7B,esEtHH,SACA,SACE,WtEoHC,esElHH,SACA,SACE,atEgHC,esE9GH,SACA,SACE,ctE4GC,esE1GH,SACA,SACE,YtEwGC,esEvHH,QAAgC,QtEkH7B,YsEjHH,SACA,SACE,YtE+GC,YsE7GH,SACA,SACE,ctE2GC,YsEzGH,SACA,SACE,etEuGC,YsErGH,SACA,SACE,atEmGC,YsElHH,QAAgC,QtEmH7B,iBsElHH,SACA,SACE,YtEgHC,iBsE9GH,SACA,SACE,ctE4GC,iBsE1GH,SACA,SACE,etEwGC,iBsEtGH,SACA,SACE,atEoGC,iBsEnHH,QAAgC,QtEoH7B,gBsEnHH,SACA,SACE,YtEiHC,gBsE/GH,SACA,SACE,ctE6GC,gBsE3GH,SACA,SACE,etEyGC,gBsEvGH,SACA,SACE,atEqGC,gBsEpHH,QAAgC,QtE+G/B,esE9GD,SACA,SACE,YtE4GD,esE1GD,SACA,SACE,ctEwGD,esEtGD,SACA,SACE,etEoGD,esElGD,SACA,SACE,atEgGD,esE/GD,QAAgC,QtEsH7B,iBsErHH,SACA,SACE,YtEmHC,iBsEjHH,SACA,SACE,ctE+GC,iBsE7GH,SACA,SACE,etE2GC,iBsEzGH,SACA,SACE,atEuGC,iBsEtHH,QAAgC,QtEuH7B,esEtHH,SACA,SACE,YtEoHC,esElHH,SACA,SACE,ctEgHC,esE9GH,SACA,SACE,etE4GC,esE1GH,SACA,SACE,atEwGC,esEhGH,SAAwB,OtE4FrB,kBsE3FH,UACA,UACE,WtEyFC,kBsEvFH,UACA,UACE,atEqFC,kBsEnFH,UACA,UACE,ctEiFC,kBsE/EH,UACA,UACE,YtE6EC,kBsE5FH,SAAwB,OtE6FrB,iBsE5FH,UACA,UACE,WtE0FC,iBsExFH,UACA,UACE,atEsFC,iBsEpFH,UACA,UACE,ctEkFC,iBsEhFH,UACA,UACE,YtE8EC,iBsE7FH,SAAwB,OtEwFvB,gBsEvFD,UACA,UACE,WtEqFD,gBsEnFD,UACA,UACE,atEiFD,gBsE/ED,UACA,UACE,ctE6ED,gBsE3ED,UACA,UACE,YtEyED,gBsExFD,SAAwB,OtE+FrB,kBsE9FH,UACA,UACE,WtE4FC,kBsE1FH,UACA,UACE,atEwFC,kBsEtFH,UACA,UACE,ctEoFC,kBsElFH,UACA,UACE,YtEgFC,kBsE/FH,SAAwB,OtEgGrB,gBsE/FH,UACA,UACE,WtE6FC,gBsE3FH,UACA,UACE,atEyFC,gBsEvFH,UACA,UACE,ctEqFC,gBsEnFH,UACA,UACE,YtEiFC,gBsE3EP,WAAmB,OAAQ,eAC3B,YACA,YACE,WAAY,eAEd,YACA,YACE,aAAc,eAEhB,YACA,YACE,cAAe,eAEjB,YACA,YACE,YAAa,gBzDTS,yByDlDpB,QAAgC,OtEkH7B,YsEjHH,SACA,SACE,WtE+GC,YsE7GH,SACA,SACE,atE2GC,YsEzGH,SACA,SACE,ctEuGC,YsErGH,SACA,SACE,YtEmGC,YsElHH,QAAgC,OtEmH7B,iBsElHH,SACA,SACE,WtEgHC,iBsE9GH,SACA,SACE,atE4GC,iBsE1GH,SACA,SACE,ctEwGC,iBsEtGH,SACA,SACE,YtEoGC,iBsEnHH,QAAgC,OtEoH7B,gBsEnHH,SACA,SACE,WtEiHC,gBsE/GH,SACA,SACE,atE6GC,gBsE3GH,SACA,SACE,ctEyGC,gBsEvGH,SACA,SACE,YtEqGC,gBsEpHH,QAAgC,OtE+G/B,esE9GD,SACA,SACE,WtE4GD,esE1GD,SACA,SACE,atEwGD,esEtGD,SACA,SACE,ctEoGD,esElGD,SACA,SACE,YtEgGD,esE/GD,QAAgC,OtEsH7B,iBsErHH,SACA,SACE,WtEmHC,iBsEjHH,SACA,SACE,atE+GC,iBsE7GH,SACA,SACE,ctE2GC,iBsEzGH,SACA,SACE,YtEuGC,iBsEtHH,QAAgC,OtEuH7B,esEtHH,SACA,SACE,WtEoHC,esElHH,SACA,SACE,atEgHC,esE9GH,SACA,SACE,ctE4GC,esE1GH,SACA,SACE,YtEwGC,esEvHH,QAAgC,QtEkH7B,YsEjHH,SACA,SACE,YtE+GC,YsE7GH,SACA,SACE,ctE2GC,YsEzGH,SACA,SACE,etEuGC,YsErGH,SACA,SACE,atEmGC,YsElHH,QAAgC,QtEmH7B,iBsElHH,SACA,SACE,YtEgHC,iBsE9GH,SACA,SACE,ctE4GC,iBsE1GH,SACA,SACE,etEwGC,iBsEtGH,SACA,SACE,atEoGC,iBsEnHH,QAAgC,QtEoH7B,gBsEnHH,SACA,SACE,YtEiHC,gBsE/GH,SACA,SACE,ctE6GC,gBsE3GH,SACA,SACE,etEyGC,gBsEvGH,SACA,SACE,atEqGC,gBsEpHH,QAAgC,QtE+G/B,esE9GD,SACA,SACE,YtE4GD,esE1GD,SACA,SACE,ctEwGD,esEtGD,SACA,SACE,etEoGD,esElGD,SACA,SACE,atEgGD,esE/GD,QAAgC,QtEsH7B,iBsErHH,SACA,SACE,YtEmHC,iBsEjHH,SACA,SACE,ctE+GC,iBsE7GH,SACA,SACE,etE2GC,iBsEzGH,SACA,SACE,atEuGC,iBsEtHH,QAAgC,QtEuH7B,esEtHH,SACA,SACE,YtEoHC,esElHH,SACA,SACE,ctEgHC,esE9GH,SACA,SACE,etE4GC,esE1GH,SACA,SACE,atEwGC,esEhGH,SAAwB,OtE4FrB,kBsE3FH,UACA,UACE,WtEyFC,kBsEvFH,UACA,UACE,atEqFC,kBsEnFH,UACA,UACE,ctEiFC,kBsE/EH,UACA,UACE,YtE6EC,kBsE5FH,SAAwB,OtE6FrB,iBsE5FH,UACA,UACE,WtE0FC,iBsExFH,UACA,UACE,atEsFC,iBsEpFH,UACA,UACE,ctEkFC,iBsEhFH,UACA,UACE,YtE8EC,iBsE7FH,SAAwB,OtEwFvB,gBsEvFD,UACA,UACE,WtEqFD,gBsEnFD,UACA,UACE,atEiFD,gBsE/ED,UACA,UACE,ctE6ED,gBsE3ED,UACA,UACE,YtEyED,gBsExFD,SAAwB,OtE+FrB,kBsE9FH,UACA,UACE,WtE4FC,kBsE1FH,UACA,UACE,atEwFC,kBsEtFH,UACA,UACE,ctEoFC,kBsElFH,UACA,UACE,YtEgFC,kBsE/FH,SAAwB,OtEgGrB,gBsE/FH,UACA,UACE,WtE6FC,gBsE3FH,UACA,UACE,atEyFC,gBsEvFH,UACA,UACE,ctEqFC,gBsEnFH,UACA,UACE,YtEiFC,gBsE3EP,WAAmB,OAAQ,eAC3B,YACA,YACE,WAAY,eAEd,YACA,YACE,aAAc,eAEhB,YACA,YACE,cAAe,eAEjB,YACA,YACE,YAAa,gBzDTU,0ByDlDrB,QAAgC,OtEkH7B,YsEjHH,SACA,SACE,WtE+GC,YsE7GH,SACA,SACE,atE2GC,YsEzGH,SACA,SACE,ctEuGC,YsErGH,SACA,SACE,YtEmGC,YsElHH,QAAgC,OtEmH7B,iBsElHH,SACA,SACE,WtEgHC,iBsE9GH,SACA,SACE,atE4GC,iBsE1GH,SACA,SACE,ctEwGC,iBsEtGH,SACA,SACE,YtEoGC,iBsEnHH,QAAgC,OtEoH7B,gBsEnHH,SACA,SACE,WtEiHC,gBsE/GH,SACA,SACE,atE6GC,gBsE3GH,SACA,SACE,ctEyGC,gBsEvGH,SACA,SACE,YtEqGC,gBsEpHH,QAAgC,OtE+G/B,esE9GD,SACA,SACE,WtE4GD,esE1GD,SACA,SACE,atEwGD,esEtGD,SACA,SACE,ctEoGD,esElGD,SACA,SACE,YtEgGD,esE/GD,QAAgC,OtEsH7B,iBsErHH,SACA,SACE,WtEmHC,iBsEjHH,SACA,SACE,atE+GC,iBsE7GH,SACA,SACE,ctE2GC,iBsEzGH,SACA,SACE,YtEuGC,iBsEtHH,QAAgC,OtEuH7B,esEtHH,SACA,SACE,WtEoHC,esElHH,SACA,SACE,atEgHC,esE9GH,SACA,SACE,ctE4GC,esE1GH,SACA,SACE,YtEwGC,esEvHH,QAAgC,QtEkH7B,YsEjHH,SACA,SACE,YtE+GC,YsE7GH,SACA,SACE,ctE2GC,YsEzGH,SACA,SACE,etEuGC,YsErGH,SACA,SACE,atEmGC,YsElHH,QAAgC,QtEmH7B,iBsElHH,SACA,SACE,YtEgHC,iBsE9GH,SACA,SACE,ctE4GC,iBsE1GH,SACA,SACE,etEwGC,iBsEtGH,SACA,SACE,atEoGC,iBsEnHH,QAAgC,QtEoH7B,gBsEnHH,SACA,SACE,YtEiHC,gBsE/GH,SACA,SACE,ctE6GC,gBsE3GH,SACA,SACE,etEyGC,gBsEvGH,SACA,SACE,atEqGC,gBsEpHH,QAAgC,QtE+G/B,esE9GD,SACA,SACE,YtE4GD,esE1GD,SACA,SACE,ctEwGD,esEtGD,SACA,SACE,etEoGD,esElGD,SACA,SACE,atEgGD,esE/GD,QAAgC,QtEsH7B,iBsErHH,SACA,SACE,YtEmHC,iBsEjHH,SACA,SACE,ctE+GC,iBsE7GH,SACA,SACE,etE2GC,iBsEzGH,SACA,SACE,atEuGC,iBsEtHH,QAAgC,QtEuH7B,esEtHH,SACA,SACE,YtEoHC,esElHH,SACA,SACE,ctEgHC,esE9GH,SACA,SACE,etE4GC,esE1GH,SACA,SACE,atEwGC,esEhGH,SAAwB,OtE4FrB,kBsE3FH,UACA,UACE,WtEyFC,kBsEvFH,UACA,UACE,atEqFC,kBsEnFH,UACA,UACE,ctEiFC,kBsE/EH,UACA,UACE,YtE6EC,kBsE5FH,SAAwB,OtE6FrB,iBsE5FH,UACA,UACE,WtE0FC,iBsExFH,UACA,UACE,atEsFC,iBsEpFH,UACA,UACE,ctEkFC,iBsEhFH,UACA,UACE,YtE8EC,iBsE7FH,SAAwB,OtEwFvB,gBsEvFD,UACA,UACE,WtEqFD,gBsEnFD,UACA,UACE,atEiFD,gBsE/ED,UACA,UACE,ctE6ED,gBsE3ED,UACA,UACE,YtEyED,gBsExFD,SAAwB,OtE+FrB,kBsE9FH,UACA,UACE,WtE4FC,kBsE1FH,UACA,UACE,atEwFC,kBsEtFH,UACA,UACE,ctEoFC,kBsElFH,UACA,UACE,YtEgFC,kBsE/FH,SAAwB,OtEgGrB,gBsE/FH,UACA,UACE,WtE6FC,gBsE3FH,UACA,UACE,atEyFC,gBsEvFH,UACA,UACE,ctEqFC,gBsEnFH,UACA,UACE,YtEiFC,gBsE3EP,WAAmB,OAAQ,eAC3B,YACA,YACE,WAAY,eAEd,YACA,YACE,aAAc,eAEhB,YACA,YACE,cAAe,eAEjB,YACA,YACE,YAAa,gBC/DnB,gBAAkB,YvEmOY,cAAc,CAAE,KAAK,CAAE,MAAM,CAAE,QAAQ,CAAE,iBAAiB,CAAE,aAAa,CAAE,oBuE/NzG,cAAiB,WAAY,kBAC7B,WAAiB,YAAa,iBAC9B,aAAiB,YAAa,iBAC9B,eCTE,SAAU,OACV,cAAe,SACf,YAAa,ODeX,WAAwB,WAAY,eACpC,YAAwB,WAAY,gBACpC,aAAwB,WAAY,iB1DqCZ,yB0DvCxB,cAAwB,WAAY,eACpC,eAAwB,WAAY,gBACpC,gBAAwB,WAAY,kB1DqCZ,yB0DvCxB,cAAwB,WAAY,eACpC,eAAwB,WAAY,gBACpC,gBAAwB,WAAY,kB1DqCZ,yB0DvCxB,cAAwB,WAAY,eACpC,eAAwB,WAAY,gBACpC,gBAAwB,WAAY,kB1DqCX,0B0DvCzB,cAAwB,WAAY,eACpC,eAAwB,WAAY,gBACpC,gBAAwB,WAAY,kBAMxC,gBAAmB,eAAgB,oBACnC,gBAAmB,eAAgB,oBACnC,iBAAmB,eAAgB,qBAInC,mBAAuB,YvE8MO,cuE7M9B,qBAAuB,YrE6PO,kBqE5P9B,oBAAuB,YvE6MO,cuE5M9B,kBAAuB,YvE6MO,cuE5M9B,oBAAuB,YrE8PO,iBqE7P9B,aAAuB,WAAY,iBAInC,YAAc,MvEjCH,eyENT,cACE,MzE8BM,kBGnBP,qBADA,qBsELK,MAAO,kBANb,gBACE,MzEWO,kBGAR,uBADA,uBsELK,MAAO,kBANb,cACE,MzEqCM,kBG1BP,qBADA,qBsELK,MAAO,kBANb,WACE,MzEuCM,kBG5BP,kBADA,kBsELK,MAAO,kBANb,cACE,MzEoCM,kBGzBP,qBADA,qBsELK,MAAO,kBANb,aACE,MzEkCM,kBGvBP,oBADA,oBsELK,MAAO,kBANb,YACE,MzEMO,kBGKR,mBADA,mBsELK,MAAO,kBANb,WACE,MzEaO,kBGFR,kBADA,kBsELK,MAAO,kBFuCf,WAAa,MvE9BF,kBuE+BX,YAAc,MvElCH,kBuEoCX,eAAiB,MvEhCN,yBuEiCX,eAAiB,MvE3CN,+BuE+CX,WGvDE,KAAM,CAAA,CAAA,EAAA,EACN,MAAO,YACP,YAAa,KACb,iBAAkB,YAClB,OAAQ,EHuDV,sBAAwB,gBAAiB,eAEzC,YACE,WAAY,qBACZ,cAAe,qBAKjB,YAAc,MAAO,kBIjErB,SACE,WAAY,kBAGd,WACE,WAAY,iBCAL,a7EOT,EAEC,QADA,S6EFK,YAAa,eAEb,WAAY,etDbd,YsDkBI,gBAAiB,UASV,mBACT,QAAS,KAAK,YAAY,I7E8LhC,I6E/KM,YAAa,mBAGf,WADA,IAEE,O5E6JwB,I4E7JF,M5ErCjB,Q4EsCL,kBAAmB,MAQrB,MACE,QAAS,mBAIX,IADA,GAEE,kBAAmB,MAIrB,GACA,GAFA,EAGE,QAAS,EACT,OAAQ,EAGV,GACA,GACE,iBAAkB,MAQpB,MACE,K5EmzB8B,GD/1BpC,K6E+CM,U5EkFM,gBWxKV,WiEyFI,U5E+EM,gBgC7JZ,Q4CmFM,QAAS,KvC/Ff,OuCkGM,O5E2GwB,I4E3GF,M5ElFjB,KejBX,O6DuGM,gBAAiB,mBAEjB,UACA,UACE,iB5EpGG,eeqCT,mBADA,mB6DuEM,OAAQ,IAAI,M5ExGT,kBemHX,Y6DNM,MAAO,Q5D3GC,wBAFR,eADA,eAEM,qB4DkHJ,a5EnHG,QemGP,sB6DqBE,MAAO,QACP,a5EzHK,S6EVP,mBACF,gBAAiB,OAKnB,SADA,KADA,KAGE,WAAY,KAGd,SACE,SAAU,SAER,0BACA,WAAY,sDAJhB,uBpEEM,WoEMkB,EAAE,EAAE,G7EDjB,e6EPX,uBAAQ,+BAYF,OAAQ,EAAA,KACR,UCYmB,ODTnB,qCACA,KAAM,QAIgC,2CACxB,gCACd,OAAQ,6BAEc,wCACtB,OAAQ,kCAKV,4DACE,ICsBe,mBDnBM,iFACrB,ICsBkB,uBDnBpB,oDACE,WCce,mBDXK,wEACpB,WCckB,uBDTpB,oEACE,ICQkB,uBDLpB,4DACE,WCIkB,uBDEpB,2EAAA,wEACE,OCPe,mBDQf,MC4De,OD9DN,mFAAA,gFAKP,OCPgB,uBDcpB,mFAAA,gFACE,OCfkB,uBD8BpB,2EACE,WAAY,iCAEZ,oFACE,WClCgB,uBDwCpB,+CACE,IAAK,EAGN,uCACC,QAAS,MACT,SAAU,SACV,IAAK,uCAIL,8DACE,WAAY,MC8DD,IACH,YD9DR,MC3FQ,MD+FZ,0CACE,SAAU,OACV,SAAU,MACV,IAAK,EACL,WAAY,MCqDC,IACH,YDrDV,MCpGU,MDqGV,Q7Eqb8B,K6EhbS,qFACrC,iB7E1HG,Q6EyHkC,sFAKrC,iB7EtIG,K6EiIkC,uFACrC,iB7E1HG,Q6EyHkC,wFAKrC,iB7EtIG,K6EiIkC,qFACrC,iB7E1HG,Q6EyHkC,sFAKrC,iB7EtIG,K6EiIkC,kFACrC,iB7E1HG,Q6EyHkC,mFAKrC,iB7EtIG,K6EiIkC,qFACrC,iB7E1HG,Q6EyHkC,sFAKrC,iB7EtIG,K6EiIkC,oFACrC,iB7E1HG,Q6EyHkC,qFAKrC,iB7EtIG,K6EiIkC,mFACrC,iB7E1HG,Q6EyHkC,oFAKrC,iB7EtIG,K6EiIkC,kFACrC,iB7E1HG,Q6EyHkC,mFAKrC,iB7EtIG,K6E0IP,+CACE,WClFe,mBDqFM,oEACrB,WClFkB,uBDqFpB,2CACE,KAAM,EACN,SAAU,MACV,MAAO,EACP,IAAK,EACL,Q7E0Z8B,K6ErZhC,uDACE,WChGkB,uBDqGpB,8CACE,SAAU,OAIZ,mDADA,2CAEE,WAAY,EAGd,+CACE,SAAU,OAKZ,wDACE,WAAY,EAhJd,4DA0JM,ICnIW,mBDuIU,iFADG,kFAExB,ICpIc,uBDnBpB,oDA2JM,WC5IW,mBDgJS,wEADG,yEAEvB,WC7Ic,uBDTpB,oEA4JM,ICnJc,uBDLpB,4DA4JM,WCvJc,uBDwCpB,+CAqHM,IAAK,EAjHV,uCAqHK,QAAS,MACT,SAAU,SACV,IAAK,uCAIL,2DACE,OC5KS,mBD6KT,WAAY,MCvDL,IACH,YDuDJ,MC1GS,ODuGA,mEAAX,oEAOI,OC9KU,uBDmLZ,+EACE,WAAY,MClEP,IACH,YDkEF,MC3NE,MD+FZ,0CAkIM,SAAU,OACV,SAAU,MACV,IAAK,EACL,WAAY,MC5EH,IACH,YD4EN,MCrOM,MDsON,Q7EoT0B,K6EhbS,qFAkIjC,iB7E3PD,Q6EyHkC,sFAsIjC,iB7EvQD,K6EiIkC,uFAkIjC,iB7E3PD,Q6EyHkC,wFAsIjC,iB7EvQD,K6EiIkC,qFAkIjC,iB7E3PD,Q6EyHkC,sFAsIjC,iB7EvQD,K6EiIkC,kFAkIjC,iB7E3PD,Q6EyHkC,mFAsIjC,iB7EvQD,K6EiIkC,qFAkIjC,iB7E3PD,Q6EyHkC,sFAsIjC,iB7EvQD,K6EiIkC,oFAkIjC,iB7E3PD,Q6EyHkC,qFAsIjC,iB7EvQD,K6EiIkC,mFAkIjC,iB7E3PD,Q6EyHkC,oFAsIjC,iB7EvQD,K6EiIkC,kFAkIjC,iB7E3PD,Q6EyHkC,mFAsIjC,iB7EvQD,K6E0IP,+CAkIM,WCnNW,mBDuNU,oEADG,qEAExB,WCpNc,uBDqFpB,2CAmIM,KAAM,EACN,SAAU,MACV,MAAO,EACP,IAAK,EACL,Q7EwR0B,K6ErZhC,uDAmIM,WClOc,uBD8BpB,2EAoNM,WAAY,iCAjNhB,oFAoNM,WCrPY,uBDqGpB,8CAuJM,SAAU,OAlJhB,mDADA,2CAwJM,WAAY,EAnJlB,+CAuJM,SAAU,OAjJhB,wDAuJM,WAAY,EhEvRM,yBgE0IpB,+DACE,ICnIW,mBDuIU,oFADG,qFAExB,ICpIc,uBDuIhB,uDACE,WC5IW,mBDgJS,2EADG,4EAEvB,WC7Ic,uBDkJhB,uEACE,ICnJc,uBDsJhB,+DACE,WCvJc,uBD4JhB,kDACE,IAAK,EAGN,0CACC,QAAS,MACT,SAAU,SACV,IAAK,uCAIL,8DACE,OC5KS,mBD6KT,WAAY,MCvDL,IACH,YDuDJ,MC1GS,ODuGA,sEAAX,uEAOI,OC9KU,uBDmLZ,kFACE,WAAY,MClEP,IACH,YDkEF,MC3NE,MDgOR,6CACE,SAAU,OACV,SAAU,MACV,IAAK,EACL,WAAY,MC5EH,IACH,YD4EN,MCrOM,MDsON,Q7EoT0B,K6EhbS,wFAkIjC,iB7E3PD,Q6EyHkC,yFAsIjC,iB7EvQD,K6EiIkC,0FAkIjC,iB7E3PD,Q6EyHkC,2FAsIjC,iB7EvQD,K6EiIkC,wFAkIjC,iB7E3PD,Q6EyHkC,yFAsIjC,iB7EvQD,K6EiIkC,qFAkIjC,iB7E3PD,Q6EyHkC,sFAsIjC,iB7EvQD,K6EiIkC,wFAkIjC,iB7E3PD,Q6EyHkC,yFAsIjC,iB7EvQD,K6EiIkC,uFAkIjC,iB7E3PD,Q6EyHkC,wFAsIjC,iB7EvQD,K6EiIkC,sFAkIjC,iB7E3PD,Q6EyHkC,uFAsIjC,iB7EvQD,K6EiIkC,qFAkIjC,iB7E3PD,Q6EyHkC,sFAsIjC,iB7EvQD,K6E2QH,kDACE,WCnNW,mBDuNU,uEADG,wEAExB,WCpNc,uBDuNhB,8CACE,KAAM,EACN,SAAU,MACV,MAAO,EACP,IAAK,EACL,Q7EwR0B,K6EnR5B,0DACE,WClOc,uBDiPhB,8EACE,WAAY,iCAEZ,uFACE,WCrPY,uBD2PhB,iDACE,SAAU,OAIZ,sDADA,8CAEE,WAAY,EAGd,kDACE,SAAU,OAKZ,2DACE,WAAY,GhEvRM,yBgE0IpB,+DACE,ICnIW,mBDuIU,oFADG,qFAExB,ICpIc,uBDuIhB,uDACE,WC5IW,mBDgJS,2EADG,4EAEvB,WC7Ic,uBDkJhB,uEACE,ICnJc,uBDsJhB,+DACE,WCvJc,uBD4JhB,kDACE,IAAK,EAGN,0CACC,QAAS,MACT,SAAU,SACV,IAAK,uCAIL,8DACE,OC5KS,mBD6KT,WAAY,MCvDL,IACH,YDuDJ,MC1GS,ODuGA,sEAAX,uEAOI,OC9KU,uBDmLZ,kFACE,WAAY,MClEP,IACH,YDkEF,MC3NE,MDgOR,6CACE,SAAU,OACV,SAAU,MACV,IAAK,EACL,WAAY,MC5EH,IACH,YD4EN,MCrOM,MDsON,Q7EoT0B,K6EhbS,wFAkIjC,iB7E3PD,Q6EyHkC,yFAsIjC,iB7EvQD,K6EiIkC,0FAkIjC,iB7E3PD,Q6EyHkC,2FAsIjC,iB7EvQD,K6EiIkC,wFAkIjC,iB7E3PD,Q6EyHkC,yFAsIjC,iB7EvQD,K6EiIkC,qFAkIjC,iB7E3PD,Q6EyHkC,sFAsIjC,iB7EvQD,K6EiIkC,wFAkIjC,iB7E3PD,Q6EyHkC,yFAsIjC,iB7EvQD,K6EiIkC,uFAkIjC,iB7E3PD,Q6EyHkC,wFAsIjC,iB7EvQD,K6EiIkC,sFAkIjC,iB7E3PD,Q6EyHkC,uFAsIjC,iB7EvQD,K6EiIkC,qFAkIjC,iB7E3PD,Q6EyHkC,sFAsIjC,iB7EvQD,K6E2QH,kDACE,WCnNW,mBDuNU,uEADG,wEAExB,WCpNc,uBDuNhB,8CACE,KAAM,EACN,SAAU,MACV,MAAO,EACP,IAAK,EACL,Q7EwR0B,K6EnR5B,0DACE,WClOc,uBDiPhB,8EACE,WAAY,iCAEZ,uFACE,WCrPY,uBD2PhB,iDACE,SAAU,OAIZ,sDADA,8CAEE,WAAY,EAGd,kDACE,SAAU,OAKZ,2DACE,WAAY,GhEvRM,yBgE0IpB,+DACE,ICnIW,mBDuIU,oFADG,qFAExB,ICpIc,uBDuIhB,uDACE,WC5IW,mBDgJS,2EADG,4EAEvB,WC7Ic,uBDkJhB,uEACE,ICnJc,uBDsJhB,+DACE,WCvJc,uBD4JhB,kDACE,IAAK,EAGN,0CACC,QAAS,MACT,SAAU,SACV,IAAK,uCAIL,8DACE,OC5KS,mBD6KT,WAAY,MCvDL,IACH,YDuDJ,MC1GS,ODuGA,sEAAX,uEAOI,OC9KU,uBDmLZ,kFACE,WAAY,MClEP,IACH,YDkEF,MC3NE,MDgOR,6CACE,SAAU,OACV,SAAU,MACV,IAAK,EACL,WAAY,MC5EH,IACH,YD4EN,MCrOM,MDsON,Q7EoT0B,K6EhbS,wFAkIjC,iB7E3PD,Q6EyHkC,yFAsIjC,iB7EvQD,K6EiIkC,0FAkIjC,iB7E3PD,Q6EyHkC,2FAsIjC,iB7EvQD,K6EiIkC,wFAkIjC,iB7E3PD,Q6EyHkC,yFAsIjC,iB7EvQD,K6EiIkC,qFAkIjC,iB7E3PD,Q6EyHkC,sFAsIjC,iB7EvQD,K6EiIkC,wFAkIjC,iB7E3PD,Q6EyHkC,yFAsIjC,iB7EvQD,K6EiIkC,uFAkIjC,iB7E3PD,Q6EyHkC,wFAsIjC,iB7EvQD,K6EiIkC,sFAkIjC,iB7E3PD,Q6EyHkC,uFAsIjC,iB7EvQD,K6EiIkC,qFAkIjC,iB7E3PD,Q6EyHkC,sFAsIjC,iB7EvQD,K6E2QH,kDACE,WCnNW,mBDuNU,uEADG,wEAExB,WCpNc,uBDuNhB,8CACE,KAAM,EACN,SAAU,MACV,MAAO,EACP,IAAK,EACL,Q7EwR0B,K6EnR5B,0DACE,WClOc,uBDiPhB,8EACE,WAAY,iCAEZ,uFACE,WCrPY,uBD2PhB,iDACE,SAAU,OAIZ,sDADA,8CAEE,WAAY,EAGd,kDACE,SAAU,OAKZ,2DACE,WAAY,GhEvRO,0BgE0IrB,+DACE,ICnIW,mBDuIU,oFADG,qFAExB,ICpIc,uBDuIhB,uDACE,WC5IW,mBDgJS,2EADG,4EAEvB,WC7Ic,uBDkJhB,uEACE,ICnJc,uBDsJhB,+DACE,WCvJc,uBD4JhB,kDACE,IAAK,EAGN,0CACC,QAAS,MACT,SAAU,SACV,IAAK,uCAIL,8DACE,OC5KS,mBD6KT,WAAY,MCvDL,IACH,YDuDJ,MC1GS,ODuGA,sEAAX,uEAOI,OC9KU,uBDmLZ,kFACE,WAAY,MClEP,IACH,YDkEF,MC3NE,MDgOR,6CACE,SAAU,OACV,SAAU,MACV,IAAK,EACL,WAAY,MC5EH,IACH,YD4EN,MCrOM,MDsON,Q7EoT0B,K6EhbS,wFAkIjC,iB7E3PD,Q6EyHkC,yFAsIjC,iB7EvQD,K6EiIkC,0FAkIjC,iB7E3PD,Q6EyHkC,2FAsIjC,iB7EvQD,K6EiIkC,wFAkIjC,iB7E3PD,Q6EyHkC,yFAsIjC,iB7EvQD,K6EiIkC,qFAkIjC,iB7E3PD,Q6EyHkC,sFAsIjC,iB7EvQD,K6EiIkC,wFAkIjC,iB7E3PD,Q6EyHkC,yFAsIjC,iB7EvQD,K6EiIkC,uFAkIjC,iB7E3PD,Q6EyHkC,wFAsIjC,iB7EvQD,K6EiIkC,sFAkIjC,iB7E3PD,Q6EyHkC,uFAsIjC,iB7EvQD,K6EiIkC,qFAkIjC,iB7E3PD,Q6EyHkC,sFAsIjC,iB7EvQD,K6E2QH,kDACE,WCnNW,mBDuNU,uEADG,wEAExB,WCpNc,uBDuNhB,8CACE,KAAM,EACN,SAAU,MACV,MAAO,EACP,IAAK,EACL,Q7EwR0B,K6EnR5B,0DACE,WClOc,uBDiPhB,8EACE,WAAY,iCAEZ,uFACE,WCrPY,uBD2PhB,iDACE,SAAU,OAIZ,sDADA,8CAEE,WAAY,EAGd,kDACE,SAAU,OAKZ,2DACE,WAAY,GAOlB,+CACE,OAAQ,EAKV,2CACE,OAAQ,EACR,KAAM,EACN,SAAU,MACV,MAAO,EACP,Q7EuN8B,K6ElNhC,+CACE,SAAU,OAGZ,mDACE,cAAe,EArBjB,+CA8BM,OAAQ,EAxBd,2CA8BM,OAAQ,EACR,KAAM,EACN,SAAU,MACV,MAAO,EACP,Q7E0L0B,K6EvL5B,+CACE,eCpSW,mBDwQjB,+CAkCM,SAAU,OhEhVQ,yBgE2TpB,kDACE,OAAQ,EAKV,8CACE,OAAQ,EACR,KAAM,EACN,SAAU,MACV,MAAO,EACP,Q7E0L0B,K6EvL5B,kDACE,eCpSW,mBDySb,kDACE,SAAU,QhEhVQ,yBgE2TpB,kDACE,OAAQ,EAKV,8CACE,OAAQ,EACR,KAAM,EACN,SAAU,MACV,MAAO,EACP,Q7E0L0B,K6EvL5B,kDACE,eCpSW,mBDySb,kDACE,SAAU,QhEhVQ,yBgE2TpB,kDACE,OAAQ,EAKV,8CACE,OAAQ,EACR,KAAM,EACN,SAAU,MACV,MAAO,EACP,Q7E0L0B,K6EvL5B,kDACE,eCpSW,mBDySb,kDACE,SAAU,QhEhVS,0BgE2TrB,kDACE,OAAQ,EAKV,8CACE,OAAQ,EACR,KAAM,EACN,SAAU,MACV,MAAO,EACP,Q7E0L0B,K6EvL5B,kDACE,eCpSW,mBDySb,kDACE,SAAU,QA9XpB,yBAqYI,YAAa,EAGX,mDACE,WAAY,OACZ,aAAc,MACd,OAAQ,KAIV,uCACA,OAAQ,QACR,OAAQ,QAGR,0CAEA,sCADA,sCAEA,YAAa,EAOjB,gFAAgB,wFAChB,4EAAY,oFACZ,4EAAY,oFAGR,YAAa,EhErXS,yBgE2X1B,4CACA,wCACA,wC1DnbI,W0DqboB,YCjQP,IACH,YDkQV,YC3ZU,O3DvB0B,6D0D4axC,4CACA,wCACA,wC1D7aI,WAAY,MNgDU,yBgE2X1B,8DAQqB,0DAAA,0DACf,YAAa,GhEvXU,4BgE8W7B,4CAAgB,oDAChB,wCAAY,gDACZ,wCAAY,gDAcN,YAAa,GhE3YO,yBgEkZ1B,kCACA,8BACA,8B1D1cI,W0D4coB,YCxRP,IACH,YDyRV,YClbU,O3DvB0B,6D0DmcxC,kCACA,8BACA,8B1DpcI,WAAY,MNgDU,yBgEkZ1B,oDAQqB,gDAAA,gDACf,YC/Ua,QjE/DU,4BgEqY7B,kCAAgB,0CAChB,8BAAY,sCACZ,8BAAY,sCAcN,YCtVa,QD4VrB,iBACE,WCxbQ,QD0bN,0BACA,QCxbgB,E9EuiBgB,M6E3GpC,cAAa,sB1DteP,W0DyeyB,YCrTZ,IACH,WAAW,CDoTkD,MCrT1D,IACH,YDsTZ,MC/cY,M3DvB0B,uC0Die1C,cAAa,sB1DheP,WAAY,M0DgelB,yEAAa,iFAWP,WAAY,eAXlB,gCAAa,wCAkBP,YC5dU,OD+dkB,4EAC5B,QAAS,EhEzbgB,4BgEma/B,cAAa,sBA6BP,WAAY,eACZ,YCxeU,OD0chB,4BAAa,oCAoCL,YAAa,GAOnB,kCACE,OAAQ,QACR,WAAY,KACZ,SAAU,SACV,IAAK,EAKP,0BACE,MC/fY,MDkgBd,4BACE,OAAQ,EACR,MAAO,KACP,OAAQ,MACR,KAAM,EACN,SAAU,MACV,IAAK,EAGP,+BACE,OAAQ,EACR,MAAO,KACP,OAAQ,MACR,SAAU,MACV,IAAK,EAEL,wDACE,OAAQ,iCAKwB,uCAtBpC,4BAyBI,OAAQ,SAKd,aACE,W7ExjBS,K6EyjBT,WCte6B,IAEwB,M9ElF5C,Q6EujBT,MAAO,QACP,QC1eoB,KDseV,qBAAZ,sBAQI,QC7eqB,QDifzB,gBACE,QAAS,K7EQyB,M6ETpC,yBAII,QAAS,K7EKuB,M6EFlC,mBACE,UAAW,OACX,OAAQ,EAFV,4BAKI,UAAW,OAIf,4BACE,WAAY,IACZ,YAAa,OACb,cAAe,EACf,QAAS,EAJX,qCAOI,YAAa,OAQjB,kCAIA,kCACiB,oCACjB,8BALA,8BACA,+BACc,iCAIZ,WAAY,eACZ,mBAAoB,aEjnBxB,aACE,c/E6M4B,I8EpJ+B,M9EhDlD,Q+ERT,Q/EsjBkC,K+EpjBlC,uBACE,O/EolBgC,O+EnlBhC,SAAU,SAKV,+BAAA,gCACE,ODsDe,WCrDf,QDoDkB,O9EggBY,K+EljB5B,mCAGA,oCADA,oCADA,oCAGA,0CACA,oCALA,oCAGA,qCADA,qCADA,qCAGA,2CACA,qCACA,U/EsNsB,Q+E/M1B,mCACE,OAAQ,EAIR,yDACE,KAAM,KACN,WAAY,KACZ,MAAO,EAEoB,4BAL7B,yDAMI,KAAM,EACN,MAAO,MAQjB,YACE,OAAQ,kBAAA,CAAA,EACR,MAAO,KAIT,cACE,UAAW,MACX,YAAa,IACb,QAAS,IAAA,IACT,SAAU,SACV,MAAO,IACP,IAAK,IAGP,YACE,iBAAkB,YAClB,kBAAmB,EAGrB,qBACE,mBAAoB,EAEhB,yCACF,YAAa,EAKjB,YADA,qBAEE,WAAY,KAKZ,yBADA,kCAEE,iBDZ+B,qBCa/B,ODVmC,ECajB,+CAEhB,MDb+B,qBCgBT,kEACtB,MDjB+B,qBCWf,wCAWQ,wEACtB,iBD3BmC,qBC4BnC,ODzBuC,YC0BvC,M/EvFG,Q+E+FT,0BADA,mCAEE,iBDhCgC,QCiChC,OD9BoC,ECiClB,gDAEhB,MDjCgC,eCoCV,mEACtB,MDrCgC,eC+BhB,yCAWQ,yEACtB,iB/EtHG,Q+EuHH,OD7CwC,YC8CxC,M/ElHG,QgFfX,YAEE,QAAS,MACT,UhFuO4B,QgFtO5B,YhFuM4B,IgFtM5B,QAJuB,SF8BL,MEzBlB,WAAY,MFgLK,IACH,YEhLd,YAAa,OAPJ,kBAUP,MhFHO,KgFIP,gBAAiB,KAXrB,qBAeI,UAAW,QAff,kCAmBI,cAAe,IAAI,MAAM,QACzB,MhFbO,qBgFPX,mCAwBI,cAAe,IAAI,MhFdZ,QgFeP,MhFRO,egFWT,yBACE,MAAO,KACP,YAAa,GACb,YAAa,MACb,aAAc,MACd,WAAY,KACZ,WAAY,KACZ,MAAO,KAGT,4BACE,MAAO,KACP,YAAa,GACb,WAAY,OACZ,WAAY,KACZ,MAAO,KAGT,4BACE,YAAa,GACb,WAAY,KACZ,MAAO,KAHM,mCAMX,WAAY,OAMd,iCAAA,kCACE,OAAQ,KACR,cAAe,QACf,YAAa,OACb,WAAY,QAGd,oCAAA,qCACE,WAAY,OACZ,WAAY,KAGd,oCAAA,qCACE,WAAY,SACZ,WAAY,KCxElB,cACE,OAAQ,MACR,WAAY,OACZ,QjFqjBkC,KiFljBjC,+BAEG,OAAQ,EACR,QAAS,EAMf,SACE,OAAQ,4BACR,WAAY,KACZ,eHckB,EGblB,aHYkB,MGXlB,cHWkB,MGVlB,YHWkB,EGPpB,YACE,SAAU,SADZ,kCAII,cAAe,IAAI,MAAM,QAJ7B,mCAQI,cAAe,IAAI,MjFvBZ,QiFeX,YAYE,kBACE,SAAU,OACV,YAAa,OAGf,mBACE,QAAS,aACT,aAAc,MAGhB,gBACE,OAAQ,KACR,MHqFuB,OGlFzB,kBACE,QAAS,aACT,QAAS,IAAA,IAAA,IAAA,KAIX,2BADA,oBAEE,UjFgL0B,QiFxKxB,iCACA,cAAe,MAEf,wC9DpEA,W8DqEsB,UHgHZ,YADG,I3D/KuB,uC8D+DpC,wC9D9DA,WAAY,M8DoEJ,8BACI,gCACd,SAAU,SACV,MAAO,KACP,IAAK,MAEL,gCACA,mCADA,kCACA,qCACE,YAAa,MAGF,2CAAA,6CACX,MAAO,OAKP,sCACA,QAAS,MAIR,0CCnFL,UAAW,eD0FT,uBACA,cAAe,EAEf,iCACE,YAAa,OACb,UAAW,OACX,aAAc,MACd,WAAY,OACZ,MHoBmB,OGzBZ,oCAAA,qCAAA,qCAAA,qCAAA,2CAAA,qCAaL,UAAW,OAIf,oCACE,WAAY,IAKhB,2BACE,QAAS,KACT,WAAY,KACZ,QAAS,EAIH,yDACA,MHNe,OGarB,4CACE,WAAY,QHmCC,IACH,YGnCV,aAAc,KAFhB,qDAKI,aAAc,MAMd,qEACE,aAAc,KACd,YAAa,MAFf,8EAKI,aAAc,KACd,YAAa,OAOvB,yBACE,UAAW,MACX,QjFoZgC,MiCxTZ,6CgDzFlB,QAAS,OAAA,KAAA,MAIH,yBACR,QAAS,aACT,eAAgB,OAChB,mBHAe,IGCf,oBAAqB,KACrB,OAAQ,EAIZ,iBAOE,iBjFlLS,eiFmLT,OAAQ,EACR,QAAS,KACT,KAAM,EACN,SAAU,MACV,MAAO,EACP,IAAK,EACL,QjF8WkC,KanfL,4BoEuH/B,+BAGM,QAAS,OAcd,wBAEC,iBjFxMS,KiF4MN,4CACC,MjFpMK,QiFuMP,4CACE,WjFvMK,eiFwML,MjF1MK,QiFwMA,mDAAA,kDAAA,kDAOH,WAAY,eACZ,MjF/MG,QiFmNP,mDxEvNE,WwEwNoB,EAAE,IAAI,IAAI,eAC9B,aAAc,eAGhB,mDACE,MjFzNK,QiFgOI,gEAAA,+DAIP,MjFrOG,QiF0OO,mEACJ,+DACR,iBjF1OK,eiF2OL,MjF5OK,QiF+OI,gEACT,MjF/OK,KiFkPH,WHnDD,EAAA,IAAA,IAAA,eAAA,CAAA,EAAA,IAAA,IAAA,gBGwDD,6DACA,WH/IqB,IGoJzB,oCACE,WAAY,QACZ,MAAO,QAKP,mCACE,MjFvQK,QiFsQN,yCAIG,gBAAiB,KAQjB,0DACA,MHvKsB,KG0Kb,iEAAO,uEAGd,iBjFvRC,eiFwRD,MjFzRC,QiF6RM,gEACT,iBjF7RG,eiFsSH,wEACE,ajFvSC,eiF2SC,8EAAS,qFAGP,ajF9SH,eiFuTV,uBAEC,iBjF3TS,QiF+TN,2CACC,MjFxUK,KiF2UP,2CACE,WH3OkB,qBG4OlB,MH3Oe,QGyOV,kDAAA,iDAAA,iDAOH,WAAY,qBACZ,MjFnVG,KiFuVP,kDxElVE,WwEmVoB,EAAE,IAAI,IAAI,eAC9B,aAAc,qBAGhB,kDACE,MjFpVK,QiF2VI,+DAGP,MHrQa,QG0QH,kEACJ,8DACG,8DACX,iBH9QkB,qBG+QlB,MjFhXK,KiFmXI,+DACT,MjFpXK,KiFuXH,WH9KD,EAAA,IAAA,IAAA,eAAA,CAAA,EAAA,IAAA,IAAA,gBGmLD,4DACA,WHxRoB,IG6RxB,mCACE,WAAY,QACZ,MAAO,QAKP,kCACE,MHxSe,QGuShB,wCAAA,wCAKG,gBAAiB,KAQjB,yDACA,MHjTqB,QGgTZ,+DAAA,+DAKP,iBH1Tc,qBG2Td,MjF5ZC,KiFgaM,gEAAO,sEAAA,sEAId,iBH1TuB,qBG2TvB,MjF7ZC,QiFuaH,uEACE,aHtUuB,qBG0UrB,6EAAS,oFAGP,aH7UmB,qBK9GhB,8DAAA,+DACX,iBnF4BI,QmF3BJ,MnFEK,KmFGM,yEAAA,0EACX,anFqBI,QmF7BO,gEAAA,iEACX,iBnFSK,QmFRL,MnFEK,KmFGM,2EAAA,4EACX,anFEK,QmFVM,8DAAA,+DACX,iBnFmCI,QmFlCJ,MnFEK,KmFGM,yEAAA,0EACX,anF4BI,QmFpCO,2DAAA,4DACX,iBnFqCI,QmFpCJ,MnFEK,KmFGM,sEAAA,uEACX,anF8BI,QmFtCO,8DAAA,+DACX,iBnFkCI,QmFjCJ,MnFoFU,QmF/EC,yEAAA,0EACX,anF2BI,QmFnCO,6DAAA,8DACX,iBnFgCI,QmF/BJ,MnFEK,KmFGM,wEAAA,yEACX,anFyBI,QmFjCO,4DAAA,6DACX,iBnFIK,QmFHL,MnFoFU,QmF/EC,uEAAA,wEACX,anFHK,QmFLM,2DAAA,4DACX,iBnFWK,QmFVL,MnFEK,KmFGM,sEAAA,uEACX,anFIK,QmFZM,gEAAA,iEACX,iBLDM,QKEN,MnFEK,KmFGM,2EAAA,4EACX,aLRM,QKAK,2DAAA,4DACX,iBLAC,QKCD,MnFEK,KmFGM,sEAAA,uEACX,aLPC,QKDU,4DAAA,6DACX,iBLEE,QKDF,MnFEK,KmFGM,uEAAA,wEACX,aLLE,QKHS,2DAAA,4DACX,iBLGC,QKFD,MnFoFU,QmF/EC,sEAAA,uEACX,aLJC,QKJU,8DAAA,+DACX,iBLKI,QKJJ,MnFEK,KmFGM,yEAAA,0EACX,aLFI,QKNO,6DAAA,8DACX,iBLOG,QKNH,MnFEK,KmFGM,wEAAA,yEACX,aLAG,QKRQ,2DAAA,4DACX,iBnF4BI,QmF3BJ,MnFEK,KmFGM,sEAAA,uEACX,anFqBI,QmF7BO,6DAAA,8DACX,iBnF6BI,QmF5BJ,MnFEK,KmFGM,wEAAA,yEACX,anFsBI,QmF9BO,6DAAA,8DACX,iBnF8BI,QmF7BJ,MnFEK,KmFGM,wEAAA,yEACX,anFuBI,QmF/BO,2DAAA,4DACX,iBnF+BI,QmF9BJ,MnFEK,KmFGM,sEAAA,uEACX,anFwBI,QmFhCO,0DAAA,2DACX,iBnFgCI,QmF/BJ,MnFEK,KmFGM,qEAAA,sEACX,anFyBI,QmFjCO,6DAAA,8DACX,iBnFiCI,QmFhCJ,MnFoFU,QmF/EC,wEAAA,yEACX,anF0BI,QmFlCO,6DAAA,8DACX,iBnFkCI,QmFjCJ,MnFoFU,QmF/EC,wEAAA,yEACX,anF2BI,QmFnCO,4DAAA,6DACX,iBnFmCI,QmFlCJ,MnFEK,KmFGM,uEAAA,wEACX,anF4BI,QmFpCO,2DAAA,4DACX,iBnFoCI,QmFnCJ,MnFEK,KmFGM,sEAAA,uEACX,anF6BI,QmFrCO,2DAAA,4DACX,iBnFqCI,QmFpCJ,MnFEK,KmFGM,sEAAA,uEACX,anF8BI,QmFtCO,4DAAA,6DACX,iBnFGK,KmFFL,MnFoFU,QmF/EC,uEAAA,wEACX,anFJK,KmFJM,2DAAA,4DACX,iBnFSK,QmFRL,MnFEK,KmFGM,sEAAA,uEACX,anFEK,QmFVM,gEAAA,iEACX,iBnFWK,QmFVL,MnFEK,KmFGM,2EAAA,4EACX,anFIK,QiF6c+C,mHAAA,2HAAA,iIAAA,sHAAA,8HAAA,oIACtD,aAAc,KACd,YAAa,OAFyB,UAQxC,OAAQ,QHrcU,OGqcoC,EAGlD,8BACA,cAAe,EACf,cAAe,EAEb,wCACA,YAAa,OAST,6EACA,YAAa,MAQrB,yCACE,aAAc,EAEd,mDACE,YAAa,OAGf,uDACE,YAAa,MAAA,MAEb,iEACE,YAAa,QAIb,+EACE,YAAa,QAIb,6FACE,YAAa,QAIb,2GACE,YAAa,QAWvB,qEACE,YAAa,OAGf,qEACE,aAAc,mBAId,mFACE,YAAa,OAIb,iGACE,YAAa,OAIb,+GACE,YAAa,QAIb,6HACE,YAAa,QAcvB,sGAAA,oHAAA,yGAAA,uHACE,YAAa,MAKf,2GAAA,yHAAA,8GAAA,4HACE,YAAa,OAIb,yHAAA,uIAAA,4HAAA,0IACE,YAAa,QAIb,uIAAA,qJAAA,0IAAA,wJACE,YAAa,QAIb,qJAAA,mKAAA,wJAAA,sKACE,YAAa,QAIb,mKAAA,iLAAA,sKAAA,oLACE,YAAa,QAS3B,oB9D5mBI,W8D6mBkB,YHxbR,YADG,I3D/KuB,uC8DumBxC,oB9DtmBI,WAAY,M8D2mBd,kCACE,YAAa,OAKf,8CACE,8CACA,WjFpnBK,sBiFunBD,kEAAA,kEACA,YAAa,MAAA,MAOvB,YACE,OAAQ,QHxmBU,OGwmBoC,EAGlD,4CACA,cAAe,EACf,cAAe,EAEb,sDACA,YAAa,OADb,+DAIE,YAAa,OAOR,mDAEP,WAAY,QACZ,YAAa,IAAA,MAAA,YACb,WAAY,KAEV,6DACA,YAAa,mBADb,sEAIE,YAAa,mBAUf,sFACA,YAAa,mBASjB,wDAAA,2D9DrrBA,W8DsrBsB,YHjgBZ,YADG,IGmgBX,YAAa,O9DlrBqB,uC8DgrBpC,wDAAA,2D9D/qBA,WAAY,M8D0rBd,2GAAA,iGAAA,wGAAA,8FACE,aAAc,KAEd,yHAAA,+GAAA,sHAAA,4GACE,aAAc,KACd,YAAa,MASjB,mHAAA,yGAAA,gHAAA,sGACE,aAAc,MAEd,iIAAA,uHAAA,8HAAA,oHACE,aAAc,KACd,YAAa,OAQf,4EAAA,yEACE,YAAa,OAIX,mFAAA,gFACA,YAAa,OAOf,2FAAA,wFACE,aAAc,EACd,YAAa,EAUjB,oFAAA,iFACE,YAAa,OAIX,2FAAA,wFACA,YAAa,mBAQjB,sEACE,sEACA,WjF/vBG,sBiFkwBM,yEACT,MjFnwBG,KiFuwB8B,2EAAA,0EAAA,0EAIjC,WAAY,IACZ,MjF5wBG,KiFmxBL,uEACE,uEACA,WjF3wBG,gBiF8wBM,0EACT,MjF/wBG,KiFmxB8B,4EAAA,2EAAA,2EAIjC,WAAY,IACZ,MjFxxBG,KiFgyBI,kDACX,WAAY,YACZ,eAAgB,OAChB,mBH9nBe,IG+nBf,oBAAqB,KAIR,oEACX,WAAY,EACZ,eAAgB,QAChB,mBHtoBa,IGuoBb,oBAAqB,KAQV,kHAAA,wGAAA,+GAAA,qGACX,WAAY,YACZ,eAAgB,OAChB,mBHlpBa,IGmpBb,oBAAqB,KAQzB,yBADA,uBAEE,YAAa,OACb,eAAgB,OhD3kBI,6CgD+kBpB,YAAa,OACb,eAAgB,OAGN,8BACI,gCACd,IAAK,QAIO,uCACI,yCACd,IAAK,MAQT,mCADA,4CAEE,WAAY,QACZ,OAAQ,IAAI,MAAM,QAClB,MAAO,KAIG,yCADS,kDAEnB,OAAQ,IAAI,MAAM,QAGR,yCACV,WAAY,QAGF,yCACV,WAAY,QAMd,oCADA,6CAEE,WAAY,QACZ,OAAQ,IAAI,MAAM,QAClB,MjF3yBY,QiF+yBF,0CADS,mDAEnB,OAAQ,IAAI,MAAM,QAGR,0CACV,WAAY,QAGF,0CACV,WAAY,QAKM,mCACpB,MAAO,KAEI,0BACX,cAAe,MAMI,6CACjB,YAAa,EAEf,+BAEA,2BADA,2BAEE,QAAS,KACT,SAAU,SGr6Bd,SADA,SAEE,QAAS,EACT,SAAU,SACV,WAAY,QAHN,wBADA,wBAOJ,KAAM,KACN,IAAK,KAPD,wBADA,wBAYJ,KAAM,KACN,IAAK,IAIT,SACE,QAAS,EACT,WAAY,OAFN,wBAKJ,KAAM,KACN,IAAK,IAKM,gCAET,QAAS,QvEyBa,yBsErC1B,2BAEa,qCADE,uCAEb,YAAa,OACb,SAAU,OAKV,8CACE,QAAS,KAIX,gDACA,4CACA,4CACE,YLgGe,iBK5FJ,wDACX,QAAS,KAGY,wDACrB,MAAO,EAKT,2CADuB,wDADA,0DAGrB,YAAa,MACb,eAAgB,QAChB,mBLiIa,IKhIb,oBAAqB,KACrB,WAAY,OAGd,wCACE,eAAgB,QAChB,mBL0Ha,IKzHb,oBAAqB,KACrB,WAAY,OAGd,wCACE,QAAS,aACT,eAAgB,OAChB,mBLkHa,IKjHb,oBAAqB,KACrB,WAAY,QAId,6CACE,WAAY,OADd,6CAAa,qDAMT,YAAa,EACb,MLmDa,OK/Cb,gEACE,MAAO,KAZA,6DAAA,mDAkBT,ML9DQ,MKgER,yEAAA,+DACE,MLjEM,MKoER,yEAAA,+DACE,WAAY,KAEZ,gFAAA,sEACE,MAAO,KAMX,yEACA,sEAFuB,sFADT,+EAEd,+DACA,4DAFuB,4EADT,qEAIZ,QAAS,aACT,YAAa,EACb,eAAgB,OAChB,mBLqES,IKpET,oBAAqB,KACrB,WAAY,QAGd,sEAAA,4DACE,eAAgB,QAChB,mBL8DS,IK7DT,oBAAqB,KACrB,WAAY,OAGd,0EAAA,gEACE,aAAc,MAKhB,2EACc,+EADd,iEACc,qEACZ,QAAS,gBACT,kBAAmB,cAGkB,mGAAA,yFACrC,QAAS,uBAMf,qDACE,QAAS,gBAKP,4EACE,MLzHM,MK6HV,wDACE,MLxBa,QjE/DU,4BsEK3B,6CC9BA,WAAY,gBvEYY,yBsErC1B,8BAEa,wCADE,0CAEb,YAAa,OACb,SAAU,OAKV,iDACE,QAAS,KAIX,mDACA,+CACA,+CACE,YLgGe,iBK5FJ,2DACX,QAAS,KAGY,2DACrB,MAAO,EAKT,8CADuB,2DADA,6DAGrB,YAAa,MACb,eAAgB,QAChB,mBLiIa,IKhIb,oBAAqB,KACrB,WAAY,OAGd,2CACE,eAAgB,QAChB,mBL0Ha,IKzHb,oBAAqB,KACrB,WAAY,OAGd,2CACE,QAAS,aACT,eAAgB,OAChB,mBLkHa,IKjHb,oBAAqB,KACrB,WAAY,QAId,gDACE,WAAY,OADd,gDAAa,wDAMT,YAAa,EACb,MLmDa,OK/Cb,mEACE,MAAO,KAZA,gEAAA,sDAkBT,ML9DQ,MKgER,4EAAA,kEACE,MLjEM,MKoER,4EAAA,kEACE,WAAY,KAEZ,mFAAA,yEACE,MAAO,KAMX,4EACA,yEAFuB,yFADT,kFAEd,kEACA,+DAFuB,+EADT,wEAIZ,QAAS,aACT,YAAa,EACb,eAAgB,OAChB,mBLqES,IKpET,oBAAqB,KACrB,WAAY,QAGd,yEAAA,+DACE,eAAgB,QAChB,mBL8DS,IK7DT,oBAAqB,KACrB,WAAY,OAGd,6EAAA,mEACE,aAAc,MAKhB,8EACc,kFADd,oEACc,wEACZ,QAAS,gBACT,kBAAmB,cAGkB,sGAAA,4FACrC,QAAS,uBAMf,wDACE,QAAS,gBAKP,+EACE,MLzHM,MK6HV,2DACE,MLxBa,QjE/DU,4BsEK3B,gDCnBA,WAAY,gBAIhB,kBACE,KACE,QAAS,EAGX,GACE,QAAS,GAIb,mBACE,KACE,QAAS,EAGX,GACE,QAAS,GAOT,4DAAA,kDACE,QAAS,aAIkB,kEACA,wDAC7B,MN2CiB,OMzCjB,8EAAA,oEACE,QAAS,KAGX,8EAAA,oEACE,MNoCe,iBMjCL,qFAAA,2EACV,MAAO,eAGT,2EAAA,iEACE,eAAgB,OAChB,mBN6Ea,IM5Eb,oBAAqB,KACrB,WAAY,QAGd,2EAAA,iEACE,eAAgB,QAChB,mBNsEa,IMrEb,oBAAqB,KACrB,WAAY,OAGgB,8GAAA,oGAC5B,aAAc,EAGhB,8EAEuB,2FADT,oFADd,oEAEuB,iFADT,0EAEZ,YAAa,MACb,eAAgB,QAChB,mBNwDa,IMvDb,oBAAqB,KACrB,WAAY,OACZ,MAAO,EAGgB,mGAAA,yFACvB,aAAc,EAKpB,aACE,SAAU,SADA,mBAIR,SAAU,QAKC,yBADf,cAEE,SAAU,OACV,cAAe,KHlFX,iCGsFJ,SAAU,SAER,8CACA,WAAY,KACZ,SAAU,SACV,MAAO,KACP,IAAK,IAKK,0BAEA,uBADA,uBAFK,qBAIE,2BjErKf,WiEsKgB,YNcH,IMdiC,MAAM,CAAE,QNczC,IMdmE,IAAI,CAAE,WNczE,IMdsG,KjEjK/E,uCiE6J5B,0BAEA,uBADA,uBAFK,qBAIE,2BjE/Jf,WAAY,MkERd,6BACF,WAAY,OAGd,iBACE,OPyFmB,mBOxFnB,SAAU,SACV,IPyDmB,mBOxDnB,QrFgjBkC,KqFpjBpC,iBAAgB,yBAQZ,OPkFiB,mBOjFjB,QAAS,KACT,MPgBY,OOfZ,MPeY,M3D5BV,WkEckB,MPsKL,IACH,WAAW,COvKqC,QPsK7C,IACH,Y3DhL0B,uCkEH1C,iBAAgB,yBlEIV,WAAY,MkEJF,yBAgBZ,QAAS,GACT,QAAS,MACT,SAAU,MACV,IAAK,EACL,QAAS,GAKX,8BACE,OPkEoB,sBOjEpB,IPqCoB,uBOjCD,sCACrB,IPgCsB,uBO7BD,sCACrB,OPwDsB,sBOpDpB,6CACA,yClE3CE,WkE4CoB,aPwIP,IACH,Y3DhL0B,uCkEqCtC,6CACA,yClErCE,WAAY,MkE4ChB,uCACE,QAAS,MADX,uCAAgB,+CAKZ,MAAO,EAMT,4DACA,wDADA,kEACA,8DACE,aPnCU,MO0Cd,6CACE,QAAS,MADX,6CAAgB,qDAKZ,MAAO,ElE3EP,WkE4EoB,MPwGP,IACH,WAAW,COzGuC,QPwG/C,IACH,Y3DhL0B,uCkEiExC,6CAAgB,qDlEhEZ,WAAY,MkE4Ed,kEACA,8DADA,wEACA,oEACE,aPxDU,MO8DhB,sBAGE,gCADA,wBAEE,MPSiB,QObrB,sBASI,WrFtFO,QqFyFR,8BACC,MrFlGO,KqFsGT,yBACA,yBACA,yBACA,yBACA,yBACA,yBACA,4BACE,MrF7GO,KqFiHT,gCACE,iBPjBoB,qBOkBpB,cAAe,EACf,cAAe,IAEf,0CACE,OAAQ,EAGV,0CACE,cAAe,EACf,QAAS,KAAA,KACT,SAAU,SACV,WAAY,OAJd,0CAAS,iDAAA,iDAAA,gDAAA,gDAWL,OAAQ,EAXH,iDAAA,iDAAA,gDAAA,gDAkBL,oBAAqB,YACrB,kBAAmB,YACnB,iBAAkB,YAClB,MrF/IG,KqF0HE,iDAyBL,iBrF3IG,QqFgJT,gCACE,QAAS,KAAA,KAKb,uBACE,MAAO,QADT,uBAKI,WrFnKO,KqFoKP,YrFmC0B,I8EpJ+B,M9EhDlD,QsFCT,wBACE,UtF+N0B,kBsF5NZ,iCACd,eAAgB,MAIpB,qBACE,UtFoN4B,KsFnN5B,OAAQ,EAGI,sBAEV,YAAa,EAKjB,kBACE,UAAW,MACX,UAAW,MACX,QAAS,EAET,oCACE,OAAQ,EAGV,iCACE,QtF+egC,MAmBA,KsF/flC,oBACE,OAAQ,EACR,YAAa,OAKjB,kBACE,SAAU,SAEL,0B3D5CL,W3B6M4B,K2B7MH,MAAM,YAC/B,aAAc,EACd,c3B2M4B,K2B3MA,MAAM,YAClC,Y3B0M4B,K2B1MF,M2D2CxB,MAAO,MACP,YAAa,MACb,WAAY,MAGV,iCACF,KAAM,KACN,YAAa,EACb,WAAY,EACZ,IAAK,EAUH,uDAAA,sDAAA,uDAAA,qCACA,QAAS,MAQf,kBACE,UAAW,MACX,UAAW,MACX,QAAS,EAET,oCACE,OAAQ,EAGV,iCACE,QtF0bgC,MAmBA,KsF1clC,oBACE,OAAQ,EACR,YAAa,OAKjB,iBACA,iBACE,QAAS,MACT,UtF+H4B,QsF9H5B,QAAS,MtF+byB,KsF9blC,WAAY,OAKO,2CJhGnB,UIiGmB,QAAQ,IAAI,KAC/B,oBAAqB,kBAGvB,mBACE,GACE,UAAW,mBAAmB,sBAC9B,2BAA4B,QAC5B,QAAS,EAGX,IACE,UAAW,mBAAmB,uBAC9B,2BAA4B,QAG9B,IACE,UAAW,mBAAmB,sBAC9B,QAAS,EAGX,IACE,UAAW,mBAAmB,sBAGhC,KACE,UAAW,oBAMX,mCACA,SAAU,SACR,kDACA,SAAU,SACV,MAAO,EACP,KAAM,KzEpFmB,4ByE0FP,gCACpB,MAAO,MAZP,mCAcE,SAAU,OAZV,kDAcE,SAAU,SACV,MAAO,GACP,KAAM,KACN,OAAQ,IAAA,MAAA,KACR,WtFjKG,MsFyKE,uCACT,QAAQ,KAGR,sC9E1KA,uB8E2K2B,E9E1K3B,wB8E0K2B,EAC3B,QAAS,EACT,MAAO,MAHP,sCAME,iD9ElKF,2B8EmKgC,I9ElKhC,0B8EkKgC,IAI5B,qDACF,OAAQ,MACR,QAAS,KACT,WAAY,OAGV,yDACA,QAAS,EACT,OAAQ,KACR,MAAO,KACP,OAAQ,IAAA,MACR,aAAc,YACd,aAAc,qBAGd,uDACA,QAAS,EACT,UAAW,KAEX,WAAY,KAEV,6DACA,QAAS,MACT,UAAW,KAMf,iDAEA,cAAe,IAAI,MtFhNd,QsFiNL,WAAY,IAAI,MtFrNX,QsFsNL,QAAS,KjCnOZ,wDACC,QAAS,MACT,MAAO,KACP,QAAS,GxCwDe,yByE0KtB,mDAEI,WtF7NC,esF8ND,MtFvNC,mBsF6NL,mDAEA,iBtFrOK,QsFsOL,QAAS,KjCjPZ,0DACC,QAAS,MACT,MAAO,KACP,QAAS,GiCgPP,gEACE,MtFpOG,Qa2CiB,yByEwLV,sEAKN,iBtF7OD,SsFoPT,mCAQE,cAAe,IACf,MAAO,KACP,ORhIuB,OQiIvB,aAAc,KACd,WAAY,KACZ,MRnIuB,OjE9EC,yByEoM1B,mCAEI,MAAO,KACP,YAAa,KACb,aAAc,MACd,WAAY,MvDzLhB,qBwDtEE,MvFWO,QuFTO,wCACZ,MvF2BI,QuFrBK,mDACP,MvFLG,KuFYF,sBACP,cAAe,EACf,avFyL4B,IuFzLS,MvFX5B,QuFaT,gCACE,0BvFyL0B,OuFxL1B,wBAAyB,EACzB,avFoL0B,KGjM3B,sCADA,sCoFiBG,avFpBK,QuFoBmB,YvFpBnB,QAAA,QuFyBM,+CADN,uCAEP,avFzBO,QuFyBiB,YvFzBjB,QAAA,QuFSU,qCAoBjB,YvFuK0B,IuFvKU,MvF7B7B,QuF8BP,aAAc,EAEd,+CACE,0BAA2B,EAC3B,2BvFqKwB,OuFpKxB,uBAAwB,EACxB,wBvFmKwB,OuFlKxB,YvF+JwB,KGjM3B,qDADA,qDoFsCK,avFzCG,QAAA,QAAA,QuFyCyC,YAKjC,8DADN,sDAEP,avF9CK,QAAA,QAAA,QuF8CuC,YAKlD,kBACE,eAAgB,IAEhB,4BACE,avFohBgC,KuFnhBhC,cvFmhBgC,KuFhhBlC,iCACE,SAAU,SAOV,cACE,iBvFtEK,QuFqEP,aACE,iBvF/DK,QuFsEP,gBACE,iBvFtDI,QuFqDN,kBACE,iBvFzEK,QuFwEP,gBACE,iBvF/CI,QuF8CN,aACE,iBvF7CI,QuF4CN,gBACE,iBvFhDI,QuF+CN,eACE,iBvFlDI,QuFwDR,kBACE,iBT1FQ,QSyFV,aACE,iBTzFG,QSwFL,cACE,iBTvFI,QSsFN,aACE,iBTtFG,QSqFL,gBACE,iBTpFM,QSmFR,eACE,iBTlFK,QSiFP,aACE,iBvF7DM,QuF4DR,eACE,iBvF5DM,QuF2DR,eACE,iBvF3DM,QuF0DR,aACE,iBvF1DM,QuFyDR,YACE,iBvFzDM,QuFwDR,eACE,iBvFxDM,QuFuDR,eACE,iBvFvDM,QuFsDR,cACE,iBvFtDM,QuFqDR,aACE,iBvFrDM,QuFoDR,aACE,iBvFpDM,QuFmDR,cACE,iBvFtFO,KuFqFT,aACE,iBvFhFO,QuF+ET,kBACE,iBvF9EO,QwFfX,oBACE,aAAc,sBAIhB,mBACE,QAAS,MACT,OAAQ,KAAA,EACR,WAAY,OAHI,iCAMd,cAAe,KAGf,uCACA,UAAW,KACX,YAAa,IACb,OAAQ,EACR,QAAS,EAGT,qCACA,eAAgB,UAIlB,qCACE,UAAW,KAMX,wCACA,YAAa,EACb,cAAe,EACf,aAAc,EACd,aAAc,EACd,cAAe,EAInB,aACE,MxF9BS,QwF+BT,UAAW,KACX,YAAa,IACb,QAAS,KAAA,IAGX,gBACE,WxFjCS,iBwFkCT,OAAQ,IACR,OAAQ,KAAA,EAAA,IAIN,aACA,MxF5CO,QwF6CP,QAAS,IAFR,mBAKC,MxF7CK,QwFmDX,YACE,MAAO,KAEP,gBACE,MAAO,KACP,OAAQ,KACR,MAAO,KAKT,qBADA,yBADA,sBAGE,QAAS,MACT,YAAa,KAGf,sBACE,UAAW,KACX,YAAa,IACb,WAAY,KAGd,yBACE,MxF7EO,QwF8EP,UAAW,KACX,WAAY,KAIZ,8BACE,MVhDQ,SUiDR,OVjDQ,SUsDV,mCADA,uCADA,oCAGE,YAAa,KAGf,oCACE,UAAW,KAQjB,QADA,QADA,QAGE,MAAO,KAGT,QACE,OVxEY,SUyEZ,MVzEY,SU2EV,kBACA,YAAa,OAIjB,QACE,MVhFY,QUiFZ,OVjFY,QUmFV,kBACA,YAAa,SAIjB,QACE,MVxFY,QUyFZ,OVzFY,QU2FV,kBACA,YAAa,SAKjB,cACE,OAAQ,IAAI,MxFzIH,QwF0IT,QAAS,IAGX,iBACE,OAAQ,IAAI,MxF9IH,QwF+IT,QAAS,IAIX,ahF9JI,cRgN0B,OwF9C9B,YhFlKI,cgFmKqB,IAMzB,aADA,aADA,aAGE,OAAQ,KAGV,aACE,MAAO,KAGT,aACE,MAAO,KAGT,aACE,MAAO,KAIT,SACA,SACA,SACE,QAAS,MACT,WAAY,OAGd,SACE,OAAQ,KACR,YAAa,KACb,MAAO,KAGT,SACE,OAAQ,KACR,YAAa,KACb,MAAO,KAGT,SACE,OAAQ,KACR,YAAa,KACb,MAAO,KAIT,kBACE,WxF9MS,QwF+MT,OAAQ,IAAI,MxFtMH,iBwFuMT,cAAe,KACf,QAAS,IAET,kCACE,MAAO,KACP,OAAQ,KACR,WAAY,MACZ,UAAW,MAGb,qCACE,YAAa,MAGf,sCACE,OAAQ,EAGV,mCACE,MxF7NO,QwFwOP,mBADA,eACA,uBADA,mBACA,8BADA,0BACA,wBADA,oBAEA,OAAQ,KACR,KAAM,EACN,SAAU,SACV,IAAK,EACL,MAAO,KAGT,eAAA,mBAAA,0BAAA,oBhF7PE,cRgN0B,OwF+C1B,YAAa,OACb,WxF1PO,qBwF2PP,QAAS,KACT,gBAAiB,OACjB,QAAS,GAEP,mBAGA,oBADA,oBADA,oBAGA,0BACA,oBALA,uBAGA,wBADA,wBADA,wBAGA,8BACA,wBALA,8BAGA,+BADA,+BADA,+BAGA,qCACA,+BALA,wBAGA,yBADA,yBADA,yBAGA,+BACA,yBACA,MxF7PK,QwF+OD,oBAAA,wBAAA,+BAAA,yBAkBJ,WxF/PK,ewFiQH,wBAGA,yBADA,yBADA,yBAGA,+BACA,yBALA,4BAGA,6BADA,6BADA,6BAGA,mCACA,6BALA,mCAGA,oCADA,oCADA,oCAGA,0CACA,oCALA,6BAGA,8BADA,8BADA,8BAGA,oCACA,8BACA,MxF7QG,QwFqRP,2BACA,SAAU,SACR,oCACA,uBAAwB,EACxB,wBAAyB,EACzB,eAAgB,OAChB,WxFiX8B,SwFhX9B,YxFgX8B,SwF/W9B,OAAQ,yBACR,MAAO,yBAPC,yCAUN,MxFrSG,KwF4SX,gBACE,OVtFoB,KUuFpB,SAAU,OACV,SAAU,SACV,MAAO,KACP,IAAK,KACL,MV3FoB,KU4FpB,QAAS,GAPI,0BAUX,OV5FqB,MU6FrB,MV7FqB,MU+FrB,kCACE,MV7FY,EU8FZ,IV/FU,KUgGV,MVjGY,MUiFH,0BAqBX,OVnGqB,MUoGrB,MVpGqB,MUsGrB,kCACE,MVpGY,IUqGZ,IVtGU,KUuGV,MVxGY,MU4GhB,wBACE,WAAY,EAAE,EV1HG,I9ExMV,ewFmUP,UVxHe,MUyHf,YV3HiB,KU4HjB,QV3Ha,QAAQ,EU4HrB,SAAU,SACV,MVxHY,KUyHZ,WAAY,OACZ,YAAa,EAAG,KAAI,ExFzUb,ewF0UP,eAAgB,UAChB,IV7HS,KU8HT,UAAW,cACX,MVjIW,KUqHN,+BAAA,gCAgBH,YVzIe,IUyIkB,MAAM,YACvC,aV1Ie,IU0ImB,MAAM,YACxC,WV3Ie,IU2IiB,MAAM,QACtC,OV5Ie,KU6If,QAAS,GACT,SAAU,SArBP,gCAyBH,KAAM,EAzBH,+BA6BH,MAAO,EAMb,aACE,OAAQ,QACR,SAAU,MACV,MAAO,QACP,QxF+LkC,KwFnMxB,mBAOR,WAAY,KzFhKhB,IyFsKE,QAAS,OzF/OX,WyFoPE,WxFhYS,KwFiYT,YAAa,MAAM,MxFxWX,QwFyWR,OAAQ,MAAA,MACR,QAAS,KAAA,MAJX,gBAOI,WxFpYO,QwFuYR,wBACC,cAAe,EAGjB,cACA,cACA,cACA,cACA,cACA,cACE,MxF1XM,QwF2XN,UAAW,QACX,YAAa,IAtBP,yBA2BJ,axFjYI,QwFmYJ,4BACA,4BACA,4BACA,4BACA,4BACA,4BACE,MxFzYE,QwFsWA,2BA2BJ,axFpZK,QwFsZL,8BACA,8BACA,8BACA,8BACA,8BACA,8BACE,MxF5ZG,QwFyXD,yBA2BJ,axF1XI,QwF4XJ,4BACA,4BACA,4BACA,4BACA,4BACA,4BACE,MxFlYE,QwF+VA,sBA2BJ,axFxXI,QwF0XJ,yBACA,yBACA,yBACA,yBACA,yBACA,yBACE,MxFhYE,QwF6VA,yBA2BJ,axF3XI,QwF6XJ,4BACA,4BACA,4BACA,4BACA,4BACA,4BACE,MxFnYE,QwFgWA,wBA2BJ,axF7XI,QwF+XJ,2BACA,2BACA,2BACA,2BACA,2BACA,2BACE,MxFrYE,QwFkWA,uBA2BJ,axFzZK,QwF2ZL,0BACA,0BACA,0BACA,0BACA,0BACA,0BACE,MxFjaG,QwF8XD,sBA2BJ,axFlZK,QwFoZL,yBACA,yBACA,yBACA,yBACA,yBACA,yBACE,MxF1ZG,QwFuXD,2BA0CJ,aV7aM,QU+aN,8BACA,8BACA,8BACA,8BACA,8BACA,8BACE,MVrbI,QUmYF,sBA0CJ,aV5aC,QU8aD,yBACA,yBACA,yBACA,yBACA,yBACA,yBACE,MVpbD,QUkYG,uBA0CJ,aV1aE,QU4aF,0BACA,0BACA,0BACA,0BACA,0BACA,0BACE,MVlbA,QUgYE,sBA0CJ,aVzaC,QU2aD,yBACA,yBACA,yBACA,yBACA,yBACA,yBACE,MVjbD,QU+XG,yBA0CJ,aVvaI,QUyaJ,4BACA,4BACA,4BACA,4BACA,4BACA,4BACE,MV/aE,QU6XA,wBA0CJ,aVraG,QUuaH,2BACA,2BACA,2BACA,2BACA,2BACA,2BACE,MV7aC,QU2XC,sBA0CJ,axFhZI,QwFkZJ,yBACA,yBACA,yBACA,yBACA,yBACA,yBACE,MxFxZE,QwFsWA,wBA0CJ,axF/YI,QwFiZJ,2BACA,2BACA,2BACA,2BACA,2BACA,2BACE,MxFvZE,QwFqWA,wBA0CJ,axF9YI,QwFgZJ,2BACA,2BACA,2BACA,2BACA,2BACA,2BACE,MxFtZE,QwFoWA,sBA0CJ,axF7YI,QwF+YJ,yBACA,yBACA,yBACA,yBACA,yBACA,yBACE,MxFrZE,QwFmWA,qBA0CJ,axF5YI,QwF8YJ,wBACA,wBACA,wBACA,wBACA,wBACA,wBACE,MxFpZE,QwFkWA,wBA0CJ,axF3YI,QwF6YJ,2BACA,2BACA,2BACA,2BACA,2BACA,2BACE,MxFnZE,QwFiWA,wBA0CJ,axF1YI,QwF4YJ,2BACA,2BACA,2BACA,2BACA,2BACA,2BACE,MxFlZE,QwFgWA,uBA0CJ,axFzYI,QwF2YJ,0BACA,0BACA,0BACA,0BACA,0BACA,0BACE,MxFjZE,QwF+VA,sBA0CJ,axFxYI,QwF0YJ,yBACA,yBACA,yBACA,yBACA,yBACA,yBACE,MxFhZE,QwF8VA,sBA0CJ,axFvYI,QwFyYJ,yBACA,yBACA,yBACA,yBACA,yBACA,yBACE,MxF/YE,QwF6VA,uBA0CJ,axFzaK,KwF2aL,0BACA,0BACA,0BACA,0BACA,0BACA,0BACE,MxFjbG,KwF+XD,sBA0CJ,axFnaK,QwFqaL,yBACA,yBACA,yBACA,yBACA,yBACA,yBACE,MxF3aG,QwFyXD,2BA0CJ,axFjaK,QwFmaL,8BACA,8BACA,8BACA,8BACA,8BACA,8BACE,MxFzaG,QwFkbX,oBACE,WxFpP4B,IwFoPO,MxFxb1B,QwFybT,WAAY,MACZ,YAAa,MAGR,yBACL,WAAY,KACZ,cxF3P4B,IwF2PU,MxF/b7B,QwFgcT,WAAY,EACZ,cAAe,MACf,eAAgB,MAKlB,WACE,cVzQwB,OU0QxB,UV3QoB,OU4QpB,YAAa,IACb,QAAS,OAAuB,MAGxB,sBACR,QAAS,QAAA,MCzdJ,aASL,gBADA,aADA,cALA,UACE,QAAS,eAWX,iBACA,aPbA,UAAW,eOeT,YAAa,YACb,WAAY,YAGA,+BACZ,YAAa,YAIf,SACE,OAAQ,EACR,OAAQ,EACR,QAAS,EACT,MAAO,KAGT,aACE,MAAO,KACP,MAAO,Y1E8HX,kB0EzHI,SAAU,KAGE,+BADA,+BAEV,YAAa,kBC3CnB,WACa,oBAAY,oBACrB,YAAa,IAIjB,SACE,UZmLa,iBYhLf,SACE,U1F+N4B,kB0F5N9B,SACE,U1FyN4B,e0FtN9B,SACE,U1FsN4B,kB0FnN9B,SACE,UZoKa,eY/Jb,gBACE,MAAO,kBADT,WACE,MAAO,kBADT,YACE,MAAO,kBADT,WACE,MAAO,kBADT,cACE,MAAO,kBADT,aACE,MAAO,kBADT,WACE,MAAO,kBADT,aACE,MAAO,kBADT,aACE,MAAO,kBADT,WACE,MAAO,kBADT,UACE,MAAO,kBADT,aACE,MAAO,kBADT,aACE,MAAO,kBADT,YACE,MAAO,kBADT,WACE,MAAO,kBADT,WACE,MAAO,kBnBUX,YmBVI,MAAO,eADT,WACE,MAAO,kBADT,gBACE,MAAO,kBC9BX,aACE,WAAY,eAKZ,aACE,WbyMG,EAAA,IAAA,IAAA,eAAA,CAAA,EAAA,IAAA,IAAA,0Ba1ML,aACE,Wb0MG,EAAA,IAAA,IAAA,eAAA,CAAA,EAAA,IAAA,IAAA,0Ba3ML,aACE,Wb2MG,EAAA,KAAA,KAAA,eAAA,CAAA,EAAA,IAAA,IAAA,0Ba5ML,aACE,Wb4MG,EAAA,KAAA,KAAA,eAAA,CAAA,EAAA,KAAA,KAAA,0Ba7ML,aACE,Wb6MG,EAAA,KAAA,KAAA,cAAA,CAAA,EAAA,KAAA,KAAA,0BtBnNL,YoCEE,iBAAkB,kBADpB,YAII,cACA,M5FAK,e4FGF,sBAED,aAAc,QACd,MAAO,QAHN,uBAAA,uBAO4B,qDADA,qDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,KwDNT,coCEE,iBAAkB,kBADpB,cAII,gBACA,M5FAK,e4FGF,wBAED,aAAc,QACd,MAAO,QAHN,yBAAA,yBAO4B,uDADA,uDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,KwDNT,YoCEE,iBAAkB,kBADpB,YAII,cACA,M5FAK,e4FGF,sBAED,aAAc,QACd,MAAO,QAHN,uBAAA,uBAO4B,qDADA,qDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,KwDNT,SoCEE,iBAAkB,kBADpB,SAII,WACA,M5FAK,e4FGF,mBAED,aAAc,QACd,MAAO,QAHN,oBAAA,oBAO4B,kDADA,kDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,KwDNT,YoCEE,iBAAkB,kBADpB,YAII,cACA,M5FkFU,kB4F/EP,sBAED,aAAc,QACd,MAAO,QAHN,uBAAA,uBAO4B,qDADA,qDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FmEQ,QwDxFd,WoCEE,iBAAkB,kBADpB,WAII,aACA,M5FAK,e4FGF,qBAED,aAAc,QACd,MAAO,QAHN,sBAAA,sBAO4B,oDADA,oDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,KwDNT,UoCEE,iBAAkB,kBADpB,UAII,YACA,M5FkFU,kB4F/EP,oBAED,aAAc,QACd,MAAO,QAHN,qBAAA,qBAO4B,mDADA,mDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FmEQ,QwDxFd,SoCEE,iBAAkB,kBADpB,SAII,WACA,M5FAK,e4FGF,mBAED,aAAc,QACd,MAAO,QAHN,oBAAA,oBAO4B,kDADA,kDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,K4FLT,cACE,iBAAkB,kBADpB,cAII,gBACA,M5FAK,e4FGF,wBAED,aAAc,QACd,MAAO,QAHN,yBAAA,yBAO4B,uDADA,uDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,K4FLT,SACE,iBAAkB,kBADpB,SAII,WACA,M5FAK,e4FGF,mBAED,aAAc,QACd,MAAO,QAHN,oBAAA,oBAO4B,kDADA,kDAI7B,iBAAkB,kBAClB,aAAc,KACd,M5FfG,K4FLT,UACE,iBAAkB,kBADpB,UAII,YACA,M5FAK,e4FGF,oBAED,aAAc,QACd,MAAO,QAHN,qBAAA,qBAO4B,mDADA,mDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,K4FLT,SACE,iBAAkB,kBADpB,SAII,WACA,M5FkFU,kB4F/EP,mBAED,aAAc,QACd,MAAO,QAHN,oBAAA,oBAO4B,kDADA,kDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,K4FLT,YACE,iBAAkB,kBADpB,YAII,cACA,M5FAK,e4FGF,sBAED,aAAc,QACd,MAAO,QAHN,uBAAA,uBAO4B,qDADA,qDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,K4FLT,WACE,iBAAkB,kBADpB,WAII,aACA,M5FAK,e4FGF,qBAED,aAAc,QACd,MAAO,QAHN,sBAAA,sBAO4B,oDADA,oDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,K4FLT,SACE,iBAAkB,kBADpB,SAII,WACA,M5FAK,e4FGF,mBAED,aAAc,QACd,MAAO,QAHN,oBAAA,oBAO4B,kDADA,kDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,K4FLT,WACE,iBAAkB,kBADpB,WAII,aACA,M5FAK,e4FGF,qBAED,aAAc,QACd,MAAO,QAHN,sBAAA,sBAO4B,oDADA,oDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,K4FLT,WACE,iBAAkB,kBADpB,WAII,aACA,M5FAK,e4FGF,qBAED,aAAc,QACd,MAAO,QAHN,sBAAA,sBAO4B,oDADA,oDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,K4FLT,SACE,iBAAkB,kBADpB,SAII,WACA,M5FAK,e4FGF,mBAED,aAAc,QACd,MAAO,QAHN,oBAAA,oBAO4B,kDADA,kDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,K4FLT,QACE,iBAAkB,kBADpB,QAII,UACA,M5FAK,e4FGF,kBAED,aAAc,QACd,MAAO,QAHN,mBAAA,mBAO4B,iDADA,iDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,K4FLT,WACE,iBAAkB,kBADpB,WAII,aACA,M5FkFU,kB4F/EP,qBAED,aAAc,QACd,MAAO,QAHN,sBAAA,sBAO4B,oDADA,oDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,K4FLT,WACE,iBAAkB,kBADpB,WAII,aACA,M5FkFU,kB4F/EP,qBAED,aAAc,QACd,MAAO,QAHN,sBAAA,sBAO4B,oDADA,oDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FmEQ,Q4FvFd,UACE,iBAAkB,kBADpB,UAII,YACA,M5FAK,e4FGF,oBAED,aAAc,QACd,MAAO,QAHN,qBAAA,qBAO4B,mDADA,mDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,K4FLT,SACE,iBAAkB,kBADpB,SAII,WACA,M5FAK,e4FGF,mBAED,aAAc,QACd,MAAO,QAHN,oBAAA,oBAO4B,kDADA,kDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,K4FLT,SACE,iBAAkB,kBADpB,SAII,WACA,M5FAK,e4FGF,mBAED,aAAc,QACd,MAAO,QAHN,oBAAA,oBAO4B,kDADA,kDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,KyDCX,UmCLI,iBAAkB,eADpB,UAII,YACA,M5FkFU,kB4F/EP,oBAED,aAAc,QACd,MAAO,QAHN,qBAAA,qBAO4B,mDADA,mDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FmEQ,Q4FvFd,SACE,iBAAkB,kBADpB,SAII,WACA,M5FAK,e4FGF,mBAED,aAAc,QACd,MAAO,QAHN,oBAAA,oBAO4B,kDADA,kDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,K4FLT,cACE,iBAAkB,kBADpB,cAII,gBACA,M5FAK,e4FGF,wBAED,aAAc,QACd,MAAO,QAHN,yBAAA,yBAO4B,uDADA,uDAI7B,iBAAkB,kBAClB,aAAc,QACd,M5FfG,K4FLT,SCSA,iB7FCS,Q6FAT,M7F6Ec,Q6F1EhB,eACE,iBAAkB,QAClB,M7FwEc,kB6FrEhB,UACE,iB7FJS,K6FKT,M7FfS,eyDCX,UoCkBE,iB7FnBS,K6FoBT,M7F8Dc,kB4F3Dd,qBAEE,M5FzBO,K4FuBT,qBpCfE,WxDiBM,QwDjBa,wCAA4D,mBoCmB1E,kCAAA,kCAI4B,8DADA,8DAH5B,+CAMD,iBAAkB,eANjB,+BAWD,aAAc,QACd,MAAO,QAZN,+BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,gCAAA,gCAgB4B,8DADA,8DAK7B,aAAc,QACd,M5FhDG,K4F2BF,gCAAA,gCAgB4B,8DADA,8DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,uBAEE,M5FzBO,K4FuBT,uBpCfE,WxDFO,QwDEY,wCAA4D,mBoCmB1E,oCAAA,oCAI4B,gEADA,gEAH5B,iDAMD,iBAAkB,eANjB,iCAWD,aAAc,QACd,MAAO,QAZN,iCpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,kCAAA,kCAgB4B,gEADA,gEAK7B,aAAc,QACd,M5FhDG,K4F2BF,kCAAA,kCAgB4B,gEADA,gEpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,qBAEE,M5FzBO,K4FuBT,qBpCfE,WxDwBM,QwDxBa,wCAA4D,mBoCmB1E,kCAAA,kCAI4B,8DADA,8DAH5B,+CAMD,iBAAkB,eANjB,+BAWD,aAAc,QACd,MAAO,QAZN,+BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,gCAAA,gCAgB4B,8DADA,8DAK7B,aAAc,QACd,M5FhDG,K4F2BF,gCAAA,gCAgB4B,8DADA,8DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,kBAEE,M5FzBO,K4FuBT,kBpCfE,WxD0BM,QwD1Ba,wCAA4D,mBoCmB1E,+BAAA,+BAI4B,2DADA,2DAH5B,4CAMD,iBAAkB,eANjB,4BAWD,aAAc,QACd,MAAO,QAZN,4BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,6BAAA,6BAgB4B,2DADA,2DAK7B,aAAc,QACd,M5FhDG,K4F2BF,6BAAA,6BAgB4B,2DADA,2DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,qBAEE,M5FyDY,Q4F3Dd,qBpCfE,WxDuBM,QwDvBa,wCAA4D,mBoCmB1E,kCAAA,kCAI4B,8DADA,8DAH5B,+CAMD,iBAAkB,eANjB,+BAWD,aAAc,QACd,MAAO,QAZN,+BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,gCAAA,gCAgB4B,8DADA,8DAK7B,aAAc,QACd,M5FkCQ,Q4FvDP,gCAAA,gCAgB4B,8DADA,8DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,oBAEE,M5FzBO,K4FuBT,oBpCfE,WxDqBM,QwDrBa,wCAA4D,mBoCmB1E,iCAAA,iCAI4B,6DADA,6DAH5B,8CAMD,iBAAkB,eANjB,8BAWD,aAAc,QACd,MAAO,QAZN,8BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,+BAAA,+BAgB4B,6DADA,6DAK7B,aAAc,QACd,M5FhDG,K4F2BF,+BAAA,+BAgB4B,6DADA,6DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,mBAEE,M5FyDY,Q4F3Dd,mBpCfE,WxDPO,QwDOY,wCAA4D,mBoCmB1E,gCAAA,gCAI4B,4DADA,4DAH5B,6CAMD,iBAAkB,eANjB,6BAWD,aAAc,QACd,MAAO,QAZN,6BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,8BAAA,8BAgB4B,4DADA,4DAK7B,aAAc,QACd,M5FkCQ,Q4FvDP,8BAAA,8BAgB4B,4DADA,4DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,kBAEE,M5FzBO,K4FuBT,kBpCfE,WxDAO,QwDAY,wCAA4D,mBoCmB1E,+BAAA,+BAI4B,2DADA,2DAH5B,4CAMD,iBAAkB,eANjB,4BAWD,aAAc,QACd,MAAO,QAZN,4BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,6BAAA,6BAgB4B,2DADA,2DAK7B,aAAc,QACd,M5FhDG,K4F2BF,6BAAA,6BAgB4B,2DADA,2DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,uBAEE,M5FzBO,K4FuBT,uBpCfE,WsBZQ,QtBYW,wCAA4D,mBoCmB1E,oCAAA,oCAI4B,gEADA,gEAH5B,iDAMD,iBAAkB,eANjB,iCAWD,aAAc,QACd,MAAO,QAZN,iCpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,kCAAA,kCAgB4B,gEADA,gEAK7B,aAAc,QACd,M5FhDG,K4F2BF,kCAAA,kCAgB4B,gEADA,gEpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,kBAEE,M5FzBO,K4FuBT,kBpCfE,WsBXG,QtBWgB,wCAA4D,mBoCmB1E,+BAAA,+BAI4B,2DADA,2DAH5B,4CAMD,iBAAkB,eANjB,4BAWD,aAAc,QACd,MAAO,QAZN,4BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,6BAAA,6BAgB4B,2DADA,2DAK7B,aAAc,KACd,M5FhDG,K4F2BF,6BAAA,6BAgB4B,2DADA,2DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,mBAEE,M5FzBO,K4FuBT,mBpCfE,WsBTI,QtBSe,wCAA4D,mBoCmB1E,gCAAA,gCAI4B,4DADA,4DAH5B,6CAMD,iBAAkB,eANjB,6BAWD,aAAc,QACd,MAAO,QAZN,6BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,8BAAA,8BAgB4B,4DADA,4DAK7B,aAAc,QACd,M5FhDG,K4F2BF,8BAAA,8BAgB4B,4DADA,4DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,kBAEE,M5FyDY,Q4F3Dd,kBpCfE,WsBRG,QtBQgB,wCAA4D,mBoCmB1E,+BAAA,+BAI4B,2DADA,2DAH5B,4CAMD,iBAAkB,eANjB,4BAWD,aAAc,QACd,MAAO,QAZN,4BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,6BAAA,6BAgB4B,2DADA,2DAK7B,aAAc,QACd,M5FhDG,K4F2BF,6BAAA,6BAgB4B,2DADA,2DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,qBAEE,M5FzBO,K4FuBT,qBpCfE,WsBNM,QtBMa,wCAA4D,mBoCmB1E,kCAAA,kCAI4B,8DADA,8DAH5B,+CAMD,iBAAkB,eANjB,+BAWD,aAAc,QACd,MAAO,QAZN,+BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,gCAAA,gCAgB4B,8DADA,8DAK7B,aAAc,QACd,M5FhDG,K4F2BF,gCAAA,gCAgB4B,8DADA,8DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,oBAEE,M5FzBO,K4FuBT,oBpCfE,WsBJK,QtBIc,wCAA4D,mBoCmB1E,iCAAA,iCAI4B,6DADA,6DAH5B,8CAMD,iBAAkB,eANjB,8BAWD,aAAc,QACd,MAAO,QAZN,8BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,+BAAA,+BAgB4B,6DADA,6DAK7B,aAAc,QACd,M5FhDG,K4F2BF,+BAAA,+BAgB4B,6DADA,6DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,kBAEE,M5FzBO,K4FuBT,kBpCfE,WxDiBM,QwDjBa,wCAA4D,mBoCmB1E,+BAAA,+BAI4B,2DADA,2DAH5B,4CAMD,iBAAkB,eANjB,4BAWD,aAAc,QACd,MAAO,QAZN,4BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,6BAAA,6BAgB4B,2DADA,2DAK7B,aAAc,QACd,M5FhDG,K4F2BF,6BAAA,6BAgB4B,2DADA,2DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,oBAEE,M5FzBO,K4FuBT,oBpCfE,WxDkBM,QwDlBa,wCAA4D,mBoCmB1E,iCAAA,iCAI4B,6DADA,6DAH5B,8CAMD,iBAAkB,eANjB,8BAWD,aAAc,QACd,MAAO,QAZN,8BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,+BAAA,+BAgB4B,6DADA,6DAK7B,aAAc,QACd,M5FhDG,K4F2BF,+BAAA,+BAgB4B,6DADA,6DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,oBAEE,M5FzBO,K4FuBT,oBpCfE,WxDmBM,QwDnBa,wCAA4D,mBoCmB1E,iCAAA,iCAI4B,6DADA,6DAH5B,8CAMD,iBAAkB,eANjB,8BAWD,aAAc,QACd,MAAO,QAZN,8BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,+BAAA,+BAgB4B,6DADA,6DAK7B,aAAc,QACd,M5FhDG,K4F2BF,+BAAA,+BAgB4B,6DADA,6DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,kBAEE,M5FzBO,K4FuBT,kBpCfE,WxDoBM,QwDpBa,wCAA4D,mBoCmB1E,+BAAA,+BAI4B,2DADA,2DAH5B,4CAMD,iBAAkB,eANjB,4BAWD,aAAc,QACd,MAAO,QAZN,4BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,6BAAA,6BAgB4B,2DADA,2DAK7B,aAAc,QACd,M5FhDG,K4F2BF,6BAAA,6BAgB4B,2DADA,2DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,iBAEE,M5FzBO,K4FuBT,iBpCfE,WxDqBM,QwDrBa,wCAA4D,mBoCmB1E,8BAAA,8BAI4B,0DADA,0DAH5B,2CAMD,iBAAkB,eANjB,2BAWD,aAAc,QACd,MAAO,QAZN,2BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,4BAAA,4BAgB4B,0DADA,0DAK7B,aAAc,QACd,M5FhDG,K4F2BF,4BAAA,4BAgB4B,0DADA,0DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,oBAEE,M5FyDY,Q4F3Dd,oBpCfE,WxDsBM,QwDtBa,wCAA4D,mBoCmB1E,iCAAA,iCAI4B,6DADA,6DAH5B,8CAMD,iBAAkB,eANjB,8BAWD,aAAc,QACd,MAAO,QAZN,8BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,+BAAA,+BAgB4B,6DADA,6DAK7B,aAAc,QACd,M5FhDG,K4F2BF,+BAAA,+BAgB4B,6DADA,6DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,oBAEE,M5FyDY,Q4F3Dd,oBpCfE,WxDuBM,QwDvBa,wCAA4D,mBoCmB1E,iCAAA,iCAI4B,6DADA,6DAH5B,8CAMD,iBAAkB,eANjB,8BAWD,aAAc,QACd,MAAO,QAZN,8BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,+BAAA,+BAgB4B,6DADA,6DAK7B,aAAc,QACd,M5FkCQ,Q4FvDP,+BAAA,+BAgB4B,6DADA,6DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,mBAEE,M5FzBO,K4FuBT,mBpCfE,WxDwBM,QwDxBa,wCAA4D,mBoCmB1E,gCAAA,gCAI4B,4DADA,4DAH5B,6CAMD,iBAAkB,eANjB,6BAWD,aAAc,QACd,MAAO,QAZN,6BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,8BAAA,8BAgB4B,4DADA,4DAK7B,aAAc,QACd,M5FhDG,K4F2BF,8BAAA,8BAgB4B,4DADA,4DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,kBAEE,M5FzBO,K4FuBT,kBpCfE,WxDyBM,QwDzBa,wCAA4D,mBoCmB1E,+BAAA,+BAI4B,2DADA,2DAH5B,4CAMD,iBAAkB,eANjB,4BAWD,aAAc,QACd,MAAO,QAZN,4BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,6BAAA,6BAgB4B,2DADA,2DAK7B,aAAc,QACd,M5FhDG,K4F2BF,6BAAA,6BAgB4B,2DADA,2DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,kBAEE,M5FzBO,K4FuBT,kBpCfE,WxD0BM,QwD1Ba,wCAA4D,mBoCmB1E,+BAAA,+BAI4B,2DADA,2DAH5B,4CAMD,iBAAkB,eANjB,4BAWD,aAAc,QACd,MAAO,QAZN,4BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,6BAAA,6BAgB4B,2DADA,2DAK7B,aAAc,QACd,M5FhDG,K4F2BF,6BAAA,6BAgB4B,2DADA,2DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,mBAEE,M5FyDY,Q4F3Dd,mBpCfE,WxDRO,KwDQY,kCAA4D,mBoCmB1E,gCAAA,gCAI4B,4DADA,4DAH5B,6CAMD,iBAAkB,eANjB,6BAWD,aAAc,QACd,MAAO,QAZN,6BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,8BAAA,8BAgB4B,4DADA,4DAK7B,aAAc,QACd,M5FkCQ,Q4FvDP,8BAAA,8BAgB4B,4DADA,4DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,kBAEE,M5FzBO,K4FuBT,kBpCfE,WxDFO,QwDEY,wCAA4D,mBoCmB1E,+BAAA,+BAI4B,2DADA,2DAH5B,4CAMD,iBAAkB,eANjB,4BAWD,aAAc,QACd,MAAO,QAZN,4BpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,6BAAA,6BAgB4B,2DADA,2DAK7B,aAAc,QACd,M5FhDG,K4F2BF,6BAAA,6BAgB4B,2DADA,2DpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBoCejF,uBAEE,M5FzBO,K4FuBT,uBpCfE,WxDAO,QwDAY,wCAA4D,mBoCmB1E,oCAAA,oCAI4B,gEADA,gEAH5B,iDAMD,iBAAkB,eANjB,iCAWD,aAAc,QACd,MAAO,QAZN,iCpCnBL,WoC6BsC,QpC7BnB,wCAA4D,mBoCmB1E,kCAAA,kCAgB4B,gEADA,gEAK7B,aAAc,QACd,M5FhDG,K4F2BF,kCAAA,kCAgB4B,gEADA,gEpClCjC,WoCsCsC,QpCtCnB,wCAA4D,mBqC0BrE,sBACZ,QAAS,IAIC,mBACV,M7FfQ,kB6FmBV,YACE,MAAO,QADE,kBAAA,kBAKP,MAAO,QAIX,YACE,M7FhDS,Q6F+CA,kBAAA,kBAKP,MAAO,QCzDP,0BxENA,8GwEQE,M9FsBI,QGxBP,gCAAA,oH2FKK,MATe,QAaL,sCAAA,sCAGV,W9FYE,Q8FXF,M9FdG,K8FkB8C,4EAEjD,W9FKE,Q8FJF,aAAc,QAHmC,2EAQjD,iB7EOI,4M6EDyD,wFACtC,4DAFb,qCADqD,qFAIjE,aAAc,QAId,sCACE,M9FdE,Q8FkBK,6CADA,oCAEP,iB9FnBE,Q8FoBF,a9FpBE,Q8FqBF,M9F9CG,K8FkDM,+CADA,sCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,oHcmED,MhBsCW,Q3EjGlB,0H2F8DS,M9F/DD,KgFPA,qHc+ED,M9FhEC,QGPR,2H2F0ES,M9FlED,Q8FRP,4BxENA,gHwEQE,M9FGK,QGLR,kCAAA,sH2FKK,MATe,QAaL,wCAAA,wCAGV,W9FPG,Q8FQH,M9FdG,K8FkB8C,8EAEjD,W9FdG,Q8FeH,aAAc,QAHmC,6EAQjD,iB7EOI,4M6EDyD,0FACtC,8DAFb,uCADqD,uFAIjE,aAAc,QAId,wCACE,M9FjCG,Q8FqCI,+CADA,sCAEP,iB9FtCG,Q8FuCH,a9FvCG,Q8FwCH,M9F9CG,K8FkDM,iDADA,wCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,sHcmED,MhBsCW,Q3EjGlB,4H2F8DS,M9F/DD,KgFPA,uHc+ED,M9FhEC,QGPR,6H2F0ES,M9FlED,Q8FRP,0BxENA,8GwEQE,M9F6BI,QG/BP,gCAAA,oH2FKK,MATe,QAaL,sCAAA,sCAGV,W9FmBE,Q8FlBF,M9FdG,K8FkB8C,4EAEjD,W9FYE,Q8FXF,aAAc,QAHmC,2EAQjD,iB7EOI,4M6EDyD,wFACtC,4DAFb,qCADqD,qFAIjE,aAAc,QAId,sCACE,M9FPE,Q8FWK,6CADA,oCAEP,iB9FZE,Q8FaF,a9FbE,Q8FcF,M9F9CG,K8FkDM,+CADA,sCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,oHcmED,MhBsCW,Q3EjGlB,0H2F8DS,M9F/DD,KgFPA,qHc+ED,M9FhEC,QGPR,2H2F0ES,M9FlED,Q8FRP,uBxENA,2GwEQE,M9F+BI,QGjCP,6BAAA,iH2FKK,MATe,QAaL,mCAAA,mCAGV,W9FqBE,Q8FpBF,M9FdG,K8FkB8C,yEAEjD,W9FcE,Q8FbF,aAAc,QAHmC,wEAQjD,iB7EOI,4M6EDyD,qFACtC,yDAFb,kCADqD,kFAIjE,aAAc,QAId,mCACE,M9FLE,Q8FSK,0CADA,iCAEP,iB9FVE,Q8FWF,a9FXE,Q8FYF,M9F9CG,K8FkDM,4CADA,mCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,iHcmED,MhBsCW,Q3EjGlB,uH2F8DS,M9F/DD,KgFPA,kHc+ED,M9FhEC,QGPR,wH2F0ES,M9FlED,Q8FRP,0BxENA,8GwEQE,M9F4BI,QG9BP,gCAAA,oH2FKK,MATe,QAaL,sCAAA,sCAGV,W9FkBE,Q8FjBF,M9FoEQ,Q8FhEyC,4EAEjD,W9FWE,Q8FVF,aAAc,QAHmC,2EAQjD,iB7EOI,4M6EDyD,wFACtC,4DAFb,qCADqD,qFAIjE,aAAc,QAId,sCACE,M9FRE,Q8FYK,6CADA,oCAEP,iB9FbE,Q8FcF,a9FdE,Q8FeF,M9F9CG,K8FkDM,+CADA,sCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,oHcmED,MhBsCW,Q3EjGlB,0H2F8DS,M9F/DD,KgFPA,qHc+ED,M9FhEC,QGPR,2H2F0ES,M9FlED,Q8FRP,yBxENA,6GwEQE,M9F0BI,QG5BP,+BAAA,mH2FKK,MATe,QAaL,qCAAA,qCAGV,W9FgBE,Q8FfF,M9FdG,K8FkB8C,2EAEjD,W9FSE,Q8FRF,aAAc,QAHmC,0EAQjD,iB7EOI,4M6EDyD,uFACtC,2DAFb,oCADqD,oFAIjE,aAAc,QAId,qCACE,M9FVE,Q8FcK,4CADA,mCAEP,iB9FfE,Q8FgBF,a9FhBE,Q8FiBF,M9F9CG,K8FkDM,8CADA,qCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,mHcmED,MhBsCW,Q3EjGlB,yH2F8DS,M9F/DD,KgFPA,oHc+ED,M9FhEC,QGPR,0H2F0ES,M9FlED,Q8FRP,wBxENA,4GwEQE,M9FFK,QGAR,8BAAA,kH2FKK,MATe,QAaL,oCAAA,oCAGV,W9FZG,Q8FaH,M9FoEQ,Q8FhEyC,0EAEjD,W9FnBG,Q8FoBH,aAAc,QAHmC,yEAQjD,iB7EOI,4M6EDyD,sFACtC,0DAFb,mCADqD,mFAIjE,aAAc,KAId,oCACE,M9FtCG,Q8F0CI,2CADA,kCAEP,iB9F3CG,Q8F4CH,a9F5CG,Q8F6CH,M9F9CG,K8FkDM,6CADA,oCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,kHcmED,MhBsCW,Q3EjGlB,wH2F8DS,M9F/DD,KgFPA,mHc+ED,M9FhEC,QGPR,yH2F0ES,M9FlED,Q8FRP,uBxENA,2GwEQE,M9FKK,QGPR,6BAAA,iH2FKK,MATe,QAaL,mCAAA,mCAGV,W9FLG,Q8FMH,M9FdG,K8FkB8C,yEAEjD,W9FZG,Q8FaH,aAAc,QAHmC,wEAQjD,iB7EOI,4M6EDyD,qFACtC,yDAFb,kCADqD,kFAIjE,aAAc,QAId,mCACE,M9F/BG,Q8FmCI,0CADA,iCAEP,iB9FpCG,Q8FqCH,a9FrCG,Q8FsCH,M9F9CG,K8FkDM,4CADA,mCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,iHcmED,MhBsCW,Q3EjGlB,uH2F8DS,M9F/DD,KgFPA,kHc+ED,M9FhEC,QGPR,wH2F0ES,M9FlED,Q8FRP,4BxENA,gHwEQE,MhBPM,Q3EKT,kCAAA,sH2FKK,MATe,QAaL,wCAAA,wCAGV,WhBjBI,QgBkBJ,M9FdG,K8FkB8C,8EAEjD,WhBxBI,QgByBJ,aAAc,QAHmC,6EAQjD,iB7EOI,4M6EDyD,0FACtC,8DAFb,uCADqD,uFAIjE,aAAc,QAId,wCACE,MhB3CI,QgB+CG,+CADA,sCAEP,iBhBhDI,QgBiDJ,ahBjDI,QgBkDJ,M9F9CG,K8FkDM,iDADA,wCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,sHcmED,MhBsCW,Q3EjGlB,4H2F8DS,M9F/DD,KgFPA,uHc+ED,M9FhEC,QGPR,6H2F0ES,M9FlED,Q8FRP,uBxENA,2GwEQE,MhBNC,Q3EIJ,6BAAA,iH2FKK,MATe,KAaL,mCAAA,mCAGV,WhBhBD,QgBiBC,M9FdG,K8FkB8C,yEAEjD,WhBvBD,QgBwBC,aAAc,KAHmC,wEAQjD,iB7EOI,4M6EDyD,qFACtC,yDAFb,kCADqD,kFAIjE,aAAc,QAId,mCACE,MhB1CD,QgB8CQ,0CADA,iCAEP,iBhB/CD,QgBgDC,ahBhDD,QgBiDC,M9F9CG,K8FkDM,4CADA,mCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,iHcmED,MhBsCW,Q3EjGlB,uH2F8DS,M9F/DD,KgFPA,kHc+ED,M9FhEC,QGPR,wH2F0ES,M9FlED,Q8FRP,wBxENA,4GwEQE,MhBJE,Q3EEL,8BAAA,kH2FKK,MATe,QAaL,oCAAA,oCAGV,WhBdA,QgBeA,M9FdG,K8FkB8C,0EAEjD,WhBrBA,QgBsBA,aAAc,QAHmC,yEAQjD,iB7EOI,4M6EDyD,sFACtC,0DAFb,mCADqD,mFAIjE,aAAc,QAId,oCACE,MhBxCA,QgB4CO,2CADA,kCAEP,iBhB7CA,QgB8CA,ahB9CA,QgB+CA,M9F9CG,K8FkDM,6CADA,oCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,kHcmED,MhBsCW,Q3EjGlB,wH2F8DS,M9F/DD,KgFPA,mHc+ED,M9FhEC,QGPR,yH2F0ES,M9FlED,Q8FRP,uBxENA,2GwEQE,MhBHC,Q3ECJ,6BAAA,iH2FKK,MATe,QAaL,mCAAA,mCAGV,WhBbD,QgBcC,M9FoEQ,Q8FhEyC,yEAEjD,WhBpBD,QgBqBC,aAAc,QAHmC,wEAQjD,iB7EOI,4M6EDyD,qFACtC,yDAFb,kCADqD,kFAIjE,aAAc,QAId,mCACE,MhBvCD,QgB2CQ,0CADA,iCAEP,iBhB5CD,QgB6CC,ahB7CD,QgB8CC,M9F9CG,K8FkDM,4CADA,mCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,iHcmED,MhBsCW,Q3EjGlB,uH2F8DS,M9F/DD,KgFPA,kHc+ED,M9FhEC,QGPR,wH2F0ES,M9FlED,Q8FRP,0BxENA,8GwEQE,MhBDI,Q3EDP,gCAAA,oH2FKK,MATe,QAaL,sCAAA,sCAGV,WhBXE,QgBYF,M9FdG,K8FkB8C,4EAEjD,WhBlBE,QgBmBF,aAAc,QAHmC,2EAQjD,iB7EOI,4M6EDyD,wFACtC,4DAFb,qCADqD,qFAIjE,aAAc,QAId,sCACE,MhBrCE,QgByCK,6CADA,oCAEP,iBhB1CE,QgB2CF,ahB3CE,QgB4CF,M9F9CG,K8FkDM,+CADA,sCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,oHcmED,MhBsCW,Q3EjGlB,0H2F8DS,M9F/DD,KgFPA,qHc+ED,M9FhEC,QGPR,2H2F0ES,M9FlED,Q8FRP,yBxENA,6GwEQE,MhBCG,Q3EHN,+BAAA,mH2FKK,MATe,QAaL,qCAAA,qCAGV,WhBTC,QgBUD,M9FdG,K8FkB8C,2EAEjD,WhBhBC,QgBiBD,aAAc,QAHmC,0EAQjD,iB7EOI,4M6EDyD,uFACtC,2DAFb,oCADqD,oFAIjE,aAAc,QAId,qCACE,MhBnCC,QgBuCM,4CADA,mCAEP,iBhBxCC,QgByCD,ahBzCC,QgB0CD,M9F9CG,K8FkDM,8CADA,qCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,mHcmED,MhBsCW,Q3EjGlB,yH2F8DS,M9F/DD,KgFPA,oHc+ED,M9FhEC,QGPR,0H2F0ES,M9FlED,Q8FRP,uBxENA,2GwEQE,M9FsBI,QGxBP,6BAAA,iH2FKK,MATe,QAaL,mCAAA,mCAGV,W9FYE,Q8FXF,M9FdG,K8FkB8C,yEAEjD,W9FKE,Q8FJF,aAAc,QAHmC,wEAQjD,iB7EOI,4M6EDyD,qFACtC,yDAFb,kCADqD,kFAIjE,aAAc,QAId,mCACE,M9FdE,Q8FkBK,0CADA,iCAEP,iB9FnBE,Q8FoBF,a9FpBE,Q8FqBF,M9F9CG,K8FkDM,4CADA,mCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,iHcmED,MhBsCW,Q3EjGlB,uH2F8DS,M9F/DD,KgFPA,kHc+ED,M9FhEC,QGPR,wH2F0ES,M9FlED,Q8FRP,yBxENA,6GwEQE,M9FuBI,QGzBP,+BAAA,mH2FKK,MATe,QAaL,qCAAA,qCAGV,W9FaE,Q8FZF,M9FdG,K8FkB8C,2EAEjD,W9FME,Q8FLF,aAAc,QAHmC,0EAQjD,iB7EOI,4M6EDyD,uFACtC,2DAFb,oCADqD,oFAIjE,aAAc,QAId,qCACE,M9FbE,Q8FiBK,4CADA,mCAEP,iB9FlBE,Q8FmBF,a9FnBE,Q8FoBF,M9F9CG,K8FkDM,8CADA,qCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,mHcmED,MhBsCW,Q3EjGlB,yH2F8DS,M9F/DD,KgFPA,oHc+ED,M9FhEC,QGPR,0H2F0ES,M9FlED,Q8FRP,yBxENA,6GwEQE,M9FwBI,QG1BP,+BAAA,mH2FKK,MATe,QAaL,qCAAA,qCAGV,W9FcE,Q8FbF,M9FdG,K8FkB8C,2EAEjD,W9FOE,Q8FNF,aAAc,QAHmC,0EAQjD,iB7EOI,4M6EDyD,uFACtC,2DAFb,oCADqD,oFAIjE,aAAc,QAId,qCACE,M9FZE,Q8FgBK,4CADA,mCAEP,iB9FjBE,Q8FkBF,a9FlBE,Q8FmBF,M9F9CG,K8FkDM,8CADA,qCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,mHcmED,MhBsCW,Q3EjGlB,yH2F8DS,M9F/DD,KgFPA,oHc+ED,M9FhEC,QGPR,0H2F0ES,M9FlED,Q8FRP,uBxENA,2GwEQE,M9FyBI,QG3BP,6BAAA,iH2FKK,MATe,QAaL,mCAAA,mCAGV,W9FeE,Q8FdF,M9FdG,K8FkB8C,yEAEjD,W9FQE,Q8FPF,aAAc,QAHmC,wEAQjD,iB7EOI,4M6EDyD,qFACtC,yDAFb,kCADqD,kFAIjE,aAAc,QAId,mCACE,M9FXE,Q8FeK,0CADA,iCAEP,iB9FhBE,Q8FiBF,a9FjBE,Q8FkBF,M9F9CG,K8FkDM,4CADA,mCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,iHcmED,MhBsCW,Q3EjGlB,uH2F8DS,M9F/DD,KgFPA,kHc+ED,M9FhEC,QGPR,wH2F0ES,M9FlED,Q8FRP,sBxENA,0GwEQE,M9F0BI,QG5BP,4BAAA,gH2FKK,MATe,QAaL,kCAAA,kCAGV,W9FgBE,Q8FfF,M9FdG,K8FkB8C,wEAEjD,W9FSE,Q8FRF,aAAc,QAHmC,uEAQjD,iB7EOI,4M6EDyD,oFACtC,wDAFb,iCADqD,iFAIjE,aAAc,QAId,kCACE,M9FVE,Q8FcK,yCADA,gCAEP,iB9FfE,Q8FgBF,a9FhBE,Q8FiBF,M9F9CG,K8FkDM,2CADA,kCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,gHcmED,MhBsCW,Q3EjGlB,sH2F8DS,M9F/DD,KgFPA,iHc+ED,M9FhEC,QGPR,uH2F0ES,M9FlED,Q8FRP,yBxENA,6GwEQE,M9F2BI,QG7BP,+BAAA,mH2FKK,MATe,QAaL,qCAAA,qCAGV,W9FiBE,Q8FhBF,M9FoEQ,Q8FhEyC,2EAEjD,W9FUE,Q8FTF,aAAc,QAHmC,0EAQjD,iB7EOI,4M6EDyD,uFACtC,2DAFb,oCADqD,oFAIjE,aAAc,QAId,qCACE,M9FTE,Q8FaK,4CADA,mCAEP,iB9FdE,Q8FeF,a9FfE,Q8FgBF,M9F9CG,K8FkDM,8CADA,qCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,mHcmED,MhBsCW,Q3EjGlB,yH2F8DS,M9F/DD,KgFPA,oHc+ED,M9FhEC,QGPR,0H2F0ES,M9FlED,Q8FRP,yBxENA,6GwEQE,M9F4BI,QG9BP,+BAAA,mH2FKK,MATe,QAaL,qCAAA,qCAGV,W9FkBE,Q8FjBF,M9FoEQ,Q8FhEyC,2EAEjD,W9FWE,Q8FVF,aAAc,QAHmC,0EAQjD,iB7EOI,4M6EDyD,uFACtC,2DAFb,oCADqD,oFAIjE,aAAc,QAId,qCACE,M9FRE,Q8FYK,4CADA,mCAEP,iB9FbE,Q8FcF,a9FdE,Q8FeF,M9F9CG,K8FkDM,8CADA,qCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,mHcmED,MhBsCW,Q3EjGlB,yH2F8DS,M9F/DD,KgFPA,oHc+ED,M9FhEC,QGPR,0H2F0ES,M9FlED,Q8FRP,wBxENA,4GwEQE,M9F6BI,QG/BP,8BAAA,kH2FKK,MATe,QAaL,oCAAA,oCAGV,W9FmBE,Q8FlBF,M9FdG,K8FkB8C,0EAEjD,W9FYE,Q8FXF,aAAc,QAHmC,yEAQjD,iB7EOI,4M6EDyD,sFACtC,0DAFb,mCADqD,mFAIjE,aAAc,QAId,oCACE,M9FPE,Q8FWK,2CADA,kCAEP,iB9FZE,Q8FaF,a9FbE,Q8FcF,M9F9CG,K8FkDM,6CADA,oCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,kHcmED,MhBsCW,Q3EjGlB,wH2F8DS,M9F/DD,KgFPA,mHc+ED,M9FhEC,QGPR,yH2F0ES,M9FlED,Q8FRP,uBxENA,2GwEQE,M9F8BI,QGhCP,6BAAA,iH2FKK,MATe,QAaL,mCAAA,mCAGV,W9FoBE,Q8FnBF,M9FdG,K8FkB8C,yEAEjD,W9FaE,Q8FZF,aAAc,QAHmC,wEAQjD,iB7EOI,4M6EDyD,qFACtC,yDAFb,kCADqD,kFAIjE,aAAc,QAId,mCACE,M9FNE,Q8FUK,0CADA,iCAEP,iB9FXE,Q8FYF,a9FZE,Q8FaF,M9F9CG,K8FkDM,4CADA,mCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,iHcmED,MhBsCW,Q3EjGlB,uH2F8DS,M9F/DD,KgFPA,kHc+ED,M9FhEC,QGPR,wH2F0ES,M9FlED,Q8FRP,uBxENA,2GwEQE,M9F+BI,QGjCP,6BAAA,iH2FKK,MATe,QAaL,mCAAA,mCAGV,W9FqBE,Q8FpBF,M9FdG,K8FkB8C,yEAEjD,W9FcE,Q8FbF,aAAc,QAHmC,wEAQjD,iB7EOI,4M6EDyD,qFACtC,yDAFb,kCADqD,kFAIjE,aAAc,QAId,mCACE,M9FLE,Q8FSK,0CADA,iCAEP,iB9FVE,Q8FWF,a9FXE,Q8FYF,M9F9CG,K8FkDM,4CADA,mCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,iHcmED,MhBsCW,Q3EjGlB,uH2F8DS,M9F/DD,KgFPA,kHc+ED,M9FhEC,QGPR,wH2F0ES,M9FlED,Q8FRP,wBxENA,4GwEQE,M9FHK,KGCR,8BAAA,kH2FKK,MATe,QAaL,oCAAA,oCAGV,W9FbG,K8FcH,M9FoEQ,Q8FhEyC,0EAEjD,W9FpBG,K8FqBH,aAAc,KAHmC,yEAQjD,iB7EOI,4M6EDyD,sFACtC,0DAFb,mCADqD,mFAIjE,aAAc,KAId,oCACE,M9FvCG,K8F2CI,2CADA,kCAEP,iB9F5CG,K8F6CH,a9F7CG,K8F8CH,M9F9CG,K8FkDM,6CADA,oCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,kHcmED,MhBsCW,Q3EjGlB,wH2F8DS,M9F/DD,KgFPA,mHc+ED,M9FhEC,QGPR,yH2F0ES,M9FlED,Q8FRP,uBxENA,2GwEQE,M9FGK,QGLR,6BAAA,iH2FKK,MATe,QAaL,mCAAA,mCAGV,W9FPG,Q8FQH,M9FdG,K8FkB8C,yEAEjD,W9FdG,Q8FeH,aAAc,QAHmC,wEAQjD,iB7EOI,4M6EDyD,qFACtC,yDAFb,kCADqD,kFAIjE,aAAc,QAId,mCACE,M9FjCG,Q8FqCI,0CADA,iCAEP,iB9FtCG,Q8FuCH,a9FvCG,Q8FwCH,M9F9CG,K8FkDM,4CADA,mCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,iHcmED,MhBsCW,Q3EjGlB,uH2F8DS,M9F/DD,KgFPA,kHc+ED,M9FhEC,QGPR,wH2F0ES,M9FlED,Q8FRP,4BxENA,gHwEQE,M9FKK,QGPR,kCAAA,sH2FKK,MATe,QAaL,wCAAA,wCAGV,W9FLG,Q8FMH,M9FdG,K8FkB8C,8EAEjD,W9FZG,Q8FaH,aAAc,QAHmC,6EAQjD,iB7EOI,4M6EDyD,0FACtC,8DAFb,uCADqD,uFAIjE,aAAc,QAId,wCACE,M9F/BG,Q8FmCI,+CADA,sCAEP,iB9FpCG,Q8FqCH,a9FrCG,Q8FsCH,M9F9CG,K8FkDM,iDADA,wCAET,iB9FnDG,K8FoDH,a9FjDG,Q8FkDH,M9F/CG,QgFbA,sHcmED,MhBsCW,Q3EjGlB,4H2F8DS,M9F/DD,KgFPA,uHc+ED,M9FhEC,QGPR,6H2F0ES,M9FlED,Q6FkEN,+BACC,M7F5EK,K6F2EN,iCACC,M7F5EK,K6F2EN,+BACC,M7F5EK,K6F2EN,4BACC,M7F5EK,K6F2EN,+BACC,M7FMU,Q6FPX,8BACC,M7F5EK,K6F2EN,6BACC,M7FMU,Q6FPX,4BACC,M7F5EK", "sourcesContent": ["/*!\n *   AdminLTE v3.0.5\n *     Only Core\n *   Author: Colorlib\n *   Website: AdminLTE.io <http://adminlte.io>\n *   License: Open source - MIT <http://opensource.org/licenses/MIT>\n */\n// Bootstrap\n// ---------------------------------------------------\n@import '~bootstrap/scss/functions';\n@import 'bootstrap-variables';\n@import '~bootstrap/scss/bootstrap';\n\n// Variables and Mixins\n// ---------------------------------------------------\n@import 'variables';\n@import 'mixins';\n\n@import 'parts/core';\n@import 'parts/miscellaneous';\n", "/*!\n * Bootstrap v4.4.1 (https://getbootstrap.com/)\n * Copyright 2011-2019 The Bootstrap Authors\n * Copyright 2011-2019 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n */\n\n@import \"functions\";\n@import \"variables\";\n@import \"mixins\";\n@import \"root\";\n@import \"reboot\";\n@import \"type\";\n@import \"images\";\n@import \"code\";\n@import \"grid\";\n@import \"tables\";\n@import \"forms\";\n@import \"buttons\";\n@import \"transitions\";\n@import \"dropdown\";\n@import \"button-group\";\n@import \"input-group\";\n@import \"custom-forms\";\n@import \"nav\";\n@import \"navbar\";\n@import \"card\";\n@import \"breadcrumb\";\n@import \"pagination\";\n@import \"badge\";\n@import \"jumbotron\";\n@import \"alert\";\n@import \"progress\";\n@import \"media\";\n@import \"list-group\";\n@import \"close\";\n@import \"toasts\";\n@import \"modal\";\n@import \"tooltip\";\n@import \"popover\";\n@import \"carousel\";\n@import \"spinners\";\n@import \"utilities\";\n@import \"print\";\n", "// Do not forget to update getting-started/theming.md!\n:root {\n  // Custom variable values only support SassScript inside `#{}`.\n  @each $color, $value in $colors {\n    --#{$color}: #{$value};\n  }\n\n  @each $color, $value in $theme-colors {\n    --#{$color}: #{$value};\n  }\n\n  @each $bp, $value in $grid-breakpoints {\n    --breakpoint-#{$bp}: #{$value};\n  }\n\n  // Use `inspect` for lists so that quoted items keep the quotes.\n  // See https://github.com/sass/sass/issues/2383#issuecomment-336349172\n  --font-family-sans-serif: #{inspect($font-family-sans-serif)};\n  --font-family-monospace: #{inspect($font-family-monospace)};\n}\n", "// stylelint-disable at-rule-no-vendor-prefix, declaration-no-important, selector-no-qualifying-type, property-no-vendor-prefix\n\n// Reboot\n//\n// Normalization of HTML elements, manually forked from Normalize.css to remove\n// styles targeting irrelevant browsers while applying new styles.\n//\n// Normalize is licensed MIT. https://github.com/necolas/normalize.css\n\n\n// Document\n//\n// 1. Change from `box-sizing: content-box` so that `width` is not affected by `padding` or `border`.\n// 2. Change the default font family in all browsers.\n// 3. Correct the line height in all browsers.\n// 4. Prevent adjustments of font size after orientation changes in IE on Windows Phone and in iOS.\n// 5. Change the default tap highlight to be completely transparent in iOS.\n\n*,\n*::before,\n*::after {\n  box-sizing: border-box; // 1\n}\n\nhtml {\n  font-family: sans-serif; // 2\n  line-height: 1.15; // 3\n  -webkit-text-size-adjust: 100%; // 4\n  -webkit-tap-highlight-color: rgba($black, 0); // 5\n}\n\n// Shim for \"new\" HTML5 structural elements to display correctly (IE10, older browsers)\n// TODO: remove in v5\n// stylelint-disable-next-line selector-list-comma-newline-after\narticle, aside, figcaption, figure, footer, header, hgroup, main, nav, section {\n  display: block;\n}\n\n// Body\n//\n// 1. Remove the margin in all browsers.\n// 2. As a best practice, apply a default `background-color`.\n// 3. Set an explicit initial text-align value so that we can later use\n//    the `inherit` value on things like `<th>` elements.\n\nbody {\n  margin: 0; // 1\n  font-family: $font-family-base;\n  @include font-size($font-size-base);\n  font-weight: $font-weight-base;\n  line-height: $line-height-base;\n  color: $body-color;\n  text-align: left; // 3\n  background-color: $body-bg; // 2\n}\n\n// Future-proof rule: in browsers that support :focus-visible, suppress the focus outline\n// on elements that programmatically receive focus but wouldn't normally show a visible\n// focus outline. In general, this would mean that the outline is only applied if the\n// interaction that led to the element receiving programmatic focus was a keyboard interaction,\n// or the browser has somehow determined that the user is primarily a keyboard user and/or\n// wants focus outlines to always be presented.\n//\n// See https://developer.mozilla.org/en-US/docs/Web/CSS/:focus-visible\n// and https://developer.paciellogroup.com/blog/2018/03/focus-visible-and-backwards-compatibility/\n[tabindex=\"-1\"]:focus:not(:focus-visible) {\n  outline: 0 !important;\n}\n\n\n// Content grouping\n//\n// 1. Add the correct box sizing in Firefox.\n// 2. Show the overflow in Edge and IE.\n\nhr {\n  box-sizing: content-box; // 1\n  height: 0; // 1\n  overflow: visible; // 2\n}\n\n\n//\n// Typography\n//\n\n// Remove top margins from headings\n//\n// By default, `<h1>`-`<h6>` all receive top and bottom margins. We nuke the top\n// margin for easier control within type scales as it avoids margin collapsing.\n// stylelint-disable-next-line selector-list-comma-newline-after\nh1, h2, h3, h4, h5, h6 {\n  margin-top: 0;\n  margin-bottom: $headings-margin-bottom;\n}\n\n// Reset margins on paragraphs\n//\n// Similarly, the top margin on `<p>`s get reset. However, we also reset the\n// bottom margin to use `rem` units instead of `em`.\np {\n  margin-top: 0;\n  margin-bottom: $paragraph-margin-bottom;\n}\n\n// Abbreviations\n//\n// 1. Duplicate behavior to the data-* attribute for our tooltip plugin\n// 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.\n// 3. Add explicit cursor to indicate changed behavior.\n// 4. Remove the bottom border in Firefox 39-.\n// 5. Prevent the text-decoration to be skipped.\n\nabbr[title],\nabbr[data-original-title] { // 1\n  text-decoration: underline; // 2\n  text-decoration: underline dotted; // 2\n  cursor: help; // 3\n  border-bottom: 0; // 4\n  text-decoration-skip-ink: none; // 5\n}\n\naddress {\n  margin-bottom: 1rem;\n  font-style: normal;\n  line-height: inherit;\n}\n\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0;\n}\n\ndt {\n  font-weight: $dt-font-weight;\n}\n\ndd {\n  margin-bottom: .5rem;\n  margin-left: 0; // Undo browser default\n}\n\nblockquote {\n  margin: 0 0 1rem;\n}\n\nb,\nstrong {\n  font-weight: $font-weight-bolder; // Add the correct font weight in Chrome, Edge, and Safari\n}\n\nsmall {\n  @include font-size(80%); // Add the correct font size in all browsers\n}\n\n//\n// Prevent `sub` and `sup` elements from affecting the line height in\n// all browsers.\n//\n\nsub,\nsup {\n  position: relative;\n  @include font-size(75%);\n  line-height: 0;\n  vertical-align: baseline;\n}\n\nsub { bottom: -.25em; }\nsup { top: -.5em; }\n\n\n//\n// Links\n//\n\na {\n  color: $link-color;\n  text-decoration: $link-decoration;\n  background-color: transparent; // Remove the gray background on active links in IE 10.\n\n  @include hover() {\n    color: $link-hover-color;\n    text-decoration: $link-hover-decoration;\n  }\n}\n\n// And undo these styles for placeholder links/named anchors (without href).\n// It would be more straightforward to just use a[href] in previous block, but that\n// causes specificity issues in many other styles that are too complex to fix.\n// See https://github.com/twbs/bootstrap/issues/19402\n\na:not([href]) {\n  color: inherit;\n  text-decoration: none;\n\n  @include hover() {\n    color: inherit;\n    text-decoration: none;\n  }\n}\n\n\n//\n// Code\n//\n\npre,\ncode,\nkbd,\nsamp {\n  font-family: $font-family-monospace;\n  @include font-size(1em); // Correct the odd `em` font sizing in all browsers.\n}\n\npre {\n  // Remove browser default top margin\n  margin-top: 0;\n  // Reset browser default of `1em` to use `rem`s\n  margin-bottom: 1rem;\n  // Don't allow content to break outside\n  overflow: auto;\n}\n\n\n//\n// Figures\n//\n\nfigure {\n  // Apply a consistent margin strategy (matches our type styles).\n  margin: 0 0 1rem;\n}\n\n\n//\n// Images and content\n//\n\nimg {\n  vertical-align: middle;\n  border-style: none; // Remove the border on images inside links in IE 10-.\n}\n\nsvg {\n  // Workaround for the SVG overflow bug in IE10/11 is still required.\n  // See https://github.com/twbs/bootstrap/issues/26878\n  overflow: hidden;\n  vertical-align: middle;\n}\n\n\n//\n// Tables\n//\n\ntable {\n  border-collapse: collapse; // Prevent double borders\n}\n\ncaption {\n  padding-top: $table-cell-padding;\n  padding-bottom: $table-cell-padding;\n  color: $table-caption-color;\n  text-align: left;\n  caption-side: bottom;\n}\n\nth {\n  // Matches default `<td>` alignment by inheriting from the `<body>`, or the\n  // closest parent with a set `text-align`.\n  text-align: inherit;\n}\n\n\n//\n// Forms\n//\n\nlabel {\n  // Allow labels to use `margin` for spacing.\n  display: inline-block;\n  margin-bottom: $label-margin-bottom;\n}\n\n// Remove the default `border-radius` that macOS Chrome adds.\n//\n// Details at https://github.com/twbs/bootstrap/issues/24093\nbutton {\n  // stylelint-disable-next-line property-blacklist\n  border-radius: 0;\n}\n\n// Work around a Firefox/IE bug where the transparent `button` background\n// results in a loss of the default `button` focus styles.\n//\n// Credit: https://github.com/suitcss/base/\nbutton:focus {\n  outline: 1px dotted;\n  outline: 5px auto -webkit-focus-ring-color;\n}\n\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0; // Remove the margin in Firefox and Safari\n  font-family: inherit;\n  @include font-size(inherit);\n  line-height: inherit;\n}\n\nbutton,\ninput {\n  overflow: visible; // Show the overflow in Edge\n}\n\nbutton,\nselect {\n  text-transform: none; // Remove the inheritance of text transform in Firefox\n}\n\n// Remove the inheritance of word-wrap in Safari.\n//\n// Details at https://github.com/twbs/bootstrap/issues/24990\nselect {\n  word-wrap: normal;\n}\n\n\n// 1. Prevent a WebKit bug where (2) destroys native `audio` and `video`\n//    controls in Android 4.\n// 2. Correct the inability to style clickable types in iOS and Safari.\nbutton,\n[type=\"button\"], // 1\n[type=\"reset\"],\n[type=\"submit\"] {\n  -webkit-appearance: button; // 2\n}\n\n// Opinionated: add \"hand\" cursor to non-disabled button elements.\n@if $enable-pointer-cursor-for-buttons {\n  button,\n  [type=\"button\"],\n  [type=\"reset\"],\n  [type=\"submit\"] {\n    &:not(:disabled) {\n      cursor: pointer;\n    }\n  }\n}\n\n// Remove inner border and padding from Firefox, but don't restore the outline like Normalize.\nbutton::-moz-focus-inner,\n[type=\"button\"]::-moz-focus-inner,\n[type=\"reset\"]::-moz-focus-inner,\n[type=\"submit\"]::-moz-focus-inner {\n  padding: 0;\n  border-style: none;\n}\n\ninput[type=\"radio\"],\ninput[type=\"checkbox\"] {\n  box-sizing: border-box; // 1. Add the correct box sizing in IE 10-\n  padding: 0; // 2. Remove the padding in IE 10-\n}\n\n\ninput[type=\"date\"],\ninput[type=\"time\"],\ninput[type=\"datetime-local\"],\ninput[type=\"month\"] {\n  // Remove the default appearance of temporal inputs to avoid a Mobile Safari\n  // bug where setting a custom line-height prevents text from being vertically\n  // centered within the input.\n  // See https://bugs.webkit.org/show_bug.cgi?id=139848\n  // and https://github.com/twbs/bootstrap/issues/11266\n  -webkit-appearance: listbox;\n}\n\ntextarea {\n  overflow: auto; // Remove the default vertical scrollbar in IE.\n  // Textareas should really only resize vertically so they don't break their (horizontal) containers.\n  resize: vertical;\n}\n\nfieldset {\n  // Browsers set a default `min-width: min-content;` on fieldsets,\n  // unlike e.g. `<div>`s, which have `min-width: 0;` by default.\n  // So we reset that to ensure fieldsets behave more like a standard block element.\n  // See https://github.com/twbs/bootstrap/issues/12359\n  // and https://html.spec.whatwg.org/multipage/#the-fieldset-and-legend-elements\n  min-width: 0;\n  // Reset the default outline behavior of fieldsets so they don't affect page layout.\n  padding: 0;\n  margin: 0;\n  border: 0;\n}\n\n// 1. Correct the text wrapping in Edge and IE.\n// 2. Correct the color inheritance from `fieldset` elements in IE.\nlegend {\n  display: block;\n  width: 100%;\n  max-width: 100%; // 1\n  padding: 0;\n  margin-bottom: .5rem;\n  @include font-size(1.5rem);\n  line-height: inherit;\n  color: inherit; // 2\n  white-space: normal; // 1\n}\n\nprogress {\n  vertical-align: baseline; // Add the correct vertical alignment in Chrome, Firefox, and Opera.\n}\n\n// Correct the cursor style of increment and decrement buttons in Chrome.\n[type=\"number\"]::-webkit-inner-spin-button,\n[type=\"number\"]::-webkit-outer-spin-button {\n  height: auto;\n}\n\n[type=\"search\"] {\n  // This overrides the extra rounded corners on search inputs in iOS so that our\n  // `.form-control` class can properly style them. Note that this cannot simply\n  // be added to `.form-control` as it's not specific enough. For details, see\n  // https://github.com/twbs/bootstrap/issues/11586.\n  outline-offset: -2px; // 2. Correct the outline style in Safari.\n  -webkit-appearance: none;\n}\n\n//\n// Remove the inner padding in Chrome and Safari on macOS.\n//\n\n[type=\"search\"]::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n//\n// 1. Correct the inability to style clickable types in iOS and Safari.\n// 2. Change font properties to `inherit` in Safari.\n//\n\n::-webkit-file-upload-button {\n  font: inherit; // 2\n  -webkit-appearance: button; // 1\n}\n\n//\n// Correct element displays\n//\n\noutput {\n  display: inline-block;\n}\n\nsummary {\n  display: list-item; // Add the correct display in all browsers\n  cursor: pointer;\n}\n\ntemplate {\n  display: none; // Add the correct display in IE\n}\n\n// Always hide an element with the `hidden` HTML attribute (from PureCSS).\n// Needed for proper display in IE 10-.\n[hidden] {\n  display: none !important;\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n\n//\n// Color system\n//\n\n// stylelint-disable\n$white:    #ffffff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n\n$grays: () !default;\n$grays: map-merge((\n        \"100\": $gray-100,\n        \"200\": $gray-200,\n        \"300\": $gray-300,\n        \"400\": $gray-400,\n        \"500\": $gray-500,\n        \"600\": $gray-600,\n        \"700\": $gray-700,\n        \"800\": $gray-800,\n        \"900\": $gray-900\n), $grays);\n\n$blue:    #007bff !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #e83e8c !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #28a745 !default;\n$teal:    #20c997 !default;\n$cyan:    #17a2b8 !default;\n\n$colors: () !default;\n$colors: map-merge((\n        \"blue\":       $blue,\n        \"indigo\":     $indigo,\n        \"purple\":     $purple,\n        \"pink\":       $pink,\n        \"red\":        $red,\n        \"orange\":     $orange,\n        \"yellow\":     $yellow,\n        \"green\":      $green,\n        \"teal\":       $teal,\n        \"cyan\":       $cyan,\n        \"white\":      $white,\n        \"gray\":       $gray-600,\n        \"gray-dark\":  $gray-800\n), $colors);\n\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-800 !default;\n\n$theme-colors: () !default;\n$theme-colors: map-merge((\n        \"primary\":    $primary,\n        \"secondary\":  $secondary,\n        \"success\":    $success,\n        \"info\":       $info,\n        \"warning\":    $warning,\n        \"danger\":     $danger,\n        \"light\":      $light,\n        \"dark\":       $dark\n), $theme-colors);\n// stylelint-enable\n\n// Set a specific jump point for requesting color jumps\n$theme-color-interval:      8% !default;\n\n// The yiq lightness value that determines when the lightness of color changes from \"dark\" to \"light\". Acceptable values are between 0 and 255.\n$yiq-contrasted-threshold: 150 !default;\n\n// Customize the light and dark text colors for use in our YIQ color contrast function.\n$yiq-text-dark: #1F2D3D !default;\n$yiq-text-light: $white !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                                true !default;\n$enable-rounded:                              true !default;\n$enable-shadows:                              true !default;\n$enable-gradients:                            false !default;\n$enable-transitions:                          true !default;\n$enable-prefers-reduced-motion-media-query:   true !default;\n$enable-hover-media-query:                    false !default; // Deprecated, no longer affects any compiled CSS\n$enable-grid-classes:                         true !default;\n$enable-pointer-cursor-for-buttons:           true !default;\n$enable-print-styles:                         true !default;\n$enable-responsive-font-sizes:                false !default;\n$enable-validation-icons:                     true !default;\n$enable-deprecation-messages:                 true !default;\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// stylelint-disable\n$spacer: 1rem !default;\n$spacers: () !default;\n$spacers: map-merge((\n        0: 0,\n        1: ($spacer * .25),\n        2: ($spacer * .5),\n        3: $spacer,\n        4: ($spacer * 1.5),\n        5: ($spacer * 3)\n), $spacers);\n\n// This variable affects the `.h-*` and `.w-*` classes.\n$sizes: () !default;\n$sizes: map-merge((\n        25: 25%,\n        50: 50%,\n        75: 75%,\n        100: 100%\n), $sizes);\n// stylelint-enable\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white !default;\n$body-color:                $gray-900 !default;\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                theme-color(\"primary\") !default;\n$link-decoration:           none !default;\n$link-hover-color:          darken($link-color, 15%) !default;\n$link-hover-decoration:     none !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n$grid-breakpoints: (\n        xs: 0,\n        sm: 576px,\n        md: 768px,\n        lg: 992px,\n        xl: 1200px\n) !default;\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints);\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n$container-max-widths: (\n        sm: 540px,\n        md: 720px,\n        lg: 960px,\n        xl: 1140px\n) !default;\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           15px !default;\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n$line-height-lg:              1.5 !default;\n$line-height-sm:              1.5 !default;\n\n$border-width:                1px !default;\n$border-color:                $gray-300 !default;\n\n$border-radius:               .25rem !default;\n$border-radius-lg:            .3rem !default;\n$border-radius-sm:            .2rem !default;\n\n$component-active-color:      $white !default;\n$component-active-bg:         theme-color(\"primary\") !default;\n\n$caret-width:                 .3em !default;\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n$transition-collapse:         height .35s ease !default;\n\n\n// Fonts\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      \"Source Sans Pro\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n$font-family-base:            $font-family-sans-serif !default;\n// stylelint-enable value-keyword-case\n\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-lg:                ($font-size-base * 1.25) !default;\n$font-size-sm:                ($font-size-base * .875) !default;\n\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-bold:            700 !default;\n\n$font-weight-base:            $font-weight-normal !default;\n$line-height-base:            1.5 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n\n$headings-margin-bottom:      ($spacer / 2) !default;\n$headings-font-family:        inherit !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              inherit !default;\n\n$display1-size:               6rem !default;\n$display2-size:               5.5rem !default;\n$display3-size:               4.5rem !default;\n$display4-size:               3.5rem !default;\n\n$display1-weight:             300 !default;\n$display2-weight:             300 !default;\n$display3-weight:             300 !default;\n$display4-weight:             300 !default;\n$display-line-height:         $headings-line-height !default;\n\n$lead-font-size:              ($font-size-base * 1.25) !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             80% !default;\n\n$text-muted:                  $gray-600 !default;\n\n$blockquote-small-color:      $gray-600 !default;\n$blockquote-font-size:        ($font-size-base * 1.25) !default;\n\n$hr-border-color:             rgba($black, .1) !default;\n$hr-border-width:             $border-width !default;\n\n$mark-padding:                .2em !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$kbd-box-shadow:              inset 0 -.1rem 0 rgba($black, .25) !default;\n$nested-kbd-font-weight:      $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-bg:                     #fcf8e3 !default;\n\n$hr-margin-y:                 $spacer !default;\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n$table-cell-padding:          .75rem !default;\n$table-cell-padding-sm:       .3rem !default;\n\n$table-bg:                    transparent !default;\n$table-accent-bg:             rgba($black, .05) !default;\n$table-hover-bg:              rgba($black, .075) !default;\n$table-active-bg:             $table-hover-bg !default;\n\n$table-border-width:          $border-width !default;\n$table-border-color:          $gray-300 !default;\n\n$table-head-bg:               $gray-200 !default;\n$table-head-color:            $gray-700 !default;\n\n$table-dark-bg:               $gray-900 !default;\n$table-dark-accent-bg:        rgba($white, .05) !default;\n$table-dark-hover-bg:         rgba($white, .075) !default;\n$table-dark-border-color:     lighten($gray-900, 10%) !default;\n$table-dark-color:            $body-bg !default;\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:       .2rem !default;\n$input-btn-focus-color:       rgba($component-active-bg, .25) !default;\n$input-btn-focus-box-shadow:  0 0 0 $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-line-height-sm:    $line-height-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-line-height-lg:    $line-height-lg !default;\n\n$input-btn-border-width:      $border-width !default;\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-line-height:             $input-btn-line-height !default;\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-line-height-sm:          $input-btn-line-height-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-line-height-lg:          $input-btn-line-height-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              none !default;\n$btn-focus-width:             0 !default;\n$btn-focus-box-shadow:        none !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       none !default;\n\n$btn-link-disabled-color:     $gray-600 !default;\n\n$btn-block-spacing-y:         .5rem !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n\n// Forms\n\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-line-height-sm:                  $input-btn-line-height-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-line-height-lg:                  $input-btn-line-height-lg !default;\n\n$input-bg:                              $white !default;\n$input-disabled-bg:                     $gray-200 !default;\n\n$input-color:                           $gray-700 !default;\n$input-border-color:                    $gray-400 !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      inset 0 0 0 rgba($black, 0) !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              lighten($component-active-bg, 25%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     0 !default;\n$input-focus-box-shadow:                none !default;\n\n$input-placeholder-color:               lighten($gray-600, 15%) !default;\n\n$input-height-border:                   $input-border-width * 2 !default;\n\n$input-height-inner:                    ($font-size-base * $input-btn-line-height) + ($input-btn-padding-y * 2) !default;\n$input-height-inner-half:               calc(#{$input-line-height * .5em} + #{$input-padding-y}) !default;\n$input-height-inner-quarter:            calc(#{$input-line-height * .25em} + #{$input-padding-y / 2}) !default;\n\n$input-height:                          calc(#{$input-height-inner} + #{$input-height-border}) !default;\n\n$input-height-inner-sm:                 ($font-size-sm * $input-btn-line-height-sm) + ($input-btn-padding-y-sm * 2) !default;\n$input-height-sm:                       calc(#{$input-height-inner-sm} + #{$input-height-border}) !default;\n\n$input-height-inner-lg:                 ($font-size-lg * $input-btn-line-height-lg) + ($input-btn-padding-y-lg * 2) !default;\n$input-height-lg:                       calc(#{$input-height-inner-lg} + #{$input-height-border}) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-text-margin-top:                  .25rem !default;\n\n$form-check-input-gutter:               1.25rem !default;\n$form-check-input-margin-y:             .3rem !default;\n$form-check-input-margin-x:             .25rem !default;\n\n$form-check-inline-margin-x:            .75rem !default;\n$form-check-inline-input-margin-x:      .3125rem !default;\n\n$form-group-margin-bottom:              1rem !default;\n\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  $gray-200 !default;\n$input-group-addon-border-color:        $input-border-color !default;\n\n$custom-control-gutter:                 .5rem !default;\n$custom-control-spacer-x:               1rem !default;\n\n$custom-control-indicator-size:         1rem !default;\n$custom-control-indicator-bg:           $gray-300 !default;\n$custom-control-indicator-bg-size:      50% 50% !default;\n$custom-control-indicator-box-shadow:   inset 0 .25rem .25rem rgba($black, .1) !default;\n\n$custom-control-indicator-disabled-bg:          $gray-200 !default;\n$custom-control-label-disabled-color:           $gray-600 !default;\n\n$custom-control-indicator-checked-color:        $component-active-color !default;\n$custom-control-indicator-checked-bg:           $component-active-bg !default;\n$custom-control-indicator-checked-disabled-bg:  rgba(theme-color(\"primary\"), .5) !default;\n$custom-control-indicator-checked-box-shadow:   none !default;\n\n$custom-control-indicator-focus-box-shadow:     0 0 0 1px $body-bg, $input-btn-focus-box-shadow !default;\n\n$custom-control-indicator-active-color:         $component-active-color !default;\n$custom-control-indicator-active-bg:            lighten($component-active-bg, 35%) !default;\n$custom-control-indicator-active-box-shadow:    none !default;\n\n$custom-checkbox-indicator-border-radius:       $border-radius !default;\n$custom-checkbox-indicator-icon-checked:        str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$custom-checkbox-indicator-indeterminate-bg:    $component-active-bg !default;\n$custom-checkbox-indicator-indeterminate-color: $custom-control-indicator-checked-color !default;\n$custom-checkbox-indicator-icon-indeterminate:  str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='#{$custom-checkbox-indicator-indeterminate-color}' d='M0 2h4'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$custom-checkbox-indicator-indeterminate-box-shadow: none !default;\n\n$custom-radio-indicator-border-radius:          50% !default;\n$custom-radio-indicator-icon-checked:           str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='#{$custom-control-indicator-checked-color}'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$custom-select-padding-y:           .375rem !default;\n$custom-select-padding-x:          .75rem !default;\n$custom-select-height:              $input-height !default;\n$custom-select-indicator-padding:   1rem !default; // Extra padding to account for the presence of the background-image based indicator\n$custom-select-line-height:         $input-btn-line-height !default;\n$custom-select-color:               $input-color !default;\n$custom-select-disabled-color:      $gray-600 !default;\n$custom-select-bg:                  $white !default;\n$custom-select-disabled-bg:         $gray-200 !default;\n$custom-select-bg-size:             8px 10px !default; // In pixels because image dimensions\n$custom-select-indicator-color:     $gray-800 !default;\n$custom-select-indicator:           str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='#{$custom-select-indicator-color}' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$custom-select-border-width:        $input-btn-border-width !default;\n$custom-select-border-color:        $input-border-color !default;\n$custom-select-border-radius:       $border-radius !default;\n\n$custom-select-focus-border-color:  $input-focus-border-color !default;\n$custom-select-focus-box-shadow:    none !default;\n\n$custom-select-font-size-sm:        75% !default;\n$custom-select-height-sm:           $input-height-sm !default;\n\n$custom-select-font-size-lg:        125% !default;\n$custom-select-height-lg:           $input-height-lg !default;\n\n$custom-file-height:                $input-height !default;\n$custom-file-focus-border-color:    $input-focus-border-color !default;\n$custom-file-focus-box-shadow:      $custom-select-focus-box-shadow !default;\n\n$custom-file-padding-y:             $input-btn-padding-y !default;\n$custom-file-padding-x:             $input-btn-padding-x !default;\n$custom-file-line-height:           $input-btn-line-height !default;\n$custom-file-color:                 $input-color !default;\n$custom-file-bg:                    $input-bg !default;\n$custom-file-border-width:          $input-btn-border-width !default;\n$custom-file-border-color:          $input-border-color !default;\n$custom-file-border-radius:         $input-border-radius !default;\n$custom-file-box-shadow:            $custom-select-focus-box-shadow !default;\n$custom-file-button-color:          $custom-file-color !default;\n$custom-file-button-bg:             $input-group-addon-bg !default;\n$custom-file-text: (\n        en: \"Browse\"\n) !default;\n\n$custom-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-btn-focus-box-shadow !default;\n\n\n// Form validation\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $small-font-size !default;\n$form-feedback-valid-color:         theme-color(\"success\") !default;\n$form-feedback-invalid-color:       theme-color(\"danger\") !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-bg:                       $white !default;\n$dropdown-border-color:             rgba($black, .15) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             $border-width !default;\n$dropdown-divider-bg:               $gray-200 !default;\n$dropdown-box-shadow:               0 .5rem 1rem rgba($black, .175) !default;\n\n$dropdown-link-color:               $gray-900 !default;\n$dropdown-link-hover-color:         darken($gray-900, 5%) !default;\n$dropdown-link-hover-bg:            $gray-100 !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-600 !default;\n\n$dropdown-item-padding-y:           .25rem !default;\n$dropdown-item-padding-x:           1rem !default;\n\n$dropdown-header-color:             $gray-600 !default;\n\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-modal-backdrop:             1040 !default;\n$zindex-modal:                      1050 !default;\n$zindex-popover:                    1060 !default;\n$zindex-tooltip:                    1070 !default;\n\n// Navs\n\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-disabled-color:           $gray-600 !default;\n\n$nav-tabs-border-color:             $gray-300 !default;\n$nav-tabs-border-width:             $border-width !default;\n$nav-tabs-border-radius:            $border-radius !default;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        $gray-700 !default;\n$nav-tabs-link-active-bg:           $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n// Navbar\n\n$navbar-padding-y:                  ($spacer / 2) !default;\n$navbar-padding-x:                  ($spacer / 2) !default;\n\n$navbar-nav-link-padding-x:         1rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   ($font-size-base * $line-height-base + $nav-link-padding-y * 2) !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) / 2 !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n\n$navbar-dark-color:                 rgba($white, .75) !default;\n$navbar-dark-hover-color:           rgba($white, 1) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#{$navbar-dark-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n\n$navbar-light-color:                rgba($black, .5) !default;\n$navbar-light-hover-color:          rgba($black, .7) !default;\n$navbar-light-active-color:         rgba($black, .9) !default;\n$navbar-light-disabled-color:       rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg:      str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#{$navbar-light-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n// Pagination\n\n$pagination-padding-y:              .5rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n$pagination-line-height:            1.25 !default;\n\n$pagination-color:                  $link-color !default;\n$pagination-bg:                     $white !default;\n$pagination-border-width:           $border-width !default;\n$pagination-border-color:           $gray-300 !default;\n\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n\n$pagination-hover-color:            $link-hover-color !default;\n$pagination-hover-bg:               $gray-200 !default;\n$pagination-hover-border-color:     $gray-300 !default;\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $pagination-active-bg !default;\n\n$pagination-disabled-color:         $gray-600 !default;\n$pagination-disabled-bg:            $white !default;\n$pagination-disabled-border-color:  $gray-300 !default;\n\n\n// Jumbotron\n\n$jumbotron-padding:                 2rem !default;\n$jumbotron-bg:                      $gray-200 !default;\n\n\n// Cards\n\n$card-spacer-y:                     .75rem !default;\n$card-spacer-x:                     1.25rem !default;\n$card-border-width:                 0 !default; //$border-width !default;\n$card-border-radius:                $border-radius !default;\n$card-border-color:                 rgba($black, .125) !default;\n$card-inner-border-radius:          calc(#{$card-border-radius} - #{$card-border-width}) !default;\n$card-cap-bg:                       rgba($black, .03) !default;\n$card-bg:                           $white !default;\n\n$card-img-overlay-padding:          1.25rem !default;\n\n$card-group-margin:                 ($grid-gutter-width / 2) !default;\n$card-deck-margin:                  $card-group-margin !default;\n\n$card-columns-count:                3 !default;\n$card-columns-gap:                  1.25rem !default;\n$card-columns-margin:               $card-spacer-y !default;\n\n\n// Tooltips\n\n$tooltip-font-size:           $font-size-sm !default;\n$tooltip-max-width:           200px !default;\n$tooltip-color:               $white !default;\n$tooltip-bg:                  $black !default;\n$tooltip-border-radius:        $border-radius !default;\n$tooltip-opacity:             .9 !default;\n$tooltip-padding-y:           .25rem !default;\n$tooltip-padding-x:           .5rem !default;\n$tooltip-margin:              0 !default;\n\n$tooltip-arrow-width:         .8rem !default;\n$tooltip-arrow-height:        .4rem !default;\n$tooltip-arrow-color:         $tooltip-bg !default;\n\n// Form tooltips must come after regular tooltips\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   $line-height-base !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n\n// Popovers\n\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        $white !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              $border-width !default;\n$popover-border-color:              rgba($black, .2) !default;\n$popover-border-radius:             $border-radius-lg !default;\n$popover-box-shadow:                0 .25rem .5rem rgba($black, .2) !default;\n\n$popover-header-bg:                 darken($popover-bg, 3%) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          .75rem !default;\n\n$popover-body-color:                $body-color !default;\n$popover-body-padding-y:            $popover-header-padding-y !default;\n$popover-body-padding-x:            $popover-header-padding-x !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n$popover-arrow-color:               $popover-bg !default;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\n\n\n// Badges\n\n$badge-font-size:                   75% !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-padding-y:                   .25em !default;\n$badge-padding-x:                   .4em !default;\n$badge-border-radius:               $border-radius !default;\n\n$badge-pill-padding-x:              .6em !default;\n// Use a higher than normal value to ensure completely rounded edges when\n// customizing padding or font-size on labels.\n$badge-pill-border-radius:          10rem !default;\n\n\n// Modals\n\n// Padding applied to the modal body\n$modal-inner-padding:         1rem !default;\n\n$modal-dialog-margin:         .5rem !default;\n$modal-dialog-margin-y-sm-up: 1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-bg:               $white !default;\n$modal-content-border-color:     rgba($black, .2) !default;\n$modal-content-border-width:     $border-width !default;\n$modal-content-box-shadow-xs:    0 .25rem .5rem rgba($black, .5) !default;\n$modal-content-box-shadow-sm-up: 0 .5rem 1rem rgba($black, .5) !default;\n\n$modal-backdrop-bg:           $black !default;\n$modal-backdrop-opacity:      .5 !default;\n$modal-header-border-color:   $gray-200 !default;\n$modal-footer-border-color:   $modal-header-border-color !default;\n$modal-header-border-width:   $modal-content-border-width !default;\n$modal-footer-border-width:   $modal-header-border-width !default;\n$modal-header-padding:        1rem !default;\n\n$modal-lg:                          800px !default;\n$modal-md:                          500px !default;\n$modal-sm:                          300px !default;\n\n$modal-transition:                  transform .3s ease-out !default;\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n$alert-padding-y:                   .75rem !default;\n$alert-padding-x:                   1.25rem !default;\n$alert-margin-bottom:               1rem !default;\n$alert-border-radius:               $border-radius !default;\n$alert-link-font-weight:            $font-weight-bold !default;\n$alert-border-width:                $border-width !default;\n\n$alert-bg-level:                    -10 !default;\n$alert-border-level:                -9 !default;\n$alert-color-level:                 6 !default;\n\n\n// Progress bars\n\n$progress-height:                   1rem !default;\n$progress-font-size:                ($font-size-base * .75) !default;\n$progress-bg:                       $gray-200 !default;\n$progress-border-radius:            $border-radius !default;\n$progress-box-shadow:               inset 0 .1rem .1rem rgba($black, .1) !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   theme-color(\"primary\") !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n\n// List group\n\n$list-group-bg:                     $white !default;\n$list-group-border-color:           rgba($black, .125) !default;\n$list-group-border-width:           $border-width !default;\n$list-group-border-radius:          $border-radius !default;\n\n$list-group-item-padding-y:         .75rem !default;\n$list-group-item-padding-x:         1.25rem !default;\n\n$list-group-hover-bg:               $gray-100 !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         $gray-600 !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           $gray-700 !default;\n$list-group-action-hover-color:     $list-group-action-color !default;\n\n$list-group-action-active-color:    $body-color !default;\n$list-group-action-active-bg:       $gray-200 !default;\n\n\n// Image thumbnails\n\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      $body-bg !default;\n$thumbnail-border-width:            $border-width !default;\n$thumbnail-border-color:            $gray-300 !default;\n$thumbnail-border-radius:           $border-radius !default;\n$thumbnail-box-shadow:              0 1px 2px rgba($black, .075) !default;\n\n\n// Figures\n\n$figure-caption-font-size:          90% !default;\n$figure-caption-color:              $gray-600 !default;\n\n\n// Breadcrumbs\n\n$breadcrumb-padding-y:              .75rem !default;\n$breadcrumb-padding-x:              1rem !default;\n$breadcrumb-item-padding:           .5rem !default;\n\n$breadcrumb-margin-bottom:          1rem !default;\n\n$breadcrumb-bg:                     $gray-200 !default;\n$breadcrumb-divider-color:          $gray-600 !default;\n$breadcrumb-active-color:           $gray-600 !default;\n$breadcrumb-divider:                \"/\" !default;\n\n\n// Carousel\n\n$carousel-control-color:            $white !default;\n$carousel-control-width:            15% !default;\n$carousel-control-opacity:          .5 !default;\n\n$carousel-indicator-width:          30px !default;\n$carousel-indicator-height:         3px !default;\n$carousel-indicator-spacer:         3px !default;\n$carousel-indicator-active-bg:      $white !default;\n\n$carousel-caption-width:            70% !default;\n$carousel-caption-color:            $white !default;\n\n$carousel-control-icon-width:       20px !default;\n\n$carousel-control-prev-icon-bg:     str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3E%3Cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$carousel-control-next-icon-bg:     str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3E%3Cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$carousel-transition:               transform .6s ease !default;\n\n\n// Close\n\n$close-font-size:                   $font-size-base * 1.5 !default;\n$close-font-weight:                 $font-weight-bold !default;\n$close-color:                       $black !default;\n$close-text-shadow:                 0 1px 0 $white !default;\n\n// Code\n\n$code-font-size:                    87.5% !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .2rem !default;\n$kbd-padding-x:                     .4rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         $white !default;\n$kbd-bg:                            $gray-900 !default;\n\n$pre-color:                         $gray-900 !default;\n$pre-scrollable-max-height:         340px !default;\n\n\n// Printing\n$print-page-size:                   a3 !default;\n$print-body-min-width:              map-get($grid-breakpoints, \"lg\") !default;\n", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated font-resizing\n//\n// See https://github.com/twbs/rfs\n\n// Configuration\n\n// Base font size\n$rfs-base-font-size: 1.25rem !default;\n$rfs-font-size-unit: rem !default;\n\n// Breakpoint at where font-size starts decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n// Resize font-size based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != \"number\" or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-responsive-font-sizes to false\n$enable-responsive-font-sizes: true !default;\n\n// Cache $rfs-base-font-size unit\n$rfs-base-font-size-unit: unit($rfs-base-font-size);\n\n// Remove px-unit from $rfs-base-font-size for calculations\n@if $rfs-base-font-size-unit == \"px\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1);\n}\n@else if $rfs-base-font-size-unit == \"rem\" {\n  $rfs-base-font-size: $rfs-base-font-size / ($rfs-base-font-size * 0 + 1 / $rfs-rem-value);\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == \"px\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == \"rem\" or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: $rfs-breakpoint / ($rfs-breakpoint * 0 + 1 / $rfs-rem-value);\n}\n\n// Responsive font-size mixin\n@mixin rfs($fs, $important: false) {\n  // Cache $fs unit\n  $fs-unit: if(type-of($fs) == \"number\", unit($fs), false);\n\n  // Add !important suffix if needed\n  $rfs-suffix: if($important, \" !important\", \"\");\n\n  // If $fs isn't a number (like inherit) or $fs has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n  @if not $fs-unit or $fs-unit != \"\" and $fs-unit != \"px\" and $fs-unit != \"rem\" or $fs == 0 {\n    font-size: #{$fs}#{$rfs-suffix};\n  }\n  @else {\n    // Variables for storing static and fluid rescaling\n    $rfs-static: null;\n    $rfs-fluid: null;\n\n    // Remove px-unit from $fs for calculations\n    @if $fs-unit == \"px\" {\n      $fs: $fs / ($fs * 0 + 1);\n    }\n    @else if $fs-unit == \"rem\" {\n      $fs: $fs / ($fs * 0 + 1 / $rfs-rem-value);\n    }\n\n    // Set default font-size\n    @if $rfs-font-size-unit == rem {\n      $rfs-static: #{$fs / $rfs-rem-value}rem#{$rfs-suffix};\n    }\n    @else if $rfs-font-size-unit == px {\n      $rfs-static: #{$fs}px#{$rfs-suffix};\n    }\n    @else {\n      @error \"`#{$rfs-font-size-unit}` is not a valid unit for $rfs-font-size-unit. Use `px` or `rem`.\";\n    }\n\n    // Only add media query if font-size is bigger as the minimum font-size\n    // If $rfs-factor == 1, no rescaling will take place\n    @if $fs > $rfs-base-font-size and $enable-responsive-font-sizes {\n      $min-width: null;\n      $variable-unit: null;\n\n      // Calculate minimum font-size for given font-size\n      $fs-min: $rfs-base-font-size + ($fs - $rfs-base-font-size) / $rfs-factor;\n\n      // Calculate difference between given font-size and minimum font-size for given font-size\n      $fs-diff: $fs - $fs-min;\n\n      // Base font-size formatting\n      // No need to check if the unit is valid, because we did that before\n      $min-width: if($rfs-font-size-unit == rem, #{$fs-min / $rfs-rem-value}rem, #{$fs-min}px);\n\n      // If two-dimensional, use smallest of screen width and height\n      $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n      // Calculate the variable width between 0 and $rfs-breakpoint\n      $variable-width: #{$fs-diff * 100 / $rfs-breakpoint}#{$variable-unit};\n\n      // Set the calculated font-size.\n      $rfs-fluid: calc(#{$min-width} + #{$variable-width}) #{$rfs-suffix};\n    }\n\n    // Rendering\n    @if $rfs-fluid == null {\n      // Only render static font-size if no fluid font-size is available\n      font-size: $rfs-static;\n    }\n    @else {\n      $mq-value: null;\n\n      // RFS breakpoint formatting\n      @if $rfs-breakpoint-unit == em or $rfs-breakpoint-unit == rem {\n        $mq-value: #{$rfs-breakpoint / $rfs-rem-value}#{$rfs-breakpoint-unit};\n      }\n      @else if $rfs-breakpoint-unit == px {\n        $mq-value: #{$rfs-breakpoint}px;\n      }\n      @else {\n        @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n      }\n\n      @if $rfs-class == \"disable\" {\n        // Adding an extra class increases specificity,\n        // which prevents the media query to override the font size\n        &,\n        .disable-responsive-font-size &,\n        &.disable-responsive-font-size {\n          font-size: $rfs-static;\n        }\n      }\n      @else {\n        font-size: $rfs-static;\n      }\n\n      @if $rfs-two-dimensional {\n        @media (max-width: #{$mq-value}), (max-height: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n      @else {\n        @media (max-width: #{$mq-value}) {\n          @if $rfs-class == \"enable\" {\n            .enable-responsive-font-size &,\n            &.enable-responsive-font-size {\n              font-size: $rfs-fluid;\n            }\n          }\n          @else {\n            font-size: $rfs-fluid;\n          }\n\n          @if $rfs-safari-iframe-resize-bug-fix {\n            // stylelint-disable-next-line length-zero-no-unit\n            min-width: 0vw;\n          }\n        }\n      }\n    }\n  }\n}\n\n// The font-size & responsive-font-size mixin uses RFS to rescale font sizes\n@mixin font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n\n@mixin responsive-font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n$white:    #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n\n$grays: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$grays: map-merge(\n  (\n    \"100\": $gray-100,\n    \"200\": $gray-200,\n    \"300\": $gray-300,\n    \"400\": $gray-400,\n    \"500\": $gray-500,\n    \"600\": $gray-600,\n    \"700\": $gray-700,\n    \"800\": $gray-800,\n    \"900\": $gray-900\n  ),\n  $grays\n);\n\n$blue:    #007bff !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #e83e8c !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #28a745 !default;\n$teal:    #20c997 !default;\n$cyan:    #17a2b8 !default;\n\n$colors: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$colors: map-merge(\n  (\n    \"blue\":       $blue,\n    \"indigo\":     $indigo,\n    \"purple\":     $purple,\n    \"pink\":       $pink,\n    \"red\":        $red,\n    \"orange\":     $orange,\n    \"yellow\":     $yellow,\n    \"green\":      $green,\n    \"teal\":       $teal,\n    \"cyan\":       $cyan,\n    \"white\":      $white,\n    \"gray\":       $gray-600,\n    \"gray-dark\":  $gray-800\n  ),\n  $colors\n);\n\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-800 !default;\n\n$theme-colors: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$theme-colors: map-merge(\n  (\n    \"primary\":    $primary,\n    \"secondary\":  $secondary,\n    \"success\":    $success,\n    \"info\":       $info,\n    \"warning\":    $warning,\n    \"danger\":     $danger,\n    \"light\":      $light,\n    \"dark\":       $dark\n  ),\n  $theme-colors\n);\n\n// Set a specific jump point for requesting color jumps\n$theme-color-interval:      8% !default;\n\n// The yiq lightness value that determines when the lightness of color changes from \"dark\" to \"light\". Acceptable values are between 0 and 255.\n$yiq-contrasted-threshold:  150 !default;\n\n// Customize the light and dark text colors for use in our YIQ color contrast function.\n$yiq-text-dark:             $gray-900 !default;\n$yiq-text-light:            $white !default;\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\",\"%3c\"),\n  (\">\",\"%3e\"),\n  (\"#\",\"%23\"),\n) !default;\n\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                                true !default;\n$enable-rounded:                              true !default;\n$enable-shadows:                              false !default;\n$enable-gradients:                            false !default;\n$enable-transitions:                          true !default;\n$enable-prefers-reduced-motion-media-query:   true !default;\n$enable-hover-media-query:                    false !default; // Deprecated, no longer affects any compiled CSS\n$enable-grid-classes:                         true !default;\n$enable-pointer-cursor-for-buttons:           true !default;\n$enable-print-styles:                         true !default;\n$enable-responsive-font-sizes:                false !default;\n$enable-validation-icons:                     true !default;\n$enable-deprecation-messages:                 true !default;\n\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n$spacer: 1rem !default;\n$spacers: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$spacers: map-merge(\n  (\n    0: 0,\n    1: ($spacer * .25),\n    2: ($spacer * .5),\n    3: $spacer,\n    4: ($spacer * 1.5),\n    5: ($spacer * 3)\n  ),\n  $spacers\n);\n\n// This variable affects the `.h-*` and `.w-*` classes.\n$sizes: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$sizes: map-merge(\n  (\n    25: 25%,\n    50: 50%,\n    75: 75%,\n    100: 100%,\n    auto: auto\n  ),\n  $sizes\n);\n\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white !default;\n$body-color:                $gray-900 !default;\n\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              theme-color(\"primary\") !default;\n$link-decoration:                         none !default;\n$link-hover-color:                        darken($link-color, 15%) !default;\n$link-hover-decoration:                   underline !default;\n// Darken percentage for links with `.text-*` class (e.g. `.text-success`)\n$emphasized-link-hover-darken-percentage: 15% !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px\n) !default;\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px\n) !default;\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           30px !default;\n$grid-row-columns:            6 !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n$line-height-lg:              1.5 !default;\n$line-height-sm:              1.5 !default;\n\n$border-width:                1px !default;\n$border-color:                $gray-300 !default;\n\n$border-radius:               .25rem !default;\n$border-radius-lg:            .3rem !default;\n$border-radius-sm:            .2rem !default;\n\n$rounded-pill:                50rem !default;\n\n$box-shadow-sm:               0 .125rem .25rem rgba($black, .075) !default;\n$box-shadow:                  0 .5rem 1rem rgba($black, .15) !default;\n$box-shadow-lg:               0 1rem 3rem rgba($black, .175) !default;\n\n$component-active-color:      $white !default;\n$component-active-bg:         theme-color(\"primary\") !default;\n\n$caret-width:                 .3em !default;\n$caret-vertical-align:        $caret-width * .85 !default;\n$caret-spacing:               $caret-width * .85 !default;\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n$transition-collapse:         height .35s ease !default;\n\n$embed-responsive-aspect-ratios: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$embed-responsive-aspect-ratios: join(\n  (\n    (21 9),\n    (16 9),\n    (4 3),\n    (1 1),\n  ),\n  $embed-responsive-aspect-ratios\n);\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n$font-family-base:            $font-family-sans-serif !default;\n// stylelint-enable value-keyword-case\n\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-lg:                $font-size-base * 1.25 !default;\n$font-size-sm:                $font-size-base * .875 !default;\n\n$font-weight-lighter:         lighter !default;\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-bold:            700 !default;\n$font-weight-bolder:          bolder !default;\n\n$font-weight-base:            $font-weight-normal !default;\n$line-height-base:            1.5 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n\n$headings-margin-bottom:      $spacer / 2 !default;\n$headings-font-family:        null !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              null !default;\n\n$display1-size:               6rem !default;\n$display2-size:               5.5rem !default;\n$display3-size:               4.5rem !default;\n$display4-size:               3.5rem !default;\n\n$display1-weight:             300 !default;\n$display2-weight:             300 !default;\n$display3-weight:             300 !default;\n$display4-weight:             300 !default;\n$display-line-height:         $headings-line-height !default;\n\n$lead-font-size:              $font-size-base * 1.25 !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             80% !default;\n\n$text-muted:                  $gray-600 !default;\n\n$blockquote-small-color:      $gray-600 !default;\n$blockquote-small-font-size:  $small-font-size !default;\n$blockquote-font-size:        $font-size-base * 1.25 !default;\n\n$hr-border-color:             rgba($black, .1) !default;\n$hr-border-width:             $border-width !default;\n\n$mark-padding:                .2em !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$kbd-box-shadow:              inset 0 -.1rem 0 rgba($black, .25) !default;\n$nested-kbd-font-weight:      $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-bg:                     #fcf8e3 !default;\n\n$hr-margin-y:                 $spacer !default;\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n$table-cell-padding:          .75rem !default;\n$table-cell-padding-sm:       .3rem !default;\n\n$table-color:                 $body-color !default;\n$table-bg:                    null !default;\n$table-accent-bg:             rgba($black, .05) !default;\n$table-hover-color:           $table-color !default;\n$table-hover-bg:              rgba($black, .075) !default;\n$table-active-bg:             $table-hover-bg !default;\n\n$table-border-width:          $border-width !default;\n$table-border-color:          $border-color !default;\n\n$table-head-bg:               $gray-200 !default;\n$table-head-color:            $gray-700 !default;\n\n$table-dark-color:            $white !default;\n$table-dark-bg:               $gray-800 !default;\n$table-dark-accent-bg:        rgba($white, .05) !default;\n$table-dark-hover-color:      $table-dark-color !default;\n$table-dark-hover-bg:         rgba($white, .075) !default;\n$table-dark-border-color:     lighten($table-dark-bg, 7.5%) !default;\n\n$table-striped-order:         odd !default;\n\n$table-caption-color:         $text-muted !default;\n\n$table-bg-level:              -9 !default;\n$table-border-level:          -6 !default;\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-font-family:       null !default;\n$input-btn-font-size:         $font-size-base !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:       .2rem !default;\n$input-btn-focus-color:       rgba($component-active-bg, .25) !default;\n$input-btn-focus-box-shadow:  0 0 0 $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-font-size-sm:      $font-size-sm !default;\n$input-btn-line-height-sm:    $line-height-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-font-size-lg:      $font-size-lg !default;\n$input-btn-line-height-lg:    $line-height-lg !default;\n\n$input-btn-border-width:      $border-width !default;\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-font-family:             $input-btn-font-family !default;\n$btn-font-size:               $input-btn-font-size !default;\n$btn-line-height:             $input-btn-line-height !default;\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\n$btn-line-height-sm:          $input-btn-line-height-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\n$btn-line-height-lg:          $input-btn-line-height-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075) !default;\n$btn-focus-width:             $input-btn-focus-width !default;\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125) !default;\n\n$btn-link-disabled-color:     $gray-600 !default;\n\n$btn-block-spacing-y:         .5rem !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n\n// Forms\n\n$label-margin-bottom:                   .5rem !default;\n\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-font-family:                     $input-btn-font-family !default;\n$input-font-size:                       $input-btn-font-size !default;\n$input-font-weight:                     $font-weight-base !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\n$input-line-height-sm:                  $input-btn-line-height-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\n$input-line-height-lg:                  $input-btn-line-height-lg !default;\n\n$input-bg:                              $white !default;\n$input-disabled-bg:                     $gray-200 !default;\n\n$input-color:                           $gray-700 !default;\n$input-border-color:                    $gray-400 !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      inset 0 1px 1px rgba($black, .075) !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              lighten($component-active-bg, 25%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     $input-btn-focus-width !default;\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\n\n$input-placeholder-color:               $gray-600 !default;\n$input-plaintext-color:                 $body-color !default;\n\n$input-height-border:                   $input-border-width * 2 !default;\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y) !default;\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y / 2) !default;\n\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\n$input-height-sm:                       add($input-line-height-sm * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\n$input-height-lg:                       add($input-line-height-lg * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-text-margin-top:                  .25rem !default;\n\n$form-check-input-gutter:               1.25rem !default;\n$form-check-input-margin-y:             .3rem !default;\n$form-check-input-margin-x:             .25rem !default;\n\n$form-check-inline-margin-x:            .75rem !default;\n$form-check-inline-input-margin-x:      .3125rem !default;\n\n$form-grid-gutter-width:                10px !default;\n$form-group-margin-bottom:              1rem !default;\n\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  $gray-200 !default;\n$input-group-addon-border-color:        $input-border-color !default;\n\n$custom-forms-transition:               background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$custom-control-gutter:                 .5rem !default;\n$custom-control-spacer-x:               1rem !default;\n$custom-control-cursor:                 null !default;\n\n$custom-control-indicator-size:         1rem !default;\n$custom-control-indicator-bg:           $input-bg !default;\n\n$custom-control-indicator-bg-size:      50% 50% !default;\n$custom-control-indicator-box-shadow:   $input-box-shadow !default;\n$custom-control-indicator-border-color: $gray-500 !default;\n$custom-control-indicator-border-width: $input-border-width !default;\n\n$custom-control-label-color:            null !default;\n\n$custom-control-indicator-disabled-bg:          $input-disabled-bg !default;\n$custom-control-label-disabled-color:           $gray-600 !default;\n\n$custom-control-indicator-checked-color:        $component-active-color !default;\n$custom-control-indicator-checked-bg:           $component-active-bg !default;\n$custom-control-indicator-checked-disabled-bg:  rgba(theme-color(\"primary\"), .5) !default;\n$custom-control-indicator-checked-box-shadow:   none !default;\n$custom-control-indicator-checked-border-color: $custom-control-indicator-checked-bg !default;\n\n$custom-control-indicator-focus-box-shadow:     $input-focus-box-shadow !default;\n$custom-control-indicator-focus-border-color:   $input-focus-border-color !default;\n\n$custom-control-indicator-active-color:         $component-active-color !default;\n$custom-control-indicator-active-bg:            lighten($component-active-bg, 35%) !default;\n$custom-control-indicator-active-box-shadow:    none !default;\n$custom-control-indicator-active-border-color:  $custom-control-indicator-active-bg !default;\n\n$custom-checkbox-indicator-border-radius:       $border-radius !default;\n$custom-checkbox-indicator-icon-checked:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'><path fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/></svg>\") !default;\n\n$custom-checkbox-indicator-indeterminate-bg:           $component-active-bg !default;\n$custom-checkbox-indicator-indeterminate-color:        $custom-control-indicator-checked-color !default;\n$custom-checkbox-indicator-icon-indeterminate:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'><path stroke='#{$custom-checkbox-indicator-indeterminate-color}' d='M0 2h4'/></svg>\") !default;\n$custom-checkbox-indicator-indeterminate-box-shadow:   none !default;\n$custom-checkbox-indicator-indeterminate-border-color: $custom-checkbox-indicator-indeterminate-bg !default;\n\n$custom-radio-indicator-border-radius:          50% !default;\n$custom-radio-indicator-icon-checked:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='-4 -4 8 8'><circle r='3' fill='#{$custom-control-indicator-checked-color}'/></svg>\") !default;\n\n$custom-switch-width:                           $custom-control-indicator-size * 1.75 !default;\n$custom-switch-indicator-border-radius:         $custom-control-indicator-size / 2 !default;\n$custom-switch-indicator-size:                  subtract($custom-control-indicator-size, $custom-control-indicator-border-width * 4) !default;\n\n$custom-select-padding-y:           $input-padding-y !default;\n$custom-select-padding-x:           $input-padding-x !default;\n$custom-select-font-family:         $input-font-family !default;\n$custom-select-font-size:           $input-font-size !default;\n$custom-select-height:              $input-height !default;\n$custom-select-indicator-padding:   1rem !default; // Extra padding to account for the presence of the background-image based indicator\n$custom-select-font-weight:         $input-font-weight !default;\n$custom-select-line-height:         $input-line-height !default;\n$custom-select-color:               $input-color !default;\n$custom-select-disabled-color:      $gray-600 !default;\n$custom-select-bg:                  $input-bg !default;\n$custom-select-disabled-bg:         $gray-200 !default;\n$custom-select-bg-size:             8px 10px !default; // In pixels because image dimensions\n$custom-select-indicator-color:     $gray-800 !default;\n$custom-select-indicator:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'><path fill='#{$custom-select-indicator-color}' d='M2 0L0 2h4zm0 5L0 3h4z'/></svg>\") !default;\n$custom-select-background:          escape-svg($custom-select-indicator) no-repeat right $custom-select-padding-x center / $custom-select-bg-size !default; // Used so we can have multiple background elements (e.g., arrow and feedback icon)\n\n$custom-select-feedback-icon-padding-right: add(1em * .75, (2 * $custom-select-padding-y * .75) + $custom-select-padding-x + $custom-select-indicator-padding) !default;\n$custom-select-feedback-icon-position:      center right ($custom-select-padding-x + $custom-select-indicator-padding) !default;\n$custom-select-feedback-icon-size:          $input-height-inner-half $input-height-inner-half !default;\n\n$custom-select-border-width:        $input-border-width !default;\n$custom-select-border-color:        $input-border-color !default;\n$custom-select-border-radius:       $border-radius !default;\n$custom-select-box-shadow:          inset 0 1px 2px rgba($black, .075) !default;\n\n$custom-select-focus-border-color:  $input-focus-border-color !default;\n$custom-select-focus-width:         $input-focus-width !default;\n$custom-select-focus-box-shadow:    0 0 0 $custom-select-focus-width $input-btn-focus-color !default;\n\n$custom-select-padding-y-sm:        $input-padding-y-sm !default;\n$custom-select-padding-x-sm:        $input-padding-x-sm !default;\n$custom-select-font-size-sm:        $input-font-size-sm !default;\n$custom-select-height-sm:           $input-height-sm !default;\n\n$custom-select-padding-y-lg:        $input-padding-y-lg !default;\n$custom-select-padding-x-lg:        $input-padding-x-lg !default;\n$custom-select-font-size-lg:        $input-font-size-lg !default;\n$custom-select-height-lg:           $input-height-lg !default;\n\n$custom-range-track-width:          100% !default;\n$custom-range-track-height:         .5rem !default;\n$custom-range-track-cursor:         pointer !default;\n$custom-range-track-bg:             $gray-300 !default;\n$custom-range-track-border-radius:  1rem !default;\n$custom-range-track-box-shadow:     inset 0 .25rem .25rem rgba($black, .1) !default;\n\n$custom-range-thumb-width:                   1rem !default;\n$custom-range-thumb-height:                  $custom-range-thumb-width !default;\n$custom-range-thumb-bg:                      $component-active-bg !default;\n$custom-range-thumb-border:                  0 !default;\n$custom-range-thumb-border-radius:           1rem !default;\n$custom-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\n$custom-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\n$custom-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in IE/Edge\n$custom-range-thumb-active-bg:               lighten($component-active-bg, 35%) !default;\n$custom-range-thumb-disabled-bg:             $gray-500 !default;\n\n$custom-file-height:                $input-height !default;\n$custom-file-height-inner:          $input-height-inner !default;\n$custom-file-focus-border-color:    $input-focus-border-color !default;\n$custom-file-focus-box-shadow:      $input-focus-box-shadow !default;\n$custom-file-disabled-bg:           $input-disabled-bg !default;\n\n$custom-file-padding-y:             $input-padding-y !default;\n$custom-file-padding-x:             $input-padding-x !default;\n$custom-file-line-height:           $input-line-height !default;\n$custom-file-font-family:           $input-font-family !default;\n$custom-file-font-weight:           $input-font-weight !default;\n$custom-file-color:                 $input-color !default;\n$custom-file-bg:                    $input-bg !default;\n$custom-file-border-width:          $input-border-width !default;\n$custom-file-border-color:          $input-border-color !default;\n$custom-file-border-radius:         $input-border-radius !default;\n$custom-file-box-shadow:            $input-box-shadow !default;\n$custom-file-button-color:          $custom-file-color !default;\n$custom-file-button-bg:             $input-group-addon-bg !default;\n$custom-file-text: (\n  en: \"Browse\"\n) !default;\n\n\n// Form validation\n\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $small-font-size !default;\n$form-feedback-valid-color:         theme-color(\"success\") !default;\n$form-feedback-invalid-color:       theme-color(\"danger\") !default;\n\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}' viewBox='0 0 12 12'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\n\n$form-validation-states: () !default;\n// stylelint-disable-next-line scss/dollar-variable-default\n$form-validation-states: map-merge(\n  (\n    \"valid\": (\n      \"color\": $form-feedback-valid-color,\n      \"icon\": $form-feedback-icon-valid\n    ),\n    \"invalid\": (\n      \"color\": $form-feedback-invalid-color,\n      \"icon\": $form-feedback-icon-invalid\n    ),\n  ),\n  $form-validation-states\n);\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-modal-backdrop:             1040 !default;\n$zindex-modal:                      1050 !default;\n$zindex-popover:                    1060 !default;\n$zindex-tooltip:                    1070 !default;\n\n\n// Navs\n\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-disabled-color:           $gray-600 !default;\n\n$nav-tabs-border-color:             $gray-300 !default;\n$nav-tabs-border-width:             $border-width !default;\n$nav-tabs-border-radius:            $border-radius !default;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        $gray-700 !default;\n$nav-tabs-link-active-bg:           $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n$nav-divider-color:                 $gray-200 !default;\n$nav-divider-margin-y:              $spacer / 2 !default;\n\n\n// Navbar\n\n$navbar-padding-y:                  $spacer / 2 !default;\n$navbar-padding-x:                  $spacer !default;\n\n$navbar-nav-link-padding-x:         .5rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) / 2 !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n\n$navbar-dark-color:                 rgba($white, .5) !default;\n$navbar-dark-hover-color:           rgba($white, .75) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n\n$navbar-light-color:                rgba($black, .5) !default;\n$navbar-light-hover-color:          rgba($black, .7) !default;\n$navbar-light-active-color:         rgba($black, .9) !default;\n$navbar-light-disabled-color:       rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'><path stroke='#{$navbar-light-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n$navbar-light-brand-color:                $navbar-light-active-color !default;\n$navbar-light-brand-hover-color:          $navbar-light-active-color !default;\n$navbar-dark-brand-color:                 $navbar-dark-active-color !default;\n$navbar-dark-brand-hover-color:           $navbar-dark-active-color !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-font-size:                $font-size-base !default;\n$dropdown-color:                    $body-color !default;\n$dropdown-bg:                       $white !default;\n$dropdown-border-color:             rgba($black, .15) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             $border-width !default;\n$dropdown-inner-border-radius:      subtract($dropdown-border-radius, $dropdown-border-width) !default;\n$dropdown-divider-bg:               $gray-200 !default;\n$dropdown-divider-margin-y:         $nav-divider-margin-y !default;\n$dropdown-box-shadow:               0 .5rem 1rem rgba($black, .175) !default;\n\n$dropdown-link-color:               $gray-900 !default;\n$dropdown-link-hover-color:         darken($gray-900, 5%) !default;\n$dropdown-link-hover-bg:            $gray-100 !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-600 !default;\n\n$dropdown-item-padding-y:           .25rem !default;\n$dropdown-item-padding-x:           1.5rem !default;\n\n$dropdown-header-color:             $gray-600 !default;\n\n\n// Pagination\n\n$pagination-padding-y:              .5rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n$pagination-line-height:            1.25 !default;\n\n$pagination-color:                  $link-color !default;\n$pagination-bg:                     $white !default;\n$pagination-border-width:           $border-width !default;\n$pagination-border-color:           $gray-300 !default;\n\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n$pagination-focus-outline:          0 !default;\n\n$pagination-hover-color:            $link-hover-color !default;\n$pagination-hover-bg:               $gray-200 !default;\n$pagination-hover-border-color:     $gray-300 !default;\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $pagination-active-bg !default;\n\n$pagination-disabled-color:         $gray-600 !default;\n$pagination-disabled-bg:            $white !default;\n$pagination-disabled-border-color:  $gray-300 !default;\n\n\n// Jumbotron\n\n$jumbotron-padding:                 2rem !default;\n$jumbotron-color:                   null !default;\n$jumbotron-bg:                      $gray-200 !default;\n\n\n// Cards\n\n$card-spacer-y:                     .75rem !default;\n$card-spacer-x:                     1.25rem !default;\n$card-border-width:                 $border-width !default;\n$card-border-radius:                $border-radius !default;\n$card-border-color:                 rgba($black, .125) !default;\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\n$card-cap-bg:                       rgba($black, .03) !default;\n$card-cap-color:                    null !default;\n$card-height:                       null !default;\n$card-color:                        null !default;\n$card-bg:                           $white !default;\n\n$card-img-overlay-padding:          1.25rem !default;\n\n$card-group-margin:                 $grid-gutter-width / 2 !default;\n$card-deck-margin:                  $card-group-margin !default;\n\n$card-columns-count:                3 !default;\n$card-columns-gap:                  1.25rem !default;\n$card-columns-margin:               $card-spacer-y !default;\n\n\n// Tooltips\n\n$tooltip-font-size:                 $font-size-sm !default;\n$tooltip-max-width:                 200px !default;\n$tooltip-color:                     $white !default;\n$tooltip-bg:                        $black !default;\n$tooltip-border-radius:             $border-radius !default;\n$tooltip-opacity:                   .9 !default;\n$tooltip-padding-y:                 .25rem !default;\n$tooltip-padding-x:                 .5rem !default;\n$tooltip-margin:                    0 !default;\n\n$tooltip-arrow-width:               .8rem !default;\n$tooltip-arrow-height:              .4rem !default;\n$tooltip-arrow-color:               $tooltip-bg !default;\n\n// Form tooltips must come after regular tooltips\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   $line-height-base !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n\n\n// Popovers\n\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        $white !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              $border-width !default;\n$popover-border-color:              rgba($black, .2) !default;\n$popover-border-radius:             $border-radius-lg !default;\n$popover-inner-border-radius:       subtract($popover-border-radius, $popover-border-width) !default;\n$popover-box-shadow:                0 .25rem .5rem rgba($black, .2) !default;\n\n$popover-header-bg:                 darken($popover-bg, 3%) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          .75rem !default;\n\n$popover-body-color:                $body-color !default;\n$popover-body-padding-y:            $popover-header-padding-y !default;\n$popover-body-padding-x:            $popover-header-padding-x !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n$popover-arrow-color:               $popover-bg !default;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\n\n\n// Toasts\n\n$toast-max-width:                   350px !default;\n$toast-padding-x:                   .75rem !default;\n$toast-padding-y:                   .25rem !default;\n$toast-font-size:                   .875rem !default;\n$toast-color:                       null !default;\n$toast-background-color:            rgba($white, .85) !default;\n$toast-border-width:                1px !default;\n$toast-border-color:                rgba(0, 0, 0, .1) !default;\n$toast-border-radius:               .25rem !default;\n$toast-box-shadow:                  0 .25rem .75rem rgba($black, .1) !default;\n\n$toast-header-color:                $gray-600 !default;\n$toast-header-background-color:     rgba($white, .85) !default;\n$toast-header-border-color:         rgba(0, 0, 0, .05) !default;\n\n\n// Badges\n\n$badge-font-size:                   75% !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-padding-y:                   .25em !default;\n$badge-padding-x:                   .4em !default;\n$badge-border-radius:               $border-radius !default;\n\n$badge-transition:                  $btn-transition !default;\n$badge-focus-width:                 $input-btn-focus-width !default;\n\n$badge-pill-padding-x:              .6em !default;\n// Use a higher than normal value to ensure completely rounded edges when\n// customizing padding or font-size on labels.\n$badge-pill-border-radius:          10rem !default;\n\n\n// Modals\n\n// Padding applied to the modal body\n$modal-inner-padding:               1rem !default;\n\n// Margin between elements in footer, must be lower than or equal to 2 * $modal-inner-padding\n$modal-footer-margin-between:       .5rem !default;\n\n$modal-dialog-margin:               .5rem !default;\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-color:               null !default;\n$modal-content-bg:                  $white !default;\n$modal-content-border-color:        rgba($black, .2) !default;\n$modal-content-border-width:        $border-width !default;\n$modal-content-border-radius:       $border-radius-lg !default;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\n$modal-content-box-shadow-xs:       0 .25rem .5rem rgba($black, .5) !default;\n$modal-content-box-shadow-sm-up:    0 .5rem 1rem rgba($black, .5) !default;\n\n$modal-backdrop-bg:                 $black !default;\n$modal-backdrop-opacity:            .5 !default;\n$modal-header-border-color:         $border-color !default;\n$modal-footer-border-color:         $modal-header-border-color !default;\n$modal-header-border-width:         $modal-content-border-width !default;\n$modal-footer-border-width:         $modal-header-border-width !default;\n$modal-header-padding-y:            1rem !default;\n$modal-header-padding-x:            1rem !default;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-xl:                          1140px !default;\n$modal-lg:                          800px !default;\n$modal-md:                          500px !default;\n$modal-sm:                          300px !default;\n\n$modal-fade-transform:              translate(0, -50px) !default;\n$modal-show-transform:              none !default;\n$modal-transition:                  transform .3s ease-out !default;\n$modal-scale-transform:             scale(1.02) !default;\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n$alert-padding-y:                   .75rem !default;\n$alert-padding-x:                   1.25rem !default;\n$alert-margin-bottom:               1rem !default;\n$alert-border-radius:               $border-radius !default;\n$alert-link-font-weight:            $font-weight-bold !default;\n$alert-border-width:                $border-width !default;\n\n$alert-bg-level:                    -10 !default;\n$alert-border-level:                -9 !default;\n$alert-color-level:                 6 !default;\n\n\n// Progress bars\n\n$progress-height:                   1rem !default;\n$progress-font-size:                $font-size-base * .75 !default;\n$progress-bg:                       $gray-200 !default;\n$progress-border-radius:            $border-radius !default;\n$progress-box-shadow:               inset 0 .1rem .1rem rgba($black, .1) !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   theme-color(\"primary\") !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n\n\n// List group\n\n$list-group-color:                  null !default;\n$list-group-bg:                     $white !default;\n$list-group-border-color:           rgba($black, .125) !default;\n$list-group-border-width:           $border-width !default;\n$list-group-border-radius:          $border-radius !default;\n\n$list-group-item-padding-y:         .75rem !default;\n$list-group-item-padding-x:         1.25rem !default;\n\n$list-group-hover-bg:               $gray-100 !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         $gray-600 !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           $gray-700 !default;\n$list-group-action-hover-color:     $list-group-action-color !default;\n\n$list-group-action-active-color:    $body-color !default;\n$list-group-action-active-bg:       $gray-200 !default;\n\n\n// Image thumbnails\n\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      $body-bg !default;\n$thumbnail-border-width:            $border-width !default;\n$thumbnail-border-color:            $gray-300 !default;\n$thumbnail-border-radius:           $border-radius !default;\n$thumbnail-box-shadow:              0 1px 2px rgba($black, .075) !default;\n\n\n// Figures\n\n$figure-caption-font-size:          90% !default;\n$figure-caption-color:              $gray-600 !default;\n\n\n// Breadcrumbs\n\n$breadcrumb-font-size:              null !default;\n\n$breadcrumb-padding-y:              .75rem !default;\n$breadcrumb-padding-x:              1rem !default;\n$breadcrumb-item-padding:           .5rem !default;\n\n$breadcrumb-margin-bottom:          1rem !default;\n\n$breadcrumb-bg:                     $gray-200 !default;\n$breadcrumb-divider-color:          $gray-600 !default;\n$breadcrumb-active-color:           $gray-600 !default;\n$breadcrumb-divider:                quote(\"/\") !default;\n\n$breadcrumb-border-radius:          $border-radius !default;\n\n\n// Carousel\n\n$carousel-control-color:             $white !default;\n$carousel-control-width:             15% !default;\n$carousel-control-opacity:           .5 !default;\n$carousel-control-hover-opacity:     .9 !default;\n$carousel-control-transition:        opacity .15s ease !default;\n\n$carousel-indicator-width:           30px !default;\n$carousel-indicator-height:          3px !default;\n$carousel-indicator-hit-area-height: 10px !default;\n$carousel-indicator-spacer:          3px !default;\n$carousel-indicator-active-bg:       $white !default;\n$carousel-indicator-transition:      opacity .6s ease !default;\n\n$carousel-caption-width:             70% !default;\n$carousel-caption-color:             $white !default;\n\n$carousel-control-icon-width:        20px !default;\n\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' width='8' height='8' viewBox='0 0 8 8'><path d='M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2.5-2.5L5.25 0z'/></svg>\") !default;\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' width='8' height='8' viewBox='0 0 8 8'><path d='M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L2.75 8l4-4-4-4z'/></svg>\") !default;\n\n$carousel-transition-duration:       .6s !default;\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n\n\n// Spinners\n\n$spinner-width:         2rem !default;\n$spinner-height:        $spinner-width !default;\n$spinner-border-width:  .25em !default;\n\n$spinner-width-sm:        1rem !default;\n$spinner-height-sm:       $spinner-width-sm !default;\n$spinner-border-width-sm: .2em !default;\n\n\n// Close\n\n$close-font-size:                   $font-size-base * 1.5 !default;\n$close-font-weight:                 $font-weight-bold !default;\n$close-color:                       $black !default;\n$close-text-shadow:                 0 1px 0 $white !default;\n\n\n// Code\n\n$code-font-size:                    87.5% !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .2rem !default;\n$kbd-padding-x:                     .4rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         $white !default;\n$kbd-bg:                            $gray-900 !default;\n\n$pre-color:                         $gray-900 !default;\n$pre-scrollable-max-height:         340px !default;\n\n\n// Utilities\n\n$displays: none, inline, inline-block, block, table, table-row, table-cell, flex, inline-flex !default;\n$overflows: auto, hidden !default;\n$positions: static, relative, absolute, fixed, sticky !default;\n\n\n// Printing\n\n$print-page-size:                   a3 !default;\n$print-body-min-width:              map-get($grid-breakpoints, \"lg\") !default;\n", "// Hover mixin and `$enable-hover-media-query` are deprecated.\n//\n// Originally added during our alphas and maintained during betas, this mixin was\n// designed to prevent `:hover` stickiness on iOS-an issue where hover styles\n// would persist after initial touch.\n//\n// For backward compatibility, we've kept these mixins and updated them to\n// always return their regular pseudo-classes instead of a shimmed media query.\n//\n// Issue: https://github.com/twbs/bootstrap/issues/25195\n\n@mixin hover() {\n  &:hover { @content; }\n}\n\n@mixin hover-focus() {\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin plain-hover-focus() {\n  &,\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin hover-focus-active() {\n  &:hover,\n  &:focus,\n  &:active {\n    @content;\n  }\n}\n", "// stylelint-disable declaration-no-important, selector-list-comma-newline-after\n\n//\n// Headings\n//\n\nh1, h2, h3, h4, h5, h6,\n.h1, .h2, .h3, .h4, .h5, .h6 {\n  margin-bottom: $headings-margin-bottom;\n  font-family: $headings-font-family;\n  font-weight: $headings-font-weight;\n  line-height: $headings-line-height;\n  color: $headings-color;\n}\n\nh1, .h1 { @include font-size($h1-font-size); }\nh2, .h2 { @include font-size($h2-font-size); }\nh3, .h3 { @include font-size($h3-font-size); }\nh4, .h4 { @include font-size($h4-font-size); }\nh5, .h5 { @include font-size($h5-font-size); }\nh6, .h6 { @include font-size($h6-font-size); }\n\n.lead {\n  @include font-size($lead-font-size);\n  font-weight: $lead-font-weight;\n}\n\n// Type display classes\n.display-1 {\n  @include font-size($display1-size);\n  font-weight: $display1-weight;\n  line-height: $display-line-height;\n}\n.display-2 {\n  @include font-size($display2-size);\n  font-weight: $display2-weight;\n  line-height: $display-line-height;\n}\n.display-3 {\n  @include font-size($display3-size);\n  font-weight: $display3-weight;\n  line-height: $display-line-height;\n}\n.display-4 {\n  @include font-size($display4-size);\n  font-weight: $display4-weight;\n  line-height: $display-line-height;\n}\n\n\n//\n// Horizontal rules\n//\n\nhr {\n  margin-top: $hr-margin-y;\n  margin-bottom: $hr-margin-y;\n  border: 0;\n  border-top: $hr-border-width solid $hr-border-color;\n}\n\n\n//\n// Emphasis\n//\n\nsmall,\n.small {\n  @include font-size($small-font-size);\n  font-weight: $font-weight-normal;\n}\n\nmark,\n.mark {\n  padding: $mark-padding;\n  background-color: $mark-bg;\n}\n\n\n//\n// Lists\n//\n\n.list-unstyled {\n  @include list-unstyled();\n}\n\n// Inline turns list items into inline-block\n.list-inline {\n  @include list-unstyled();\n}\n.list-inline-item {\n  display: inline-block;\n\n  &:not(:last-child) {\n    margin-right: $list-inline-padding;\n  }\n}\n\n\n//\n// Misc\n//\n\n// Builds on `abbr`\n.initialism {\n  @include font-size(90%);\n  text-transform: uppercase;\n}\n\n// Blockquotes\n.blockquote {\n  margin-bottom: $spacer;\n  @include font-size($blockquote-font-size);\n}\n\n.blockquote-footer {\n  display: block;\n  @include font-size($blockquote-small-font-size);\n  color: $blockquote-small-color;\n\n  &::before {\n    content: \"\\2014\\00A0\"; // em dash, nbsp\n  }\n}\n", "// Lists\n\n// Unstyled keeps list items block level, just removes default browser padding and list-style\n@mixin list-unstyled() {\n  padding-left: 0;\n  list-style: none;\n}\n", "// Responsive images (ensure images don't scale beyond their parents)\n//\n// This is purposefully opt-in via an explicit class rather than being the default for all `<img>`s.\n// We previously tried the \"images are responsive by default\" approach in Bootstrap v2,\n// and abandoned it in Bootstrap v3 because it breaks lots of third-party widgets (including Google Maps)\n// which weren't expecting the images within themselves to be involuntarily resized.\n// See also https://github.com/twbs/bootstrap/issues/18178\n.img-fluid {\n  @include img-fluid();\n}\n\n\n// Image thumbnails\n.img-thumbnail {\n  padding: $thumbnail-padding;\n  background-color: $thumbnail-bg;\n  border: $thumbnail-border-width solid $thumbnail-border-color;\n  @include border-radius($thumbnail-border-radius);\n  @include box-shadow($thumbnail-box-shadow);\n\n  // Keep them at most 100% wide\n  @include img-fluid();\n}\n\n//\n// Figures\n//\n\n.figure {\n  // Ensures the caption's text aligns with the image.\n  display: inline-block;\n}\n\n.figure-img {\n  margin-bottom: $spacer / 2;\n  line-height: 1;\n}\n\n.figure-caption {\n  @include font-size($figure-caption-font-size);\n  color: $figure-caption-color;\n}\n", "// Image Mixins\n// - Responsive image\n// - Retina image\n\n\n// Responsive image\n//\n// Keep images from scaling beyond the width of their parents.\n\n@mixin img-fluid() {\n  // Part 1: Set a maximum relative to the parent\n  max-width: 100%;\n  // Part 2: Override the height to auto, otherwise images will be stretched\n  // when setting a width and height attribute on the img element.\n  height: auto;\n}\n\n\n// Retina image\n//\n// Short retina mixin for setting background-image and -size.\n\n@mixin img-retina($file-1x, $file-2x, $width-1x, $height-1x) {\n  background-image: url($file-1x);\n\n  // Autoprefixer takes care of adding -webkit-min-device-pixel-ratio and -o-min-device-pixel-ratio,\n  // but doesn't convert dppx=>dpi.\n  // There's no such thing as unprefixed min-device-pixel-ratio since it's nonstandard.\n  // Compatibility info: https://caniuse.com/#feat=css-media-resolution\n  @media only screen and (min-resolution: 192dpi), // IE9-11 don't support dppx\n    only screen and (min-resolution: 2dppx) { // Standardized\n    background-image: url($file-2x);\n    background-size: $width-1x $height-1x;\n  }\n  @include deprecate(\"`img-retina()`\", \"v4.3.0\", \"v5\");\n}\n", "// stylelint-disable property-blacklist\n// Single side border-radius\n\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: $radius;\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n    border-top-right-radius: $radius;\n  }\n}\n\n@mixin border-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: $radius;\n    border-bottom-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: $radius;\n    border-bottom-left-radius: $radius;\n  }\n}\n\n@mixin border-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n    border-bottom-left-radius: $radius;\n  }\n}\n\n@mixin border-top-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: $radius;\n  }\n}\n\n@mixin border-top-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-right-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: $radius;\n  }\n}\n\n@mixin border-bottom-left-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: $radius;\n  }\n}\n", "@mixin box-shadow($shadow...) {\n  @if $enable-shadows {\n    $result: ();\n\n    @if (length($shadow) == 1) {\n      // We can pass `@include box-shadow(none);`\n      $result: $shadow;\n    } @else {\n      // Filter to avoid invalid properties for example `box-shadow: none, 1px 1px black;`\n      @for $i from 1 through length($shadow) {\n        @if nth($shadow, $i) != \"none\" {\n          $result: append($result, nth($shadow, $i), \"comma\");\n        }\n      }\n    }\n    @if (length($result) > 0) {\n      box-shadow: $result;\n    }\n  }\n}\n", "// Inline code\ncode {\n  @include font-size($code-font-size);\n  color: $code-color;\n  word-wrap: break-word;\n\n  // Streamline the style when inside anchors to avoid broken underline and more\n  a > & {\n    color: inherit;\n  }\n}\n\n// User input typically entered via keyboard\nkbd {\n  padding: $kbd-padding-y $kbd-padding-x;\n  @include font-size($kbd-font-size);\n  color: $kbd-color;\n  background-color: $kbd-bg;\n  @include border-radius($border-radius-sm);\n  @include box-shadow($kbd-box-shadow);\n\n  kbd {\n    padding: 0;\n    @include font-size(100%);\n    font-weight: $nested-kbd-font-weight;\n    @include box-shadow(none);\n  }\n}\n\n// Blocks of code\npre {\n  display: block;\n  @include font-size($code-font-size);\n  color: $pre-color;\n\n  // Account for some code outputs that place code tags in pre tags\n  code {\n    @include font-size(inherit);\n    color: inherit;\n    word-break: normal;\n  }\n}\n\n// Enable scrollable blocks of code\n.pre-scrollable {\n  max-height: $pre-scrollable-max-height;\n  overflow-y: scroll;\n}\n", "// Container widths\n//\n// Set the container width, and override it for fixed navbars in media queries.\n\n@if $enable-grid-classes {\n  // Single container class with breakpoint max-widths\n  .container {\n    @include make-container();\n    @include make-container-max-widths();\n  }\n\n  // 100% wide container at all breakpoints\n  .container-fluid {\n    @include make-container();\n  }\n\n  // Responsive containers that are 100% wide until a breakpoint\n  @each $breakpoint, $container-max-width in $container-max-widths {\n    .container-#{$breakpoint} {\n      @extend .container-fluid;\n    }\n\n    @include media-breakpoint-up($breakpoint, $grid-breakpoints) {\n      %responsive-container-#{$breakpoint} {\n        max-width: $container-max-width;\n      }\n\n      @each $name, $width in $grid-breakpoints {\n        @if ($container-max-width > $width or $breakpoint == $name) {\n          .container#{breakpoint-infix($name, $grid-breakpoints)} {\n            @extend %responsive-container-#{$breakpoint};\n          }\n        }\n      }\n    }\n  }\n}\n\n\n// Row\n//\n// Rows contain your columns.\n\n@if $enable-grid-classes {\n  .row {\n    @include make-row();\n  }\n\n  // Remove the negative margin from default .row, then the horizontal padding\n  // from all immediate children columns (to prevent runaway style inheritance).\n  .no-gutters {\n    margin-right: 0;\n    margin-left: 0;\n\n    > .col,\n    > [class*=\"col-\"] {\n      padding-right: 0;\n      padding-left: 0;\n    }\n  }\n}\n\n// Columns\n//\n// Common styles for small and large grid columns\n\n@if $enable-grid-classes {\n  @include make-grid-columns();\n}\n", "/// Grid system\n//\n// Generate semantic grid columns with these mixins.\n\n@mixin make-container($gutter: $grid-gutter-width) {\n  width: 100%;\n  padding-right: $gutter / 2;\n  padding-left: $gutter / 2;\n  margin-right: auto;\n  margin-left: auto;\n}\n\n\n// For each breakpoint, define the maximum width of the container in a media query\n@mixin make-container-max-widths($max-widths: $container-max-widths, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint, $container-max-width in $max-widths {\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      max-width: $container-max-width;\n    }\n  }\n}\n\n@mixin make-row($gutter: $grid-gutter-width) {\n  display: flex;\n  flex-wrap: wrap;\n  margin-right: -$gutter / 2;\n  margin-left: -$gutter / 2;\n}\n\n@mixin make-col-ready($gutter: $grid-gutter-width) {\n  position: relative;\n  // Prevent columns from becoming too narrow when at smaller grid tiers by\n  // always setting `width: 100%;`. This works because we use `flex` values\n  // later on to override this initial width.\n  width: 100%;\n  padding-right: $gutter / 2;\n  padding-left: $gutter / 2;\n}\n\n@mixin make-col($size, $columns: $grid-columns) {\n  flex: 0 0 percentage($size / $columns);\n  // Add a `max-width` to ensure content within each column does not blow out\n  // the width of the column. Applies to IE10+ and Firefox. Chrome and Safari\n  // do not appear to require this.\n  max-width: percentage($size / $columns);\n}\n\n@mixin make-col-auto() {\n  flex: 0 0 auto;\n  width: auto;\n  max-width: 100%; // Reset earlier grid tiers\n}\n\n@mixin make-col-offset($size, $columns: $grid-columns) {\n  $num: $size / $columns;\n  margin-left: if($num == 0, 0, percentage($num));\n}\n\n// Row columns\n//\n// Specify on a parent element(e.g., .row) to force immediate children into NN\n// numberof columns. Supports wrapping to new lines, but does not do a Masonry\n// style grid.\n@mixin row-cols($count) {\n  & > * {\n    flex: 0 0 100% / $count;\n    max-width: 100% / $count;\n  }\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// Framework grid generation\n//\n// Used only by <PERSON><PERSON><PERSON> to generate the correct number of grid classes given\n// any value of `$grid-columns`.\n\n@mixin make-grid-columns($columns: $grid-columns, $gutter: $grid-gutter-width, $breakpoints: $grid-breakpoints) {\n  // Common properties for all breakpoints\n  %grid-column {\n    position: relative;\n    width: 100%;\n    padding-right: $gutter / 2;\n    padding-left: $gutter / 2;\n  }\n\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    // Allow columns to stretch full width below their breakpoints\n    @for $i from 1 through $columns {\n      .col#{$infix}-#{$i} {\n        @extend %grid-column;\n      }\n    }\n    .col#{$infix},\n    .col#{$infix}-auto {\n      @extend %grid-column;\n    }\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      // Provide basic `.col-{bp}` classes for equal-width flexbox columns\n      .col#{$infix} {\n        flex-basis: 0;\n        flex-grow: 1;\n        max-width: 100%;\n      }\n\n      @for $i from 1 through $grid-row-columns {\n        .row-cols#{$infix}-#{$i} {\n          @include row-cols($i);\n        }\n      }\n\n      .col#{$infix}-auto {\n        @include make-col-auto();\n      }\n\n      @for $i from 1 through $columns {\n        .col#{$infix}-#{$i} {\n          @include make-col($i, $columns);\n        }\n      }\n\n      .order#{$infix}-first { order: -1; }\n\n      .order#{$infix}-last { order: $columns + 1; }\n\n      @for $i from 0 through $columns {\n        .order#{$infix}-#{$i} { order: $i; }\n      }\n\n      // `$columns - 1` because offsetting by the width of an entire row isn't possible\n      @for $i from 0 through ($columns - 1) {\n        @if not ($infix == \"\" and $i == 0) { // Avoid emitting useless .offset-0\n          .offset#{$infix}-#{$i} {\n            @include make-col-offset($i, $columns);\n          }\n        }\n      }\n    }\n  }\n}\n", "//\n// Basic Bootstrap table\n//\n\n.table {\n  width: 100%;\n  margin-bottom: $spacer;\n  color: $table-color;\n  background-color: $table-bg; // Reset for nesting within parents with `background-color`.\n\n  th,\n  td {\n    padding: $table-cell-padding;\n    vertical-align: top;\n    border-top: $table-border-width solid $table-border-color;\n  }\n\n  thead th {\n    vertical-align: bottom;\n    border-bottom: (2 * $table-border-width) solid $table-border-color;\n  }\n\n  tbody + tbody {\n    border-top: (2 * $table-border-width) solid $table-border-color;\n  }\n}\n\n\n//\n// Condensed table w/ half padding\n//\n\n.table-sm {\n  th,\n  td {\n    padding: $table-cell-padding-sm;\n  }\n}\n\n\n// Border versions\n//\n// Add or remove borders all around the table and between all the columns.\n\n.table-bordered {\n  border: $table-border-width solid $table-border-color;\n\n  th,\n  td {\n    border: $table-border-width solid $table-border-color;\n  }\n\n  thead {\n    th,\n    td {\n      border-bottom-width: 2 * $table-border-width;\n    }\n  }\n}\n\n.table-borderless {\n  th,\n  td,\n  thead th,\n  tbody + tbody {\n    border: 0;\n  }\n}\n\n// Zebra-striping\n//\n// Default zebra-stripe styles (alternating gray and transparent backgrounds)\n\n.table-striped {\n  tbody tr:nth-of-type(#{$table-striped-order}) {\n    background-color: $table-accent-bg;\n  }\n}\n\n\n// Hover effect\n//\n// Placed here since it has to come after the potential zebra striping\n\n.table-hover {\n  tbody tr {\n    @include hover() {\n      color: $table-hover-color;\n      background-color: $table-hover-bg;\n    }\n  }\n}\n\n\n// Table backgrounds\n//\n// Exact selectors below required to override `.table-striped` and prevent\n// inheritance to nested tables.\n\n@each $color, $value in $theme-colors {\n  @include table-row-variant($color, theme-color-level($color, $table-bg-level), theme-color-level($color, $table-border-level));\n}\n\n@include table-row-variant(active, $table-active-bg);\n\n\n// Dark styles\n//\n// Same table markup, but inverted color scheme: dark background and light text.\n\n// stylelint-disable-next-line no-duplicate-selectors\n.table {\n  .thead-dark {\n    th {\n      color: $table-dark-color;\n      background-color: $table-dark-bg;\n      border-color: $table-dark-border-color;\n    }\n  }\n\n  .thead-light {\n    th {\n      color: $table-head-color;\n      background-color: $table-head-bg;\n      border-color: $table-border-color;\n    }\n  }\n}\n\n.table-dark {\n  color: $table-dark-color;\n  background-color: $table-dark-bg;\n\n  th,\n  td,\n  thead th {\n    border-color: $table-dark-border-color;\n  }\n\n  &.table-bordered {\n    border: 0;\n  }\n\n  &.table-striped {\n    tbody tr:nth-of-type(#{$table-striped-order}) {\n      background-color: $table-dark-accent-bg;\n    }\n  }\n\n  &.table-hover {\n    tbody tr {\n      @include hover() {\n        color: $table-dark-hover-color;\n        background-color: $table-dark-hover-bg;\n      }\n    }\n  }\n}\n\n\n// Responsive tables\n//\n// Generate series of `.table-responsive-*` classes for configuring the screen\n// size of where your table will overflow.\n\n.table-responsive {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $next: breakpoint-next($breakpoint, $grid-breakpoints);\n    $infix: breakpoint-infix($next, $grid-breakpoints);\n\n    &#{$infix} {\n      @include media-breakpoint-down($breakpoint) {\n        display: block;\n        width: 100%;\n        overflow-x: auto;\n        -webkit-overflow-scrolling: touch;\n\n        // Prevent double border on horizontal scroll due to use of `display: block;`\n        > .table-bordered {\n          border: 0;\n        }\n      }\n    }\n  }\n}\n", "// Tables\n\n@mixin table-row-variant($state, $background, $border: null) {\n  // Exact selectors below required to override `.table-striped` and prevent\n  // inheritance to nested tables.\n  .table-#{$state} {\n    &,\n    > th,\n    > td {\n      background-color: $background;\n    }\n\n    @if $border != null {\n      th,\n      td,\n      thead th,\n      tbody + tbody {\n        border-color: $border;\n      }\n    }\n  }\n\n  // Hover states for `.table-hover`\n  // Note: this is not available for cells or rows within `thead` or `tfoot`.\n  .table-hover {\n    $hover-background: darken($background, 5%);\n\n    .table-#{$state} {\n      @include hover() {\n        background-color: $hover-background;\n\n        > td,\n        > th {\n          background-color: $hover-background;\n        }\n      }\n    }\n  }\n}\n", "// Bootstrap functions\n//\n// Utility mixins and functions for evaluating source code across our variables, maps, and mixins.\n\n// Ascending\n// Used to evaluate Sass maps like our grid breakpoints.\n@mixin _assert-ascending($map, $map-name) {\n  $prev-key: null;\n  $prev-num: null;\n  @each $key, $num in $map {\n    @if $prev-num == null or unit($num) == \"%\" or unit($prev-num) == \"%\" {\n      // Do nothing\n    } @else if not comparable($prev-num, $num) {\n      @warn \"Potentially invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} whose unit makes it incomparable to #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    } @else if $prev-num >= $num {\n      @warn \"Invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} which isn't greater than #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    }\n    $prev-key: $key;\n    $prev-num: $num;\n  }\n}\n\n// Starts at zero\n// Used to ensure the min-width of the lowest breakpoint starts at 0.\n@mixin _assert-starts-at-zero($map, $map-name: \"$grid-breakpoints\") {\n  $values: map-values($map);\n  $first-value: nth($values, 1);\n  @if $first-value != 0 {\n    @warn \"First breakpoint in #{$map-name} must start at 0, but starts at #{$first-value}.\";\n  }\n}\n\n// Replace `$search` with `$replace` in `$string`\n// Used on our SVG icon backgrounds for custom forms.\n//\n// <AUTHOR> Giraudel\n// @param {String} $string - Initial string\n// @param {String} $search - Substring to replace\n// @param {String} $replace ('') - New value\n// @return {String} - Updated string\n@function str-replace($string, $search, $replace: \"\") {\n  $index: str-index($string, $search);\n\n  @if $index {\n    @return str-slice($string, 1, $index - 1) + $replace + str-replace(str-slice($string, $index + str-length($search)), $search, $replace);\n  }\n\n  @return $string;\n}\n\n// See https://codepen.io/kevinweber/pen/dXWoRw\n@function escape-svg($string) {\n  @if str-index($string, \"data:image/svg+xml\") {\n    @each $char, $encoded in $escaped-characters {\n      $string: str-replace($string, $char, $encoded);\n    }\n  }\n\n  @return $string;\n}\n\n// Color contrast\n@function color-yiq($color, $dark: $yiq-text-dark, $light: $yiq-text-light) {\n  $r: red($color);\n  $g: green($color);\n  $b: blue($color);\n\n  $yiq: (($r * 299) + ($g * 587) + ($b * 114)) / 1000;\n\n  @if ($yiq >= $yiq-contrasted-threshold) {\n    @return $dark;\n  } @else {\n    @return $light;\n  }\n}\n\n// Retrieve color Sass maps\n@function color($key: \"blue\") {\n  @return map-get($colors, $key);\n}\n\n@function theme-color($key: \"primary\") {\n  @return map-get($theme-colors, $key);\n}\n\n@function gray($key: \"100\") {\n  @return map-get($grays, $key);\n}\n\n// Request a theme color level\n@function theme-color-level($color-name: \"primary\", $level: 0) {\n  $color: theme-color($color-name);\n  $color-base: if($level > 0, $black, $white);\n  $level: abs($level);\n\n  @return mix($color-base, $color, $level * $theme-color-interval);\n}\n\n// Return valid calc\n@function add($value1, $value2, $return-calc: true) {\n  @if $value1 == null {\n    @return $value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 + $value2;\n  }\n\n  @return if($return-calc == true, calc(#{$value1} + #{$value2}), $value1 + unquote(\" + \") + $value2);\n}\n\n@function subtract($value1, $value2, $return-calc: true) {\n  @if $value1 == null and $value2 == null {\n    @return null;\n  }\n\n  @if $value1 == null {\n    @return -$value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 - $value2;\n  }\n\n  @return if($return-calc == true, calc(#{$value1} - #{$value2}), $value1 + unquote(\" - \") + $value2);\n}\n", "// stylelint-disable selector-no-qualifying-type\n\n//\n// Textual form controls\n//\n\n.form-control {\n  display: block;\n  width: 100%;\n  height: $input-height;\n  padding: $input-padding-y $input-padding-x;\n  font-family: $input-font-family;\n  @include font-size($input-font-size);\n  font-weight: $input-font-weight;\n  line-height: $input-line-height;\n  color: $input-color;\n  background-color: $input-bg;\n  background-clip: padding-box;\n  border: $input-border-width solid $input-border-color;\n\n  // Note: This has no effect on <select>s in some browsers, due to the limited stylability of `<select>`s in CSS.\n  @include border-radius($input-border-radius, 0);\n\n  @include box-shadow($input-box-shadow);\n  @include transition($input-transition);\n\n  // Unstyle the caret on `<select>`s in IE10+.\n  &::-ms-expand {\n    background-color: transparent;\n    border: 0;\n  }\n\n  // Remove select outline from select box in FF\n  &:-moz-focusring {\n    color: transparent;\n    text-shadow: 0 0 0 $input-color;\n  }\n\n  // Customize the `:focus` state to imitate native WebKit styles.\n  @include form-control-focus($ignore-warning: true);\n\n  // Placeholder\n  &::placeholder {\n    color: $input-placeholder-color;\n    // Override Firefox's unusual default opacity; see https://github.com/twbs/bootstrap/pull/11526.\n    opacity: 1;\n  }\n\n  // Disabled and read-only inputs\n  //\n  // HTML5 says that controls under a fieldset > legend:first-child won't be\n  // disabled if the fieldset is disabled. Due to implementation difficulty, we\n  // don't honor that edge case; we style them as disabled anyway.\n  &:disabled,\n  &[readonly] {\n    background-color: $input-disabled-bg;\n    // iOS fix for unreadable disabled content; see https://github.com/twbs/bootstrap/issues/11655.\n    opacity: 1;\n  }\n}\n\nselect.form-control {\n  &:focus::-ms-value {\n    // Suppress the nested default white text on blue background highlight given to\n    // the selected option text when the (still closed) <select> receives focus\n    // in IE and (under certain conditions) Edge, as it looks bad and cannot be made to\n    // match the appearance of the native widget.\n    // See https://github.com/twbs/bootstrap/issues/19398.\n    color: $input-color;\n    background-color: $input-bg;\n  }\n}\n\n// Make file inputs better match text inputs by forcing them to new lines.\n.form-control-file,\n.form-control-range {\n  display: block;\n  width: 100%;\n}\n\n\n//\n// Labels\n//\n\n// For use with horizontal and inline forms, when you need the label (or legend)\n// text to align with the form controls.\n.col-form-label {\n  padding-top: add($input-padding-y, $input-border-width);\n  padding-bottom: add($input-padding-y, $input-border-width);\n  margin-bottom: 0; // Override the `<label>/<legend>` default\n  @include font-size(inherit); // Override the `<legend>` default\n  line-height: $input-line-height;\n}\n\n.col-form-label-lg {\n  padding-top: add($input-padding-y-lg, $input-border-width);\n  padding-bottom: add($input-padding-y-lg, $input-border-width);\n  @include font-size($input-font-size-lg);\n  line-height: $input-line-height-lg;\n}\n\n.col-form-label-sm {\n  padding-top: add($input-padding-y-sm, $input-border-width);\n  padding-bottom: add($input-padding-y-sm, $input-border-width);\n  @include font-size($input-font-size-sm);\n  line-height: $input-line-height-sm;\n}\n\n\n// Readonly controls as plain text\n//\n// Apply class to a readonly input to make it appear like regular plain\n// text (without any border, background color, focus indicator)\n\n.form-control-plaintext {\n  display: block;\n  width: 100%;\n  padding: $input-padding-y 0;\n  margin-bottom: 0; // match inputs if this class comes on inputs with default margins\n  @include font-size($input-font-size);\n  line-height: $input-line-height;\n  color: $input-plaintext-color;\n  background-color: transparent;\n  border: solid transparent;\n  border-width: $input-border-width 0;\n\n  &.form-control-sm,\n  &.form-control-lg {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n\n\n// Form control sizing\n//\n// Build on `.form-control` with modifier classes to decrease or increase the\n// height and font-size of form controls.\n//\n// Repeated in `_input_group.scss` to avoid Sass extend issues.\n\n.form-control-sm {\n  height: $input-height-sm;\n  padding: $input-padding-y-sm $input-padding-x-sm;\n  @include font-size($input-font-size-sm);\n  line-height: $input-line-height-sm;\n  @include border-radius($input-border-radius-sm);\n}\n\n.form-control-lg {\n  height: $input-height-lg;\n  padding: $input-padding-y-lg $input-padding-x-lg;\n  @include font-size($input-font-size-lg);\n  line-height: $input-line-height-lg;\n  @include border-radius($input-border-radius-lg);\n}\n\n// stylelint-disable-next-line no-duplicate-selectors\nselect.form-control {\n  &[size],\n  &[multiple] {\n    height: auto;\n  }\n}\n\ntextarea.form-control {\n  height: auto;\n}\n\n// Form groups\n//\n// Designed to help with the organization and spacing of vertical forms. For\n// horizontal forms, use the predefined grid classes.\n\n.form-group {\n  margin-bottom: $form-group-margin-bottom;\n}\n\n.form-text {\n  display: block;\n  margin-top: $form-text-margin-top;\n}\n\n\n// Form grid\n//\n// Special replacement for our grid system's `.row` for tighter form layouts.\n\n.form-row {\n  display: flex;\n  flex-wrap: wrap;\n  margin-right: -$form-grid-gutter-width / 2;\n  margin-left: -$form-grid-gutter-width / 2;\n\n  > .col,\n  > [class*=\"col-\"] {\n    padding-right: $form-grid-gutter-width / 2;\n    padding-left: $form-grid-gutter-width / 2;\n  }\n}\n\n\n// Checkboxes and radios\n//\n// Indent the labels to position radios/checkboxes as hanging controls.\n\n.form-check {\n  position: relative;\n  display: block;\n  padding-left: $form-check-input-gutter;\n}\n\n.form-check-input {\n  position: absolute;\n  margin-top: $form-check-input-margin-y;\n  margin-left: -$form-check-input-gutter;\n\n  // Use [disabled] and :disabled for workaround https://github.com/twbs/bootstrap/issues/28247\n  &[disabled] ~ .form-check-label,\n  &:disabled ~ .form-check-label {\n    color: $text-muted;\n  }\n}\n\n.form-check-label {\n  margin-bottom: 0; // Override default `<label>` bottom margin\n}\n\n.form-check-inline {\n  display: inline-flex;\n  align-items: center;\n  padding-left: 0; // Override base .form-check\n  margin-right: $form-check-inline-margin-x;\n\n  // Undo .form-check-input defaults and add some `margin-right`.\n  .form-check-input {\n    position: static;\n    margin-top: 0;\n    margin-right: $form-check-inline-input-margin-x;\n    margin-left: 0;\n  }\n}\n\n\n// Form validation\n//\n// Provide feedback to users when form field values are valid or invalid. Works\n// primarily for client-side validation via scoped `:invalid` and `:valid`\n// pseudo-classes but also includes `.is-invalid` and `.is-valid` classes for\n// server side validation.\n\n@each $state, $data in $form-validation-states {\n  @include form-validation-state($state, map-get($data, color), map-get($data, icon));\n}\n\n// Inline forms\n//\n// Make forms appear inline(-block) by adding the `.form-inline` class. Inline\n// forms begin stacked on extra small (mobile) devices and then go inline when\n// viewports reach <768px.\n//\n// Requires wrapping inputs and labels with `.form-group` for proper display of\n// default HTML form controls and our custom form controls (e.g., input groups).\n\n.form-inline {\n  display: flex;\n  flex-flow: row wrap;\n  align-items: center; // Prevent shorter elements from growing to same height as others (e.g., small buttons growing to normal sized button height)\n\n  // Because we use flex, the initial sizing of checkboxes is collapsed and\n  // doesn't occupy the full-width (which is what we want for xs grid tier),\n  // so we force that here.\n  .form-check {\n    width: 100%;\n  }\n\n  // Kick in the inline\n  @include media-breakpoint-up(sm) {\n    label {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-bottom: 0;\n    }\n\n    // Inline-block all the things for \"inline\"\n    .form-group {\n      display: flex;\n      flex: 0 0 auto;\n      flex-flow: row wrap;\n      align-items: center;\n      margin-bottom: 0;\n    }\n\n    // Allow folks to *not* use `.form-group`\n    .form-control {\n      display: inline-block;\n      width: auto; // Prevent labels from stacking above inputs in `.form-group`\n      vertical-align: middle;\n    }\n\n    // Make static controls behave like regular ones\n    .form-control-plaintext {\n      display: inline-block;\n    }\n\n    .input-group,\n    .custom-select {\n      width: auto;\n    }\n\n    // Remove default margin on radios/checkboxes that were used for stacking, and\n    // then undo the floating of radios and checkboxes to match.\n    .form-check {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      width: auto;\n      padding-left: 0;\n    }\n    .form-check-input {\n      position: relative;\n      flex-shrink: 0;\n      margin-top: 0;\n      margin-right: $form-check-input-margin-x;\n      margin-left: 0;\n    }\n\n    .custom-control {\n      align-items: center;\n      justify-content: center;\n    }\n    .custom-control-label {\n      margin-bottom: 0;\n    }\n  }\n}\n", "// stylelint-disable property-blacklist\n@mixin transition($transition...) {\n  @if $enable-transitions {\n    @if length($transition) == 0 {\n      transition: $transition-base;\n    } @else {\n      transition: $transition;\n    }\n  }\n\n  @if $enable-prefers-reduced-motion-media-query {\n    @media (prefers-reduced-motion: reduce) {\n      transition: none;\n    }\n  }\n}\n", "// Form control focus state\n//\n// Generate a customized focus state and for any input with the specified color,\n// which defaults to the `$input-focus-border-color` variable.\n//\n// We highly encourage you to not customize the default value, but instead use\n// this to tweak colors on an as-needed basis. This aesthetic change is based on\n// WebKit's default styles, but applicable to a wider range of browsers. Its\n// usability and accessibility should be taken into account with any change.\n//\n// Example usage: change the default blue border and shadow to white for better\n// contrast against a dark gray background.\n@mixin form-control-focus($ignore-warning: false) {\n  &:focus {\n    color: $input-focus-color;\n    background-color: $input-focus-bg;\n    border-color: $input-focus-border-color;\n    outline: 0;\n    // Avoid using mixin so we can pass custom focus shadow properly\n    @if $enable-shadows {\n      box-shadow: $input-box-shadow, $input-focus-box-shadow;\n    } @else {\n      box-shadow: $input-focus-box-shadow;\n    }\n  }\n  @include deprecate(\"The `form-control-focus()` mixin\", \"v4.4.0\", \"v5\", $ignore-warning);\n}\n\n// This mixin uses an `if()` technique to be compatible with Dart Sass\n// See https://github.com/sass/sass/issues/1873#issuecomment-********* for more details\n@mixin form-validation-state-selector($state) {\n  @if ($state == \"valid\" or $state == \"invalid\") {\n    .was-validated #{if(&, \"&\", \"\")}:#{$state},\n    #{if(&, \"&\", \"\")}.is-#{$state} {\n      @content;\n    }\n  } @else {\n    #{if(&, \"&\", \"\")}.is-#{$state} {\n      @content;\n    }\n  }\n}\n\n@mixin form-validation-state($state, $color, $icon) {\n  .#{$state}-feedback {\n    display: none;\n    width: 100%;\n    margin-top: $form-feedback-margin-top;\n    @include font-size($form-feedback-font-size);\n    color: $color;\n  }\n\n  .#{$state}-tooltip {\n    position: absolute;\n    top: 100%;\n    z-index: 5;\n    display: none;\n    max-width: 100%; // Contain to parent when possible\n    padding: $form-feedback-tooltip-padding-y $form-feedback-tooltip-padding-x;\n    margin-top: .1rem;\n    @include font-size($form-feedback-tooltip-font-size);\n    line-height: $form-feedback-tooltip-line-height;\n    color: color-yiq($color);\n    background-color: rgba($color, $form-feedback-tooltip-opacity);\n    @include border-radius($form-feedback-tooltip-border-radius);\n  }\n\n  @include form-validation-state-selector($state) {\n    ~ .#{$state}-feedback,\n    ~ .#{$state}-tooltip {\n      display: block;\n    }\n  }\n\n  .form-control {\n    @include form-validation-state-selector($state) {\n      border-color: $color;\n\n      @if $enable-validation-icons {\n        padding-right: $input-height-inner;\n        background-image: escape-svg($icon);\n        background-repeat: no-repeat;\n        background-position: right $input-height-inner-quarter center;\n        background-size: $input-height-inner-half $input-height-inner-half;\n      }\n\n      &:focus {\n        border-color: $color;\n        box-shadow: 0 0 0 $input-focus-width rgba($color, .25);\n      }\n    }\n  }\n\n  // stylelint-disable-next-line selector-no-qualifying-type\n  textarea.form-control {\n    @include form-validation-state-selector($state) {\n      @if $enable-validation-icons {\n        padding-right: $input-height-inner;\n        background-position: top $input-height-inner-quarter right $input-height-inner-quarter;\n      }\n    }\n  }\n\n  .custom-select {\n    @include form-validation-state-selector($state) {\n      border-color: $color;\n\n      @if $enable-validation-icons {\n        padding-right: $custom-select-feedback-icon-padding-right;\n        background: $custom-select-background, escape-svg($icon) $custom-select-bg no-repeat $custom-select-feedback-icon-position / $custom-select-feedback-icon-size;\n      }\n\n      &:focus {\n        border-color: $color;\n        box-shadow: 0 0 0 $input-focus-width rgba($color, .25);\n      }\n    }\n  }\n\n  .form-check-input {\n    @include form-validation-state-selector($state) {\n      ~ .form-check-label {\n        color: $color;\n      }\n\n      ~ .#{$state}-feedback,\n      ~ .#{$state}-tooltip {\n        display: block;\n      }\n    }\n  }\n\n  .custom-control-input {\n    @include form-validation-state-selector($state) {\n      ~ .custom-control-label {\n        color: $color;\n\n        &::before {\n          border-color: $color;\n        }\n      }\n\n      &:checked {\n        ~ .custom-control-label::before {\n          border-color: lighten($color, 10%);\n          @include gradient-bg(lighten($color, 10%));\n        }\n      }\n\n      &:focus {\n        ~ .custom-control-label::before {\n          box-shadow: 0 0 0 $input-focus-width rgba($color, .25);\n        }\n\n        &:not(:checked) ~ .custom-control-label::before {\n          border-color: $color;\n        }\n      }\n    }\n  }\n\n  // custom file\n  .custom-file-input {\n    @include form-validation-state-selector($state) {\n      ~ .custom-file-label {\n        border-color: $color;\n      }\n\n      &:focus {\n        ~ .custom-file-label {\n          border-color: $color;\n          box-shadow: 0 0 0 $input-focus-width rgba($color, .25);\n        }\n      }\n    }\n  }\n}\n", "// Gradients\n\n@mixin gradient-bg($color) {\n  @if $enable-gradients {\n    background: $color linear-gradient(180deg, mix($body-bg, $color, 15%), $color) repeat-x;\n  } @else {\n    background-color: $color;\n  }\n}\n\n// Horizontal gradient, from left to right\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-x($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to right, $start-color $start-percent, $end-color $end-percent);\n  background-repeat: repeat-x;\n}\n\n// Vertical gradient, from top to bottom\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-y($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to bottom, $start-color $start-percent, $end-color $end-percent);\n  background-repeat: repeat-x;\n}\n\n@mixin gradient-directional($start-color: $gray-700, $end-color: $gray-800, $deg: 45deg) {\n  background-image: linear-gradient($deg, $start-color, $end-color);\n  background-repeat: repeat-x;\n}\n@mixin gradient-x-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient(to right, $start-color, $mid-color $color-stop, $end-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-y-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient($start-color, $mid-color $color-stop, $end-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-radial($inner-color: $gray-700, $outer-color: $gray-800) {\n  background-image: radial-gradient(circle, $inner-color, $outer-color);\n  background-repeat: no-repeat;\n}\n@mixin gradient-striped($color: rgba($white, .15), $angle: 45deg) {\n  background-image: linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);\n}\n", "// stylelint-disable selector-no-qualifying-type\n\n//\n// Base styles\n//\n\n.btn {\n  display: inline-block;\n  font-family: $btn-font-family;\n  font-weight: $btn-font-weight;\n  color: $body-color;\n  text-align: center;\n  white-space: $btn-white-space;\n  vertical-align: middle;\n  cursor: if($enable-pointer-cursor-for-buttons, pointer, null);\n  user-select: none;\n  background-color: transparent;\n  border: $btn-border-width solid transparent;\n  @include button-size($btn-padding-y, $btn-padding-x, $btn-font-size, $btn-line-height, $btn-border-radius);\n  @include transition($btn-transition);\n\n  @include hover() {\n    color: $body-color;\n    text-decoration: none;\n  }\n\n  &:focus,\n  &.focus {\n    outline: 0;\n    box-shadow: $btn-focus-box-shadow;\n  }\n\n  // Disabled comes first so active can properly restyle\n  &.disabled,\n  &:disabled {\n    opacity: $btn-disabled-opacity;\n    @include box-shadow(none);\n  }\n\n  &:not(:disabled):not(.disabled):active,\n  &:not(:disabled):not(.disabled).active {\n    @include box-shadow($btn-active-box-shadow);\n\n    &:focus {\n      @include box-shadow($btn-focus-box-shadow, $btn-active-box-shadow);\n    }\n  }\n}\n\n// Future-proof disabling of clicks on `<a>` elements\na.btn.disabled,\nfieldset:disabled a.btn {\n  pointer-events: none;\n}\n\n\n//\n// Alternate buttons\n//\n\n@each $color, $value in $theme-colors {\n  .btn-#{$color} {\n    @include button-variant($value, $value);\n  }\n}\n\n@each $color, $value in $theme-colors {\n  .btn-outline-#{$color} {\n    @include button-outline-variant($value);\n  }\n}\n\n\n//\n// Link buttons\n//\n\n// Make a button look and behave like a link\n.btn-link {\n  font-weight: $font-weight-normal;\n  color: $link-color;\n  text-decoration: $link-decoration;\n\n  @include hover() {\n    color: $link-hover-color;\n    text-decoration: $link-hover-decoration;\n  }\n\n  &:focus,\n  &.focus {\n    text-decoration: $link-hover-decoration;\n    box-shadow: none;\n  }\n\n  &:disabled,\n  &.disabled {\n    color: $btn-link-disabled-color;\n    pointer-events: none;\n  }\n\n  // No need for an active state here\n}\n\n\n//\n// Button Sizes\n//\n\n.btn-lg {\n  @include button-size($btn-padding-y-lg, $btn-padding-x-lg, $btn-font-size-lg, $btn-line-height-lg, $btn-border-radius-lg);\n}\n\n.btn-sm {\n  @include button-size($btn-padding-y-sm, $btn-padding-x-sm, $btn-font-size-sm, $btn-line-height-sm, $btn-border-radius-sm);\n}\n\n\n//\n// Block button\n//\n\n.btn-block {\n  display: block;\n  width: 100%;\n\n  // Vertically space out multiple block buttons\n  + .btn-block {\n    margin-top: $btn-block-spacing-y;\n  }\n}\n\n// Specificity overrides\ninput[type=\"submit\"],\ninput[type=\"reset\"],\ninput[type=\"button\"] {\n  &.btn-block {\n    width: 100%;\n  }\n}\n", "// Button variants\n//\n// Easily pump out default styles, as well as :hover, :focus, :active,\n// and disabled options for all buttons\n\n@mixin button-variant($background, $border, $hover-background: darken($background, 7.5%), $hover-border: darken($border, 10%), $active-background: darken($background, 10%), $active-border: darken($border, 12.5%)) {\n  color: color-yiq($background);\n  @include gradient-bg($background);\n  border-color: $border;\n  @include box-shadow($btn-box-shadow);\n\n  @include hover() {\n    color: color-yiq($hover-background);\n    @include gradient-bg($hover-background);\n    border-color: $hover-border;\n  }\n\n  &:focus,\n  &.focus {\n    color: color-yiq($hover-background);\n    @include gradient-bg($hover-background);\n    border-color: $hover-border;\n    // Avoid using mixin so we can pass custom focus shadow properly\n    @if $enable-shadows {\n      box-shadow: $btn-box-shadow, 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n    } @else {\n      box-shadow: 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n    }\n  }\n\n  // Disabled comes first so active can properly restyle\n  &.disabled,\n  &:disabled {\n    color: color-yiq($background);\n    background-color: $background;\n    border-color: $border;\n    // Remove CSS gradients if they're enabled\n    @if $enable-gradients {\n      background-image: none;\n    }\n  }\n\n  &:not(:disabled):not(.disabled):active,\n  &:not(:disabled):not(.disabled).active,\n  .show > &.dropdown-toggle {\n    color: color-yiq($active-background);\n    background-color: $active-background;\n    @if $enable-gradients {\n      background-image: none; // Remove the gradient for the pressed/active state\n    }\n    border-color: $active-border;\n\n    &:focus {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      @if $enable-shadows and $btn-active-box-shadow != none {\n        box-shadow: $btn-active-box-shadow, 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n      } @else {\n        box-shadow: 0 0 0 $btn-focus-width rgba(mix(color-yiq($background), $border, 15%), .5);\n      }\n    }\n  }\n}\n\n@mixin button-outline-variant($color, $color-hover: color-yiq($color), $active-background: $color, $active-border: $color) {\n  color: $color;\n  border-color: $color;\n\n  @include hover() {\n    color: $color-hover;\n    background-color: $active-background;\n    border-color: $active-border;\n  }\n\n  &:focus,\n  &.focus {\n    box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\n  }\n\n  &.disabled,\n  &:disabled {\n    color: $color;\n    background-color: transparent;\n  }\n\n  &:not(:disabled):not(.disabled):active,\n  &:not(:disabled):not(.disabled).active,\n  .show > &.dropdown-toggle {\n    color: color-yiq($active-background);\n    background-color: $active-background;\n    border-color: $active-border;\n\n    &:focus {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      @if $enable-shadows and $btn-active-box-shadow != none {\n        box-shadow: $btn-active-box-shadow, 0 0 0 $btn-focus-width rgba($color, .5);\n      } @else {\n        box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);\n      }\n    }\n  }\n}\n\n// Button sizes\n@mixin button-size($padding-y, $padding-x, $font-size, $line-height, $border-radius) {\n  padding: $padding-y $padding-x;\n  @include font-size($font-size);\n  line-height: $line-height;\n  // Manually declare to provide an override to the browser default\n  @include border-radius($border-radius, 0);\n}\n", "// stylelint-disable selector-no-qualifying-type\n\n// Make the div behave like a button\n.btn-group,\n.btn-group-vertical {\n  position: relative;\n  display: inline-flex;\n  vertical-align: middle; // match .btn alignment given font-size hack above\n\n  > .btn {\n    position: relative;\n    flex: 1 1 auto;\n\n    // Bring the hover, focused, and \"active\" buttons to the front to overlay\n    // the borders properly\n    @include hover() {\n      z-index: 1;\n    }\n    &:focus,\n    &:active,\n    &.active {\n      z-index: 1;\n    }\n  }\n}\n\n// Optional: Group multiple button groups together for a toolbar\n.btn-toolbar {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n\n  .input-group {\n    width: auto;\n  }\n}\n\n.btn-group {\n  // Prevent double borders when buttons are next to each other\n  > .btn:not(:first-child),\n  > .btn-group:not(:first-child) {\n    margin-left: -$btn-border-width;\n  }\n\n  // Reset rounded corners\n  > .btn:not(:last-child):not(.dropdown-toggle),\n  > .btn-group:not(:last-child) > .btn {\n    @include border-right-radius(0);\n  }\n\n  > .btn:not(:first-child),\n  > .btn-group:not(:first-child) > .btn {\n    @include border-left-radius(0);\n  }\n}\n\n// Sizing\n//\n// Remix the default button sizing classes into new ones for easier manipulation.\n\n.btn-group-sm > .btn { @extend .btn-sm; }\n.btn-group-lg > .btn { @extend .btn-lg; }\n\n\n//\n// Split button dropdowns\n//\n\n.dropdown-toggle-split {\n  padding-right: $btn-padding-x * .75;\n  padding-left: $btn-padding-x * .75;\n\n  &::after,\n  .dropup &::after,\n  .dropright &::after {\n    margin-left: 0;\n  }\n\n  .dropleft &::before {\n    margin-right: 0;\n  }\n}\n\n.btn-sm + .dropdown-toggle-split {\n  padding-right: $btn-padding-x-sm * .75;\n  padding-left: $btn-padding-x-sm * .75;\n}\n\n.btn-lg + .dropdown-toggle-split {\n  padding-right: $btn-padding-x-lg * .75;\n  padding-left: $btn-padding-x-lg * .75;\n}\n\n\n// The clickable button for toggling the menu\n// Set the same inset shadow as the :active state\n.btn-group.show .dropdown-toggle {\n  @include box-shadow($btn-active-box-shadow);\n\n  // Show no shadow for `.btn-link` since it has no other button styles.\n  &.btn-link {\n    @include box-shadow(none);\n  }\n}\n\n\n//\n// Vertical button groups\n//\n\n.btn-group-vertical {\n  flex-direction: column;\n  align-items: flex-start;\n  justify-content: center;\n\n  > .btn,\n  > .btn-group {\n    width: 100%;\n  }\n\n  > .btn:not(:first-child),\n  > .btn-group:not(:first-child) {\n    margin-top: -$btn-border-width;\n  }\n\n  // Reset rounded corners\n  > .btn:not(:last-child):not(.dropdown-toggle),\n  > .btn-group:not(:last-child) > .btn {\n    @include border-bottom-radius(0);\n  }\n\n  > .btn:not(:first-child),\n  > .btn-group:not(:first-child) > .btn {\n    @include border-top-radius(0);\n  }\n}\n\n\n// Checkbox and radio options\n//\n// In order to support the browser's form validation feedback, powered by the\n// `required` attribute, we have to \"hide\" the inputs via `clip`. We cannot use\n// `display: none;` or `visibility: hidden;` as that also hides the popover.\n// Simply visually hiding the inputs via `opacity` would leave them clickable in\n// certain cases which is prevented by using `clip` and `pointer-events`.\n// This way, we ensure a DOM element is visible to position the popover from.\n//\n// See https://github.com/twbs/bootstrap/pull/12794 and\n// https://github.com/twbs/bootstrap/pull/14559 for more information.\n\n.btn-group-toggle {\n  > .btn,\n  > .btn-group > .btn {\n    margin-bottom: 0; // Override default `<label>` value\n\n    input[type=\"radio\"],\n    input[type=\"checkbox\"] {\n      position: absolute;\n      clip: rect(0, 0, 0, 0);\n      pointer-events: none;\n    }\n  }\n}\n", ".fade {\n  @include transition($transition-fade);\n\n  &:not(.show) {\n    opacity: 0;\n  }\n}\n\n.collapse {\n  &:not(.show) {\n    display: none;\n  }\n}\n\n.collapsing {\n  position: relative;\n  height: 0;\n  overflow: hidden;\n  @include transition($transition-collapse);\n}\n", "// The dropdown wrapper (`<div>`)\n.dropup,\n.dropright,\n.dropdown,\n.dropleft {\n  position: relative;\n}\n\n.dropdown-toggle {\n  white-space: nowrap;\n\n  // Generate the caret automatically\n  @include caret();\n}\n\n// The dropdown menu\n.dropdown-menu {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  z-index: $zindex-dropdown;\n  display: none; // none by default, but block on \"open\" of the menu\n  float: left;\n  min-width: $dropdown-min-width;\n  padding: $dropdown-padding-y 0;\n  margin: $dropdown-spacer 0 0; // override default ul\n  @include font-size($dropdown-font-size);\n  color: $dropdown-color;\n  text-align: left; // Ensures proper alignment if parent has it changed (e.g., modal footer)\n  list-style: none;\n  background-color: $dropdown-bg;\n  background-clip: padding-box;\n  border: $dropdown-border-width solid $dropdown-border-color;\n  @include border-radius($dropdown-border-radius);\n  @include box-shadow($dropdown-box-shadow);\n}\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .dropdown-menu#{$infix}-left {\n      right: auto;\n      left: 0;\n    }\n\n    .dropdown-menu#{$infix}-right {\n      right: 0;\n      left: auto;\n    }\n  }\n}\n\n// Allow for dropdowns to go bottom up (aka, dropup-menu)\n// Just add .dropup after the standard .dropdown class and you're set.\n.dropup {\n  .dropdown-menu {\n    top: auto;\n    bottom: 100%;\n    margin-top: 0;\n    margin-bottom: $dropdown-spacer;\n  }\n\n  .dropdown-toggle {\n    @include caret(up);\n  }\n}\n\n.dropright {\n  .dropdown-menu {\n    top: 0;\n    right: auto;\n    left: 100%;\n    margin-top: 0;\n    margin-left: $dropdown-spacer;\n  }\n\n  .dropdown-toggle {\n    @include caret(right);\n    &::after {\n      vertical-align: 0;\n    }\n  }\n}\n\n.dropleft {\n  .dropdown-menu {\n    top: 0;\n    right: 100%;\n    left: auto;\n    margin-top: 0;\n    margin-right: $dropdown-spacer;\n  }\n\n  .dropdown-toggle {\n    @include caret(left);\n    &::before {\n      vertical-align: 0;\n    }\n  }\n}\n\n// When enabled Popper.js, reset basic dropdown position\n// stylelint-disable-next-line no-duplicate-selectors\n.dropdown-menu {\n  &[x-placement^=\"top\"],\n  &[x-placement^=\"right\"],\n  &[x-placement^=\"bottom\"],\n  &[x-placement^=\"left\"] {\n    right: auto;\n    bottom: auto;\n  }\n}\n\n// Dividers (basically an `<hr>`) within the dropdown\n.dropdown-divider {\n  @include nav-divider($dropdown-divider-bg, $dropdown-divider-margin-y, true);\n}\n\n// Links, buttons, and more within the dropdown menu\n//\n// `<button>`-specific styles are denoted with `// For <button>s`\n.dropdown-item {\n  display: block;\n  width: 100%; // For `<button>`s\n  padding: $dropdown-item-padding-y $dropdown-item-padding-x;\n  clear: both;\n  font-weight: $font-weight-normal;\n  color: $dropdown-link-color;\n  text-align: inherit; // For `<button>`s\n  white-space: nowrap; // prevent links from randomly breaking onto new lines\n  background-color: transparent; // For `<button>`s\n  border: 0; // For `<button>`s\n\n  // Prevent dropdown overflow if there's no padding\n  // See https://github.com/twbs/bootstrap/pull/27703\n  @if $dropdown-padding-y == 0 {\n    &:first-child {\n      @include border-top-radius($dropdown-inner-border-radius);\n    }\n\n    &:last-child {\n      @include border-bottom-radius($dropdown-inner-border-radius);\n    }\n  }\n\n  @include hover-focus() {\n    color: $dropdown-link-hover-color;\n    text-decoration: none;\n    @include gradient-bg($dropdown-link-hover-bg);\n  }\n\n  &.active,\n  &:active {\n    color: $dropdown-link-active-color;\n    text-decoration: none;\n    @include gradient-bg($dropdown-link-active-bg);\n  }\n\n  &.disabled,\n  &:disabled {\n    color: $dropdown-link-disabled-color;\n    pointer-events: none;\n    background-color: transparent;\n    // Remove CSS gradients if they're enabled\n    @if $enable-gradients {\n      background-image: none;\n    }\n  }\n}\n\n.dropdown-menu.show {\n  display: block;\n}\n\n// Dropdown section headers\n.dropdown-header {\n  display: block;\n  padding: $dropdown-padding-y $dropdown-item-padding-x;\n  margin-bottom: 0; // for use with heading elements\n  @include font-size($font-size-sm);\n  color: $dropdown-header-color;\n  white-space: nowrap; // as with > li > a\n}\n\n// Dropdown text\n.dropdown-item-text {\n  display: block;\n  padding: $dropdown-item-padding-y $dropdown-item-padding-x;\n  color: $dropdown-link-color;\n}\n", "@mixin caret-down() {\n  border-top: $caret-width solid;\n  border-right: $caret-width solid transparent;\n  border-bottom: 0;\n  border-left: $caret-width solid transparent;\n}\n\n@mixin caret-up() {\n  border-top: 0;\n  border-right: $caret-width solid transparent;\n  border-bottom: $caret-width solid;\n  border-left: $caret-width solid transparent;\n}\n\n@mixin caret-right() {\n  border-top: $caret-width solid transparent;\n  border-right: 0;\n  border-bottom: $caret-width solid transparent;\n  border-left: $caret-width solid;\n}\n\n@mixin caret-left() {\n  border-top: $caret-width solid transparent;\n  border-right: $caret-width solid;\n  border-bottom: $caret-width solid transparent;\n}\n\n@mixin caret($direction: down) {\n  @if $enable-caret {\n    &::after {\n      display: inline-block;\n      margin-left: $caret-spacing;\n      vertical-align: $caret-vertical-align;\n      content: \"\";\n      @if $direction == down {\n        @include caret-down();\n      } @else if $direction == up {\n        @include caret-up();\n      } @else if $direction == right {\n        @include caret-right();\n      }\n    }\n\n    @if $direction == left {\n      &::after {\n        display: none;\n      }\n\n      &::before {\n        display: inline-block;\n        margin-right: $caret-spacing;\n        vertical-align: $caret-vertical-align;\n        content: \"\";\n        @include caret-left();\n      }\n    }\n\n    &:empty::after {\n      margin-left: 0;\n    }\n  }\n}\n", "// Horizontal dividers\n//\n// Dividers (basically an hr) within dropdowns and nav lists\n\n@mixin nav-divider($color: $nav-divider-color, $margin-y: $nav-divider-margin-y, $ignore-warning: false) {\n  height: 0;\n  margin: $margin-y 0;\n  overflow: hidden;\n  border-top: 1px solid $color;\n  @include deprecate(\"The `nav-divider()` mixin\", \"v4.4.0\", \"v5\", $ignore-warning);\n}\n", "// stylelint-disable selector-no-qualifying-type\n\n//\n// Base styles\n//\n\n.input-group {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap; // For form validation feedback\n  align-items: stretch;\n  width: 100%;\n\n  > .form-control,\n  > .form-control-plaintext,\n  > .custom-select,\n  > .custom-file {\n    position: relative; // For focus state's z-index\n    flex: 1 1 0%;\n    min-width: 0; // https://stackoverflow.com/questions/36247140/why-dont-flex-items-shrink-past-content-size\n    margin-bottom: 0;\n\n    + .form-control,\n    + .custom-select,\n    + .custom-file {\n      margin-left: -$input-border-width;\n    }\n  }\n\n  // Bring the \"active\" form control to the top of surrounding elements\n  > .form-control:focus,\n  > .custom-select:focus,\n  > .custom-file .custom-file-input:focus ~ .custom-file-label {\n    z-index: 3;\n  }\n\n  // Bring the custom file input above the label\n  > .custom-file .custom-file-input:focus {\n    z-index: 4;\n  }\n\n  > .form-control,\n  > .custom-select {\n    &:not(:last-child) { @include border-right-radius(0); }\n    &:not(:first-child) { @include border-left-radius(0); }\n  }\n\n  // Custom file inputs have more complex markup, thus requiring different\n  // border-radius overrides.\n  > .custom-file {\n    display: flex;\n    align-items: center;\n\n    &:not(:last-child) .custom-file-label,\n    &:not(:last-child) .custom-file-label::after { @include border-right-radius(0); }\n    &:not(:first-child) .custom-file-label { @include border-left-radius(0); }\n  }\n}\n\n\n// Prepend and append\n//\n// While it requires one extra layer of HTML for each, dedicated prepend and\n// append elements allow us to 1) be less clever, 2) simplify our selectors, and\n// 3) support HTML5 form validation.\n\n.input-group-prepend,\n.input-group-append {\n  display: flex;\n\n  // Ensure buttons are always above inputs for more visually pleasing borders.\n  // This isn't needed for `.input-group-text` since it shares the same border-color\n  // as our inputs.\n  .btn {\n    position: relative;\n    z-index: 2;\n\n    &:focus {\n      z-index: 3;\n    }\n  }\n\n  .btn + .btn,\n  .btn + .input-group-text,\n  .input-group-text + .input-group-text,\n  .input-group-text + .btn {\n    margin-left: -$input-border-width;\n  }\n}\n\n.input-group-prepend { margin-right: -$input-border-width; }\n.input-group-append { margin-left: -$input-border-width; }\n\n\n// Textual addons\n//\n// Serves as a catch-all element for any text or radio/checkbox input you wish\n// to prepend or append to an input.\n\n.input-group-text {\n  display: flex;\n  align-items: center;\n  padding: $input-padding-y $input-padding-x;\n  margin-bottom: 0; // Allow use of <label> elements by overriding our default margin-bottom\n  @include font-size($input-font-size); // Match inputs\n  font-weight: $font-weight-normal;\n  line-height: $input-line-height;\n  color: $input-group-addon-color;\n  text-align: center;\n  white-space: nowrap;\n  background-color: $input-group-addon-bg;\n  border: $input-border-width solid $input-group-addon-border-color;\n  @include border-radius($input-border-radius);\n\n  // Nuke default margins from checkboxes and radios to vertically center within.\n  input[type=\"radio\"],\n  input[type=\"checkbox\"] {\n    margin-top: 0;\n  }\n}\n\n\n// Sizing\n//\n// Remix the default form control sizing classes into new ones for easier\n// manipulation.\n\n.input-group-lg > .form-control:not(textarea),\n.input-group-lg > .custom-select {\n  height: $input-height-lg;\n}\n\n.input-group-lg > .form-control,\n.input-group-lg > .custom-select,\n.input-group-lg > .input-group-prepend > .input-group-text,\n.input-group-lg > .input-group-append > .input-group-text,\n.input-group-lg > .input-group-prepend > .btn,\n.input-group-lg > .input-group-append > .btn {\n  padding: $input-padding-y-lg $input-padding-x-lg;\n  @include font-size($input-font-size-lg);\n  line-height: $input-line-height-lg;\n  @include border-radius($input-border-radius-lg);\n}\n\n.input-group-sm > .form-control:not(textarea),\n.input-group-sm > .custom-select {\n  height: $input-height-sm;\n}\n\n.input-group-sm > .form-control,\n.input-group-sm > .custom-select,\n.input-group-sm > .input-group-prepend > .input-group-text,\n.input-group-sm > .input-group-append > .input-group-text,\n.input-group-sm > .input-group-prepend > .btn,\n.input-group-sm > .input-group-append > .btn {\n  padding: $input-padding-y-sm $input-padding-x-sm;\n  @include font-size($input-font-size-sm);\n  line-height: $input-line-height-sm;\n  @include border-radius($input-border-radius-sm);\n}\n\n.input-group-lg > .custom-select,\n.input-group-sm > .custom-select {\n  padding-right: $custom-select-padding-x + $custom-select-indicator-padding;\n}\n\n\n// Prepend and append rounded corners\n//\n// These rulesets must come after the sizing ones to properly override sm and lg\n// border-radius values when extending. They're more specific than we'd like\n// with the `.input-group >` part, but without it, we cannot override the sizing.\n\n\n.input-group > .input-group-prepend > .btn,\n.input-group > .input-group-prepend > .input-group-text,\n.input-group > .input-group-append:not(:last-child) > .btn,\n.input-group > .input-group-append:not(:last-child) > .input-group-text,\n.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle),\n.input-group > .input-group-append:last-child > .input-group-text:not(:last-child) {\n  @include border-right-radius(0);\n}\n\n.input-group > .input-group-append > .btn,\n.input-group > .input-group-append > .input-group-text,\n.input-group > .input-group-prepend:not(:first-child) > .btn,\n.input-group > .input-group-prepend:not(:first-child) > .input-group-text,\n.input-group > .input-group-prepend:first-child > .btn:not(:first-child),\n.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child) {\n  @include border-left-radius(0);\n}\n", "// Embedded icons from Open Iconic.\n// Released under MIT and copyright 2014 Waybury.\n// https://useiconic.com/open\n\n\n// Checkboxes and radios\n//\n// Base class takes care of all the key behavioral aspects.\n\n.custom-control {\n  position: relative;\n  display: block;\n  min-height: $font-size-base * $line-height-base;\n  padding-left: $custom-control-gutter + $custom-control-indicator-size;\n}\n\n.custom-control-inline {\n  display: inline-flex;\n  margin-right: $custom-control-spacer-x;\n}\n\n.custom-control-input {\n  position: absolute;\n  left: 0;\n  z-index: -1; // Put the input behind the label so it doesn't overlay text\n  width: $custom-control-indicator-size;\n  height: ($font-size-base * $line-height-base + $custom-control-indicator-size) / 2;\n  opacity: 0;\n\n  &:checked ~ .custom-control-label::before {\n    color: $custom-control-indicator-checked-color;\n    border-color: $custom-control-indicator-checked-border-color;\n    @include gradient-bg($custom-control-indicator-checked-bg);\n    @include box-shadow($custom-control-indicator-checked-box-shadow);\n  }\n\n  &:focus ~ .custom-control-label::before {\n    // the mixin is not used here to make sure there is feedback\n    @if $enable-shadows {\n      box-shadow: $input-box-shadow, $input-focus-box-shadow;\n    } @else {\n      box-shadow: $custom-control-indicator-focus-box-shadow;\n    }\n  }\n\n  &:focus:not(:checked) ~ .custom-control-label::before {\n    border-color: $custom-control-indicator-focus-border-color;\n  }\n\n  &:not(:disabled):active ~ .custom-control-label::before {\n    color: $custom-control-indicator-active-color;\n    background-color: $custom-control-indicator-active-bg;\n    border-color: $custom-control-indicator-active-border-color;\n    @include box-shadow($custom-control-indicator-active-box-shadow);\n  }\n\n  // Use [disabled] and :disabled to work around https://github.com/twbs/bootstrap/issues/28247\n  &[disabled],\n  &:disabled {\n    ~ .custom-control-label {\n      color: $custom-control-label-disabled-color;\n\n      &::before {\n        background-color: $custom-control-indicator-disabled-bg;\n      }\n    }\n  }\n}\n\n// Custom control indicators\n//\n// Build the custom controls out of pseudo-elements.\n\n.custom-control-label {\n  position: relative;\n  margin-bottom: 0;\n  color: $custom-control-label-color;\n  vertical-align: top;\n  cursor: $custom-control-cursor;\n\n  // Background-color and (when enabled) gradient\n  &::before {\n    position: absolute;\n    top: ($font-size-base * $line-height-base - $custom-control-indicator-size) / 2;\n    left: -($custom-control-gutter + $custom-control-indicator-size);\n    display: block;\n    width: $custom-control-indicator-size;\n    height: $custom-control-indicator-size;\n    pointer-events: none;\n    content: \"\";\n    background-color: $custom-control-indicator-bg;\n    border: $custom-control-indicator-border-color solid $custom-control-indicator-border-width;\n    @include box-shadow($custom-control-indicator-box-shadow);\n  }\n\n  // Foreground (icon)\n  &::after {\n    position: absolute;\n    top: ($font-size-base * $line-height-base - $custom-control-indicator-size) / 2;\n    left: -($custom-control-gutter + $custom-control-indicator-size);\n    display: block;\n    width: $custom-control-indicator-size;\n    height: $custom-control-indicator-size;\n    content: \"\";\n    background: no-repeat 50% / #{$custom-control-indicator-bg-size};\n  }\n}\n\n\n// Checkboxes\n//\n// Tweak just a few things for checkboxes.\n\n.custom-checkbox {\n  .custom-control-label::before {\n    @include border-radius($custom-checkbox-indicator-border-radius);\n  }\n\n  .custom-control-input:checked ~ .custom-control-label {\n    &::after {\n      background-image: escape-svg($custom-checkbox-indicator-icon-checked);\n    }\n  }\n\n  .custom-control-input:indeterminate ~ .custom-control-label {\n    &::before {\n      border-color: $custom-checkbox-indicator-indeterminate-border-color;\n      @include gradient-bg($custom-checkbox-indicator-indeterminate-bg);\n      @include box-shadow($custom-checkbox-indicator-indeterminate-box-shadow);\n    }\n    &::after {\n      background-image: escape-svg($custom-checkbox-indicator-icon-indeterminate);\n    }\n  }\n\n  .custom-control-input:disabled {\n    &:checked ~ .custom-control-label::before {\n      background-color: $custom-control-indicator-checked-disabled-bg;\n    }\n    &:indeterminate ~ .custom-control-label::before {\n      background-color: $custom-control-indicator-checked-disabled-bg;\n    }\n  }\n}\n\n// Radios\n//\n// Tweak just a few things for radios.\n\n.custom-radio {\n  .custom-control-label::before {\n    // stylelint-disable-next-line property-blacklist\n    border-radius: $custom-radio-indicator-border-radius;\n  }\n\n  .custom-control-input:checked ~ .custom-control-label {\n    &::after {\n      background-image: escape-svg($custom-radio-indicator-icon-checked);\n    }\n  }\n\n  .custom-control-input:disabled {\n    &:checked ~ .custom-control-label::before {\n      background-color: $custom-control-indicator-checked-disabled-bg;\n    }\n  }\n}\n\n\n// switches\n//\n// Tweak a few things for switches\n\n.custom-switch {\n  padding-left: $custom-switch-width + $custom-control-gutter;\n\n  .custom-control-label {\n    &::before {\n      left: -($custom-switch-width + $custom-control-gutter);\n      width: $custom-switch-width;\n      pointer-events: all;\n      // stylelint-disable-next-line property-blacklist\n      border-radius: $custom-switch-indicator-border-radius;\n    }\n\n    &::after {\n      top: add(($font-size-base * $line-height-base - $custom-control-indicator-size) / 2, $custom-control-indicator-border-width * 2);\n      left: add(-($custom-switch-width + $custom-control-gutter), $custom-control-indicator-border-width * 2);\n      width: $custom-switch-indicator-size;\n      height: $custom-switch-indicator-size;\n      background-color: $custom-control-indicator-border-color;\n      // stylelint-disable-next-line property-blacklist\n      border-radius: $custom-switch-indicator-border-radius;\n      @include transition(transform .15s ease-in-out, $custom-forms-transition);\n    }\n  }\n\n  .custom-control-input:checked ~ .custom-control-label {\n    &::after {\n      background-color: $custom-control-indicator-bg;\n      transform: translateX($custom-switch-width - $custom-control-indicator-size);\n    }\n  }\n\n  .custom-control-input:disabled {\n    &:checked ~ .custom-control-label::before {\n      background-color: $custom-control-indicator-checked-disabled-bg;\n    }\n  }\n}\n\n\n// Select\n//\n// Replaces the browser default select with a custom one, mostly pulled from\n// https://primer.github.io/.\n//\n\n.custom-select {\n  display: inline-block;\n  width: 100%;\n  height: $custom-select-height;\n  padding: $custom-select-padding-y ($custom-select-padding-x + $custom-select-indicator-padding) $custom-select-padding-y $custom-select-padding-x;\n  font-family: $custom-select-font-family;\n  @include font-size($custom-select-font-size);\n  font-weight: $custom-select-font-weight;\n  line-height: $custom-select-line-height;\n  color: $custom-select-color;\n  vertical-align: middle;\n  background: $custom-select-bg $custom-select-background;\n  border: $custom-select-border-width solid $custom-select-border-color;\n  @include border-radius($custom-select-border-radius, 0);\n  @include box-shadow($custom-select-box-shadow);\n  appearance: none;\n\n  &:focus {\n    border-color: $custom-select-focus-border-color;\n    outline: 0;\n    @if $enable-shadows {\n      box-shadow: $custom-select-box-shadow, $custom-select-focus-box-shadow;\n    } @else {\n      box-shadow: $custom-select-focus-box-shadow;\n    }\n\n    &::-ms-value {\n      // For visual consistency with other platforms/browsers,\n      // suppress the default white text on blue background highlight given to\n      // the selected option text when the (still closed) <select> receives focus\n      // in IE and (under certain conditions) Edge.\n      // See https://github.com/twbs/bootstrap/issues/19398.\n      color: $input-color;\n      background-color: $input-bg;\n    }\n  }\n\n  &[multiple],\n  &[size]:not([size=\"1\"]) {\n    height: auto;\n    padding-right: $custom-select-padding-x;\n    background-image: none;\n  }\n\n  &:disabled {\n    color: $custom-select-disabled-color;\n    background-color: $custom-select-disabled-bg;\n  }\n\n  // Hides the default caret in IE11\n  &::-ms-expand {\n    display: none;\n  }\n\n  // Remove outline from select box in FF\n  &:-moz-focusring {\n    color: transparent;\n    text-shadow: 0 0 0 $custom-select-color;\n  }\n}\n\n.custom-select-sm {\n  height: $custom-select-height-sm;\n  padding-top: $custom-select-padding-y-sm;\n  padding-bottom: $custom-select-padding-y-sm;\n  padding-left: $custom-select-padding-x-sm;\n  @include font-size($custom-select-font-size-sm);\n}\n\n.custom-select-lg {\n  height: $custom-select-height-lg;\n  padding-top: $custom-select-padding-y-lg;\n  padding-bottom: $custom-select-padding-y-lg;\n  padding-left: $custom-select-padding-x-lg;\n  @include font-size($custom-select-font-size-lg);\n}\n\n\n// File\n//\n// Custom file input.\n\n.custom-file {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n  height: $custom-file-height;\n  margin-bottom: 0;\n}\n\n.custom-file-input {\n  position: relative;\n  z-index: 2;\n  width: 100%;\n  height: $custom-file-height;\n  margin: 0;\n  opacity: 0;\n\n  &:focus ~ .custom-file-label {\n    border-color: $custom-file-focus-border-color;\n    box-shadow: $custom-file-focus-box-shadow;\n  }\n\n  // Use [disabled] and :disabled to work around https://github.com/twbs/bootstrap/issues/28247\n  &[disabled] ~ .custom-file-label,\n  &:disabled ~ .custom-file-label {\n    background-color: $custom-file-disabled-bg;\n  }\n\n  @each $lang, $value in $custom-file-text {\n    &:lang(#{$lang}) ~ .custom-file-label::after {\n      content: $value;\n    }\n  }\n\n  ~ .custom-file-label[data-browse]::after {\n    content: attr(data-browse);\n  }\n}\n\n.custom-file-label {\n  position: absolute;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1;\n  height: $custom-file-height;\n  padding: $custom-file-padding-y $custom-file-padding-x;\n  font-family: $custom-file-font-family;\n  font-weight: $custom-file-font-weight;\n  line-height: $custom-file-line-height;\n  color: $custom-file-color;\n  background-color: $custom-file-bg;\n  border: $custom-file-border-width solid $custom-file-border-color;\n  @include border-radius($custom-file-border-radius);\n  @include box-shadow($custom-file-box-shadow);\n\n  &::after {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    z-index: 3;\n    display: block;\n    height: $custom-file-height-inner;\n    padding: $custom-file-padding-y $custom-file-padding-x;\n    line-height: $custom-file-line-height;\n    color: $custom-file-button-color;\n    content: \"Browse\";\n    @include gradient-bg($custom-file-button-bg);\n    border-left: inherit;\n    @include border-radius(0 $custom-file-border-radius $custom-file-border-radius 0);\n  }\n}\n\n// Range\n//\n// Style range inputs the same across browsers. Vendor-specific rules for pseudo\n// elements cannot be mixed. As such, there are no shared styles for focus or\n// active states on prefixed selectors.\n\n.custom-range {\n  width: 100%;\n  height: add($custom-range-thumb-height, $custom-range-thumb-focus-box-shadow-width * 2);\n  padding: 0; // Need to reset padding\n  background-color: transparent;\n  appearance: none;\n\n  &:focus {\n    outline: none;\n\n    // Pseudo-elements must be split across multiple rulesets to have an effect.\n    // No box-shadow() mixin for focus accessibility.\n    &::-webkit-slider-thumb { box-shadow: $custom-range-thumb-focus-box-shadow; }\n    &::-moz-range-thumb     { box-shadow: $custom-range-thumb-focus-box-shadow; }\n    &::-ms-thumb            { box-shadow: $custom-range-thumb-focus-box-shadow; }\n  }\n\n  &::-moz-focus-outer {\n    border: 0;\n  }\n\n  &::-webkit-slider-thumb {\n    width: $custom-range-thumb-width;\n    height: $custom-range-thumb-height;\n    margin-top: ($custom-range-track-height - $custom-range-thumb-height) / 2; // Webkit specific\n    @include gradient-bg($custom-range-thumb-bg);\n    border: $custom-range-thumb-border;\n    @include border-radius($custom-range-thumb-border-radius);\n    @include box-shadow($custom-range-thumb-box-shadow);\n    @include transition($custom-forms-transition);\n    appearance: none;\n\n    &:active {\n      @include gradient-bg($custom-range-thumb-active-bg);\n    }\n  }\n\n  &::-webkit-slider-runnable-track {\n    width: $custom-range-track-width;\n    height: $custom-range-track-height;\n    color: transparent; // Why?\n    cursor: $custom-range-track-cursor;\n    background-color: $custom-range-track-bg;\n    border-color: transparent;\n    @include border-radius($custom-range-track-border-radius);\n    @include box-shadow($custom-range-track-box-shadow);\n  }\n\n  &::-moz-range-thumb {\n    width: $custom-range-thumb-width;\n    height: $custom-range-thumb-height;\n    @include gradient-bg($custom-range-thumb-bg);\n    border: $custom-range-thumb-border;\n    @include border-radius($custom-range-thumb-border-radius);\n    @include box-shadow($custom-range-thumb-box-shadow);\n    @include transition($custom-forms-transition);\n    appearance: none;\n\n    &:active {\n      @include gradient-bg($custom-range-thumb-active-bg);\n    }\n  }\n\n  &::-moz-range-track {\n    width: $custom-range-track-width;\n    height: $custom-range-track-height;\n    color: transparent;\n    cursor: $custom-range-track-cursor;\n    background-color: $custom-range-track-bg;\n    border-color: transparent; // Firefox specific?\n    @include border-radius($custom-range-track-border-radius);\n    @include box-shadow($custom-range-track-box-shadow);\n  }\n\n  &::-ms-thumb {\n    width: $custom-range-thumb-width;\n    height: $custom-range-thumb-height;\n    margin-top: 0; // Edge specific\n    margin-right: $custom-range-thumb-focus-box-shadow-width; // Workaround that overflowed box-shadow is hidden.\n    margin-left: $custom-range-thumb-focus-box-shadow-width;  // Workaround that overflowed box-shadow is hidden.\n    @include gradient-bg($custom-range-thumb-bg);\n    border: $custom-range-thumb-border;\n    @include border-radius($custom-range-thumb-border-radius);\n    @include box-shadow($custom-range-thumb-box-shadow);\n    @include transition($custom-forms-transition);\n    appearance: none;\n\n    &:active {\n      @include gradient-bg($custom-range-thumb-active-bg);\n    }\n  }\n\n  &::-ms-track {\n    width: $custom-range-track-width;\n    height: $custom-range-track-height;\n    color: transparent;\n    cursor: $custom-range-track-cursor;\n    background-color: transparent;\n    border-color: transparent;\n    border-width: $custom-range-thumb-height / 2;\n    @include box-shadow($custom-range-track-box-shadow);\n  }\n\n  &::-ms-fill-lower {\n    background-color: $custom-range-track-bg;\n    @include border-radius($custom-range-track-border-radius);\n  }\n\n  &::-ms-fill-upper {\n    margin-right: 15px; // arbitrary?\n    background-color: $custom-range-track-bg;\n    @include border-radius($custom-range-track-border-radius);\n  }\n\n  &:disabled {\n    &::-webkit-slider-thumb {\n      background-color: $custom-range-thumb-disabled-bg;\n    }\n\n    &::-webkit-slider-runnable-track {\n      cursor: default;\n    }\n\n    &::-moz-range-thumb {\n      background-color: $custom-range-thumb-disabled-bg;\n    }\n\n    &::-moz-range-track {\n      cursor: default;\n    }\n\n    &::-ms-thumb {\n      background-color: $custom-range-thumb-disabled-bg;\n    }\n  }\n}\n\n.custom-control-label::before,\n.custom-file-label,\n.custom-select {\n  @include transition($custom-forms-transition);\n}\n", "// Base class\n//\n// Kickstart any navigation component with a set of style resets. Works with\n// `<nav>`s, `<ul>`s or `<ol>`s.\n\n.nav {\n  display: flex;\n  flex-wrap: wrap;\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n}\n\n.nav-link {\n  display: block;\n  padding: $nav-link-padding-y $nav-link-padding-x;\n\n  @include hover-focus() {\n    text-decoration: none;\n  }\n\n  // Disabled state lightens text\n  &.disabled {\n    color: $nav-link-disabled-color;\n    pointer-events: none;\n    cursor: default;\n  }\n}\n\n//\n// Tabs\n//\n\n.nav-tabs {\n  border-bottom: $nav-tabs-border-width solid $nav-tabs-border-color;\n\n  .nav-item {\n    margin-bottom: -$nav-tabs-border-width;\n  }\n\n  .nav-link {\n    border: $nav-tabs-border-width solid transparent;\n    @include border-top-radius($nav-tabs-border-radius);\n\n    @include hover-focus() {\n      border-color: $nav-tabs-link-hover-border-color;\n    }\n\n    &.disabled {\n      color: $nav-link-disabled-color;\n      background-color: transparent;\n      border-color: transparent;\n    }\n  }\n\n  .nav-link.active,\n  .nav-item.show .nav-link {\n    color: $nav-tabs-link-active-color;\n    background-color: $nav-tabs-link-active-bg;\n    border-color: $nav-tabs-link-active-border-color;\n  }\n\n  .dropdown-menu {\n    // Make dropdown border overlap tab border\n    margin-top: -$nav-tabs-border-width;\n    // Remove the top rounded corners here since there is a hard edge above the menu\n    @include border-top-radius(0);\n  }\n}\n\n\n//\n// Pills\n//\n\n.nav-pills {\n  .nav-link {\n    @include border-radius($nav-pills-border-radius);\n  }\n\n  .nav-link.active,\n  .show > .nav-link {\n    color: $nav-pills-link-active-color;\n    background-color: $nav-pills-link-active-bg;\n  }\n}\n\n\n//\n// Justified variants\n//\n\n.nav-fill {\n  .nav-item {\n    flex: 1 1 auto;\n    text-align: center;\n  }\n}\n\n.nav-justified {\n  .nav-item {\n    flex-basis: 0;\n    flex-grow: 1;\n    text-align: center;\n  }\n}\n\n\n// Tabbable tabs\n//\n// Hide tabbable panes to start, show them when `.active`\n\n.tab-content {\n  > .tab-pane {\n    display: none;\n  }\n  > .active {\n    display: block;\n  }\n}\n", "// Contents\n//\n// Navbar\n// Navbar brand\n// Navbar nav\n// Navbar text\n// Navbar divider\n// Responsive navbar\n// Navbar position\n// Navbar themes\n\n\n// Navbar\n//\n// Provide a static navbar from which we expand to create full-width, fixed, and\n// other navbar variations.\n\n.navbar {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap; // allow us to do the line break for collapsing content\n  align-items: center;\n  justify-content: space-between; // space out brand from logo\n  padding: $navbar-padding-y $navbar-padding-x;\n\n  // Because flex properties aren't inherited, we need to redeclare these first\n  // few properties so that content nested within behave properly.\n  %container-flex-properties {\n    display: flex;\n    flex-wrap: wrap;\n    align-items: center;\n    justify-content: space-between;\n  }\n\n  .container,\n  .container-fluid {\n    @extend %container-flex-properties;\n  }\n\n  @each $breakpoint, $container-max-width in $container-max-widths {\n    > .container#{breakpoint-infix($breakpoint, $container-max-widths)} {\n      @extend %container-flex-properties;\n    }\n  }\n}\n\n\n// Navbar brand\n//\n// Used for brand, project, or site names.\n\n.navbar-brand {\n  display: inline-block;\n  padding-top: $navbar-brand-padding-y;\n  padding-bottom: $navbar-brand-padding-y;\n  margin-right: $navbar-padding-x;\n  @include font-size($navbar-brand-font-size);\n  line-height: inherit;\n  white-space: nowrap;\n\n  @include hover-focus() {\n    text-decoration: none;\n  }\n}\n\n\n// Navbar nav\n//\n// Custom navbar navigation (doesn't require `.nav`, but does make use of `.nav-link`).\n\n.navbar-nav {\n  display: flex;\n  flex-direction: column; // cannot use `inherit` to get the `.navbar`s value\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n\n  .nav-link {\n    padding-right: 0;\n    padding-left: 0;\n  }\n\n  .dropdown-menu {\n    position: static;\n    float: none;\n  }\n}\n\n\n// Navbar text\n//\n//\n\n.navbar-text {\n  display: inline-block;\n  padding-top: $nav-link-padding-y;\n  padding-bottom: $nav-link-padding-y;\n}\n\n\n// Responsive navbar\n//\n// Custom styles for responsive collapsing and toggling of navbar contents.\n// Powered by the collapse Bootstrap JavaScript plugin.\n\n// When collapsed, prevent the toggleable navbar contents from appearing in\n// the default flexbox row orientation. Requires the use of `flex-wrap: wrap`\n// on the `.navbar` parent.\n.navbar-collapse {\n  flex-basis: 100%;\n  flex-grow: 1;\n  // For always expanded or extra full navbars, ensure content aligns itself\n  // properly vertically. Can be easily overridden with flex utilities.\n  align-items: center;\n}\n\n// Button for toggling the navbar when in its collapsed state\n.navbar-toggler {\n  padding: $navbar-toggler-padding-y $navbar-toggler-padding-x;\n  @include font-size($navbar-toggler-font-size);\n  line-height: 1;\n  background-color: transparent; // remove default button style\n  border: $border-width solid transparent; // remove default button style\n  @include border-radius($navbar-toggler-border-radius);\n\n  @include hover-focus() {\n    text-decoration: none;\n  }\n}\n\n// Keep as a separate element so folks can easily override it with another icon\n// or image file as needed.\n.navbar-toggler-icon {\n  display: inline-block;\n  width: 1.5em;\n  height: 1.5em;\n  vertical-align: middle;\n  content: \"\";\n  background: no-repeat center center;\n  background-size: 100% 100%;\n}\n\n// Generate series of `.navbar-expand-*` responsive classes for configuring\n// where your navbar collapses.\n.navbar-expand {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $next: breakpoint-next($breakpoint, $grid-breakpoints);\n    $infix: breakpoint-infix($next, $grid-breakpoints);\n\n    &#{$infix} {\n      @include media-breakpoint-down($breakpoint) {\n        %container-navbar-expand-#{$breakpoint} {\n          padding-right: 0;\n          padding-left: 0;\n        }\n\n        > .container,\n        > .container-fluid {\n          @extend %container-navbar-expand-#{$breakpoint};\n        }\n\n        @each $size, $container-max-width in $container-max-widths {\n          > .container#{breakpoint-infix($size, $container-max-widths)} {\n            @extend %container-navbar-expand-#{$breakpoint};\n          }\n        }\n      }\n\n      @include media-breakpoint-up($next) {\n        flex-flow: row nowrap;\n        justify-content: flex-start;\n\n        .navbar-nav {\n          flex-direction: row;\n\n          .dropdown-menu {\n            position: absolute;\n          }\n\n          .nav-link {\n            padding-right: $navbar-nav-link-padding-x;\n            padding-left: $navbar-nav-link-padding-x;\n          }\n        }\n\n        // For nesting containers, have to redeclare for alignment purposes\n        %container-nesting-#{$breakpoint} {\n          flex-wrap: nowrap;\n        }\n\n        > .container,\n        > .container-fluid {\n          @extend %container-nesting-#{$breakpoint};\n        }\n\n        @each $size, $container-max-width in $container-max-widths {\n          > .container#{breakpoint-infix($size, $container-max-widths)} {\n            @extend %container-nesting-#{$breakpoint};\n          }\n        }\n\n        .navbar-collapse {\n          display: flex !important; // stylelint-disable-line declaration-no-important\n\n          // Changes flex-bases to auto because of an IE10 bug\n          flex-basis: auto;\n        }\n\n        .navbar-toggler {\n          display: none;\n        }\n      }\n    }\n  }\n}\n\n\n// Navbar themes\n//\n// Styles for switching between navbars with light or dark background.\n\n// Dark links against a light background\n.navbar-light {\n  .navbar-brand {\n    color: $navbar-light-brand-color;\n\n    @include hover-focus() {\n      color: $navbar-light-brand-hover-color;\n    }\n  }\n\n  .navbar-nav {\n    .nav-link {\n      color: $navbar-light-color;\n\n      @include hover-focus() {\n        color: $navbar-light-hover-color;\n      }\n\n      &.disabled {\n        color: $navbar-light-disabled-color;\n      }\n    }\n\n    .show > .nav-link,\n    .active > .nav-link,\n    .nav-link.show,\n    .nav-link.active {\n      color: $navbar-light-active-color;\n    }\n  }\n\n  .navbar-toggler {\n    color: $navbar-light-color;\n    border-color: $navbar-light-toggler-border-color;\n  }\n\n  .navbar-toggler-icon {\n    background-image: escape-svg($navbar-light-toggler-icon-bg);\n  }\n\n  .navbar-text {\n    color: $navbar-light-color;\n    a {\n      color: $navbar-light-active-color;\n\n      @include hover-focus() {\n        color: $navbar-light-active-color;\n      }\n    }\n  }\n}\n\n// White links against a dark background\n.navbar-dark {\n  .navbar-brand {\n    color: $navbar-dark-brand-color;\n\n    @include hover-focus() {\n      color: $navbar-dark-brand-hover-color;\n    }\n  }\n\n  .navbar-nav {\n    .nav-link {\n      color: $navbar-dark-color;\n\n      @include hover-focus() {\n        color: $navbar-dark-hover-color;\n      }\n\n      &.disabled {\n        color: $navbar-dark-disabled-color;\n      }\n    }\n\n    .show > .nav-link,\n    .active > .nav-link,\n    .nav-link.show,\n    .nav-link.active {\n      color: $navbar-dark-active-color;\n    }\n  }\n\n  .navbar-toggler {\n    color: $navbar-dark-color;\n    border-color: $navbar-dark-toggler-border-color;\n  }\n\n  .navbar-toggler-icon {\n    background-image: escape-svg($navbar-dark-toggler-icon-bg);\n  }\n\n  .navbar-text {\n    color: $navbar-dark-color;\n    a {\n      color: $navbar-dark-active-color;\n\n      @include hover-focus() {\n        color: $navbar-dark-active-color;\n      }\n    }\n  }\n}\n", "//\n// Base styles\n//\n\n.card {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  min-width: 0; // See https://github.com/twbs/bootstrap/pull/22740#issuecomment-305868106\n  height: $card-height;\n  word-wrap: break-word;\n  background-color: $card-bg;\n  background-clip: border-box;\n  border: $card-border-width solid $card-border-color;\n  @include border-radius($card-border-radius);\n\n  > hr {\n    margin-right: 0;\n    margin-left: 0;\n  }\n\n  > .list-group:first-child {\n    .list-group-item:first-child {\n      @include border-top-radius($card-border-radius);\n    }\n  }\n\n  > .list-group:last-child {\n    .list-group-item:last-child {\n      @include border-bottom-radius($card-border-radius);\n    }\n  }\n}\n\n.card-body {\n  // Enable `flex-grow: 1` for decks and groups so that card blocks take up\n  // as much space as possible, ensuring footers are aligned to the bottom.\n  flex: 1 1 auto;\n  // Workaround for the image size bug in IE\n  // See: https://github.com/twbs/bootstrap/pull/28855\n  min-height: 1px;\n  padding: $card-spacer-x;\n  color: $card-color;\n}\n\n.card-title {\n  margin-bottom: $card-spacer-y;\n}\n\n.card-subtitle {\n  margin-top: -$card-spacer-y / 2;\n  margin-bottom: 0;\n}\n\n.card-text:last-child {\n  margin-bottom: 0;\n}\n\n.card-link {\n  @include hover() {\n    text-decoration: none;\n  }\n\n  + .card-link {\n    margin-left: $card-spacer-x;\n  }\n}\n\n//\n// Optional textual caps\n//\n\n.card-header {\n  padding: $card-spacer-y $card-spacer-x;\n  margin-bottom: 0; // Removes the default margin-bottom of <hN>\n  color: $card-cap-color;\n  background-color: $card-cap-bg;\n  border-bottom: $card-border-width solid $card-border-color;\n\n  &:first-child {\n    @include border-radius($card-inner-border-radius $card-inner-border-radius 0 0);\n  }\n\n  + .list-group {\n    .list-group-item:first-child {\n      border-top: 0;\n    }\n  }\n}\n\n.card-footer {\n  padding: $card-spacer-y $card-spacer-x;\n  background-color: $card-cap-bg;\n  border-top: $card-border-width solid $card-border-color;\n\n  &:last-child {\n    @include border-radius(0 0 $card-inner-border-radius $card-inner-border-radius);\n  }\n}\n\n\n//\n// Header navs\n//\n\n.card-header-tabs {\n  margin-right: -$card-spacer-x / 2;\n  margin-bottom: -$card-spacer-y;\n  margin-left: -$card-spacer-x / 2;\n  border-bottom: 0;\n}\n\n.card-header-pills {\n  margin-right: -$card-spacer-x / 2;\n  margin-left: -$card-spacer-x / 2;\n}\n\n// Card image\n.card-img-overlay {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  padding: $card-img-overlay-padding;\n}\n\n.card-img,\n.card-img-top,\n.card-img-bottom {\n  flex-shrink: 0; // For IE: https://github.com/twbs/bootstrap/issues/29396\n  width: 100%; // Required because we use flexbox and this inherently applies align-self: stretch\n}\n\n.card-img,\n.card-img-top {\n  @include border-top-radius($card-inner-border-radius);\n}\n\n.card-img,\n.card-img-bottom {\n  @include border-bottom-radius($card-inner-border-radius);\n}\n\n\n// Card deck\n\n.card-deck {\n  .card {\n    margin-bottom: $card-deck-margin;\n  }\n\n  @include media-breakpoint-up(sm) {\n    display: flex;\n    flex-flow: row wrap;\n    margin-right: -$card-deck-margin;\n    margin-left: -$card-deck-margin;\n\n    .card {\n      // Flexbugs #4: https://github.com/philipwalton/flexbugs#flexbug-4\n      flex: 1 0 0%;\n      margin-right: $card-deck-margin;\n      margin-bottom: 0; // Override the default\n      margin-left: $card-deck-margin;\n    }\n  }\n}\n\n\n//\n// Card groups\n//\n\n.card-group {\n  // The child selector allows nested `.card` within `.card-group`\n  // to display properly.\n  > .card {\n    margin-bottom: $card-group-margin;\n  }\n\n  @include media-breakpoint-up(sm) {\n    display: flex;\n    flex-flow: row wrap;\n    // The child selector allows nested `.card` within `.card-group`\n    // to display properly.\n    > .card {\n      // Flexbugs #4: https://github.com/philipwalton/flexbugs#flexbug-4\n      flex: 1 0 0%;\n      margin-bottom: 0;\n\n      + .card {\n        margin-left: 0;\n        border-left: 0;\n      }\n\n      // Handle rounded corners\n      @if $enable-rounded {\n        &:not(:last-child) {\n          @include border-right-radius(0);\n\n          .card-img-top,\n          .card-header {\n            // stylelint-disable-next-line property-blacklist\n            border-top-right-radius: 0;\n          }\n          .card-img-bottom,\n          .card-footer {\n            // stylelint-disable-next-line property-blacklist\n            border-bottom-right-radius: 0;\n          }\n        }\n\n        &:not(:first-child) {\n          @include border-left-radius(0);\n\n          .card-img-top,\n          .card-header {\n            // stylelint-disable-next-line property-blacklist\n            border-top-left-radius: 0;\n          }\n          .card-img-bottom,\n          .card-footer {\n            // stylelint-disable-next-line property-blacklist\n            border-bottom-left-radius: 0;\n          }\n        }\n      }\n    }\n  }\n}\n\n\n//\n// Columns\n//\n\n.card-columns {\n  .card {\n    margin-bottom: $card-columns-margin;\n  }\n\n  @include media-breakpoint-up(sm) {\n    column-count: $card-columns-count;\n    column-gap: $card-columns-gap;\n    orphans: 1;\n    widows: 1;\n\n    .card {\n      display: inline-block; // Don't let them vertically span multiple columns\n      width: 100%; // Don't let their width change\n    }\n  }\n}\n\n\n//\n// Accordion\n//\n\n.accordion {\n  > .card {\n    overflow: hidden;\n\n    &:not(:last-of-type) {\n      border-bottom: 0;\n      @include border-bottom-radius(0);\n    }\n\n    &:not(:first-of-type) {\n      @include border-top-radius(0);\n    }\n\n    > .card-header {\n      @include border-radius(0);\n      margin-bottom: -$card-border-width;\n    }\n  }\n}\n", ".breadcrumb {\n  display: flex;\n  flex-wrap: wrap;\n  padding: $breadcrumb-padding-y $breadcrumb-padding-x;\n  margin-bottom: $breadcrumb-margin-bottom;\n  @include font-size($breadcrumb-font-size);\n  list-style: none;\n  background-color: $breadcrumb-bg;\n  @include border-radius($breadcrumb-border-radius);\n}\n\n.breadcrumb-item {\n  // The separator between breadcrumbs (by default, a forward-slash: \"/\")\n  + .breadcrumb-item {\n    padding-left: $breadcrumb-item-padding;\n\n    &::before {\n      display: inline-block; // Suppress underlining of the separator in modern browsers\n      padding-right: $breadcrumb-item-padding;\n      color: $breadcrumb-divider-color;\n      content: escape-svg($breadcrumb-divider);\n    }\n  }\n\n  // IE9-11 hack to properly handle hyperlink underlines for breadcrumbs built\n  // without `<ul>`s. The `::before` pseudo-element generates an element\n  // *within* the .breadcrumb-item and thereby inherits the `text-decoration`.\n  //\n  // To trick <PERSON><PERSON> into suppressing the underline, we give the pseudo-element an\n  // underline and then immediately remove it.\n  + .breadcrumb-item:hover::before {\n    text-decoration: underline;\n  }\n  // stylelint-disable-next-line no-duplicate-selectors\n  + .breadcrumb-item:hover::before {\n    text-decoration: none;\n  }\n\n  &.active {\n    color: $breadcrumb-active-color;\n  }\n}\n", ".pagination {\n  display: flex;\n  @include list-unstyled();\n  @include border-radius();\n}\n\n.page-link {\n  position: relative;\n  display: block;\n  padding: $pagination-padding-y $pagination-padding-x;\n  margin-left: -$pagination-border-width;\n  line-height: $pagination-line-height;\n  color: $pagination-color;\n  background-color: $pagination-bg;\n  border: $pagination-border-width solid $pagination-border-color;\n\n  &:hover {\n    z-index: 2;\n    color: $pagination-hover-color;\n    text-decoration: none;\n    background-color: $pagination-hover-bg;\n    border-color: $pagination-hover-border-color;\n  }\n\n  &:focus {\n    z-index: 3;\n    outline: $pagination-focus-outline;\n    box-shadow: $pagination-focus-box-shadow;\n  }\n}\n\n.page-item {\n  &:first-child {\n    .page-link {\n      margin-left: 0;\n      @include border-left-radius($border-radius);\n    }\n  }\n  &:last-child {\n    .page-link {\n      @include border-right-radius($border-radius);\n    }\n  }\n\n  &.active .page-link {\n    z-index: 3;\n    color: $pagination-active-color;\n    background-color: $pagination-active-bg;\n    border-color: $pagination-active-border-color;\n  }\n\n  &.disabled .page-link {\n    color: $pagination-disabled-color;\n    pointer-events: none;\n    // Opinionated: remove the \"hand\" cursor set previously for .page-link\n    cursor: auto;\n    background-color: $pagination-disabled-bg;\n    border-color: $pagination-disabled-border-color;\n  }\n}\n\n\n//\n// Sizing\n//\n\n.pagination-lg {\n  @include pagination-size($pagination-padding-y-lg, $pagination-padding-x-lg, $font-size-lg, $line-height-lg, $border-radius-lg);\n}\n\n.pagination-sm {\n  @include pagination-size($pagination-padding-y-sm, $pagination-padding-x-sm, $font-size-sm, $line-height-sm, $border-radius-sm);\n}\n", "// Pagination\n\n@mixin pagination-size($padding-y, $padding-x, $font-size, $line-height, $border-radius) {\n  .page-link {\n    padding: $padding-y $padding-x;\n    @include font-size($font-size);\n    line-height: $line-height;\n  }\n\n  .page-item {\n    &:first-child {\n      .page-link {\n        @include border-left-radius($border-radius);\n      }\n    }\n    &:last-child {\n      .page-link {\n        @include border-right-radius($border-radius);\n      }\n    }\n  }\n}\n", "// Base class\n//\n// Requires one of the contextual, color modifier classes for `color` and\n// `background-color`.\n\n.badge {\n  display: inline-block;\n  padding: $badge-padding-y $badge-padding-x;\n  @include font-size($badge-font-size);\n  font-weight: $badge-font-weight;\n  line-height: 1;\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: baseline;\n  @include border-radius($badge-border-radius);\n  @include transition($badge-transition);\n\n  @at-root a#{&} {\n    @include hover-focus() {\n      text-decoration: none;\n    }\n  }\n\n  // Empty badges collapse automatically\n  &:empty {\n    display: none;\n  }\n}\n\n// Quick fix for badges in buttons\n.btn .badge {\n  position: relative;\n  top: -1px;\n}\n\n// Pill badges\n//\n// Make them extra rounded with a modifier to replace v3's badges.\n\n.badge-pill {\n  padding-right: $badge-pill-padding-x;\n  padding-left: $badge-pill-padding-x;\n  @include border-radius($badge-pill-border-radius);\n}\n\n// Colors\n//\n// Contextual variations (linked badges get darker on :hover).\n\n@each $color, $value in $theme-colors {\n  .badge-#{$color} {\n    @include badge-variant($value);\n  }\n}\n", "@mixin badge-variant($bg) {\n  color: color-yiq($bg);\n  background-color: $bg;\n\n  @at-root a#{&} {\n    @include hover-focus() {\n      color: color-yiq($bg);\n      background-color: darken($bg, 10%);\n    }\n\n    &:focus,\n    &.focus {\n      outline: 0;\n      box-shadow: 0 0 0 $badge-focus-width rgba($bg, .5);\n    }\n  }\n}\n", ".jumbotron {\n  padding: $jumbotron-padding ($jumbotron-padding / 2);\n  margin-bottom: $jumbotron-padding;\n  color: $jumbotron-color;\n  background-color: $jumbotron-bg;\n  @include border-radius($border-radius-lg);\n\n  @include media-breakpoint-up(sm) {\n    padding: ($jumbotron-padding * 2) $jumbotron-padding;\n  }\n}\n\n.jumbotron-fluid {\n  padding-right: 0;\n  padding-left: 0;\n  @include border-radius(0);\n}\n", "//\n// Base styles\n//\n\n.alert {\n  position: relative;\n  padding: $alert-padding-y $alert-padding-x;\n  margin-bottom: $alert-margin-bottom;\n  border: $alert-border-width solid transparent;\n  @include border-radius($alert-border-radius);\n}\n\n// Headings for larger alerts\n.alert-heading {\n  // Specified to prevent conflicts of changing $headings-color\n  color: inherit;\n}\n\n// Provide class for links that match alerts\n.alert-link {\n  font-weight: $alert-link-font-weight;\n}\n\n\n// Dismissible alerts\n//\n// Expand the right padding and account for the close button's positioning.\n\n.alert-dismissible {\n  padding-right: $close-font-size + $alert-padding-x * 2;\n\n  // Adjust close link position\n  .close {\n    position: absolute;\n    top: 0;\n    right: 0;\n    padding: $alert-padding-y $alert-padding-x;\n    color: inherit;\n  }\n}\n\n\n// Alternate styles\n//\n// Generate contextual modifier classes for colorizing the alert.\n\n@each $color, $value in $theme-colors {\n  .alert-#{$color} {\n    @include alert-variant(theme-color-level($color, $alert-bg-level), theme-color-level($color, $alert-border-level), theme-color-level($color, $alert-color-level));\n  }\n}\n", "@mixin alert-variant($background, $border, $color) {\n  color: $color;\n  @include gradient-bg($background);\n  border-color: $border;\n\n  hr {\n    border-top-color: darken($border, 5%);\n  }\n\n  .alert-link {\n    color: darken($color, 10%);\n  }\n}\n", "// Disable animation if transitions are disabled\n@if $enable-transitions {\n  @keyframes progress-bar-stripes {\n    from { background-position: $progress-height 0; }\n    to { background-position: 0 0; }\n  }\n}\n\n.progress {\n  display: flex;\n  height: $progress-height;\n  overflow: hidden; // force rounded corners by cropping it\n  @include font-size($progress-font-size);\n  background-color: $progress-bg;\n  @include border-radius($progress-border-radius);\n  @include box-shadow($progress-box-shadow);\n}\n\n.progress-bar {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  overflow: hidden;\n  color: $progress-bar-color;\n  text-align: center;\n  white-space: nowrap;\n  background-color: $progress-bar-bg;\n  @include transition($progress-bar-transition);\n}\n\n.progress-bar-striped {\n  @include gradient-striped();\n  background-size: $progress-height $progress-height;\n}\n\n@if $enable-transitions {\n  .progress-bar-animated {\n    animation: progress-bar-stripes $progress-bar-animation-timing;\n\n    @if $enable-prefers-reduced-motion-media-query {\n      @media (prefers-reduced-motion: reduce) {\n        animation: none;\n      }\n    }\n  }\n}\n", ".media {\n  display: flex;\n  align-items: flex-start;\n}\n\n.media-body {\n  flex: 1;\n}\n", "// Base class\n//\n// Easily usable on <ul>, <ol>, or <div>.\n\n.list-group {\n  display: flex;\n  flex-direction: column;\n\n  // No need to set list-style: none; since .list-group-item is block level\n  padding-left: 0; // reset padding because ul and ol\n  margin-bottom: 0;\n}\n\n\n// Interactive list items\n//\n// Use anchor or button elements instead of `li`s or `div`s to create interactive\n// list items. Includes an extra `.active` modifier class for selected items.\n\n.list-group-item-action {\n  width: 100%; // For `<button>`s (anchors become 100% by default though)\n  color: $list-group-action-color;\n  text-align: inherit; // For `<button>`s (anchors inherit)\n\n  // Hover state\n  @include hover-focus() {\n    z-index: 1; // Place hover/focus items above their siblings for proper border styling\n    color: $list-group-action-hover-color;\n    text-decoration: none;\n    background-color: $list-group-hover-bg;\n  }\n\n  &:active {\n    color: $list-group-action-active-color;\n    background-color: $list-group-action-active-bg;\n  }\n}\n\n\n// Individual list items\n//\n// Use on `li`s or `div`s within the `.list-group` parent.\n\n.list-group-item {\n  position: relative;\n  display: block;\n  padding: $list-group-item-padding-y $list-group-item-padding-x;\n  color: $list-group-color;\n  background-color: $list-group-bg;\n  border: $list-group-border-width solid $list-group-border-color;\n\n  &:first-child {\n    @include border-top-radius($list-group-border-radius);\n  }\n\n  &:last-child {\n    @include border-bottom-radius($list-group-border-radius);\n  }\n\n  &.disabled,\n  &:disabled {\n    color: $list-group-disabled-color;\n    pointer-events: none;\n    background-color: $list-group-disabled-bg;\n  }\n\n  // Include both here for `<a>`s and `<button>`s\n  &.active {\n    z-index: 2; // Place active items above their siblings for proper border styling\n    color: $list-group-active-color;\n    background-color: $list-group-active-bg;\n    border-color: $list-group-active-border-color;\n  }\n\n  & + & {\n    border-top-width: 0;\n\n    &.active {\n      margin-top: -$list-group-border-width;\n      border-top-width: $list-group-border-width;\n    }\n  }\n}\n\n\n// Horizontal\n//\n// Change the layout of list group items from vertical (default) to horizontal.\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .list-group-horizontal#{$infix} {\n      flex-direction: row;\n\n      .list-group-item {\n        &:first-child {\n          @include border-bottom-left-radius($list-group-border-radius);\n          @include border-top-right-radius(0);\n        }\n\n        &:last-child {\n          @include border-top-right-radius($list-group-border-radius);\n          @include border-bottom-left-radius(0);\n        }\n\n        &.active {\n          margin-top: 0;\n        }\n\n        & + .list-group-item {\n          border-top-width: $list-group-border-width;\n          border-left-width: 0;\n\n          &.active {\n            margin-left: -$list-group-border-width;\n            border-left-width: $list-group-border-width;\n          }\n        }\n      }\n    }\n  }\n}\n\n\n// Flush list items\n//\n// Remove borders and border-radius to keep list group items edge-to-edge. Most\n// useful within other components (e.g., cards).\n\n.list-group-flush {\n  .list-group-item {\n    border-right-width: 0;\n    border-left-width: 0;\n    @include border-radius(0);\n\n    &:first-child {\n      border-top-width: 0;\n    }\n  }\n\n  &:last-child {\n    .list-group-item:last-child {\n      border-bottom-width: 0;\n    }\n  }\n}\n\n\n// Contextual variants\n//\n// Add modifier classes to change text and background color on individual items.\n// Organizationally, this must come after the `:hover` states.\n\n@each $color, $value in $theme-colors {\n  @include list-group-item-variant($color, theme-color-level($color, -9), theme-color-level($color, 6));\n}\n", "// List Groups\n\n@mixin list-group-item-variant($state, $background, $color) {\n  .list-group-item-#{$state} {\n    color: $color;\n    background-color: $background;\n\n    &.list-group-item-action {\n      @include hover-focus() {\n        color: $color;\n        background-color: darken($background, 5%);\n      }\n\n      &.active {\n        color: $white;\n        background-color: $color;\n        border-color: $color;\n      }\n    }\n  }\n}\n", ".close {\n  float: right;\n  @include font-size($close-font-size);\n  font-weight: $close-font-weight;\n  line-height: 1;\n  color: $close-color;\n  text-shadow: $close-text-shadow;\n  opacity: .5;\n\n  // Override <a>'s hover style\n  @include hover() {\n    color: $close-color;\n    text-decoration: none;\n  }\n\n  &:not(:disabled):not(.disabled) {\n    @include hover-focus() {\n      opacity: .75;\n    }\n  }\n}\n\n// Additional properties for button version\n// iOS requires the button element instead of an anchor tag.\n// If you want the anchor version, it requires `href=\"#\"`.\n// See https://developer.mozilla.org/en-US/docs/Web/Events/click#Safari_Mobile\n\n// stylelint-disable-next-line selector-no-qualifying-type\nbutton.close {\n  padding: 0;\n  background-color: transparent;\n  border: 0;\n  appearance: none;\n}\n\n// Future-proof disabling of clicks on `<a>` elements\n\n// stylelint-disable-next-line selector-no-qualifying-type\na.close.disabled {\n  pointer-events: none;\n}\n", ".toast {\n  max-width: $toast-max-width;\n  overflow: hidden; // cheap rounded corners on nested items\n  @include font-size($toast-font-size);\n  color: $toast-color;\n  background-color: $toast-background-color;\n  background-clip: padding-box;\n  border: $toast-border-width solid $toast-border-color;\n  box-shadow: $toast-box-shadow;\n  backdrop-filter: blur(10px);\n  opacity: 0;\n  @include border-radius($toast-border-radius);\n\n  &:not(:last-child) {\n    margin-bottom: $toast-padding-x;\n  }\n\n  &.showing {\n    opacity: 1;\n  }\n\n  &.show {\n    display: block;\n    opacity: 1;\n  }\n\n  &.hide {\n    display: none;\n  }\n}\n\n.toast-header {\n  display: flex;\n  align-items: center;\n  padding: $toast-padding-y $toast-padding-x;\n  color: $toast-header-color;\n  background-color: $toast-header-background-color;\n  background-clip: padding-box;\n  border-bottom: $toast-border-width solid $toast-header-border-color;\n}\n\n.toast-body {\n  padding: $toast-padding-x; // apply to both vertical and horizontal\n}\n", "// .modal-open      - body class for killing the scroll\n// .modal           - container to scroll within\n// .modal-dialog    - positioning shell for the actual modal\n// .modal-content   - actual modal w/ bg and corners and stuff\n\n\n.modal-open {\n  // Kill the scroll on the body\n  overflow: hidden;\n\n  .modal {\n    overflow-x: hidden;\n    overflow-y: auto;\n  }\n}\n\n// Container that the modal scrolls within\n.modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: $zindex-modal;\n  display: none;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n  // Prevent Chrome on Windows from adding a focus outline. For details, see\n  // https://github.com/twbs/bootstrap/pull/10951.\n  outline: 0;\n  // We deliberately don't use `-webkit-overflow-scrolling: touch;` due to a\n  // gnarly iOS Safari bug: https://bugs.webkit.org/show_bug.cgi?id=158342\n  // See also https://github.com/twbs/bootstrap/issues/17695\n}\n\n// Shell div to position the modal with bottom padding\n.modal-dialog {\n  position: relative;\n  width: auto;\n  margin: $modal-dialog-margin;\n  // allow clicks to pass through for custom click handling to close modal\n  pointer-events: none;\n\n  // When fading in the modal, animate it to slide down\n  .modal.fade & {\n    @include transition($modal-transition);\n    transform: $modal-fade-transform;\n  }\n  .modal.show & {\n    transform: $modal-show-transform;\n  }\n\n  // When trying to close, animate focus to scale\n  .modal.modal-static & {\n    transform: $modal-scale-transform;\n  }\n}\n\n.modal-dialog-scrollable {\n  display: flex; // IE10/11\n  max-height: subtract(100%, $modal-dialog-margin * 2);\n\n  .modal-content {\n    max-height: subtract(100vh, $modal-dialog-margin * 2); // IE10/11\n    overflow: hidden;\n  }\n\n  .modal-header,\n  .modal-footer {\n    flex-shrink: 0;\n  }\n\n  .modal-body {\n    overflow-y: auto;\n  }\n}\n\n.modal-dialog-centered {\n  display: flex;\n  align-items: center;\n  min-height: subtract(100%, $modal-dialog-margin * 2);\n\n  // Ensure `modal-dialog-centered` extends the full height of the view (IE10/11)\n  &::before {\n    display: block; // IE10\n    height: subtract(100vh, $modal-dialog-margin * 2);\n    content: \"\";\n  }\n\n  // Ensure `.modal-body` shows scrollbar (IE10/11)\n  &.modal-dialog-scrollable {\n    flex-direction: column;\n    justify-content: center;\n    height: 100%;\n\n    .modal-content {\n      max-height: none;\n    }\n\n    &::before {\n      content: none;\n    }\n  }\n}\n\n// Actual modal\n.modal-content {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  width: 100%; // Ensure `.modal-content` extends the full width of the parent `.modal-dialog`\n  // counteract the pointer-events: none; in the .modal-dialog\n  color: $modal-content-color;\n  pointer-events: auto;\n  background-color: $modal-content-bg;\n  background-clip: padding-box;\n  border: $modal-content-border-width solid $modal-content-border-color;\n  @include border-radius($modal-content-border-radius);\n  @include box-shadow($modal-content-box-shadow-xs);\n  // Remove focus outline from opened modal\n  outline: 0;\n}\n\n// Modal background\n.modal-backdrop {\n  position: fixed;\n  top: 0;\n  left: 0;\n  z-index: $zindex-modal-backdrop;\n  width: 100vw;\n  height: 100vh;\n  background-color: $modal-backdrop-bg;\n\n  // Fade for backdrop\n  &.fade { opacity: 0; }\n  &.show { opacity: $modal-backdrop-opacity; }\n}\n\n// Modal header\n// Top section of the modal w/ title and dismiss\n.modal-header {\n  display: flex;\n  align-items: flex-start; // so the close btn always stays on the upper right corner\n  justify-content: space-between; // Put modal header elements (title and dismiss) on opposite ends\n  padding: $modal-header-padding;\n  border-bottom: $modal-header-border-width solid $modal-header-border-color;\n  @include border-top-radius($modal-content-inner-border-radius);\n\n  .close {\n    padding: $modal-header-padding;\n    // auto on the left force icon to the right even when there is no .modal-title\n    margin: (-$modal-header-padding-y) (-$modal-header-padding-x) (-$modal-header-padding-y) auto;\n  }\n}\n\n// Title text within header\n.modal-title {\n  margin-bottom: 0;\n  line-height: $modal-title-line-height;\n}\n\n// Modal body\n// Where all modal content resides (sibling of .modal-header and .modal-footer)\n.modal-body {\n  position: relative;\n  // Enable `flex-grow: 1` so that the body take up as much space as possible\n  // when there should be a fixed height on `.modal-dialog`.\n  flex: 1 1 auto;\n  padding: $modal-inner-padding;\n}\n\n// Footer (for actions)\n.modal-footer {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center; // vertically center\n  justify-content: flex-end; // Right align buttons with flex property because text-align doesn't work on flex items\n  padding: $modal-inner-padding - $modal-footer-margin-between / 2;\n  border-top: $modal-footer-border-width solid $modal-footer-border-color;\n  @include border-bottom-radius($modal-content-inner-border-radius);\n\n  // Place margin between footer elements\n  // This solution is far from ideal because of the universal selector usage,\n  // but is needed to fix https://github.com/twbs/bootstrap/issues/24800\n  // stylelint-disable-next-line selector-max-universal\n  > * {\n    margin: $modal-footer-margin-between / 2;\n  }\n}\n\n// Measure scrollbar width for padding body during modal show/hide\n.modal-scrollbar-measure {\n  position: absolute;\n  top: -9999px;\n  width: 50px;\n  height: 50px;\n  overflow: scroll;\n}\n\n// Scale up the modal\n@include media-breakpoint-up(sm) {\n  // Automatically set modal's width for larger viewports\n  .modal-dialog {\n    max-width: $modal-md;\n    margin: $modal-dialog-margin-y-sm-up auto;\n  }\n\n  .modal-dialog-scrollable {\n    max-height: subtract(100%, $modal-dialog-margin-y-sm-up * 2);\n\n    .modal-content {\n      max-height: subtract(100vh, $modal-dialog-margin-y-sm-up * 2);\n    }\n  }\n\n  .modal-dialog-centered {\n    min-height: subtract(100%, $modal-dialog-margin-y-sm-up * 2);\n\n    &::before {\n      height: subtract(100vh, $modal-dialog-margin-y-sm-up * 2);\n    }\n  }\n\n  .modal-content {\n    @include box-shadow($modal-content-box-shadow-sm-up);\n  }\n\n  .modal-sm { max-width: $modal-sm; }\n}\n\n@include media-breakpoint-up(lg) {\n  .modal-lg,\n  .modal-xl {\n    max-width: $modal-lg;\n  }\n}\n\n@include media-breakpoint-up(xl) {\n  .modal-xl { max-width: $modal-xl; }\n}\n", "// Base class\n.tooltip {\n  position: absolute;\n  z-index: $zindex-tooltip;\n  display: block;\n  margin: $tooltip-margin;\n  // Our parent element can be arbitrary since tooltips are by default inserted as a sibling of their target element.\n  // So reset our font and text properties to avoid inheriting weird values.\n  @include reset-text();\n  @include font-size($tooltip-font-size);\n  // Allow breaking very long words so they don't overflow the tooltip's bounds\n  word-wrap: break-word;\n  opacity: 0;\n\n  &.show { opacity: $tooltip-opacity; }\n\n  .arrow {\n    position: absolute;\n    display: block;\n    width: $tooltip-arrow-width;\n    height: $tooltip-arrow-height;\n\n    &::before {\n      position: absolute;\n      content: \"\";\n      border-color: transparent;\n      border-style: solid;\n    }\n  }\n}\n\n.bs-tooltip-top {\n  padding: $tooltip-arrow-height 0;\n\n  .arrow {\n    bottom: 0;\n\n    &::before {\n      top: 0;\n      border-width: $tooltip-arrow-height ($tooltip-arrow-width / 2) 0;\n      border-top-color: $tooltip-arrow-color;\n    }\n  }\n}\n\n.bs-tooltip-right {\n  padding: 0 $tooltip-arrow-height;\n\n  .arrow {\n    left: 0;\n    width: $tooltip-arrow-height;\n    height: $tooltip-arrow-width;\n\n    &::before {\n      right: 0;\n      border-width: ($tooltip-arrow-width / 2) $tooltip-arrow-height ($tooltip-arrow-width / 2) 0;\n      border-right-color: $tooltip-arrow-color;\n    }\n  }\n}\n\n.bs-tooltip-bottom {\n  padding: $tooltip-arrow-height 0;\n\n  .arrow {\n    top: 0;\n\n    &::before {\n      bottom: 0;\n      border-width: 0 ($tooltip-arrow-width / 2) $tooltip-arrow-height;\n      border-bottom-color: $tooltip-arrow-color;\n    }\n  }\n}\n\n.bs-tooltip-left {\n  padding: 0 $tooltip-arrow-height;\n\n  .arrow {\n    right: 0;\n    width: $tooltip-arrow-height;\n    height: $tooltip-arrow-width;\n\n    &::before {\n      left: 0;\n      border-width: ($tooltip-arrow-width / 2) 0 ($tooltip-arrow-width / 2) $tooltip-arrow-height;\n      border-left-color: $tooltip-arrow-color;\n    }\n  }\n}\n\n.bs-tooltip-auto {\n  &[x-placement^=\"top\"] {\n    @extend .bs-tooltip-top;\n  }\n  &[x-placement^=\"right\"] {\n    @extend .bs-tooltip-right;\n  }\n  &[x-placement^=\"bottom\"] {\n    @extend .bs-tooltip-bottom;\n  }\n  &[x-placement^=\"left\"] {\n    @extend .bs-tooltip-left;\n  }\n}\n\n// Wrapper for the tooltip content\n.tooltip-inner {\n  max-width: $tooltip-max-width;\n  padding: $tooltip-padding-y $tooltip-padding-x;\n  color: $tooltip-color;\n  text-align: center;\n  background-color: $tooltip-bg;\n  @include border-radius($tooltip-border-radius);\n}\n", "@mixin reset-text() {\n  font-family: $font-family-base;\n  // We deliberately do NOT reset font-size or word-wrap.\n  font-style: normal;\n  font-weight: $font-weight-normal;\n  line-height: $line-height-base;\n  text-align: left; // Fallback for where `start` is not supported\n  text-align: start;\n  text-decoration: none;\n  text-shadow: none;\n  text-transform: none;\n  letter-spacing: normal;\n  word-break: normal;\n  word-spacing: normal;\n  white-space: normal;\n  line-break: auto;\n}\n", ".popover {\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: $zindex-popover;\n  display: block;\n  max-width: $popover-max-width;\n  // Our parent element can be arbitrary since tooltips are by default inserted as a sibling of their target element.\n  // So reset our font and text properties to avoid inheriting weird values.\n  @include reset-text();\n  @include font-size($popover-font-size);\n  // Allow breaking very long words so they don't overflow the popover's bounds\n  word-wrap: break-word;\n  background-color: $popover-bg;\n  background-clip: padding-box;\n  border: $popover-border-width solid $popover-border-color;\n  @include border-radius($popover-border-radius);\n  @include box-shadow($popover-box-shadow);\n\n  .arrow {\n    position: absolute;\n    display: block;\n    width: $popover-arrow-width;\n    height: $popover-arrow-height;\n    margin: 0 $popover-border-radius;\n\n    &::before,\n    &::after {\n      position: absolute;\n      display: block;\n      content: \"\";\n      border-color: transparent;\n      border-style: solid;\n    }\n  }\n}\n\n.bs-popover-top {\n  margin-bottom: $popover-arrow-height;\n\n  > .arrow {\n    bottom: subtract(-$popover-arrow-height, $popover-border-width);\n\n    &::before {\n      bottom: 0;\n      border-width: $popover-arrow-height ($popover-arrow-width / 2) 0;\n      border-top-color: $popover-arrow-outer-color;\n    }\n\n    &::after {\n      bottom: $popover-border-width;\n      border-width: $popover-arrow-height ($popover-arrow-width / 2) 0;\n      border-top-color: $popover-arrow-color;\n    }\n  }\n}\n\n.bs-popover-right {\n  margin-left: $popover-arrow-height;\n\n  > .arrow {\n    left: subtract(-$popover-arrow-height, $popover-border-width);\n    width: $popover-arrow-height;\n    height: $popover-arrow-width;\n    margin: $popover-border-radius 0; // make sure the arrow does not touch the popover's rounded corners\n\n    &::before {\n      left: 0;\n      border-width: ($popover-arrow-width / 2) $popover-arrow-height ($popover-arrow-width / 2) 0;\n      border-right-color: $popover-arrow-outer-color;\n    }\n\n    &::after {\n      left: $popover-border-width;\n      border-width: ($popover-arrow-width / 2) $popover-arrow-height ($popover-arrow-width / 2) 0;\n      border-right-color: $popover-arrow-color;\n    }\n  }\n}\n\n.bs-popover-bottom {\n  margin-top: $popover-arrow-height;\n\n  > .arrow {\n    top: subtract(-$popover-arrow-height, $popover-border-width);\n\n    &::before {\n      top: 0;\n      border-width: 0 ($popover-arrow-width / 2) $popover-arrow-height ($popover-arrow-width / 2);\n      border-bottom-color: $popover-arrow-outer-color;\n    }\n\n    &::after {\n      top: $popover-border-width;\n      border-width: 0 ($popover-arrow-width / 2) $popover-arrow-height ($popover-arrow-width / 2);\n      border-bottom-color: $popover-arrow-color;\n    }\n  }\n\n  // This will remove the popover-header's border just below the arrow\n  .popover-header::before {\n    position: absolute;\n    top: 0;\n    left: 50%;\n    display: block;\n    width: $popover-arrow-width;\n    margin-left: -$popover-arrow-width / 2;\n    content: \"\";\n    border-bottom: $popover-border-width solid $popover-header-bg;\n  }\n}\n\n.bs-popover-left {\n  margin-right: $popover-arrow-height;\n\n  > .arrow {\n    right: subtract(-$popover-arrow-height, $popover-border-width);\n    width: $popover-arrow-height;\n    height: $popover-arrow-width;\n    margin: $popover-border-radius 0; // make sure the arrow does not touch the popover's rounded corners\n\n    &::before {\n      right: 0;\n      border-width: ($popover-arrow-width / 2) 0 ($popover-arrow-width / 2) $popover-arrow-height;\n      border-left-color: $popover-arrow-outer-color;\n    }\n\n    &::after {\n      right: $popover-border-width;\n      border-width: ($popover-arrow-width / 2) 0 ($popover-arrow-width / 2) $popover-arrow-height;\n      border-left-color: $popover-arrow-color;\n    }\n  }\n}\n\n.bs-popover-auto {\n  &[x-placement^=\"top\"] {\n    @extend .bs-popover-top;\n  }\n  &[x-placement^=\"right\"] {\n    @extend .bs-popover-right;\n  }\n  &[x-placement^=\"bottom\"] {\n    @extend .bs-popover-bottom;\n  }\n  &[x-placement^=\"left\"] {\n    @extend .bs-popover-left;\n  }\n}\n\n\n// Offset the popover to account for the popover arrow\n.popover-header {\n  padding: $popover-header-padding-y $popover-header-padding-x;\n  margin-bottom: 0; // Reset the default from Reboot\n  @include font-size($font-size-base);\n  color: $popover-header-color;\n  background-color: $popover-header-bg;\n  border-bottom: $popover-border-width solid darken($popover-header-bg, 5%);\n  @include border-top-radius($popover-inner-border-radius);\n\n  &:empty {\n    display: none;\n  }\n}\n\n.popover-body {\n  padding: $popover-body-padding-y $popover-body-padding-x;\n  color: $popover-body-color;\n}\n", "// Notes on the classes:\n//\n// 1. .carousel.pointer-event should ideally be pan-y (to allow for users to scroll vertically)\n//    even when their scroll action started on a carousel, but for compatibility (with Firefox)\n//    we're preventing all actions instead\n// 2. The .carousel-item-left and .carousel-item-right is used to indicate where\n//    the active slide is heading.\n// 3. .active.carousel-item is the current slide.\n// 4. .active.carousel-item-left and .active.carousel-item-right is the current\n//    slide in its in-transition state. Only one of these occurs at a time.\n// 5. .carousel-item-next.carousel-item-left and .carousel-item-prev.carousel-item-right\n//    is the upcoming slide in transition.\n\n.carousel {\n  position: relative;\n}\n\n.carousel.pointer-event {\n  touch-action: pan-y;\n}\n\n.carousel-inner {\n  position: relative;\n  width: 100%;\n  overflow: hidden;\n  @include clearfix();\n}\n\n.carousel-item {\n  position: relative;\n  display: none;\n  float: left;\n  width: 100%;\n  margin-right: -100%;\n  backface-visibility: hidden;\n  @include transition($carousel-transition);\n}\n\n.carousel-item.active,\n.carousel-item-next,\n.carousel-item-prev {\n  display: block;\n}\n\n.carousel-item-next:not(.carousel-item-left),\n.active.carousel-item-right {\n  transform: translateX(100%);\n}\n\n.carousel-item-prev:not(.carousel-item-right),\n.active.carousel-item-left {\n  transform: translateX(-100%);\n}\n\n\n//\n// Alternate transitions\n//\n\n.carousel-fade {\n  .carousel-item {\n    opacity: 0;\n    transition-property: opacity;\n    transform: none;\n  }\n\n  .carousel-item.active,\n  .carousel-item-next.carousel-item-left,\n  .carousel-item-prev.carousel-item-right {\n    z-index: 1;\n    opacity: 1;\n  }\n\n  .active.carousel-item-left,\n  .active.carousel-item-right {\n    z-index: 0;\n    opacity: 0;\n    @include transition(opacity 0s $carousel-transition-duration);\n  }\n}\n\n\n//\n// Left/right controls for nav\n//\n\n.carousel-control-prev,\n.carousel-control-next {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  z-index: 1;\n  // Use flex for alignment (1-3)\n  display: flex; // 1. allow flex styles\n  align-items: center; // 2. vertically center contents\n  justify-content: center; // 3. horizontally center contents\n  width: $carousel-control-width;\n  color: $carousel-control-color;\n  text-align: center;\n  opacity: $carousel-control-opacity;\n  @include transition($carousel-control-transition);\n\n  // Hover/focus state\n  @include hover-focus() {\n    color: $carousel-control-color;\n    text-decoration: none;\n    outline: 0;\n    opacity: $carousel-control-hover-opacity;\n  }\n}\n.carousel-control-prev {\n  left: 0;\n  @if $enable-gradients {\n    background-image: linear-gradient(90deg, rgba($black, .25), rgba($black, .001));\n  }\n}\n.carousel-control-next {\n  right: 0;\n  @if $enable-gradients {\n    background-image: linear-gradient(270deg, rgba($black, .25), rgba($black, .001));\n  }\n}\n\n// Icons for within\n.carousel-control-prev-icon,\n.carousel-control-next-icon {\n  display: inline-block;\n  width: $carousel-control-icon-width;\n  height: $carousel-control-icon-width;\n  background: no-repeat 50% / 100% 100%;\n}\n.carousel-control-prev-icon {\n  background-image: escape-svg($carousel-control-prev-icon-bg);\n}\n.carousel-control-next-icon {\n  background-image: escape-svg($carousel-control-next-icon-bg);\n}\n\n\n// Optional indicator pips\n//\n// Add an ordered list with the following class and add a list item for each\n// slide your carousel holds.\n\n.carousel-indicators {\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 15;\n  display: flex;\n  justify-content: center;\n  padding-left: 0; // override <ol> default\n  // Use the .carousel-control's width as margin so we don't overlay those\n  margin-right: $carousel-control-width;\n  margin-left: $carousel-control-width;\n  list-style: none;\n\n  li {\n    box-sizing: content-box;\n    flex: 0 1 auto;\n    width: $carousel-indicator-width;\n    height: $carousel-indicator-height;\n    margin-right: $carousel-indicator-spacer;\n    margin-left: $carousel-indicator-spacer;\n    text-indent: -999px;\n    cursor: pointer;\n    background-color: $carousel-indicator-active-bg;\n    background-clip: padding-box;\n    // Use transparent borders to increase the hit area by 10px on top and bottom.\n    border-top: $carousel-indicator-hit-area-height solid transparent;\n    border-bottom: $carousel-indicator-hit-area-height solid transparent;\n    opacity: .5;\n    @include transition($carousel-indicator-transition);\n  }\n\n  .active {\n    opacity: 1;\n  }\n}\n\n\n// Optional captions\n//\n//\n\n.carousel-caption {\n  position: absolute;\n  right: (100% - $carousel-caption-width) / 2;\n  bottom: 20px;\n  left: (100% - $carousel-caption-width) / 2;\n  z-index: 10;\n  padding-top: 20px;\n  padding-bottom: 20px;\n  color: $carousel-caption-color;\n  text-align: center;\n}\n", "@mixin clearfix() {\n  &::after {\n    display: block;\n    clear: both;\n    content: \"\";\n  }\n}\n", "//\n// Rotating border\n//\n\n@keyframes spinner-border {\n  to { transform: rotate(360deg); }\n}\n\n.spinner-border {\n  display: inline-block;\n  width: $spinner-width;\n  height: $spinner-height;\n  vertical-align: text-bottom;\n  border: $spinner-border-width solid currentColor;\n  border-right-color: transparent;\n  // stylelint-disable-next-line property-blacklist\n  border-radius: 50%;\n  animation: spinner-border .75s linear infinite;\n}\n\n.spinner-border-sm {\n  width: $spinner-width-sm;\n  height: $spinner-height-sm;\n  border-width: $spinner-border-width-sm;\n}\n\n//\n// Growing circle\n//\n\n@keyframes spinner-grow {\n  0% {\n    transform: scale(0);\n  }\n  50% {\n    opacity: 1;\n  }\n}\n\n.spinner-grow {\n  display: inline-block;\n  width: $spinner-width;\n  height: $spinner-height;\n  vertical-align: text-bottom;\n  background-color: currentColor;\n  // stylelint-disable-next-line property-blacklist\n  border-radius: 50%;\n  opacity: 0;\n  animation: spinner-grow .75s linear infinite;\n}\n\n.spinner-grow-sm {\n  width: $spinner-width-sm;\n  height: $spinner-height-sm;\n}\n", "// stylelint-disable declaration-no-important\n\n.align-baseline    { vertical-align: baseline !important; } // Browser default\n.align-top         { vertical-align: top !important; }\n.align-middle      { vertical-align: middle !important; }\n.align-bottom      { vertical-align: bottom !important; }\n.align-text-bottom { vertical-align: text-bottom !important; }\n.align-text-top    { vertical-align: text-top !important; }\n", "// stylelint-disable declaration-no-important\n\n// Contextual backgrounds\n\n@mixin bg-variant($parent, $color, $ignore-warning: false) {\n  #{$parent} {\n    background-color: $color !important;\n  }\n  a#{$parent},\n  button#{$parent} {\n    @include hover-focus() {\n      background-color: darken($color, 10%) !important;\n    }\n  }\n  @include deprecate(\"The `bg-variant` mixin\", \"v4.4.0\", \"v5\", $ignore-warning);\n}\n\n@mixin bg-gradient-variant($parent, $color) {\n  #{$parent} {\n    background: $color linear-gradient(180deg, mix($body-bg, $color, 15%), $color) repeat-x !important;\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n@each $color, $value in $theme-colors {\n  @include bg-variant(\".bg-#{$color}\", $value, true);\n}\n\n@if $enable-gradients {\n  @each $color, $value in $theme-colors {\n    @include bg-gradient-variant(\".bg-gradient-#{$color}\", $value);\n  }\n}\n\n.bg-white {\n  background-color: $white !important;\n}\n\n.bg-transparent {\n  background-color: transparent !important;\n}\n", "// stylelint-disable property-blacklist, declaration-no-important\n\n//\n// Border\n//\n\n.border         { border: $border-width solid $border-color !important; }\n.border-top     { border-top: $border-width solid $border-color !important; }\n.border-right   { border-right: $border-width solid $border-color !important; }\n.border-bottom  { border-bottom: $border-width solid $border-color !important; }\n.border-left    { border-left: $border-width solid $border-color !important; }\n\n.border-0        { border: 0 !important; }\n.border-top-0    { border-top: 0 !important; }\n.border-right-0  { border-right: 0 !important; }\n.border-bottom-0 { border-bottom: 0 !important; }\n.border-left-0   { border-left: 0 !important; }\n\n@each $color, $value in $theme-colors {\n  .border-#{$color} {\n    border-color: $value !important;\n  }\n}\n\n.border-white {\n  border-color: $white !important;\n}\n\n//\n// Border-radius\n//\n\n.rounded-sm {\n  border-radius: $border-radius-sm !important;\n}\n\n.rounded {\n  border-radius: $border-radius !important;\n}\n\n.rounded-top {\n  border-top-left-radius: $border-radius !important;\n  border-top-right-radius: $border-radius !important;\n}\n\n.rounded-right {\n  border-top-right-radius: $border-radius !important;\n  border-bottom-right-radius: $border-radius !important;\n}\n\n.rounded-bottom {\n  border-bottom-right-radius: $border-radius !important;\n  border-bottom-left-radius: $border-radius !important;\n}\n\n.rounded-left {\n  border-top-left-radius: $border-radius !important;\n  border-bottom-left-radius: $border-radius !important;\n}\n\n.rounded-lg {\n  border-radius: $border-radius-lg !important;\n}\n\n.rounded-circle {\n  border-radius: 50% !important;\n}\n\n.rounded-pill {\n  border-radius: $rounded-pill !important;\n}\n\n.rounded-0 {\n  border-radius: 0 !important;\n}\n", "// stylelint-disable declaration-no-important\n\n//\n// Utilities for common `display` values\n//\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @each $value in $displays {\n      .d#{$infix}-#{$value} { display: $value !important; }\n    }\n  }\n}\n\n\n//\n// Utilities for toggling `display` in print\n//\n\n@media print {\n  @each $value in $displays {\n    .d-print-#{$value} { display: $value !important; }\n  }\n}\n", "// Credit: <PERSON> and <PERSON><PERSON><PERSON> CSS.\n\n.embed-responsive {\n  position: relative;\n  display: block;\n  width: 100%;\n  padding: 0;\n  overflow: hidden;\n\n  &::before {\n    display: block;\n    content: \"\";\n  }\n\n  .embed-responsive-item,\n  iframe,\n  embed,\n  object,\n  video {\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    border: 0;\n  }\n}\n\n@each $embed-responsive-aspect-ratio in $embed-responsive-aspect-ratios {\n  $embed-responsive-aspect-ratio-x: nth($embed-responsive-aspect-ratio, 1);\n  $embed-responsive-aspect-ratio-y: nth($embed-responsive-aspect-ratio, 2);\n\n  .embed-responsive-#{$embed-responsive-aspect-ratio-x}by#{$embed-responsive-aspect-ratio-y} {\n    &::before {\n      padding-top: percentage($embed-responsive-aspect-ratio-y / $embed-responsive-aspect-ratio-x);\n    }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n// Flex variation\n//\n// Custom styles for additional flex alignment options.\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .flex#{$infix}-row            { flex-direction: row !important; }\n    .flex#{$infix}-column         { flex-direction: column !important; }\n    .flex#{$infix}-row-reverse    { flex-direction: row-reverse !important; }\n    .flex#{$infix}-column-reverse { flex-direction: column-reverse !important; }\n\n    .flex#{$infix}-wrap         { flex-wrap: wrap !important; }\n    .flex#{$infix}-nowrap       { flex-wrap: nowrap !important; }\n    .flex#{$infix}-wrap-reverse { flex-wrap: wrap-reverse !important; }\n    .flex#{$infix}-fill         { flex: 1 1 auto !important; }\n    .flex#{$infix}-grow-0       { flex-grow: 0 !important; }\n    .flex#{$infix}-grow-1       { flex-grow: 1 !important; }\n    .flex#{$infix}-shrink-0     { flex-shrink: 0 !important; }\n    .flex#{$infix}-shrink-1     { flex-shrink: 1 !important; }\n\n    .justify-content#{$infix}-start   { justify-content: flex-start !important; }\n    .justify-content#{$infix}-end     { justify-content: flex-end !important; }\n    .justify-content#{$infix}-center  { justify-content: center !important; }\n    .justify-content#{$infix}-between { justify-content: space-between !important; }\n    .justify-content#{$infix}-around  { justify-content: space-around !important; }\n\n    .align-items#{$infix}-start    { align-items: flex-start !important; }\n    .align-items#{$infix}-end      { align-items: flex-end !important; }\n    .align-items#{$infix}-center   { align-items: center !important; }\n    .align-items#{$infix}-baseline { align-items: baseline !important; }\n    .align-items#{$infix}-stretch  { align-items: stretch !important; }\n\n    .align-content#{$infix}-start   { align-content: flex-start !important; }\n    .align-content#{$infix}-end     { align-content: flex-end !important; }\n    .align-content#{$infix}-center  { align-content: center !important; }\n    .align-content#{$infix}-between { align-content: space-between !important; }\n    .align-content#{$infix}-around  { align-content: space-around !important; }\n    .align-content#{$infix}-stretch { align-content: stretch !important; }\n\n    .align-self#{$infix}-auto     { align-self: auto !important; }\n    .align-self#{$infix}-start    { align-self: flex-start !important; }\n    .align-self#{$infix}-end      { align-self: flex-end !important; }\n    .align-self#{$infix}-center   { align-self: center !important; }\n    .align-self#{$infix}-baseline { align-self: baseline !important; }\n    .align-self#{$infix}-stretch  { align-self: stretch !important; }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .float#{$infix}-left  { float: left !important; }\n    .float#{$infix}-right { float: right !important; }\n    .float#{$infix}-none  { float: none !important; }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n@each $value in $overflows {\n  .overflow-#{$value} { overflow: $value !important; }\n}\n", "// stylelint-disable declaration-no-important\n\n// Common values\n@each $position in $positions {\n  .position-#{$position} { position: $position !important; }\n}\n\n// Shorthand\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n.sticky-top {\n  @supports (position: sticky) {\n    position: sticky;\n    top: 0;\n    z-index: $zindex-sticky;\n  }\n}\n", "//\n// Screenreaders\n//\n\n.sr-only {\n  @include sr-only();\n}\n\n.sr-only-focusable {\n  @include sr-only-focusable();\n}\n", "// Only display content to screen readers\n//\n// See: https://a11yproject.com/posts/how-to-hide-content/\n// See: https://hugogiraudel.com/2016/10/13/css-hide-and-seek/\n\n@mixin sr-only() {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px; // Fix for https://github.com/twbs/bootstrap/issues/25686\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n}\n\n// Use in conjunction with .sr-only to only display content when it's focused.\n//\n// Useful for \"Skip to main content\" links; see https://www.w3.org/TR/2013/NOTE-WCAG20-TECHS-20130905/G1\n//\n// Credit: HTML5 Boilerplate\n\n@mixin sr-only-focusable() {\n  &:active,\n  &:focus {\n    position: static;\n    width: auto;\n    height: auto;\n    overflow: visible;\n    clip: auto;\n    white-space: normal;\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n.shadow-sm { box-shadow: $box-shadow-sm !important; }\n.shadow { box-shadow: $box-shadow !important; }\n.shadow-lg { box-shadow: $box-shadow-lg !important; }\n.shadow-none { box-shadow: none !important; }\n", "// stylelint-disable declaration-no-important\n\n// Width and height\n\n@each $prop, $abbrev in (width: w, height: h) {\n  @each $size, $length in $sizes {\n    .#{$abbrev}-#{$size} { #{$prop}: $length !important; }\n  }\n}\n\n.mw-100 { max-width: 100% !important; }\n.mh-100 { max-height: 100% !important; }\n\n// Viewport additional helpers\n\n.min-vw-100 { min-width: 100vw !important; }\n.min-vh-100 { min-height: 100vh !important; }\n\n.vw-100 { width: 100vw !important; }\n.vh-100 { height: 100vh !important; }\n", "//\n// Stretched link\n//\n\n.stretched-link {\n  &::after {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    z-index: 1;\n    // Just in case `pointer-events: none` is set on a parent\n    pointer-events: auto;\n    content: \"\";\n    // IE10 bugfix, see https://stackoverflow.com/questions/16947967/ie10-hover-pseudo-class-doesnt-work-without-background-color\n    background-color: rgba(0, 0, 0, 0);\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n// Mar<PERSON> and Padding\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @each $prop, $abbrev in (margin: m, padding: p) {\n      @each $size, $length in $spacers {\n        .#{$abbrev}#{$infix}-#{$size} { #{$prop}: $length !important; }\n        .#{$abbrev}t#{$infix}-#{$size},\n        .#{$abbrev}y#{$infix}-#{$size} {\n          #{$prop}-top: $length !important;\n        }\n        .#{$abbrev}r#{$infix}-#{$size},\n        .#{$abbrev}x#{$infix}-#{$size} {\n          #{$prop}-right: $length !important;\n        }\n        .#{$abbrev}b#{$infix}-#{$size},\n        .#{$abbrev}y#{$infix}-#{$size} {\n          #{$prop}-bottom: $length !important;\n        }\n        .#{$abbrev}l#{$infix}-#{$size},\n        .#{$abbrev}x#{$infix}-#{$size} {\n          #{$prop}-left: $length !important;\n        }\n      }\n    }\n\n    // Negative margins (e.g., where `.mb-n1` is negative version of `.mb-1`)\n    @each $size, $length in $spacers {\n      @if $size != 0 {\n        .m#{$infix}-n#{$size} { margin: -$length !important; }\n        .mt#{$infix}-n#{$size},\n        .my#{$infix}-n#{$size} {\n          margin-top: -$length !important;\n        }\n        .mr#{$infix}-n#{$size},\n        .mx#{$infix}-n#{$size} {\n          margin-right: -$length !important;\n        }\n        .mb#{$infix}-n#{$size},\n        .my#{$infix}-n#{$size} {\n          margin-bottom: -$length !important;\n        }\n        .ml#{$infix}-n#{$size},\n        .mx#{$infix}-n#{$size} {\n          margin-left: -$length !important;\n        }\n      }\n    }\n\n    // Some special margin utils\n    .m#{$infix}-auto { margin: auto !important; }\n    .mt#{$infix}-auto,\n    .my#{$infix}-auto {\n      margin-top: auto !important;\n    }\n    .mr#{$infix}-auto,\n    .mx#{$infix}-auto {\n      margin-right: auto !important;\n    }\n    .mb#{$infix}-auto,\n    .my#{$infix}-auto {\n      margin-bottom: auto !important;\n    }\n    .ml#{$infix}-auto,\n    .mx#{$infix}-auto {\n      margin-left: auto !important;\n    }\n  }\n}\n", "// stylelint-disable declaration-no-important\n\n//\n// Text\n//\n\n.text-monospace { font-family: $font-family-monospace !important; }\n\n// Alignment\n\n.text-justify  { text-align: justify !important; }\n.text-wrap     { white-space: normal !important; }\n.text-nowrap   { white-space: nowrap !important; }\n.text-truncate { @include text-truncate(); }\n\n// Responsive alignment\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .text#{$infix}-left   { text-align: left !important; }\n    .text#{$infix}-right  { text-align: right !important; }\n    .text#{$infix}-center { text-align: center !important; }\n  }\n}\n\n// Transformation\n\n.text-lowercase  { text-transform: lowercase !important; }\n.text-uppercase  { text-transform: uppercase !important; }\n.text-capitalize { text-transform: capitalize !important; }\n\n// Weight and italics\n\n.font-weight-light   { font-weight: $font-weight-light !important; }\n.font-weight-lighter { font-weight: $font-weight-lighter !important; }\n.font-weight-normal  { font-weight: $font-weight-normal !important; }\n.font-weight-bold    { font-weight: $font-weight-bold !important; }\n.font-weight-bolder  { font-weight: $font-weight-bolder !important; }\n.font-italic         { font-style: italic !important; }\n\n// Contextual colors\n\n.text-white { color: $white !important; }\n\n@each $color, $value in $theme-colors {\n  @include text-emphasis-variant(\".text-#{$color}\", $value, true);\n}\n\n.text-body { color: $body-color !important; }\n.text-muted { color: $text-muted !important; }\n\n.text-black-50 { color: rgba($black, .5) !important; }\n.text-white-50 { color: rgba($white, .5) !important; }\n\n// Misc\n\n.text-hide {\n  @include text-hide($ignore-warning: true);\n}\n\n.text-decoration-none { text-decoration: none !important; }\n\n.text-break {\n  word-break: break-word !important; // IE & < Edge 18\n  overflow-wrap: break-word !important;\n}\n\n// Reset\n\n.text-reset { color: inherit !important; }\n", "// Text truncate\n// Requires inline-block or block for proper styling\n\n@mixin text-truncate() {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n", "// stylelint-disable declaration-no-important\n\n// Typography\n\n@mixin text-emphasis-variant($parent, $color, $ignore-warning: false) {\n  #{$parent} {\n    color: $color !important;\n  }\n  @if $emphasized-link-hover-darken-percentage != 0 {\n    a#{$parent} {\n      @include hover-focus() {\n        color: darken($color, $emphasized-link-hover-darken-percentage) !important;\n      }\n    }\n  }\n  @include deprecate(\"`text-emphasis-variant()`\", \"v4.4.0\", \"v5\", $ignore-warning);\n}\n", "// CSS image replacement\n@mixin text-hide($ignore-warning: false) {\n  // stylelint-disable-next-line font-family-no-missing-generic-family-keyword\n  font: 0/0 a;\n  color: transparent;\n  text-shadow: none;\n  background-color: transparent;\n  border: 0;\n\n  @include deprecate(\"`text-hide()`\", \"v4.1.0\", \"v5\", $ignore-warning);\n}\n", "// stylelint-disable declaration-no-important\n\n//\n// Visibility utilities\n//\n\n.visible {\n  visibility: visible !important;\n}\n\n.invisible {\n  visibility: hidden !important;\n}\n", "// stylelint-disable declaration-no-important, selector-no-qualifying-type\n\n// Source: https://github.com/h5bp/main.css/blob/master/src/_print.css\n\n// ==========================================================================\n// Print styles.\n// Inlined to avoid the additional HTTP request:\n// https://www.phpied.com/delay-loading-your-print-css/\n// ==========================================================================\n\n@if $enable-print-styles {\n  @media print {\n    *,\n    *::before,\n    *::after {\n      // Bootstrap specific; comment out `color` and `background`\n      //color: $black !important; // Black prints faster\n      text-shadow: none !important;\n      //background: transparent !important;\n      box-shadow: none !important;\n    }\n\n    a {\n      &:not(.btn) {\n        text-decoration: underline;\n      }\n    }\n\n    // Bootstrap specific; comment the following selector out\n    //a[href]::after {\n    //  content: \" (\" attr(href) \")\";\n    //}\n\n    abbr[title]::after {\n      content: \" (\" attr(title) \")\";\n    }\n\n    // Bootstrap specific; comment the following selector out\n    //\n    // Don't show links that are fragment identifiers,\n    // or use the `javascript:` pseudo protocol\n    //\n\n    //a[href^=\"#\"]::after,\n    //a[href^=\"javascript:\"]::after {\n    // content: \"\";\n    //}\n\n    pre {\n      white-space: pre-wrap !important;\n    }\n    pre,\n    blockquote {\n      border: $border-width solid $gray-500; // Bootstrap custom code; using `$border-width` instead of 1px\n      page-break-inside: avoid;\n    }\n\n    //\n    // Printing Tables:\n    // https://web.archive.org/web/20180815150934/http://css-discuss.incutio.com/wiki/Printing_Tables\n    //\n\n    thead {\n      display: table-header-group;\n    }\n\n    tr,\n    img {\n      page-break-inside: avoid;\n    }\n\n    p,\n    h2,\n    h3 {\n      orphans: 3;\n      widows: 3;\n    }\n\n    h2,\n    h3 {\n      page-break-after: avoid;\n    }\n\n    // Bootstrap specific changes start\n\n    // Specify a size and min-width to make printing closer across browsers.\n    // We don't set margin here because it breaks `size` in Chrome. We also\n    // don't use `!important` on `size` as it breaks in Chrome.\n    @page {\n      size: $print-page-size;\n    }\n    body {\n      min-width: $print-body-min-width !important;\n    }\n    .container {\n      min-width: $print-body-min-width !important;\n    }\n\n    // Bootstrap components\n    .navbar {\n      display: none;\n    }\n    .badge {\n      border: $border-width solid $black;\n    }\n\n    .table {\n      border-collapse: collapse !important;\n\n      td,\n      th {\n        background-color: $white !important;\n      }\n    }\n\n    .table-bordered {\n      th,\n      td {\n        border: 1px solid $gray-300 !important;\n      }\n    }\n\n    .table-dark {\n      color: inherit;\n\n      th,\n      td,\n      thead th,\n      tbody + tbody {\n        border-color: $table-border-color;\n      }\n    }\n\n    .table .thead-dark th {\n      color: inherit;\n      border-color: $table-border-color;\n    }\n\n    // Bootstrap specific changes end\n  }\n}\n", "//\r\n// Core: Layout\r\n//\r\n\r\nhtml.scroll-smooth {\r\n  scroll-behavior: smooth;\r\n}\r\n\r\nhtml,\r\nbody,\r\n.wrapper {\r\n  min-height: 100%;\r\n}\r\n\r\n.wrapper {\r\n  position: relative;\r\n\r\n  & .content-wrapper {\r\n    min-height: calc(100vh - #{$main-header-height} - #{$main-footer-height});\r\n  }\r\n\r\n  .layout-boxed & {\r\n    @include box-shadow(0 0 10 rgba($black, .3));\r\n\r\n    &,\r\n    &::before {\r\n      margin: 0 auto;\r\n      max-width: $boxed-layout-max-width;\r\n    }\r\n\r\n    & .main-sidebar {\r\n      left: inherit;\r\n    }\r\n  }\r\n\r\n  @supports not (-webkit-touch-callout: none) {\r\n    .layout-fixed & .sidebar {\r\n      height: calc(100vh - (#{$main-header-height-inner} + #{$main-header-bottom-border-width}));\r\n    }\r\n    .layout-fixed.text-sm & .sidebar {\r\n      height: calc(100vh - (#{$main-header-height-sm-inner} + #{$main-header-bottom-border-width}));\r\n    }\r\n  }\r\n\r\n  .layout-navbar-fixed.layout-fixed & {\r\n    .control-sidebar {\r\n      top: $main-header-height;\r\n    }\r\n\r\n    .main-header.text-sm ~ .control-sidebar {\r\n      top: $main-header-height-sm;\r\n    }\r\n\r\n    .sidebar {\r\n      margin-top: $main-header-height;\r\n    }\r\n\r\n    .brand-link.text-sm ~ .sidebar {\r\n      margin-top: $main-header-height-sm;\r\n    }\r\n  }\r\n\r\n  .layout-navbar-fixed.layout-fixed.text-sm & {\r\n    .control-sidebar {\r\n      top: $main-header-height-sm;\r\n    }\r\n\r\n    .sidebar {\r\n      margin-top: $main-header-height-sm;\r\n    }\r\n  }\r\n\r\n  .layout-navbar-fixed.sidebar-mini.sidebar-collapse &,\r\n  .layout-navbar-fixed.sidebar-mini-md.sidebar-collapse & {\r\n    .brand-link {\r\n      height: $main-header-height;\r\n      width: $sidebar-mini-width;\r\n\r\n      &.text-sm {\r\n        height: $main-header-height-sm;\r\n      }\r\n    }\r\n  }\r\n\r\n  .layout-navbar-fixed.sidebar-mini.sidebar-collapse.text-sm &,\r\n  .layout-navbar-fixed.sidebar-mini-md.sidebar-collapse.text-sm & {\r\n    .brand-link {\r\n      height: $main-header-height-sm;\r\n    }\r\n  }\r\n\r\n  body:not(.layout-fixed).layout-navbar-fixed & {\r\n    .main-sidebar {\r\n      // margin-top: calc(#{$main-header-height} / -1);\r\n\r\n      // .sidebar {\r\n      //   margin-top: $main-header-height;\r\n      // }\r\n    }\r\n  }\r\n\r\n  body:not(.layout-fixed).layout-navbar-fixed.text-sm & {\r\n    .main-sidebar {\r\n      margin-top: calc(#{$main-header-height-sm} / -1);\r\n\r\n      .sidebar {\r\n        margin-top: $main-header-height-sm;\r\n      }\r\n    }\r\n  }\r\n\r\n  .layout-navbar-fixed & {\r\n    .control-sidebar {\r\n      top: 0;\r\n    }\r\n\r\n    a.anchor {\r\n      display: block;\r\n      position: relative;\r\n      top: calc((#{$main-header-height-inner} + #{$main-header-bottom-border-width} + (#{$main-header-link-padding-y} * 2)) / -1);\r\n    }\r\n\r\n    .main-sidebar:hover {\r\n      .brand-link {\r\n        transition: width $transition-speed $transition-fn;\r\n        width: $sidebar-width;\r\n      }\r\n    }\r\n\r\n    .brand-link {\r\n      overflow: hidden;\r\n      position: fixed;\r\n      top: 0;\r\n      transition: width $transition-speed $transition-fn;\r\n      width: $sidebar-width;\r\n      z-index: $zindex-main-header + 1;\r\n    }\r\n\r\n    // Sidebar variants brand-link fix\r\n    @each $name, $color in $theme-colors {\r\n      .sidebar-dark-#{$name} .brand-link:not([class*=\"navbar\"]) {\r\n        background-color: $sidebar-dark-bg;\r\n      }\r\n\r\n      .sidebar-light-#{$name} .brand-link:not([class*=\"navbar\"]) {\r\n        background-color: $sidebar-light-bg;\r\n      }\r\n    }\r\n\r\n    .content-wrapper {\r\n      margin-top: $main-header-height;\r\n    }\r\n\r\n    .main-header.text-sm ~ .content-wrapper {\r\n      margin-top: $main-header-height-sm;\r\n    }\r\n\r\n    .main-header {\r\n      left: 0;\r\n      position: fixed;\r\n      right: 0;\r\n      top: 0;\r\n      z-index: $zindex-main-header - 1;\r\n    }\r\n  }\r\n\r\n  .layout-navbar-fixed.text-sm & {\r\n    .content-wrapper {\r\n      margin-top: $main-header-height-sm;\r\n    }\r\n  }\r\n\r\n  .layout-navbar-not-fixed & {\r\n    .brand-link {\r\n      position: static;\r\n    }\r\n\r\n    .sidebar,\r\n    .content-wrapper {\r\n      margin-top: 0;\r\n    }\r\n\r\n    .main-header {\r\n      position: static;\r\n    }\r\n  }\r\n\r\n  .layout-navbar-not-fixed.layout-fixed & {\r\n    .sidebar {\r\n      margin-top: 0;\r\n    }\r\n  }\r\n\r\n  @each $breakpoint in map-keys($grid-breakpoints) {\r\n    @include media-breakpoint-up($breakpoint) {\r\n      $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\r\n\r\n      .layout#{$infix}-navbar-fixed.layout-fixed & {\r\n        .control-sidebar {\r\n          top: $main-header-height;\r\n        }\r\n\r\n        .text-sm & .main-header ~ .control-sidebar,\r\n        .main-header.text-sm ~ .control-sidebar {\r\n          top: $main-header-height-sm;\r\n        }\r\n\r\n        .sidebar {\r\n          margin-top: $main-header-height;\r\n        }\r\n\r\n        .text-sm & .brand-link ~ .sidebar,\r\n        .brand-link.text-sm ~ .sidebar {\r\n          margin-top: $main-header-height-sm;\r\n        }\r\n      }\r\n\r\n      .layout#{$infix}-navbar-fixed.layout-fixed.text-sm & {\r\n        .control-sidebar {\r\n          top: $main-header-height-sm;\r\n        }\r\n\r\n        .sidebar {\r\n          margin-top: $main-header-height-sm;\r\n        }\r\n      }\r\n\r\n      .layout#{$infix}-navbar-fixed & {\r\n        .control-sidebar {\r\n          top: 0;\r\n        }\r\n\r\n        a.anchor {\r\n          display: block;\r\n          position: relative;\r\n          top: calc((#{$main-header-height-inner} + #{$main-header-bottom-border-width} + (#{$main-header-link-padding-y} * 2)) / -1);\r\n        }\r\n\r\n        &.sidebar-collapse {\r\n          .brand-link {\r\n            height: $main-header-height;\r\n            transition: width $transition-speed $transition-fn;\r\n            width: $sidebar-mini-width;\r\n\r\n            .text-sm &,\r\n            &.text-sm {\r\n              height: $main-header-height-sm;\r\n            }\r\n          }\r\n\r\n          .main-sidebar:hover {\r\n            .brand-link {\r\n              transition: width $transition-speed $transition-fn;\r\n              width: $sidebar-width;\r\n            }\r\n          }\r\n        }\r\n\r\n        .brand-link {\r\n          overflow: hidden;\r\n          position: fixed;\r\n          top: 0;\r\n          transition: width $transition-speed $transition-fn;\r\n          width: $sidebar-width;\r\n          z-index: $zindex-main-header + 1;\r\n        }\r\n\r\n        // Sidebar variants brand-link fix\r\n        @each $name, $color in $theme-colors {\r\n          .sidebar-dark-#{$name} .brand-link:not([class*=\"navbar\"]) {\r\n            background-color: $sidebar-dark-bg;\r\n          }\r\n\r\n          .sidebar-light-#{$name} .brand-link:not([class*=\"navbar\"]) {\r\n            background-color: $sidebar-light-bg;\r\n          }\r\n        }\r\n\r\n        .content-wrapper {\r\n          margin-top: $main-header-height;\r\n        }\r\n\r\n        .text-sm & .main-header ~ .content-wrapper,\r\n        .main-header.text-sm ~ .content-wrapper {\r\n          margin-top: $main-header-height-sm;\r\n        }\r\n\r\n        .main-header {\r\n          left: 0;\r\n          position: fixed;\r\n          right: 0;\r\n          top: 0;\r\n          z-index: $zindex-main-sidebar - 1;\r\n        }\r\n      }\r\n\r\n      .layout#{$infix}-navbar-fixed.text-sm & {\r\n        .content-wrapper {\r\n          margin-top: $main-header-height-sm;\r\n        }\r\n      }\r\n\r\n      body:not(.layout-fixed).layout#{$infix}-navbar-fixed & {\r\n        // .main-sidebar {\r\n        //   margin-top: calc(#{$main-header-height} / -1);\r\n\r\n        //   .sidebar {\r\n        //     margin-top: $main-header-height;\r\n        //   }\r\n        // }\r\n      }\r\n\r\n      body:not(.layout-fixed).layout#{$infix}-navbar-fixed.text-sm & {\r\n        .main-sidebar {\r\n          margin-top: calc(#{$main-header-height-sm} / -1);\r\n\r\n          .sidebar {\r\n            margin-top: $main-header-height-sm;\r\n          }\r\n        }\r\n      }\r\n\r\n      .layout#{$infix}-navbar-not-fixed & {\r\n        .brand-link {\r\n          position: static;\r\n        }\r\n\r\n        .sidebar,\r\n        .content-wrapper {\r\n          margin-top: 0;\r\n        }\r\n\r\n        .main-header {\r\n          position: static;\r\n        }\r\n      }\r\n\r\n      .layout#{$infix}-navbar-not-fixed.layout-fixed & {\r\n        .sidebar {\r\n          margin-top: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .layout-footer-fixed & {\r\n    .control-sidebar {\r\n      bottom: 0;\r\n    }\r\n  }\r\n\r\n  .layout-footer-fixed & {\r\n    .main-footer {\r\n      bottom: 0;\r\n      left: 0;\r\n      position: fixed;\r\n      right: 0;\r\n      z-index: $zindex-main-footer;\r\n    }\r\n  }\r\n\r\n  .layout-footer-not-fixed & {\r\n    .main-footer {\r\n      position: static;\r\n    }\r\n\r\n    .content-wrapper {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n\r\n  @each $breakpoint in map-keys($grid-breakpoints) {\r\n    @include media-breakpoint-up($breakpoint) {\r\n      $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\r\n      .layout#{$infix}-footer-fixed & {\r\n        .control-sidebar {\r\n          bottom: 0;\r\n        }\r\n      }\r\n\r\n      .layout#{$infix}-footer-fixed & {\r\n        .main-footer {\r\n          bottom: 0;\r\n          left: 0;\r\n          position: fixed;\r\n          right: 0;\r\n          z-index: $zindex-main-footer;\r\n        }\r\n\r\n        .content-wrapper {\r\n          padding-bottom: $main-footer-height;\r\n        }\r\n      }\r\n\r\n      .layout#{$infix}-footer-not-fixed & {\r\n        .main-footer {\r\n          position: static;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .layout-top-nav & {\r\n    margin-left: 0;\r\n\r\n    .main-header {    \r\n      .brand-image {\r\n        margin-top: -.5rem;\r\n        margin-right: .2rem;\r\n        height: 33px;\r\n      }\r\n    }\r\n\r\n    & .main-sidebar {\r\n      bottom: inherit;\r\n      height: inherit;\r\n    }\r\n\r\n    & .content-wrapper,\r\n    & .main-header,\r\n    & .main-footer {\r\n      margin-left: 0;\r\n    }\r\n  }\r\n}\r\n\r\n\r\nbody.sidebar-collapse:not(.sidebar-mini-md):not(.sidebar-mini) {\r\n  .content-wrapper,\r\n  .main-footer,\r\n  .main-header {\r\n    &,\r\n    &::before {\r\n      margin-left: 0;\r\n    }\r\n  }\r\n}\r\n\r\nbody:not(.sidebar-mini-md) {\r\n  .content-wrapper,\r\n  .main-footer,\r\n  .main-header {\r\n    @include media-breakpoint-up(md) {\r\n      @include transition(margin-left $transition-speed $transition-fn);\r\n\r\n      margin-left: $sidebar-width;\r\n\r\n      .sidebar-collapse & {\r\n        margin-left: 0;\r\n      }\r\n    }\r\n\r\n    @include media-breakpoint-down(md) {\r\n      &,\r\n      &::before {\r\n        margin-left: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.sidebar-mini-md {\r\n  .content-wrapper,\r\n  .main-footer,\r\n  .main-header {\r\n    @include media-breakpoint-up(md) {\r\n      @include transition(margin-left $transition-speed $transition-fn);\r\n\r\n      margin-left: $sidebar-width;\r\n\r\n      .sidebar-collapse & {\r\n        margin-left: $sidebar-mini-width;\r\n      }\r\n    }\r\n\r\n    @include media-breakpoint-down(md) {\r\n      &,\r\n      &::before {\r\n        margin-left: $sidebar-mini-width;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.content-wrapper {\r\n  background: $main-bg;\r\n\r\n  > .content {\r\n    padding: $content-padding-y $content-padding-x;\r\n  }\r\n}\r\n\r\n.main-sidebar {\r\n  &,\r\n  &::before {\r\n    $local-sidebar-transition: margin-left $transition-speed $transition-fn, width $transition-speed $transition-fn;\r\n    @include transition($local-sidebar-transition);\r\n    width: $sidebar-width;\r\n  }\r\n\r\n  .sidebar-collapse:not(.sidebar-mini):not(.sidebar-mini-md) & {\r\n    &,\r\n    &::before {\r\n      box-shadow: none !important;\r\n    }\r\n  }\r\n\r\n  .sidebar-collapse & {\r\n    &,\r\n    &::before {\r\n      margin-left: -$sidebar-width;\r\n    }\r\n\r\n    .nav-sidebar.nav-child-indent .nav-treeview {\r\n      padding: 0;\r\n    }\r\n  }\r\n\r\n  @include media-breakpoint-down(sm) {\r\n    &,\r\n    &::before {\r\n      box-shadow: none !important;\r\n      margin-left: -$sidebar-width;\r\n    }\r\n\r\n    .sidebar-open & {\r\n      &,\r\n      &::before {\r\n        margin-left: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n:not(.layout-fixed) {\r\n  .main-sidebar {\r\n    height: inherit;\r\n    min-height: 100%;\r\n    position: absolute;\r\n    top: 0;\r\n  }\r\n}\r\n\r\n.layout-fixed {\r\n  .brand-link {\r\n    width: $sidebar-width;\r\n  }\r\n\r\n  .main-sidebar {\r\n    bottom: 0;\r\n    float: none;\r\n    height: 100vh;\r\n    left: 0;\r\n    position: fixed;\r\n    top: 0;\r\n  }\r\n\r\n  .control-sidebar {\r\n    bottom: 0;\r\n    float: none;\r\n    height: 100vh;\r\n    position: fixed;\r\n    top: 0;\r\n\r\n    .control-sidebar-content {\r\n      height: calc(100vh - #{$main-header-height});\r\n    }\r\n  }\r\n}\r\n\r\n@supports (-webkit-touch-callout: none) {\r\n  .layout-fixed {\r\n    .main-sidebar {\r\n      height: inherit;\r\n    }\r\n  }\r\n}\r\n\r\n.main-footer {\r\n  background: $main-footer-bg;\r\n  border-top: $main-footer-border-top;\r\n  color: lighten($gray-700, 25%);\r\n  padding: $main-footer-padding;\r\n\r\n  .text-sm &,\r\n  &.text-sm {\r\n    padding: $main-footer-padding-sm;\r\n  }\r\n}\r\n\r\n.content-header {\r\n  padding: 15px $content-padding-x;\r\n\r\n  .text-sm & {\r\n    padding: 10px $content-padding-x;\r\n  }\r\n\r\n  h1 {\r\n    font-size: 1.8rem;\r\n    margin: 0;\r\n\r\n    .text-sm & {\r\n      font-size: 1.5rem;\r\n    }\r\n  }\r\n\r\n  .breadcrumb {\r\n    background: transparent;\r\n    line-height: 1.8rem;\r\n    margin-bottom: 0;\r\n    padding: 0;\r\n\r\n    .text-sm & {\r\n      line-height: 1.5rem;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\r\n.hold-transition {\r\n  .content-wrapper,\r\n  .main-header,\r\n  .main-sidebar,\r\n  .main-sidebar *,\r\n  .control-sidebar,\r\n  .control-sidebar *,\r\n  .main-footer {\r\n    transition: none !important;\r\n    animation-duration: 0s !important;\r\n  }\r\n}\r\n", "//\n// Core: Variables\n//\n\n// COLORS\n// --------------------------------------------------------\n$blue: #0073b7 !default;\n$lightblue: #3c8dbc !default;\n$navy: #001f3f !default;\n$teal: #39cccc !default;\n$olive: #3d9970 !default;\n$lime: #01ff70 !default;\n$orange: #ff851b !default;\n$fuchsia: #f012be !default;\n$purple: #605ca8 !default;\n$maroon: #d81b60 !default;\n$black: #111 !default;\n$gray-x-light: #d2d6de !default;\n\n$colors: map-merge((\n    'lightblue': $lightblue,\n    'navy': $navy,\n    'olive': $olive,\n    'lime': $lime,\n    'fuchsia': $fuchsia,\n    'maroon': $maroon,\n), $colors);\n\n// LAYOUT\n// --------------------------------------------------------\n\n$font-size-root: 1rem !default;\n\n// Sidebar\n$sidebar-width: 250px !default;\n$sidebar-padding-x: 0.5rem !default;\n$sidebar-padding-y: 0 !default;\n\n// Boxed layout maximum width\n$boxed-layout-max-width: 1250px !default;\n\n// When to show the smaller logo\n$screen-header-collapse: map-get($grid-breakpoints, md) !default;\n\n// Body background (Affects main content background only)\n$main-bg: #f4f6f9 !default;\n\n// Content padding\n$content-padding-y: 0 !default;\n$content-padding-x: $navbar-padding-x !default;\n\n// IMAGE SIZES\n// --------------------------------------------------------\n$img-size-sm: 1.875rem !default;\n$img-size-md: 3.75rem !default;\n$img-size-lg: 6.25rem !default;\n$img-size-push: .625rem !default;\n\n// MAIN HEADER\n// --------------------------------------------------------\n$main-header-bottom-border-width: $border-width !default;\n$main-header-bottom-border-color: $gray-300 !default;\n$main-header-bottom-border: $main-header-bottom-border-width solid $main-header-bottom-border-color !default;\n$main-header-link-padding-y: $navbar-padding-y !default;\n$main-header-link-padding-x: $navbar-padding-x !default;\n$main-header-brand-padding-y: $navbar-brand-padding-y !default;\n$main-header-brand-padding-x: $navbar-padding-x !default;\n$main-header-height-inner: ($nav-link-height + ($main-header-link-padding-y * 2)) !default;\n$main-header-height: calc(#{$main-header-height-inner} + #{$main-header-bottom-border-width}) !default;\n$nav-link-sm-padding-y: .35rem !default;\n$nav-link-sm-height: ($font-size-sm * $line-height-sm + $nav-link-sm-padding-y * 1.785) !default;\n$main-header-height-sm-inner: ($nav-link-sm-height + ($main-header-link-padding-y * 2)) !default;\n$main-header-height-sm: calc(#{$main-header-height-sm-inner} + #{$main-header-bottom-border-width}) !default;\n\n\n// Main header skins\n$main-header-dark-form-control-bg: hsla(100, 100%, 100%, 0.2) !default;\n$main-header-dark-form-control-focused-bg: hsla(100, 100%, 100%, 0.6) !default;\n$main-header-dark-form-control-focused-color: $gray-800 !default;\n$main-header-dark-form-control-border: 0 !default;\n$main-header-dark-form-control-focused-border: 0 !default;\n$main-header-dark-placeholder-color: hsla(100, 100%, 100%, 0.6) !default;\n\n$main-header-light-form-control-bg: darken($gray-100, 2%) !default;\n$main-header-light-form-control-focused-bg: $gray-200 !default;\n$main-header-light-form-control-focused-color: $gray-800 !default;\n$main-header-light-form-control-border: 0 !default;\n$main-header-light-form-control-focused-border: 0 !default;\n$main-header-light-placeholder-color: hsla(0, 0%, 0%, 0.6) !default;\n\n// MAIN FOOTER\n// --------------------------------------------------------\n$main-footer-padding: 1rem !default;\n$main-footer-padding-sm: $main-footer-padding * .812 !default;\n$main-footer-border-top-width: 1px !default;\n$main-footer-border-top-color: $gray-300 !default;\n$main-footer-border-top: $main-footer-border-top-width solid $main-footer-border-top-color !default;\n$main-footer-height-inner: (($font-size-root * $line-height-base) + ($main-footer-padding * 2)) !default;\n$main-footer-height: calc(#{$main-footer-height-inner} + #{$main-footer-border-top-width}) !default;\n$main-footer-height-sm-inner: (($font-size-sm * $line-height-base) + ($main-footer-padding-sm * 2)) !default;\n$main-footer-height-sm: calc(#{$main-footer-height-sm-inner} + #{$main-footer-border-top-width}) !default;\n$main-footer-bg: $white !default;\n\n// SIDEBAR SKINS\n// --------------------------------------------------------\n\n// Dark sidebar\n$sidebar-dark-bg: $dark !default;\n$sidebar-dark-hover-bg: hsla(100, 100%, 100%, 0.1) !default;\n$sidebar-dark-color: #C2C7D0 !default;\n$sidebar-dark-hover-color: $white !default;\n$sidebar-dark-active-color: $white !default;\n$sidebar-dark-submenu-bg: transparent !default;\n$sidebar-dark-submenu-color: #C2C7D0 !default;\n$sidebar-dark-submenu-hover-color: $white !default;\n$sidebar-dark-submenu-hover-bg: $sidebar-dark-hover-bg !default;\n$sidebar-dark-submenu-active-color: $sidebar-dark-bg !default;\n$sidebar-dark-submenu-active-bg: hsla(100, 100%, 100%, 0.9) !default;\n$sidebar-dark-header-color: $white !default;\n\n// Light sidebar\n$sidebar-light-bg: $white !default;\n$sidebar-light-hover-bg: rgba($black, .1) !default;\n$sidebar-light-color: $gray-800 !default;\n$sidebar-light-hover-color: $gray-900 !default;\n$sidebar-light-active-color: $black !default;\n$sidebar-light-submenu-bg: transparent !default;\n$sidebar-light-submenu-color: #777 !default;\n$sidebar-light-submenu-hover-color: #000 !default;\n$sidebar-light-submenu-hover-bg: $sidebar-light-hover-bg !default;\n$sidebar-light-submenu-active-color: $sidebar-light-hover-color !default;\n$sidebar-light-submenu-active-bg: $sidebar-light-submenu-hover-bg !default;\n$sidebar-light-header-color: $gray-800 !default;\n\n// SIDEBAR MINI\n// --------------------------------------------------------\n$sidebar-mini-width: ($nav-link-padding-x + $sidebar-padding-x + .8rem) * 2 !default;\n$sidebar-nav-icon-width: $sidebar-mini-width - (($sidebar-padding-x + $nav-link-padding-x) * 2) !default;\n$sidebar-user-image-width: $sidebar-nav-icon-width + ($nav-link-padding-x / 2) !default;\n\n// CONTROL SIDEBAR\n// --------------------------------------------------------\n$control-sidebar-width: $sidebar-width !default;\n\n// Cards\n// --------------------------------------------------------\n$card-border-color: $gray-100 !default;\n$card-dark-border-color: lighten($gray-900, 10%) !default;\n$card-shadow: 0 0 1px rgba(0, 0, 0, .125), 0 1px 3px rgba(0, 0, 0, .2) !default;\n$card-title-font-size: 1.1rem !default;\n$card-title-font-size-sm: 1rem !default;\n$card-title-font-weight: $font-weight-normal !default;\n$card-nav-link-padding-sm-y: .4rem !default;\n$card-nav-link-padding-sm-x: .8rem !default;\n$card-img-size: $img-size-sm !default;\n\n// PROGRESS BARS\n// --------------------------------------------------------\n$progress-bar-border-radius: 1px !default;\n$progress-bar-sm-border-radius: 1px !default;\n$progress-bar-xs-border-radius: 1px !default;\n\n// DIRECT CHAT\n// --------------------------------------------------------\n$direct-chat-height: 250px !default;\n$direct-chat-default-msg-bg: $gray-x-light !default;\n$direct-chat-default-font-color: #444 !default;\n$direct-chat-default-msg-border-color: $gray-x-light !default;\n\n// CHAT WIDGET\n// --------------------------------------------------------\n$attachment-border-radius: 3px !default;\n\n// Z-INDEX\n// --------------------------------------------------------\n$zindex-main-header: $zindex-fixed + 4 !default;\n$zindex-main-sidebar: $zindex-fixed + 8 !default;\n$zindex-main-footer: $zindex-fixed + 2 !default;\n$zindex-control-sidebar: $zindex-fixed + 1 !default;\n$zindex-sidebar-mini-links: 010 !default;\n$zindex-toasts: $zindex-main-sidebar + 2 !default;\n\n// TRANSITIONS SETTINGS\n// --------------------------------------------------------\n\n// Transition global options\n$transition-speed: 0.3s !default;\n$transition-fn: ease-in-out !default;\n\n// TEXT\n// --------------------------------------------------------\n$font-size-xs: ($font-size-base * .75) !default;\n$font-size-xl: ($font-size-base * 2) !default;\n\n\n// BUTTON\n// --------------------------------------------------------\n$button-default-background-color: $gray-100 !default;\n$button-default-color: #444 !default;\n$button-default-border-color: #ddd !default;\n\n$button-padding-y-xs: .125rem !default;\n$button-padding-x-xs: .25rem !default;\n$button-line-height-xs: $line-height-sm !default;\n$button-font-size-xs: ($font-size-base * .75) !default;\n$button-border-radius-xs: .15rem !default;\n\n  \n// ELEVATION\n// --------------------------------------------------------\n$elevations: ();\n$elevations: map-merge((\n    1: unquote('0 1px 3px ' + rgba($black, 0.12) + ', 0 1px 2px ' + rgba($black, 0.24)),\n    2: unquote('0 3px 6px ' + rgba($black, 0.16) + ', 0 3px 6px ' + rgba($black, 0.23)),\n    3: unquote('0 10px 20px ' + rgba($black, 0.19) + ', 0 6px 6px ' + rgba($black, 0.23)),\n    4: unquote('0 14px 28px ' + rgba($black, 0.25) + ', 0 10px 10px ' + rgba($black, 0.22)),\n    5: unquote('0 19px 38px ' + rgba($black, 0.30) + ', 0 15px 12px ' + rgba($black, 0.22)),\n), $elevations);\n  \n// RIBBON\n// --------------------------------------------------------\n$ribbon-border-size: 3px !default;\n$ribbon-line-height: 100% !default;\n$ribbon-padding: .375rem 0 !default;\n$ribbon-font-size: .8rem !default;\n$ribbon-width: 90px !default;\n$ribbon-wrapper-size: 70px !default;\n$ribbon-top: 10px !default;\n$ribbon-right: -2px !default;\n$ribbon-lg-wrapper-size: 120px !default;\n$ribbon-lg-width: 160px !default;\n$ribbon-lg-top: 26px !default;\n$ribbon-lg-right: 0px !default;\n$ribbon-xl-wrapper-size: 180px !default;\n$ribbon-xl-width: 240px !default;\n$ribbon-xl-top: 47px !default;\n$ribbon-xl-right: 4px !default;\n", "//\n// Component: Main Header\n//\n \n.main-header {\n  border-bottom: $main-header-bottom-border;\n  z-index: $zindex-main-header;\n\n  .nav-link {\n    height: $nav-link-height;\n    position: relative;\n  }\n\n  .text-sm &,\n  &.text-sm {\n    .nav-link {\n      height: $nav-link-sm-height;\n      padding: $nav-link-sm-padding-y $nav-link-padding-x;\n\n      > .fa,\n      > .fas,\n      > .far,\n      > .fab,\n      > .glyphicon,\n      > .ion {\n        font-size: $font-size-sm;\n      }\n    }\n\n  }\n\n  .navbar-nav {\n    .nav-item {\n      margin: 0;\n    }\n\n    &[class*='-right'] {\n      .dropdown-menu {\n        left: auto;\n        margin-top: -3px;\n        right: 0;\n\n        @media (max-width: breakpoint-max(xs)) {\n          left: 0;\n          right: auto;\n        }\n      }\n    }\n  }\n}\n\n// Add this class to images within a nav-link\n.navbar-img {\n  height: $main-header-height / 2;\n  width: auto;\n}\n\n// Navbar badge\n.navbar-badge {\n  font-size: .6rem;\n  font-weight: 300;\n  padding: 2px 4px;\n  position: absolute;\n  right: 5px;\n  top: 9px;\n}\n\n.btn-navbar {\n  background-color: transparent;\n  border-left-width: 0;\n}\n\n.form-control-navbar {\n  border-right-width: 0;\n\n  & + .input-group-append {\n    margin-left: 0;\n  }\n}\n\n.form-control-navbar,\n.btn-navbar {\n  transition: none;\n}\n\n.navbar-dark {\n  .form-control-navbar,\n  .btn-navbar {\n    background-color: $main-header-dark-form-control-bg;\n    border: $main-header-dark-form-control-border;\n  }\n\n  .form-control-navbar {\n    &::placeholder {\n      color: $main-header-dark-placeholder-color;\n    }\n\n    + .input-group-append > .btn-navbar {\n      color: $main-header-dark-placeholder-color;\n    }\n\n    &:focus {\n      &,\n      & + .input-group-append .btn-navbar {\n        background-color: $main-header-dark-form-control-focused-bg;\n        border: $main-header-dark-form-control-focused-border !important;\n        color: $main-header-dark-form-control-focused-color;\n      }\n    }\n  }\n}\n\n.navbar-light {\n  .form-control-navbar,\n  .btn-navbar {\n    background-color: $main-header-light-form-control-bg;\n    border: $main-header-light-form-control-border;\n  }\n\n  .form-control-navbar {\n    &::placeholder {\n      color: $main-header-light-placeholder-color;\n    }\n\n    + .input-group-append > .btn-navbar {\n      color: $main-header-light-placeholder-color;\n    }\n\n    &:focus {\n      &,\n      & + .input-group-append .btn-navbar {\n        background-color: $main-header-light-form-control-focused-bg;\n        border: $main-header-light-form-control-focused-border !important;\n        color: $main-header-light-form-control-focused-color;\n      }\n    }\n  }\n}\n", "//\n// Component: Brand\n//\n\n.brand-link {\n  $brand-link-padding-y: $navbar-brand-padding-y + $navbar-padding-y;\n  display: block;\n  font-size: $navbar-brand-font-size;\n  line-height: $line-height-lg;\n  padding: $brand-link-padding-y $sidebar-padding-x;\n  transition: width $transition-speed $transition-fn;\n  white-space: nowrap;\n\n  &:hover {\n    color: $white;\n    text-decoration: none;\n  }\n\n  .text-sm & {\n    font-size: inherit;\n  }\n\n  [class*='sidebar-dark'] & {\n    border-bottom: 1px solid lighten($dark, 10%);\n    color: rgba($white, .8);\n  }\n\n  [class*='sidebar-light'] & {\n    border-bottom: 1px solid $gray-300;\n    color: rgba($black, .8);\n  }\n\n  .brand-image {\n    float: left;\n    line-height: .8;\n    margin-left: .8rem;\n    margin-right: .5rem;\n    margin-top: -3px;\n    max-height: 33px;\n    width: auto;\n  }\n\n  .brand-image-xs {\n    float: left;\n    line-height: .8;\n    margin-top: -.1rem;\n    max-height: 33px;\n    width: auto;\n  }\n\n  .brand-image-xl {\n    line-height: .8;\n    max-height: 40px;\n    width: auto;\n\n    &.single {\n      margin-top: -.3rem;\n    }\n  }\n\n  &.text-sm,\n  .text-sm & {\n    .brand-image {\n      height: 29px;\n      margin-bottom: -.25rem;\n      margin-left: .95rem;\n      margin-top: -.25rem;\n    }\n\n    .brand-image-xs {\n      margin-top: -.2rem;\n      max-height: 29px;\n    }\n\n    .brand-image-xl {\n      margin-top: -.225rem;\n      max-height: 38px;\n    }\n  }\n}\n", "//\n// Component: Main Sidebar\n//\n\n.main-sidebar {\n  height: 100vh;\n  overflow-y: hidden;\n  z-index: $zindex-main-sidebar;\n\n  // Remove Firefox Focusring\n  a {\n    &:-moz-focusring {\n      border: 0;\n      outline: none;\n    }\n  }\n\n}\n\n.sidebar {\n  height: calc(100% - (#{$main-header-height-inner} + #{$main-header-bottom-border-width}));\n  overflow-y: auto;\n  padding-bottom: $sidebar-padding-y;\n  padding-left: $sidebar-padding-x;\n  padding-right: $sidebar-padding-x;\n  padding-top: $sidebar-padding-y;\n}\n\n// Sidebar user panel\n.user-panel {\n  position: relative;\n\n  [class*='sidebar-dark'] & {\n    border-bottom: 1px solid lighten($dark, 12%);\n  }\n\n  [class*='sidebar-light'] & {\n    border-bottom: 1px solid $gray-300;\n  }\n\n  &,\n  .info {\n    overflow: hidden;\n    white-space: nowrap;\n  }\n\n  .image {\n    display: inline-block;\n    padding-left: $nav-link-padding-x - .2;\n  }\n\n  img {\n    height: auto;\n    width: $sidebar-user-image-width;\n  }\n\n  .info {\n    display: inline-block;\n    padding: 5px 5px 5px 10px;\n  }\n\n  .status,\n  .dropdown-menu {\n    font-size: $font-size-sm;\n  }\n}\n\n// Sidebar navigation menu\n.nav-sidebar {\n  // All levels\n  .nav-item {\n    > .nav-link {\n      margin-bottom: .2rem;\n\n      .right {\n        @include transition(transform $transition-fn $transition-speed);\n      }\n    }\n  }\n\n  .nav-link > .right,\n  .nav-link > p > .right {\n    position: absolute;\n    right: 1rem;\n    top: .7rem;\n\n    i,\n    span {\n      margin-left: .5rem;\n    }\n\n    &:nth-child(2) {\n      right: 2.2rem;\n    }\n  }\n\n  .menu-open {\n    > .nav-treeview {\n      display: block;\n    }\n\n    > .nav-link {\n      i.right {\n        @include rotate(-90deg);\n      }\n    }\n  }\n\n  // First Level\n  > .nav-item {\n    margin-bottom: 0;\n\n    .nav-icon {\n      margin-left: .05rem;\n      font-size: 1.2rem;\n      margin-right: .2rem;\n      text-align: center;\n      width: $sidebar-nav-icon-width;\n\n      &.fa,\n      &.fas,\n      &.far,\n      &.fab,\n      &.glyphicon,\n      &.ion {\n        font-size: 1.1rem;\n      }\n    }\n\n    .float-right {\n      margin-top: 3px;\n    }\n  }\n\n  // Tree view menu\n  .nav-treeview {\n    display: none;\n    list-style: none;\n    padding: 0;\n\n    > .nav-item {\n      > .nav-link {\n        > .nav-icon {\n          width: $sidebar-nav-icon-width;\n        }\n      }\n    }\n  }\n\n  &.nav-child-indent {\n    .nav-treeview {\n      transition: padding $transition-speed $transition-fn;\n      padding-left: 1rem;\n\n      .text-sm & {\n        padding-left: .5rem;\n      }\n    }\n\n    &.nav-legacy {\n      .nav-treeview {      \n        .nav-treeview {\n          padding-left: 2rem;\n          margin-left: -1rem;\n\n          .text-sm & {          \n            padding-left: 1rem;\n            margin-left: -.5rem;\n          }\n        }\n      }\n    }\n  }\n\n  .nav-header {\n    font-size: .9rem;\n    padding: $nav-link-padding-y;\n\n    &:not(:first-of-type) {\n      padding: 1.7rem 1rem .5rem;\n    }\n  }\n\n  .nav-link p {\n    display: inline-block;\n    animation-name: fadeIn;\n    animation-duration: $transition-speed;\n    animation-fill-mode: both;\n    margin: 0;\n  }\n}\n\n#sidebar-overlay {\n  @include media-breakpoint-down(md) {\n    .sidebar-open & {\n      display: block;\n    }\n  }\n\n  background-color: rgba($black, 0.1);\n  bottom: 0;\n  display: none;\n  left: 0;\n  position: fixed;\n  right: 0;\n  top: 0;\n  z-index: $zindex-main-sidebar - 1;\n}\n\n[class*='sidebar-light-'] {\n  // Sidebar background color\n  background-color: $sidebar-light-bg;\n\n  // User Panel (resides in the sidebar)\n  .user-panel {\n    a:hover {\n      color: $sidebar-light-hover-color;\n    }\n\n    .status {\n      background: $sidebar-light-hover-bg;\n      color: $sidebar-light-color;\n\n      &:hover,\n      &:focus,\n      &:active {\n        background: darken($sidebar-light-hover-bg, 3%);\n        color: $sidebar-light-hover-color;\n      }\n    }\n\n    .dropdown-menu {\n      @include box-shadow(0 2px 4px rgba(0, 0, 0, .4));\n      border-color: darken($sidebar-light-hover-bg, 5%);\n    }\n\n    .dropdown-item {\n      color: $body-color;\n    }\n  }\n\n  // Sidebar Menu. First level links\n  .nav-sidebar > .nav-item {\n    // links\n    > .nav-link {\n      // border-left: 3px solid transparent;\n      &:active,\n      &:focus {\n        color: $sidebar-light-color;\n      }\n    }\n\n    // Hover and active states\n    &.menu-open > .nav-link,\n    &:hover > .nav-link {\n      background-color: $sidebar-light-hover-bg;\n      color: $sidebar-light-hover-color;\n    }\n\n    > .nav-link.active {\n      color: $sidebar-light-active-color;\n\n      @if $enable-shadows {\n        box-shadow: map-get($elevations, 1);\n      }\n    }\n\n    // First Level Submenu\n    > .nav-treeview {\n      background: $sidebar-light-submenu-bg;\n    }\n  }\n\n  // Section Heading\n  .nav-header {\n    background: inherit;\n    color: darken($sidebar-light-color, 5%);\n  }\n\n  // All links within the sidebar menu\n  .sidebar {\n    a {\n      color: $sidebar-light-color;\n\n      &:hover {\n        text-decoration: none;\n      }\n    }\n  }\n\n  // All submenus\n  .nav-treeview {\n    > .nav-item {\n      > .nav-link {\n        color: $sidebar-light-submenu-color;\n      }\n\n      > .nav-link.active {\n        &,\n        &:hover {\n          background-color: $sidebar-light-submenu-active-bg;\n          color: $sidebar-light-submenu-active-color;\n        }\n      }\n\n      > .nav-link:hover {\n        background-color: $sidebar-light-submenu-hover-bg;\n      }\n    }\n  }\n\n  // Flat style\n  .nav-flat {\n    .nav-item {\n      .nav-treeview {\n        .nav-treeview {\n          border-color: $sidebar-light-submenu-active-bg;\n        }\n\n        > .nav-item {\n          > .nav-link {\n            &,\n            &.active {\n              border-color: $sidebar-light-submenu-active-bg;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n\n[class*='sidebar-dark-'] {\n  // Sidebar background color\n  background-color: $sidebar-dark-bg;\n\n  // User Panel (resides in the sidebar)\n  .user-panel {\n    a:hover {\n      color: $sidebar-dark-hover-color;\n    }\n\n    .status {\n      background: $sidebar-dark-hover-bg;\n      color: $sidebar-dark-color;\n\n      &:hover,\n      &:focus,\n      &:active {\n        background: darken($sidebar-dark-hover-bg, 3%);\n        color: $sidebar-dark-hover-color;\n      }\n    }\n\n    .dropdown-menu {\n      @include box-shadow(0 2px 4px rgba(0, 0, 0, .4));\n      border-color: darken($sidebar-dark-hover-bg, 5%);\n    }\n\n    .dropdown-item {\n      color: $body-color;\n    }\n  }\n\n  // Sidebar Menu. First level links\n  .nav-sidebar > .nav-item {\n    // links\n    > .nav-link {\n      // border-left: 3px solid transparent;\n      &:active {\n        color: $sidebar-dark-color;\n      }\n    }\n\n    // Hover and active states\n    &.menu-open > .nav-link,\n    &:hover > .nav-link,\n    & > .nav-link:focus  {\n      background-color: $sidebar-dark-hover-bg;\n      color: $sidebar-dark-hover-color;\n    }\n\n    > .nav-link.active {\n      color: $sidebar-dark-hover-color;\n\n      @if $enable-shadows {\n        box-shadow: map-get($elevations, 1);\n      }\n    }\n\n    // First Level Submenu\n    > .nav-treeview {\n      background: $sidebar-dark-submenu-bg;\n    }\n  }\n\n  // Section Heading\n  .nav-header {\n    background: inherit; //darken($sidebar-dark-bg, 3%);\n    color: lighten($sidebar-dark-color, 5%);\n  }\n\n  // All links within the sidebar menu\n  .sidebar {\n    a {\n      color: $sidebar-dark-color;\n\n      &:hover,\n      &:focus {\n        text-decoration: none;\n      }\n    }\n  }\n\n  // All submenus\n  .nav-treeview {\n    > .nav-item {\n      > .nav-link {\n        color: $sidebar-dark-submenu-color;\n\n        &:hover,\n        &:focus {\n          background-color: $sidebar-dark-submenu-hover-bg;\n          color: $sidebar-dark-submenu-hover-color;\n        }\n      }\n\n      > .nav-link.active {\n        &,\n        &:hover,\n        &:focus {\n          background-color: $sidebar-dark-submenu-active-bg;\n          color: $sidebar-dark-submenu-active-color;\n        }\n      }\n    }\n  }\n\n  // Flat Style\n  .nav-flat {\n    .nav-item {\n      .nav-treeview {\n        .nav-treeview {\n          border-color: $sidebar-dark-submenu-active-bg;\n        }\n\n        > .nav-item {\n          > .nav-link {\n            &,\n            &.active {\n              border-color: $sidebar-dark-submenu-active-bg;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n\n// Sidebar variants\n@each $name, $color in $theme-colors {\n  .sidebar-dark-#{$name},\n  .sidebar-light-#{$name} {\n    @include sidebar-color($color)\n  }\n}\n\n@each $name, $color in $colors {\n  .sidebar-dark-#{$name},\n  .sidebar-light-#{$name} {\n    @include sidebar-color($color)\n  }\n}\n\n.sidebar-mini .main-sidebar:not(.sidebar-no-expand),\n.sidebar-mini-md .main-sidebar:not(.sidebar-no-expand),\n.sidebar-mini .main-sidebar:not(.sidebar-no-expand):hover,\n.sidebar-mini-md .main-sidebar:not(.sidebar-no-expand):hover,\n.sidebar-mini .main-sidebar.sidebar-focused,\n.sidebar-mini-md .main-sidebar.sidebar-focused {\n .nav-compact.nav-sidebar.nav-child-indent:not(.nav-flat) .nav-treeview {\n    padding-left: 1rem;\n    margin-left: -.5rem;\n  }\n}\n\n// Nav Flat\n.nav-flat {\n  margin: (-$sidebar-padding-x/2) (-$sidebar-padding-x) 0;\n\n  .nav-item {\n    > .nav-link {\n      border-radius: 0;\n      margin-bottom: 0;\n\n      > .nav-icon {\n        margin-left: .55rem;\n      }\n    }\n  }\n\n  &:not(.nav-child-indent) {\n    .nav-treeview {  \n      .nav-item {\n        > .nav-link {\n          > .nav-icon {\n            margin-left: .4rem;\n          }\n        }\n      }\n    }\n  }\n\n  &.nav-child-indent {\n    .nav-treeview {\n      padding-left: 0;\n\n      .nav-icon {\n        margin-left: .85rem;\n      }\n\n      .nav-treeview {\n        border-left: .2rem solid;\n\n        .nav-icon {\n          margin-left: 1.15rem;\n        }\n\n        .nav-treeview {\n          .nav-icon {\n            margin-left: 1.45rem;\n          }\n\n          .nav-treeview {\n            .nav-icon {\n              margin-left: 1.75rem;\n            }\n\n            .nav-treeview {\n              .nav-icon {\n                margin-left: 2.05rem;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .sidebar-collapse &.nav-child-indent {\n    .nav-treeview {\n      .nav-icon {\n        margin-left: .55rem;\n      }\n\n      .nav-link {\n        padding-left: calc(#{$nav-link-padding-x} - .2rem);\n      }\n\n      .nav-treeview {\n        .nav-icon {\n          margin-left: .35rem;\n        }\n\n        .nav-treeview {\n          .nav-icon {\n            margin-left: .15rem;\n          }\n\n          .nav-treeview {\n            .nav-icon {\n              margin-left: -.15rem;\n            }\n\n            .nav-treeview {\n              .nav-icon {\n                margin-left: -.35rem;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .sidebar-mini .main-sidebar:not(.sidebar-no-expand):hover &,\n  .sidebar-mini-md .main-sidebar:not(.sidebar-no-expand):hover &,\n  .sidebar-mini .main-sidebar.sidebar-focused &,\n  .sidebar-mini-md .main-sidebar.sidebar-focused & {\n    &.nav-compact.nav-sidebar .nav-treeview {\n      .nav-icon {\n        margin-left: .4rem;\n      }\n    }\n\n    &.nav-sidebar.nav-child-indent .nav-treeview {\n      .nav-icon {\n        margin-left: .85rem;\n      }\n\n      .nav-treeview {\n        .nav-icon {\n          margin-left: 1.15rem;\n        }\n\n        .nav-treeview {\n          .nav-icon {\n            margin-left: 1.45rem;\n          }\n\n          .nav-treeview {\n            .nav-icon {\n              margin-left: 1.75rem;\n            }\n\n            .nav-treeview {\n              .nav-icon {\n                margin-left: 2.05rem;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .nav-icon {\n    @include transition(margin-left $transition-fn $transition-speed);\n  }\n\n  .nav-treeview {\n    .nav-icon {\n      margin-left: -.2rem;\n    }\n  }\n\n  &.nav-sidebar > .nav-item {\n    .nav-treeview,\n    > .nav-treeview {\n      background: rgba($white, .05);\n\n      .nav-item {\n        > .nav-link {\n          border-left: .2rem solid;\n        }\n      }\n    }\n  }\n}\n\n.nav-legacy {\n  margin: (-$sidebar-padding-x/2) (-$sidebar-padding-x) 0;\n\n  &.nav-sidebar .nav-item {\n    > .nav-link {\n      border-radius: 0;\n      margin-bottom: 0;\n\n      > .nav-icon {\n        margin-left: .55rem;\n\n        .text-sm & {\n          margin-left: .75rem;\n        }\n      }\n    }\n  }\n\n  &.nav-sidebar > .nav-item {\n    > .nav-link {\n      &.active {\n        background: inherit;\n        border-left: 3px solid transparent;\n        box-shadow: none;\n\n        > .nav-icon {\n          margin-left: calc(.55rem - 3px);\n          \n          .text-sm & {\n            margin-left: calc(.75rem - 3px);\n          }\n        }\n      }\n    }\n  }\n\n  .text-sm &.nav-sidebar.nav-flat .nav-treeview {\n    .nav-item {\n      > .nav-link {\n        > .nav-icon {\n          margin-left: calc(.75rem - 3px);\n        }\n      }\n    }\n  }\n\n  .sidebar-mini &,\n  .sidebar-mini-md & {\n    > .nav-item .nav-link {\n      .nav-icon {\n        @include transition(margin-left $transition-fn $transition-speed);\n        margin-left: .75rem;\n      }\n    }\n  }\n\n  .sidebar-mini.sidebar-collapse .main-sidebar.sidebar-focused &.nav-child-indent,\n  .sidebar-mini.sidebar-collapse .main-sidebar:hover &.nav-child-indent,\n  .sidebar-mini-md.sidebar-collapse .main-sidebar.sidebar-focused &.nav-child-indent,\n  .sidebar-mini-md.sidebar-collapse .main-sidebar:hover &.nav-child-indent {\n    .nav-treeview {\n      padding-left: 1rem;\n\n      .nav-treeview {\n        padding-left: 2rem;\n        margin-left: -1rem;\n      }\n    }\n  }\n\n  .sidebar-mini.sidebar-collapse.text-sm .main-sidebar.sidebar-focused &.nav-child-indent,\n  .sidebar-mini.sidebar-collapse.text-sm .main-sidebar:hover &.nav-child-indent,\n  .sidebar-mini-md.sidebar-collapse.text-sm .main-sidebar.sidebar-focused &.nav-child-indent,\n  .sidebar-mini-md.sidebar-collapse.text-sm .main-sidebar:hover &.nav-child-indent {\n    .nav-treeview {\n      padding-left: .5rem;\n\n      .nav-treeview {\n        padding-left: 1rem;\n        margin-left: -.5rem;\n      }\n    }\n  }\n\n  .sidebar-mini.sidebar-collapse &,\n  .sidebar-mini-md.sidebar-collapse & {\n    > .nav-item > .nav-link {\n      .nav-icon {\n        margin-left: .55rem;\n      }\n\n      &.active {\n        > .nav-icon{\n          margin-left: .36rem;\n        }\n      }\n    }\n\n    &.nav-child-indent {   \n      .nav-treeview {\n        .nav-treeview {\n          padding-left: 0;\n          margin-left: 0;\n        }\n      }\n      }\n    }\n\n\n  .sidebar-mini.sidebar-collapse.text-sm &,\n  .sidebar-mini-md.sidebar-collapse.text-sm & {\n    > .nav-item > .nav-link {\n      .nav-icon {\n        margin-left: .75rem;\n      }\n\n      &.active {\n        > .nav-icon{\n          margin-left: calc(.75rem - 3px);\n        }\n      }\n    }\n  }\n\n  [class*='sidebar-dark'] & {\n    &.nav-sidebar > .nav-item {\n      .nav-treeview,\n      > .nav-treeview {\n        background: rgba($white, .05);\n      }\n\n      > .nav-link.active {\n        color: $sidebar-dark-active-color;\n      }\n    }\n\n    .nav-treeview > .nav-item > .nav-link {\n      &.active,\n      &:focus,\n      &:hover {\n        background: none;\n        color: $sidebar-dark-active-color;\n      }\n    }\n  }\n\n  [class*='sidebar-light'] & {\n    &.nav-sidebar > .nav-item {\n      .nav-treeview,\n      > .nav-treeview {\n        background: rgba($black, .05);\n      }\n\n      > .nav-link.active {\n        color: $sidebar-light-active-color;\n      }\n    }\n\n    .nav-treeview > .nav-item > .nav-link {\n      &.active,\n      &:focus,\n      &:hover {\n        background: none;\n        color: $sidebar-light-active-color;\n      }\n    }\n  }\n}\n\n\n.nav-collapse-hide-child {\n  .menu-open > .nav-treeview {\n    max-height: min-content;\n    animation-name: fadeIn;\n    animation-duration: $transition-speed;\n    animation-fill-mode: both;\n  }\n\n  .sidebar-collapse & {\n    .menu-open > .nav-treeview {\n      max-height: 0;\n      animation-name: fadeOut;\n      animation-duration: $transition-speed;\n      animation-fill-mode: both;\n    }\n  }\n\n  .sidebar-mini.sidebar-collapse .main-sidebar.sidebar-focused &,\n  .sidebar-mini.sidebar-collapse .main-sidebar:hover &,\n  .sidebar-mini-md.sidebar-collapse .main-sidebar.sidebar-focused &,\n  .sidebar-mini-md.sidebar-collapse .main-sidebar:hover & {\n    .menu-open > .nav-treeview {\n      max-height: min-content;\n      animation-name: fadeIn;\n      animation-duration: $transition-speed;\n      animation-fill-mode: both;\n    }\n  }\n}\n\n// Nav Compact\n.nav-compact {\n  .nav-link,\n  .nav-header {\n    padding-top: ($nav-link-padding-y / 2);\n    padding-bottom: ($nav-link-padding-y / 2);\n  }\n\n  .nav-header:not(:first-of-type) {\n    padding-top: ($nav-link-padding-y * 1.5);\n    padding-bottom: ($nav-link-padding-y / 2);\n  }\n\n  .nav-link > .right,\n  .nav-link > p > .right {\n    top: .465rem;\n  }\n\n  .text-sm & {\n    .nav-link > .right,\n    .nav-link > p > .right {\n      top: .7rem;\n    }\n  }\n}\n\n// Sidebar Form Control\n[class*='sidebar-dark'] {\n  .form-control-sidebar,\n  .btn-sidebar {\n    background: lighten($sidebar-dark-bg, 5%);\n    border: 1px solid lighten($sidebar-dark-bg, 15%);\n    color: lighten(color-yiq(lighten($sidebar-dark-bg, 5%)), 15%);\n  }\n\n  .form-control-sidebar:focus,\n  .btn-sidebar:focus {\n    border: 1px solid lighten($sidebar-dark-bg, 30%);\n  }\n\n  .btn-sidebar:hover {\n    background: lighten($sidebar-dark-bg, 7.5%);\n  }\n\n  .btn-sidebar:focus {\n    background: lighten($sidebar-dark-bg, 10%);\n  }\n}\n\n[class*='sidebar-light'] {\n  .form-control-sidebar,\n  .btn-sidebar {\n    background: darken($sidebar-light-bg, 5%);\n    border: 1px solid darken($sidebar-light-bg, 15%);\n    color: color-yiq(darken($sidebar-light-bg, 5%));\n  }\n\n  .form-control-sidebar:focus,\n  .btn-sidebar:focus {\n    border: 1px solid darken($sidebar-light-bg, 30%);\n  }\n\n  .btn-sidebar:hover {\n    background: darken($sidebar-light-bg, 7.5%);\n  }\n\n  .btn-sidebar:focus {\n    background: darken($sidebar-light-bg, 10%);\n  }\n}\n\n// Sidebar inline input-group fix\n.sidebar .form-inline .input-group {\n  width: 100%;\n}\n.sidebar nav .form-inline {\n  margin-bottom: .2rem;\n}\n\n// Sidebar Collapse on Layout Boxed\n\n.layout-boxed {\n  &.sidebar-collapse .main-sidebar {\n    margin-left: 0;\n  }\n  .content-wrapper, \n  .main-header,\n  .main-footer {\n    z-index: 9999;\n    position: relative;\n  }\n}\n", "//\n// Mixins: Miscellaneous\n//\n\n// ETC\n@mixin translate($x, $y) {\n  transform: translate($x, $y);\n}\n\n// Different radius each side\n@mixin border-radius-sides($top-left, $top-right, $bottom-left, $bottom-right) {\n  border-radius: $top-left $top-right $bottom-left $bottom-right;\n}\n\n@mixin calc($property, $expression) {\n  #{$property}: calc(#{$expression});\n}\n\n@mixin rotate($value) {\n  transform: rotate($value);\n}\n\n@mixin animation($animation) {\n  animation: $animation;\n}\n\n// Gradient background\n@mixin gradient($color: #F5F5F5, $start: #EEE, $stop: #FFF) {\n  background: $color;\n  background: -webkit-gradient(linear, left bottom, left top, color-stop(0, $start), color-stop(1, $stop));\n  background: -ms-linear-gradient(bottom, $start, $stop);\n  background: -moz-linear-gradient(center bottom, $start 0%, $stop 100%);\n  background: -o-linear-gradient($stop, $start);\n}\n\n", "//\r\n// Mixins: Sidebar\r\n//\r\n\r\n// Sidebar Color\r\n@mixin sidebar-color($color) {\r\n  .nav-sidebar > .nav-item {\r\n    & > .nav-link.active {\r\n      background-color: $color;\r\n      color: color-yiq($color);\r\n    }\r\n  }\r\n\r\n  .nav-sidebar.nav-legacy > .nav-item {\r\n    & > .nav-link.active {\r\n      border-color: $color;\r\n    }\r\n  }\r\n}\r\n\r\n// Sidebar Mini Breakpoints\r\n@mixin sidebar-mini-breakpoint() {\r\n  // A fix for text overflow while transitioning from sidebar mini to full sidebar\r\n  .nav-sidebar,\r\n  .nav-sidebar > .nav-header,\r\n  .nav-sidebar .nav-link {\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n  }\r\n\r\n  // When the sidebar is collapsed...\r\n  &.sidebar-collapse {\r\n    .d-hidden-mini {\r\n      display: none;\r\n    }\r\n\r\n    // Apply the new margins to the main content and footer\r\n    .content-wrapper,\r\n    .main-footer,\r\n    .main-header {\r\n      margin-left: $sidebar-mini-width !important;\r\n    }\r\n\r\n    // Make the sidebar headers\r\n    .nav-sidebar .nav-header {\r\n      display: none;\r\n    }\r\n\r\n    .nav-sidebar .nav-link p {\r\n      width: 0;\r\n    }\r\n\r\n    .sidebar .user-panel > .info,\r\n    .nav-sidebar .nav-link p,\r\n    .brand-text {\r\n      margin-left: -10px;\r\n      animation-name: fadeOut;\r\n      animation-duration: $transition-speed;\r\n      animation-fill-mode: both;\r\n      visibility: hidden;\r\n    }\r\n\r\n    .logo-xl {\r\n      animation-name: fadeOut;\r\n      animation-duration: $transition-speed;\r\n      animation-fill-mode: both;\r\n      visibility: hidden;\r\n    }\r\n\r\n    .logo-xs {\r\n      display: inline-block;\r\n      animation-name: fadeIn;\r\n      animation-duration: $transition-speed;\r\n      animation-fill-mode: both;\r\n      visibility: visible;\r\n    }\r\n\r\n    // Modify the sidebar to shrink instead of disappearing\r\n    .main-sidebar {\r\n      overflow-x: hidden;\r\n\r\n      &,\r\n      &::before {\r\n        // Don't go away! Just shrink\r\n        margin-left: 0;\r\n        width: $sidebar-mini-width;\r\n      }\r\n\r\n      .user-panel {\r\n        .image {\r\n          float: none;\r\n        }\r\n      }\r\n\r\n      &:hover,\r\n      &.sidebar-focused {\r\n        width: $sidebar-width;\r\n\r\n        .brand-link {\r\n          width: $sidebar-width;\r\n        }\r\n\r\n        .user-panel {\r\n          text-align: left;\r\n\r\n          .image {\r\n            float: left;\r\n          }\r\n        }\r\n\r\n        .user-panel > .info,\r\n        .nav-sidebar .nav-link p,\r\n        .brand-text,\r\n        .logo-xl {\r\n          display: inline-block;\r\n          margin-left: 0;\r\n          animation-name: fadeIn;\r\n          animation-duration: $transition-speed;\r\n          animation-fill-mode: both;\r\n          visibility: visible;\r\n        }\r\n\r\n        .logo-xs {\r\n          animation-name: fadeOut;\r\n          animation-duration: $transition-speed;\r\n          animation-fill-mode: both;\r\n          visibility: hidden;\r\n        }\r\n\r\n        .brand-image {\r\n          margin-right: .5rem;\r\n        }\r\n\r\n        // Make the sidebar links, menus, labels, badges\r\n        // and angle icons disappear\r\n        .sidebar-form,\r\n        .user-panel > .info {\r\n          display: block !important;\r\n          -webkit-transform: translateZ(0);\r\n        }\r\n\r\n        .nav-sidebar > .nav-item > .nav-link > span {\r\n          display: inline-block !important;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Make an element visible only when sidebar mini is active\r\n    .visible-sidebar-mini {\r\n      display: block !important;\r\n    }\r\n\r\n    &.layout-fixed {\r\n      .main-sidebar:hover {\r\n        .brand-link {\r\n          width: $sidebar-width;\r\n        }\r\n      }\r\n\r\n      .brand-link {\r\n        width: $sidebar-mini-width;\r\n      }\r\n    }\r\n  }\r\n}\r\n", "//\r\n// Component: Sidebar Mini\r\n//\r\n\r\n// Logo style\r\n.logo-xs,\r\n.logo-xl {\r\n  opacity: 1;\r\n  position: absolute;\r\n  visibility: visible;\r\n\r\n  &.brand-image-xs {\r\n    left: 18px;\r\n    top: 12px;\r\n  }\r\n\r\n  &.brand-image-xl {\r\n    left: 12px;\r\n    top: 6px;\r\n  }\r\n}\r\n\r\n.logo-xs {\r\n  opacity: 0;\r\n  visibility: hidden;\r\n\r\n  &.brand-image-xl {\r\n    left: 16px;\r\n    top: 8px;\r\n  }\r\n}\r\n\r\n.brand-link {\r\n  &.logo-switch {\r\n    &::before {\r\n      content: '\\00a0';\r\n    }\r\n  }\r\n}\r\n\r\n// Add sidebar-mini class to the body tag to activate this feature\r\n.sidebar-mini {\r\n  @include media-breakpoint-up(lg) {\r\n    @include sidebar-mini-breakpoint;\r\n  }\r\n}\r\n@include media-breakpoint-down(md) {\r\n  .sidebar-mini.sidebar-collapse .main-sidebar {\r\n    box-shadow: none !important;\r\n  }\r\n}\r\n\r\n.sidebar-mini-md {\r\n  @include media-breakpoint-up(md) {\r\n    @include sidebar-mini-breakpoint;\r\n  }\r\n}\r\n@include media-breakpoint-down(sm) {\r\n  .sidebar-mini-md.sidebar-collapse .main-sidebar {\r\n    box-shadow: none !important;\r\n  }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n  }\r\n\r\n  to {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes fadeOut {\r\n  from {\r\n    opacity: 1;\r\n  }\r\n\r\n  to {\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n.sidebar-collapse {\r\n  .main-sidebar.sidebar-focused,\r\n  .main-sidebar:hover {\r\n    .nav-header {\r\n      display: inline-block;\r\n    }\r\n  }\r\n\r\n  .sidebar-no-expand.main-sidebar.sidebar-focused,\r\n  .sidebar-no-expand.main-sidebar:hover {\r\n    width: $sidebar-mini-width;\r\n\r\n    .nav-header {\r\n      display: none;\r\n    }\r\n\r\n    .brand-link {\r\n      width: $sidebar-mini-width !important;\r\n    }\r\n\r\n    .user-panel .image {\r\n      float: none !important;\r\n    }\r\n\r\n    .logo-xs {\r\n      animation-name: fadeIn;\r\n      animation-duration: $transition-speed;\r\n      animation-fill-mode: both;\r\n      visibility: visible;\r\n    }\r\n\r\n    .logo-xl {\r\n      animation-name: fadeOut;\r\n      animation-duration: $transition-speed;\r\n      animation-fill-mode: both;\r\n      visibility: hidden;\r\n    }\r\n\r\n    .nav-sidebar.nav-child-indent .nav-treeview {\r\n      padding-left: 0;\r\n    }\r\n\r\n    .brand-text,\r\n    .user-panel > .info,\r\n    .nav-sidebar .nav-link p {\r\n      margin-left: -10px;\r\n      animation-name: fadeOut;\r\n      animation-duration: $transition-speed;\r\n      animation-fill-mode: both;\r\n      visibility: hidden;\r\n      width: 0;\r\n    }\r\n\r\n    .nav-sidebar > .nav-item .nav-icon {\r\n      margin-right: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.nav-sidebar {\r\n  position: relative;\r\n\r\n  &:hover {\r\n    overflow: visible;\r\n  }\r\n}\r\n\r\n.sidebar-form,\r\n.nav-sidebar > .nav-header {\r\n  overflow: hidden;\r\n  text-overflow: clip;\r\n}\r\n\r\n.nav-sidebar .nav-item > .nav-link {\r\n  position: relative;\r\n\r\n  > .float-right {\r\n    margin-top: -7px;\r\n    position: absolute;\r\n    right: 10px;\r\n    top: 50%;\r\n  }\r\n}\r\n\r\n.sidebar .nav-link p,\r\n.main-sidebar .brand-text,\r\n.main-sidebar .logo-xs,\r\n.main-sidebar .logo-xl,\r\n.sidebar .user-panel .info {\r\n  @include transition(margin-left $transition-speed linear, opacity $transition-speed ease, visibility $transition-speed ease)\r\n}\r\n", "//\n// Component: Control Sidebar\n//\n \nhtml.control-sidebar-animate {\n  overflow-x: hidden;\n}\n\n.control-sidebar {\n  bottom: $main-footer-height;\n  position: absolute;\n  top: $main-header-height;\n  z-index: $zindex-control-sidebar;\n  \n  &,\n  &::before {\n    bottom: $main-footer-height;\n    display: none;\n    right: -$control-sidebar-width;\n    width: $control-sidebar-width;\n    @include transition(right $transition-speed $transition-fn, display $transition-speed $transition-fn);\n  }\n\n  &::before {\n    content: '';\n    display: block;\n    position: fixed;\n    top: 0;\n    z-index: -1;\n  }\n}\n\nbody.text-sm {\n  .control-sidebar {\n    bottom: $main-footer-height-sm;\n    top: $main-header-height-sm;\n  }\n}\n\n.main-header.text-sm ~ .control-sidebar {\n  top: $main-header-height-sm;\n}\n\n.main-footer.text-sm ~ .control-sidebar {\n  bottom: $main-footer-height-sm;\n}\n\n.control-sidebar-push-slide {\n    .content-wrapper,\n    .main-footer {\n      @include transition(margin-right $transition-speed $transition-fn);\n    }\n  }\n\n// Control sidebar open state\n.control-sidebar-open {\n  .control-sidebar {\n    display: block;\n\n    &,\n    &::before {\n      right: 0;\n    }\n  }\n\n  &.control-sidebar-push,\n  &.control-sidebar-push-slide {\n    .content-wrapper,\n    .main-footer {\n      margin-right: $control-sidebar-width;\n    }\n  }\n}\n\n// Control sidebar slide over content state\n.control-sidebar-slide-open {\n  .control-sidebar {\n    display: block;\n\n    &,\n    &::before {\n      right: 0;\n      @include transition(right $transition-speed $transition-fn, display $transition-speed $transition-fn);\n    }\n  }\n\n  &.control-sidebar-push,\n  &.control-sidebar-push-slide {\n    .content-wrapper,\n    .main-footer {\n      margin-right: $control-sidebar-width;\n    }\n  }\n}\n\n// Dark skin\n.control-sidebar-dark {\n  &,\n  a,\n  .nav-link {\n    color: $sidebar-dark-color;\n  }\n\n  //  Background\n  & {\n    background: $sidebar-dark-bg;\n  }\n\n  a:hover {\n    color: $sidebar-dark-hover-color;\n  }\n\n  // Headers and labels\n  h1,\n  h2,\n  h3,\n  h4,\n  h5,\n  h6,\n  label {\n    color: $sidebar-dark-hover-color;\n  }\n\n  // Tabs\n  .nav-tabs {\n    background-color: $sidebar-dark-hover-bg;\n    border-bottom: 0;\n    margin-bottom: 5px;\n\n    .nav-item {\n      margin: 0;\n    }\n\n    .nav-link {\n      border-radius: 0;\n      padding: 10px 20px;\n      position: relative;\n      text-align: center;\n\n      &,\n      &:hover,\n      &:active,\n      &:focus,\n      &.active {\n        border: 0;\n      }\n\n      &:hover,\n      &:active,\n      &:focus,\n      &.active {\n        border-bottom-color: transparent;\n        border-left-color: transparent;\n        border-top-color: transparent;\n        color: $sidebar-dark-hover-color;\n      }\n\n      &.active {\n        background-color: $sidebar-dark-bg;\n      }\n    }\n  }\n\n  .tab-pane {\n    padding: 10px 15px;\n  }\n}\n\n// Light skin\n.control-sidebar-light {\n  color: lighten($sidebar-light-color, 10%);\n\n  //  Background\n  & {\n    background: $sidebar-light-bg;\n    border-left: $main-header-bottom-border;\n  }\n}\n", "//\n// Component: Dropdown\n//\n\n// General Dropdown Rules\n//.dropdown-item {\n//  &:first-of-type {\n//    @include border-top-radius($border-radius);\n//  }\n//  &:last-of-type {\n//    @include border-bottom-radius($border-radius);\n//  }\n//}\n\n.text-sm {\n  .dropdown-menu {\n    font-size: $font-size-sm !important;\n  }\n\n  .dropdown-toggle::after {\n    vertical-align: .2rem\n  }\n}\n\n.dropdown-item-title {\n  font-size: $font-size-base;\n  margin: 0;\n}\n\n.dropdown-icon {\n  &::after {\n    margin-left: 0;\n  }\n}\n\n// Dropdown Sizes\n.dropdown-menu-lg {\n  max-width: 300px;\n  min-width: 280px;\n  padding: 0;\n\n  .dropdown-divider {\n    margin: 0;\n  }\n\n  .dropdown-item {\n    padding: $dropdown-padding-y $dropdown-item-padding-x;\n  }\n\n  p {\n    margin: 0;\n    white-space: normal;\n  }\n}\n\n// Dropdown Submenu\n.dropdown-submenu {\n  position: relative;\n\n  & > a:after {\n    @include caret-right;\n    float: right;\n    margin-left: .5rem;\n    margin-top: .5rem;\n  }\n\n  & > .dropdown-menu {\n    left: 100%;\n    margin-left: 0px;\n    margin-top: 0px;\n    top: 0;\n  }\n}\n\n// Dropdown Hover\n.dropdown-hover {\n  &:hover,\n  &.nav-item.dropdown:hover,\n  .dropdown-submenu:hover,\n  &.dropdown-submenu:hover {\n    > .dropdown-menu {\n      display: block;\n    }\n  }\n}\n\n\n\n// Dropdown Sizes\n.dropdown-menu-xl {\n  max-width: 420px;\n  min-width: 360px;\n  padding: 0;\n\n  .dropdown-divider {\n    margin: 0;\n  }\n\n  .dropdown-item {\n    padding: $dropdown-padding-y $dropdown-item-padding-x;\n  }\n\n  p {\n    margin: 0;\n    white-space: normal;\n  }\n}\n\n// Dropdown header and footer\n.dropdown-footer,\n.dropdown-header {\n  display: block;\n  font-size: $font-size-sm;\n  padding: .5rem $dropdown-item-padding-x;\n  text-align: center;\n}\n\n// Add fade animation to dropdown menus by appending \n// the class .animated-dropdown-menu to the .dropdown-menu ul (or ol)\n.open:not(.dropup) > .animated-dropdown-menu {\n  @include animation(flipInX .7s both);\n  backface-visibility: visible !important;\n}\n\n@keyframes flipInX {\n  0% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    transition-timing-function: ease-in;\n    opacity: 0;\n  }\n\n  40% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    transition-timing-function: ease-in;\n  }\n\n  60% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    opacity: 1;\n  }\n\n  80% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n  }\n\n  100% {\n    transform: perspective(400px);\n  }\n}\n\n// Fix dropdown menu in navbars\n.navbar-custom-menu > .navbar-nav {\n  > li {\n    position: relative;\n    > .dropdown-menu {\n      position: absolute;\n      right: 0;\n      left: auto;\n    }\n  }\n}\n\n@include media-breakpoint-down(sm) {\n  .navbar-custom-menu > .navbar-nav {\n    float: right;\n    > li {\n      position: static;\n      > .dropdown-menu {\n        position: absolute;\n        right: 5%;\n        left: auto;\n        border: 1px solid #ddd;\n        background: $white;\n      }\n    }\n  }\n}\n\n// User Menu\n.navbar-nav > .user-menu {\n  > .nav-link:after {\n    content:none;\n  }\n\n  > .dropdown-menu {\n    @include border-top-radius(0);\n    padding: 0;\n    width: 280px;\n\n    &,\n    > .user-body {\n      @include border-bottom-radius(4px);\n    }\n\n    // Header menu\n    > li.user-header {\n      height: 175px;\n      padding: 10px;\n      text-align: center;\n\n      // User image\n      > img {\n        z-index: 5;\n        height: 90px;\n        width: 90px;\n        border: 3px solid;\n        border-color: transparent;\n        border-color: rgba(255, 255, 255, 0.2);\n      }\n\n      > p {\n        z-index: 5;\n        font-size: 17px;\n        //text-shadow: 2px 2px 3px #333333;\n        margin-top: 10px;\n\n        > small {\n          display: block;\n          font-size: 12px;\n        }\n      }\n    }\n\n    // Menu Body\n    > .user-body {\n      @include clearfix;\n      border-bottom: 1px solid $gray-700;\n      border-top: 1px solid $gray-300;\n      padding: 15px;\n\n      a {\n        @include media-breakpoint-up(sm) {\n          background: $white !important;\n          color: $gray-700 !important;\n        }\n      }\n    }\n\n    // Menu Footer\n    > .user-footer {\n      @include clearfix;\n      background-color: $gray-100;\n      padding: 10px;\n\n      .btn-default {\n        color: $gray-600;\n\n        &:hover {\n          @include media-breakpoint-up(sm) {\n            background-color: $gray-100;\n          }\n        }\n      }\n    }\n  }\n\n  .user-image {\n    @include media-breakpoint-up(sm) {\n      float: none;\n      line-height: 10px;\n      margin-right: .4rem;\n      margin-top: -8px;\n    }\n\n    border-radius: 50%;\n    float: left;\n    height: $sidebar-user-image-width;\n    margin-right: 10px;\n    margin-top: -2px;\n    width: $sidebar-user-image-width;\n  }\n}\n", "//\n// Component: Nav\n//\n\n.nav-pills {\n  .nav-link {\n    color: $gray-600;\n\n    &:not(.active):hover {\n      color: theme-color('primary');\n    }\n  }\n\n  .nav-item {\n    &.dropdown.show {\n      .nav-link:hover {\n        color: $dropdown-link-active-color;\n      }\n    }\n  }\n}\n\n// Vertical Tabs\n.nav-tabs.flex-column {\n  border-bottom: 0;\n  border-right: $nav-tabs-border-width solid $nav-tabs-border-color;\n\n  .nav-link {\n    border-bottom-left-radius: $nav-tabs-border-radius;\n    border-top-right-radius: 0;\n    margin-right: -$nav-tabs-border-width;\n\n    @include hover-focus {\n      border-color: $gray-200 transparent $gray-200 $gray-200;\n    }\n  }\n\n  .nav-link.active,\n  .nav-item.show .nav-link {\n    border-color: $gray-300 transparent $gray-300 $gray-300;\n  }\n\n  &.nav-tabs-right {\n    border-left: $nav-tabs-border-width solid $nav-tabs-border-color;\n    border-right: 0;\n\n    .nav-link {\n      border-bottom-left-radius: 0;\n      border-bottom-right-radius: $nav-tabs-border-radius;\n      border-top-left-radius: 0;\n      border-top-right-radius: $nav-tabs-border-radius;\n      margin-left: -$nav-tabs-border-width;\n\n      @include hover-focus {\n        border-color: $gray-200 $gray-200 $gray-200 transparent;\n      }\n    }\n\n    .nav-link.active,\n    .nav-item.show .nav-link {\n      border-color: $gray-300 $gray-300 $gray-300 transparent;\n    }\n  }\n}\n\n.navbar-no-expand {\n  flex-direction: row;\n\n  .nav-link {\n    padding-left: $navbar-nav-link-padding-x;\n    padding-right: $navbar-nav-link-padding-x;\n  }\n\n  .dropdown-menu {\n    position: absolute;\n  }\n}\n\n// Color variants\n@each $color, $value in $theme-colors {\n  @if $color == dark or $color == light {\n    .navbar-#{$color} {\n      background-color: $value;\n    }\n  }\n}\n\n@each $color, $value in $theme-colors {\n  @if $color != dark and $color != light {\n    .navbar-#{$color} {\n      background-color: $value;\n    }\n  }\n}\n\n@each $color, $value in $colors {\n  .navbar-#{$color} {\n    background-color: $value;\n  }\n}\n", "//\r\n// Misc: Miscellaneous\r\n//\r\n\r\n.border-transparent {\r\n  border-color: transparent !important;\r\n}\r\n\r\n// Description Blocks\r\n.description-block {\r\n  display: block;\r\n  margin: 10px 0;\r\n  text-align: center;\r\n\r\n  &.margin-bottom {\r\n    margin-bottom: 25px;\r\n  }\r\n\r\n  > .description-header {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    margin: 0;\r\n    padding: 0;\r\n  }\r\n\r\n  > .description-text {\r\n    text-transform: uppercase;\r\n  }\r\n\r\n  // Description Block Extension\r\n  .description-icon {\r\n    font-size: 16px;\r\n  }\r\n}\r\n\r\n// List utility classes\r\n.list-group-unbordered {\r\n  > .list-group-item {\r\n    border-left: 0;\r\n    border-radius: 0;\r\n    border-right: 0;\r\n    padding-left: 0;\r\n    padding-right: 0;\r\n  }\r\n}\r\n\r\n.list-header {\r\n  color: $gray-600;\r\n  font-size: 15px;\r\n  font-weight: bold;\r\n  padding: 10px 4px;\r\n}\r\n\r\n.list-seperator {\r\n  background: $card-border-color;\r\n  height: 1px;\r\n  margin: 15px 0 9px;\r\n}\r\n\r\n.list-link {\r\n  > a {\r\n    color: $gray-600;\r\n    padding: 4px;\r\n\r\n    &:hover {\r\n      color: $gray-900;\r\n    }\r\n  }\r\n}\r\n\r\n// User block\r\n.user-block {\r\n  float: left;\r\n\r\n  img {\r\n    float: left;\r\n    height: 40px;\r\n    width: 40px;\r\n  }\r\n\r\n  .username,\r\n  .description,\r\n  .comment {\r\n    display: block;\r\n    margin-left: 50px;\r\n  }\r\n\r\n  .username {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    margin-top: -1px;\r\n  }\r\n\r\n  .description {\r\n    color: $gray-600;\r\n    font-size: 13px;\r\n    margin-top: -3px;\r\n  }\r\n\r\n  &.user-block-sm {\r\n    img {\r\n      width: $img-size-sm;\r\n      height: $img-size-sm;\r\n    }\r\n\r\n    .username,\r\n    .description,\r\n    .comment {\r\n      margin-left: 40px;\r\n    }\r\n\r\n    .username {\r\n      font-size: 14px;\r\n    }\r\n  }\r\n}\r\n\r\n// Image sizes\r\n.img-sm,\r\n.img-md,\r\n.img-lg {\r\n  float: left;\r\n}\r\n\r\n.img-sm {\r\n  height: $img-size-sm;\r\n  width: $img-size-sm;\r\n\r\n  + .img-push {\r\n    margin-left: $img-size-sm + $img-size-push;\r\n  }\r\n}\r\n\r\n.img-md {\r\n  width: $img-size-md;\r\n  height: $img-size-md;\r\n\r\n  + .img-push {\r\n    margin-left: $img-size-md + $img-size-push;\r\n  }\r\n}\r\n\r\n.img-lg {\r\n  width: $img-size-lg;\r\n  height: $img-size-lg;\r\n\r\n  + .img-push {\r\n    margin-left: $img-size-lg + $img-size-push;\r\n  }\r\n}\r\n\r\n// Image bordered\r\n.img-bordered {\r\n  border: 3px solid $gray-500;\r\n  padding: 3px;\r\n}\r\n\r\n.img-bordered-sm {\r\n  border: 2px solid $gray-500;\r\n  padding: 2px;\r\n}\r\n\r\n// Rounded and Circle Images\r\n.img-rounded {\r\n  @include border-radius($border-radius)\r\n}\r\n\r\n.img-circle {\r\n  @include border-radius(50%);\r\n}\r\n\r\n// Image sizes\r\n.img-size-64,\r\n.img-size-50,\r\n.img-size-32 {\r\n  height: auto;\r\n}\r\n\r\n.img-size-64 {\r\n  width: 64px;\r\n}\r\n\r\n.img-size-50 {\r\n  width: 50px;\r\n}\r\n\r\n.img-size-32 {\r\n  width: 32px;\r\n}\r\n\r\n// Block sizes\r\n.size-32,\r\n.size-40,\r\n.size-50 {\r\n  display: block;\r\n  text-align: center;\r\n}\r\n\r\n.size-32 {\r\n  height: 32px;\r\n  line-height: 32px;\r\n  width: 32px;\r\n}\r\n\r\n.size-40 {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  width: 40px;\r\n}\r\n\r\n.size-50 {\r\n  height: 50px;\r\n  line-height: 50px;\r\n  width: 50px;\r\n}\r\n\r\n// General attachemnt block\r\n.attachment-block {\r\n  background: $gray-100;\r\n  border: 1px solid $card-border-color;\r\n  margin-bottom: 10px;\r\n  padding: 5px;\r\n\r\n  .attachment-img {\r\n    float: left;\r\n    height: auto;\r\n    max-height: 100px;\r\n    max-width: 100px;\r\n  }\r\n\r\n  .attachment-pushed {\r\n    margin-left: 110px;\r\n  }\r\n\r\n  .attachment-heading {\r\n    margin: 0;\r\n  }\r\n\r\n  .attachment-text {\r\n    color: $gray-700;\r\n  }\r\n}\r\n\r\n// Overlays for Card, InfoBox & SmallBox\r\n.card,\r\n.overlay-wrapper,\r\n.info-box,\r\n.small-box {\r\n  // Box overlay for LOADING STATE effect\r\n  > .overlay,\r\n  > .loading-img {\r\n    height: 100%;\r\n    left: 0;\r\n    position: absolute;\r\n    top: 0;\r\n    width: 100%;\r\n  }\r\n\r\n  .overlay {\r\n    @include border-radius($border-radius);\r\n    align-items: center;\r\n    background: rgba($white, 0.7);\r\n    display: flex;\r\n    justify-content: center;\r\n    z-index: 50;\r\n\r\n    > .fa,\r\n    > .fas,\r\n    > .far,\r\n    > .fab,\r\n    > .glyphicon,\r\n    > .ion {\r\n      color: $gray-800;\r\n    }\r\n\r\n    &.dark {\r\n      background: rgba($black, 0.5);\r\n\r\n      > .fa,\r\n      > .fas,\r\n      > .far,\r\n      > .fab,\r\n      > .glyphicon,\r\n      > .ion {\r\n        color: $gray-400;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.tab-pane {\r\n  // Box overlay for LOADING STATE effect on Tab Panels\r\n  > .overlay-wrapper {\r\n    position: relative;\r\n    > .overlay {\r\n      border-top-left-radius: 0;\r\n      border-top-right-radius: 0;\r\n      flex-direction: column;\r\n      margin-top: -$card-spacer-x;\r\n      margin-left: -$card-spacer-x;\r\n      height: calc(100% + 2 * #{$card-spacer-x});\r\n      width: calc(100% + 2 * #{$card-spacer-x});\r\n\r\n      &.dark {\r\n        color: $white;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// Ribbon\r\n.ribbon-wrapper {\r\n  height: $ribbon-wrapper-size;\r\n  overflow: hidden;\r\n  position: absolute;\r\n  right: -2px;\r\n  top: -2px;\r\n  width: $ribbon-wrapper-size;\r\n  z-index: 10;\r\n\r\n  &.ribbon-lg {\r\n    height: $ribbon-lg-wrapper-size;\r\n    width: $ribbon-lg-wrapper-size;\r\n\r\n    .ribbon {\r\n      right: $ribbon-lg-right;\r\n      top: $ribbon-lg-top;\r\n      width: $ribbon-lg-width;\r\n    }\r\n  }\r\n\r\n  &.ribbon-xl {\r\n    height: $ribbon-xl-wrapper-size;\r\n    width: $ribbon-xl-wrapper-size;\r\n\r\n    .ribbon {\r\n      right: $ribbon-xl-right;\r\n      top: $ribbon-xl-top;\r\n      width: $ribbon-xl-width;\r\n    }\r\n  }\r\n\r\n  .ribbon {\r\n    box-shadow: 0 0 $ribbon-border-size rgba($black, .3);\r\n    font-size: $ribbon-font-size;\r\n    line-height: $ribbon-line-height;\r\n    padding: $ribbon-padding;\r\n    position: relative;\r\n    right: $ribbon-right;\r\n    text-align: center;\r\n    text-shadow: 0 -1px 0 rgba($black, .4);\r\n    text-transform: uppercase;\r\n    top: $ribbon-top;\r\n    transform: rotate(45deg);\r\n    width: $ribbon-width;\r\n\r\n    &::before,\r\n    &::after {\r\n      border-left: $ribbon-border-size solid transparent;\r\n      border-right: $ribbon-border-size solid transparent;\r\n      border-top: $ribbon-border-size solid #9e9e9e;\r\n      bottom: -$ribbon-border-size;\r\n      content: '';\r\n      position: absolute;\r\n    }\r\n\r\n    &::before {\r\n      left: 0;\r\n    }\r\n\r\n    &::after {\r\n      right: 0;\r\n    }\r\n  }\r\n}\r\n\r\n// Scroll To Top\r\n.back-to-top {\r\n  bottom: 1.25rem;\r\n  position: fixed;\r\n  right: 1.25rem;\r\n  z-index: $zindex-control-sidebar + 1;\r\n\r\n  &:focus {\r\n    box-shadow: none;\r\n  }\r\n}\r\n\r\n// Pre\r\npre {\r\n  padding: .75rem;\r\n}\r\n\r\n// Blockquotes styles\r\nblockquote {\r\n  background: $white;\r\n  border-left: .7rem solid $primary;\r\n  margin: 1.5em .7rem;\r\n  padding: 0.5em .7rem;\r\n\r\n  .box & {\r\n    background: $gray-200;\r\n  }\r\n\r\n  p:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  h1,\r\n  h2,\r\n  h3,\r\n  h4,\r\n  h5,\r\n  h6 {\r\n    color: $primary;\r\n    font-size: 1.25rem;\r\n    font-weight: 600;\r\n  }\r\n\r\n  @each $color, $value in $theme-colors {\r\n    &.quote-#{$color} {\r\n      border-color: $value;\r\n\r\n      h1,\r\n      h2,\r\n      h3,\r\n      h4,\r\n      h5,\r\n      h6 {\r\n        color: $value;\r\n      }\r\n    }\r\n  }\r\n\r\n  @each $color, $value in $colors {\r\n    &.quote-#{$color} {\r\n      border-color: $value;\r\n\r\n      h1,\r\n      h2,\r\n      h3,\r\n      h4,\r\n      h5,\r\n      h6 {\r\n        color: $value;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Tab Custom Content\r\n\r\n.tab-custom-content {\r\n  border-top: $nav-tabs-border-width solid $nav-tabs-border-color;\r\n  margin-top: .5rem;\r\n  padding-top: .5rem;\r\n}\r\n\r\n.nav + .tab-custom-content {\r\n  border-top: none;\r\n  border-bottom: $nav-tabs-border-width solid $nav-tabs-border-color;\r\n  margin-top: 0;\r\n  margin-bottom: .5rem;\r\n  padding-bottom: .5rem;\r\n}\r\n\r\n\r\n// Badge BTN Style\r\n.badge-btn {\r\n  border-radius: $button-border-radius-xs;\r\n  font-size: $button-font-size-xs;\r\n  font-weight: 400;\r\n  padding: $button-padding-y-xs*2 $button-padding-x-xs*2;\r\n}\r\n\r\n.badge-btn.badge-pill {\r\n  padding: .375rem .6rem;\r\n}\r\n", "//\n// Misc: Print\n//\n\n@media print {\n  //Add to elements that you do not want to show when printing\n  .no-print {\n    display: none !important;\n  }\n\n  //Elements that we want to hide when printing\n  .main-sidebar,\n  .main-header,\n  .content-header {\n    @extend .no-print;\n  }\n\n  //This is the only element that should appear, so let's remove the margins\n  .content-wrapper,\n  .main-footer {\n    @include translate(0, 0);\n    margin-left: 0 !important;\n    min-height: 0 !important;\n  }\n\n  .layout-fixed .content-wrapper {\n    padding-top: 0 !important;\n  }\n\n  //Invoice printing\n  .invoice {\n    border: 0;\n    margin: 0;\n    padding: 0;\n    width: 100%;\n  }\n\n  .invoice-col {\n    float: left;\n    width: 33.3333333%;\n  }\n\n  //Make sure table content displays properly\n  .table-responsive {\n    overflow: auto;\n\n    > .table tr th,\n    > .table tr td {\n      white-space: normal !important;\n    }\n  }\n}\n", "//\n// Component: Text\n//\n\n// text modification\n.text-bold {\n  &, &.table td, &.table th {\n    font-weight: 700;\n  }\n}\n\n.text-xs {\n  font-size: $font-size-xs !important;\n}\n\n.text-sm {\n  font-size: $font-size-sm !important;\n}\n\n.text-md {\n  font-size: $font-size-base !important;\n}\n\n.text-lg {\n  font-size: $font-size-lg !important;\n}\n\n.text-xl {\n  font-size: $font-size-xl !important;\n}\n\n// text color variations\n@each $name, $color in $colors {\n  .text-#{$name} {\n    color: #{$color} !important;\n  }\n}\n", "//\n// Component: Elevation\n//\n\n.elevation-0 {\n  box-shadow: none !important;\n}\n\n// Background colors (colors)\n@each $name, $value in $elevations {\n  .elevation-#{$name} {\n    box-shadow: $value !important;\n  }\n}\n", "//\n// Mixins: Backgrounds\n//\n\n// Background Variant\n@mixin background-variant($name, $color) {\n  .bg-#{$name} {\n    background-color: #{$color} !important;\n\n    &,\n    > a {\n      color: color-yiq($color) !important;\n    }\n\n    &.btn {\n      &:hover {\n        border-color: darken($color, 10%);\n        color: darken(color-yiq($color), 7.5%);\n      }\n\n      &:not(:disabled):not(.disabled):active,\n      &:not(:disabled):not(.disabled).active,\n      &:active,\n      &.active {\n        background-color: darken($color, 10%) !important;\n        border-color: darken($color, 12.5%);\n        color: color-yiq(darken($color, 10%));\n      }\n    }\n  }\n}\n\n// Background Gradient Variant\n@mixin background-gradient-variant($name, $color) {\n  .bg-gradient-#{$name} {\n    @include bg-gradient-variant('&', $color);\n    color: color-yiq($color);\n\n    &.btn {\n      &.disabled,\n      &:disabled,\n      &:not(:disabled):not(.disabled):active,\n      &:not(:disabled):not(.disabled).active,\n      .show > &.dropdown-toggle {\n        background-image: none !important;\n      }\n\n      &:hover {\n        @include bg-gradient-variant('&', darken($color, 7.5%));\n        border-color: darken($color, 10%);\n        color: darken(color-yiq($color), 7.5%);\n      }\n\n      &:not(:disabled):not(.disabled):active,\n      &:not(:disabled):not(.disabled).active,\n      &:active,\n      &.active {\n        @include bg-gradient-variant('&', darken($color, 10%));\n        border-color: darken($color, 12.5%);\n        color: color-yiq(darken($color, 10%));\n      }\n    }\n  }\n}\n", "//\n// Misc: Colors\n//\n\n// Background colors (theme colors)\n@each $name, $color in $theme-colors {\n  @include background-variant($name, $color);\n}\n\n// Background colors (colors)\n@each $name, $color in $colors {\n  @include background-variant($name, $color);\n}\n\n.bg-gray {\n  background-color: $gray-500;\n  color: color-yiq($gray-500);\n}\n\n.bg-gray-light {\n  background-color: lighten($gray-200, 3%);\n  color: color-yiq(lighten($gray-200, 3%)) !important;\n}\n\n.bg-black {\n  background-color: $black;\n  color: color-yiq($black) !important;\n}\n\n.bg-white {\n  background-color: $white;\n  color: color-yiq($white) !important;\n}\n\n// Gradient Background colors (theme colors)\n@each $name, $color in $theme-colors {\n  @include background-gradient-variant($name, $color);\n}\n\n// Gradient Background colors (colors)\n@each $name, $color in $colors {\n  @include background-gradient-variant($name, $color);\n}\n\n// Backgrund Color Disabled\n[class^='bg-'].disabled {\n  opacity: .65;\n}\n\n// Text muted hover\na.text-muted:hover {\n  color: theme-color(primary) !important;\n}\n\n// Link Styles\n.link-muted {\n  color: darken($gray-500, 30%);\n\n  &:hover,\n  &:focus {\n    color: darken($gray-500, 40%);\n  }\n}\n\n.link-black {\n  color: $gray-600;\n\n  &:hover,\n  &:focus {\n    color: lighten($gray-500, 20%);\n  }\n}\n\n// Accent colors (theme colors)\n@each $name, $color in $theme-colors {\n  @include accent-variant($name, $color);\n}\n\n// Accent colors (colors)\n@each $name, $color in $colors {\n  @include accent-variant($name, $color);\n}\n\n// Accent button override fix\n[class*=\"accent-\"] {\n  @each $name, $color in $theme-colors {\n    a.btn-#{$name} {\n      color: color-yiq($color);\n    }\n  }\n}\n", "//\n// Mixins: Accent\n//\n\n// Accent Variant\n@mixin accent-variant($name, $color) {\n  .accent-#{$name} {\n    $link-color: $color;\n    $link-hover-color: darken($color, 15%);\n    $pagination-active-bg: $color;\n    $pagination-active-border-color: $color;\n\n    .btn-link,\n    a:not(.dropdown-item):not(.btn-app):not(.nav-link):not(.brand-link):not(.page-link):not(.btn) {\n      color: $link-color;\n\n      @include hover {\n        color: $link-hover-color;\n      }\n    }\n\n    .dropdown-item {\n      &:active,\n      &.active {\n        background: $color;\n        color: color-yiq($color);\n      }\n    }\n\n    .custom-control-input:checked ~ .custom-control-label {\n      &::before {\n        background: $color;\n        border-color: darken($color, 20%);\n      }\n\n      &::after {\n        $newColor: color-yiq($color);\n        background-image: str-replace($custom-checkbox-indicator-icon-checked, str-replace(#{$custom-control-indicator-checked-color}, '#', '%23'), str-replace(#{$newColor}, '#', '%23'));\n      }\n    }\n\n    .form-control:focus:not(.is-invalid):not(.is-warning):not(.is-valid),\n    .custom-select:focus,\n    .custom-control-input:focus:not(:checked) ~ .custom-control-label::before,\n    .custom-file-input:focus ~ .custom-file-label {\n      border-color: lighten($color, 25%);\n    }\n    \n    .page-item {\n      .page-link {\n        color: $link-color;\n      }\n\n      &.active a,\n      &.active .page-link {\n        background-color: $pagination-active-bg;\n        border-color: $pagination-active-border-color;\n        color: $pagination-active-color;\n      }\n\n      &.disabled a,\n      &.disabled .page-link {\n        background-color: $pagination-disabled-bg;\n        border-color: $pagination-disabled-border-color;\n        color: $pagination-disabled-color;\n      }\n    }\n\n    [class*=\"sidebar-dark-\"] {\n      .sidebar {\n        a:not(.dropdown-item):not(.btn-app):not(.nav-link):not(.brand-link) {\n          color: $sidebar-dark-color;\n      \n          @include hover {\n            color: $sidebar-dark-hover-color;\n          }\n        }\n      }\n    }\n\n    [class*=\"sidebar-light-\"] {\n      .sidebar {\n        a:not(.dropdown-item):not(.btn-app):not(.nav-link):not(.brand-link) {\n          color: $sidebar-light-color;\n\n          @include hover {\n            color: $sidebar-light-hover-color;\n          }\n        }\n      }\n    }\n  }\n}\n\n"]}