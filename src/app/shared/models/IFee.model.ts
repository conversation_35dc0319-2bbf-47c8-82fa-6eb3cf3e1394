import { Moment } from 'moment';
import { SearchWithPagination } from './request/base.request.model';

export interface IFee extends SearchWithPagination {
  feeId?: number;
  feeName?: string;
  configurationFeeType?: string;
  configurationFeeTypeName?: string;
  transactionFeeTypeId?: number;
  transactionFeeTypeName?: number;
  status?: number;
  feeType?: string;
  createdDate?: string;
  createdBy?: string;
  createDateTime?: Moment;
  merchantId?: number;

}
