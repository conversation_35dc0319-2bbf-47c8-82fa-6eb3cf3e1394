import { Injectable, isDevMode } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  CanActivate,
  Router,
  RouterStateSnapshot
} from '@angular/router';
import { FREE_PRIVILEGE } from '@shared/constants/authority.constants';
import { AuthenticationService } from '@shared/services/auth/authentication.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_UTILS } from '@shared/utils';

@Injectable({ providedIn: 'root' })
export class AuthGuard implements CanActivate {
  constructor(
    private authenticationService: AuthenticationService,
    private toastService: ToastrCustomService,
    private router: Router,
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    state: RouterStateSnapshot
  ): boolean | Promise<boolean> {
    const privilege = route.data['privilege'];
    // We need to call the checkLogin / and so the authenticationService.identity() function, to ensure,
    // that the client has a principal too, if they already logged in by the server.
    // This could happen on a page refresh.
    return this.authenticationService
      .identity(false, route.routeConfig?.path)
      .then((account) => {
        // this.localStorage.store(STORAGE_APP.USER_INFO, account);
        if (
          !privilege ||
          (Array.isArray(privilege) && privilege?.length === 0)
        ) {
          return false;
        } else if (
          privilege === FREE_PRIVILEGE ||
          (Array.isArray(privilege) &&
            privilege?.length > 0 &&
            privilege[0] === FREE_PRIVILEGE)
        ) {
          return true;
        } else if (account) {
          if (this.authenticationService.hasAnyPrivileges(privilege)) {
            return true;
          }
          if (isDevMode()) {
            console.error(
              'User has not any of required privilege: ',
              privilege
            );
          }
          this.toastService.error('appError.auth');
          this.router.navigate([ROUTER_UTILS.auth.login]);
          return false;
        }
        this.router.navigate([ROUTER_UTILS.auth.login]);
        return false;
      });
  }
}
