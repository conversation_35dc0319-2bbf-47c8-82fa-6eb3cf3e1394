export const ROUTER_ACTIONS = {
  create: 'create',
  update: 'update',
  detail: 'detail',
  addBanner: 'addBanner',
  view: 'view',
  verify: 'verify',
  setUpPassword: 'set-up-password',
  backup: 'backup',
  deny: 'deny',
  approval: 'approval',
};

export const ROUTER_UTILS = {
  base: {
    home: '',
    dashboard: 'dashboard',
    freeRoute: '**',
  },
  auth: {
    root: 'auth',
    login: 'login',
    register: 'register',
    changePasswordExpire: 'change-password',
    forgotPassword: 'forgot-password',
    forgotPasswordEmailSent: 'forgot-password-email-sent',
  },
  portal: {
    root: 'portal',
    verify: 'verify-user',
    userActive: {
      root: 'user-active',
      token: `:token`,
    },
    forgotPassword: {
      root: 'forgot-password',
      verify: `:token/${ROUTER_ACTIONS.verify}`,
      setUpPw: `${ROUTER_ACTIONS.setUpPassword}/:token`,
    },
  },
  user: {
    root: 'user',
    create: ROUTER_ACTIONS.create,
    update: `:userId/${ROUTER_ACTIONS.update}`,
    detail: `:userId/${ROUTER_ACTIONS.detail}`,
    overview: 'overview',
    profile: 'profile',
  },
  customer: {
    root: 'customer',
    router: {
      register: 'customer/registration/register',
    },
    registration: {
      root: 'registration',
      register: 'register',
      update: `:customerId/${ROUTER_ACTIONS.update}`,
      detail: `:customerId/${ROUTER_ACTIONS.detail}`,
    },
    approve: {
      root: 'approve',
      update: `:customerId/${ROUTER_ACTIONS.update}`,
      detail: `:customerId/${ROUTER_ACTIONS.detail}`,
      view: `:customerId/${ROUTER_ACTIONS.view}`,
    },
    accountNoTransaction: {
      root: 'account-no-transaction',
      view: `:customerId/${ROUTER_ACTIONS.view}`,
    },
    activateAccountManual: {
      root: 'activate-account-manual',
      view: `:customerId/${ROUTER_ACTIONS.view}`,
    },
  },
  loanOnline: {
    root: 'loan-online',
    detail: `:loanOnlineId/${ROUTER_ACTIONS.detail}`,
  },
  referral: {
    root: 'referral',
    detail: `:referralId/${ROUTER_ACTIONS.detail}`,
  },
  notification: {
    root: 'notifications',
    detail: `:eventId/${ROUTER_ACTIONS.detail}`,
    create: ROUTER_ACTIONS.create,
  },
  premiumAccountNumber: {
    root: 'premium-account-number',
    create: 'create',
    numberStructure: {
      root: 'structure',
      update: `:id/${ROUTER_ACTIONS.update}`,
      create: ROUTER_ACTIONS.create,
      detail: `:id/${ROUTER_ACTIONS.detail}`,
    },
    interest: {
      root: 'interest',
    },
    special: {
      root: 'special',
      update: `:id/${ROUTER_ACTIONS.update}`,
      create: ROUTER_ACTIONS.create,
      detail: `:id/${ROUTER_ACTIONS.detail}`,
    },
  },
  news: {
    root: 'news',
    detail: `:newsId/${ROUTER_ACTIONS.detail}`,
    update: `:newsId/${ROUTER_ACTIONS.update}`,
    create: ROUTER_ACTIONS.create,
  },
  fee: {
    root: 'fees',
    detail: `:feeId/${ROUTER_ACTIONS.detail}`,
    create: `:configurationFeeType/${ROUTER_ACTIONS.create}`,
    update: `:feeId/${ROUTER_ACTIONS.update}`,
  },
  bank: {
    root: 'bank',
    create: ROUTER_ACTIONS.create,
    update: `:bankId/${ROUTER_ACTIONS.update}`,
    detail: `:bankId/${ROUTER_ACTIONS.detail}`,
  },
  sysManage: {
    root: 'management',
    configTransaction: {
      root: 'config-transaction-management',
      create: 'create',
      update: `:transactionLimitId/${ROUTER_ACTIONS.update}`,
      detail: `:transactionLimitId/${ROUTER_ACTIONS.detail}`,
    },
    configSaving: {
      root: 'config-saving-management',
      create: 'create',
      update: `:savingLimitId/${ROUTER_ACTIONS.update}`,
      detail: `:savingLimitId/${ROUTER_ACTIONS.detail}`,
    },
    backgroundLogin: {
      root: 'background-login',
    },
    customerSupport: {
      root: 'customer-support',
    },
    role: {
      root: 'management-role',
      create: 'create',
      update: `:roleId/${ROUTER_ACTIONS.update}`,
      detail: `:roleId/${ROUTER_ACTIONS.detail}`,
    },
    position: {
      root: 'position',
      create: 'create',
      update: `:positionId/${ROUTER_ACTIONS.update}`,
      detail: `:positionId/${ROUTER_ACTIONS.detail}`,
    },
    campaign: {
      root: 'campaign',
      create: 'create',
      update: `:campaignId/${ROUTER_ACTIONS.update}`,
      detail: `:campaignId/${ROUTER_ACTIONS.detail}`,
      addBanner: `:campaignId/${ROUTER_ACTIONS.addBanner}`,
    },
    department: {
      root: 'department',
      create: ROUTER_ACTIONS.create,
      update: `${ROUTER_ACTIONS.update}/:departmentId`,
      detail: `${ROUTER_ACTIONS.detail}/:departmentId`,
    },
    informationTemplate: {
      root: 'information-template',
      create: ROUTER_ACTIONS.create,
      detail: `:informationTemplateId/${ROUTER_ACTIONS.detail}`,
      update: `:informationTemplateId/${ROUTER_ACTIONS.update}`,
    },
    errorCode: {
      root: 'management-error-code',
      create: 'create',
      update: `:errorCodeId/${ROUTER_ACTIONS.update}`,
      detail: `:errorCodeId/${ROUTER_ACTIONS.detail}`,
    },
    setting: 'management-setting',
  },
  merchant: {
    root: 'merchant',
    masterMerchant: {
      root: 'master-merchant',
    },
    create: 'create',
    update: `:merchantId/${ROUTER_ACTIONS.update}`,
    detail: `:merchantId/${ROUTER_ACTIONS.detail}`,
  },
  payment: {
    root: 'payment',
  },
  debitAccount: {
    root: 'debit-account',
    history: 'debit-account-history',
    report: 'debit-account-report',
  },
  transaction: {
    root: 'transaction-report',
    internationalTransaction: {
      root: 'transaction-report-international',
    },
    transactionUmoney: {
      root: 'transaction-report-Umoney',
    },
    transactionOther: {
      root: 'transaction-report-other',
    },
    transQuantity: {
      root: 'transaction-report-quantity',
    },
    transMMoney: {
      root: 'transaction-report-mmoney',
    },
    transactionNotificationLimit: {
      root: 'transaction-notification-limit',
    },
    premiumAccountRevertNotificationLimit: {
      root: 'premium-account-revert',
    },
    transactionQrPay: {
      root: 'transaction-qr-pay',
    },
  },
  managementServiceFeeFailed: {
    root: ' management-service-fee-failed',
    managementSMSBalanceFeeFailed: {
      root: 'sms-balance-fee-failed',
    },
    transactionNotificationLimit: {
      root: 'transaction-notification-limit',
    },
  },
  lapnet: {
    root: 'lapnet-report',
  },
  version: {
    root: 'management-version',
    create: 'create',
    detail: `:versionId/${ROUTER_ACTIONS.detail}`,
    update: `:versionId/${ROUTER_ACTIONS.update}`,
  },
  insurance: {
    root: 'management-insurance',
  },
  smsManage: {
    root: 'management-sms',
  },
  saving: {
    root: 'management-saving',
    detail: `:savingId/${ROUTER_ACTIONS.detail}`,
  },
  rate: {
    root: 'management-rate',
    create: 'create',
    update: `:rateId/${ROUTER_ACTIONS.update}`,
    detail: `:rateId/${ROUTER_ACTIONS.detail}`,
  },
  feeSchedule: {
    root: 'fee-schedule',
    create: 'create',
    update: `:id/${ROUTER_ACTIONS.update}`,
    detail: `:id/${ROUTER_ACTIONS.detail}`,
  },
  monitorLog: {
    root: 'monitor-log',
  },
  servicePack: {
    root: 'service-pack',
    create: 'create',
    update: `:id/${ROUTER_ACTIONS.update}`,
    detail: `:id/${ROUTER_ACTIONS.detail}`,
  },
  client: {
    root: 'client',
    create: 'create',
    update: `:id/${ROUTER_ACTIONS.update}`,
    detail: `:id/${ROUTER_ACTIONS.detail}`,
  },
  smsBalance: {
    root: 'management-sms-balance',
    detail: `:id/${ROUTER_ACTIONS.detail}`,
  },
  numberGroup: {
    root: 'number-group',
    create: 'create',
    update: `:id/${ROUTER_ACTIONS.update}`,
    detail: `:id/${ROUTER_ACTIONS.detail}`,
  },
  currency: {
    root: 'currency',
  },
  error: {
    notFound: '404',
    permissionDenied: '403',
    systemError: '500',
  },
};
