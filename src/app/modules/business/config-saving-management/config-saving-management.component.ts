import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import {
  MODAL_ACTION,
  PAGINATION,
  PERIOD_TYPE_MAP,
  SAVING_TYPE,
  SAVING_TYPE_MAP,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IConfigSaving } from '@shared/models/config-saving.model';
import { ConfigSavingService } from '@shared/services/config-saving.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import { CreateConfigSavingComponent } from './create-config-saving/create-config-saving.component';
@Component({
  selector: 'app-config-saving-management',
  templateUrl: './config-saving-management.component.html',
  styleUrls: ['./config-saving-management.component.scss'],
})
export class ConfigSavingManagementComponent implements OnInit {
  SAVING_TYPE = SAVING_TYPE;
  ROUTER_UTILS = ROUTER_UTILS;
  SYSTEM_RULES = SYSTEM_RULES;
  SAVING_TYPE_MAP = SAVING_TYPE_MAP;
  PERIOD_TYPE_MAP = PERIOD_TYPE_MAP;
  formSearch: FormGroup = new FormGroup({});
  data: IConfigSaving[] = [];
  PAGINATION = PAGINATION;
  VALIDATOR = VALIDATORS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  pageSizeOptions = PAGINATION.OPTIONS;
  constructor(
    private fb: FormBuilder,
    private modal: NgbModal,
    private modalService: ModalService,
    private toastService: ToastrCustomService,
    private configSavingService: ConfigSavingService,
    private router: Router
  ) {
    this.formSearch = this.fb.group({
      type: null,
      pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
      pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
      length: 0,
    });
  }

  ngOnInit(): void {
    this.onSearch();
  }

  onSearch() {
    const params = this.formSearch.value;
    this.configSavingService.search(params).subscribe((res: any) => {
      this.data = res.body.content;
      this.formSearch.controls.length.setValue(res.body.totalElements);
    });
  }

  openModal(data?: IConfigSaving, type?: string) {
    const modalRef = this.modal.open(CreateConfigSavingComponent, {
      size: 'lg',
      keyboard: true,
      backdrop: 'static',
      centered: true,
    });
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;
    modalRef.componentInstance.data = data;
    modalRef.componentInstance.type = type;
    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }

  openCreate(type?: string) {
    const modalRef = this.modal.open(CreateConfigSavingComponent, {
      size: 'lg',
      keyboard: true,
      backdrop: 'static',
      centered: true,
    });
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;
    modalRef.componentInstance.type = type;
    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }

  onSearchSubmit(): void {
    this.onSearch();
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formSearch.value.pageIndex,
      this.formSearch.value.pageSize
    );
  }

  onChangePage(page: PageEvent): void {
    this.formSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  onReset(): void {
    this.formSearch.controls.type.reset();
  }

  onDelete(configSavingId?: number): void {
    const modalData = {
      title: 'model.configSaving.modal.delete',
      content: 'model.configSaving.modal.deleteConfigSavingContent',
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { id: configSavingId };
        this.configSavingService.delete(params).subscribe((res: any) => {
          this.toastService.success('common.action.deleteSuccess');
          if (this.data.length === 1) {
            this.formSearch.controls.pageIndex.setValue(
              this.formSearch.controls.pageIndex.value === 0
                ? ''
                : Number(this.formSearch.controls.pageIndex.value) - 1
            );
          }
          this.onSearch();
        });
      }
    });
  }
}
