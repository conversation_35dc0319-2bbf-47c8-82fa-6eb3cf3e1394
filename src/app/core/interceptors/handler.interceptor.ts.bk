/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  <PERSON>ttp<PERSON><PERSON>,
  <PERSON>tt<PERSON><PERSON><PERSON><PERSON>,
  HttpInterceptor,
  HttpRequest,
  HttpStatusCode,
  HttpHeaders,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { STORAGE_TOKEN } from '@shared/constants/storage.constants';
import { AuthProviderService } from '@shared/services/auth/auth-provider.service';
import { CookieService } from 'ngx-cookie-service';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { filter, switchMap, take, tap } from 'rxjs/operators';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { HTTP_HEADERS } from '@shared/constants/http.constants';
import { SERVER_API_URL, TIME_CHECKING_REFRESH_TOKEN } from '@shared/constants/app.constants';
import { JwtPayload } from 'jwt-decode';
import moment from 'moment';

@Injectable()
export class HandlerInterceptor implements HttpInterceptor {
  private isTokenRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<string> =
    new BehaviorSubject<string>('');

  constructor(
    private authProviderService: AuthProviderService,
    private toastService: ToastrService,
    private cookieService: CookieService,
    private toastCustomService: ToastrCustomService
  ) {}

  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(
      tap(
        (event: HttpEvent<any>) => {
          if (
            !request ||
            !request.url ||
            (/^http/.test(request.url) &&
              !(SERVER_API_URL && request.url.startsWith(SERVER_API_URL)))
          ) {
            return event;
          }
          const jwtTokenDecode: JwtPayload | undefined =
            this.authProviderService.tokenDecode();
          if (jwtTokenDecode && jwtTokenDecode.exp) {
            const timeDeviant = jwtTokenDecode.exp - moment.utc().unix();
            if (timeDeviant <= TIME_CHECKING_REFRESH_TOKEN) {
              return this.handle401Error(request, next);
            }
          }
          return event;
        },
        (err: any) => {
          if (err.status === HttpStatusCode.Unauthorized) {
            const refreshToken = this.cookieService.get(
              STORAGE_TOKEN.ACCESS_TOKEN
            );
            if (!!refreshToken) {
              return this.handle401Error(request, next);
            } else {
              // auto logout if 401 response returned from api
              this.authProviderService.logout();
              // location.reload();
            }
          } else if (err?.error) {
            if (err.error?.fieldErrors) {
              err.error.fieldErrors?.forEach(
                (error: { message: string | undefined }) =>
                  this.toastService.error(error.message)
              );
            } else {
              if (err.error.message) {
                this.toastService.error(err.error.message);
              } else {
                this.toastCustomService.handlerError(err.error.message);
              }
            }
          }
          return throwError(() => err);
        }
      )
    );
  }

  private handle401Error(request: HttpRequest<any>, next: HttpHandler) {
    if (!this.isTokenRefreshing) {
      this.isTokenRefreshing = true;
      this.refreshTokenSubject.next('');

      return this.authProviderService.refreshToken().subscribe(res => {
          this.isTokenRefreshing = false;
          this.refreshTokenSubject.next(res.token);
          return next.handle(this.addTokenToHeader(request, res.token));
        }, err => {
          return throwError(() => err);
        }
      );
    } else {
      return this.refreshTokenSubject.pipe(
        filter(token => token != null && token !== ''),
        take(1),
        switchMap(jwt => {
          return next.handle(this.addTokenToHeader(request, jwt));
        })
      );
    }
  }

  private addTokenToHeader(request: HttpRequest<any>, token: string) {
    const headers = new HttpHeaders({
      ...request.headers,
      [HTTP_HEADERS.AUTHORIZATION]: `${HTTP_HEADERS.AUTHORIZATION_TYPE}${token}`,
    });

    return request.clone({
      headers,
      withCredentials: true,
    });

    // return request = request.clone({
    //   headers: request.headers.set(
    //     HTTP_HEADERS.AUTHORIZATION,
    //     `${HTTP_HEADERS.AUTHORIZATION_TYPE}${token}`
    //   ),
    //   withCredentials: true,
    // });
  }
}
