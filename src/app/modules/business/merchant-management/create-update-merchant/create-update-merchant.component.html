<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="backToList()" />
      <h5>
        {{
          isUpdate
            ? ("merchant.updateTitle" | translate)
            : ("merchant.createTitle" | translate)
        }}
      </h5>
    </div>
    <!-- <form [formGroup]="form" *ngIf="customer?.customerId"></form> -->
    <form
      [formGroup]="formMerchant"
      *ngIf="isUpdate ? merchant?.merchantId : !isUpdate"
    >
      <div class="col-md-12">
        <div class="row">
          <h2 class="title-create">
            {{
              (isUpdate ? "merchant.updateTitle" : "merchant.createTitle")
                | translate
            }}
          </h2>
        </div>
        <div class="row border-create">
          <h3>{{ "sidebar.merchant.infor" | translate }}</h3>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-6" *ngIf="isUpdate">
                <div class="form-group">
                  <label>{{ "model.merchant.merchantCode" | translate }}</label>
                  <input
                    trim
                    readonly="true"
                    formControlName="merchantCode"
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{
                      'model.merchant.merchantCode' | placeholder
                    }}"
                    [maxLength]="VALIDATORS.LENGTH.MERCHANT_CODE_MAX_LENGTH"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('merchantCode')?.errors?.required &&
                      formMerchant.get('merchantCode')?.touched
                    "
                  >
                    {{ "error.merchant.required.merchantCode" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('merchantCode')?.errors?.minlength &&
                      formMerchant.get('merchantCode')?.touched
                    "
                  >
                    {{
                      "error.merchant.minLength.merchantCode"
                        | translate
                          : {
                              param: VALIDATORS.LENGTH.CODE_MIN_LENGTH
                            }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('merchantCode')?.errors?.maxlength &&
                      formMerchant.get('merchantCode')?.touched
                    "
                  >
                    {{
                      "error.merchant.maxLength.merchantCode"
                        | translate
                          : {
                              param: VALIDATORS.LENGTH.MERCHANT_CODE_MAX_LENGTH
                            }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('merchantCode')?.errors?.pattern &&
                      formMerchant.get('merchantCode')?.touched
                    "
                  >
                    {{ "error.merchant.pattern.merchantCode" | translate }}
                  </small>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label
                    >{{ "model.merchant.merchantName" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    trim
                    formControlName="merchantName"
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{
                      'model.merchant.merchantName' | placeholder
                    }}"
                    [maxLength]="VALIDATORS.LENGTH.MERCHANT_NAME_MAX_LENGTH"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('merchantName')?.errors?.required &&
                      formMerchant.get('merchantName')?.touched
                    "
                  >
                    {{ "error.merchant.required.merchantName" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('merchantName')?.errors?.maxlength &&
                      formMerchant.get('merchantName')?.touched
                    "
                  >
                    {{
                      "error.merchant.maxLength.merchantName"
                        | translate
                          : {
                              param: VALIDATORS.LENGTH.MERCHANT_NAME_MAX_LENGTH
                            }
                    }}
                  </small>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label
                    >{{
                      "model.merchant.masterMerchant.merchantName" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <ng-select
                    appearance="outline"
                    [searchable]="
                      merchant?.origin
                        ? merchant?.origin !== ORIGIN_CONST.APP.code
                        : true
                    "
                    bindLabel="merchantName"
                    appDebounceKeyup
                    bindValue="merchantId"
                    [items]="masterMerchant"
                    (keyupDelay)="getMasterMerchant($event)"
                    [searchFn]="onSearchKeywordMasterMerchant"
                    [clearable]="false"
                    formControlName="parentId"
                    placeholder="{{
                      'model.merchant.masterMerchant.merchantName'
                        | placeholder : 'select'
                    }}"
                    (change)="onChangeGetMerchantAccountNumber($event)"
                  >
                    <ng-option
                      *ngFor="let item of masterMerchant; let i = index"
                      [value]="item.merchantId"
                    >
                      {{ item.merchantName }}
                    </ng-option>
                  </ng-select>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('parentId')?.errors?.required &&
                      formMerchant.get('parentId')?.touched
                    "
                  >
                    {{
                      "error.merchant.required.masterMerchantName" | translate
                    }}
                  </small>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label>{{
                    "model.merchant.merchantAccountNumber" | translate
                  }}</label>
                  <input
                    trim
                    formControlName="merchantAccountNumber"
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{
                      'model.merchant.merchantAccountNumber' | placeholder
                    }}"
                  />
                </div>
              </div>
              <!-- <div class="col-md-3">
                <div class="form-group">
                  <label>{{
                    "currency.root" | translate
                    }}</label>
                  <input trim formControlName="currency" type="text" class="w-100" class="form-control" placeholder="{{
                      'currency.root' | placeholder
                    }}" />
                </div>
              </div> -->
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-12">
                <div class="form-group">
                  <label>{{ "model.merchant.description" | translate }}</label>
                  <textarea
                    formControlName="description"
                    [maxLength]="VALIDATORS.LENGTH.MEDIUM_TEXT_MAX_LENGTH"
                    type="text"
                    class="w-100"
                    class="form-control"
                    rows="4"
                    cols="50"
                    placeholder="{{
                      'model.merchant.description' | placeholder
                    }}"
                  >
                  </textarea>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('description')?.errors?.maxlength &&
                      formMerchant.get('description')?.touched
                    "
                  >
                    {{
                      "error.merchant.maxLength.description"
                        | translate
                          : {
                              param: VALIDATORS.LENGTH.MEDIUM_TEXT_MAX_LENGTH
                            }
                    }}
                  </small>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12" *ngIf="qrCode">
            <div class="row">
              <div class="col-md-12">
                <div class="form-group">
                  <div class="qrcode-left">
                    <qrcode
                      #qrCodeData
                      [qrdata]="qrCode"
                      [width]="256"
                      [errorCorrectionLevel]="'M'"
                      (imageSrc)="getImage($event)"
                      (qrCodeURL)="onChangeURL($event)"
                      id="printQrcode"
                      class="printQrcodeClass"
                    ></qrcode>
                  </div>
                </div>
                <button
                  class="bi bi-printer btn-action bi-bi-printer btn btn-white col-lg-2 mr-2"
                  nz-button
                  nzType="default"
                  (click)="this.generatePdf(BUTTON_ACTION_CONST.PRINT)"
                  *hasPrivileges="SYSTEM_RULES.MERCHANT_PRINT_QR_CODE"
                >
                  <span> {{ "common.action.print" | translate }}</span></button
                ><button
                  class="bi bi-download btn-action btn btn-white col-lg-2"
                  nz-button
                  nzType="default"
                  (click)="this.generatePdf(BUTTON_ACTION_CONST.DOWNLOAD)"
                  *hasPrivileges="SYSTEM_RULES.MERCHANT_PRINT_QR_CODE"
                >
                  <span> {{ "common.action.download" | translate }}</span>
                </button>
              </div>
              <!-- <div class="row mt-3"></div> -->
            </div>
          </div>
        </div>
      </div>
    </form>
    <div class="d-block text-center mb-5 mt-4">
      <button
        class="btn btn-white mr-2"
        data-toggle="modal"
        (click)="backToList()"
      >
        {{ "common.action.back" | translate }}
      </button>
      <ng-container
        *hasPrivileges="
          isUpdate ? SYSTEM_RULES.MERCHANT_WRITE : SYSTEM_RULES.MERCHANT_CREATE
        "
      >
        <button
          *ngIf="!(action === ROUTER_ACTIONS.detail)"
          class="btn btn-red"
          (click)="isUpdate ? onUpdate() : onCreate()"
        >
          {{
            (isUpdate ? "common.action.update" : "common.action.create")
              | translate
          }}
        </button>
      </ng-container>
    </div>
  </div>
</section>
