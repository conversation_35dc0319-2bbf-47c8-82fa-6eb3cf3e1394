import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { IConfigAuto } from '@shared/models/config-auto-transaction.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { Observable } from 'rxjs/internal/Observable';
import { AbstractDomainService } from './common/abstract.domain.service';
import { EntityResponseType } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class ConfigAutoService extends AbstractDomainService<IConfigAuto> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.CONFIG_AUTO;

  info(param: IConfigAuto): Observable<EntityResponseType<IConfigAuto>> {
    return super.post<EntityResponseType<IConfigAuto>>(`/info`, param);
  }
}
