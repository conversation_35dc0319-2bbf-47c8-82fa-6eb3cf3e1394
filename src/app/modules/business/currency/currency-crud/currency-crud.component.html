<div class="modal-content">
  <div class="modal-header">
    <h5 class="modal-title">
      {{
      action === ROUTER_ACTIONS.detail
      ? ("currency.detailTitle" | translate)
      : action === ROUTER_ACTIONS.update
      ? ("currency.updateTitle" | translate)
      : ("currency.createTitle" | translate)
      }}
    </h5>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="activeModal.close()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="form" *ngIf="currency?.code || action === ROUTER_ACTIONS.create">
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label>{{ "currency.code" | translate}}<span class="text-danger">*</span></label>
                  <input formControlName="code" type="text" class="w-100 form-control"
                    placeholder="{{ 'currency.code' | placeholder }}" [maxLength]="VALIDATORS.LENGTH.TEXT_MAX_LENGTH"
                    appAutoValidate />
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label>{{ "currency.value" | translate
                    }}<span class="text-danger">*</span></label>
                  <input formControlName="value" type="text" class="w-100 form-control"
                    placeholder="{{ 'currency.value' | placeholder }}"
                    [maxLength]="VALIDATORS.LENGTH.CURRENCY_VALUE_MAX_LENGTH" appAutoValidate numbersOnly />
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-12">
                <div class="form-group">
                  <label>{{ "currency.name" | translate
                    }}<span class="text-danger">*</span></label>
                  <input formControlName="name" type="text" class="w-100 form-control"
                    placeholder="{{ 'currency.name' | placeholder }}"
                    [maxLength]="VALIDATORS.LENGTH.SMALL_TEXT_MAX_LENGTH" appAutoValidate />
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12" *ngIf="action === ROUTER_ACTIONS.detail">
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.role.creator" | translate }}</label>
                  <input trim type="text" disabled class="w-100" class="form-control"
                    [value]="currency?.createdBy || ''" />
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.role.fromDate" | translate }}</label>
                  <input trim type="text" [value]="currency?.createdDate || ''" disabled class="w-100"
                    class="form-control" />
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.role.updater" | translate }}</label>
                  <input trim type="text" disabled class="w-100" class="form-control"
                    [value]="currency?.lastModifiedBy || ''" />
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.role.lastModifiedDate" | translate }}</label>
                  <input trim type="text" disabled [value]="currency?.lastModifiedDate || ''" class="w-100"
                    class="form-control" />
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </form>
  </div>
  <div class="text-center pb-4">
    <button type="button" class="btn mb-btn-outline-color mr-3" (click)="activeModal.close()">
      {{ "common.action.cancel" | translate }}
    </button>
    <ng-container *hasPrivileges="SYSTEM_RULES.CURRENCY_WRITE">
      <button type="button" *ngIf="action === ROUTER_ACTIONS.detail" class="btn mb-btn-color mr-3"
        (click)="actionOnUpdate()">
        {{ "common.action.update" | translate }}
      </button>
    </ng-container>
    <ng-container *hasPrivileges="
        action === ROUTER_ACTIONS.update
          ? SYSTEM_RULES.CURRENCY_WRITE
          : SYSTEM_RULES.CURRENCY_CREATE
      ">
      <button *ngIf="
          action === ROUTER_ACTIONS.update || action === ROUTER_ACTIONS.create
        " type="button" class="btn mb-btn-color" (click)="action === ROUTER_ACTIONS.create ? onCreate() : onUpdate()"
        [disabled]="hasFilter">
        {{
        (action === ROUTER_ACTIONS.update
        ? "common.action.update"
        : "common.action.add"
        ) | translate
        }}
      </button>
    </ng-container>
  </div>
</div>