import { IDocument } from './file.model';

export interface IBank {
  bankId?: number;
  bankName?: string;
  bankCode?: string;
  branchCode?: string;
  order?: number;
  bankCodeNumber?: number;
  status?: number;
  description?: string;
  nation?: string;
  icon?: IDocument;
  iconUrL?: any;
}

export class Bank implements IBank {
  constructor(
    public bankId?: number,
    public bankName?: string,
    public bankCode?: string,
    public branchCode?: string,
    public order?: number,
    public bankCodeNumber?: number,
    public status?: number,
    public description?: string,
    public icon?: IDocument,
    public iconUrL?: any,
    public nation?: string,
  ) {
    this.bankId = bankId;
    this.bankName = bankName;
    this.bankCode = bankCode;
    this.branchCode = branchCode;
    this.order = order;
    this.bankCodeNumber = bankCodeNumber;
    this.status = status;
    this.description = description;
    this.icon = icon;
    this.iconUrL = iconUrL;
    this.nation = nation;
  }
}
