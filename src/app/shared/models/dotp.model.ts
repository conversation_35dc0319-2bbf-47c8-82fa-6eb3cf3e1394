export interface IDotp {
  dotpId?: number;
  cifNumber?: string;
  customerName?: string;
  phoneNumber?: string;
  deviceId?: string;
  registrationDate?: string;
  cancelDate?: string;
  status?: number;
  createdDate?: string;
  lastModifiedDate?: string;
  lastModifiedBy?: string;
}

export interface ISearchDotp {
  keyword?: string;
  status?: number;
  pageIndex?: number;
  pageSize?: number;
  hasPageable?: boolean;
}
