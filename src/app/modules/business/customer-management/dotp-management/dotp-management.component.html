<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h4 class="card-title">{{ "sidebar.customer.dotpManagement.root" | translate }}</h4>
        </div>
        <div class="card-body">
          <!-- Search Form -->
          <form [formGroup]="searchForm" (ngSubmit)="onSearch()">
            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label for="keyword">{{ "common.keyword" | translate }}</label>
                  <input
                    type="text"
                    id="keyword"
                    class="form-control"
                    formControlName="keyword"
                    [placeholder]="'dotp.search.keyword.placeholder' | translate"
                    trim
                  />
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="status">{{ "common.status" | translate }}</label>
                  <select id="status" class="form-control" formControlName="status">
                    <option *ngFor="let option of statusOptions" [value]="option.value">
                      {{ option.label | translate }}
                    </option>
                  </select>
                </div>
              </div>
              <div class="col-md-4 d-flex align-items-end">
                <div class="form-group w-100">
                  <div class="btn-group w-100">
                    <button type="submit" class="btn btn-primary">
                      <i class="bi bi-search"></i>
                      {{ "common.action.search" | translate }}
                    </button>
                    <button type="button" class="btn btn-secondary" (click)="onReset()">
                      <i class="bi bi-arrow-clockwise"></i>
                      {{ "common.action.reset" | translate }}
                    </button>
                    <button 
                      type="button" 
                      class="btn btn-success" 
                      (click)="onExport()"
                      *hasPrivileges="SYSTEM_RULES.CUSTOMER_EXPORT"
                    >
                      <i class="bi bi-download"></i>
                      {{ "common.action.export" | translate }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </form>

          <!-- Data Table -->
          <div class="table-responsive mt-4">
            <table class="table table-striped table-hover">
              <thead class="table-dark">
                <tr>
                  <th class="text-center" style="width: 60px;">
                    {{ "common.no" | translate }}
                  </th>
                  <th>{{ "dotp.table.cifNumber" | translate }}</th>
                  <th>{{ "dotp.table.customerName" | translate }}</th>
                  <th>{{ "dotp.table.phoneNumber" | translate }}</th>
                  <th>{{ "dotp.table.deviceCode" | translate }}</th>
                  <th class="text-center">{{ "dotp.table.registrationDate" | translate }}</th>
                  <th class="text-center">{{ "dotp.table.cancelDate" | translate }}</th>
                  <th class="text-center">{{ "common.status" | translate }}</th>
                  <th class="text-center" style="width: 100px;">{{ "common.action" | translate }}</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let item of data; let i = index">
                  <td class="text-center">{{ fillIndexItem(i) }}</td>
                  <td>{{ item.cifNumber }}</td>
                  <td>{{ item.customerName }}</td>
                  <td>{{ item.phoneNumber }}</td>
                  <td>{{ item.deviceCode }}</td>
                  <td class="text-center">
                    {{ item.registrationDate | date: 'dd/MM/yyyy HH:mm' }}
                  </td>
                  <td class="text-center">
                    {{ item.cancelDate ? (item.cancelDate | date: 'dd/MM/yyyy HH:mm') : '-' }}
                  </td>
                  <td class="text-center">
                    <span 
                      class="badge"
                      [ngClass]="getStatusClass(item.status || 0)"
                    >
                      {{ getStatusLabel(item.status || 0) | translate }}
                    </span>
                  </td>
                  <td class="text-center">
                    <button
                      *ngIf="canDelete(item)"
                      type="button"
                      class="btn btn-sm btn-danger"
                      (click)="onDelete(item)"
                      [title]="'common.action.delete' | translate"
                      *hasPrivileges="SYSTEM_RULES.CUSTOMER_DELETE"
                    >
                      <i class="bi bi-trash"></i>
                    </button>
                    <span *ngIf="!canDelete(item)" class="text-muted">-</span>
                  </td>
                </tr>
                <tr *ngIf="data.length === 0">
                  <td colspan="9" class="text-center text-muted py-4">
                    {{ "common.noData" | translate }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div class="d-flex justify-content-between align-items-center mt-3" *ngIf="data.length > 0">
            <div class="text-muted">
              {{ "common.pagination.showing" | translate }}
              {{ (searchForm.value.pageIndex * searchForm.value.pageSize) + 1 }}
              {{ "common.pagination.to" | translate }}
              {{ 
                ((searchForm.value.pageIndex + 1) * searchForm.value.pageSize) > searchForm.value.length 
                  ? searchForm.value.length 
                  : ((searchForm.value.pageIndex + 1) * searchForm.value.pageSize)
              }}
              {{ "common.pagination.of" | translate }}
              {{ searchForm.value.length }}
              {{ "common.pagination.entries" | translate }}
            </div>
            <mat-paginator
              [length]="searchForm.value.length"
              [pageSize]="searchForm.value.pageSize"
              [pageIndex]="searchForm.value.pageIndex"
              [pageSizeOptions]="PAGINATION.PAGE_SIZE_OPTIONS"
              (page)="onPageChange($event)"
              showFirstLastButtons
            ></mat-paginator>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
