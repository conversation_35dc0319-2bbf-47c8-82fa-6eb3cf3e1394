<aside class="main-sidebar sidebar-dark-primary">
  <!-- Brand Logo -->
  <div routerLink="/" class="text-center pt-4 pb-4 pointer">
    <img
      src="assets/dist/img/mblogsvg.svg"
      height="30"
      alt="MB"
      class="brand-image"
    />
  </div>
  <!-- Sidebar -->
  <div class="sidebar" style="height: 80vh">
    <nav class="mt-0">
      <ul
        class="nav nav-pills nav-sidebar flex-column"
        data-widget="treeview"
        role="menu"
        data-accordion="false"
      >
        <ng-container *ngFor="let item of SIDEBAR_CONST; let i = index">
          <li
            [ngClass]="
              isShowDropdownMenu[i]
                ? 'item-menu has-treeview menu-is-opening menu-open'
                : 'item-menu has-treeview '
            "
          >
            <ng-container *ngIf="item.root">
              <span
                class="nav-link xxx"
                [routerLinkActive]="['active']"
                *hasPrivileges="item.authorities"
                (click)="onOpenMenu(i)"
              >
                <i
                  class="item-color"
                  [class]="'nav-icon mr-2 ' + item.icon"
                ></i>
                <p class="item-color" title="{{ item.title | translate }}">
                  {{ item.title | translate }}
                  <i class="fas fa-angle-left right"></i>
                </p>
              </span>
            </ng-container>
            <ng-container *ngIf="!item.root">
              <span
                class="nav-link xxx"
                [routerLink]="[item.path]"
                [routerLinkActive]="['active']"
                *hasPrivileges="item.authorities"
              >
                <i [class]="'nav-icon mr-2 ' + item.icon"></i>
                <p title="{{ item.title | translate }}">
                  {{ item.title | translate | limitWord : 30 }}
                </p>
              </span>
            </ng-container>
            <ng-container *ngIf="item.child">
              <ul class="nav nav-treeview">
                <li class="nav-item" *ngFor="let child of item.child">
                  <span
                    [routerLink]="[child.path]"
                    [routerLinkActive]="['active']"
                    class="nav-link xxx"
                    *hasPrivileges="child.authorities"
                  >
                    <!-- <i [class]="'nav-icon mr-2'"></i> -->
                    <p title="{{ child.title | translate }}">
                      {{ child.title | translate | limitWord : 30 }}
                    </p>
                  </span>
                </li>
              </ul>
            </ng-container>
          </li>
        </ng-container>
      </ul>
    </nav>
    <!-- /.sidebar-menu -->
  </div>
  <!-- /.sidebar -->
  <div class="icon-hoa">
    <img src="assets/dist/img/SidebarMenu2.png" alt="" class="brand-image" />
  </div>
</aside>
