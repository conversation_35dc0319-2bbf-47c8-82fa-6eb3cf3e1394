import { Component, ViewChild } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { LANGUAGE_MAP } from '@shared/constants/app.constants';
import { LANGUAGES } from '@shared/constants/language.constants';
import { STORAGE_LANGUAGES } from '@shared/constants/storage.constants';
import { ROUTER_UTILS } from '@shared/utils/router.utils';
import { LocalStorageService } from 'ngx-webstorage';
import { SidebarComponent } from './sidebar/sidebar.component';

@Component({
  selector: 'app-main-layout',
  templateUrl: './main-layout.component.html',
  styleUrls: ['./main-layout.component.scss'],
})
export class MainLayoutComponent {
  @ViewChild('closeMenu') closeMenu!: SidebarComponent;

  welcomeScreen = false;

  isCollapsed = false;

  constructor(
    private router: Router,
    private localStorage: LocalStorageService
  ) {
    this.router.events.subscribe((event: any) => {
      if (event instanceof NavigationEnd) {
        if (event.urlAfterRedirects === `/${ROUTER_UTILS.base.home}`) {
          this.welcomeScreen = true;
        } else {
          this.welcomeScreen = false;
        }
      }
    });
    const currentLanguage =
      this.localStorage.retrieve(STORAGE_LANGUAGES) || LANGUAGES.VI.code;
    const style =
      "body {font-family: '" + LANGUAGE_MAP[currentLanguage].font + "'}";
    const node = document.createElement('style');
    node.innerText = style;
    document.head.appendChild(node);
  }

  collapse(value: boolean): void {
    this.isCollapsed = value;
    this.closeMenu.close(value);
  }
}
