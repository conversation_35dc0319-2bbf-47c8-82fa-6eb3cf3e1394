import {
  Component,
  EventEmitter,
  HostListener,
  OnInit,
  Output,
} from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { MODAL_ACTION } from '@shared/constants/app.constants';
import { ICustomer } from '@shared/models/customer.model';
import { MODEL_MAP_ITEM_COMMON } from '@shared/models/model.type';
import { CustomerService } from '@shared/services/customer.service';

@Component({
  selector: 'app-modal-add-item',
  templateUrl: './modal-add-item.component.html',
  styleUrls: ['./modal-add-item.component.scss'],
})
export class ModalAddItemComponent implements OnInit {
  @Output() data: EventEmitter<any> = new EventEmitter();
  @Output() dataEmit: EventEmitter<any> = new EventEmitter();

  customers: ICustomer[] = [];

  public item = '';
  public selectedValue: ICustomer[] = [];

  public title = 'common.action.confirm';
  public content = '';
  public interpolateParams: object = {};
  public isHiddenBtnClose = false;
  public action!: MODEL_MAP_ITEM_COMMON;

  MODAL_ACTION = MODAL_ACTION;

  constructor(
    public activeModal: NgbActiveModal,
    public translateService: TranslateService,
    public customerService: CustomerService
  ) {}

  ngOnInit(): void {
    this.loadListCustomer('');
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.activeModal.close();
  }

  showContent(): string {
    return this.translateService.instant(this.content, this.interpolateParams);
  }

  loadListCustomer = (event?: any) => {
    let keywordSearch = '';
    if (event?.target?.value) {
      keywordSearch = event?.target?.value;
    }

    this.customerService
      .searchAutoComplete({ keyword: keywordSearch })
      .subscribe((res: any) => {
        this.customers = res.body.content;
      });
  };

  getItem(event: ICustomer) {
    if (event) {
      this.selectedValue.push(event);
      this.dataEmit.emit(this.selectedValue);
    }
  }

  removeItem(event: any) {
    if (event) {
      this.selectedValue = this.selectedValue.filter(
        (e) => e.customerId !== event.value.customerId
      );
      this.dataEmit.emit(this.selectedValue);
    }
  }

  customSearchFn(term: string, item: any) {
    return true;
  }
}
