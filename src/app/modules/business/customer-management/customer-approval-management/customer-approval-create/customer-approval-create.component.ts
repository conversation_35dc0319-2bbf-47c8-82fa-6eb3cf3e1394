import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { MODAL_ACTION } from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import {
  APPROVAL_STATUS_CONST,
  APPROVAL_TYPE_CONST,
} from '@shared/constants/customer.constants';
import { Customer } from '@shared/models/customer.model';
import { CustomerService } from '@shared/services/customer.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_UTILS } from '@shared/utils';
import { CustomerApprovalModalComponent } from '../customer-approval-modal/customer-approval-modal.component';

@Component({
  selector: 'app-customer-approval-create',
  templateUrl: './customer-approval-create.component.html',
  styleUrls: ['./customer-approval-create.component.scss'],
})
export class CustomerApprovalCreateComponent implements OnInit {
  customerInfo: Customer = new Customer();

  APPROVAL_STATUS_CONST = APPROVAL_STATUS_CONST;

  SYSTEM_RULES = SYSTEM_RULES;

  constructor(
    private customerService: CustomerService,
    private routerActive: ActivatedRoute,
    private router: Router,
    private toastService: ToastrCustomService,
    private modalServiceOpen: NgbModal
  ) {
    this.routerActive.paramMap.subscribe((res) => {
      const idParam = res.get('customerId');
      if (idParam) {
        this.customerInfo.customerId = +idParam;
      }
    });
  }

  ngOnInit(): void {
    this.getDetail();
  }

  /**
   * get detail customer
   */
  getDetail(): void {
    if (this.customerInfo.customerId) {
      this.customerService.info(this.customerInfo).subscribe((res: any) => {
        this.customerInfo = res.body;
      });
    }
  }

  /**
   * approval create: check approval status and approval type then call api
   */
  approval(): void {
    if (
      (this.customerInfo.approvalStatus ===
        APPROVAL_STATUS_CONST.PENDING.value ||
        this.customerInfo.approvalStatus ===
          APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
      this.customerInfo.approvalType === APPROVAL_TYPE_CONST.CREATE.value
    ) {
      const modalRef = this.modalServiceOpen.open(
        CustomerApprovalModalComponent,
        {
          backdrop: 'static',
          size: 'lg',
          centered: true,
          keyboard: false,
        }
      );
      modalRef.componentInstance.action = MODAL_ACTION.CONFIRM;
      modalRef.componentInstance.title =
        'customerRegisterManagement.customerApproval.createApproval';
      modalRef.componentInstance.content =
        'customerRegisterManagement.customerApproval.createApprovalContent';
      modalRef.componentInstance.customer = this.customerInfo;
      modalRef.componentInstance.isDeny = false;
      modalRef.result.then((result) => {
        if (result === MODAL_ACTION.CONFIRM.code) {
          this.toastService.success(
            'customerRegisterManagement.customerApproval.success.createApproval'
          );
          this.router.navigate([
            ROUTER_UTILS.customer.root,
            ROUTER_UTILS.customer.approve.root,
          ]);
        }
      });
    }
  }

  /**
   * deny create: check approval status and approval type then call api
   *
   * @param customer ICustomer
   */
  deny(): void {
    if (
      (this.customerInfo.approvalStatus ===
        APPROVAL_STATUS_CONST.PENDING.value ||
        this.customerInfo.approvalStatus ===
          APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
      this.customerInfo.approvalType === APPROVAL_TYPE_CONST.CREATE.value
    ) {
      const modalRef = this.modalServiceOpen.open(
        CustomerApprovalModalComponent,
        {
          backdrop: 'static',
          size: 'lg',
          centered: true,
          keyboard: false,
        }
      );
      modalRef.componentInstance.action = MODAL_ACTION.CONFIRM;
      modalRef.componentInstance.title =
        'customerRegisterManagement.customerApproval.createDeny';
      modalRef.componentInstance.content =
        'customerRegisterManagement.customerApproval.createDenyContent';
      modalRef.componentInstance.customer = this.customerInfo;
      modalRef.componentInstance.isDeny = true;
      modalRef.result.then((result) => {
        if (result === MODAL_ACTION.CONFIRM.code) {
          this.toastService.success(
            'customerRegisterManagement.customerApproval.success.deny.createDeny'
          );
          this.router.navigate([
            ROUTER_UTILS.customer.root,
            ROUTER_UTILS.customer.approve.root,
          ]);
        }
      });
    }
  }

  /**
   * back
   */
  onBack(): void {
    window.history.back();
  }
}
