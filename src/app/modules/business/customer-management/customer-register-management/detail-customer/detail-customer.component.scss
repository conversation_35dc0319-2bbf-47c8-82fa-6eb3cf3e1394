// .col-md-12{
//     font-size: 12px;
// }
// .form-input-img {
//   border: 1px dashed #00000066;
//   border-radius: 4px;
//   height: 30px;
//   text-align: right;
//   padding-right: 12px;
//   padding-top: 5px;
// }

// ::ng-deep .mat-form-field-appearance-fill .mat-form-field-flex {
//   background-color: white;
//   padding: 0px !important;
//   border-radius: 0px !important;
//   border: 1.5px solid #cecece;
// }

// ::ng-deep .mat-form-field-underline {
//   display: none;
// }

// .mat-form-field-infix {
//   padding: 3px !important;
//   border-top: 5px solid transparent !important;
// }

// ::ng-deep .mat-form-field-infix {
//   padding: 3px !important;
//   border-top: 5px solid transparent !important;
// }

// .mat-form-field {
//   display: block;
// }

// ::ng-deep .mat-select-panel .mat-option.mat-selected:not(.mat-option-multiple),
// ::ng-deep .mat-option.mat-active {
//   background: #eb2d4b !important;
//   border-radius: 4px !important;
//   color: white;
// }
// ::ng-deep .mat-select-panel .mat-option {
//   border-bottom: 1px solid #cecece;
// }

// ::ng-deep .mat-select-arrow-wrapper {
//   padding-top: 11px;
// }

.btn-tc {
  height: 34px;
  font-size: 14px;
}

hr {
  border-top: 1px solid #cecece;
  height: 1px;
  width: 100%;
}
