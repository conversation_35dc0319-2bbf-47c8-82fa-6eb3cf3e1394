export interface IDotp {
  dotpId?: number;
  customerId?: number;
  cif?: string;
  encKey: string;
  token: string;
  fullName?: string;
  phoneNumber?: string;
  transData: string;
  deviceId?: string;
  deviceName?: string;
  signUpDate?: string;
  cancellationDate?: string;
  status?: number;
}

export interface ISearchDotp {
  keyword?: string;
  status?: number;
  pageIndex?: number;
  pageSize?: number;
}

export class Dotp implements IDotp {
  constructor(
    public dotpId?: number,
    public customerId?: number,
    public cif?: string,
    public encKey: string,
    public token: string,
    public fullName?: string,
    public phoneNumber?: string,
    public transData: string,
    public deviceId?: string,
    public deviceName?: string,
    public signUpDate?: string,
    public cancellationDate?: string,
    public status?: number
  ) {
    this.dotpId = dotpId;
    this.customerId = customerId;
    this.cif = cif;
    this.encKey = encKey;
    this.token = token;
    this.fullName = fullName;
    this.phoneNumber = phoneNumber;
    this.transData = transData;
    this.deviceId = deviceId;
    this.deviceName = deviceName;
    this.signUpDate = signUpDate;
    this.cancellationDate = cancellationDate;
    this.status = status;
  }
}
