<div class="modal-content">
  <div class="modal-header">
    <h5 class="modal-title">
      {{
        action === ROUTER_ACTIONS.deny
          ? ("model.configTransaction.modal.confirmDeny" | translate)
          : ("model.configTransaction.modal.confirmApproval" | translate)
      }}
    </h5>
    <button
      type="button"
      class="close"
      data-dismiss="modal"
      aria-label="Close"
      (click)="activeModal.close()"
    >
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <span>{{
      action === ROUTER_ACTIONS.deny
        ? ("model.configTransaction.modal.confirmReasonDeny" | translate)
        : ("model.configTransaction.modal.confirmReasonApproval" | translate)
    }}</span>
    <form [formGroup]="formReason">
      <label
        >{{ "model.configTransaction.reason" | translate
        }}<small *ngIf="action === ROUTER_ACTIONS.deny" class="text-danger"
          >*</small
        ></label
      >
      <input
        type="text"
        trim
        formControlName="reason"
        class="w-100 form-control"
        [maxlength]="VALIDATORS.LENGTH.MEDIUM_TEXT_MAX_LENGTH"
      />
      <small
        class="text-danger"
        *ngIf="
          formReason.get('reason')?.errors?.required &&
          formReason.get('reason')?.touched
        "
      >
        {{ "error.configTransaction.required.reason" | translate }}</small
      >
    </form>
  </div>
  <div class="text-center pb-4">
    <button
      type="button"
      class="btn mb-btn-outline-color mr-3"
      (click)="activeModal.close()"
    >
      {{ "common.action.close" | translate }}
    </button>
    <ng-container
      *hasPrivileges="SYSTEM_RULES.TRANSACTION_LIMIT_UPDATE_APPROVAL"
    >
      <button type="button" class="btn mb-btn-color" (click)="onConfirm()">
        {{ "common.action.confirm" | translate }}
      </button>
    </ng-container>
  </div>
</div>
