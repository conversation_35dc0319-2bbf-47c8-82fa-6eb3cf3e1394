export type MODEL_MAP_ITEM_COMMON = {
  label: string;
  code: string | number;
  style?: string;
  font?: string;
};

export type MODEL_MAP_COMMON = {
  [name: string]: MODEL_MAP_ITEM_COMMON;
};

export interface ModelDialog {
  title: string;
  content: string;
  action?: MODEL_MAP_ITEM_COMMON;
  interpolateParams?: any;
  dialogSize?: 'sm' | 'lg' | 'xl' | string;

  // Thêm các thuộc tính mới
  confirmButtonText?: string; // Text cho nút xác nhận
  cancelButtonText?: string; // Text cho nút hủy
  buttonOrder?: string; // Thứ tự nút
  confirmBtnClass?: string; // Lớp CSS cho nút xác nhận
}
