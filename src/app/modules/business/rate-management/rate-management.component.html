<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formSearch" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-md-6 col-lg-3">
            <div class="form-group">
              <label>{{ "model.event.title" | translate }}</label>
              <input
                trim
                type="text"
                placeholder="{{ 'model.event.title' | translate }}"
                formControlName="title"
                class="w-100"
                class="form-control"
              />
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label>{{ "common.status" | translate }}</label>
              <ng-select
                appearance="outline"
                [searchable]="false"
                placeholder="{{ 'common.status' | placeholder : 'select' }}"
                [clearable]="true"
                formControlName="status"
              >
                <ng-option
                  [value]="item.code"
                  *ngFor="let item of ENTITY_STATUS"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-8 col-lg-4">
            <div class="date-picker-container form-group row">
              <div class="col-md-6">
                <label
                  >{{ "common.action.fromDate" | translate
                  }}<span class="text-danger">*</span></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="fromDate"
                    formControlName="fromDate"
                    placeholder="DD/MM/YYYY"
                    [max]="maxDate | date : 'yyyy-MM-dd'"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="fromDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #fromDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formSearch.get('fromDate')?.errors?.required &&
                    formSearch.get('fromDate')?.touched
                  "
                >
                  {{ "error.required.fromDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="isErrorStartDateGreaterEndDate"
                >
                  {{ "error.toDateMustGreatherFromDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="formSearch.get('fromDate')?.errors?.invalidDate"
                >
                  {{ "error.required.inValidDate" | translate }}
                </small>
              </div>
              <div class="col-md-6">
                <label
                  >{{ "common.action.toDate" | translate
                  }}<span class="text-danger">*</span></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="toDate"
                    formControlName="toDate"
                    placeholder="DD/MM/YYYY"
                    min="{{
                      formSearch.controls['fromDate'].value
                        | date : 'yyyy-MM-dd'
                    }}"
                    [max]="MAX_DATE_CONST"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="toDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #toDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formSearch.get('toDate')?.errors?.required &&
                    formSearch.get('toDate')?.touched
                  "
                >
                  {{ "error.required.toDate" | translate }}
                </small>
              </div>
            </div>
          </div>
          <div class="col-lg-2 col-md-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
            >
              <div class="btn-reset">
                <i
                  class="bi bi-arrow-clockwise"
                  type="button"
                  (click)="onReset()"
                >
                </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" type="submit">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-3 mt-4">
          <button
            type="button"
            [routerLink]="['create']"
            [routerLinkActive]="['active']"
            class="btn btn-red"
            *hasPrivileges="SYSTEM_RULES.INTEREST_RATE_CREATE"
          >
            {{ "common.action.create" | translate }}
          </button>
        </div>
      </form>
      <div></div>
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th class="text-center">{{ "model.rate.no" | translate }}</th>
              <th class="text-center">{{ "model.rate.image" | translate }}</th>
              <th class="text-center">{{ "model.rate.title" | translate }}</th>
              <th class="text-center">
                {{ "common.createdDate" | translate }}
              </th>
              <th class="text-center">
                {{ "common.lastModifiedDate" | translate }}
              </th>
              <th class="text-center">
                {{ "model.rate.status" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of data; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td class="text-center">
                <img
                  [src]="item.iconUrL | async"
                  height="60px"
                  class="rounded"
                />
              </td>
              <td class="text-left">
                {{ item?.interestRateDetailDTO?.title }}
              </td>
              <td class="text-center">
                {{ item?.createdDate }}
              </td>
              <td class="text-center">
                {{ item?.lastModifiedDate }}
              </td>
              <td class="text-center">
                <span
                  class="badge"
                  [ngClass]="ENTITY_STATUS_MAP[item.status || 0].style"
                  >{{
                    ENTITY_STATUS_MAP[item.status || 0].label | translate
                  }}</span
                >
              </td>
              <td class="text-center">
                <ng-container>
                  <button
                    ngbTooltip="{{ 'common.action.detail' | translate }}"
                    class="btn px-1 py-0"
                    *hasPrivileges="SYSTEM_RULES.INTEREST_RATE_READ"
                    (click)="
                      onDetail(item?.interestRateId, ROUTER_ACTIONS.detail)
                    "
                  >
                    <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                  </button>
                  <button
                    ngbTooltip="{{ 'common.action.update' | translate }}"
                    class="btn px-1 py-0"
                    *hasPrivileges="SYSTEM_RULES.INTEREST_RATE_UPDATE"
                    (click)="
                      onDetail(item?.interestRateId, ROUTER_ACTIONS.update)
                    "
                  >
                    <i class="fa fa-edit mb-color" aria-hidden="true"></i>
                  </button>
                  <button
                    [ngbTooltip]="
                      (item?.status === ENTITY_STATUS_CONST.ACTIVE.code
                        ? 'common.action.lock'
                        : 'common.action.unlock'
                      ) | translate
                    "
                    *hasPrivileges="
                      item.status === 0
                        ? SYSTEM_RULES.INTEREST_RATE_LOCK
                        : SYSTEM_RULES.INTEREST_RATE_UNLOCK
                    "
                    class="btn px-1 py-0"
                    (click)="lockAndUnlock(item)"
                  >
                    <i
                      [className]="
                        item?.status === 0
                          ? 'fa fa-lock mb-color'
                          : 'fa fa-unlock mb-color'
                      "
                      aria-hidden="true"
                    ></i>
                  </button>
                  <button
                    ngbTooltip="{{ 'common.action.delete' | translate }}"
                    class="btn px-1 py-0"
                    *hasPrivileges="SYSTEM_RULES.INTEREST_RATE_DELETE"
                    (click)="onDelete(item?.interestRateId)"
                  >
                    <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                  </button>
                </ng-container>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="row d-block text-center m-0" *ngIf="data?.length === 0">
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
        <div *ngIf="data?.length" class="paginator col-md-12">
          <mat-paginator
            [length]="formSearch.value.length"
            [pageSize]="formSearch.value.pageSize"
            [pageIndex]="formSearch.value.pageIndex"
            [pageSizeOptions]="pageSizeOptions"
            (page)="onChangePage($event)"
            aria-label="Select page"
          >
          </mat-paginator>
        </div>
      </div>
    </div>
  </div>
</section>
