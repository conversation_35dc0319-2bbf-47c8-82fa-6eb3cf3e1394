{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "sourceMap": true,
    "declaration": false,
    "downlevelIteration": true,
    "experimentalDecorators": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "allowJs": true,
    "target": "es2017",
    "module": "es2020",
    "lib": ["es2020", "dom", "dom.iterable", "esnext.asynciterable"],
    "paths": {
      "@core/*": ["src/app/core/*"],
      "@shared/*": ["src/app/shared/*"],
      "@business/*": ["src/app/modules/business/*"],
      "@portal/*": ["src/app/modules/portal/*"],
      "@auth/*": ["src/app/modules/auth/*"],
      "@env/*": ["src/environments/*"],
      "@stores/*": ["src/app/stores/*"]
      // "tslib": ["./node_modules/tslib/tslib.d.ts"]
    },
    "allowSyntheticDefaultImports": true
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "fullTemplateTypeCheck": true,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  }
}
