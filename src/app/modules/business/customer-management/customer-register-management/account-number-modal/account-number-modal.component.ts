import { Component, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { MODAL_ACTION } from '@shared/constants/app.constants';
import { ACCOUNT_TYPE_MAP } from '@shared/constants/customer.constants';
import { ICustomer } from '@shared/models/customer.model';
import { IMoneyAccount } from '@shared/models/money-account.model';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-account-number-modal',
  templateUrl: './account-number-modal.component.html',
  styleUrls: ['./account-number-modal.component.scss'],
})
export class AccountNumberModalComponent implements OnInit {
  customer!: ICustomer;

  accountCustomers: IMoneyAccount[] = [];

  MODAL_ACTION = MODAL_ACTION;

  isHiddenBtnClose = false;

  ACCOUNT_TYPE_MAP = ACCOUNT_TYPE_MAP;

  constructor(public activeModal: NgbActiveModal) {}

  ngOnInit(): void {
    this.accountCustomers = this.customer.moneyAccounts || [];
  }

  /**
   * index on list
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(index);
  }
}
