<div class="modal-content">
  <!-- <div class="modal-header" [ngClass]="isHiddenBtnClose ? 'border-0' : ''">
    <h3 class="modal-title">{{title | translate}}</h3>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"
      (click)="activeModal.close(MODAL_ACTION.CLOSE.code)">
      <span aria-hidden="true">&times;</span>
    </button>
  </div> -->
  <div class="modal-header" [ngClass]="isHiddenBtnClose ? 'border-0' : ''">
    <h5 class="modal-title">{{ title | translate }}</h5>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"
      (click)="activeModal.close(MODAL_ACTION.CLOSE.code)">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <span class="preventLongTextFix" [innerHTML]="showContent() | translate"></span>
    <ul>
      <li *ngFor="let item of numberGroup.structures">
        <b>{{ item.name + ' - ' + item.numberStructure }}</b>
      </li>
    </ul>
    <span class="preventLongTextFix">{{'numberGroup.performModalContent' | translate}}</span>
  </div>
  <div class="text-center pb-4">
    <button type="button" class="btn mb-btn-outline-color mr-3" (click)="activeModal.close(MODAL_ACTION.CANCEL.code)">
      {{ MODAL_ACTION.CANCEL.label | translate }}
    </button>
    <button type="button" class="btn mb-btn-color mr-3" [ngClass]="confirmBtnClass || 'btn-primary'"
      (click)="onDelete()">
      {{ 'common.action.perform' | translate }}
    </button>
  </div>
</div>