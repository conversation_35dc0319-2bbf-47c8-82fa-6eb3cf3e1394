import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { CONFIG_ACCOUNT_CONFIG_CONST, PREMIUM_ACCOUNT_NUMBER_CONST } from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { MODEL_MAP_ITEM_COMMON } from '@shared/models/model.type';
import { INumberGroup } from '@shared/models/number-group.model';
import { ISpecialPremiumAccountNumber } from '@shared/models/premium-account-number.modal';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { SpecialAccountService } from '@shared/services/special-account.service';
import { ROUTER_ACTIONS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-account-config',
  templateUrl: './special-account-number-create.component.html',
  styleUrls: ['./special-account-number-create.component.scss'],
})
export class SpecialAccountNumberCreateComponent implements OnInit {
  CONFIG_ACCOUNT_CONFIG_CONST = CONFIG_ACCOUNT_CONFIG_CONST;
  VALIDATORS = VALIDATORS;
  SYSTEM_RULES = SYSTEM_RULES;
  formCreate: FormGroup = new FormGroup({});
  premiumAccountNumbers: FormArray = new FormArray([]);
  isShowDelete: boolean[] = [];
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  numberGroups: INumberGroup[] = [];
  specialPremiumAccNumber?: ISpecialPremiumAccountNumber;
  actionConfirm!: MODEL_MAP_ITEM_COMMON;
  action = '';
  id: number | undefined;

  constructor(
    private fb: FormBuilder,
    public activeModal: NgbActiveModal,
    private specialAccountService: SpecialAccountService,
    private toastService: ToastrCustomService,
    private specialAccountNumberService: SpecialAccountService
  ) {}

  ngOnInit(): void {
    if (this.action === ROUTER_ACTIONS.create) {
      this.initForm();
      this.isShowDelete[PREMIUM_ACCOUNT_NUMBER_CONST.DEFAULT_LENGTH] = false;
    } else {
      this.getDetail();
    }
  }

  getDetail() {
    if (this.id) {
      this.specialAccountService.detail(this.id).subscribe((res: any) => {
        this.specialPremiumAccNumber = res.body;
        const data = res.body || undefined;
        this.initForm(data);
      });
    }
  }

  initForm(item?: ISpecialPremiumAccountNumber) {
    this.formCreate = this.fb.group({
      specialAccountNumbers: this.fb.array([this.addAccountNumberToForm(item)]),
    });
  }

  addAccountNumberToForm(item?: ISpecialPremiumAccountNumber): FormGroup {
    const listPrice = item?.price || 0;
    const discount = item?.discount || 0;

    const paymentPrice = this.calculatePaymentPriceNumber(listPrice, discount);

    return this.fb.group({
      accountNumber: [
        {
          value: item?.specialPremiumAccountNumber || '',
          disabled: this.action !== ROUTER_ACTIONS.create,
        },
        [Validators.required, Validators.pattern(VALIDATORS.PATTERN.SPECIAL_ACCOUNT_NUMBER)],
      ],
      listPrice: [
        {
          value: item?.price ? CommonUtils.moneyFormat(item?.price.toString())?.toString() : '0',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [Validators.required],
      ],
      discount: [
        {
          value: item?.discount || '0',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [Validators.required],
      ],
      hided: [
        {
          value: item?.hided || false,
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
      ],
      paymentPrice: [
        {
          value: paymentPrice ? CommonUtils.moneyFormat(paymentPrice.toString())?.toString() : '0',
          disabled: true,
        },
      ],
    });
  }

  get specialAccountNumbers() {
    return this.formCreate.get('specialAccountNumbers') as FormArray;
  }

  createRow(item?: ISpecialPremiumAccountNumber): FormGroup {
    const listPrice = item?.price || 0;
    const discount = item?.discount || 0;

    const paymentPrice = this.calculatePaymentPriceNumber(listPrice, discount);

    return this.fb.group({
      accountNumber: [
        {
          value: item?.specialPremiumAccountNumber || '',
          disabled: this.action !== ROUTER_ACTIONS.create,
        },
        [Validators.required, Validators.pattern(VALIDATORS.PATTERN.SPECIAL_ACCOUNT_NUMBER)],
      ],
      paymentPrice: [
        {
          value: paymentPrice ? CommonUtils.moneyFormat(paymentPrice.toString())?.toString() : '0',
          disabled: true,
        },
        [Validators.required],
      ],
      listPrice: [
        {
          value: item?.price ? CommonUtils.moneyFormat(item?.price.toString())?.toString() : '0',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [Validators.required],
      ],
      discount: [
        {
          value: item?.discount || '0',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [Validators.required],
      ],
      hided: [
        {
          value: item?.hided || false,
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
      ],
    });
  }

  addRow(): void {
    this.specialAccountNumbers.push(this.createRow());
    this.onShowDelete();
  }

  removeRow(index: number): void {
    if (this.specialAccountNumbers.controls.length - 1 === PREMIUM_ACCOUNT_NUMBER_CONST.MIN_INDEX) {
      this.isShowDelete[PREMIUM_ACCOUNT_NUMBER_CONST.DEFAULT_LENGTH] = false;
    }
    this.specialAccountNumbers.removeAt(index);
  }

  maxValidator(value: number): any {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return +value > VALIDATORS.LENGTH.NUMBER_MAX_AMOUNT ? { invalidMax: true } : null;
    };
  }

  calculatePaymentPrice(index: number): void {
    const item = this.specialAccountNumbers.at(index);
    this.calculatePaymentPriceByItem(item);
  }

  calculatePaymentPriceByItem(item: any): void {
    const discount = item.get('discount')?.value || 0;
    const listPrice = CommonUtils.replaceString(item.get('listPrice')?.value.toString().split('.').join('')) || 0;

    const paymentPrice = this.calculatePaymentPriceNumber(listPrice, discount);
    item.get('paymentPrice')?.setValue(CommonUtils.moneyFormat(paymentPrice + ''));

    if (listPrice && listPrice > VALIDATORS.LENGTH.NUMBER_MAX_AMOUNT) {
      item.get('listPrice')?.setValidators([this.maxValidator(listPrice)]);
      item.get('listPrice')?.updateValueAndValidity();
      item.get('paymentPrice')?.setValue('');
    } else {
      item.get('listPrice')?.setValidators([Validators.required]);
      item.get('listPrice')?.updateValueAndValidity();
      item.get('paymentPrice')?.setValue(CommonUtils.moneyFormat(paymentPrice + ''));
    }
  }

  calculatePaymentPriceNumber(listPrice: any, discount: any) {
    if (listPrice && discount >= 0) { return Math.ceil(listPrice - (listPrice * discount) / 100); }
    return listPrice;
  }

  onShowDelete() {
    this.specialAccountNumbers.controls.forEach((item, i: number) => {
      this.isShowDelete[i] = true;
    });
  }

  onCreate(): void {
    if (this.formCreate.invalid) {
      CommonUtils.markFormGroupTouched(this.formCreate);
    }

    const data: ISpecialPremiumAccountNumber[] = [];

    if (this.specialAccountNumbers.controls.length > 0) {
      this.specialAccountNumbers.controls.forEach((item) => {
        data.push({
          specialPremiumAccountNumber: item.value.accountNumber,
          price: CommonUtils.replaceString(item.value.listPrice?.toString().split('.').join('')),
          discount: item.value.discount,
          hided: item.value.hided,
        });
      });

      const request = { premiumAccountNumbers: data };
      if (this.formCreate.valid) {
        this.specialAccountNumberService.create(request).subscribe(() => {
          this.toastService.success('common.action.createSuccess');
          this.activeModal.close(this.actionConfirm.code);
        });
      }
    }
  }

  onUpdate(): void {
    let body;
    if (this.formCreate.invalid) {
      CommonUtils.markFormGroupTouched(this.formCreate);
    }

    if (this.specialAccountNumbers.controls.length > 0) {
      this.specialAccountNumbers.controls.forEach((item) => {
        body = {
          specialPremiumAccNumberId: this.id,
          discount: item.value.discount,
          price: CommonUtils.replaceString(item.value.listPrice?.toString().split('.').join('')),
          hided: item.value.hided,
        };
      });

      if (this.formCreate.valid) {
        this.specialAccountNumberService.update(body).subscribe(() => {
          this.toastService.success('common.action.updateSuccess');
          this.activeModal.close(this.actionConfirm.code);
        });
      }
    }
  }
}
