import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { IFileEntrySearch } from '@shared/models/request/file-entry.search';
import { map } from 'rxjs/operators';
import { AbstractService } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class IconService extends AbstractService {
  constructor(protected http: HttpClient, private sanitizer: DomSanitizer) {
    super(http);
  }
  public resourceUrl = '/file';

  getIconUri(
    search: IFileEntrySearch,
    loading = false,
    ignoreError = true
  ): any {
    return super
      .postFile(`${this.resourceUrl}/icon`, search, {
        loading,
        ignoreError,
      })
      .pipe(
        map((res) => {
          const objectURL = URL.createObjectURL(res.body);
          return this.sanitizer.bypassSecurityTrustUrl(objectURL);
        })
      );
  }

  getIcon(search: IFileEntrySearch, loading = false, ignoreError = true): any {
    return super.postFile(`${this.resourceUrl}/icon`, search, {
      loading,
      ignoreError,
    });
  }
}
