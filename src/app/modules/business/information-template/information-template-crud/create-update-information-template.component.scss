.upload-file-customer {
  border: none !important;
  background: none !important;
  text-decoration: var(--mb-color);
}

.create-customer {
  border: none !important;
  background: none !important;
  border-left: 1px solid var(--mb-color) !important;
}

.text-decoration {
  text-decoration: var(--mb-color);
  color: var(--mb-color);
}

.btn-danger {
  background: var(--mb-color);
  border-radius: 8px;
  color: white;
  font-size: 16px;
  padding: 8px 25px;
}

:host ::ng-deep .bank-upload .ant-upload.ant-upload-select-picture-card {
  height: 110px !important;
  width: 350px !important;
}

@media (min-width: 600px) and (max-width: 1199px) {
  :host ::ng-deep .bank-upload .ant-upload.ant-upload-select-picture-card {
    height: 110px !important;
    width: 150px !important;
  }
}

@media (min-width: 1200px) and (max-width: 1370px) {
  :host ::ng-deep .bank-upload .ant-upload.ant-upload-select-picture-card {
    height: 110px !important;
    width: 200px !important;
  }
}

@media (min-width: 1380px) and (max-width: 1690px) {
  :host ::ng-deep .bank-upload .ant-upload.ant-upload-select-picture-card {
    height: 110px !important;
    width: 240px !important;
  }
}

@media (min-width: 1691px) and (max-width: 1900px) {
  :host ::ng-deep .bank-upload .ant-upload.ant-upload-select-picture-card {
    height: 110px !important;
    width: 300px !important;
  }
}

::ng-deep .mat-radio-button.mat-accent .mat-radio-inner-circle {
  background-color: var(--mb-color);
}

::ng-deep .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
  border-color: var(--mb-color);
}

::ng-deep .mat-tab-labels {
  justify-content: end;
}

::ng-deep .mat-tab-group .mat-tab-header {
  justify-content: end;
}

.tab-labels {
  flex-grow: inherit;
  border-radius: 9px;
  transition: background-color 0.3s;
  box-shadow: 0px 0px 3.5px 0px #******** inset;
  background: #f3f3f3;
  display: flex;
  line-height: 40px;
  width: 367px;
  cursor: pointer;
}

.tab-group {
  padding: 0 24px;
  opacity: 0.6;
  justify-content: center;
  height: 40px;
  min-width: 120px;
}

.tab-list {
  display: flex;
  justify-content: end;
}

.action {
  background: linear-gradient(172.44deg, #3641ff 11.52%, #141ed2 81.46%);
  border-radius: 6px;
  color: white;
  box-shadow: 4px 0px 3px 0px #00000033;
  min-width: 120px;
  opacity: 1;
  margin: 3px;

  text-align: center;
}

.text-labels {
  text-align: center;
  margin-top: 12px;
}
