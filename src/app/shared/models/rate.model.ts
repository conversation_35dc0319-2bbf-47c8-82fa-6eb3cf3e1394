import { IDocument } from './file.model';
export interface IRate {
  interestRateId?: number;
  title?: string;
  createDate?: string;
  status?: number;
  toDate?: string;
  fromDate?: string;
  lastModifiedDate?: string;
  createdDate?: string;
  icon?: IDocument;
  iconUrL?: any;
  classPk?: number;
  language?: string;
  interestRateDetailDTO?: any;
  imageUrls?: any;
  interestRateDetailId?: number;
}
export class Rate implements IRate {
  constructor(
    public interestRateId?: number,
    public title?: string,
    public createDate?: string,
    public status?: number,
    public toDate?: string,
    public fromDate?: string,
    public createdDate?: string,
    public images?: IDocument,
    public iconUrL?: any,
    public classPk?: number,
    public language?: string,
    public interestRateDetailDTO?: any,
    public imageUrls?: any,
    public interestRateDetailId?: number
  ) {
    this.interestRateId = interestRateId;
    this.title = title;
    this.createDate = createDate;
    this.status = status;
    this.toDate = toDate;
    this.fromDate = fromDate;
    this.status = status;
    this.images = images;
    this.classPk = classPk;
    this.language = language;
    this.interestRateDetailDTO = interestRateDetailDTO;
    this.iconUrL = iconUrL;
    this.imageUrls = imageUrls;
    this.interestRateDetailId = interestRateDetailId;
  }
}
