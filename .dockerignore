.dockerignore
.git
.gitignore
# package-lock.json

# compiled output
dist
tmp
out-tsc
# Only exists if <PERSON><PERSON> was run
/bazel-out

# dependencies
node_modules

# profiling files
chrome-profiler-events*.json

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
.history/*
.angular/*

# husky
.husky/*

# misc
/.angular/cache
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings
