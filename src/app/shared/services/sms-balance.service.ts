import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { IClient, IServicePackType } from '@shared/models/service-pack.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { Observable } from 'rxjs';
import { AbstractDomainService } from './common/abstract.domain.service';
import { EntityResponseType } from './common/abstract.service';
import { ISmsBalance } from '@shared/models/sms-balance.model';

@Injectable({
  providedIn: 'root',
})
export class SmsBalanceService extends AbstractDomainService<ISmsBalance> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.SMS_BALANCE;

  export(request: ISmsBalance): Observable<EntityResponseType<ISmsBalance>> {
    return super.postFile(RESOURCE_CONST.SMS_BALANCE.API.EXPORT, request);
  }
}
