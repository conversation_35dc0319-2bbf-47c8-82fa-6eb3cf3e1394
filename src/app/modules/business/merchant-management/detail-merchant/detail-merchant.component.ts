import {
  <PERSON>mpo<PERSON>,
  <PERSON>ement<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ViewChild,
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { SafeUrl } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  APP_NAME_CONST,
  BUTTON_ACTION_CONST,
  CALCULATE_MILLISECONDS_IN_A_YEAR,
  DATE_RANGE_STATISTICAL_LOAN_ONLINE,
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  FILE_EXTENSION,
  GENDER,
  MOMENT_CONST,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IMerchantTransactionHistory } from '@shared/models/merchant-transaction-history.model';
import { IMerchant } from '@shared/models/merchant.model';
import { IMerchantTransactionHistorySearch } from '@shared/models/request/merchant-transaction-history.search';
import { AuthenticationService } from '@shared/services/auth/authentication.service';
import { DownloadService } from '@shared/services/helpers/download.service';
import { MerchantService } from '@shared/services/merchant.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';

import * as pdfFonts from 'pdfmake/build/vfs_fonts';
declare let pdfMake: any;
pdfMake.vfs = pdfFonts.pdfMake.vfs;

@Component({
  selector: 'app-detail-merchant',
  templateUrl: './detail-merchant.component.html',
  styleUrls: ['./detail-merchant.component.scss'],
})
export class DetailMerchantComponent implements OnInit, OnDestroy {
  formMerchant: FormGroup = new FormGroup({});
  // input call api
  merchantDto: IMerchant = {};
  // input data form control
  merchant: IMerchant = {};
  // master Merchant
  masterMerchant: IMerchant[] = [];
  // master Merchant
  merchantHistory: IMerchantTransactionHistory[] = [];
  action = '';
  currentValue = '';
  merchantAccountNumber = '';
  qrCode = '';
  qrCodeDownloadLink: SafeUrl = '';
  hasFilter = false;
  isUpdate = false;
  maxDateOfBirth = new Date();
  maxToDate = new Date();

  pageSizeOptions = PAGINATION.OPTIONS;
  ROUTER_UTILS = ROUTER_UTILS;
  BUTTON_ACTION_CONST = BUTTON_ACTION_CONST;
  ENTITY_STATUS = ENTITY_STATUS;
  VALIDATORS = VALIDATORS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  GENDER_LIST = GENDER;
  SYSTEM_RULES = SYSTEM_RULES;

  // default form search
  formMerchantHistorySearch = this.fb.group({
    // customerAccountNumber: '',
    // customerCif: '',
    // transactionId: '',
    keywordMerchant: '',
    transactionDateStart: [null, [Validators.required]],
    transactionDateEnd: [null, [Validators.required]],
    merchantId: null,
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    sourceZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
    // previousPageIndex: 0,
  });

  merchantHistorySearch: IMerchantTransactionHistorySearch = {};

  maxDate = new Date();

  @ViewChild('qrElement')
  qrCodeElement: ElementRef | any;

  constructor(
    private fb: FormBuilder,
    private formBuilder: FormBuilder,
    private merchantService: MerchantService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private downloadService: DownloadService,
    private translateService: TranslateService,
    private authenticationService: AuthenticationService
  ) {
    this.activatedRoute.data.subscribe((res) => {
      this.action = res.action;
    });
    // get id form paramater
    this.activatedRoute.paramMap.subscribe((res) => {
      const idParam = res.get('merchantId');
      if (idParam) {
        this.merchantDto.merchantId = +idParam;
        this.isUpdate = true;
      }
    });
  }

  /**
   * remove storage
   */
  ngOnDestroy(): void {
    if (!this.hasFilter) {
      sessionStorage.removeItem(STORAGE_APP.MERCHANT);
    }
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.hasFilter = true;
  }

  ngOnInit(): void {
    this.getMasterMerchant();
    if (this.isUpdate) {
      this.getDetail();
      // this.mappingRoles(this.roles);
    } else {
      this.initForm();
    }
  }

  /**
   * init form
   *
   * @param merchant User
   */
  initForm(merchant?: IMerchant): void {
    this.formMerchant = this.formBuilder.group({
      merchantCode: [merchant?.merchantCode || ''],
      merchantName: [merchant?.merchantName || ''],
      merchantAccountNumber: [merchant?.merchantAccountNumber || ''],
      parentId: [merchant?.parentId || null, [Validators.required]],
      status: [merchant?.status || ENTITY_STATUS_CONST.ACTIVE.code],
      description: [merchant?.description || ''],
      qrCode: [merchant?.qrCode || ''],
      currency: [merchant?.currency || ''],
    });
    this.formMerchant.disable();
    this.getMasterMerchant(merchant);
  }

  /**
   * back to monitor list
   */
  backToList(): void {
    this.hasFilter = true;
    this.router.navigate([ROUTER_UTILS.merchant.root]);
  }

  /**
   * get detail merchant
   */
  getDetail(): void {
    if (this.merchantDto.merchantId) {
      this.merchantService.detail(this.merchantDto).subscribe((res: any) => {
        this.merchant = res.body;
        const data = res.body as IMerchant;
        if (data.qrCode?.qrCodeValue) {
          this.qrCode = data.qrCode?.qrCodeValue;
        }
        if (this.merchant.merchantId) {
          // this.formMerchantHistorySearch.controls.parentId.setValue(
          //   this.merchant.merchantId
          // );
          const startDate = moment()
            .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
            .format(MOMENT_CONST.FORMAT_DEFAULT);
          const endDate = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
          this.formMerchantHistorySearch.controls.transactionDateStart.setValue(
            CommonUtils.reverseDate(startDate)
          );
          this.formMerchantHistorySearch.controls.transactionDateEnd.setValue(
            CommonUtils.reverseDate(endDate)
          );
          this.formMerchantHistorySearch.controls.merchantId.setValue(
            this.merchant.merchantId
          );
          this.onSearchMerchantHistory();
        }

        this.initForm(data);
      });
    }
  }

  totalAmount(value = 0, fee = 0): number {
    return +value + +fee;
  }

  /**
   * index on list
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formMerchantHistorySearch.value.pageIndex,
      this.formMerchantHistorySearch.value.pageSize
    );
  }

  /**
   * Change page: next page and preview page
   *
   * @param page PageEvent
   */
  onChangePage(page: PageEvent) {
    this.formMerchantHistorySearch.controls.pageIndex.setValue(page.pageIndex);
    this.formMerchantHistorySearch.controls.pageSize.setValue(page.pageSize);
    this.onSearchMerchantHistory();
  }

  // /**
  //  * load list data Loan Online
  //  *
  //  * @param page PageEvent
  //  */
  // loadData(page: PageEvent): void {
  //   this.merchantHistorySearch.pageIndex = page.pageIndex;
  //   this.merchantHistorySearch.pageSize = page.pageSize;
  //   this.merchantService
  //     .transactionHistory(this.merchantHistorySearch)
  //     .subscribe((res: any): void => {
  //       this.merchantHistory = res.body.content;
  //       this.totalRecord = res.body.totalElements;
  //     });
  // }

  onSearchMerchantHistorySubmit() {
    this.formMerchantHistorySearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.formMerchantHistorySearch.controls.length.setValue(
      PAGINATION.LENGTH_DEFAULT
    );
    this.onSearchMerchantHistory();
  }

  onSearchMerchantHistory(): void {
    if (
      this.authenticationService.hasAnyPrivileges(
        SYSTEM_RULES.MERCHANT_QUERY_TRANSACTION_HISTORY
      )
    ) {
      const body = this.formMerchantHistorySearch.value;
      const params = { ...body, keyword: body.keywordMerchant };
      if (this.formMerchantHistorySearch.invalid) {
        CommonUtils.markFormGroupTouched(this.formMerchantHistorySearch);
      } else {
        this.merchantService
          .transactionHistory(params)
          .subscribe((res: any): void => {
            this.merchantHistory = res.body.content;
            this.merchantHistory.forEach((element) => {
              element.transactionDateStr = moment(
                CommonUtils.reverseDateTimeZone(element.transactionDateStr)
              ).format(MOMENT_CONST.LOCAL_DATE_TIME_FORMAT);
            });
            this.formMerchantHistorySearch.controls.length.setValue(
              res.body.totalElements
            );
          });
      }
    }
  }

  /**
   * load list data master Merchant
   *
   */
  getMasterMerchant(merchant?: IMerchant): void {
    let params = {};
    if (merchant) {
      params = {
        serviceType: merchant.serviceType,
        status: merchant.status,
        origin: merchant.origin,
        keyword: merchant.merchantAccountNumber,
        timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
      };
    } else {
      params = {
        timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
      };
    }

    this.merchantService.searchMaster(params).subscribe((res: any): void => {
      this.masterMerchant = res.body.content;
    });
  }

  // getTransactionHistory(
  //   merchantHistorySearchKey: IMerchantTransactionHistorySearch
  // ): void {
  //   this.merchantService
  //     .transactionHistory(merchantHistorySearchKey)
  //     .subscribe((res: any): void => {
  //       this.merchantHistory = res.body.content;
  //       this.totalRecord = res.body.totalElements;
  //     });
  // }

  onChangeGetMerchantAccountNumber(value: any): void {
    this.masterMerchant.forEach((element) => {
      if (element.merchantId === value) {
        this.formMerchant.controls.merchantAccountNumber.setValue(
          element.merchantAccountNumber
        );
      }
    });
  }

  getImage(event: any) {
    // console.log(event);
  }

  onChangeURL(url: SafeUrl) {
    this.qrCodeDownloadLink = url;
  }

  /**
   * export qr code merchant
   *
   * @param action BUTTON_ACTION_CONST
   */
  generatePdf(action: string): void {
    /**
     * Case 1
     */
    const docDefinition = {
      compress: false,
      watermark: {
        text: APP_NAME_CONST,
        angle: 30,
        opacity: 0.1,
        bold: true,
        italics: true,
      },
      info: {
        author: APP_NAME_CONST,
        title:
          APP_NAME_CONST +
          ' | ' +
          this.merchant.merchantName +
          ' [' +
          this.merchant.merchantCode +
          ']',
      },
      defaultStyle: {
        font: 'Roboto',
      },
      content: [
        {
          text: this.merchant.merchantName + ' - ' + this.merchant.merchantCode,
          style: 'content',
        },
        {
          qr: this.qrCode,
          foreground: '#000000',
          background: '#ffffff',
          fit: 500,
          alignment: 'center',
          margin: [0, 0, 0, 0],
          mask: 1,
          ecLevel: 'H',
        },
      ],
      styles: {
        name: {
          fontSize: 16,
          bold: true,
          alignment: 'center',
        },
        content: {
          bold: true,
          fontSize: 20,
          alignment: 'center',
          margin: [30, 0, 30, 20],
        },
      },
    };

    switch (action) {
      case BUTTON_ACTION_CONST.OPEN:
        pdfMake.createPdf(docDefinition).open();
        break;
      case BUTTON_ACTION_CONST.PRINT:
        pdfMake.createPdf(docDefinition).print();
        break;
      case BUTTON_ACTION_CONST.DOWNLOAD:
        pdfMake
          .createPdf(docDefinition)
          .download(
            `QrCode_${this.merchant.merchantName}[${
              this.merchant.merchantCode
            }]_${moment(Date.now()).format(MOMENT_CONST.TIMESTAMP_FORMAT)}.pdf`
          );
        break;
      default:
        pdfMake.createPdf(docDefinition).open();
        break;
    }

    /**
     * Case 2
     */
    // const canvasData = this.qrCodeElement?.nativeElement
    //   .querySelector('canvas')
    //   .toDataURL();
    // const win = window.open('about:blank', '_blank');
    // win?.document.open();
    // win?.document.write(
    //   [
    //     '<html>',
    //     '   <head>',
    //     '   </head>',
    //     '   <body onload="window.print()" onafterprint="window.close()">',
    //     '       <img src="' + canvasData + '"/>',
    //     '   </body>',
    //     '</html>',
    //   ].join('')
    // );
    // win?.document.close();

    /**
     * Case 3
     */
    // const canvasData = this.qrCodeElement?.nativeElement
    //   .querySelector('canvas')
    //   .toDataURL();
    // let windowContent = '<!DOCTYPE html>';
    // windowContent += '<html>';
    // windowContent += '<head><title>Print canvas</title></head>';
    // windowContent +=
    //   '<body onload="window.print()" onafterprint="window.close()">';
    // windowContent += '<img src="' + canvasData + '">';
    // windowContent += '</body>';
    // windowContent += '</html>';

    // const printWin = window.open(
    //   '',
    //   '',
    //   'width=' + screen.availWidth + ',height=' + screen.availHeight
    // );
    // printWin?.document.open();
    // printWin?.document.write(windowContent);
    // printWin?.document.close();
    // printWin?.document.addEventListener(
    //   'load',
    //   function () {
    //     printWin.focus();
    //     printWin.print();
    //     printWin.document.close();
    //     // printWin.close();
    //   },
    //   true
    // );
  }

  printDocument(documentId: any) {
    const doc = document.getElementById(documentId);
  }

  /**
   * export file
   */
  exportFile(): void {
    // get value form control
    const bodySearch = this.formMerchantHistorySearch.value;
    if (this.formMerchantHistorySearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formMerchantHistorySearch);
    } else {
      // get file name
      const fileName = this.translateService.instant(
        'template.merchantHistory',
        { param: this.merchant.merchantCode }
      );
      const obFile = this.merchantService.export({
        transactionDateStart: bodySearch.transactionDateStart,
        transactionDateEnd: bodySearch.transactionDateEnd,
        keyword: bodySearch.keywordMerchant,
        merchantId: this.merchant.merchantId,
        sourceZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      });

      this.downloadService.downloadFileWithObservableAndName(
        obFile,
        fileName,
        FILE_EXTENSION.XLSX
      );
    }
  }

  isValidMaxDate = (control: FormControl) => {
    const dateOfBirth = new Date(control.value).getTime();
    const maxDateOfBirth = new Date().getTime();
    return dateOfBirth > maxDateOfBirth ? { invalidMaxDate: true } : null;
  };

  dateValidator(value: number): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return value > DATE_RANGE_STATISTICAL_LOAN_ONLINE
        ? { invalidDate: true }
        : null;
    };
  }

  changeValidDate() {
    if (
      this.formMerchantHistorySearch.controls.transactionDateStart.value &&
      this.formMerchantHistorySearch.controls.transactionDateEnd.value
    ) {
      if (this.formMerchantHistorySearch.controls['transactionDateEnd'].value) {
        this.maxDate =
          this.formMerchantHistorySearch.controls['transactionDateEnd'].value;
      }
      const startDate = CommonUtils.setStartTime(
        new Date(
          this.formMerchantHistorySearch.controls.transactionDateStart.value
        ).getTime()
      );
      const endDate = CommonUtils.setStartTime(
        new Date(
          this.formMerchantHistorySearch.controls.transactionDateEnd.value
        ).getTime()
      );
      const dayMax =
        (endDate - startDate) / CALCULATE_MILLISECONDS_IN_A_YEAR.DAY;

      if (dayMax > DATE_RANGE_STATISTICAL_LOAN_ONLINE) {
        this.formMerchantHistorySearch.controls.transactionDateStart.setValidators(
          [Validators.required, this.dateValidator(dayMax), this.isValidMaxDate]
        );
        this.formMerchantHistorySearch.controls.transactionDateStart.updateValueAndValidity();
      } else {
        this.formMerchantHistorySearch.controls.transactionDateStart.clearValidators();
        this.formMerchantHistorySearch.controls.transactionDateStart.setValidators(
          [Validators.required, this.isValidMaxDate]
        );
        this.formMerchantHistorySearch.controls.transactionDateStart.updateValueAndValidity();
      }
    }
    this.formMerchantHistorySearch.controls.transactionDateEnd.setValidators([
      Validators.required,
      this.isValidMaxDate,
    ]);
    this.formMerchantHistorySearch.controls.transactionDateEnd.updateValueAndValidity();
  }
}
