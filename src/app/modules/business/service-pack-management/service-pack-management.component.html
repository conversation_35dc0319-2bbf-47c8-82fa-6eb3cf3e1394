<div class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="searchForm" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-lg-2">
            <div class="form-group">
              <label>{{ "common.action.searchKeyword" | translate }}</label>
              <input trim class="form-control w-100" formControlName="keyword" type="text"
                placeholder="{{ 'common.action.searchKeyword' | placeholder }}"
                [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH" />
            </div>
          </div>
          <div class="col-lg-2">
            <div class="form-group">
              <label>{{ "servicePack.type" | translate }}</label>
              <ng-select [searchFn]="customServicePackSearchFn" [multiple]="true" appearance="outline"
                [searchable]="false" placeholder="{{ 'servicePack.type' | translate }}" formControlName="types"
                [clearable]="false">
                <ng-option *ngFor="let item of servicePackTypes" [value]="item.transactionFeeTypeCode">
                  <span [title]="item.transactionFeeTypeName">{{ item.transactionFeeTypeName | limitWord : 25 }}</span>
                </ng-option>
                <ng-template ng-header-tmp>
                  <div>
                    <button class="btn btn-link" type="button" (click)="onSelectAll(SELECTED_CONST.SERVICE_PACK_TYPE)">
                      {{ "common.action.selectAll" | translate }}
                    </button>
                    <button class="btn btn-link" type="button" (click)="onClearAll(SELECTED_CONST.SERVICE_PACK_TYPE)">
                      {{ "common.action.clearAll" | translate }}
                    </button>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="col-lg-2">
            <div class="form-group">
              <label>{{ "servicePack.clientId" | translate }}</label>
              <ng-select [searchFn]="customClientSearchFn" [multiple]="true" appearance="outline" [searchable]="true"
                [clearable]="true" placeholder="{{ 'servicePack.clientId' | translate }}" [searchable]="true"
                formControlName="clientIds" [clearable]="false">
                <ng-option *ngFor="let item of clients" [value]="item.clientId">
                  {{ item.clientId }} - {{ item.name }}
                </ng-option>
                <ng-template ng-header-tmp>
                  <div>
                    <button class="btn btn-link" type="button" (click)="onSelectAll(SELECTED_CONST.CLIENT_ID)">
                      {{ "common.action.selectAll" | translate }}
                    </button>
                    <button class="btn btn-link" type="button" (click)="onClearAll(SELECTED_CONST.CLIENT_ID)">
                      {{ "common.action.clearAll" | translate }}
                    </button>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="col-lg-2">
            <div class="form-group">
              <label>{{ "model.manageSaving.status" | translate }}</label>
              <ng-select placeholder="{{ 'model.manageSaving.status' | translate }}" [searchable]="false"
                formControlName="status" [clearable]="true">
                <ng-option [value]="item.code" *ngFor="let item of ENTITY_STATUS">
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 mt-4 d-flex">
            <div class="col-btn-reset" ngbTooltip="{{ 'common.action.reset' | translate }}" (click)="onReset()">
              <div class="btn-reset">
                <i class="bi bi-arrow-clockwise" type="button"> </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" type="submit" *hasPrivileges="SYSTEM_RULES.SERVICE_PACK_READ">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-3 mt-4">
          <button class="btn btn-red" type="button" (click)="onCreate()"
            *hasPrivileges="SYSTEM_RULES.SERVICE_PACK_CREATE">
            {{ "common.action.create" | translate }}
          </button>
        </div>
      </form>

      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th [width]="'50px'" class="text-center">
                {{ "model.manageSaving.no" | translate }}
              </th>
              <th class="text-left" [width]="'250px'">
                {{ "servicePack.code" | translate }}
              </th>
              <th class="text-left" [width]="'250px'">
                {{ "servicePack.name" | translate }}
              </th>
              <th [width]="'180px'" class="text-left" [width]="'250px'">
                {{ "servicePack.type" | translate }}
              </th>
              <th [width]="'180px'" class="text-left">
                {{ "servicePack.clientId" | translate }}
              </th>
              <th [width]="'200px'" class="text-left">
                {{ "model.role.updater" | translate }}
              </th>
              <th [width]="'200px'" class="text-left">
                {{ "model.role.lastModifiedDate" | translate }}
              </th>
              <th [width]="'200px'" class="text-center">
                {{ "common.status" | translate }}
              </th>
              <th [width]="'200px'" class="text-center">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of data; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td class="text-left">{{ item?.code }}</td>
              <td class="text-left" title="{{ item?.name }}">{{ item?.name | limitWord : 30 }}</td>
              <td class="text-left">{{ item?.typeStr }}</td>
              <td class="text-left">{{ item?.clientId }}</td>
              <td class="text-left">{{ item?.lastModifiedBy }}</td>
              <td class="text-left">{{ item?.lastModifiedDate }}</td>
              <td class="text-center">
                <span class="badge" [ngClass]="ENTITY_STATUS_MAP[item?.status || 0].style">
                  {{ ENTITY_STATUS_MAP[item?.status || 0].label | translate }}
                </span>
              </td>
              <td class="text-center">
                <button ngbTooltip="{{ 'common.action.detail' | translate }}" class="btn px-1 py-0"
                  (click)="detail(item?.servicePackId)">
                  <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                </button>
                <button ngbTooltip="{{ 'common.action.update' | translate }}" class="btn px-1 py-0"
                  (click)="edit(item?.servicePackId)" data-toggle="modal"
                  *hasPrivileges="SYSTEM_RULES.SERVICE_PACK_UPDATE">
                  <i class="fa fa-edit mb-color" aria-hidden="true"></i>
                </button>
                <ng-container *hasPrivileges="
                    item?.status === 1
                      ? SYSTEM_RULES.SERVICE_PACK_LOCK
                      : SYSTEM_RULES.SERVICE_PACK_UNLOCK
                  ">
                  <button [ngbTooltip]="
                      (item?.status === ENTITY_STATUS_CONST.ACTIVE.code
                        ? 'common.action.lock'
                        : 'common.action.unlock'
                      ) | translate
                    " class="btn px-1 py-0" (click)="lockAndUnlock(item)">
                    <i [className]="
                        item?.status === ENTITY_STATUS_CONST.ACTIVE.code
                          ? 'fa fa-lock mb-color'
                          : 'fa fa-unlock mb-color'
                      " aria-hidden="true"></i>
                  </button>
                </ng-container>
                <ng-container *hasPrivileges="SYSTEM_RULES.SERVICE_PACK_DELETE">
                  <button ngbTooltip="{{ 'common.action.delete' | translate }}" class="btn px-1 py-0"
                    (click)="delete(item)">
                    <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                  </button>
                </ng-container>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="row d-block text-center m-0 no-search-result-wrapper" *ngIf="data?.length === 0">
          <img src="/assets/dist/img/icon/empty.svg" height="120" alt="no_search_result" />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
      </div>
      <div *ngIf="data?.length">
        <mat-paginator [length]="searchForm.value.length" [pageSize]="searchForm.value.pageSize"
          [pageIndex]="searchForm.value.pageIndex" [pageSizeOptions]="pageSizeOptions" (page)="onChangePage($event)"
          aria-label="Select page">
        </mat-paginator>
      </div>
    </div>
  </div>
</div>