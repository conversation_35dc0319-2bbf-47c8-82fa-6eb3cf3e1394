<section class="box-content">
  <div class="container-fluid">
    <div class="back-container">
      <div class="col-md-6 back-container">
        <img src="assets/dist/img/icon/back.png" (click)="backToList()" />
        <h5>
          {{
            action === ROUTER_ACTIONS.create
              ? ("model.news.create" | translate)
              : action === ROUTER_ACTIONS.update
              ? ("model.news.update" | translate)
              : ("model.news.detail" | translate)
          }}
        </h5>
      </div>
      <div class="col-md-6">
        <div class="tab-list">
          <div class="col-3 text-labels">
            {{ "common.textLanguage" | translate }}
          </div>
          <div class="tab-labels">
            <div class="" *ngFor="let lang of LANGUAGES">
              <div
                [ngClass]="
                  language === lang.code ? 'tab-group action' : 'tab-group'
                "
                (click)="onChangeLang(lang.code)"
              >
                {{ lang.label | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-12">
      <div class="row">
        <h2 class="title-create">{{ "model.news.information" | translate }}</h2>
      </div>
      <div class="row border-ckeditor">
        <h3 class="border-ckeditor-h3">
          {{ "model.news.information" | translate }}
        </h3>
        <div class="w-100">
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-12">
                <form
                  [formGroup]="formCreate"
                  *ngIf="
                    this.action === ROUTER_ACTIONS.create
                      ? formCreate
                      : data?.newsId
                  "
                >
                  <div class="col-md-12">
                    <div class="row">
                      <div class="col-md-3">
                        <div class="form-group">
                          <label
                            >{{ "model.news.categories" | translate
                            }}<span class="text-danger">*</span></label
                          >
                          <ng-select
                            appearance="outline"
                            [searchable]="false"
                            placeholder="{{
                              'model.news.categories' | translate
                            }}"
                            [clearable]="false"
                            formControlName="categoryId"
                          >
                            <ng-option
                              [value]="item?.categoryId"
                              *ngFor="let item of categoryList"
                            >
                              {{ item?.name }}
                            </ng-option>
                          </ng-select>
                          <small
                            class="form-text text-danger noti-small"
                            *ngIf="
                              formCreate.get('categoryId')?.errors?.required &&
                              formCreate.get('categoryId')?.touched
                            "
                          >
                            {{ "model.news.required.categories" | translate }}
                          </small>
                        </div>
                      </div>
                      <div class="col-12">
                        <div class="form-group">
                          <label
                            >{{ "model.event.title" | translate
                            }}<span class="text-danger">*</span></label
                          >
                          <input
                            trim
                            formControlName="title"
                            type="text"
                            placeholder="{{ 'model.event.title' | translate }}"
                            class="w-100"
                            class="form-control"
                            [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
                          />
                          <small
                            class="form-text text-danger noti-small"
                            *ngIf="
                              formCreate.get('title')?.errors?.required &&
                              formCreate.get('title')?.touched
                            "
                          >
                            {{ "error.event.required.title" | translate }}
                          </small>
                          <small
                            class="form-text text-danger noti-small"
                            *ngIf="
                              formCreate.get('title')?.errors?.maxlength &&
                              formCreate.get('title')?.touched
                            "
                          >
                            {{
                              "error.event.maxLength.title"
                                | translate
                                  : {
                                      param:
                                        VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH
                                    }
                            }}
                          </small>
                        </div>
                      </div>
                      <div class="col-3">
                        <div class="col-12">
                          <label>{{ "common.image" | translate }}</label>
                          <div class="bank-upload">
                            <app-upload-images
                              (imageUploaded)="onUploadPics($event)"
                              [fileRequired]="fileRequired"
                              [fileExtension]="FILE_UPLOAD_EXTENSIONS.ICON"
                              (imageRemoved)="onRemoveImage($event)"
                              [imageUrls]="imageUrls"
                              [checkDisabled]="action === ROUTER_ACTIONS.detail"
                              [type]="action"
                            ></app-upload-images>
                          </div>
                        </div>
                      </div>
                      <div class="col-12">
                        <div class="form-group">
                          <label
                            >{{ "model.event.content" | translate
                            }}<span class="text-danger">*</span></label
                          >
                          <mb-editor
                            #content
                            [placeholder]="'model.event.content' | translate"
                            [isReadOnly]="action === ROUTER_ACTIONS.detail"
                            [value]="formCreate?.get('content')?.value || ''"
                            [maxLength]="maxLengthContent"
                            (HTMLdata)="getTemplate($event)"
                            (data)="onChangeData('content', $event)"
                            trim
                          >
                          </mb-editor>
                          <small
                            class="form-text text-danger noti-small"
                            *ngIf="
                              formCreate.get('content')?.errors?.required &&
                              formCreate.get('content')?.touched
                            "
                          >
                            {{ "error.event.required.content" | translate }}
                          </small>
                          <small
                            class="form-text text-danger noti-small"
                            *ngIf="
                              formCreate.get('content')?.errors?.maxlength &&
                              formCreate.get('content')?.touched
                            "
                          >
                            {{ "model.news.error.content" | translate }}
                          </small>
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
            <div class="d-block text-center mb-5 mt-4">
              <ng-container>
                <button class="btn btn-white mr-2" (click)="backToList()">
                  {{ "common.action.back" | translate }}
                </button>
                <button
                  class="btn btn-red"
                  (click)="onSubmit()"
                  *ngIf="action !== ROUTER_ACTIONS.detail"
                >
                  {{
                    (action === ROUTER_ACTIONS.update
                      ? "common.action.update"
                      : "common.action.create"
                    ) | translate
                  }}
                </button>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
