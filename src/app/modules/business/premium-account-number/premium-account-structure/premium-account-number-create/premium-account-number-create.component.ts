import { Compo<PERSON>, HostListener, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  FormBuilder,
  FormGroup,
  Validators
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CONFIG_ACCOUNT_CONFIG_CONST } from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { INumberGroup } from '@shared/models/number-group.model';
import { INumberStructure } from '@shared/models/premium-account-number.modal';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { NumberGroupService } from '@shared/services/number-group.service';
import { NumberStructureService } from '@shared/services/premium-account-number.serice';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-premium-account-number-create',
  templateUrl: './premium-account-number-create.component.html',
  styleUrls: ['./premium-account-number-create.component.scss'],
})
export class PremiumAccountNumberCreateComponent implements OnInit, OnDestroy {
  formCreate: FormGroup = new FormGroup({});
  VALIDATORS = VALIDATORS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  action = '';
  hasFilter = false;
  SYSTEM_RULES = SYSTEM_RULES;
  CONFIG_ACCOUNT_CONFIG_CONST = CONFIG_ACCOUNT_CONFIG_CONST;
  numberStructure: INumberStructure = {};
  numberStructures: FormArray = new FormArray([]);
  numberGroupsDetail: INumberGroup[] = [];
  numberGroups: INumberGroup[] = [];
  numberGroup: INumberGroup = {};
  lengths: INumberGroup[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private numberStructureService: NumberStructureService,
    private numberGroupService: NumberGroupService,
    private toastService: ToastrCustomService,
  ) {
    this.activatedRoute.data.subscribe((res) => {
      this.action = res.action;
    });

    this.activatedRoute.paramMap.subscribe((res) => {
      const idParam = res.get('id');
      if (idParam) {
        this.numberStructure.premiumAccountNumberStructureId = +idParam;
      }
    });
  }

  ngOnDestroy(): void {
    if (!this.hasFilter) {
      sessionStorage.removeItem(STORAGE_APP.USER);
    }
  }

  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.hasFilter = true;
  }

  ngOnInit(): void {
    this.getLength();
    if (this.action === ROUTER_ACTIONS.create) {
      this.initForm();
      // this.getNumberGroup();
    } else {
      this.getDetail();
    }
  }

  getDetail() {
    if (this.numberStructure?.premiumAccountNumberStructureId) {
      this.numberStructureService
        .detail(this.numberStructure?.premiumAccountNumberStructureId)
        .subscribe((res: any) => {
          this.numberStructure = res.body;
          this.initForm(res.body);
        });
    }
  }

  initForm(structure?: INumberStructure): void {
    this.formCreate = this.formBuilder.group({
      name: [
        {
          value: structure?.name || '',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [Validators.required, Validators.pattern(VALIDATORS.PATTERN.NO_SPECIAL_CHARACTERS)],
      ],
      // numberGroupId: [
      //   {
      //     value: structure?.numberGroupId || null,
      //     disabled: this.action === ROUTER_ACTIONS.detail,
      //   },
      //   [Validators.required]
      // ],
      numberStructure: [
        {
          value: structure?.numberStructure || '',
          disabled: this.action !== ROUTER_ACTIONS.create,
        },
        [Validators.required],
      ],
      pattern: [
        {
          value: structure?.pattern || '',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [Validators.required]
      ],
      price: [
        {
          value: structure?.price ? CommonUtils.moneyFormat(structure.price)?.toString() : '',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [Validators.required]
      ],
      // minPrice: [
      //   {
      //     value: structure?.minPrice ? CommonUtils.moneyFormat(structure.minPrice)?.toString() : '0',
      //     disabled: true,
      //   },
      //   [Validators.required]
      // ],
      // maxPrice: [
      //   {
      //     value: structure?.maxPrice ? CommonUtils.moneyFormat(structure.maxPrice)?.toString() : '0',
      //     disabled: true,
      //   },
      //   [Validators.required]
      // ],
      discount: [
        {
          value: structure?.discount || '0',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [Validators.required]
      ],
      totalPrice: [
        {
          value: structure?.totalPrice ? CommonUtils.moneyFormat(structure.totalPrice)?.toString() : '0',
          disabled: true,
        },
        [Validators.required]
      ],
      length: [
        {
          value: structure?.length || null,
          disabled: this.action !== ROUTER_ACTIONS.create,
        },
        [Validators.required]
      ],
      createdBy: [
        {
          value: structure?.createdBy || '',
          disabled: true,
        }
      ],
      createdDate: [
        {
          value: structure?.createdDate || '',
          disabled: true,
        }
      ],
      lastModifiedBy: [
        {
          value: structure?.lastModifiedBy || '',
          disabled: true,
        }
      ],
      lastModifiedDate: [
        {
          value: structure?.lastModifiedDate || '',
          disabled: true
        }
      ]
    });
    // this.getNumberGroup(structure);
  }

  backToList(): void {
    this.hasFilter = true;
    this.router.navigate([
      `${ROUTER_UTILS.premiumAccountNumber.root}/${ROUTER_UTILS.premiumAccountNumber.numberStructure.root}`,
    ]);
  }

  onCreate() {
    if (this.formCreate.invalid) {
      CommonUtils.markFormGroupTouched(this.formCreate);
    }

    if (this.formCreate.valid) {
      let price = this.formCreate.get('price')?.value || 0;
      const discount = this.formCreate.get('discount')?.value || 0;
      if (price !== 0) {
        price = price.toString().split('.').join('');
      }

      const body = {
        name: this.formCreate.get('name')?.value,
        numberStructure: this.formCreate.get('numberStructure')?.value,
        length: this.formCreate.get('length')?.value,
        price,
        pattern: this.formCreate.get('pattern')?.value,
        discount
      };

      this.numberStructureService.create(body).subscribe(() => {
        this.toastService.success('common.action.createSuccess');
        this.router.navigate([
          ROUTER_UTILS.premiumAccountNumber.root,
          ROUTER_UTILS.premiumAccountNumber.numberStructure.root
        ]);
      });

    }
  }

  onUpdate() {
    if (this.formCreate.invalid) {
      CommonUtils.markFormGroupTouched(this.formCreate);
    }

    if (this.formCreate.valid) {
      let price = this.formCreate.get('price')?.value || 0;
      const discount = this.formCreate.get('discount')?.value || 0;

      if (price !== 0) {
        price = price.toString().split('.').join('');
      }

      const body = {
        premiumAccountNumberStructureId: this.numberStructure.premiumAccountNumberStructureId,
        name: this.formCreate.get('name')?.value,
        numberStructure: this.formCreate.get('numberStructure')?.value,
        length: this.formCreate.get('length')?.value,
        price,
        pattern: this.formCreate.get('pattern')?.value,
        discount
      };

      this.numberStructureService.update(body).subscribe(() => {
        this.toastService.success('common.action.updateSuccess');
        this.router.navigate([
          ROUTER_UTILS.premiumAccountNumber.root,
          ROUTER_UTILS.premiumAccountNumber.numberStructure.root
        ]);
      });

    }
  }

  getNumberGroup(structure?: INumberStructure) {
    this.numberGroupService
      .getNumberGroup()
      .subscribe((res: any) => {
        if (res.body.length) {
          this.numberGroups = res.body;
        }
      });
  }

  getLength() {
    this.numberStructureService
      .getLength()
      .subscribe((res: any) => {
        if (res.body.length) {
          this.lengths = res.body;
        }
      });
  }

  // onChange(item: INumberGroup) {
  //   this.formCreate.get('minPrice')?.setValue(CommonUtils.moneyFormat(item?.minPrice + ''));
  //   this.formCreate.get('maxPrice')?.setValue(CommonUtils.moneyFormat(item?.maxPrice + ''));
  // }

  procesAccountAmount(item: any) {
    let price = this.formCreate.get('price')?.value || 0;
    let discount = this.formCreate.get('discount')?.value || 0;
    // const maxPrice = this.formCreate.get('maxPrice')?.value.split('.').join('') || '';

    if (price[0] === '0' && price.length > 1) {
      this.formCreate.get('price')?.setValue(price.replace(/^0+/, ''));
    }

    if (price !== 0) {
      price = price.split('.').join('');
    }

    if (discount[0] === '0' && discount.length > 1) {
      if (discount[1] !== '.') {
        this.formCreate.get('discount')?.setValue(discount.replace(/^0+/, ''));
      }
    }

    const totalPrice = Math.round(price - (price * discount) / 100);
    this.formCreate.get('totalPrice')?.setValue(CommonUtils.moneyFormat(totalPrice + ''));
  }

  // validatePrice() {
  //   let price = this.formCreate.get('price')?.value || '';
  //   const minPrice = this.formCreate.get('minPrice')?.value.split('.').join('') || 0;
  //   const maxPrice = this.formCreate.get('maxPrice')?.value.split('.').join('') || 0;

  //   if (price === '' || price === undefined || price === null) {
  //     this.formCreate.controls.price.setValidators([Validators.required]);
  //   } else {
  //     price = price.split('.').join('');

  //     if (+price < +minPrice || +price > +maxPrice) {
  //       this.formCreate.controls.price.setValidators([
  //         Validators.required,
  //         Validators.min(minPrice),
  //         Validators.max(maxPrice)
  //       ]);
  //     } else {
  //       this.formCreate.controls.price.setValidators(null);
  //     }
  //   }
  //   this.formCreate.controls.price.updateValueAndValidity();
  // }

  onOpenUpdate() {
    this.router.navigate([
      ROUTER_UTILS.premiumAccountNumber.root,
      ROUTER_UTILS.premiumAccountNumber.numberStructure.root,
      this.numberStructure.premiumAccountNumberStructureId,
      ROUTER_ACTIONS.update,
    ]);
  }
}
