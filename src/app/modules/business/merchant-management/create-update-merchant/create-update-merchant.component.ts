import {
  Component,
  ElementRef,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ni<PERSON>,
  ViewChild,
} from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { SafeUrl } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import {
  APP_NAME_CONST,
  BUTTON_ACTION_CONST,
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  GENDER,
  MASTER_MERCHANT_NAME_STATUS,
  MASTER_MERCHANT_SERVICE_ENUM,
  MOMENT_CONST,
  ORIGIN_CONST,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IMerchantTransactionHistory } from '@shared/models/merchant-transaction-history.model';
import { I<PERSON>er<PERSON>t } from '@shared/models/merchant.model';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { MerchantService } from '@shared/services/merchant.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';

import * as pdfFonts from 'pdfmake/build/vfs_fonts';
declare let pdfMake: any;
pdfMake.vfs = pdfFonts.pdfMake.vfs;

@Component({
  selector: 'app-create-update-merchant',
  templateUrl: './create-update-merchant.component.html',
  styleUrls: ['./create-update-merchant.component.scss'],
})
export class CreateUpdateMerchantComponent implements OnInit, OnDestroy {
  formMerchant: FormGroup = new FormGroup({});

  // input call api
  merchantDto: IMerchant = {};
  // input data form control
  merchant: IMerchant = {};
  // master Merchant
  masterMerchant: IMerchant[] = [];
  // master Merchant
  merchantHistory: IMerchantTransactionHistory[] = [];
  action = '';
  currentValue = '';
  merchantAccountNumber = '';
  qrCode = '';
  qrCodeDownloadLink: SafeUrl = '';
  hasFilter = false;
  isUpdate = false;
  maxDateOfBirth = new Date();

  pageSize = PAGINATION.PAGE_SIZE_DEFAULT;
  pageIndex = PAGINATION.PAGE_NUMBER_DEFAULT;
  ROUTER_UTILS = ROUTER_UTILS;
  ENTITY_STATUS = ENTITY_STATUS;
  VALIDATORS = VALIDATORS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  GENDER_LIST = GENDER;
  BUTTON_ACTION_CONST = BUTTON_ACTION_CONST;
  SYSTEM_RULES = SYSTEM_RULES;
  MASTER_MERCHANT_SERVICE_ENUM = MASTER_MERCHANT_SERVICE_ENUM;
  MASTER_MERCHANT_NAME_STATUS = MASTER_MERCHANT_NAME_STATUS;
  ORIGIN_CONST = ORIGIN_CONST;

  // default form search
  formMerchantHistorySearch = this.fb.group({
    // merchantName: '',
    // shortName: '',
    // MerchantCode: '',
    keyword: '',
    status: null,
  });

  @ViewChild('qrElement')
  qrCodeElement: ElementRef | any;

  constructor(
    private fb: FormBuilder,
    private formBuilder: FormBuilder,
    private merchantService: MerchantService,
    private toastService: ToastrCustomService,
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) {
    this.activatedRoute.data.subscribe((res) => {
      this.action = res.action;
    });
    // get id form paramater
    this.activatedRoute.paramMap.subscribe((res) => {
      const idParam = res.get('merchantId');
      if (idParam) {
        this.merchantDto.merchantId = +idParam;
        this.isUpdate = true;
      }
    });
  }

  /**
   * remove storage
   */
  ngOnDestroy(): void {
    if (!this.hasFilter) {
      sessionStorage.removeItem(STORAGE_APP.MERCHANT);
    }
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.hasFilter = true;
  }

  ngOnInit(): void {
    if (this.isUpdate) {
      this.getDetail();
      // this.mappingRoles(this.roles);
    } else {
      this.initForm();
    }
  }

  /**
   * init form
   *
   * @param merchant User
   */
  initForm(merchant?: IMerchant): void {
    this.formMerchant = this.formBuilder.group({
      merchantCode: [
        {
          value: merchant?.merchantCode || '',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [
          Validators.pattern(VALIDATORS.PATTERN.MERCHANT_CODE),
          Validators.minLength(VALIDATORS.LENGTH.CODE_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.MERCHANT_CODE_MAX_LENGTH),
        ],
      ],
      merchantName: [
        {
          value: merchant?.merchantName || '',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [
          Validators.required,
          Validators.maxLength(VALIDATORS.LENGTH.MERCHANT_NAME_MAX_LENGTH),
        ],
      ],
      merchantAccountNumber: [
        {
          value: merchant?.merchantAccountNumber || '',
          disabled: true,
        },
      ],
      parentId: [
        {
          value: merchant?.parentId || null,
          disabled:
            this.action === ROUTER_ACTIONS.detail ||
            merchant?.origin === ORIGIN_CONST.APP.code,
        },
        [Validators.required],
      ],
      merchantId: [merchant?.merchantId || null],
      status: [merchant?.status || ENTITY_STATUS_CONST.ACTIVE.code],
      description: [
        merchant?.description || '',
        [Validators.maxLength(VALIDATORS.LENGTH.MEDIUM_TEXT_MAX_LENGTH)],
      ],
      qrCode: [merchant?.qrCode || ''],
      // currency: [
      //   {
      //     value: merchant?.currency || '',
      //     disabled: true,
      //   },
      // ],
    });

    this.getMasterMerchant(
      null,
      this.merchant.origin,
      this.merchant.merchantAccountNumber
    );
  }
  /**
   * back to monitor list
   */
  backToList(): void {
    this.hasFilter = true;
    this.router.navigate([ROUTER_UTILS.merchant.root]);
  }

  /**
   * get detail merchant
   */
  getDetail(): void {
    if (this.merchantDto.merchantId) {
      this.merchantService.detail(this.merchantDto).subscribe((res: any) => {
        this.merchant = res.body;
        const data = res.body as IMerchant;
        if (data.qrCode?.qrCodeValue) {
          this.qrCode = data.qrCode?.qrCodeValue;
        }
        // if (this.merchant.merchantId) {
        //   this.getTransactionHistory(this.merchant.merchantId);
        // }

        this.initForm(data);
      });
    }
  }

  /**
   * index on list
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(index, this.pageIndex, this.pageSize);
  }

  onSearch(): void {}

  /**
   * load list data master Merchant
   *
   */
  getMasterMerchant(
    event?: any,
    origin?: string | number,
    merchantAccountNumber?: string
  ): void {
    let merchantNameKeyword = '';
    if (event?.target?.value) {
      merchantNameKeyword = event?.target?.value;
    }
    if (!this.isUpdate) {
      origin = ORIGIN_CONST.CMS.code;
    }
    const params = {
      serviceType: MASTER_MERCHANT_SERVICE_ENUM.OTHER,
      status: MASTER_MERCHANT_NAME_STATUS.ACTIVE,
      origin: origin || this.merchant.origin,
      merchantName: merchantNameKeyword,
      keyword: merchantAccountNumber || '',
      timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
    };

    this.merchantService.searchMaster(params).subscribe((res: any): void => {
      this.masterMerchant = res.body.content;
    });
  }

  // getTransactionHistory(merchantId: number): void {
  //   const params = {
  //     merchantId,
  //   };
  //   this.merchantService
  //     .transactionHistory(params)
  //     .subscribe((res: any): void => {
  //       this.merchantHistory = res.body.content;
  //     });
  // }

  onChangeGetMerchantAccountNumber(value: any): void {
    this.masterMerchant?.forEach((element) => {
      if (element.merchantId === value?.merchantId) {
        this.formMerchant.controls.merchantAccountNumber.setValue(
          element.merchantAccountNumber
        );
        this.formMerchant.controls.currency.setValue(element.currency);
      }
    });
  }

  getImage(event: any) {
    // console.log(event);
  }

  onChangeURL(url: SafeUrl) {
    this.qrCodeDownloadLink = url;
  }

  /**
   * export qr code merchant
   *
   * @param action BUTTON_ACTION_CONST
   */
  generatePdf(action: string): void {
    /**
     * Case 1
     */
    const docDefinition = {
      compress: false,
      watermark: {
        text: APP_NAME_CONST,
        angle: 30,
        opacity: 0.1,
        bold: true,
        italics: true,
      },
      info: {
        author: APP_NAME_CONST,
        title:
          APP_NAME_CONST +
          ' | ' +
          this.merchant.merchantName +
          ' [' +
          this.merchant.merchantCode +
          ']',
      },
      defaultStyle: {
        font: 'Roboto',
      },
      content: [
        {
          text: this.merchant.merchantName + ' - ' + this.merchant.merchantCode,
          style: 'content',
        },
        {
          qr: this.qrCode,
          foreground: '#000000',
          background: '#ffffff',
          fit: 500,
          alignment: 'center',
          margin: [0, 0, 0, 0],
          mask: 1,
          ecLevel: 'H',
        },
      ],
      styles: {
        name: {
          fontSize: 16,
          bold: true,
          alignment: 'center',
        },
        content: {
          bold: true,
          fontSize: 20,
          alignment: 'center',
          margin: [30, 0, 30, 20],
        },
      },
    };

    switch (action) {
      case BUTTON_ACTION_CONST.OPEN:
        pdfMake.createPdf(docDefinition).open();
        break;
      case BUTTON_ACTION_CONST.PRINT:
        pdfMake.createPdf(docDefinition).print();
        break;
      case BUTTON_ACTION_CONST.DOWNLOAD:
        pdfMake
          .createPdf(docDefinition)
          .download(
            `QrCode_${this.merchant.merchantName}[${
              this.merchant.merchantCode
            }]_${moment(Date.now()).format(MOMENT_CONST.TIMESTAMP_FORMAT)}.pdf`
          );
        break;
      default:
        pdfMake.createPdf(docDefinition).open();
        break;
    }

    /**
     * Case 2
     */
    // const canvasData = this.qrCodeElement?.nativeElement
    //   .querySelector('canvas')
    //   .toDataURL();
    // const win = window.open('about:blank', '_blank');
    // win?.document.open();
    // win?.document.write(
    //   [
    //     '<html>',
    //     '   <head>',
    //     '   </head>',
    //     '   <body onload="window.print()" onafterprint="window.close()">',
    //     '       <img src="' + canvasData + '"/>',
    //     '   </body>',
    //     '</html>',
    //   ].join('')
    // );
    // win?.document.close();

    /**
     * Case 3
     */
    // const canvasData = this.qrCodeElement?.nativeElement
    //   .querySelector('canvas')
    //   .toDataURL();
    // let windowContent = '<!DOCTYPE html>';
    // windowContent += '<html>';
    // windowContent += '<head><title>Print canvas</title></head>';
    // windowContent +=
    //   '<body onload="window.print()" onafterprint="window.close()">';
    // windowContent += '<img src="' + canvasData + '">';
    // windowContent += '</body>';
    // windowContent += '</html>';

    // const printWin = window.open(
    //   '',
    //   '',
    //   'width=' + screen.availWidth + ',height=' + screen.availHeight
    // );
    // printWin?.document.open();
    // printWin?.document.write(windowContent);
    // printWin?.document.close();
    // printWin?.document.addEventListener(
    //   'load',
    //   function () {
    //     printWin.focus();
    //     printWin.print();
    //     printWin.document.close();
    //     // printWin.close();
    //   },
    //   true
    // );
  }

  printDocument(documentId: any) {
    const doc = document.getElementById(documentId);
  }

  /**
   * Create: check validate file and data input if valid then call api create
   */
  onCreate() {
    // touched form
    if (this.formMerchant.invalid) {
      CommonUtils.markFormGroupTouched(this.formMerchant);
    }
    const data = this.formMerchant.getRawValue();
    //  set data files = map fileUploads
    // data.files = this.fileUploads.map((fileX) => fileX.file);
    if (this.formMerchant.valid) {
      this.merchantService.create(data).subscribe((res): void => {
        this.router.navigate([ROUTER_UTILS.merchant.root]);
        this.toastService.success('common.action.createSuccess');
      });
    }
  }

  /**
   * Update: check validate file and data input if valid then call api create
   */
  onUpdate() {
    if (this.formMerchant.invalid) {
      CommonUtils.markFormGroupTouched(this.formMerchant);
    }
    const data = this.formMerchant.getRawValue();
    //  set data files = map fileUploads
    // data.files = this.fileUploads.map((fileX) => fileX.file);
    // data.merchantId = this.merchant?.merchantId;
    if (this.formMerchant.valid) {
      this.merchantService.update(data).subscribe((res): void => {
        this.router.navigate([
          ROUTER_UTILS.merchant.root,
          this.merchantDto.merchantId,
          ROUTER_ACTIONS.detail,
        ]);
        this.toastService.success('common.action.updateSuccess');
      });
    }
  }

  onSearchKeywordMasterMerchant(term: string, item: IMerchant) {
    if (item.merchantName) {
      term = term.replace(/ + /g, ' ');
      term = term.trim();
      term = term.toLocaleLowerCase();
      return item.merchantName.toLocaleLowerCase().indexOf(term) > -1;
    }
    return '';
  }
}
