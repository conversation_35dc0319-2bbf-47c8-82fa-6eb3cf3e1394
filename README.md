# BaseProject

This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 12.2.15.

## Development server

Run DEV environment `ng serve` or `npm start` for a dev server. Navigate to `http://localhost:4200/`. The app will automatically reload if you change any of the source files.

Run QA environment `ng serve -c qa` or `npm run qa`

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Build DEV environment `ng build` to build the project. The build artifacts will be stored in the `dist/` directory.

Build QA environment `npm run build:qa`

Build PROD environment `npm run build:prod`

## Running unit tests

Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI Overview and Command Reference](https://angular.io/cli) page.
