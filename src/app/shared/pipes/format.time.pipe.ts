import { Pipe, PipeTransform } from '@angular/core';

/**
 * Slice date from 0 to 10
 *
 * <AUTHOR>
 * @date 2022-05-15
 * @export
 * @class AppDatePipe
 * @implements {PipeTransform}
 * @howToUse
 * ```
 *     <some-element>{{ user?.date | appDate }}</some-element>
 * ```
 */
@Pipe({ name: 'formatTime' })
export class FormatTimePipe implements PipeTransform {
  transform(value: number): string {
    if (value > 0) {
      const minutes: number = Math.floor(value / 60);
      return (
        ('00' + minutes).slice(-2) +
        ':' +
        ('00' + Math.floor(value - minutes * 60)).slice(-2)
      );
    }
    return '';
  }
}
