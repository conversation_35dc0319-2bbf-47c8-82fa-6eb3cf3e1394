<div class="modal-content">
  <div class="modal-header">
    <h5 class="modal-title">
      {{ "model.referral.createTitle" | translate }}
    </h5>
    <h5 class="modal-title"></h5>
    <button
      type="button"
      class="close"
      data-dismiss="modal"
      aria-label="Close"
      (click)="activeModal.close()"
    >
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="formReferral">
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label>
                {{ "model.referral.userFullName" | translate }}
                <span class="text-danger">* </span></label
              >
              <input
                trim
                placeholder="{{ 'model.referral.userFullName' | placeholder }}"
                formControlName="userFullName"
                type="text"
                class="w-100"
                class="form-control"
                [maxlength]="VALIDATORS.LENGTH.REFERRAL_FULL_NAME_MAX_LENGTH"
              />
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formReferral.get('userFullName')?.errors?.required &&
                  formReferral.get('userFullName')?.touched
                "
              >
                {{ "error.referral.required.userFullName" | translate }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formReferral.get('userFullName')?.errors?.pattern &&
                  formReferral.get('userFullName')?.touched
                "
              >
                {{ "error.referral.pattern.userFullName" | translate }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formReferral.get('userFullName')?.errors?.minlength &&
                  formReferral.get('userFullName')?.touched
                "
              >
                {{
                  "error.referral.minLength.userFullName"
                    | translate
                      : {
                          param: VALIDATORS.LENGTH.REFERRAL_FULL_NAME_MIN_LENGTH
                        }
                }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formReferral.get('userFullName')?.errors?.maxlength &&
                  formReferral.get('userFullName')?.touched
                "
              >
                {{
                  "error.referral.maxLength.userFullName"
                    | translate
                      : {
                          param: VALIDATORS.LENGTH.REFERRAL_FULL_NAME_MAX_LENGTH
                        }
                }}
              </small>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label
                >{{ "model.referral.phoneNumber" | translate }}
                <span class="text-danger">*</span></label
              >
              <input
                trim
                placeholder="{{ 'model.referral.phoneNumber' | placeholder }}"
                formControlName="phoneNumber"
                type="text"
                class="w-100"
                class="form-control"
                [maxlength]="VALIDATORS.LENGTH.REFERRAL_PHONE_NUMBER_MAX_LENGTH"
              />
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formReferral.get('phoneNumber')?.errors?.required &&
                  formReferral.get('phoneNumber')?.touched
                "
              >
                {{ "error.referral.required.phoneNumber" | translate }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formReferral.get('phoneNumber')?.errors?.pattern &&
                  formReferral.get('phoneNumber')?.touched
                "
              >
                {{ "error.referral.pattern.phoneNumber" | translate }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formReferral.get('phoneNumber')?.errors?.minlength &&
                  formReferral.get('phoneNumber')?.touched
                "
              >
                {{
                  "error.referral.minLength.phoneNumber"
                    | translate
                      : {
                          param: VALIDATORS.LENGTH.PHONE_NUMBER_MIN_LENGTH
                        }
                }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formReferral.get('phoneNumber')?.errors?.maxlength &&
                  formReferral.get('phoneNumber')?.touched
                "
              >
                {{
                  "error.referral.maxLength.phoneNumber"
                    | translate
                      : {
                          param: VALIDATORS.LENGTH.PHONE_NUMBER_MAX_LENGTH
                        }
                }}
              </small>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label>
                {{ "model.referral.type" | translate }}
                <span class="text-danger">*</span></label
              >
              <ng-select
                appearance="outline"
                [searchable]="false"
                placeholder="{{
                  'model.referral.type' | placeholder : 'select'
                }}"
                [clearable]="true"
                formControlName="type"
              >
                <ng-option [value]="item.code" *ngFor="let item of RM_TYPE">
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formReferral.get('type')?.errors?.required &&
                  formReferral.get('type')?.touched
                "
              >
                {{ "error.referral.required.type" | translate }}
              </small>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label>{{ "model.referral.userCode" | translate }} </label>
              <input
                trim
                placeholder="{{ 'model.referral.userCode' | placeholder }}"
                formControlName="userCode"
                type="text"
                class="w-100"
                class="form-control"
                [maxLength]="VALIDATORS.LENGTH.REFERRAL_STAFF_CODE_MAX_LENGTH"
              />
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formReferral.get('userCode')?.errors?.minlength &&
                  formReferral.get('userCode')?.touched
                "
              >
                {{
                  "error.referral.minLength.userCode"
                    | translate
                      : {
                          param:
                            VALIDATORS.LENGTH.REFERRAL_STAFF_CODE_MIN_LENGTH
                        }
                }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formReferral.get('userCode')?.errors?.maxlength &&
                  formReferral.get('userCode')?.touched
                "
              >
                {{
                  "error.referral.maxLength.userCode"
                    | translate
                      : {
                          param:
                            VALIDATORS.LENGTH.REFERRAL_STAFF_CODE_MAX_LENGTH
                        }
                }}
              </small>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-12">
            <div class="form-group">
              <label>{{ "model.referral.rmCode" | translate }} </label>
              <input
                trim
                placeholder="{{ 'model.referral.rmCode' | placeholder }}"
                formControlName="rmCode"
                type="text"
                class="w-100"
                class="form-control"
                [maxLength]="VALIDATORS.LENGTH.REFERRAL_RM_CODE_MAX_LENGTH"
              />
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formReferral.get('rmCode')?.errors?.maxlength &&
                  formReferral.get('rmCode')?.touched
                "
              >
                {{
                  "error.referral.maxLength.rmCode"
                    | translate
                      : {
                          param: VALIDATORS.LENGTH.REFERRAL_RM_CODE_MAX_LENGTH
                        }
                }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formReferral.get('rmCode')?.errors?.minlength &&
                  formReferral.get('rmCode')?.touched
                "
              >
                {{
                  "error.referral.minLength.rmCode"
                    | translate
                      : {
                          param: VALIDATORS.LENGTH.REFERRAL_RM_CODE_MIN_LENGTH
                        }
                }}
              </small>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="text-center pb-4">
    <button
      type="button"
      class="btn mb-btn-outline-color mr-3"
      (click)="activeModal.close()"
    >
      {{ "common.action.close" | translate }}
    </button>
    <ng-container *hasPrivileges="SYSTEM_RULES.REFERRAL_CREATE">
      <button type="button" class="btn mb-btn-color" (click)="onCreate()">
        {{ "common.action.create" | translate }}
      </button>
    </ng-container>
  </div>
</div>
