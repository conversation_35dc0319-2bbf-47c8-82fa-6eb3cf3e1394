.currentcy {
  width: 120px;
  height: 100% !important;
  display: flex;
  justify-content: flex-end;
  padding-right: 10px;
}
.form-container-money {
  margin-bottom: 0 !important;
}
.form-control-money {
  flex: 1;
}
.table > thead > tr > th {
  border: 1px solid #cecece;
}
.table > tbody > tr > td {
  border: 1px solid #cecece;
}
.header-create {
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 10px;
}
.header-create > img {
  height: 25px;
  width: 28px;
  position: relative;
  top: 4px;
  cursor: pointer;
}
.header-create > h3 {
  margin: 0;
}
.table-create > h4 {
  margin: 0;
  font-weight: 600;
}
.table {
  width: 900px;
}
.max-transfer {
  text-align: right;
}
.sector-header {
  font-size: 14px;
  font-weight: 600;
}
.sector-error {
  font-size: 12px;
}
.label-maxmoney {
  margin-left: 10px !important;
}
