import { SearchWithPagination } from '@shared/models/request/base.request.model';

export interface IRoleSearch extends SearchWithPagination {
  name?: string;
  status?: number;
}

export class RoleSearch implements IRoleSearch {
  constructor(
    public name?: string,
    public status?: number,
    public orderByType?: string,
    public orderByColumn?: string,
    public page?: number,
    public size?: number
  ) {
    this.name = name;
    this.status = status;
    this.orderByType = orderByType;
    this.orderByColumn = orderByColumn;
    this.page = page;
    this.size = size;
  }
}
