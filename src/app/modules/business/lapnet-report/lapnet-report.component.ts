import { Component, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  CALCULATE_MILLISECONDS_IN_A_YEAR,
  DATE_RANGE_STATISTICAL_LOAN_ONLINE,
  ENTITY_STATUS,
  ENTITY_STATUS_MAP,
  FILE_EXTENSION,
  MODAL_ACTION,
  MOMENT_CONST,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ILapnetReport } from '@shared/models/lapnet-report.model';
import { ITransactionSearch } from '@shared/models/request/transaction.search';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { LapnetService } from '@shared/services/lapnet.service';
import { ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';
import { ConFigAutoReportLapNetComponent } from './config-auto-report/config-auto-report-lapnet.component';
import { AsyncDateLapnetReport } from './asyn-date-report/async-date-report.component';

@Component({
  selector: 'app-lapnet-report',
  templateUrl: './lapnet-report.component.html',
  styleUrls: ['./lapnet-report.component.scss'],
})
export class LapnetReportComponent implements OnInit {
  // lapnetReport: ILoanOnline[] = [];
  lapnetReport: ILapnetReport[] = [];
  action: any = '';
  storage: any;
  maxDate = new Date();
  maxToDate = new Date();

  ROUTER_UTILS = ROUTER_UTILS;
  // input search
  pageSizeOptions = PAGINATION.OPTIONS;
  ENTITY_STATUS = ENTITY_STATUS;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  SYSTEM_RULES = SYSTEM_RULES;
  VALIDATORS = VALIDATORS;
  MOMENT_CONST = MOMENT_CONST;

  isValidMaxDate = (control: FormControl) => {
    const dateOfBirth = new Date(control.value).getTime();
    const maxDateOfBirth = new Date().getTime();
    return dateOfBirth > maxDateOfBirth ? { invalidMaxDate: true } : null;
  };

  // default form search
  formLapnetReportSearch = this.fb.group({
    keyword: '',
    status: null,
    fromDate: [null, [Validators.required]],
    toDate: [null, [Validators.required, this.isValidMaxDate]],
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    hasPageable: true,
    // previousPageIndex: 0,
  });

  constructor(
    private lapnetService: LapnetService,
    private fb: FormBuilder,
    private downloadService: DownloadService,
    private translateService: TranslateService,
    private modalService: ModalService,
    private toastService: ToastrCustomService,
    private modal: NgbModal
  ) {
    // get session storage
    this.storage = sessionStorage.getItem(STORAGE_APP.LAPNET_REPORT);
  }

  /**
   * Init data
   */
  ngOnInit(): void {
    // check storage
    // (when click back button or back website)
    if (this.storage) {
      // get session storage and parse json
      const filter = JSON.parse(this.storage) as ITransactionSearch;
      // set value form control
      this.formLapnetReportSearch.controls.keyword.setValue(filter.keyword);
      this.formLapnetReportSearch.controls.fromDate.setValue(filter.fromDate);
      this.formLapnetReportSearch.controls.toDate.setValue(filter.toDate);
      this.onSearch();
    } else {
      // set default value start date and end date
      const startDate = moment()
        .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
        .format(MOMENT_CONST.FORMAT_DEFAULT);
      const endDate = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
      // this.loanOnlineSearch.fromDate = startDate;
      // this.loanOnlineSearch.toDate = endDate;
      this.formLapnetReportSearch.controls.fromDate.setValue(
        CommonUtils.reverseDate(startDate)
      );
      this.formLapnetReportSearch.controls.toDate.setValue(
        CommonUtils.reverseDate(endDate)
      );
      this.onSearch();
    }
    // click form clear storage
    sessionStorage.removeItem(STORAGE_APP.LAPNET_REPORT);
  }

  /**
   * export file
   */
  exportFile(): void {
    // get value form control
    const bodySearch = this.formLapnetReportSearch.getRawValue();
    if (this.formLapnetReportSearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formLapnetReportSearch);
    } else {
      // get file name
      const fileName = this.translateService.instant('template.lapnetReport');
      const obFile = this.lapnetService.export({
        fromDate: bodySearch.fromDate,
        toDate: bodySearch.toDate,
        keyword: bodySearch.keyword,
      });

      this.downloadService.downloadFileWithObservableAndName(
        obFile,
        fileName,
        FILE_EXTENSION.XLSX
      );
    }
  }

  /**
   * Change page: next page and preview page
   *
   * @param page PageEvent
   */
  onChangePage(page: PageEvent) {
    this.formLapnetReportSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formLapnetReportSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  dateValidator(value: number): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return value > DATE_RANGE_STATISTICAL_LOAN_ONLINE
        ? { invalidDate: true }
        : null;
    };
  }

  changeValidDate() {
    if (
      this.formLapnetReportSearch.controls.fromDate.value &&
      this.formLapnetReportSearch.controls.toDate.value
    ) {
      if (this.formLapnetReportSearch.controls['toDate'].value) {
        this.maxDate = this.formLapnetReportSearch.controls['toDate'].value;
      }
      const startDate = CommonUtils.setStartTime(
        new Date(this.formLapnetReportSearch.controls.fromDate.value).getTime()
      );
      const endDate = CommonUtils.setStartTime(
        new Date(this.formLapnetReportSearch.controls.toDate.value).getTime()
      );
      const dayMax =
        (endDate - startDate) / CALCULATE_MILLISECONDS_IN_A_YEAR.DAY;

      if (dayMax > DATE_RANGE_STATISTICAL_LOAN_ONLINE) {
        this.formLapnetReportSearch.controls.fromDate.setValidators([
          Validators.required,
          this.dateValidator(dayMax),
          this.isValidMaxDate,
        ]);
        this.formLapnetReportSearch.controls.fromDate.updateValueAndValidity();
      } else {
        this.formLapnetReportSearch.controls.fromDate.clearValidators();
        this.formLapnetReportSearch.controls.fromDate.setValidators([
          Validators.required,
          this.isValidMaxDate,
        ]);
        this.formLapnetReportSearch.controls.fromDate.updateValueAndValidity();
      }
    }
  }

  onSearchSubmit() {
    this.formLapnetReportSearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.formLapnetReportSearch.controls.length.setValue(
      PAGINATION.LENGTH_DEFAULT
    );
    this.onSearch();
  }

  /**
   * search form
   */
  onSearch(): void {
    if (this.formLapnetReportSearch.valid) {
      this.formLapnetReportSearch.controls.hasPageable.setValue(true);
      this.lapnetService
        .search(this.formLapnetReportSearch.value)
        .subscribe((res: any): void => {
          this.lapnetReport = res.body.content;
          this.lapnetReport.forEach((element) => {
            element.time = moment(
              CommonUtils.reverseDateTimeZone(element.time)
            ).format(MOMENT_CONST.LOCAL_DATE_TIME_FORMAT);
          });
          this.formLapnetReportSearch.controls.length.setValue(
            res.body.totalElements
          );
        });
    }
  }

  /**
   * index on list loan online
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formLapnetReportSearch.value.pageIndex,
      this.formLapnetReportSearch.value.pageSize
    );
  }

  // checkValidateDate() {
  //   this.maxDate = this.formLapnetReportSearch.controls['toDate'].value;
  // }

  /**
   * reset form
   */
  onReset(): void {
    // set default value date and reset data
    const startDateReset = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDateReset = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.formLapnetReportSearch.controls.keyword.reset();
    this.formLapnetReportSearch.controls.fromDate.setValue(
      CommonUtils.reverseDate(startDateReset)
    );
    this.formLapnetReportSearch.controls.toDate.setValue(
      CommonUtils.reverseDate(endDateReset)
    );
    this.formLapnetReportSearch.controls.fromDate.clearValidators();
    this.formLapnetReportSearch.controls.fromDate.setValidators([
      Validators.required,
    ]);
    this.formLapnetReportSearch.controls.fromDate.updateValueAndValidity();
  }

  totalAmount(value = 0, fee = 0): number {
    return +value + +fee;
  }

  onConfigAutoReportLapNet() {
    const modalRef = this.modal.open(ConFigAutoReportLapNetComponent, {
      size: 'lg',
      keyboard: true,
      backdrop: 'static',
      centered: true,
    });
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;
    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }

  onAsyncDateReport() {
      const modalRef = this.modal.open(AsyncDateLapnetReport, {
        size: '70%',
        keyboard: true,
        backdrop: 'static',
        centered: true,
      });
      modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;
      modalRef.result.then((result) => {
        if (result === MODAL_ACTION.CONFIRM.code) {
          this.onSearch();
        }
      });
    }
}
