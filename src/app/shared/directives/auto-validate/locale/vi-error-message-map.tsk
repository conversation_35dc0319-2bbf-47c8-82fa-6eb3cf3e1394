// import { ErrorMessageMap } from '@shared/directive/auto-validate/error-message-map-token';

// export const ViErrorMessageMap: ErrorMessageMap = {
//   required: 'Required',
//   max: (err: any) => `Max: ${err.max}`,
//   min: (err: any) => `Min: ${err.min}`,
//   maxlength: (err: any) => `Max Length: ${err.actualLength}/${err.requiredLength}`,
//   minlength: (err: any) => `Min Length: ${err.actualLength}/${err.requiredLength}`,
//   email: 'Invalid Email Format',
//   pattern: (err: any) => `Invalid Pattern: ${err.requiredPattern}`,
// };
