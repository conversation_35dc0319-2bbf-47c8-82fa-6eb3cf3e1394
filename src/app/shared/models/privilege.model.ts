export interface IPrivilege {
  privilegeId?: number;
  name?: string;
  groupId?: number;
  description?: string;
  checked?: boolean;
}

// export class Privilege {
//   constructor(
//     public id?: number,
//     public name?: string,
//     public description?: string
//   ) {
//     this.id = id ?? null;
//     this.name = name ?? null;
//     this.description = description ?? null;
//   }
// }
