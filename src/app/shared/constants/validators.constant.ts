export const VALIDATORS = {
  PATTERN: {
    BLANK: '(\\s){0,}\\S+.*(\\s){0,}',
    USERNAME: '^[a-zA-Z0-9]([._](?![._])|[a-zA-Z0-9]){1,72}[a-zA-Z0-9]$',
    ID: '^[a-zA-Z0-9]+$',
    DATE: /^(?:(?:31(\/|-|\.)(?:0?[13578]|1[02]))\1|(?:(?:29|30)(\/|-|\.)(?:0?[13-9]|1[0-2])\2))(?:(?:1[6-9]|[2-9]\d)?\d{2})$|^(?:29(\/|-|\.)0?2\3(?:(?:(?:1[6-9]|[2-9]\d)?(?:0[48]|[2468][048]|[13579][26])|(?:(?:16|[2468][048]|[3579][26])00))))$|^(?:0?[1-9]|1\d|2[0-8])(\/|-|\.)(?:(?:0?[1-9])|(?:1[0-2]))\4(?:(?:1[6-9]|[2-9]\d)?\d{2})$/,
    EMAIL:
      '^(\\s){0,}[a-zA-Z][a-zA-Z0-9_\\.]{2,32}@[a-zA-Z0-9]{2,}(\\.[a-zA-Z0-9]{2,4}){1,2}(\\s){0,}$',
    EMAIL_REPORT:
      '^(?=.{1,64}@)[A-Za-z0-9+_-]+(.[A-Za-z0-9+_-]+)*@[^-][A-Za-z0-9+-]+(.[A-Za-z0-9+-]+)*(.[A-Za-z]{2,35})$',
    EMAIL_MB:
      '^(\\s){0,}[a-zA-Z][a-zA-Z0-9_\\.]{2,37}(@mbbank.com.vn|@evotek.vn)',
    PHONE: /^((\\s){0,}(0|856)(((30)[0-9]{7})|((20)[0-9]{8}))(\\s){0,})$/,
    PHONE_MB:
      '^((\\s){0,}(020))((2|5|7|8|9)[0-9]{7}(\\s){0,})$|^((\\s){0,}(030))((2|4|5|7|8|9)[0-9]{6}(\\s){0,})$',
    COORDINATE: /^(-?\d+(\.\d+)?),\s*(-?\d+(\.\d+)?)$/,
    PASSWORD: /^(?!.*[\s])(?=.*[a-z])(?=.*[A-Z]).{8,20}$/,
    // FULLNAME: /^[A-Za-z\s]*$/,
    FULLNAME_LAO: /^[\s, \u0e80-\u0eff]*$/,
    FULLNAME: /^[ a-zA-Z]*$/,
    // DEPARTMENT_SHORT_NAME: '^(\\s){0,}[a-zA-Z0-9]{0,100}(\\s){0,}$',
    DEPARTMENT_SHORT_NAME: '^[_a-zA-Z0-9]*$',
    POSITION_SHORT_NAME: '^[_a-zA-Z0-9]*$',
    // CODE: /^[A-Z]/,
    MERCHANT_NAME: /^[ a-zA-Z]*$/,
    NUMBER: /^[0-9]*$/,
    MERCHANT_NUMBER: /^[a-zA-Z0-9]{4,50}$/,
    MERCHANT_CODE: '^[a-zA-Z0-9]([._](?![._])|[a-zA-Z0-9]){1,72}[a-zA-Z0-9]$',
    MERCHANT_CODE_NAME: /^[a-zA-Z0-9]*$/,
    PHONE_SEARCH: /^((\s){0,}\d(\s){0,})*$/,
    CIF_SEARCH: /^((\s){0,}[a-zA-Z0-9](\s){0,})*$/,
    ID_CARD_NUMBER_SEARCH: /^((\s){0,}[a-zA-Z0-9](\s){0,})*$/,
    // PATTERN_NAME:
    //   /^[a-zA-Z0-9ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂẾưăạảấầẩẫậắằẳẵặẹẻẽềềểếỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳýỵỷỹ\s]+$/,
    PATTERN_NAME: /^[\w\s]*$/,
    ESCAPE_HTML: /<[^>]*>/g,
    BANK_CODE: '^[a-zA-Z0-9]*$',
    PHONE_NUMBER_START_ZERO: /^((\\s){0,}(0)((([0-9])[0-9]*))(\\s){0,})$/,
    LOCALE_DATE: /^\d{4}-\d{2}-\d{2}$/,
    LOCALE_DATE_TIME: /^\d{2}-\d{2}-\d{4}$/,
    MATCH_DATE_TIME: /^\d{2}\-\d{2}\-\d{4}\ \d{2}\:\d{2}\:\d{2}$/,
    MATCH_DATE_TIME_REVERSE: /^\d{4}\-\d{2}\-\d{2}\ \d{2}\:\d{2}\:\d{2}$/,
    MATCH_DATE_TIME_ZONE:
      /^\d{4}\-\d{2}\-\d{2}T\d{2}\:\d{2}\:\d{2}(.?)\d{0,}Z$/,
    SPACE_HTML: /&nbsp;/g,
    VERSION_NAME: /^[0-9]+(\.[0-9]+){0,}$/,
    VERSION_CODE: /^[0-9]*$/,

    CAMPAIGN_NAME: /^[^-=+@].*/,
    CAMPAIGN_NAME_ASCII: /^((?!0x0[9Dd])).*/,

    HTTP_PATTERN:
      '^https?:\\/\\/(?:www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b(?:[-a-zA-Z0-9()@:%_\\+.~#?&\\/=]*)$',

    NAME_NO_SPECIAL: /^[^-=+@].*/,
    NAME_NO_ASCII: /^((?!0x0[9Dd])).*/,
    PREMIUM_ACCOUNT_NUMBER: /^[0-9]{4,9}$/,
    SPECIAL_ACCOUNT_NUMBER:
      /^\d{4,5}$|^\d{9,10}$|^((\s){0,}(020))((2|5|7|8|9)[0-9]{7}(\s){0,})$|^((\s){0,}(030))((2|4|5|7|8|9)[0-9]{6}(\s){0,})$/,
    DISCOUNT:
      /^(?:100(?:\.0{1,2})?|[1-9]?[0-9](?:\.[0-9]{1,2})?|0(?:\.[0-9]{1,2})?)$/,
    NUMBER_AND_DECIMAL: /^\d*\.?\d*$/,
    PHONE_NUMBER_CUSTOMER_SUPPORT_REGEX: /^[0-9]{8,30}$/,
    NO_SPECIAL_CHARACTERS: /^[^!@#$%~+=;:,.|?<>[{}_^]*$/,
    DECIMAL: /^[+-]?(\d*\.\d+|\d+\.\d*)$/,
    EXPECT_STRING: /^[1-9]\d{3,4}$|^[1-9]\d{8,10}$/,
  },
  LENGTH: {
    TRANSACTION_REPORT_MIN: 5,
    TRANSACTION_REPORT_MAX: 5,
    CASHOUT_ACCOUNT_NUMBER_MAX_LENGTH: 20,
    PHONE_NUMBER_MIN_LENGTH: 10,
    PHONE_NUMBER_MAX_LENGTH: 13,
    FULL_NAME_MIN_LENGTH: 3,
    IDENTIFICATION_MIN_LENGTH: 8,
    IDENTIFICATION_MAX_LENGTH: 15,
    USERNAME_MIN_LENGTH: 3,
    USERNAME_MAX_LENGTH: 72,
    PASSWORD_MIN_LENGTH: 8,
    PASSWORD_MAX_LENGTH: 20,
    EMAIL_MIN_LENGTH: 4,
    EMAIL_MAX_LENGTH: 65,
    EMAIL_USER_MAX_LENGTH: 50,
    NAME_MAX_LENGTH: 255,
    CODE_MIN_LENGTH: 2,
    CODE_MAX_LENGTH: 50,
    ADDRESS_MIN_LENGTH: 2,
    ADDRESS_MAX_LENGTH: 255,
    DESCRIPTION_MIN_LENGTH: 2,
    DESCRIPTION_MAX_LENGTH: 255,
    PLACE_OF_ORIGIN_MIN_LENGTH: 2,
    PLACE_OF_ORIGIN_MAX_LENGTH: 50,
    CIF_MIN_LENGTH: 2,
    CIF_MAX_LENGTH: 12,
    URL_MAX_LENGTH: 255,
    MINI_SMALL_TEXT_MAX_LENGTH: 20,
    TEXT_MAX_LENGTH: 50,
    MINI_TEXT_MAX_LENGTH: 100,
    SMALL_TEXT_MAX_LENGTH: 255,
    MEDIUM_TEXT_MAX_LENGTH: 500,
    LONG_TEXT_MAX_LENGTH: 1000,
    SUPER_TEXT_MAX_LENGTH: 4000,
    INT_NUMBER_MAX_LENGTH: 10,
    MAX_FILE_UPLOAD: 5,
    MAX_ORDER_NUMBER: 99,
    COMPANY_ID_MAX_LENGTH: 65,
    JOB_MAX_LENGTH: 65,
    POSITION_MAX_LENGTH: 65,
    STAFF_CODE_MIN_LENGTH: 10,
    STAFF_CODE_MAX_LENGTH: 13,
    BANK_CODE_NUMBER_MIN_LENGTH: 2,
    BANK_CODE_NUMBER_MAX_LENGTH: 10,
    BANK_CODE_MIN_LENGTH: 2,
    BANK_CODE_MAX_LENGTH: 10,
    BANK_NAME_MIN_LENGTH: 2,
    POSITION_NAME_MIN_LENGTHG: 1,
    POSITION_NAME_MAX_LENGTH: 200,
    POSITION_SHORT_NAME_MIN_LENGTH: 2,
    POSITION_CODE_MAX_LENGTH: 50,
    DEPARTMENT_NAME_MAX_LENGTH: 200,
    DEPARTMENT_NAME_MIN_LENGTH: 1,
    DEPARTMENT_CODE_MAX_LENGTH: 50,
    DEPARTMENT_CODE_MIN_LENGTH: 1,
    DEPARTMENT_DESCRIPTION_MAX_LENGTH: 500,
    CONTENT_NOTIFICATION: 500,
    MERCHANT_CODE_MAX_LENGTH: 20,
    MASTER_MERCHANT_CODE_MAX_LENGTH: 50,
    MERCHANT_NAME_MAX_LENGTH: 50,
    ACCOUNT_NUMBER_MAX_LENGTH: 50,
    ACCOUNT_NUMBER_MERCHANT_MAX_LENGTH: 50,
    FEE_RATE_NAME_MIN_LENGTH: 1,
    TRANSACTION_AMOUNT_MIN_LENGTH: ***************,
    TRANSACTION_AMOUNT_MAX_LENGTH: ***************,
    VAT_MAX_LENGTH: 2,
    INFORMATION_TEMPLATE_CODE_MAX_LENGTH: 50,
    INFORMATION_TEMPLATE_DISPLAY_NAME_MAX_LENGTH: 15,
    INFORMATION_TEMPLATE_MAX_LENGTH: ***************,
    PERCENT: 100,
    DESCRIPTION_ROLE_MAX_LENGTH: 150,
    NAME_ROLE_MAX_LENGTH: 50,
    CAMPAIGN_NAME_MAX_LENGTH: 50,
    CAMPAIGN_DESCRIPTION_MAX_LENGTH: 250,
    CAMPAIGN_EMBED_LINK_MAX_LENGTH: 500,
    BANK_ORDER_MAX: 99,
    BANK_ORDER_MIN: 1,
    BANK_ORDER_MAX_LENGTH: 2,
    LOAN_ONLINE_DATE_MAX: 30,
    TRANSACTION_AMOUNT_MIN: 1,
    REFERRAL_MAX_LENGTH: 200,
    REFERRAL_FULL_NAME_MIN_LENGTH: 5,
    REFERRAL_FULL_NAME_MAX_LENGTH: 100,
    REFERRAL_PHONE_NUMBER_MIN_LENGTH: 5,
    REFERRAL_PHONE_NUMBER_MAX_LENGTH: 13,
    REFERRAL_STAFF_CODE_MIN_LENGTH: 5,
    REFERRAL_STAFF_CODE_MAX_LENGTH: 50,
    REFERRAL_RM_CODE_MIN_LENGTH: 5,
    REFERRAL_RM_CODE_MAX_LENGTH: 50,
    VERSION_NAME_LENGTH: 10,
    VERSION_CODE_LENGTH: 10,
    VERSION_CONTENT: 500,
    CONTENT_NEWS: 5000,
    CONTENT_NOTIFICATION_MAX: 1500,

    MONEY_LENGTH_1890: 11,
    MONEY_LENGTH_1891: 10,
    MONEY_MONTH_LENGTH_1891: 10,
    MONEY_MAX_LENGTH: 17,
    PACKAGE_NAME_LENGTH: 50,
    ACCOUNT_NAME_LENGTH: 65,
    TRANSACTION_LIMIT_MAX_LENGTH: 17,
    NUMBER_MONEY_MAX_LENGTH: 13,
    NATION_CODE_OTHER_MAX_LENGTH: 5,
    PREMIUM_ACCOUNT_NUMBER_MAX_LENGTH: 9,
    PHONE_NUMBER_CUSTOMER_SUPPORT_MAX_LENGTH: 30,
    KEYWORD_MAX_LENGTH: 100,
    NAME_NUMBER_GROUP_MIN_LENGTH: 4,
    DESCRIPTION_NUMBER_GROUP_MIN_LENGTH: 10,
    NUMBER_MAX_LENGTH: 18,
    NUMBER_MAX_AMOUNT: **************,
    NUMBER_STRUCTURE: 75,
    CURRENCY_MIN_LENGTH: 2,
    AMOUNT_DECIMAL_MAX_LENGTH: 18,
    CURRENCY_VALUE_MIN_LENGTH: 3,
    CURRENCY_VALUE_MAX_LENGTH: 3,
    PREMIUM_ACC_NUMBER_MAX_LENGTH: 10,
    OTP_MAX_LENGTH: 8,
    INTERVAL_MINUTE_MAX_LENGTH: 6,
  },
};
