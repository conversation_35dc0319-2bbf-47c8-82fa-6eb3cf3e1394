// ::ng-deep .mat-form-field-appearance-fill .mat-form-field-flex {
//   background-color: white;
//   padding: 0px !important;
//   border-radius: 0px !important;
//   border: 1.5px solid #CECECE;
// }

// ::ng-deep .mat-form-field-underline {
//   display: none;
// }

// .mat-form-field-infix {
//   padding: 3px !important;
//   border-top: 5px solid transparent !important;
// }

// ::ng-deep .mat-form-field-infix {
//   padding: 3px !important;
//   border-top: 5px solid transparent !important;
// }

// .mat-form-field {
//   display: block;
// }

// ::ng-deep .mat-select-panel .mat-option {
//   border-bottom: 1px solid #CECECE;
// }

// ::ng-deep .mat-select-arrow-wrapper {
//   padding-top: 11px;
// }

/////////// close
// ::ng-deep  .mat-select-panel .mat-option.mat-selected:not(.mat-option-multiple),::ng-deep .mat-option.mat-active  {
//     background: #EB2D4B!important;
//     border-radius: 4px!important;
//     color: white;
// }
