import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  FILE_EXTENSION,
  MODAL_ACTION,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { IMerchant } from '@shared/models/merchant.model';
import { IMerchantSearch } from '@shared/models/request/merchant.search';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { MerchantService } from '@shared/services/merchant.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-merchant-management',
  templateUrl: './merchant-management.component.html',
  styleUrls: ['./merchant-management.component.scss'],
})
export class MerchantManagementComponent implements OnInit {
  merchants: IMerchant[] = [];
  recordSelected: any = [];
  storage: any;
  action: any = '';
  maxDate = new Date();
  maxToDate = new Date();
  isSync = false;

  ROUTER_UTILS = ROUTER_UTILS;
  pageSizeOptions = PAGINATION.OPTIONS;
  ENTITY_STATUS = ENTITY_STATUS;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  SYSTEM_RULES = SYSTEM_RULES;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;

  // default form search
  formMerchantSearch = this.fb.group({
    // merchantName: '',
    // shortName: '',
    // MerchantCode: '',
    keyword: '',
    status: null,
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    fromDate: [null],
    toDate: [null],
    timeZoneStr: '',
    // previousPageIndex: 0,
  });

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private modalService: ModalService,
    private toastService: ToastrCustomService,
    private merchantService: MerchantService,
    private translateService: TranslateService,
    private downloadService: DownloadService
  ) {
    // get session storage
    this.storage = sessionStorage.getItem(STORAGE_APP.MERCHANT);
  }

  /**
   * Init data
   */
  ngOnInit(): void {
    // check storage
    // (when click back button or back website)
    this.formMerchantSearch
      .get('timeZoneStr')
      ?.setValue(Intl.DateTimeFormat().resolvedOptions().timeZone);
    if (this.storage) {
      // get session storage and parse json
      const filter = JSON.parse(this.storage) as IMerchantSearch;
      // set value form control
      if (filter.status) {
        const status = +filter.status;
        this.formMerchantSearch.controls.status.setValue(status);
      }
      this.formMerchantSearch.controls.keyword.setValue(filter.keyword);
      // this.formMerchantSearch.controls.merchantName.setValue(
      //   filter.merchantName
      // );
      // this.formMerchantSearch.controls.shortName.setValue(filter.shortName);
      // this.formMerchantSearch.controls.MerchantCode.setValue(
      //   filter.MerchantCode
      // );
      this.onSearch();
    } else {
      // set default value start date and end date
      this.onSearch();
    }
    // click form clear storage
    sessionStorage.removeItem(STORAGE_APP.MERCHANT);
  }

  // /**
  //  * load list data Loan Online
  //  *
  //  * @param page PageEvent
  //  */
  // loadData(page: PageEvent): void {
  //   this.merchantSearch.pageIndex = page.pageIndex;
  //   this.merchantSearch.pageSize = page.pageSize;
  //   this.merchantService
  //     .search(this.merchantSearch)
  //     .subscribe((res: any): void => {
  //       this.merchants = res.body.content;
  //       this.totalRecord = res.body.totalElements;
  //     });
  // }

  /**
   * Change page: next page and preview page
   *
   * @param page PageEvent
   */
  onChangePage(page: PageEvent) {
    this.formMerchantSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formMerchantSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  onSearchSubmit() {
    this.formMerchantSearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.formMerchantSearch.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    this.onSearch();
  }

  /**
   * search form
   */
  onSearch(): void {
    this.merchantService
      .search(this.formMerchantSearch.value)
      .subscribe((res: any): void => {
        this.merchants = res.body.content;
        this.formMerchantSearch.controls.length.setValue(
          res.body.totalElements
        );

        this.merchants.some((item) => {
          if (String(item.merchantCode).length > 15) {
            this.isSync = true;
            return;
          }
        });
        // this.formMerchantSearch.controls.pageIndex.setValue(
        //   PAGINATION.PAGE_NUMBER_DEFAULT
        // );
        // this.formMerchantSearch.controls.pageSize.setValue(res.body.pageSize);
      });
  }

  /**
   * index on list loan online
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formMerchantSearch.value.pageIndex,
      this.formMerchantSearch.value.pageSize
    );
  }

  /**
   * button click detail
   *
   * @param merchantId number
   */
  detail(merchantId?: number): void {
    sessionStorage.setItem(
      STORAGE_APP.MERCHANT,
      JSON.stringify(this.formMerchantSearch.value)
    );
    this.router.navigate([
      ROUTER_UTILS.merchant.root,
      merchantId,
      ROUTER_ACTIONS.detail,
    ]);
  }

  /**
   * button click edit
   *
   * @param merchantId number
   */
  edit(merchantId?: number): void {
    sessionStorage.setItem(
      STORAGE_APP.MERCHANT,
      JSON.stringify(this.formMerchantSearch.value)
    );
    this.router.navigate([
      ROUTER_UTILS.merchant.root,
      merchantId,
      ROUTER_ACTIONS.update,
    ]);
  }

  /**
   * button click create
   *
   * @param merchantId number
   */
  create(): void {
    sessionStorage.setItem(
      STORAGE_APP.MERCHANT,
      JSON.stringify(this.formMerchantSearch.value)
    );
    this.router.navigate([ROUTER_UTILS.merchant.root, ROUTER_ACTIONS.create]);
  }

  /**
   * reset form
   */
  onReset(): void {
    // set default value date and reset data
    this.formMerchantSearch.controls.keyword.reset();
    this.formMerchantSearch.controls.status.reset();
    this.formMerchantSearch.controls.fromDate.reset();
    this.formMerchantSearch.controls.toDate.reset();
    this.formMerchantSearch.controls.toDate.reset();
    this.maxDate = new Date();
    this.maxToDate = new Date();
  }

  /**
   * check lock and unlock and call api
   *
   * @param merchant IMerchant
   */
  lockAndUnlock(merchant: IMerchant): void {
    if (merchant.status === ENTITY_STATUS_CONST.INACTIVE.code) {
      this.unLockMerchant(merchant);
    } else {
      this.lockMerchant(merchant);
    }
  }

  /**
   * Lock merchant register
   *
   * @param merchant IMerchant
   */
  private lockMerchant(merchant: IMerchant) {
    const modalData = {
      title: 'merchant.lock',
      content: 'merchant.lockMerchantContent',
      interpolateParams: {
        merchantName: `<b>${merchant?.merchantName || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { merchantId: merchant?.merchantId };
        this.merchantService.lock(params).subscribe((res) => {
          this.toastService.success('common.action.lockSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * UnLock merchant register
   *
   * @param merchant: IMerchant
   */
  private unLockMerchant(merchant: IMerchant) {
    const modalData = {
      title: 'merchant.unlock',
      content: 'merchant.unlockMerchantContent',
      interpolateParams: {
        merchantName: `<b>${merchant?.merchantName || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { merchantId: merchant?.merchantId };
        this.merchantService.unlock(params).subscribe((res) => {
          this.toastService.success('common.action.unlockSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * delete
   *
   * @param merchant IMerchant
   */
  delete(merchant: IMerchant): void {
    // open modal
    const modalData = {
      title: 'merchant.delete',
      content: 'merchant.deleteMerchantContent',
      interpolateParams: {
        merchantName: `<b>${merchant?.merchantName || ''}</b>`,
      },
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { merchantId: merchant?.merchantId };
        this.merchantService.deleteMerchant(params).subscribe((res: any) => {
          this.toastService.success('common.action.deleteSuccess');
          this.onSearch();
        });
      }
    });
  }

  exportFile(): void {
    const bodySearch = this.formMerchantSearch.value;
    const fileName = this.translateService.instant('template.merchant');
    const obFile = this.merchantService.exportMerchant({
      keyword: bodySearch.keyword,
      merchantName: bodySearch.merchantName,
      status: bodySearch.status,
      fromDate: bodySearch.fromDate,
      toDate: bodySearch.toDate,
      timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
    });
    this.downloadService.downloadFileWithObservableAndName(
      obFile,
      fileName,
      FILE_EXTENSION.XLSX
    );
  }

  merchantCodeSync(): void {
    this.merchantService.merchantCodeSync({}).subscribe((res: any) => {
      this.toastService.success('merchant.merchantCodeSyncSuccess');
      this.onSearch();
      this.isSync = false;
    });
  }

  changeValidDate() {
    if (this.formMerchantSearch.controls['toDate'].value) {
      this.maxDate = this.formMerchantSearch.controls['toDate'].value;
    }
    if (
      this.formMerchantSearch.controls.fromDate.value &&
      this.formMerchantSearch.controls.toDate.value
    ) {
      this.formMerchantSearch.controls.fromDate.clearValidators();
      this.formMerchantSearch.controls.fromDate.updateValueAndValidity();
    }
  }
}
