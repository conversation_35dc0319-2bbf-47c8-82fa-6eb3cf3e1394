import { Component, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { AbstractDomainManageComponent } from '@core/components/abstract-domain-manage/abstract-domain-manage.component';
import { TranslateService } from '@ngx-translate/core';
import {
  CALCULATE_MILLISECONDS_IN_A_YEAR,
  DATE_RANGE_STATISTICAL_LOAN_ONLINE,
  ENTITY_STATUS,
  ENTITY_STATUS_MAP,
  FILE_EXTENSION,
  MODAL_ACTION,
  MOMENT_CONST,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IBaseResponseModel } from '@shared/models/base/base.model';
import { ILoanOnline } from '@shared/models/loan-online.models';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { LoanOnlineService } from '@shared/services/loan-online.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';

@Component({
  selector: 'app-loan-online-management',
  templateUrl: './loan-online-management.component.html',
  styleUrls: ['./loan-online-management.component.scss'],
})
export class LoanOnlineManagementComponent
  extends AbstractDomainManageComponent<ILoanOnline>
  implements OnInit
{
  resource: RESOURCE_TYPE = RESOURCE_CONST.LOAN_ONLINE;

  isValidMaxDate = (control: FormControl) => {
    const dateOfBirth = new Date(control.value).getTime();
    const maxDateOfBirth = new Date().getTime();
    return dateOfBirth > maxDateOfBirth ? { invalidMaxDate: true } : null;
  };

  searchForm: FormGroup;

  loanOnlines: ILoanOnline[] = [];
  recordSelected: any = [];
  action: any = '';
  storage: any;
  maxDate = new Date();
  maxToDate = new Date();

  ROUTER_UTILS = ROUTER_UTILS;
  // input search
  pageSizeOptions = PAGINATION.OPTIONS;
  ENTITY_STATUS = ENTITY_STATUS;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  SYSTEM_RULES = SYSTEM_RULES;
  VALIDATORS = VALIDATORS;

  searchResults: IBaseResponseModel<ILoanOnline[]> = {};

  constructor(
    private loanOnlineService: LoanOnlineService,
    private fb: FormBuilder,
    protected router: Router,
    private downloadService: DownloadService,
    private translateService: TranslateService,
    protected modalService: ModalService,
    private toastService: ToastrCustomService
  ) {
    super(loanOnlineService);

    const startDate = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDate = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.searchForm = this.fb.group({
      keyword: '',
      createdDateStartAt: [
        CommonUtils.reverseDate(startDate),
        [Validators.required],
      ],
      createdDateEndAt: [
        CommonUtils.reverseDate(endDate),
        [Validators.required, this.isValidMaxDate],
      ],
      status: null,
      length: 0,
      pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
      pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
      // previousPageIndex: 0,
    });
  }

  /**
   * Init data
   */
  ngOnInit(): void {
    super.ngOnInit();
  }

  // ngOnDestroy(): void {
  //   this._unsubscribeAll$.next();
  //   this._unsubscribeAll$.complete();
  // }

  onParseValueSearchForm(): void {
    super.onParseValueSearchForm();
  }

  /**
   * export file
   */
  exportFile(): void {
    // get value form control
    const bodySearch = this.searchForm.getRawValue();
    if (this.searchForm.invalid) {
      CommonUtils.markFormGroupTouched(this.searchForm);
    } else {
      // get file name
      const fileName = this.translateService.instant('template.loanOnline');
      const obFile = this.loanOnlineService.export({
        createdDateStartAt: bodySearch.createdDateStartAt,
        createdDateEndAt: bodySearch.createdDateEndAt,
        keyword: bodySearch.keyword,
        status: bodySearch.status,
      });

      this.downloadService.downloadFileWithObservableAndName(
        obFile,
        fileName,
        FILE_EXTENSION.XLSX
      );
    }
  }

  dateValidator(value: number): any {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return value > DATE_RANGE_STATISTICAL_LOAN_ONLINE
        ? { invalidDate: true }
        : null;
    };
  }

  changeValidDate() {
    if (
      this.searchForm.controls.createdDateStartAt.value &&
      this.searchForm.controls.createdDateEndAt.value
    ) {
      if (this.searchForm.controls['createdDateEndAt'].value) {
        this.maxDate = this.searchForm.controls['createdDateEndAt'].value;
      }
      const startDate = CommonUtils.setStartTime(
        new Date(this.searchForm.controls.createdDateStartAt.value).getTime()
      );
      const endDate = CommonUtils.setStartTime(
        new Date(this.searchForm.controls.createdDateEndAt.value).getTime()
      );
      const dayMax =
        (endDate - startDate) / CALCULATE_MILLISECONDS_IN_A_YEAR.DAY;
      if (dayMax > DATE_RANGE_STATISTICAL_LOAN_ONLINE) {
        this.searchForm.controls.createdDateStartAt.setValidators([
          Validators.required,
          this.dateValidator(dayMax),
          this.isValidMaxDate,
        ]);
        this.searchForm.controls.createdDateStartAt.updateValueAndValidity();
      } else {
        this.searchForm.controls.createdDateStartAt.clearValidators();
        this.searchForm.controls.createdDateStartAt.setValidators([
          Validators.required,
          this.isValidMaxDate,
        ]);
        this.searchForm.controls.createdDateStartAt.updateValueAndValidity();
      }
    }
  }

  /**
   * search form
   */
  onSearch(): void {
    if (this.searchForm.valid) {
      this.loanOnlineService
        .searchByKeyWord(this.searchForm.value)
        .subscribe((res: any): void => {
          this.searchResults = res.body;
          this.loanOnlines = res.body.content;
          this.searchForm.controls.length.setValue(res.body.totalElements, {
            emitEvent: false,
          });
        });
    }
  }

  /**
   * button click detail
   *
   * @param loanOnlineId number
   */
  detail(loanOnlineId?: number): void {
    this.router.navigate([
      ROUTER_UTILS.loanOnline.root,
      loanOnlineId,
      ROUTER_ACTIONS.detail,
    ]);
  }

  // checkValidateDate() {
  //   this.maxDate = this.searchForm.controls['createdDateEndAt'].value;
  // }

  /**
   * reset form
   */
  onReset(): void {
    // set default value date and reset data
    const startDateReset = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDateReset = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.searchForm.controls.status.reset();
    this.searchForm.controls.keyword.reset();
    this.searchForm.controls.createdDateStartAt.setValue(
      CommonUtils.reverseDate(startDateReset)
    );
    this.searchForm.controls.createdDateEndAt.setValue(
      CommonUtils.reverseDate(endDateReset)
    );
    this.searchForm.controls.createdDateStartAt.clearValidators();
    this.searchForm.controls.createdDateStartAt.setValidators([
      Validators.required,
    ]);
    this.searchForm.controls.createdDateStartAt.updateValueAndValidity();
  }

  /**
   * delete
   *
   * @param loanOnline ILoanOnline
   */
  delete(loanOnline: ILoanOnline): void {
    // open modal
    const modalData = {
      title: 'loanOnline.delete',
      content: 'loanOnline.deleteLoanOnlineContent',
      interpolateParams: { fullname: `<b>${loanOnline?.fullName || ''}</b>` },
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        if (loanOnline.loanOnlineId) {
          this.loanOnlineService
            .delete({ loanOnlineId: loanOnline.loanOnlineId })
            .subscribe((res: any) => {
              this.toastService.success('common.action.deleteSuccess');
              this.onSearch();
            });
        }
      }
    });
  }
}
