import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { INotification } from '@shared/models/notification.model';
import { IEventSearch } from '@shared/models/request/event.search';
import { INotificationSearch } from '@shared/models/request/notification.search';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class NotificationService extends AbstractService {
  public resourceUrl = '/notifications';

  constructor(protected http: HttpClient) {
    super(http);
  }

  search(request: IEventSearch): Observable<EntityResponseType<INotification>> {
    return super.post<EntityResponseType<INotification>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  /**
   * API get Notification of me
   *
   * @param request IPagination
   * @returns Observable<EntityResponseType<INotification[]>>
   */
  getNotificationOfMe(
    request: INotificationSearch,
    loading = false
  ): Observable<EntityResponseType<INotification[]>> {
    return super.post<EntityResponseType<INotification[]>>(
      `${this.resourceUrl}/me/notification`,
      request,
      { loading }
    );
  }

  /**
   * API delete
   *
   * @param request INotification
   * @returns Observable<EntityResponseType<INotification>>
   */
  deleteNotification(
    request: INotification
  ): Observable<EntityResponseType<INotification>> {
    return super.post<EntityResponseType<INotification>>(
      `${this.resourceUrl}/delete`,
      request
    );
  }
}
