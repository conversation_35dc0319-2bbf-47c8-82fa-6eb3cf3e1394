import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { IBgLogin } from '@shared/models/bg-login.model';
import { IFileEntrySearch } from '@shared/models/request/file-entry.search';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import CommonUtils from '@shared/utils/common-utils';
import { Observable } from 'rxjs/internal/Observable';
import { AbstractDomainService } from './common/abstract.domain.service';
import { EntityResponseType } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class BackgroundLoginService extends AbstractDomainService<IBgLogin> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.BG_LOGIN_APP;

  create(request: IBgLogin): Observable<EntityResponseType<IBgLogin>> {
    const body = CommonUtils.appendObjectToFormData(request, new FormData());
    return super.post<EntityResponseType<IBgLogin>>(`/create`, body);
  }

  update(request: IBgLogin): Observable<EntityResponseType<IBgLogin>> {
    const body = CommonUtils.appendObjectToFormData(request, new FormData());
    return super.post<EntityResponseType<IBgLogin>>(`/update`, body);
  }

  detail(): Observable<EntityResponseType<IBgLogin>> {
    return super.post<EntityResponseType<IBgLogin>>(`/info`);
  }

  getIcon(search: IFileEntrySearch, loading = false, ignoreError = true): any {
    return super.postFile(`/icon`, search, {
      loading,
      ignoreError,
    });
  }
}
