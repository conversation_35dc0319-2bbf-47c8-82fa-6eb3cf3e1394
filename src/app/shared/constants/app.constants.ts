import { NativeDateAdapter } from '@angular/material/core';
import {
  MODEL_MAP_COMMON,
  MODEL_MAP_ITEM_COMMON,
} from '@shared/models/model.type';
import moment from 'moment';
import { environment } from 'src/environments/environment';

export const SERVER_API_URL = environment.apiUrl;

export const TIME_CHECKING_REFRESH_TOKEN = 5 * 60;

export const DEVICE_TOKEN_CONST = 'BROWSER';

export const APP_NAME_CONST = 'MB LAOS';

export const PAGINATION = {
  PAGE_NUMBER_DEFAULT: 0, // Page Index
  PAGE_SIZE_DEFAULT: 10, // Page Size
  LARGE_SIZE_DEFAULT: 100, // Page Size
  MAX_SIZE_DEFAULT: 1000, // Page Size
  OPTIONS: [10, 25, 50, 100], // Page Size Options
  LENGTH_DEFAULT: 0, // Length default,
  PAGE_SIZE_DEFAULT_MINI: 5, // Page Size
};

export const TIME_RELOAD = 3 * 60 * 1000;

export const APP_FORMAT_DATE = {
  parse: {
    dateInput: ['DD/MM/YYYY'],
  },
  display: {
    dateInput: 'DD/MM/YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};

// export const DateFormats = {
//   parse: {
//     dateInput: 'YYYY-MM-DD HH:mm:ss',
//   },
//   display: {
//     dateInput: 'YYYY-MM-DD HH:mm:ss',
//     dateOutput: 'YYYY-MM-DD HH:mm:ss',
//     monthYearLabel: 'MMM YYYY',
//     dateA11yLabel: 'YYYY-MM-DD HH:mm:ss',
//     monthYearA11yLabel: 'MMMM YYYY',
//   },
// };

// export const CUSTOM_DATE_FORMATS = {
//   parse: {
//     dateInput: { month: 'short', year: 'numeric', day: 'numeric' },
//   },
//   display: {
//     dateInput: 'input',
//     monthYearLabel: { year: 'numeric', month: 'short' },
//     dateA11yLabel: { year: 'numeric', month: 'long', day: 'numeric' },
//     monthYearA11yLabel: { year: 'numeric', month: 'long' },
//   },
// };

export class AppDateAdapter extends NativeDateAdapter {
  format(date: Date, displayFormat?: any): string {
    // debugger;
    if (displayFormat === 'input') {
      return moment(date).format(dateFormat);
    } else {
      return date.toDateString();
    }
  }
}

export const BUTTON_ACTION_CONST = {
  OPEN: 'open',
  PRINT: 'print',
  DOWNLOAD: 'download',
};

export const dateFormat = 'DD/MM/YYYY';

export const TIME_OF_DAY = ['SÁNG', 'CHIỀU', 'CẢ NGÀY'];

export const DAY_OF_WEEK = [
  'Chủ Nhật',
  'Thứ hai',
  'Thứ ba',
  'Thứ tư',
  'Thứ năm',
  'Thứ sáu',
  'Thứ bảy',
];

export const WORKING_TIME_LIST = [
  { id: '1', text: 'Sáng' },
  { id: '2', text: 'Chiều' },
  { id: '3', text: 'Cả ngày' },
];

export const DATE_LIMIT = {
  MIN: '1900-01-01',
  MAX: '3000-01-01',
};

export const LIST_DAY_OF_WEEK = [
  {
    id: 1,
    text: 'Thứ 2',
  },
  {
    id: 2,
    text: 'Thứ 3',
  },
  {
    id: 3,
    text: 'Thứ 4',
  },
  {
    id: 4,
    text: 'Thứ 5',
  },
  {
    id: 5,
    text: 'Thứ 6',
  },
  {
    id: 6,
    text: 'Thứ 7',
  },
  {
    id: 7,
    text: 'Chủ nhật',
  },
];

export const CONFIG_DATA_TYPE = [
  { id: 1, text: 'Number', value: 'NUMBER' },
  { id: 2, text: 'Text', value: 'TEXT' },
  { id: 3, text: 'List', value: 'LIST' },
  { id: 4, text: 'Checkbox', value: 'CHECKBOX' },
  { id: 5, text: 'Radio', value: 'RADIO' },
  { id: 6, text: 'Date', value: 'DATE' },
  { id: 7, text: 'Select', value: 'SELECT' },
];

export const CONFIG_HOUR_NOTIFICATION = [
  { id: 0, text: '00:00' },
  { id: 1, text: '01:00' },
  { id: 2, text: '02:00' },
  { id: 3, text: '03:00' },
  { id: 4, text: '04:00' },
  { id: 5, text: '05:00' },
  { id: 6, text: '06:00' },
  { id: 7, text: '07:00' },
  { id: 8, text: '08:00' },
  { id: 9, text: '09:00' },
  { id: 10, text: '10:00' },
  { id: 11, text: '11:00' },
  { id: 12, text: '12:00' },
  { id: 13, text: '13:00' },
  { id: 14, text: '14:00' },
  { id: 15, text: '15:00' },
  { id: 16, text: '16:00' },
  { id: 17, text: '17:00' },
  { id: 18, text: '18:00' },
  { id: 19, text: '19:00' },
  { id: 20, text: '20:00' },
  { id: 21, text: '21:00' },
  { id: 22, text: '22:00' },
  { id: 23, text: '23:00' },
];
export const CONFIG_SAVING = [
  { value: 'FIXED', text: 'common.fixed' },
  { value: 'ACCUMULATED', text: 'common.accumulated' },
];
// export const FILE_UPLOAD = {
//   MIME_TYPE_RESOURCE: [
//     'application/msword',
//     'application/doc',
//     'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
//     'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
//     'application/java-archive',
//     'application/vnd.ms-excel',
//     'text/xml',
//     'application/xml',
//     'application/zip',
//     'application/x-zip-compressed',
//     'application/rar',
//     'application/x-rar-compressed',
//     'application/pdf',
//     'image/png',
//     'image/jpeg',
//     'image/gif',
//     'application/vnd.ms-powerpoint',
//     'application/vnd.openxmlformats-officedocument.presentationml.presentation',
//     'application/vnd.rar',
//     'text/plain',
//     'text/html',
//     'application/x-msdownload',
//     'video/mp4',
//     'video/x-flv',
//     'video/quicktime',
//     'video/x-msvideo',
//     'video/x-ms-wmv',
//     'video/avi',
//     'video/x-ms-wm',
//   ],
//   MIME_TYPE_VIDEO: [
//     'video/mp4',
//     'video/x-flv',
//     'video/quicktime',
//     'video/x-msvideo',
//     'video/x-ms-wmv',
//     'video/x-ms-wm',
//     'video/avi',
//   ],
//   TYPE_DOCUMENT: [
//     'doc',
//     'docx',
//     'xlm',
//     'xlsx',
//     'xlsm',
//     'pdf',
//     'powerpoint',
//     'rar',
//     'zip',
//     'excel',
//     'jpg',
//     'jpeg',
//     'png',
//     'ppt',
//     'pptx',
//     'jar',
//     'gif',
//     'exe',
//     'html',
//     'htm',
//   ],
//   TYPE_VIDEO: ['mp4', 'mov', 'flv', 'avi', 'wmv'],
//   SCORM: ['xml', 'zip'],
// };

export const GENDER: MODEL_MAP_ITEM_COMMON[] = [
  {
    label: 'common.female',
    code: 0,
  },
  {
    label: 'common.male',
    code: 1,
  },
  {
    label: 'common.other',
    code: 2,
  },
];

export const GENDER_MAP = GENDER.reduce(
  (data, valueMap) => ({ ...data, [valueMap.code]: valueMap.label }),
  {}
) as any;

export const ENTITY_STATUS_CONST: MODEL_MAP_COMMON = {
  ACTIVE: {
    label: 'common.active',
    code: 1,
    style: 'badge-success',
  },
  INACTIVE: {
    label: 'common.inactive',
    code: 0,
    style: 'badge-danger',
  },
};

export const HIDDEN_ACCOUNT_NUMBER_CONST: MODEL_MAP_COMMON = {
  HIDDEN: {
    label: 'model.specialAccountNumber.hiddenNumber',
    code: 'true',
    style: 'badge-secondary',
  },
};

export const SPECIAL_ACCOUNT_NUMBER_STATUS_CONST: MODEL_MAP_COMMON = {
  SOLD: {
    label: 'common.sold',
    code: 4,
    style: 'special-account badge-primary',
  },
  HIDDEN: {
    label: 'common.suspended',
    code: 2,
    style: 'special-account badge-warning',
  },
};

export const SAVING_ACCOUNT_STATUS_CONST: MODEL_MAP_COMMON = {
  ACTIVE: {
    label: 'common.active',
    code: 1,
    style: 'badge-success',
  },
  INACTIVE: {
    label: 'common.settled',
    code: 0,
    style: 'badge-danger',
  },
};

export const FINALIZATION_METHOD_CONST = {
  ROOT_ROTATION: {
    label: 'common.root-rotation',
    value: 'ROOT_ROTATION',
  },
  NO_ROTATION: {
    label: 'common.no-rotation',
    value: 'NO_ROTATION',
  },
  ROTATION_OF_PRINCIPAL_AND_INTEREST: {
    label: 'common.rotation-of-principal-and-interest',
    value: 'ROTATION_OF_PRINCIPAL_AND_INTEREST',
  },
};

export const RM_TYPE_CONST: MODEL_MAP_COMMON = {
  STAFF: {
    label: 'model.customer.staff',
    code: 'STAFF',
  },
  COLLABORATORS: {
    label: 'model.customer.collaborators',
    code: 'COLLABORATORS',
  },
  CUSTOMER: {
    label: 'model.customer.customer',
    code: 'CUSTOMER',
  },
};

export const RM_TYPE_CREATE_CONST: MODEL_MAP_COMMON = {
  STAFF: {
    label: 'model.customer.staff',
    code: 'STAFF',
  },
  COLLABORATORS: {
    label: 'model.customer.collaborators',
    code: 'COLLABORATORS',
  },
};

export const IMPORT_OR_EXPORT_CONST: MODEL_MAP_COMMON = {
  UPLOAD_TEMPLATE_FILE: {
    label: 'common.action.downloadTemplate',
    code: 0,
  },
  UPLOAD_FILE: {
    label: 'common.action.uploadFiles',
    code: 1,
  },
  EXPORT_FILE: {
    label: 'common.action.export',
    code: 2,
  },
};

export const CONFIGURATION_FEE_TYPE_CONST: MODEL_MAP_COMMON = {
  FEE: {
    label: 'fee.fee',
    code: 'FEE',
  },
  DISCOUNT: {
    label: 'fee.discount',
    code: 'DISCOUNT',
  },
};

export const CONFIGURATION_FEE_TYPE = Object.values(
  CONFIGURATION_FEE_TYPE_CONST
);

export const CONFIGURATION_FEE_TYPE_MAP = CONFIGURATION_FEE_TYPE.reduce(
  (data, valueMap) => ({ ...data, [valueMap.code]: valueMap.label }),
  {}
) as any;

export const EVENT_STATUS_CONST: MODEL_MAP_COMMON = {
  DONE: {
    label: 'event.sent',
    code: 'DONE',
    style: 'text-primary',
  },
  DRAFT: {
    label: 'event.draft',
    code: 'DRAFT',
    style: 'text-secondary',
  },
  WAITING: {
    label: 'event.waiting',
    code: 'WAITING',
    style: 'text-warning',
  },
};

export const NOTIFICATION_CUSTOMER_STATUS: MODEL_MAP_COMMON = {
  SUCCESS: {
    label: 'common.success',
    code: 'DONE',
    style: 'text-success',
  },
  FAILED: {
    label: 'common.fail',
    code: 'FAILED',
    style: 'text-danger',
  },
};

export const RECEIVER_TYPE = [
  {
    value: 'ALL_CUSTOMER',
    label: 'model.event.allCustomer',
  },
  {
    value: 'EACH_CUSTOMER',
    label: 'model.event.eachCustomer',
  },
];

export const CONFIGURATION_FEE_TYPE_RADIO = [
  {
    value: 'FEE',
    label: 'fee.fee',
  },
  {
    value: 'DISCOUNT',
    label: 'fee.discount',
  },
];

export const EVENT_TYPE = {
  DRAFT: 'DRAFT',
  WAITING: 'WAITING',
  DONE: 'DONE',
};

export const EVENT_STATUS = Object.values(EVENT_STATUS_CONST);

export const EVENT_STATUS_MAP = EVENT_STATUS.reduce(
  (data, valueMap) => ({ ...data, [valueMap.code]: valueMap.label }),
  {}
) as any;

export const ENTITY_STATUS = Object.values(ENTITY_STATUS_CONST);

export const HIDDEN_ACCOUNT_NUMBER = Object.values(HIDDEN_ACCOUNT_NUMBER_CONST);

export const SPECIAL_ACCOUNT_NUMBER_STATUS = Object.values(
  SPECIAL_ACCOUNT_NUMBER_STATUS_CONST
);

export const SAVING_ACCOUNT_STATUS = Object.values(SAVING_ACCOUNT_STATUS_CONST);

export const RM_TYPE = Object.values(RM_TYPE_CONST);

export const RM_TYPE_CREATE = Object.values(RM_TYPE_CREATE_CONST);

export const IMPORT_OR_EXPORT = Object.values(IMPORT_OR_EXPORT_CONST);

export const ENTITY_STATUS_MAP = ENTITY_STATUS.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.code]: { label: valueMap.label, style: valueMap.style },
  }),
  {}
) as any;

export const SPECIAL_ACCOUNT_NUMBER_STATUS_MAP =
  SPECIAL_ACCOUNT_NUMBER_STATUS.reduce(
    (data, valueMap) => ({
      ...data,
      [valueMap.code]: { label: valueMap.label, style: valueMap.style },
    }),
    {}
  ) as any;

export const HIDDEN_ACCOUNT_NUMBER_MAP = HIDDEN_ACCOUNT_NUMBER.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.code]: { label: valueMap.label, style: valueMap.style },
  }),
  {}
) as any;

export const SAVING_ACCOUNT_STATUS_MAP = SAVING_ACCOUNT_STATUS.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.code]: { label: valueMap.label, style: valueMap.style },
  }),
  {}
) as any;

export const ENTITY_TYPE_MAP = RM_TYPE.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.code]: { label: valueMap.label, style: valueMap.style },
  }),
  {}
) as any;

export const ENTITY_TRANSACTION_STATUS_CONST: MODEL_MAP_COMMON = {
  PROCESSING: {
    label: 'common.processing',
    code: 'PROCESSING',
    style: 'badge-info',
  },
  FAILED: {
    label: 'common.failed',
    code: 'FAILED',
    style: 'badge-danger',
  },
  SUCCESS: {
    label: 'common.success',
    code: 'SUCCESS',
    style: 'badge-success',
  },
  TIMEOUT: {
    label: 'common.timeout',
    code: 'TIMEOUT',
    style: 'badge-secondary',
  },
};

export const TRANSACTION_ENTITY_STATUS = Object.values(
  ENTITY_TRANSACTION_STATUS_CONST
);

export const TRANSACTION_ENTITY_STATUS_MAP = TRANSACTION_ENTITY_STATUS.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.code]: { label: valueMap.label, style: valueMap.style },
  }),
  {}
) as any;

export const TYPE_BANK_CODE_MB_CONST = 'MB';
export const TRANSACTION_TYPE_TRANSFER_MONEY_CONST = 'TRANSFER_MONEY';
export const INTERNATIONAL_TRANSFER_MONEY_TYPE_CONST =
  'QR_CODE_INTERNATIONAL_MERCHANT';

export const ENTITY_TRANSACTION_TYPE_CONST: MODEL_MAP_COMMON = {
  TRANSFER_MONEY_INTERNAL: {
    label: 'model.report.transaction.internalBank',
    code: 'INTERNAL_BANK',
    style: 'badge-info',
  },
  TRANSFER_MONEY_INTER: {
    label: 'model.report.transaction.interBank',
    code: 'INTER_BANK',
    style: 'badge-info',
  },
  // QRCODE: {
  //   label: 'model.report.transaction.qrcode',
  //   code: 'QRCODE',
  //   style: 'badge-danger',
  // },
  // SAVED_TRANSACTION: {
  //   label: 'model.report.transaction.saveTransaction',
  //   code: 'SAVED_TRANSACTION',
  //   style: 'badge-success',
  // },
  TOPUP: {
    label: 'model.report.transaction.topup',
    code: 'TOPUP',
    style: 'badge-success',
  },
  // CASH_IN: {
  //   label: 'model.report.transaction.cashIn',
  //   code: 'CASH_IN',
  //   style: 'badge-success',
  // },
  BILLING: {
    label: 'model.report.transaction.billing',
    code: 'BILLING',
    style: 'badge-success',
  },
  INSURANCE: {
    label: 'model.report.transaction.insurance',
    code: 'INSURANCE',
    style: 'badge-success',
  },
};

export const TRANSACTION_ENTITY_TYPE = Object.values(
  ENTITY_TRANSACTION_TYPE_CONST
);

export const TRANSACTION_ENTITY_TYPE_MAP = TRANSACTION_ENTITY_TYPE.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.code]: { label: valueMap.label, style: valueMap.style },
  }),
  {}
) as any;

export const TRANSACTION_UMONEY_ENTITY_TYPE_CONST = {
  CASH_IN: {
    code: 'CASH_IN',
    label: 'Cash in Umoney',
  },
  CASH_OUT: {
    code: 'CASH_OUT',
    label: 'Cash out Umoney',
  },
};

export const TRANSACTION_UMONEY_ENTITY_TYPE = Object.values(
  TRANSACTION_UMONEY_ENTITY_TYPE_CONST
);

export const TRANSACTION_UMONEY_ENTITY_TYPE_MAP =
  TRANSACTION_UMONEY_ENTITY_TYPE.reduce(
    (data, valueMap) => ({
      ...data,
      [valueMap.code]: valueMap.label,
    }),
    {}
  ) as any;

export const MODAL_ACTION: MODEL_MAP_COMMON = {
  CONFIRM: {
    code: 'confirm',
    label: 'common.action.confirm',
  },
  DISMISS: {
    code: 'dismiss',
    label: 'common.action.dismiss',
  },
  CLOSE: {
    code: 'close',
    label: 'common.action.close',
  },
  CANCEL: {
    code: 'cancel',
    label: 'common.action.cancel',
  },
  LOCK: {
    code: 'lock',
    label: 'common.action.lock',
  },
  UNLOCK: {
    code: 'unlock',
    label: 'common.action.unlock',
  },
  DELETE: {
    code: 'delete',
    label: 'common.action.delete',
  },
  RESET: {
    code: 'reset',
    label: 'common.action.reset',
  },
  CREATE: {
    code: 'create',
    label: 'common.action.create',
  },
  UPLOAD: {
    code: 'upload',
    label: 'common.action.uploadFile',
  },
  DOWNLOAD_TEMPLATE: {
    code: 'download-template',
    label: 'common.action.download-template',
  },
};

export const ALERT_TYPE = {
  OTHER: 'OTHER',
  ADVERTISEMENT: 'ADVERTISEMENT',
};

export const BOOLEAN_STRING = {
  TRUE: 'true',
  FALSE: 'false',
};

export const BOOLEAN_NUMBER = {
  TRUE: 1,
  FALSE: 0,
};

export const DATE_RANGE_STATISTICAL_LOAN_ONLINE = 89;

export const DATE_RANGE_STATISTICAL_CUSTOMER = 89;

export const DATE_RANGE_STATISTICAL = 89;

export const MOMENT_CONST = {
  FORMAT_DEFAULT: 'DD-MM-YYYY',
  FORMAT_DATE_PLACEHOLDER: 'DD/MM/YYYY',
  FORMAT_YEAR: 'YYYY-MM-DD',
  TIMESTAMP_FORMAT: 'YYYYMMDDHHmmss',
  LOCAL_DATE_TIME_FORMAT: 'DD-MM-YYYY HH:mm:ss',
  TIMEZONE_FORMAT: 'DD-MM-YYYY HH:mm:ssZ',
  DATE_TIME_FORMAT: 'YYYY-MM-DDTHH:mm',
  LOCAL_DATE_TIME_MOMENT_FORMAT: 'YYYY-MM-DDTHH:mm',
  DATE_TIME_MOMENT_FORMAT: 'YYYY-MM-DDTHH:mm:ss', // '2018-08-23T00:00:00+02:00'
  LOCAL_DATE_TIME_YEAR_FORMAT: 'YYYY-MM-DD HH:mm:ss',
  FORMAT_DATE_MAT_PICKER: 'yyyy-MM-dd',
  FORMAT_DATE_TIME_PLACEHOLDER: 'HH:mm:ss, DD/MM/YYYY',
  FORMAT_TIME: 'HH:mm',
};

export const FILE_EXTENSION = {
  XLSX: '.xlsx',
  PDF: '.pdf',
};

export const QUANTITY_UPLOAD = {
  FILE_ID_CUSTOMER: 2,
  ICON_BANK: 1,
  BANNER_CAMPAIGN: 1,
  BANNER_CAMPAIGN_MAX_NUMBER: 5,
};

export const MAX_FILE_SIZE_CONST = {
  '10MB': {
    BYTE: 1024 * 1024 * 10,
    KB: 1024 * 10,
    MB: 10,
  },
  '5MB': {
    BYTE: 1024 * 1024 * 5,
    KB: 1024 * 5,
    MB: 5,
  },
};

//     'image/png',
//     'image/jpeg',
//     'image/gif',
// png,jpg,jpeg,bmp
export const FILE_UPLOAD_EXTENSIONS = {
  AVATAR: ['image/png', 'image/jpg', 'image/jpeg', 'image/bmp'],
  BACKGROUND: ['image/png', 'image/jpg', 'image/jpeg', 'image/heic'],
  ICON: ['image/png', 'image/jpg', 'image/jpeg'],
  BANNER: ['image/png', 'image/jpg', 'image/jpeg'],
  THUMBNAIL: ['image/png', 'image/jpg', 'image/jpeg', 'image/heic'],
  DOCUMENT: [
    'application/pdf',
    'application/msword',
    'application/doc',
    'image/png',
    'image/jpg',
    'image/jpeg',
    'image/heic',
  ],
  ATTACHMENT: [
    'application/pdf',
    'application/msword',
    'application/doc',
    'image/png',
    'image/jpg',
    'image/jpeg',
    'image/heic',
  ],
  NEWS: ['image/png', 'image/jpg', 'image/gif'],
};

export const TYPE_FILES = [
  {
    type: 'docx',
    value:
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  },
  {
    type: 'docx',
    value: 'application/msword',
  },
  {
    type: 'excel',
    value: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  },
  {
    type: 'excel',
    value: 'application/vnd.ms-excel',
  },
  {
    type: 'pdf',
    value: 'application/pdf',
  },
  {
    type: 'image',
    value: 'image/jpeg',
  },
  {
    type: 'image',
    value: 'image/png',
  },
];

export const LIMIT_LENGTH_WORD_DEFAULT = 20;

export const EXTREME_EMAIL_MBBANK = '@mbbank.com.vn';

export const EXTREME_EMAIL_EVOTEK = '@evotek.vn';

export const CALCULATE_MILLISECONDS_IN_A_YEAR = {
  MINUTE: 1000 * 60,
  HOUR: 1000 * 60 * 60,
  DAY: 1000 * 60 * 60 * 24,
  YEAR: 1000 * 60 * 60 * 24 * 365,
};

export const LIMIT_LENGTH_WORD_CONST = {
  SMALL: 20,
  MEDIUM: 50,
  LARGE: 100,
  MEDIUM_LARGE: 150,
  VERY_LARGE: 500,
  LIMIT_WORD_26: 26,
  LIMIT_WORD_40: 40,
};

export const MAX_MIN_VALUE_CONST = {
  MAX: 100,
  MIN: 0,
};

export const MAX_DATE_CONST = '9999-12-31T00:00';

export const DATE_CONSTANT = {
  YYYYMMDD_HYPHEN: 'YYYY-MM-dd',
  YYYYMMDD_HYPHEN_BIG: 'YYYY-MM-DD',
  DDMMYYYY_HYPHEN: 'dd-MM-YYYY',
  yyyyMMDD_HYPHEN: 'yyyy-MM-DD',
  YYYYMMDD_SLASH: 'YYYY/MM/dd',
  YYYYMMDD_SLASH_BIG: 'YYYY/MM/DD',
  DDMMYYYY_SLASH: 'dd/MM/YYYY',
  DDMMYYYY_SLASH_SMALL: 'dd/MM/yyyy',
  DDMMYYYY_SLASH_BIG: 'DD/MM/YYYY',
  DDMMYYYY: 'DDMMYYYY',
  HHmmDDMMYYYY_SLASH_SMALL: 'HH:mm - dd/MM/yyyy',
  DDMMYYYY_HHmm_SLASH_SMALL: 'dd/MM/yyyy - HH:mm',
  HHMMSSDDMMYYYY_SLASH_SMALL: 'HH:mm:ss - dd/MM/yyyy',
  HHMMSSDDMMYYYY_SLASH_BIG: 'HH:mm:ss, DD/MM/YYYY',
  HHMMSSDDMMYYYY_SLASH: 'HH:mm:ss - dd/MM/YYYY',
  HHmmddMMYYYY_SLASH_SMALL: 'HH:mm - DD/MM/YYYY',
  YYYYMMDD_HYPHEN_SMALL: 'yyyy-MM-dd',
};

export const LANGUAGE_CONST: MODEL_MAP_COMMON = {
  VI: {
    label: '',
    code: 'vi',
    font: 'Font MB',
  },
  LO: {
    label: '',
    code: 'lo',
    font: 'Font Laos',
  },
  EN: {
    label: '',
    code: 'en',
    font: 'Font MB',
  },
};

export const LANGUAGE = Object.values(LANGUAGE_CONST);

export const LANGUAGE_MAP = LANGUAGE.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.code]: { label: valueMap.label, font: valueMap.font },
  }),
  {}
) as any;

export const PUBLISHED_STATUS_CONST = {
  PUBLISHED: {
    label: 'model.versionManage.versionStatus.published',
    value: 1,
    style: 'badge-success',
  },
  UNRELEASED: {
    label: 'model.versionManage.versionStatus.unreleased',
    value: 0,
    style: 'badge-secondary',
  },
};

export const PUBLISHED_STATUS = Object.values(PUBLISHED_STATUS_CONST);

export const PUBLISHED_STATUS_MAP = PUBLISHED_STATUS.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.value]: {
      label: valueMap.label,
      style: valueMap.style,
    },
  }),
  {}
) as any;

/// loại cập nhật
export const UPDATE_TYPE_CONST = {
  FORCE: {
    code: 1,
    value: true,
  },
  NOTI: {
    code: 1,
    value: true,
  },
  MANUAL: {
    code: 1,
    value: true,
  },
};

export const UPDATE_TYPE = Object.values(UPDATE_TYPE_CONST);

export const TYPE_OPERATE_SYSTEM = {
  IOS: {
    value: 'IOS',
    type: 'IOS',
  },
  ANDROID: {
    value: 'ANDROID',
    type: 'ANDROID',
  },
};

export const URL_STORE_CONST = {
  IOS: {
    value: 'https://apps.apple.com/us/app/mb-laos/id1526248099',
  },
  ANDROID: {
    value:
      'https://play.google.com/store/apps/details?id=com.mblaos&hl=vi&gl=US',
  },
};

export const PRODUCT_TYPE_VALUE_CONST = {
  VEHICLE: {
    value: 'VEHICLE',
  },
  HEALTH: {
    value: 'HEALTH',
  },
};

export const PRODUCT_TYPE_VALUE = Object.values(PRODUCT_TYPE_VALUE_CONST);

export enum TRANSACTION_FEE_ENUM {
  INTERNAL_QR_BILLING = 'INTERNAL_QR_BILLING',
  LAPNET_QR_BILLING = 'LAPNET_QR_BILLING',
  TRANSFER_INTERNAL_BANK = 'TRANSFER_INTERNAL_BANK',
  TRANSFER_INTER_BANK = 'TRANSFER_INTER_BANK',
  E_WALLET_RECHARGE = 'E_WALLET_RECHARGE',
  INTERNET_BILLING = 'INTERNET_BILLING',
  WATER_BILLING = 'WATER_BILLING',
  ELECTRIC_BILLING = 'ELECTRIC_BILLING',
  TOPUP = 'TOPUP',
  POST_PAID_BILLING = 'POST_PAID_BILLING',
  PSTN_BILLING = 'PSTN_BILLING',
  INSURANCE = 'INSURANCE',
}
export const TRANCSACTION_FEE_CONST = {
  [TRANSACTION_FEE_ENUM.INTERNAL_QR_BILLING]: {
    value: 11,
    name: TRANSACTION_FEE_ENUM.INTERNAL_QR_BILLING,
  },
  [TRANSACTION_FEE_ENUM.LAPNET_QR_BILLING]: {
    value: 12,
    name: TRANSACTION_FEE_ENUM.LAPNET_QR_BILLING,
  },
  [TRANSACTION_FEE_ENUM.TRANSFER_INTERNAL_BANK]: {
    value: 1,
    name: TRANSACTION_FEE_ENUM.TRANSFER_INTERNAL_BANK,
  },
  [TRANSACTION_FEE_ENUM.TRANSFER_INTER_BANK]: {
    value: 2,
    name: TRANSACTION_FEE_ENUM.TRANSFER_INTER_BANK,
  },
  [TRANSACTION_FEE_ENUM.E_WALLET_RECHARGE]: {
    value: 3,
    name: TRANSACTION_FEE_ENUM.E_WALLET_RECHARGE,
  },
  [TRANSACTION_FEE_ENUM.INTERNET_BILLING]: {
    value: 4,
    name: TRANSACTION_FEE_ENUM.INTERNET_BILLING,
  },
  [TRANSACTION_FEE_ENUM.WATER_BILLING]: {
    value: 5,
    name: TRANSACTION_FEE_ENUM.WATER_BILLING,
  },
  [TRANSACTION_FEE_ENUM.ELECTRIC_BILLING]: {
    value: 6,
    name: TRANSACTION_FEE_ENUM.ELECTRIC_BILLING,
  },
  [TRANSACTION_FEE_ENUM.TOPUP]: {
    value: 7,
    name: TRANSACTION_FEE_ENUM.TOPUP,
  },
  [TRANSACTION_FEE_ENUM.POST_PAID_BILLING]: {
    value: 8,
    name: TRANSACTION_FEE_ENUM.POST_PAID_BILLING,
  },
  [TRANSACTION_FEE_ENUM.PSTN_BILLING]: {
    value: 9,
    name: TRANSACTION_FEE_ENUM.PSTN_BILLING,
  },
  [TRANSACTION_FEE_ENUM.INSURANCE]: {
    value: 10,
    name: TRANSACTION_FEE_ENUM.INSURANCE,
  },
};

export const PACKAGE_INSURANCE_VEHICLE_CONST = {
  A0: {
    code: 'A0',
    nameLa: 'ປະກັນໄພຄວາມຮັບຜິດຊອບທາງແພ່ງ A0',
    nameEn: 'Liability insurance A0',
    nameVn: 'Bảo hiểm trách nhiệm xe mức A0',
  },
  A1: {
    code: 'A1',
    nameLa: 'ປະກັນໄພຄວາມຮັບຜິດຊອບທາງແພ່ງ A1',
    nameEn: 'Liability insurance A1',
    nameVn: 'Bảo hiểm trách nhiệm xe mức A1',
  },
  A2: {
    code: 'A2',
    nameLa: 'ປະກັນໄພຄວາມຮັບຜິດຊອບທາງແພ່ງ A2',
    nameEn: 'Liability insurance A2',
    nameVn: 'Bảo hiểm trách nhiệm xe mức A2',
  },
  FLEX1: {
    code: 'FLEX1',
    nameLa: '​ປະ​ກັນ​ໄພ​ຮອບ​ດ້ານ FLEX1',
    nameEn: 'Liability and physical demage FLEX1',
    nameVn: 'Bảo hiểm trách nhiệm và vật chất FLEX1',
  },
  FLEX2: {
    code: 'FLEX2',
    nameLa: '​ປະ​ກັນ​ໄພ​ຮອບ​ດ້ານ FLEX2',
    nameEn: 'Liability and physical demage FLEX2',
    nameVn: 'Bảo hiểm trách nhiệm và vật chất FLEX2',
  },
  FLEX3: {
    code: 'FLEX3',
    nameLa: '​ປະ​ກັນ​ໄພ​ຮອບ​ດ້ານ FLEX3',
    nameEn: 'Liability and physical demage FLEX3',
    nameVn: 'Bảo hiểm trách nhiệm và vật chất FLEX3',
  },
};

export const PACKAGE_INSURANCE_VEHICLE = Object.values(
  PACKAGE_INSURANCE_VEHICLE_CONST
);

export const PACKAGE_INSURANCE_VEHICLE_MAP = PACKAGE_INSURANCE_VEHICLE.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.code]: {
      nameLa: valueMap.nameLa,
      nameEn: valueMap.nameEn,
      nameVn: valueMap.nameVn,
    },
  }),
  {}
) as any;

export const PACKAGE_INSURANCE_HEALTH_CONST = {
  PA01: {
    code: 'PA01',
    nameLa: 'ແພັກເກັດດູແລສຸຂະພາບ ຂັ້ນພື້ນຖານ (ລວມ Covid-19)',
    nameEn: 'Health care BASIC (included Covid-19)',
    nameVn: 'Gói chăm sóc sức khỏe cơ bản (gồm Covid-19)',
  },
  PA02: {
    code: 'PA02',
    nameLa: 'ແພັກເກັດດູແລສຸຂະພາບ ຂັ້ນສູງ (ລວມ Covid-19)',
    nameEn: 'Health care ADVANCE (included Covid-19)',
    nameVn: 'Gói chăm sóc sức khỏe nâng cao (gồm Covid-19)',
  },
  PA03: {
    code: 'PA03',
    nameLa: 'ແພັກເກັດດູແລສຸຂະພາບ ສຳຫຼັບ VIP (ລວມ Covid-19)',
    nameEn: 'Health care VIP (included Covid-19)',
    nameVn: 'Gói chăm sóc sức khỏe VIP (gồm Covid-19)',
  },
};

export const PACKAGE_INSURANCE_HEALTH = Object.values(
  PACKAGE_INSURANCE_HEALTH_CONST
);

export const PACKAGE_INSURANCE_HEALTH_MAP = PACKAGE_INSURANCE_HEALTH.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.code]: {
      nameVn: valueMap.nameVn,
      nameLa: valueMap.nameLa,
      nameEn: valueMap.nameEn,
    },
  }),
  {}
) as any;

export const VEHICLE_TYPE_CONST = {
  MV01: {
    code: 'MV01',
    nameLa: 'ລົດຕູ້ (8-12 ບອ່ນ)',
    nameEn: 'Car 4 seats (COUPER,SEDAN)',
    nameVn: 'Xe cá nhân 4 chỗ ngồi',
  },
  MV02: {
    code: 'MV02',
    nameLa: 'ລົດບັນທຸກ ຕ່ຳກວ່າ 10ໂຕນ',
    nameEn: 'Truck less than 10ton',
    nameVn: 'Xe tải nhỏ hơn 10 tấn',
  },
  MV03: {
    code: 'MV03',
    nameLa: 'ລົດຕູ້ (8-12 ບອ່ນນັ່ງ)',
    nameEn: 'Bus 8-12 seats (MINIBUS)',
    nameVn: 'Xe Bus 8-12 chỗ ngồi',
  },
  MV04: {
    code: 'MV04',
    nameLa: 'ລົດບັນທຸກ ຕ່ຳກວ່າ 10ໂຕນ',
    nameEn: 'Truck less than 10ton',
    nameVn: 'Xe tải nhỏ hơn 10 tấn',
  },
  MV05: {
    code: 'MV05',
    nameLa: 'ລົດ​ຈັກ',
    nameEn: 'Motorbike',
    nameVn: 'Xe máy',
  },
};

export const VEHICLE_TYPE = Object.values(VEHICLE_TYPE_CONST);

export const VEHICLE_TYPE_MAP = VEHICLE_TYPE.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.code]: {
      nameVn: valueMap.nameVn,
      nameLa: valueMap.nameLa,
      nameEn: valueMap.nameEn,
    },
  }),
  {}
) as any;

export const SERVICE_TYPE_CONST = {
  BASIC: {
    label: 'model.configTransaction.form.basicService',
    value: 1,
  },
  ADVANCE: {
    label: 'model.configTransaction.form.advanceService',
    value: 2,
  },
};

export const SERVICE_TYPE = Object.values(SERVICE_TYPE_CONST);
export const SERVICE_TYPE_MAP = SERVICE_TYPE.reduce(
  (data, valueMap) => ({ ...data, [valueMap.value]: valueMap.label }),
  {}
) as any;

export const TRANSACTION_TYPE_CONST = {
  INTERNAL_BANK: {
    label: 'model.configTransaction.form.internalTransfer',
    value: 1,
    formName: 'INTERNAL_BANK',
  },
  INTERNATIONAL_BANK: {
    label: 'model.configTransaction.form.internationalTransfer',
    value: 2,
    formName: 'INTERNATIONAL_BANK',
  },
  INTER_BANK: {
    label: 'model.configTransaction.form.interbankTransfer',
    value: 3,
    formName: 'INTER_BANK',
  },
  CASH_IN: {
    label: 'model.configTransaction.form.cashIn',
    value: 4,
    formName: 'CASH_IN',
  },
  TOPUP: {
    label: 'model.configTransaction.form.topUp',
    value: 5,
    formName: 'TOPUP',
  },
  INSURANCE: {
    label: 'model.configTransaction.form.insurance',
    value: 6,
    formName: 'INSURANCE',
  },
  BILLING: {
    label: 'model.configTransaction.form.billing',
    value: 7,
    formName: 'BILLING',
  },
};

export const TRANSACTION_TYPE = Object.values(TRANSACTION_TYPE_CONST);
export const INTERNAL_TRANSACTION_TYPE = [
  TRANSACTION_TYPE_CONST.INTERNAL_BANK,
  TRANSACTION_TYPE_CONST.INTER_BANK,
  TRANSACTION_TYPE_CONST.CASH_IN,
  TRANSACTION_TYPE_CONST.TOPUP,
  TRANSACTION_TYPE_CONST.INSURANCE,
  TRANSACTION_TYPE_CONST.BILLING,
];
export const INTER_TRANSACTION_TYPE = [
  TRANSACTION_TYPE_CONST.INTERNAL_BANK,
  TRANSACTION_TYPE_CONST.INTERNATIONAL_BANK,
];
export const INTERNATIONAL_TRANSACTION_TYPE = [
  TRANSACTION_TYPE_CONST.INTERNATIONAL_BANK,
];
export const OTHER_TRANSACTION_TYPE = [TRANSACTION_TYPE_CONST.INTERNAL_BANK];

export const TRANSACTION_TYPE_MAP = TRANSACTION_TYPE.reduce(
  (data, valueMap) => ({ ...data, [valueMap.value]: valueMap.label }),
  {}
) as any;

export const TRANSACTION_SECTOR_CONST = {
  SECTOR_1890: {
    label: 'model.customer.customerSector.customerId1890',
    value: 1890,
  },
  SECTOR_1891: {
    label: 'model.customer.customerSector.customerId1891',
    value: 1891,
  },
  SECTOR_1740: {
    label: 'model.customer.customerSector.customerId1740',
    value: 1740,
  },
};

export const CURRENCY_CONST = {
  LAK: {
    value: 'LAK',
  },
  THB: {
    value: 'THB',
  },
  USD: {
    value: 'USD',
  },
  KHR: {
    value: 'KHR',
  },
  VND: {
    value: 'VND',
  },
};

export const CURRENCY = Object.values(CURRENCY_CONST);
export const TRANSACTION_SECTOR = Object.values(TRANSACTION_SECTOR_CONST);

export const TRANSACTION_SECTOR_MAP = TRANSACTION_SECTOR.reduce(
  (data, valueMap) => ({ ...data, [valueMap.value]: valueMap.label }),
  {}
) as any;

export enum TRANS_MONEY_SECTOR {
  TRANS_DAY_MONEY_1890 = 300000000,
  TRANS_DAY_MONEY_1891 = 20000000,
  TRANS_MONTH_MONEY_1891 = 50000000,
}

export const CONFIG_TRANS_STATUS_CONST = {
  APPROVAL: {
    value: 1,
    label: 'model.configTransaction.approved',
    style: 'badge-success',
  },
  DENY: {
    value: 2,
    label: 'model.configTransaction.denied',
    style: 'badge-danger',
  },
  WAITING: {
    value: 0,
    label: 'model.configTransaction.waiting',
    style: 'badge-warning',
  },
};

export const CONFIG_TRANS_STATUS = Object.values(CONFIG_TRANS_STATUS_CONST);

export const CONFIG_TRANS_STATUS_MAP = CONFIG_TRANS_STATUS.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.value]: {
      value: valueMap.value,
      label: valueMap.label,
      style: valueMap.style,
    },
  }),
  {}
) as any;

export const NATION_CODES_CONST = {
  VN: {
    code: 'VN',
    label: 'model.nationCode.VN',
  },
  KH: {
    code: 'KH',
    label: 'model.nationCode.KH',
  },
  LA: {
    code: 'LA',
    label: 'model.nationCode.LA',
  },
  CN: {
    code: 'CN',
    label: 'model.nationCode.CN',
  },
  TH: {
    code: 'TH',
    label: 'model.nationCode.TH',
  },
};

export const CROSS_BORDER_NATION_CODES = [NATION_CODES_CONST.VN, NATION_CODES_CONST.KH, NATION_CODES_CONST.TH, NATION_CODES_CONST.CN];
export const NATION_CODES = Object.values(NATION_CODES_CONST);

export const SCREEN_SHOW_BANNER = {
  LOGIN: {
    value: 1,
    label: 'LOGIN',
  },
  HOMEPAGE: {
    value: 2,
    label: 'HOME',
  },
  OTHER: {
    value: 3,
    label: 'OTHER',
  },
};

export enum STATUS_BUTTON {
  CHECKED = 'CHECKED',
  NOT_CHECKED = 'NOT_CHECKED',
}

export interface IStatusOption {
  value: number;
}
export const STATUS_BUTTON_OPTION: Record<STATUS_BUTTON, IStatusOption> = {
  CHECKED: { value: 1 },
  NOT_CHECKED: { value: 0 },
};

export enum MASTER_MERCHANT_SERVICE_ENUM {
  TOPUP = 'TOPUP',
  BILLING = 'BILLING',
  OTHER = 'OTHER',
}

export const MASTER_MERCHANT_SERVICE_CONST = {
  [MASTER_MERCHANT_SERVICE_ENUM.TOPUP]: {
    label: 'model.merchant.masterMerchant.masterMerchantTopup',
    value: MASTER_MERCHANT_SERVICE_ENUM.TOPUP,
  },
  [MASTER_MERCHANT_SERVICE_ENUM.BILLING]: {
    label: 'model.merchant.masterMerchant.masterMerchantBilling',
    value: MASTER_MERCHANT_SERVICE_ENUM.BILLING,
  },
  [MASTER_MERCHANT_SERVICE_ENUM.OTHER]: {
    label: 'model.merchant.masterMerchant.masterMerchantOther',
    value: MASTER_MERCHANT_SERVICE_ENUM.OTHER,
  },
};

export const MASTER_MERCHANT_SERVICE = Object.values(
  MASTER_MERCHANT_SERVICE_CONST
);

export const MASTER_MERCHANT_SERVICE_MAP = MASTER_MERCHANT_SERVICE.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.value]: valueMap.label,
  }),
  {}
) as any;

export enum MASTER_MERCHANT_TYPE_ENUM {
  ETL = 'ETL',
  LTC = 'LTC',
  TPLUS = 'TPLUS',
  UNITEL = 'UNITEL',
  BTC = 'BTC',
}

export const MASTER_MERCHANT_TYPE_TOPUP_CONST = {
  [MASTER_MERCHANT_TYPE_ENUM.ETL]: {
    value: MASTER_MERCHANT_TYPE_ENUM.ETL,
  },
  [MASTER_MERCHANT_TYPE_ENUM.TPLUS]: {
    value: MASTER_MERCHANT_TYPE_ENUM.TPLUS,
  },
  [MASTER_MERCHANT_TYPE_ENUM.LTC]: {
    value: MASTER_MERCHANT_TYPE_ENUM.LTC,
  },
  [MASTER_MERCHANT_TYPE_ENUM.UNITEL]: {
    value: MASTER_MERCHANT_TYPE_ENUM.UNITEL,
  },
};

export const MASTER_MERCHANT_TYPE_TOPUP = Object.values(
  MASTER_MERCHANT_TYPE_TOPUP_CONST
);

export const MASTER_MERCHANT_TYPE_BILLING_CONST = {
  [MASTER_MERCHANT_TYPE_ENUM.ETL]: {
    value: MASTER_MERCHANT_TYPE_ENUM.ETL,
  },
  [MASTER_MERCHANT_TYPE_ENUM.BTC]: {
    value: MASTER_MERCHANT_TYPE_ENUM.BTC,
  },
  [MASTER_MERCHANT_TYPE_ENUM.LTC]: {
    value: MASTER_MERCHANT_TYPE_ENUM.LTC,
  },
  [MASTER_MERCHANT_TYPE_ENUM.UNITEL]: {
    value: MASTER_MERCHANT_TYPE_ENUM.UNITEL,
  },
};

export const MASTER_MERCHANT_TYPE_BILLING = Object.values(
  MASTER_MERCHANT_TYPE_BILLING_CONST
);

export enum MASTER_MERCHANT_NAME_STATUS {
  'ACTIVE' = 1,
  'INACTIVE' = 0,
}

export enum customErrorCode {
  errorCodeImportCustomer = 'MSG101058',
}

export enum tabVersionActive {
  ANDROID = 0,
  IOS = 1,
}

export const SAVING_TYPE_CONST = {
  FIXED: {
    label: 'model.manageSaving.fixed',
    value: 'FIXED',
  },
  ACCUMULATED: {
    label: 'model.manageSaving.accumulated',
    value: 'ACCUMULATED',
  },
  // NO_TERM: {
  //   label: 'model.manageSaving.no_term',
  //   value: 'NO_TERM',
  // },
};

export const PERIOD_TYPE_CONST = {
  M: {
    label: 'model.configSaving.m',
    value: 'M',
  },
};

export const TRANSFER_TYPE_CONST: MODEL_MAP_COMMON = {
  SETTLEMENT: {
    label: 'common.settlement',
    code: 'SETTLEMENT',
  },
  DEPOSIT_MORE: {
    label: 'common.deposit-more',
    code: 'DEPOSIT_MORE',
  },
  SAVING_ACCOUNT: {
    label: 'common.saving-account',
    code: 'SAVING_ACCOUNT',
  },
};

export const SAVING_TYPE = Object.values(SAVING_TYPE_CONST);
export const TRANSFER_TYPE = Object.values(TRANSFER_TYPE_CONST);
export const PERIOD_TYPE = Object.values(PERIOD_TYPE_CONST);

export const SAVING_TYPE_MAP = SAVING_TYPE.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.value]: { label: valueMap.label },
  }),
  {}
) as any;

export const PERIOD_TYPE_MAP = PERIOD_TYPE.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.value]: { label: valueMap.label },
  }),
  {}
) as any;

export const TRANSFER_TYPE_MAP = TRANSFER_TYPE.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.code]: { label: valueMap.label },
  }),
  {}
) as any;

export const CASHOUT_CONFIGURATION_TYPE_CONST = {
  AUTO: {
    label: 'common.deposit-more',
    code: 'AUTO',
  },
  MANUAL: {
    label: 'common.saving-account',
    code: 'MANUAL',
  },
};

export const CASHOUT_CONFIGURATION_TYPE = Object.values(
  CASHOUT_CONFIGURATION_TYPE_CONST
);

export const CASHOUT_CONFIGURATION_TYPE_MAP = CASHOUT_CONFIGURATION_TYPE.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.code]: { label: valueMap.label },
  }),
  {}
) as any;

export const ENTITY_TRANSACTION_MMONEY_TYPE_CONST: MODEL_MAP_COMMON = {
  WATER: {
    label: 'model.report.transaction.water',
    code: 'WATER',
    style: 'badge-success',
  },
  ELECTRIC: {
    label: 'model.report.transaction.electric',
    code: 'ELECTRIC',
    style: 'badge-success',
  },
};

export const TRANSACTION_MMONEY_ENTITY_TYPE = Object.values(
  ENTITY_TRANSACTION_MMONEY_TYPE_CONST
);

export const RATE_TYPE = {
  TYPE: 'INTEREST_RATE',
};
export const CATEGOTY_TYPE: MODEL_MAP_COMMON = {
  NEWS_INTERNAL: {
    label: 'model.news.newsInternal',
    code: 'label.category-internal',
  },
  NEWS_INTER: {
    label: 'model.news.newsInter',
    code: 'label.category-inter',
  },
};

export const CATEGORY = Object.values(CATEGOTY_TYPE);

export const CATEGORY_MAP = CATEGORY.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.code]: { label: valueMap.label },
  }),
  {}
) as any;

export const NEWSFILE_TYPE = {
  CLASSNAME: 'NEWS_CONTENT',
  RESOURCE: 'ICON',
};

export const FILE_TYPE = {
  FEE_SCHEDULE: 'FEE_SCHEDULE',
};

export const ENTITY_NOTIFICATION_LIMIT_STATUS_CONST: MODEL_MAP_COMMON = {
  CONTACTED: {
    label: 'common.contacted',
    code: 1,
    style: 'badge-success',
  },
  NO_CONTACT_YET: {
    label: 'common.notContacted',
    code: 0,
    style: 'badge-info',
  },
};

export const NOTIFICATION_LIMIT_ENTITY_STATUS = Object.values(
  ENTITY_NOTIFICATION_LIMIT_STATUS_CONST
);

export const NOTIFICATION_LIMIT_ENTITY_STATUS_MAP =
  NOTIFICATION_LIMIT_ENTITY_STATUS.reduce(
    (data, valueMap) => ({
      ...data,
      [valueMap.code]: { label: valueMap.label, style: valueMap.style },
    }),
    {}
  ) as any;

export const ENTITY_TRANSACTION_NOTIFICATION_TYPE_CONST: MODEL_MAP_COMMON = {
  TRANSFER_MONEY_INTERNAL: {
    label: 'model.report.transaction.internalBank',
    code: 'INTERNAL_BANK',
    style: 'badge-info',
  },
  TRANSFER_MONEY_INTER: {
    label: 'model.report.transaction.interBank',
    code: 'INTER_BANK',
    style: 'badge-info',
  },
  TOPUP: {
    label: 'model.report.transaction.topup',
    code: 'TOPUP',
    style: 'badge-success',
  },
  BILLING: {
    label: 'model.report.transaction.billing',
    code: 'BILLING',
    style: 'badge-success',
  },
  INSURANCE: {
    label: 'model.report.transaction.insurance',
    code: 'INSURANCE',
    style: 'badge-success',
  },
  CASH_IN: {
    label: 'model.report.transaction.cashIn',
    code: 'CASH_IN',
    style: 'badge-success',
  },
};

export const TRANSACTION_NOTIFICATION_ENTITY_TYPE = Object.values(
  ENTITY_TRANSACTION_NOTIFICATION_TYPE_CONST
);

export const TRANSACTION_NOTIFICATION_ENTITY_TYPE_MAP =
  TRANSACTION_NOTIFICATION_ENTITY_TYPE.reduce(
    (data, valueMap) => ({
      ...data,
      [valueMap.code]: { label: valueMap.label, style: valueMap.style },
    }),
    {}
  ) as any;

export const CONFIG_AUTO_REPORT_CONST = {
  MAIL_DEFAULT: 0,
  MAIL_DEFAULT_LENGTH: 1,
  MAIL_SCROLL_LENGTH: 5,
  MAIL_MAX_LENGTH: 50,
};

export const CONFIG_AUTO_TYPE_CONST = {
  TYPE_UMONEY: 'UMONEY',
  TYPE_LAPNET: 'LAPNET',
  TYPE_UMONEY_ON_BEHALF: 'UMONEY_ON_BEHALF',
  TYPE_NOTIFICATION_HISTORY: 'PREMIUM_ACC_NUMBER_CHARGE_FEE',
  TYPE_SMS_BALANCE_FEE_FAILED: 'SMS_BALANCE_CHARGE_FEE',
  ENTRUSTED_HISTORY: 'ENTRUSTED_HISTORY',
};

export const FILE_TYPE_CONST = [
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel',
];

export const FILE_TYPE_ACCEPT_CONST = {
  TYPE_ACCEPT: '.xlsx, .xls',
};

export const BG_COLOR_CONST = {
  COLOR: '#FFFFFF',
};

export const ENTITY_NUMBER_GROUP = {
  NUMBER_GROUP4: {
    label: 'model.structureAccount.group4',
    code: 4,
  },
  NUMBER_GROUP5: {
    label: 'model.structureAccount.group5',
    code: 5,
  },
  NUMBER_GROUP6: {
    label: 'model.structureAccount.group6',
    code: 6,
  },
  NUMBER_GROUP8: {
    label: 'model.structureAccount.group8',
    code: 8,
  },
  NUMBER_GROUP9: {
    label: 'model.structureAccount.group9',
    code: 9,
  },
};

export const ENTITY_NUMBER_GROUP_CONST = Object.values(ENTITY_NUMBER_GROUP);

export const CONFIG_ACCOUNT_CONFIG_CONST = {
  MAIL_DEFAULT: 0,
  MAIL_DEFAULT_LENGTH: 1,
  MAIL_SCROLL_LENGTH: 5,
  MAIL_MAX_LENGTH: 100,
};

export const PREMIUM_ACCOUNT_NUMBER_CONST = {
  DEFAULT_LENGTH: 0,
  MIN_INDEX: 1,
};

export const ORIGIN_CONST: MODEL_MAP_COMMON = {
  APP: {
    label: '',
    code: 'APP',
    style: '',
  },
  CMS: {
    label: '',
    code: 'CMS',
    style: '',
  },
};

export const CONFIG_PHONE_CONST = {
  PHONE_DEFAULT: 0,
  PHONE_DEFAULT_LENGTH: 1,
  PHONE_MAX_LENGTH: 10,
};

export const ENTITY_ORDER_CONST: MODEL_MAP_COMMON = {
  ASC: {
    label: 'ASC',
    code: 'ascend',
    style: 'badge-success',
  },
  DESC: {
    label: 'DESC',
    code: 'descend',
    style: 'badge-danger',
  },
};

export const CLIENT_TYPE_CONST: MODEL_MAP_COMMON = {
  INTERNAL: {
    label: 'client.internal',
    code: 'INTERNAL',
    style: 'badge-success',
  },
  INTER: {
    label: 'client.inter',
    code: 'INTER',
    style: 'badge-danger',
  },
};

export const CLIENT_TYPE = Object.values(CLIENT_TYPE_CONST);

export const CLIENT_TYPE_MAP = CLIENT_TYPE.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.code]: { label: valueMap.label, style: valueMap.style },
  }),
  {}
) as any;

export const DRCR_CONST: MODEL_MAP_COMMON = {
  D: {
    label: 'model.manageTransQuantity.debit',
    code: 'D',
  },
  C: {
    label: 'model.manageTransQuantity.credit',
    code: 'C',
  },
};

export const PREMIUM_ACC_NUMBER_CONST: MODEL_MAP_COMMON = {
  ACTIVE: {
    label: 'common.active',
    code: 1,
    style: 'badge-primary',
  },
  INACTIVE: {
    label: 'model.premiumAccountNumber.unKnown',
    code: 0,
    style: 'badge-secondary',
  },
  PENDING: {
    label: 'model.premiumAccountNumber.pending',
    code: 2,
    style: 'badge-warning',
  },
  UNCLOSED: {
    label: 'common.revert',
    code: 3,
    style: 'badge-danger',
  },
};

export const PREMIUM_ACC_NUMBER_STATUS = Object.values(
  PREMIUM_ACC_NUMBER_CONST
);

export const DRCR = Object.values(DRCR_CONST);

export const PREMIUM_ACC_NUMBER_STATUS_MAP = PREMIUM_ACC_NUMBER_STATUS.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.code]: { label: valueMap.label, style: valueMap.style },
  }),
  {}
) as any;

export const NUMBER_TYPE_CONST: MODEL_MAP_COMMON = {
  ALL: {
    label: 'common.all',
    code: 0,
    style: 'badge-primary',
  },
  HIDDEN: {
    label: 'model.specialAccountNumber.hiddenNumber',
    code: 2,
    style: 'badge-secondary',
  },
  SPECIAL: {
    label: 'model.structureAccount.special',
    code: 1,
    style: 'badge-warning',
  },
};

export const PREMIUM_ACC_TYPE_CONST: MODEL_MAP_COMMON = {
  PHONE_NUMBER: {
    label: 'model.premiumAccountNumber.accountType.phoneNumber',
    code: 0,
  },
  INTEREST: {
    label: 'model.premiumAccountNumber.accountType.interest',
    code: 1,
  },
};

export const NUMBER_TYPE_STATUS = Object.values(NUMBER_TYPE_CONST);
export const PREMIUM_ACC_TYPE = Object.values(PREMIUM_ACC_TYPE_CONST);

export const NUMBER_TYPE_STATUS_MAP = NUMBER_TYPE_STATUS.reduce(
  (data, valueMap) => ({
    ...data,
    [valueMap.code]: { label: valueMap.label, style: valueMap.style },
  }),
  {}
) as any;

export const TRANSACTION_DEBIT_DEPOSIT_ENTITY_TYPE_CONST = {
  DEBIT: {
    code: 'DEBIT',
    label: 'managePayment.debit',
  },
  DEPOSIT: {
    code: 'DEPOSIT',
    label: 'managePayment.deposit',
  },
};

export const TRANSACTION_DEBIT_DEPOSIT_ENTITY_TYPE = Object.values(
  TRANSACTION_DEBIT_DEPOSIT_ENTITY_TYPE_CONST
);
