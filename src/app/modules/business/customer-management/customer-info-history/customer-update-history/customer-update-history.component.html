<div class="row border-create">
  <h3>{{ "model.customer.updateHistory" | translate }}</h3>
  <div class="table-responsive cuh">
    <ngb-accordion #acc="ngbAccordion">
      <ngb-panel *ngFor="let item of customerInfo?.oldVersions; let i = index">
        <ng-template ngbPanelTitle>
          <span>{{i + 1}}. {{ 'model.customer.lastModifiedDate' | translate }}: <b>{{item.lastModifiedDate}}</b></span>
        </ng-template>
        <ng-template ngbPanelContent>
          <div class="row d-flex">
            <div class="mb-4 col-3">
              <label class="mb-1">{{ 'model.customer.fullname' | translate }}</label>
              <p class="mb-0"><b>{{ item.fullname }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label class="mb-1">{{ 'model.customer.cif' | translate }}</label>
              <p class="mb-0"><b>{{ item.cif }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.phoneNumber' | translate }}</label>
              <p class="mb-0"><b>{{ item.phoneNumber }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.idCardNumber' | translate }}</label>
              <p class="mb-0"><b>{{ item.idCardNumber }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.idCardType' | translate }}</label>
              <p class="mb-0"><b>{{ item.idCardTypeName }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.issueDate' | translate }}</label>
              <p class="mb-0"><b>{{ item.issueDate }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.issuePlace' | translate }}</label>
              <p class="mb-0"><b>{{ item.issuePlace }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.dateOfBirth' | translate }}</label>
              <p class="mb-0"><b>{{ item.dateOfBirth }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.placeOfOrigin' | translate }}</label>
              <p class="mb-0"><b>{{ item.placeOfOrigin }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.gender' | translate }}</label>
              <p class="mb-0"><b>{{ GENDER_MAP[item.gender || 0] | translate }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.statusOfResidence' | translate }}</label>
              <p class="mb-0"><b>{{ RESIDENT_STATUS_MAP[item.residentStatus || 0] | translate }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.nationCode' | translate }}</label>
              <p class="mb-0"><b>{{ item.nationName }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.nationCodeOther' | translate }}</label>
              <p class="mb-0"><b>{{ item.nationOtherName || ('common.not' | translate) }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.placeOfResidence' | translate }}</label>
              <p class="mb-0"><b>{{ item.placeOfResidence }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.placeOfResidenceOutCountry' | translate }}</label>
              <p class="mb-0"><b>{{ item.placeOfResidenceOutCountry || ('common.not' | translate) }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.currentAddress' | translate }}</label>
              <p class="mb-0"><b>{{ item.currentAddress }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.phoneContact' | translate }}</label>
              <p class="mb-0"><b>{{ item.phoneContact }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.email' | translate }}</label>
              <p class="mb-0"><b>{{ item.email || '---' }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.maritalStatus' | translate }}</label>
              <p class="mb-0"><b>{{ MARITAL_STATUS_MAP[item.maritalStatus || 0] | translate }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.job' | translate }}</label>
              <p class="mb-0"><b>{{ item.job || '---' }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.position' | translate }}</label>
              <p class="mb-0"><b>{{ item.position || '---' }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.createdDateAccountT24' | translate }}</label>
              <p class="mb-0"><b>{{ item.createdDate }}</b></p>
            </div>
            <div class="mb-4 col-3">
              <label>{{ 'model.customer.staffCode' | translate }}</label>
              <p class="mb-0"><b>{{ item.staffCode || '---' }}</b></p>
            </div>
            <div class="col-md-12">
              <label>{{ 'model.customer.identityCardImages' | translate }}</label>
              <app-upload-images [checkDisabled]="true" [imageUrls]="imageUrls.get(item.customerId)"
                [type]="ROUTER_ACTIONS.detail">
              </app-upload-images>
            </div>
          </div>
        </ng-template>
      </ngb-panel>
    </ngb-accordion>
    <div class="row d-block text-center m-0" *ngIf="!customerInfo?.oldVersions">
      <!-- <img
          src="/assets/dist/img/icon/empty.svg"
          height="120"
          alt="no_history_update_result"
        /> -->
      <p class="text-center mb-5">
        {{ "common.no_history_update_result" | translate }}
      </p>
    </div>
  </div>
</div>
