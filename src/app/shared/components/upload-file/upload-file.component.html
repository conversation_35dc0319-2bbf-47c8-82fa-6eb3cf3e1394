<div class="modal-content">
  <div class="modal-header">
    <h5 class="modal-title">{{ "Upload" | translate }}</h5>
    <button
      type="button"
      class="close"
      data-dismiss="modal"
      aria-label="Close"
      (click)="activeModal.close('')"
    >
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-md-12">
      <div class="row">
        <div class="col-12">
          <nz-upload
            nzType="drag"
            [nzMultiple]="multiple"
            [nzAccept]="acceptFiles"
            [nzShowUploadList]="true"
            [nzCustomRequest]="handleCustomUploadReq"
            [nzBeforeUpload]="beforeUpload"
            (nzChange)="handleChange($event)"
          >
            <p class="ant-upload-drag-icon">
              <img
                class="icon-upload"
                src="/assets/dist/img/icon-upload.svg"
                alt="welcome"
              />
            </p>
            <p class="ant-upload-text">
              {{ "common.selectFileXlsx" | translate }}
            </p>
            <p class="ant-upload-hint">
              {{ "common.maxSizeFile" | translate }}
            </p>
          </nz-upload>
        </div>
      </div>
    </div>
  </div>
  <div class="row mt-3">
    <div class="text-left pb-4 col-6" *ngIf="hasBtnDownloadTemplate">
      <button
        type="button"
        class="btn btn-secondary ml-4"
        (click)="downloadTemplate()"
      >
        {{ MODAL_ACTION.DOWNLOAD_TEMPLATE.label | translate }}
      </button>
    </div>
    <div class="text-right pb-4 col-6">
      <button
        type="button"
        class="btn btn-secondary mr-2"
        (click)="activeModal.close('')"
      >
        {{ MODAL_ACTION.CANCEL.label | translate }}
      </button>
      <button
        type="button"
        [disabled]="!hasFile"
        class="btn btn-danger mr-5"
        (click)="activeModal.close(files)"
      >
        {{ action.label | translate }}
      </button>
    </div>
  </div>
</div>
