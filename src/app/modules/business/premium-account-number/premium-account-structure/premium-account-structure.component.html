<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formSearch" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-md-6 col-lg-4">
            <div class="form-group">
              <label>{{ "model.structureAccount.name" | translate }}</label>
              <input trim type="text" placeholder="{{ 'model.structureAccount.name' | placeholder}}"
                formControlName="name" class="w-100" class="form-control"
                [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH" />
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label>{{ "model.structureAccount.length" | translate }}</label>
              <ng-select [items]="lengths" bindLabel="label" formControlName="structureLengths"
                (change)="onChangeLength($event)" bindValue="code" [multiple]="true" [clearable]="false"
                placeholder="{{ 'model.structureAccount.length' | placeholder : 'select' }}">
                <ng-template ng-header-tmp>
                  <div>
                    <button class="btn btn-link" type="button" (click)="onSelectAll(SELECTED_CONST.LENGTH_ID)">
                      {{ "common.action.selectAll" | translate }}
                    </button>
                    <button class="btn btn-link" type="button" (click)="onClearAll(SELECTED_CONST.LENGTH_ID)">
                      {{ "common.action.clearAll" | translate }}
                    </button>
                  </div>
                </ng-template>
                <ng-template ng-multi-label-tmp let-items="selectLengths">
                  <span *ngIf="selectLengths.length > 1" class="item-select">{{ firstLength?.label }} (+ {{
                    selectLengths.length -1
                    }})</span>
                  <span *ngIf="selectLengths.length === 1" class="item-select">
                    {{ firstLength?.label }}
                  </span>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="col-md-2 col-lg-2">
            <div class="form-group">
              <label>{{
                "model.structureAccount.numberStructure" | translate
                }}</label>
              <ng-select [items]="listStructureCode" bindLabel="numberStructure" bindValue="numberStructure"
                (change)="onChangeStructure($event)" [multiple]="true" appearance="outline" [searchable]="true"
                placeholder="{{
                  'model.structureAccount.numberStructure' | placeholder : 'select'
                }}" formControlName="numberStructures" [clearable]="true">
                <ng-template ng-header-tmp>
                  <div>
                    <button class="btn btn-link" type="button" (click)="onSelectAll(SELECTED_CONST.NUMBER_STRUCTURE)">
                      {{ "common.action.selectAll" | translate }}
                    </button>
                    <button class="btn btn-link" type="button" (click)="onClearAll(SELECTED_CONST.NUMBER_STRUCTURE)">
                      {{ "common.action.clearAll" | translate }}
                    </button>
                  </div>
                </ng-template>
                <ng-template ng-multi-label-tmp let-items="selectStructures">
                  <span class="item-select" *ngIf="selectStructures.length > 1">{{ firstStructure?.numberStructure }} (+
                    {{
                    selectStructures.length -1
                    }})</span>
                  <span *ngIf="selectStructures.length === 1" class="item-select">
                    {{ firstStructure?.numberStructure }}
                  </span>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="col-lg-2 col-md-2 mt-4 d-flex">
            <div class="col-btn-reset" ngbTooltip="{{ 'common.action.reset' | translate }}">
              <div class="btn-reset">
                <i class="bi bi-arrow-clockwise" type="button" (click)="onReset()">
                </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" type="submit" (click)="onSearch()" *hasPrivileges="
                  SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_STRUCTURE_READ
                ">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-flex justify-content-between align-items-end text-right mb-3 mt-4">
          <h5 class="title-table text-uppercase mb-4">
            {{ "model.structureAccount.structureTitle" | translate }}
          </h5>
          <div class="d-block text-right mb-3 mt-4">
            <button type="button" class="btn btn-white mr-2" (click)="onCreate()" *hasPrivileges="
                SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_STRUCTURE_CREATE
              ">
              {{ "common.action.create" | translate }}
            </button>
            <button type="button" class="btn btn-red mr-2" (click)="uploadFile()" *hasPrivileges="
                SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_STRUCTURE_IMPORT
              ">
              {{ "model.structureAccount.uploadFile" | translate }}
            </button>
            <button type="button" class="btn btn-white" (click)="onExport()" *hasPrivileges="
                SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_STRUCTURE_EXPORT
              ">
              {{ "common.action.export" | translate }}
            </button>
          </div>
        </div>
      </form>
      <div></div>
      <div class="table-responsive">
        <table class="table table-customer">
          <thead>
            <tr>
              <th class="text-center">{{ "model.rate.no" | translate }}</th>
              <th class="text-left">
                {{ "model.structureAccount.name" | translate }}
              </th>
              <th class="text-left">
                {{ "model.structureAccount.numberStructure" | translate }}
              </th>
              <th class="text-center" width="75px">
                {{ "model.structureAccount.length" | translate }}
              </th>
              <th class="text-right" width="140px">
                {{ "model.structureAccount.listPrice" | translate }}
              </th>
              <th class="text-center" width="110px">
                {{ "model.structureAccount.discount" | translate }}
              </th>
              <th class="text-right" width="160px">
                {{ "model.structureAccount.paymentPrice" | translate}}
              </th>
              <th class="text-left" width="125px">
                {{ "model.campaign.lastModifiedBy" | translate }}
              </th>
              <th class="text-center" width="120px">
                {{ "model.campaign.lastModifiedDate" | translate }}
              </th>
              <th class="text-left">
                {{ "model.campaign.createdBy" | translate }}
              </th>
              <th class="text-center" width="110px">
                {{ "common.createdDate" | translate }}
              </th>
              <th class="text-center status-freezer" scope="col">
                {{ "common.status" | translate }}
              </th>
              <th class="text-center freezer" scope="col">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of data; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td class="text-left" [title]="item?.name">
                {{ item?.name | limitWord : 30 }}
              </td>
              <td class="text-left">
                {{ item?.numberStructure }}
              </td>
              <td class="text-center">
                {{ item?.length + ' ' + ("common.number" | translate) }}
              </td>
              <td class="text-right">{{ item?.price | currencyLak }}</td>
              <td class="text-center">{{ item?.discount }}</td>
              <td class="text-right">{{ item?.totalPrice |currencyLak}}</td>
              <td class="text-left">
                {{ item?.lastModifiedBy }}
              </td>
              <td class="text-center">
                {{ item?.lastModifiedDate }}
              </td>
              <td class="text-left">{{ item?.createdBy }}</td>
              <td class="text-center">{{ item?.createdDate }}</td>
              <td class="text-center status-freezer-td">
                <span class="badge" [ngClass]="ENTITY_STATUS_MAP[item.status || 0].style">{{
                  ENTITY_STATUS_MAP[item.status || 0].label | translate
                  }}</span>
              </td>
              <td class="text-center freezer-td">
                <ng-container>
                  <button ngbTooltip="{{ 'common.action.detail' | translate }}" class="btn px-1 py-0"
                    (click)="detail(item.premiumAccountNumberStructureId)" *hasPrivileges="
                      SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_STRUCTURE_READ
                    ">
                    <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                  </button>
                  <button ngbTooltip="{{ 'common.action.update' | translate }}" class="btn px-1 py-0" *hasPrivileges="
                      SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_STRUCTURE_UPDATE
                    " (click)="update(item?.premiumAccountNumberStructureId)">
                    <i class="fa fa-edit mb-color" aria-hidden="true"></i>
                  </button>
                  <button [ngbTooltip]="
                      (item.status === ENTITY_STATUS_CONST.ACTIVE.code
                        ? 'common.action.lock'
                        : 'common.action.unlock'
                      ) | translate
                    " class="btn px-1 py-0" (click)="lockAndUnlock(item)" *hasPrivileges="
                      item.status === 1
                        ? SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_STRUCTURE_LOCK
                        : SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_STRUCTURE_UNLOCK
                    ">
                    <i [className]="
                        item.status === 1
                          ? 'fa fa-lock mb-color'
                          : 'fa fa-unlock mb-color'
                      " aria-hidden="true"></i>
                  </button>
                  <button ngbTooltip="{{ 'common.action.delete' | translate }}" class="btn px-1 py-0" *hasPrivileges="
                      SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_STRUCTURE_DELETE
                    " (click)="onDelete(item)">
                    <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                  </button>
                </ng-container>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="row d-block text-center m-0 no-search-result-wrapper" *ngIf="data?.length === 0">
          <img src="/assets/dist/img/icon/empty.svg" height="120" alt="no_search_result" />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
      </div>
      <div *ngIf="data?.length" class="paginator col-md-12">
        <mat-paginator [length]="formSearch.value.length" [pageSize]="formSearch.value.pageSize"
          [pageIndex]="formSearch.value.pageIndex" [pageSizeOptions]="pageSizeOptions" (page)="onChangePage($event)"
          aria-label="Select page">
        </mat-paginator>
      </div>
    </div>
  </div>
</section>