export interface IServicePack {
  servicePackId?: number;
  type?: string;
  clientId?: string;
  password?: string;
  status?: number;
  name?: string;
  code?: string;
  client?: IClient;
  servicePackFunctions?: IServicePackFunction[];
  carrierType?: string;
  createdBy?: string;
  createdDate?: string;
  lastModifiedBy?: string;
  lastModifiedDate?: string;
  fullName?: string;
  typeStr?: string;
}

export interface IClient {
  clientId?: string;
  status?: number;
  clientSecret?: string;
  name?: string;
  type?: string;
  servicePack?: IServicePack;
  servicePacks?: IServicePack[];
  createdBy?: string;
  createdDate?: string;
  lastModifiedBy?: string;
  lastModifiedDate?: string;
  fullName?: string;
}

export interface IServicePackType {
  clientId?: string;
  status?: number;
  clientSecret?: string;
  servicePack?: IServicePack;
  name?: string;
  transferType?: string;
}

export interface IServicePackFunction {
  name?: string;
  status?: number;
  url?: string;
  servicePackId?: number;
  servicePackFunctionId?: number;
  servicePack?: IServicePack;
}

export class Client implements IClient {
  constructor(data: IClient) {
    Object.assign(this, data);
  }
}

export class ServicePack implements IServicePack {
  constructor(data: IServicePack) {
    Object.assign(this, data);
  }
}
