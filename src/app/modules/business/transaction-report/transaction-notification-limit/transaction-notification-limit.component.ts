import { Component, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  Validators,
} from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { TranslateService } from '@ngx-translate/core';
import {
  CALCULATE_MILLISECONDS_IN_A_YEAR,
  DATE_RANGE_STATISTICAL_LOAN_ONLINE,
  ENTITY_NOTIFICATION_LIMIT_STATUS_CONST,
  ENTITY_STATUS,
  ENTITY_TRANSACTION_TYPE_CONST,
  FILE_EXTENSION,
  MODAL_ACTION,
  MOMENT_CONST,
  NOTIFICATION_LIMIT_ENTITY_STATUS,
  NOTIFICATION_LIMIT_ENTITY_STATUS_MAP,
  PAGINATION,
  TRANSACTION_NOTIFICATION_ENTITY_TYPE,
  TRANSACTION_NOTIFICATION_ENTITY_TYPE_MAP,
  TRANSACTION_TYPE_TRANSFER_MONEY_CONST,
  TRANSACTION_UMONEY_ENTITY_TYPE,
  TRANSACTION_UMONEY_ENTITY_TYPE_CONST,
  TRANSACTION_UMONEY_ENTITY_TYPE_MAP,
  TYPE_BANK_CODE_MB_CONST,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { SELECTED_CONST } from '@shared/constants/user.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { INotificationLimitTransaction } from '@shared/models/notification-limit-transaction.model';
import { NotificationLimitEnum } from '@shared/models/request/notification-limit.search';
import { ITransactionSearch } from '@shared/models/request/transaction.search';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { NotificationLimitService } from '@shared/services/notification-limit.service';
import { ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';

@Component({
  selector: 'app-transaction-notification-limit',
  templateUrl: './transaction-notification-limit.component.html',
  styleUrls: ['./transaction-notification-limit.component.scss'],
})
export class TransactionNotificationLimitComponent implements OnInit {
  // transactionReport: ILoanOnline[] = [];
  transactionReport: INotificationLimitTransaction[] = [];
  action: any = '';
  storage: any;
  maxDate = new Date();
  maxToDate = new Date();
  CONTACTED = ENTITY_NOTIFICATION_LIMIT_STATUS_CONST.CONTACTED.code;

  ROUTER_UTILS = ROUTER_UTILS;
  // input search
  pageSizeOptions = PAGINATION.OPTIONS;
  ENTITY_STATUS = ENTITY_STATUS;
  NOTIFICATION_LIMIT_ENTITY_STATUS_MAP = NOTIFICATION_LIMIT_ENTITY_STATUS_MAP;
  SYSTEM_RULES = SYSTEM_RULES;
  VALIDATORS = VALIDATORS;
  ENTITY_NOTIFICATION_LIMIT_STATUS_CONST =
    ENTITY_NOTIFICATION_LIMIT_STATUS_CONST;
  TRANSACTION_ENTITY_TYPE_MAP = TRANSACTION_NOTIFICATION_ENTITY_TYPE_MAP;
  ENTITY_TRANSACTION_TYPE_CONST = ENTITY_TRANSACTION_TYPE_CONST;
  TYPE_BANK_CODE_MB_CONST = TYPE_BANK_CODE_MB_CONST;
  TRANSACTION_ENTITY_TYPE = TRANSACTION_NOTIFICATION_ENTITY_TYPE;
  TRANSACTION_TYPE_TRANSFER_MONEY_CONST = TRANSACTION_TYPE_TRANSFER_MONEY_CONST;
  MOMENT_CONST = MOMENT_CONST;
  NOTIFICATION_LIMIT_ENTITY_STATUS = NOTIFICATION_LIMIT_ENTITY_STATUS;
  SELECTED_CONST = SELECTED_CONST;
  TRANSACTION_UMONEY_ENTITY_TYPE = TRANSACTION_UMONEY_ENTITY_TYPE;
  TRANSACTION_UMONEY_ENTITY_TYPE_MAP = TRANSACTION_UMONEY_ENTITY_TYPE_MAP;
  TRANSACTION_UMONEY_ENTITY_TYPE_CONST = TRANSACTION_UMONEY_ENTITY_TYPE_CONST;

  isValidMaxDate = (control: FormControl) => {
    const dateOfBirth = new Date(control.value).getTime();
    const maxDateOfBirth = new Date().getTime();
    return dateOfBirth > maxDateOfBirth ? { invalidMaxDate: true } : null;
  };

  // default form search
  formNotificationLimitSearch = this.fb.group({
    keyword: '',
    fromDate: [null, [Validators.required]],
    toDate: [null, [Validators.required, this.isValidMaxDate]],
    transferTypes: [],
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    // bankcode: '',
    hasPageable: true,
    timeZoneStr: '',
    statuses: [],
    notificationTypes: [],
    // previousPageIndex: 0,
  });

  constructor(
    private notificationLimitService: NotificationLimitService,
    private fb: FormBuilder,
    private downloadService: DownloadService,
    private translateService: TranslateService,
    private modalService: ModalService,
    private toastService: ToastrCustomService
  ) {
    // get session storage
    this.storage = sessionStorage.getItem(
      STORAGE_APP.TRANSACTION_NOTIFICATION_LIMIT_REPORT
    );
  }

  /**
   * Init data
   */
  ngOnInit(): void {
    // check storage
    // (when click back button or back website)
    this.formNotificationLimitSearch
      .get('timeZoneStr')
      ?.setValue(Intl.DateTimeFormat().resolvedOptions().timeZone);
    if (this.storage) {
      // get session storage and parse json
      const filter = JSON.parse(this.storage) as ITransactionSearch;
      // set value form control
      this.formNotificationLimitSearch.controls.keyword.setValue(
        filter.keyword
      );
      this.formNotificationLimitSearch.controls.fromDate.setValue(
        filter.fromDate
      );
      this.formNotificationLimitSearch.controls.toDate.setValue(filter.toDate);
      this.formNotificationLimitSearch.controls.transferTypes.setValue(
        filter.transferTypes
      );
      this.formNotificationLimitSearch.controls.statuses.setValue(
        filter.statuses
      );
      this.onSearch();
    } else {
      // set default value start date and end date
      const startDate = moment()
        .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
        .format(MOMENT_CONST.FORMAT_DEFAULT);
      const endDate = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
      // this.loanOnlineSearch.fromDate = startDate;
      // this.loanOnlineSearch.toDate = endDate;
      this.formNotificationLimitSearch.controls.fromDate.setValue(
        CommonUtils.reverseDate(startDate)
      );
      this.formNotificationLimitSearch.controls.toDate.setValue(
        CommonUtils.reverseDate(endDate)
      );
      this.onSearch();
    }
    // click form clear storage
    sessionStorage.removeItem(STORAGE_APP.TRANSACTION_REPORT);
  }

  /**
   * export file
   */
  exportFile(): void {
    // get value form control
    const bodySearch = this.formNotificationLimitSearch.getRawValue();
    if (this.formNotificationLimitSearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formNotificationLimitSearch);
    } else {
      // get file name
      const fileName = this.translateService.instant(
        'template.transactionNotifiLimitReport'
      );
      const obFile = this.notificationLimitService.export({
        fromDate: bodySearch.fromDate,
        toDate: bodySearch.toDate,
        keyword: bodySearch.keyword,
        transferTypes: bodySearch.transferTypes,
        statuses: bodySearch.statuses,
        timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
        notificationTypes: [NotificationLimitEnum.NOTIFICATION_LIMIT],
      });

      this.downloadService.downloadFileWithObservableAndName(
        obFile,
        fileName,
        FILE_EXTENSION.XLSX
      );
    }
  }

  /**
   * Change page: next page and preview page
   *
   * @param page PageEvent
   */
  onChangePage(page: PageEvent) {
    this.formNotificationLimitSearch.controls.pageIndex.setValue(
      page.pageIndex
    );
    this.formNotificationLimitSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  dateValidator(value: number): any {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return value > DATE_RANGE_STATISTICAL_LOAN_ONLINE
        ? { invalidDate: true }
        : null;
    };
  }

  changeValidDate() {
    if (
      this.formNotificationLimitSearch.controls.fromDate.value &&
      this.formNotificationLimitSearch.controls.toDate.value
    ) {
      if (this.formNotificationLimitSearch.controls['toDate'].value) {
        this.maxDate =
          this.formNotificationLimitSearch.controls['toDate'].value;
      }
      const startDate = CommonUtils.setStartTime(
        new Date(
          this.formNotificationLimitSearch.controls.fromDate.value
        ).getTime()
      );
      const endDate = CommonUtils.setStartTime(
        new Date(
          this.formNotificationLimitSearch.controls.toDate.value
        ).getTime()
      );
      const dayMax =
        (endDate - startDate) / CALCULATE_MILLISECONDS_IN_A_YEAR.DAY;
      if (dayMax > DATE_RANGE_STATISTICAL_LOAN_ONLINE) {
        this.formNotificationLimitSearch.controls.fromDate.setValidators([
          Validators.required,
          this.dateValidator(dayMax),
          this.isValidMaxDate,
        ]);
        this.formNotificationLimitSearch.controls.fromDate.updateValueAndValidity();
      } else {
        this.formNotificationLimitSearch.controls.fromDate.clearValidators();
        this.formNotificationLimitSearch.controls.fromDate.setValidators([
          Validators.required,
          this.isValidMaxDate,
        ]);
        this.formNotificationLimitSearch.controls.fromDate.updateValueAndValidity();
      }
    }
  }

  onSearchSubmit() {
    this.formNotificationLimitSearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.formNotificationLimitSearch.controls.length.setValue(
      PAGINATION.LENGTH_DEFAULT
    );
    this.onSearch();
  }

  /**
   * search form
   */
  onSearch(): void {
    if (this.formNotificationLimitSearch.valid) {
      this.formNotificationLimitSearch.controls.hasPageable.setValue(true);
      this.formNotificationLimitSearch.controls.notificationTypes.setValue([
        NotificationLimitEnum.NOTIFICATION_LIMIT,
      ]);
      this.notificationLimitService
        .search(this.formNotificationLimitSearch.value)
        .subscribe((res: any): void => {
          this.transactionReport = res.body.content;
          this.formNotificationLimitSearch.controls.length.setValue(
            res.body.totalElements
          );
        });
    }
  }

  /**
   * index on list loan online
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formNotificationLimitSearch.value.pageIndex,
      this.formNotificationLimitSearch.value.pageSize
    );
  }

  // checkValidateDate() {
  //   this.maxDate = this.formNotificationLimitSearch.controls['toDate'].value;
  // }

  /**
   * reset form
   */
  onReset(): void {
    // set default value date and reset data
    const startDateReset = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDateReset = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.formNotificationLimitSearch.controls.keyword.reset();
    this.formNotificationLimitSearch.controls.fromDate.setValue(
      CommonUtils.reverseDate(startDateReset)
    );
    this.formNotificationLimitSearch.controls.toDate.setValue(
      CommonUtils.reverseDate(endDateReset)
    );
    this.formNotificationLimitSearch.controls.fromDate.clearValidators();
    this.formNotificationLimitSearch.controls.fromDate.setValidators([
      Validators.required,
    ]);
    this.formNotificationLimitSearch.controls.fromDate.updateValueAndValidity();
    this.formNotificationLimitSearch.controls.transferTypes.setValue(null);
    this.formNotificationLimitSearch.controls.statuses.setValue(null);
    this.formNotificationLimitSearch
      .get('timeZoneStr')
      ?.setValue(Intl.DateTimeFormat().resolvedOptions().timeZone);
  }

  totalAmount(value = 0, fee = 0): number {
    return +value + +fee;
  }

  onSelectAll(value?: string) {
    const selectedTransferTypes = this.TRANSACTION_ENTITY_TYPE.map(
      (item) => item.code
    );
    const selectedTransactionStatus = this.NOTIFICATION_LIMIT_ENTITY_STATUS.map(
      (item) => item.code
    );
    switch (value) {
      case SELECTED_CONST.TRANSACTION_TYPE:
        this.formNotificationLimitSearch
          .get('transferTypes')
          ?.patchValue(selectedTransferTypes);
        break;
      case SELECTED_CONST.TRANSACTION_STATUS:
        this.formNotificationLimitSearch
          .get('statuses')
          ?.patchValue(selectedTransactionStatus);
        break;
    }
  }

  onClearAll(value?: string) {
    switch (value) {
      case SELECTED_CONST.TRANSACTION_TYPE:
        this.formNotificationLimitSearch.get('transferTypes')?.patchValue([]);
        break;
      case SELECTED_CONST.TRANSACTION_STATUS:
        this.formNotificationLimitSearch.get('statuses')?.patchValue([]);
        break;
    }
  }

  toggleActive(item: INotificationLimitTransaction): void {
    if (item.status === ENTITY_NOTIFICATION_LIMIT_STATUS_CONST.CONTACTED.code) {
      this.notContacted(item);
    } else {
      this.contacted(item);
    }
  }

  /**
   * Lock user register
   *
   * @param user IUser
   */
  private notContacted(item: INotificationLimitTransaction) {
    const modalData = {
      title: 'model.notificationLimit.notContacted',
      content: 'model.notificationLimit.notContactedContent',
      interpolateParams: {
        fullname: `<b>${item.customerAccountName || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.notificationLimitService
          .notContacted({ notificationLimitId: item.notificationLimitId })
          .subscribe((res) => {
            this.toastService.success('common.action.notContactedSuccess');
            this.onSearch();
          });
      }
    });
  }

  /**
   * UnLock user register
   *
   * @param user: IUser
   */
  private contacted(item: INotificationLimitTransaction) {
    const modalData = {
      title: 'model.notificationLimit.contacted',
      content: 'model.notificationLimit.contactedContent',
      interpolateParams: {
        fullname: `<b>${item.customerAccountName || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.notificationLimitService
          .contacted({ notificationLimitId: item.notificationLimitId })
          .subscribe((res) => {
            this.toastService.success('common.action.contactedSuccess');
            this.onSearch();
          });
      }
    });
  }
}
