import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ITransReportSearch } from '@shared/models/request/trans-report.search';
import { ITransReport } from '@shared/models/trans-report.model';
import { ITransactionFeeType } from '@shared/models/transaction-fee-type.model';
import {
  AbstractService,
  EntityResponseType,
} from '@shared/services/common/abstract.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class TransReportQuantityService extends AbstractService {
  public resourceUrl = '/report-transaction';

  constructor(protected http: HttpClient) {
    super(http);
  }

  search(request: ITransReportSearch): Observable<EntityResponseType<any>> {
    return super.post<EntityResponseType<ITransReport>>(
      `${this.resourceUrl}/search-number-transaction`,
      request
    );
  }

  export(request: ITransReportSearch): Observable<any> {
    return super.postFile(
      `${this.resourceUrl}/export-number-transaction`,
      request
    );
  }
}
