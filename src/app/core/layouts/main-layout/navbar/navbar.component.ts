import {
  Component,
  EventEmitter,
  HostListener,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
  Output,
} from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { environment } from '@env/environment';
import {
  ENTITY_STATUS_CONST,
  PAGINATION,
  TIME_RELOAD,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { LANGUAGES } from '@shared/constants/language.constants';
import {
  STORAGE_APP,
  STORAGE_LANGUAGES,
} from '@shared/constants/storage.constants';
import { TAB_CONST } from '@shared/constants/user.constants';
import { IAccount } from '@shared/models/account.model';
import { INotificationHistoryTransaction } from '@shared/models/notification-history-transaction.model';
import { INotificationLimitTransaction } from '@shared/models/notification-limit-transaction.model';
import { Notification } from '@shared/models/notification.model';
import { NotificationLimitEnum } from '@shared/models/request/notification-limit.search';
import { IUserStorage, UserStorage } from '@shared/models/user.model';
import { AuthProviderService } from '@shared/services/auth/auth-provider.service';
import { AuthenticationService } from '@shared/services/auth/authentication.service';
import { LanguageService } from '@shared/services/helpers/language.service';
import { NotificationHistoryService } from '@shared/services/notification-history.service';
import { NotificationLimitService } from '@shared/services/notification-limit.service';
import { ROUTER_UTILS } from '@shared/utils';
import { getMessaging, onMessage } from 'firebase/messaging';
import { LocalStorageService } from 'ngx-webstorage';
import { Subject, interval } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import * as SockJS from 'sockjs-client';
import * as Stomp from 'stompjs';

@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.scss'],
})
export class NavbarComponent implements OnInit, OnDestroy {
  @Input() isCollapsed = false;
  @Output() collapsedEvent = new EventEmitter<boolean>();

  message: any;
  stompClient: any;
  showDropdown = false;
  activeTab = TAB_CONST.ALL;
  webSocketEndPoint = environment.apiUrl + '/ws';
  topicNotification = '/topic/notification/';
  newNotificationTotal: any;
  pageTitle = '';

  LANGUAGES = Object.values(LANGUAGES);
  TAB_CONST = TAB_CONST;
  currentLanguage = '';

  totalRecord = 0;
  isShow = false;
  isShowUnread = false;
  isShowRead = false;
  pageSize = PAGINATION.PAGE_SIZE_DEFAULT_MINI;
  pageIndex = PAGINATION.PAGE_NUMBER_DEFAULT;

  pageIndexUnread = PAGINATION.PAGE_NUMBER_DEFAULT;
  pageSizeUnread = PAGINATION.PAGE_SIZE_DEFAULT_MINI;

  pageIndexRead = PAGINATION.PAGE_NUMBER_DEFAULT;
  pageSizeRead = PAGINATION.PAGE_SIZE_DEFAULT_MINI;

  pageSizeOptions = PAGINATION.OPTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;

  notifications: INotificationHistoryTransaction[] = [];
  notificationUnread: INotificationHistoryTransaction[] = [];
  notificationRead: INotificationHistoryTransaction[] = [];
  notificationsPremium: INotificationLimitTransaction[] = [];
  notificationsSmsBalance: INotificationLimitTransaction[] = [];

  userInfo: IUserStorage = new UserStorage();
  interval$ = interval(TIME_RELOAD);
  unSubscribe$ = new Subject<void>();

  constructor(
    private authProviderService: AuthProviderService,
    private notificationLimitService: NotificationLimitService,
    private notificationHistoryService: NotificationHistoryService,
    private languageHelper: LanguageService,
    private router: Router,
    private localStorage: LocalStorageService,
    private authenticationService: AuthenticationService
  ) {}

  ngOnInit(): void {
    this.getNotificationOfMe();
    this.currentLanguage =
      this.localStorage.retrieve(STORAGE_LANGUAGES) || LANGUAGES.VI.code;
    this.pageTitle = this.languageHelper.getPageTitle(
      this.router.routerState.snapshot.root
    );

    this.userInfo = this.localStorage.retrieve(STORAGE_APP.USER_INFO);

    this.router.events.subscribe((event: any) => {
      if (event instanceof NavigationEnd) {
        this.pageTitle = this.languageHelper.getPageTitle(
          this.router.routerState.snapshot.root
        );
      }
    });
    this.interval$.pipe(takeUntil(this.unSubscribe$)).subscribe(() => {
      this.getNotificationOfMe();
    });
  }

  ngOnDestroy(): void {
    this.unSubscribe$.next();
    this.unSubscribe$.complete();
    this.unSubscribe$.unsubscribe();
  }

  // on error, schedule a reconnection attempt
  errorCallBack(error: string) {
    // setTimeout(() => {
    //   this.connectSocket();
    // }, 3000);
  }

  logout(): void {
    this.authProviderService.logout();
  }

  collapseMenu(value: boolean) {
    this.collapsedEvent.emit(value);
  }

  listen() {
    const messaging = getMessaging();
    onMessage(messaging, (payload) => {
      this.message = payload;
      const data = payload?.data;
      const notification = new Notification();
      notification.title = data?.title;
      notification.content = data?.content;
      // this.notifications.unshift(notification);
      this.newNotificationTotal += 1;
    });
  }

  connectSocket() {
    const currentAccount = this.localStorage.retrieve(
      STORAGE_APP.USER_INFO
    ) as IAccount;
    if (currentAccount) {
      const ws = new SockJS(this.webSocketEndPoint);
      this.stompClient = Stomp.over(ws);
      this.stompClient.connect(
        {},
        () => {
          this.stompClient.subscribe(
            this.topicNotification + currentAccount?.userId,
            (payload: any) => {
              const data = JSON.parse(payload?.body);
              const notification = new Notification();
              notification.title = data?.title;
              notification.content = data?.body;
              // this.notifications.unshift(notification);
              this.newNotificationTotal += 1;
            }
          );
          // _this.stompClient.reconnect_delay = 2000;
        },
        this.errorCallBack
      );
    }
  }

  getNotificationOfMe() {
    if (
      this.authenticationService.hasAnyPrivileges([
        SYSTEM_RULES.NOTIFICATION_LIMIT_READ,
        SYSTEM_RULES.NOTIFICATION_HISTORY_READ,
      ])
    ) {
      this.countUnread();
      this.searchUnread();
      this.searchAll();
      this.searchRead();
    }
  }

  loadAllData(): void {
    this.pageIndex = (this.pageIndex || PAGINATION.PAGE_NUMBER_DEFAULT) + 1;

    const params = {
      pageIndex: this.pageIndex,
      pageSize: this.pageSize,
      total: this.totalRecord,
      notificationTypes: [
        NotificationLimitEnum.NOTIFICATION_LIMIT,
        NotificationLimitEnum.PREMIUM_ACCOUNT_NUMBER_REVERT,
        NotificationLimitEnum.SMS_BALANCE_CHANGE_FAILED,
      ],
      sort: [],
      hasPageable: true,
      timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
    };
    this.notificationHistoryService
      .notification(params)
      .subscribe((res: any) => {
        this.notifications = [...this.notifications, ...res?.body?.content];
        this.isShow = !(res?.body?.totalPages === this.pageIndex + 1);
      });
  }

  loadUnreadData(): void {
    this.pageIndexUnread =
      (this.pageIndexUnread || PAGINATION.PAGE_NUMBER_DEFAULT) + 1;

    const params = {
      pageIndex: this.pageIndexUnread,
      pageSize: this.pageSizeUnread,
      total: this.totalRecord,
      notificationTypes: [
        NotificationLimitEnum.NOTIFICATION_LIMIT,
        NotificationLimitEnum.PREMIUM_ACCOUNT_NUMBER_REVERT,
        NotificationLimitEnum.SMS_BALANCE_CHANGE_FAILED,
      ],
      status: 0,
      sort: [],
      hasPageable: true,
      timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
    };
    this.notificationHistoryService
      .notification(params)
      .subscribe((res: any) => {
        this.notificationUnread = [
          ...this.notificationUnread,
          ...res?.body?.content,
        ];
        this.isShowUnread = !(
          res?.body?.totalPages ===
          this.pageIndexUnread + 1
        );
      });
  }

  loadReadData(): void {
    this.pageIndexRead =
      (this.pageIndexRead || PAGINATION.PAGE_NUMBER_DEFAULT) + 1;

    const params = {
      pageIndex: this.pageIndexRead,
      pageSize: this.pageSizeRead,
      total: this.totalRecord,
      notificationTypes: [
        NotificationLimitEnum.NOTIFICATION_LIMIT,
        NotificationLimitEnum.PREMIUM_ACCOUNT_NUMBER_REVERT,
        NotificationLimitEnum.SMS_BALANCE_CHANGE_FAILED,
      ],
      status: 1,
      sort: [],
      hasPageable: true,
      timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
    };
    this.notificationHistoryService
      .notification(params)
      .subscribe((res: any) => {
        this.notificationRead = [
          ...this.notificationRead,
          ...res?.body?.content,
        ];
        this.isShowRead = !(res?.body?.totalPages === this.pageIndexRead + 1);
      });
  }

  getNotificationCount(): string {
    const limit = this.newNotificationTotal?.numberNotificationLimit || 0;
    const revert = this.newNotificationTotal?.numberPremiumAccountRevert || 0;
    const smsBalance =
      this.newNotificationTotal?.numberSmsBalanceChargeFail || 0;
    const total = limit + revert + smsBalance;
    return total > 99 ? '99+' : total.toString();
  }

  countUnread(): void {
    const params = {
      statuses: [0],
    };
    this.notificationLimitService.countUnread(params).subscribe((res) => {
      if (!!res.body) {
        this.newNotificationTotal = res.body;
      }
    }, this.errorCallBack);
  }

  /**
   * Change lang
   *
   * @param {string} lang
   * @memberof NavbarComponent
   */
  onChangeLanguage(lang: string): void {
    if (lang !== this.currentLanguage) {
      this.localStorage.store(STORAGE_LANGUAGES, lang);
      location.reload();
    }
  }

  onProfile(): void {
    this.router.navigate([ROUTER_UTILS.user.profile]);
  }

  onConfigSavingManagement() {
    this.router.navigate([
      ROUTER_UTILS.transaction.transactionNotificationLimit.root,
    ]);
  }

  onRouterPremiumAccountRevert() {
    this.router.navigate([
      ROUTER_UTILS.transaction.premiumAccountRevertNotificationLimit.root,
    ]);
  }

  onRouterSmsBalanceChangeFailed() {
    this.router.navigate([
      ROUTER_UTILS.managementServiceFeeFailed.managementSMSBalanceFeeFailed
        .root,
    ]);
  }

  closeDropdown() {
    this.showDropdown = false;
  }

  @HostListener('document:click', ['$event'])
  onClickOutside(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (!target.closest('.noti-wrapper')) {
      this.closeDropdown();
    }
  }

  toggleNotification() {
    this.showDropdown = !this.showDropdown;
  }

  filterNotifications() {
    if (this.activeTab === TAB_CONST.ALL) {
      return this.notifications;
    }

    if (this.activeTab === TAB_CONST.UNREAD) {
      return this.notificationUnread;
    }

    return this.notificationRead;
  }

  changeTab(type?: any) {
    this.activeTab = type || TAB_CONST.ALL;
    this.filterNotifications();
  }

  searchAll() {
    const params = {
      pageIndex: this.pageIndex,
      pageSize: this.pageSize,
      total: this.totalRecord,
      notificationTypes: [
        NotificationLimitEnum.NOTIFICATION_LIMIT,
        NotificationLimitEnum.PREMIUM_ACCOUNT_NUMBER_REVERT,
        NotificationLimitEnum.SMS_BALANCE_CHANGE_FAILED,
      ],
      sort: [],
      hasPageable: true,
      timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
    };
    this.notificationHistoryService
      .notification(params)
      .subscribe((res: any) => {
        this.notifications = res?.body?.content;
        this.isShow = !(res?.body?.totalPages === this.pageIndex + 1);
      });
  }

  searchUnread() {
    const params = {
      pageIndex: this.pageIndexUnread,
      pageSize: this.pageSizeUnread,
      total: this.totalRecord,
      notificationTypes: [
        NotificationLimitEnum.NOTIFICATION_LIMIT,
        NotificationLimitEnum.PREMIUM_ACCOUNT_NUMBER_REVERT,
        NotificationLimitEnum.SMS_BALANCE_CHANGE_FAILED,
      ],
      status: 0,
      sort: [],
      hasPageable: true,
      timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
    };
    this.notificationHistoryService
      .notification(params)
      .subscribe((res: any) => {
        this.notificationUnread = res?.body?.content;
        this.isShowUnread = !(res?.body?.totalPages === this.pageIndexUnread + 1);
      });
  }

  searchRead() {
    const params = {
      pageIndex: this.pageIndexRead,
      pageSize: this.pageSizeRead,
      total: this.totalRecord,
      notificationTypes: [
        NotificationLimitEnum.NOTIFICATION_LIMIT,
        NotificationLimitEnum.PREMIUM_ACCOUNT_NUMBER_REVERT,
        NotificationLimitEnum.SMS_BALANCE_CHANGE_FAILED,
      ],
      status: 1,
      sort: [],
      hasPageable: true,
      timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
    };
    this.notificationHistoryService
      .notification(params)
      .subscribe((res: any) => {
        this.notificationRead = res?.body?.content;
        this.isShowRead = !(res?.body?.totalPages === this.pageIndexRead + 1);
      });
  }
}
