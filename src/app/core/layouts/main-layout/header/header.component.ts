import { Component, OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { LanguageService } from '@shared/services/helpers/language.service';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
})
export class HeaderComponent implements OnInit {
  title = '';
  constructor(private languageHelper: LanguageService, private router: Router) {}

  ngOnInit(): void {
    this.title = this.languageHelper.getPageTitle(
      this.router.routerState.snapshot.root
    );
    this.router.events.subscribe((event: any) => {
      if (event instanceof NavigationEnd) {
        this.title = this.languageHelper.getPageTitle(
          this.router.routerState.snapshot.root
        );
      }
    });
  }
}
