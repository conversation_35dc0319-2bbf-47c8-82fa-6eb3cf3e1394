import { createSelector } from '@ngxs/store';

export abstract class AbstractState {

  static entities<E>() {
    return createSelector([this], (state: { entities: E[] }) => {
      return state.entities;
    });
  }

  static entity<E>() {
    return createSelector([this], (state: { entity: E }) => {
      return state.entity;
    });
  }

  static filter<F>() {
    return createSelector([this], (state: { filter: F }) => {
      return state.filter;
    });
  }

}
