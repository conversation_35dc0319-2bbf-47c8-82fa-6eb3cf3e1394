import { Component, OnInit, ViewChild } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { EditorComponent } from '@shared/components/editor/editor.component';
import {
  FILE_UPLOAD_EXTENSIONS,
  MODAL_ACTION,
} from '@shared/constants/app.constants';
import { LANGUAGES } from '@shared/constants/language.constants';
import { STORAGE_LANGUAGES } from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ICategory } from '@shared/models/category.model';
import { IInformationTemplate } from '@shared/models/information-template.model';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { InformationTemplateService } from '@shared/services/information-template.service';
import CommonUtils from '@shared/utils/common-utils';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils/router.utils';
import { LocalStorageService } from 'ngx-webstorage';
@Component({
  selector: 'app-create-update-information-template',
  templateUrl: './create-update-information-template.component.html',
  styleUrls: ['./create-update-information-template.component.scss'],
})
export class CreateUpdateInformationTemplateComponent implements OnInit {
  formCreate: FormGroup = new FormGroup({});
  ROUTER_UTILS = ROUTER_UTILS;
  VALIDATORS = VALIDATORS;
  FILE_UPLOAD_EXTENSIONS = FILE_UPLOAD_EXTENSIONS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  STORAGE_LANGUAGES = STORAGE_LANGUAGES;
  LANGUAGES = Object.values(LANGUAGES);
  language = '';
  informationTemplateId?: number;
  action?: string;
  data: IInformationTemplate = {};
  categoryList?: ICategory[] = [];
  @ViewChild('content') content: EditorComponent | any;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private informationTemplateService: InformationTemplateService,
    private localStorage: LocalStorageService,
    private toastService: ToastrCustomService,
    private modalService: ModalService
  ) {
    this.activatedRoute.paramMap.subscribe((res) => {
      const idParam = res.get('informationTemplateId');
      if (idParam) {
        this.informationTemplateId = +idParam;
      }
    });
    this.activatedRoute.data.subscribe((res) => {
      this.action = res.action;
    });
    this.language =
      this.router?.getCurrentNavigation()?.extras.state?.language ||
      this.localStorage.retrieve(STORAGE_LANGUAGES);
  }

  ngOnInit(): void {
    if (this.informationTemplateId) {
      this.informationTemplateService
        .detail({ informationTemplateId: this.informationTemplateId })
        .subscribe((res: any) => {
          this.data = res?.body;
          this.initForm(this.getData(res?.body?.informationTemplateContents));
        });
    } else {
      this.initForm();
    }
  }

  getData(data?: any) {
    return data?.find(
      (item: IInformationTemplate) => item.language === this.language
    );
  }

  initForm(data?: IInformationTemplate): void {
    this.formCreate = this.formBuilder.group({
      informationTemplateCode: [
        this.data.informationTemplateCode || '',
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.POSITION_SHORT_NAME),
          Validators.maxLength(
            VALIDATORS.LENGTH.INFORMATION_TEMPLATE_CODE_MAX_LENGTH
          ),
        ],
      ],
      informationTemplateName: [
        data?.informationTemplateName || '',
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.NO_SPECIAL_CHARACTERS),
          Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
        ],
      ],
      displayName: [
        data?.displayName || '',
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.NO_SPECIAL_CHARACTERS),
          Validators.maxLength(
            VALIDATORS.LENGTH.INFORMATION_TEMPLATE_DISPLAY_NAME_MAX_LENGTH
          ),
        ],
      ],
      content: [
        data?.content || '',
        [
          Validators.required,
          Validators.maxLength(
            VALIDATORS.LENGTH.INFORMATION_TEMPLATE_MAX_LENGTH
          ),
        ],
      ],
    });
    this.content?.setValue(data?.content);
  }

  onChangeData(type: string, content: any): void {
    this.formCreate.get(type)?.setValue(content);
  }

  onChangeLang(language: string) {
    if (
      this.action === ROUTER_ACTIONS.create &&
      (this.formCreate.value.informationTemplateCode ||
        this.formCreate.value.informationTemplateName ||
        this.formCreate.value.displayName ||
        this.formCreate.value.content) &&
      language !== this.language
    ) {
      const modalData = {
        title: 'model.news.modal.title',
        content: 'model.news.modal.content',
      };
      this.modalService.confirm(modalData).then((result) => {
        if (result === MODAL_ACTION.CONFIRM.code) {
          this.getDataLang(language);
        }
      });
    } else if (language !== this.language) {
      this.getDataLang(language);
    }
  }

  getDataLang(language: string) {
    this.language = language;
    this.formCreate.reset();
    this.initForm(this.getData(this.data?.informationTemplateContents));
  }

  backToList(): void {
    this.router.navigate([ROUTER_UTILS.sysManage.informationTemplate.root]);
  }

  onSubmit() {
    if (this.formCreate.invalid) {
      CommonUtils.markFormGroupTouched(this.formCreate);
      return;
    }
    const body = this.formCreate.value;
    body.language = this.language;
    body.informationTemplateId = this.informationTemplateId
      ? this.informationTemplateId
      : '';
    body.informationTemplateContentId =
      this.getData(this.data?.informationTemplateContents)
        ?.informationTemplateContentId || '';
    const serviceEvent =
      this.action === ROUTER_ACTIONS.update
        ? this.informationTemplateService.update(body)
        : this.informationTemplateService.create(body);
    serviceEvent.subscribe((res: any) => {
      if (res) {
        this.action === ROUTER_ACTIONS.create
          ? this.router.navigate(
              [
                ROUTER_UTILS.sysManage.informationTemplate.root,
                res?.body?.informationTemplateId,
                ROUTER_ACTIONS.update,
              ],
              {
                state: {
                  language: this.language,
                },
              }
            )
          : this.router.navigate([
              ROUTER_UTILS.sysManage.informationTemplate.root,
            ]);
      }
      this.toastService.success(
        this.action === ROUTER_ACTIONS.update
          ? 'common.action.updateSuccess'
          : 'common.action.createSuccess'
      );
    });
  }

  getTemplate(data: string) {
    this.formCreate.patchValue({
      content: data.split(VALIDATORS.PATTERN.SPACE_HTML).join(' '),
    });
  }
}
