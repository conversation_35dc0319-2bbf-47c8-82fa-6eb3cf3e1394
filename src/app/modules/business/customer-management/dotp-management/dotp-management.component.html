<div class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="searchForm" (submit)="onSearch()">
        <div class="row">
          <div class="col-lg-2">
            <div class="form-group">
              <label>{{ "common.action.searchKeyword" | translate }}</label>
              <input
                trim
                class="form-control w-100"
                formControlName="keyword"
                type="text"
                placeholder="{{ 'common.action.searchKeyword' | placeholder }}"
                [maxlength]="VALIDATORS.LENGTH.KEYWORD_MAX_LENGTH"
              />
            </div>
          </div>
          <div class="col-lg-2">
            <div class="form-group">
              <label>{{ "model.manageSaving.status" | translate }}</label>
              <ng-select
                placeholder="{{ 'model.manageSaving.status' | translate }}"
                [searchable]="false"
                formControlName="status"
                [clearable]="true"
              >
                <ng-option
                  [value]="item.code"
                  *ngFor="let item of ENTITY_STATUS"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
              (click)="onReset()"
            >
              <div class="btn-reset">
                <i
                  class="bi bi-arrow-clockwise"
                  type="button"
                  *hasPrivileges="SYSTEM_RULES.SMS_BALANCE_READ"
                >
                </i>
              </div>
            </div>
            <div class="">
              <button
                class="btn btn-search mr-2"
                type="submit"
                *hasPrivileges="SYSTEM_RULES.SMS_BALANCE_READ"
              >
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div
          class="d-flex justify-content-between align-items-end text-right mb-3 mt-4"
        >
          <h5 class="title-table">
            {{ "dotp.title" | translate }}
          </h5>
          <button
            class="btn btn-red"
            type="button"
            *hasPrivileges="SYSTEM_RULES.SMS_BALANCE_EXPORT"
            (click)="exportFile()"
          >
            {{ "common.action.export" | translate }}
          </button>
        </div>
      </form>

      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th class="text-center">
                {{ "model.manageSaving.no" | translate }}
              </th>
              <th class="text-right">
                {{ "dotp.table.cifNumber" | translate }}
              </th>
              <th class="text-left">
                {{ "dotp.table.customerName" | translate }}
              </th>
              <th class="text-right">
                {{ "dotp.table.phoneNumber" | translate }}
              </th>
              <th class="text-left">
                {{ "dotp.table.deviceId" | translate }}
              </th>
              <th class="text-right">
                {{ "dotp.table.registrationDate" | translate }}
              </th>
              <th class="text-center">
                {{ "dotp.table.cancelDate" | translate }}
              </th>
              <th class="text-center">
                {{ "common.status" | translate }}
              </th>
              <th class="text-center">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of data; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td class="text-right">{{ item.cifNumber }}</td>
              <td class="text-left">{{ item.customerName }}</td>
              <td class="text-right">{{ item?.deviceId }}</td>
              <td class="text-left">{{ item?.phoneNumber }}</td>
              <td class="text-right">{{ item?.phoneNumber }}</td>
              <td class="text-center">{{ item?.registrationDate }}</td>
              <td class="text-center">{{ item?.cancelDate }}</td>
              <td class="text-center">
                <span
                  class="badge"
                  [ngClass]="ENTITY_STATUS_MAP[item?.status || 0].style"
                >
                  {{ ENTITY_STATUS_MAP[item?.status || 0].label | translate }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
        <div
          class="row d-block text-center m-0 no-search-result-wrapper"
          *ngIf="data?.length === 0"
        >
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
      </div>
      <div *ngIf="data?.length">
        <mat-paginator
          [length]="searchForm.value.length"
          [pageSize]="searchForm.value.pageSize"
          [pageIndex]="searchForm.value.pageIndex"
          [pageSizeOptions]="pageSizeOptions"
          (page)="onChangePage($event)"
          aria-label="Select page"
        >
        </mat-paginator>
      </div>
    </div>
  </div>
</div>
