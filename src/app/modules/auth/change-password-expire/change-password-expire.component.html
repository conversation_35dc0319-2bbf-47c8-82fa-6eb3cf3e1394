<main class="bg-login d-flex align-items-center min-vh-100 py-3 py-md-0">
  <app-change-language></app-change-language>
  <div class="wrap-login">
    <div class="login-card">
      <div class="row no-gutters">
        <div class="col-lg-12 col-md-12">
          <div class="card-body">
            <!-- <div class="brand-wrapper text-center">
              <img src="../../assets/dist/img/mblogo.png" alt="logo login" />
            </div> -->
            <div class="brand-wrapper text-center">
              <strong class="title">{{
                "common.action.changePassword" | translate
              }}</strong>
            </div>
            <form [formGroup]="userChangePasswordForm">
              <div class="input mb-3">
                <div class="form-group position-relative">
                  <label
                    >{{ "model.user.currentPassword" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    trim
                    #currentPassword
                    [type]="currentPasswordVisible ? 'text' : 'password'"
                    class="w-100"
                    formControlName="currentPassword"
                    class="form-control"
                    placeholder="{{ 'model.user.currentPassword' | translate }}"
                  />
                  <span
                    class="icon-eye cursor-pointer"
                    (click)="onChangeCurrentPasswordVisible()"
                  >
                    <i
                      [classList]="
                        currentPasswordVisible ? 'bi-eye-slash' : 'bi bi-eye'
                      "
                    ></i>
                  </span>
                </div>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userChangePasswordForm.get('currentPassword')?.errors
                      ?.required &&
                    userChangePasswordForm.get('currentPassword')?.touched
                  "
                >
                  {{ "error.user.required.currentPassword" | translate }}
                </small>
              </div>
              <div class="input mb-3">
                <div class="form-group position-relative">
                  <label
                    >{{ "model.user.newPassword" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    trim
                    #password
                    [type]="passwordVisible ? 'text' : 'password'"
                    class="w-100"
                    formControlName="password"
                    class="form-control"
                    placeholder="{{ 'model.user.newPassword' | translate }}"
                  />
                  <span
                    class="icon-eye cursor-pointer"
                    (click)="onChangePasswordVisible()"
                  >
                    <i
                      [classList]="
                        passwordVisible ? 'bi-eye-slash' : 'bi bi-eye'
                      "
                    ></i>
                  </span>
                </div>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userChangePasswordForm.get('password')?.errors?.required &&
                    userChangePasswordForm.get('password')?.touched
                  "
                >
                  {{ "error.user.required.newPassword" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userChangePasswordForm.get('password')?.errors?.minlength &&
                    userChangePasswordForm.get('password')?.touched
                  "
                >
                  {{
                    "error.user.minLength.newPassword"
                      | translate
                        : {
                            param: VALIDATORS.LENGTH.PASSWORD_MIN_LENGTH
                          }
                  }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userChangePasswordForm.get('password')?.errors?.maxlength &&
                    userChangePasswordForm.get('password')?.touched
                  "
                >
                  {{
                    "error.user.maxLength.newPassword"
                      | translate
                        : {
                            param: VALIDATORS.LENGTH.PASSWORD_MAX_LENGTH
                          }
                  }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userChangePasswordForm.get('password')?.errors?.pattern &&
                    userChangePasswordForm.get('password')?.touched
                  "
                >
                  {{ "error.user.pattern.newPassword" | translate }}
                </small>
              </div>
              <div class="input">
                <div class="form-group position-relative">
                  <label
                    >{{ "model.user.confirmPassword" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    trim
                    #confirmPassword
                    [type]="confirmPasswordVisible ? 'text' : 'password'"
                    class="w-100"
                    formControlName="confirmPassword"
                    class="form-control"
                    placeholder="{{ 'model.user.confirmPassword' | translate }}"
                  />
                  <span
                    class="icon-eye cursor-pointer"
                    (click)="onChangeConfirmPasswordVisible()"
                  >
                    <i
                      [classList]="
                        confirmPasswordVisible ? 'bi-eye-slash' : 'bi bi-eye'
                      "
                    ></i>
                  </span>
                </div>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userChangePasswordForm.get('confirmPassword')?.errors
                      ?.required &&
                    userChangePasswordForm.get('confirmPassword')?.touched
                  "
                >
                  {{ "error.user.required.confirmPassword" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userChangePasswordForm.get('confirmPassword')?.errors
                      ?.minlength &&
                    userChangePasswordForm.get('confirmPassword')?.touched
                  "
                >
                  {{
                    "error.user.minLength.confirmPassword"
                      | translate
                        : {
                            param: VALIDATORS.LENGTH.PASSWORD_MIN_LENGTH
                          }
                  }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userChangePasswordForm.get('confirmPassword')?.errors
                      ?.maxlength &&
                    userChangePasswordForm.get('confirmPassword')?.touched
                  "
                >
                  {{
                    "error.user.maxLength.confirmPassword"
                      | translate
                        : {
                            param: VALIDATORS.LENGTH.PASSWORD_MAX_LENGTH
                          }
                  }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userChangePasswordForm.get('confirmPassword')?.errors
                      ?.pattern &&
                    userChangePasswordForm.get('confirmPassword')?.touched
                  "
                >
                  {{ "error.user.pattern.confirmPassword" | translate }}
                </small>
                <span
                  class="text-danger"
                  *ngIf="f.confirmPassword.errors?.matching"
                >
                  <small>{{
                    "public.forgotPassword.error.notMatch" | translate
                  }}</small>
                </span>
              </div>
              <div class="text-left mb-5 mt-3">
                <div
                  class="footer"
                  [innerHTML]="
                    displayContent('public.forgotPassword.footerChangePassword')
                  "
                ></div>
              </div>
              <button
                class="btn btn-block login-btn mb-4"
                (click)="onSetUpPassword()"
              >
                {{ "common.action.next" | translate }}
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>
