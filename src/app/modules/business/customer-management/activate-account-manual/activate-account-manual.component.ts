import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  CALCULATE_MILLISECONDS_IN_A_YEAR,
  DATE_RANGE_STATISTICAL_LOAN_ONLINE,
  FILE_EXTENSION,
  LIMIT_LENGTH_WORD_CONST,
  MAX_DATE_CONST,
  MODAL_ACTION,
  MOMENT_CONST,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import {
  CUSTOMER_ACTIVE_STATUS,
  CUSTOMER_ACTIVE_STATUS_CONST,
  CUSTOMER_ACTIVE_STATUS_MAP,
  CUSTOMER_TYPE,
  CUSTOMER_TYPE_MAP,
} from '@shared/constants/customer.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { <PERSON><PERSON>ustomer } from '@shared/models/customer.model';
import { IEventSearch } from '@shared/models/request/event.search';
import { CustomerService } from '@shared/services/customer.service';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import CommonUtils from '@shared/utils/common-utils';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils/router.utils';
import moment from 'moment';

@Component({
  selector: 'app-activate-manual',
  templateUrl: './activate-account-manual.component.html',
  styleUrls: ['./activate-account-manual.component.scss'],
})
export class ActiveAccountManualComponent implements OnInit {
  data: ICustomer[] = [];
  eventSearch: IEventSearch = {};
  pageSizeOptions = PAGINATION.OPTIONS;
  LIMIT_LENGTH_WORD_CONST = LIMIT_LENGTH_WORD_CONST;
  CUSTOMER_ACTIVE_STATUS = CUSTOMER_ACTIVE_STATUS;
  CUSTOMER_ACTIVE_STATUS_MAP = CUSTOMER_ACTIVE_STATUS_MAP;
  CUSTOMER_ACTIVE_STATUS_CONST = CUSTOMER_ACTIVE_STATUS_CONST;
  SYSTEM_RULES = SYSTEM_RULES;
  VALIDATORS = VALIDATORS;
  CUSTOMER_TYPE = CUSTOMER_TYPE;
  CUSTOMER_TYPE_MAP = CUSTOMER_TYPE_MAP;
  MAX_DATE_CONST = MAX_DATE_CONST;
  checked = false;
  indeterminate = false;
  setOfCheckedId = new Set();
  listOfCurrentPageData: readonly ICustomer[] = [];
  onShowButtonAndCol = true;
  maxDate = new Date();
  isErrorStartDateGreaterEndDate = false;

  constructor(
    private fb: FormBuilder,
    private toastService: ToastrCustomService,
    private router: Router,
    private modalService: ModalService,
    private customerService: CustomerService,
    private translateService: TranslateService,
    private downloadService: DownloadService
  ) {}

  formSearch = this.fb.group({
    cif: ['', [Validators.pattern(VALIDATORS.PATTERN.CIF_SEARCH)]],
    phoneNumber: ['', [Validators.pattern(VALIDATORS.PATTERN.PHONE_SEARCH)]],
    idCardNumber: '',
    email: '',
    accountType: null,
    activeStatus: null,
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    hasPageable: true,
    fromDate: [null, [Validators.required]],
    toDate: [null, [Validators.required]],
  });

  ngOnInit(): void {
    const startDate = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDate = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.eventSearch.fromTime = startDate;
    this.eventSearch.toTime = endDate;
    this.formSearch.controls.fromDate.setValue(
      CommonUtils.reverseDate(startDate)
    );
    this.formSearch.controls.toDate.setValue(CommonUtils.reverseDate(endDate));
    this.onSearch();
  }

  onSearch(): void {
    const body = {
      ...this.formSearch.value,
      createdDateStartAt: this.formSearch.value.fromDate,
      createdDateEndAt: this.formSearch.value.toDate,
    };
    if (this.formSearch.valid) {
      this.customerService
        .searchActivateAccount(body)
        .subscribe((res: any): void => {
          this.data = res.body.content;
          this.onShowButtonAndCol = res?.body?.content?.some(
            (item: ICustomer) => {
              return (
                item?.activeStatus ===
                CUSTOMER_ACTIVE_STATUS_CONST.INACTIVATED.value
              );
            }
          );
          this.formSearch.controls.length.setValue(res.body.totalElements);
        });
    }
  }

  onDetail(customer: ICustomer) {
    this.router.navigate([
      ROUTER_UTILS.customer.root,
      ROUTER_UTILS.customer.activateAccountManual.root,
      customer.customerId,
      ROUTER_ACTIONS.view,
    ]);
  }

  onSearchSubmit(): void {
    this.onSearch();
  }

  onChangePage(page: any) {
    this.formSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  onReset(): void {
    const startDateReset = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDateReset = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.formSearch.controls.cif.reset();
    this.formSearch.controls.phoneNumber.reset();
    this.formSearch.controls.idCardNumber.reset();
    this.formSearch.controls.email.reset();
    this.formSearch.controls.accountType.reset();
    this.formSearch.controls.activeStatus.reset();
    this.formSearch.controls.hasPageable.setValue(true);
    this.formSearch.controls.fromDate.setValue(
      CommonUtils.reverseDate(startDateReset)
    );
    this.formSearch.controls.toDate.setValue(
      CommonUtils.reverseDate(endDateReset)
    );
    this.formSearch.controls.fromDate.clearValidators();
    this.formSearch.controls.fromDate.setValidators([Validators.required]);
    this.formSearch.controls.fromDate.updateValueAndValidity();
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formSearch.value.pageIndex,
      this.formSearch.value.pageSize
    );
  }

  onActiveAccount(data?: any): void {
    const customerIds =
      data instanceof Set ? Array.from(data) : [data?.customerId];
    if (customerIds.length > 0) {
      const modalData = {
        title: 'model.customer.activateAccountManual.activateAccountTitle',
        content: 'model.customer.activateAccountManual.activateAccountContent',
      };
      this.modalService.confirm(modalData).then((result) => {
        if (result === MODAL_ACTION.CONFIRM.code) {
          this.customerService
            .activateAccount(customerIds)
            .subscribe((): void => {
              this.toastService.success('common.action.activateSuccess');
              this.onSearch();
            });
        }
      });
    } else {
      this.toastService.warning(
        'model.customer.activateAccountManual.alertNotification'
      );
    }
  }

  exportFile(): void {
    const bodySearch = this.formSearch.getRawValue();
    if (this.formSearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formSearch);
    } else {
      const fileName = this.translateService.instant(
        'template.activateAccountManual'
      );
      const obFile = this.customerService.exportActivateAccount({
        ...bodySearch,
        createdDateStartAt: this.formSearch.value.fromDate,
        createdDateEndAt: this.formSearch.value.toDate,
      });
      this.downloadService.downloadFileWithObservableAndName(
        obFile,
        fileName,
        FILE_EXTENSION.XLSX
      );
    }
  }

  changeValidDate() {
    if (
      this.formSearch.controls.fromDate.value &&
      this.formSearch.controls.toDate.value
    ) {
      if (this.formSearch.controls['toDate'].value) {
        this.maxDate = this.formSearch.controls['toDate'].value;
      }
      const fromDate = CommonUtils.setStartTime(
        new Date(this.formSearch.controls.fromDate.value).getTime()
      );
      const toDate = CommonUtils.setStartTime(
        new Date(this.formSearch.controls.toDate.value).getTime()
      );
      const dayMax = (toDate - fromDate) / CALCULATE_MILLISECONDS_IN_A_YEAR.DAY;
      if (dayMax > DATE_RANGE_STATISTICAL_LOAN_ONLINE) {
        this.formSearch.controls.fromDate.setValidators([
          Validators.required,
          this.dateValidator(dayMax),
        ]);
        this.formSearch.controls.fromDate.updateValueAndValidity();
      } else {
        this.formSearch.controls.fromDate.clearValidators();
        this.formSearch.controls.fromDate.setValidators([Validators.required]);
        this.formSearch.controls.fromDate.updateValueAndValidity();
      }
    }
    this.changeEndDate();
  }

  changeEndDate() {
    if (this.formSearch.controls['toDate'].value) {
      this.maxDate = this.formSearch.controls['toDate'].value;
    }
    const dataSearch = this.formSearch.getRawValue();
    if (dataSearch.fromDate && dataSearch.toDate) {
      const startDateSearch = new Date(dataSearch.fromDate);
      const endDateSearch = new Date(dataSearch.toDate);
      this.isErrorStartDateGreaterEndDate =
        startDateSearch.getTime() > endDateSearch.getTime();
    }
  }

  dateValidator(value: number): any {
    return (control: AbstractControl): { [key: string]: any } | null => {
      return value > DATE_RANGE_STATISTICAL_LOAN_ONLINE
        ? { invalidDate: true }
        : null;
    };
  }

  /**
   * Tick one customer and Tick all customer
   */

  onCurrentPageDataChange(listOfCurrentPageData: readonly ICustomer[]): void {
    this.listOfCurrentPageData = listOfCurrentPageData;
    this.refreshCheckedStatus();
  }

  refreshCheckedStatus(): void {
    const listOfEnabledData = this.listOfCurrentPageData.filter(
      (item) =>
        item?.activeStatus === CUSTOMER_ACTIVE_STATUS_CONST.INACTIVATED.value
    );
    this.checked = listOfEnabledData?.every(({ customerId }) =>
      this.setOfCheckedId.has(customerId || 0)
    );
    this.indeterminate =
      listOfEnabledData.some(({ customerId }) =>
        this.setOfCheckedId.has(customerId || 0)
      ) && !this.checked;
  }

  onItemChecked(customerId?: number, checked?: boolean): void {
    if (customerId) {
      this.updateCheckedSet(customerId, checked);
      this.refreshCheckedStatus();
    }
  }

  onAllChecked(checked?: boolean): void {
    this.indeterminate = false;
    this.data.forEach((item: ICustomer) => {
      if (
        item?.activeStatus === CUSTOMER_ACTIVE_STATUS_CONST.INACTIVATED.value
      ) {
        this.updateCheckedSet(item.customerId || 0, checked);
      }
    });
    this.refreshCheckedStatus();
  }

  updateCheckedSet(customerId: number, checked?: boolean): void {
    if (checked) {
      this.setOfCheckedId.add(customerId);
    } else {
      this.setOfCheckedId.delete(customerId);
    }
  }
}
