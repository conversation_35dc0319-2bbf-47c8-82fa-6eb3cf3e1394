import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ROUTER_UTILS } from '@shared/utils';
import { ActiveUserComponent } from './active-user/active-user.component';
import { ForgotPasswordSetUpComponent } from './forgot-password/forgot-password-set-up/forgot-password-set-up.component';
import { ForgotPasswordVerifyComponent } from './forgot-password/forgot-password-verify/forgot-password-verify.component';
import { ForgotPasswordComponent } from './forgot-password/forgot-password.component';

export const PORTAL_ROUTES: Routes = [
  {
    path: `${ROUTER_UTILS.portal.verify}/${ROUTER_UTILS.portal.userActive.token}`,
    component: ActiveUserComponent,
  },
  {
    path: `${ROUTER_UTILS.portal.forgotPassword.root}`,
    component: ForgotPasswordComponent,
  },
  {
    path: `${ROUTER_UTILS.portal.forgotPassword.root}/${ROUTER_UTILS.portal.forgotPassword.verify}`,
    component: ForgotPasswordVerifyComponent,
  },
  {
    path: `${ROUTER_UTILS.portal.forgotPassword.setUpPw}`,
    component: ForgotPasswordSetUpComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(PORTAL_ROUTES)],
  exports: [RouterModule],
})
export class PortalRoutingModule {}
