import { IDocument } from './file.model';

export interface ICampaign {
  campaignId?: number;
  campaignName?: string;
  startDate?: string;
  endDate?: string;
  embedLink?: string[];
  description?: number;
  status?: number;
  file?: IDocument;
  fileUrls?: any[];
  banners?: IDocument[];
  createdDate?: string;
  createdBy?: string;
  lastModifiedDate?: string;
  lastModifiedBy?: string;
  loginScreen?: number;
  homepageScreen?: number;
  position?: string;
  files?: any[];
}

export class Campaign implements ICampaign {
  constructor(
    public campaignId?: number,
    public campaignName?: string,
    public startDate?: string,
    public endDate?: string,
    public embedLink?: string[],
    public description?: number,
    public status?: number,
    public file?: IDocument,
    public fileUrls?: any[],
    public banners?: IDocument[],
    public createdDate?: string,
    public createdBy?: string,
    public lastModifiedDate?: string,
    public lastModifiedBy?: string,
    public position?: string,
    public files?: any[]
  ) {
    this.campaignId = campaignId;
    this.campaignName = campaignName;
    this.startDate = startDate;
    this.endDate = endDate;
    this.embedLink = embedLink;
    this.status = status;
    this.description = description;
    this.file = file;
    this.fileUrls = fileUrls;
    this.banners = banners;
    this.createdDate = createdDate;
    this.createdBy = createdBy;
    this.lastModifiedDate = lastModifiedDate;
    this.lastModifiedBy = lastModifiedBy;
    this.position = position;
    this.files = files;
  }
}
