<div>
  <div class="row border-create">
    <h3>{{ "model.customer.accountsList" | translate }}</h3>
    <div class="table-responsive">
      <table class="table">
        <thead>
          <tr>
            <th class="text-center" scope="col">#</th>
            <!-- <th scope="col">
              {{
                "customerRegisterManagement.customerApproval.detail.accountName"
                  | translate
              }}
            </th> -->
            <th scope="col">
              {{
                "customerRegisterManagement.customerApproval.detail.accountNumber"
                  | translate
              }}
            </th>
            <th scope="col">
              {{
                "customerRegisterManagement.customerApproval.detail.accountType"
                  | translate
              }}
            </th>
            <th scope="col">{{ "model.customer.default" | translate }}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let dataItem of moneyAccounts; let i = index">
            <td class="text-center">{{ fillIndexItem(i) }}</td>
            <!-- <td>{{ dataItem.customerName }}</td> -->
            <td>{{ dataItem.accountNumber }}</td>
            <td>{{ dataItem.accountTypeLabel }}</td>
            <td>
              <mat-radio-button
                [checked]="dataItem.isDefault"
                [disabled]="true"
              ></mat-radio-button>
            </td>
          </tr>
        </tbody>
      </table>
      <div
        class="row d-block text-center m-0"
        *ngIf="moneyAccounts?.length === 0"
      >
        <!-- <img
          src="/assets/dist/img/icon/empty.svg"
          height="120"
          alt="no_list_account_result"
        /> -->
        <p class="text-center mb-5">
          {{ "common.no_list_account_result" | translate }}
        </p>
      </div>
    </div>
  </div>
  <div class="row border-create">
    <h3>{{ "model.customer.usageInformation" | translate }}</h3>
    <div class="col-md-12">
      <form [formGroup]="formInfo" *ngIf="customerInfo?.customerId">
        <div class="col-md-12">
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <label>{{ "model.customer.username" | translate }}</label>
                <input
                  trim
                  formControlName="username"
                  type="text"
                  class="w-100"
                  class="form-control"
                />
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label>{{ "model.customer.phoneNumber" | translate }}</label>
                <input
                  trim
                  formControlName="phoneNumber"
                  type="text"
                  class="w-100"
                  class="form-control"
                />
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label>{{ "model.customer.email" | translate }}</label>
                <input
                  trim
                  formControlName="email"
                  type="text"
                  class="w-100"
                  class="form-control"
                />
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label>{{
                  "model.customer.createdDateAccount" | translate
                }}</label>
                <input
                  trim
                  formControlName="createdDateAccount"
                  type="text"
                  class="w-100"
                  class="form-control"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-12">
          <div class="row">
            <!-- <div class="col-md-3">
              <div class="form-group">
                <label>{{ "model.customer.approvedBy" | translate }}</label>
                <input trim formControlName="approvedBy" type="text" class="w-100" class="form-control" />
              </div>
            </div> -->
            <div class="col-md-3">
              <div class="form-group">
                <label>{{ "common.status" | translate }}</label>
                <ng-select
                  appearance="outline"
                  [searchable]="false"
                  placeholder="{{ 'common.status' | placeholder : 'select' }}"
                  [clearable]="true"
                  formControlName="status"
                >
                  <ng-option
                    [value]="item.code"
                    *ngFor="let item of ENTITY_STATUS"
                  >
                    {{ item.label | translate }}
                  </ng-option>
                </ng-select>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
    <div class="col-md-12">
      <hr class="divider" />
    </div>
    <h4 class="">
      {{ "model.customer.activatedHistory" | translate }}
    </h4>
    <div class="table-responsive">
      <table class="table">
        <thead>
          <tr>
            <th scope="col" class="text-center">
              {{ "model.customer.activatedDate" | translate }}
            </th>
            <th scope="col">{{ "model.customer.imei" | translate }}</th>
            <th scope="col">
              {{ "model.customer.deviceId" | translate }}
            </th>
            <th scope="col">
              {{ "model.customer.operatingSystem" | translate }}
            </th>
            <th scope="col">
              {{ "model.customer.deviceName" | translate }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngIf="activationHistory?.activationTime">
            <td class="text-center">{{ activationHistory.activationTime }}</td>
            <td>{{ activationHistory.deviceImei }}</td>
            <td>{{ activationHistory.deviceId }}</td>
            <td>{{ activationHistory.deviceOs }}</td>
            <td>{{ activationHistory.deviceName }}</td>
          </tr>
        </tbody>
      </table>
      <div
        class="row d-block text-center m-0"
        *ngIf="!activationHistory?.activationTime"
      >
        <!-- <img
          src="/assets/dist/img/icon/empty.svg"
          height="120"
          alt="no_list_history_active_result"
        /> -->
        <p class="text-center mb-5">
          {{ "common.no_list_history_active_result" | translate }}
        </p>
      </div>
    </div>
    <div class="col-md-12">
      <hr class="divider" />
    </div>
    <h4 class="">
      {{ "model.customer.updateServiceHistory" | translate }}
    </h4>
    <div class="table-responsive">
      <table class="table">
        <thead>
          <tr>
            <th scope="col">#</th>
            <th scope="col">{{ "model.customer.action" | translate }}</th>
            <th scope="col">
              {{ "model.customer.editedBy" | translate }}
            </th>
            <th scope="col">
              {{ "model.customer.requestedTime" | translate }}
            </th>
            <th scope="col">
              {{ "model.customer.approvedBy" | translate }}
            </th>
            <th scope="col">
              {{ "model.customer.approvedDate" | translate }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let dataItem of updateHistory; let i = index">
            <td>{{ fillIndexItem(i) }}</td>
            <td>
              {{ APPROVAL_TYPE_MAP[dataItem.approvalType || 0] | translate }}
            </td>
            <td>{{ dataItem.sentUserName }}</td>
            <td>{{ dataItem.sentDate }}</td>
            <td>{{ dataItem.approvalUserName }}</td>
            <td>{{ dataItem.createdDate }}</td>
          </tr>
        </tbody>
      </table>
      <div
        class="row d-block text-center m-0"
        *ngIf="updateHistory?.length === 0"
      >
        <!-- <img
          src="/assets/dist/img/icon/empty.svg"
          height="120"
          alt="no_list_history_update_result"
        /> -->
        <p class="text-center mb-5">
          {{ "common.no_list_history_update_result" | translate }}
        </p>
      </div>
    </div>
  </div>
</div>
