{"name": "ckeditor5-custom-build", "author": "CKSource", "description": "A custom CKEditor 5 build made by the CKEditor 5 online builder.", "version": "0.0.1", "license": "SEE LICENSE IN LICENSE.md", "private": true, "main": "./build/ckeditor.js", "devDependencies": {"@ckeditor/ckeditor5-alignment": "^27.1.0", "@ckeditor/ckeditor5-autosave": "^27.1.0", "@ckeditor/ckeditor5-basic-styles": "^27.1.0", "@ckeditor/ckeditor5-block-quote": "^27.1.0", "@ckeditor/ckeditor5-code-block": "^27.1.0", "@ckeditor/ckeditor5-dev-utils": "^24.4.2", "@ckeditor/ckeditor5-dev-webpack-plugin": "^24.4.2", "@ckeditor/ckeditor5-editor-classic": "^27.1.0", "@ckeditor/ckeditor5-essentials": "^27.1.0", "@ckeditor/ckeditor5-font": "^27.1.0", "@ckeditor/ckeditor5-heading": "^27.1.0", "@ckeditor/ckeditor5-highlight": "^27.1.0", "@ckeditor/ckeditor5-image": "^27.1.0", "@ckeditor/ckeditor5-indent": "^27.1.0", "@ckeditor/ckeditor5-link": "^27.1.0", "@ckeditor/ckeditor5-list": "^27.1.0", "@ckeditor/ckeditor5-media-embed": "^27.1.0", "@ckeditor/ckeditor5-mention": "^27.1.0", "@ckeditor/ckeditor5-paragraph": "^27.1.0", "@ckeditor/ckeditor5-paste-from-office": "^27.1.0", "@ckeditor/ckeditor5-remove-format": "^27.1.0", "@ckeditor/ckeditor5-special-characters": "^27.1.0", "@ckeditor/ckeditor5-table": "^27.1.0", "@ckeditor/ckeditor5-theme-lark": "^27.1.0", "@ckeditor/ckeditor5-typing": "^27.1.0", "css-loader": "^5.2.4", "postcss": "^8.2.10", "postcss-loader": "^4.2.0", "raw-loader": "^4.0.2", "style-loader": "^2.0.0", "terser-webpack-plugin": "^4.2.3", "webpack": "^4.46.0", "webpack-cli": "^4.6.0"}, "scripts": {"build": "webpack --mode production"}, "dependencies": {"ckeditor5-custom-build": "file:"}}