import { Clipboard } from '@angular/cdk/clipboard';
import {
  Component,
  ElementRef,
  HostListener,
  OnD<PERSON>roy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { SafeUrl } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  APP_NAME_CONST,
  BUTTON_ACTION_CONST,
  ENTITY_TYPE_MAP,
  MOMENT_CONST,
} from '@shared/constants/app.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { IReferral } from '@shared/models/referral.model';
import { ReferralService } from '@shared/services/referral.service';
import { ROUTER_UTILS } from '@shared/utils';
import moment from 'moment';

import * as pdfFonts from 'pdfmake/build/vfs_fonts';
declare let pdfMake: any;
pdfMake.vfs = pdfFonts.pdfMake.vfs;

@Component({
  selector: 'app-referral-management-detail',
  templateUrl: './referral-management-detail.component.html',
  styleUrls: ['./referral-management-detail.component.scss'],
})
export class ReferralDetailManagementComponent implements OnInit, OnDestroy {
  qrCode = '';
  action = '';
  isUpdate = false;
  hasFilter = false;
  qrCodeDownloadLink: SafeUrl = '';
  referral: IReferral = {};
  referralDto: IReferral = {};
  BUTTON_ACTION_CONST = BUTTON_ACTION_CONST;
  ENTITY_TYPE_MAP = ENTITY_TYPE_MAP;

  @ViewChild('qrElement')
  qrCodeElement: ElementRef | any;

  constructor(
    private fb: FormBuilder,
    private formBuilder: FormBuilder,
    private router: Router,
    private referralService: ReferralService,
    private activatedRoute: ActivatedRoute,
    private translate: TranslateService,
    private clipboard: Clipboard
  ) {
    this.activatedRoute.data.subscribe((res) => {
      this.action = res.action;
    });
    // get id form paramater
    this.activatedRoute.paramMap.subscribe((res) => {
      const idParam = res.get('referralId');
      if (idParam) {
        this.referralDto.referralId = +idParam;
        this.isUpdate = true;
      }
    });
  }

  formReferralDetail = this.fb.group({
    userFullName: '',
    userCode: '',
    phoneNumber: '',
    referralCode: '',
    type: '',
    rmCode: '',
    rmLink: '',
    quantity: '0',
  });

  /**
   * remove storage
   */
  ngOnDestroy(): void {
    if (!this.hasFilter) {
      sessionStorage.removeItem(STORAGE_APP.REFERRAL);
    }
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.hasFilter = true;
  }

  ngOnInit(): void {
    this.getDetail();
    this.formReferralDetail.disable();
  }

  /**
   * init form
   *
   * @param referral User
   */
  initForm(referral?: IReferral): void {
    this.formReferralDetail = this.formBuilder.group({
      userFullName: [referral?.userFullName || ''],
      phoneNumber: [referral?.phoneNumber || ''],
      type: [
        this.translate.instant(ENTITY_TYPE_MAP[referral?.type || ''].label),
      ],
      userCode: [referral?.userCode || ''],
      rmCode: [referral?.rmCode || ''],
      referralCode: [referral?.referralCode || ''],
      rmLink: [referral?.rmLink || ''],
      qrCode: [referral?.qrCode || ''],
      quantity: [referral?.quantity || '0'],
    });
  }

  backToList(): void {
    this.router.navigate([ROUTER_UTILS.referral.root]);
  }

  getImage(event: any) {}

  onChangeURL(url: SafeUrl) {
    this.qrCodeDownloadLink = url;
  }

  getDetail(): void {
    if (this.referralDto.referralId) {
      this.referralService.detail(this.referralDto).subscribe((res: any) => {
        this.referral = res.body;
        const data = res.body as IReferral;
        if (data.qrCode?.qrCodeValue) {
          this.qrCode = data.qrCode?.qrCodeValue;
        }
        this.initForm(data);
      });
    }
  }

  copyText() {
    const textToCopy = this.formReferralDetail.get('rmLink')?.value;
    this.clipboard.copy(textToCopy);
  }

  /**
   * export qr code merchant
   *
   * @param action BUTTON_ACTION_CONST
   */
  generatePdf(action: string): void {
    /**
     * Case 1
     */
    const docDefinition = {
      compress: false,
      watermark: {
        text: APP_NAME_CONST,
        angle: 30,
        opacity: 0.1,
        bold: true,
        italics: true,
      },
      info: {
        author: APP_NAME_CONST,
        title:
          APP_NAME_CONST +
          ' | ' +
          this.referral.userFullName +
          ' [' +
          this.referral.referralCode +
          ']',
      },
      defaultStyle: {
        font: 'Roboto',
      },
      content: [
        {
          text: this.referral.referralCode,
          style: 'content',
        },
        {
          text: this.referral.rmLink,
          style: 'content',
        },
        {
          qr: this.qrCode,
          foreground: '#000000',
          background: '#ffffff',
          fit: 500,
          alignment: 'center',
          margin: [0, 0, 0, 0],
          mask: 1,
          ecLevel: 'H',
        },
      ],
      styles: {
        name: {
          fontSize: 16,
          bold: true,
          alignment: 'center',
        },
        content: {
          bold: true,
          fontSize: 20,
          alignment: 'center',
          margin: [30, 0, 30, 20],
        },
      },
    };

    switch (action) {
      case BUTTON_ACTION_CONST.OPEN:
        pdfMake.createPdf(docDefinition).open();
        break;
      case BUTTON_ACTION_CONST.PRINT:
        pdfMake.createPdf(docDefinition).print();
        break;
      case BUTTON_ACTION_CONST.DOWNLOAD:
        pdfMake
          .createPdf(docDefinition)
          .download(
            `QrCode_${this.referral.referralCode}_${moment(Date.now()).format(
              MOMENT_CONST.TIMESTAMP_FORMAT
            )}.pdf`
          );
        break;
      default:
        pdfMake.createPdf(docDefinition).open();
        break;
    }
  }
}
