import { Component, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import {
  CONFIG_TRANS_STATUS_CONST,
  MODAL_ACTION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { CampaignService } from '@shared/services/campaign.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS } from '@shared/utils';
@Component({
  selector: 'app-modal-delete-banner',
  templateUrl: './modal-delete-banner.component.html',
  styleUrls: ['./modal-delete-banner.component.scss'],
})
export class ModalDeleteBannerComponent implements OnInit {
  item: any;

  SYSTEM_RULES = SYSTEM_RULES;
  MODAL_ACTION = MODAL_ACTION;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  CONFIG_TRANS_STATUS_CONST = CONFIG_TRANS_STATUS_CONST;
  constructor(
    public activeModal: NgbActiveModal,
    private campaignService: CampaignService,
    private toastrCustom: ToastrCustomService
  ) {}

  ngOnInit(): void {}

  onConfirm(): void {
    this.activeModal.close(MODAL_ACTION.CONFIRM.code);
    this.toastrCustom.success('model.campaign.deleteBannerSuccess');
  }
}
