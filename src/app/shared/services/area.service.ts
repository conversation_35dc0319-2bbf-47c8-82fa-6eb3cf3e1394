import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { INation } from '@shared/models/nation.model';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({ providedIn: 'root' })
export class AreaService extends AbstractService {
  public resourceUrl = '/public/area';

  constructor(protected http: HttpClient) {
    super(http);
  }

  /**
   * API get All nation
   *
   * @returns Observable<EntityResponseType<INation[]>>
   */
  public getAllNation(): Observable<EntityResponseType<INation[]>> {
    return super.post(`${this.resourceUrl}/nations`);
  }
}
