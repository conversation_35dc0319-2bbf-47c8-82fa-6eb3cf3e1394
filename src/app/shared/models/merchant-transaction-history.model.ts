export interface IMerchantTransactionHistory {
  merchantId?: number;
  customerAccountNumber?: string;
  customerCif?: string;
  transactionId?: string;
  transactionDateStart?: string;
  transactionDateEnd?: string;
  merchantIds?: string;
  customerFullName?: string;
  transactionAmount?: number;
  transactionFee?: number;
  transactionDate?: string;
  customerId?: number;
  merchantCode?: string;
  merchantName?: string;
  customerAccNumber?: string;
  customerAccountName?: string;
  transactionStartTime?: string;
  transactionCode?: string;
  transactionStatus?: string;
  transferType?: string;
  transferTypeStr?: string;
  bankCode?: string;
  target?: string;
  beneficiaryCustomerName?: string;
  transactionCurrency?: string;
  totalAmountTransaction?: number;
  transID?: string;
  transactionStartTimeStr?: string;
  extraTransactionCode?: string;
  transactionDateStr?: string;
  discount?: string;
  discountFixed?: number;
  clientMessageId?: string;
  billingTypeStr?: string;
  billingType?: string;
  provinceCode?: string;
  message?: string;
  titleLa?: string;
  accountName?: string;
  beneficiaryAccountNumber?: string;
  discountFixedStr?: string;
  totalAmount?: number;
  bankName?: string;
  clientAccountName?: string;
  clientAccountNumber?: string;
  description?: string;
  fromMember?: string;
  fromUser?: string;
  fromUserFullName?: string;
  fromAccount?: string;
  type?: string;
  nationCode?: string;
  nationName?: string;
  beneficiaryCurrency?: number;
  foreignAmount?: number;
  foreignFee?: number;
  totalForeignAmount?: number;
  exchangeRate?: number;
}
