<div class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="searchForm" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-md-4 col-lg-2">
            <div class="form-group">
              <label>{{ "common.action.searchKeyword" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="keyword"
                class="w-100"
                class="form-control"
                placeholder="{{ 'common.action.searchKeyword' | placeholder }}"
                [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
              />
            </div>
          </div>
          <div class="col-lg-2">
            <div class="form-group">
              <label>{{
                "model.transactionQrPay.partnerId" | translate
              }}</label>
              <input
                trim
                class="form-control w-100"
                formControlName="partnerId"
                type="text"
                placeholder="{{
                  'model.transactionQrPay.partnerId' | translate
                }}"
                [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
              />
            </div>
          </div>
          <div class="form-group col-md-4 col-lg-2">
            <label>{{
              "model.report.transaction.transactionStatus" | translate
            }}</label>
            <ng-select
              placeholder="{{
                'model.report.transaction.transactionStatus' | translate
              }}"
              [searchable]="true"
              formControlName="transactionStatus"
              [clearable]="true"
              [multiple]="true"
              appearance="outline"
            >
              <ng-option
                [value]="item.code"
                *ngFor="let item of TRANSACTION_ENTITY_STATUS"
              >
                {{ item.label | translate }}
              </ng-option>
              <ng-template ng-header-tmp>
                <div>
                  <button
                    class="btn btn-link"
                    type="button"
                    (click)="onSelectAll(SELECTED_CONST.TRANSACTION_STATUS)"
                  >
                    {{ "common.action.selectAll" | translate }}
                  </button>
                  <button
                    class="btn btn-link"
                    type="button"
                    (click)="onClearAll(SELECTED_CONST.TRANSACTION_STATUS)"
                  >
                    {{ "common.action.clearAll" | translate }}
                  </button>
                </div>
              </ng-template>
            </ng-select>
          </div>
          <div class="col-lg-4">
            <div class="form-group row">
              <div class="col-md-6">
                <label
                  >{{ "common.action.fromDate" | translate
                  }}<span class="text-danger">*</span></label
                >

                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="fromDate"
                    formControlName="fromDate"
                    placeholder="DD/MM/YYYY"
                    [max]="maxDate"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="fromDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #fromDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    searchForm.get('fromDate')?.errors?.required &&
                    searchForm.get('fromDate')?.touched
                  "
                >
                  {{ "error.required.fromDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    searchForm.get('fromDate')?.errors?.invalidDate &&
                    searchForm.get('fromDate')?.touched
                  "
                >
                  {{ "error.required.inValidDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    searchForm.get('fromDate')?.errors?.invalidMaxDate &&
                    searchForm.get('fromDate')?.touched
                  "
                >
                  {{
                    "error.maxDateCurrent"
                      | translate
                        : {
                            param: "common.action.fromDate" | translate
                          }
                  }}
                </small>
              </div>
              <div class="col-md-6">
                <label
                  >{{ "common.action.toDate" | translate
                  }}<span class="text-danger">*</span></label
                >

                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="toDate"
                    formControlName="toDate"
                    placeholder="DD/MM/YYYY"
                    min="{{
                      searchForm.controls['fromDate'].value
                        | date : 'yyyy-MM-dd'
                    }}"
                    [max]="maxToDate"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="toDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #toDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    searchForm.get('toDate')?.errors?.required &&
                    searchForm.get('toDate')?.touched
                  "
                >
                  {{ "error.required.toDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    searchForm.get('toDate')?.errors?.invalidMaxDate &&
                    searchForm.get('toDate')?.touched
                  "
                >
                  {{
                    "error.maxDateCurrent"
                      | translate
                        : {
                            param: "common.action.toDate" | translate
                          }
                  }}
                </small>
              </div>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
              (click)="onReset()"
            >
              <div class="btn-reset">
                <i class="bi bi-arrow-clockwise" type="button"> </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" type="submit">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-4 mt-4">
          <!-- <button class="btn btn-red mr-2" (click)="print()">{{ "common.action.print" | translate }}
          </button> -->
          <button
            class="btn btn-red mr-2"
            type="button"
            (click)="exportFile()"
            [disabled]="this.data.length === 0"
            *hasPrivileges="SYSTEM_RULES.TRANSACTION_QRPAY_EXPORT"
          >
            {{ "common.action.export" | translate }}
          </button>
        </div>
      </form>

      <div class="table-responsive">
        <nz-table
          [nzData]="data"
          class="table-account-transaction"
          (nzQueryParams)="onChangePageSort($event)"
        >
          <thead>
            <tr>
              <th [width]="'50px'" class="text-center">
                {{ "model.manageSaving.no" | translate }}
              </th>
              <th [width]="'180px'" class="text-left">
                {{ "model.transactionQrPay.bankTransId" | translate }}
              </th>
              <th class="text-left" [width]="'250px'">
                {{ "model.transactionQrPay.partnerId" | translate }}
              </th>
              <th class="text-right" [width]="'250px'">
                {{ "model.transactionQrPay.bankAccount" | translate }}
              </th>
              <th class="text-right" [width]="'250px'">
                {{ "model.transactionQrPay.accountNumber" | translate }}
              </th>
              <th class="text-right" [width]="'250px'">
                {{ "model.transactionQrPay.amount" | translate }}
              </th>
              <th class="text-left" [width]="'250px'">
                {{ "model.transactionQrPay.content" | translate }}
              </th>
              <th class="text-center" [width]="'250px'">
                {{ "model.transactionQrPay.transDate" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.status" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of data; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td class="text-left">{{ item.bankTransId }}</td>
              <td class="text-left">{{ item.partnerId }}</td>
              <td class="text-right">{{ item.bankAccount }}</td>
              <td class="text-right">{{ item.accountNumber }}</td>
              <td class="text-right">{{ item.amount | currencyLak }}</td>
              <td class="text-left">{{ item.content }}</td>
              <td class="text-center">{{ item.createdDateStr }}</td>
              <td class="text-center">
                <span
                  class="badge"
                  [ngClass]="
                    TRANSACTION_ENTITY_STATUS_MAP[
                      item.transactionStatus ||
                        ENTITY_TRANSACTION_STATUS_CONST.PROCESSING.code
                    ].style
                  "
                  >{{
                    TRANSACTION_ENTITY_STATUS_MAP[
                      item.transactionStatus ||
                        ENTITY_TRANSACTION_STATUS_CONST.PROCESSING.code
                    ].label | translate
                  }}</span
                >
              </td>
            </tr>
          </tbody>
        </nz-table>
        <div
          class="row d-block text-center m-0 no-search-result-wrapper"
          *ngIf="data?.length === 0"
        >
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
      </div>
      <div *ngIf="data?.length">
        <mat-paginator
          [length]="searchForm.value.length"
          [pageSize]="searchForm.value.pageSize"
          [pageIndex]="searchForm.value.pageIndex"
          [pageSizeOptions]="pageSizeOptions"
          (page)="onChangePage($event)"
          aria-label="Select page"
        >
        </mat-paginator>
      </div>
    </div>
  </div>
</div>
