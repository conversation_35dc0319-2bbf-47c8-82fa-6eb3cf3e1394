import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { ROUTER_UTILS } from '@shared/utils/router.utils';

export const SIDEBAR_CONST = [
  {
    path: `${ROUTER_UTILS.customer.root}`,
    title: 'sidebar.customer.root',
    icon: 'bi bi-person-bounding-box',
    root: true,
    authorities: [SYSTEM_RULES.CUSTOMER_READ],
    child: [
      {
        path: `${ROUTER_UTILS.customer.root}/${ROUTER_UTILS.customer.registration.root}`,
        title: 'sidebar.customer.registration.root',
        // icon: 'bi bi-person-badge',
        authorities: [SYSTEM_RULES.CUSTOMER_READ],
      },
      {
        path: `${ROUTER_UTILS.customer.root}/${ROUTER_UTILS.customer.approve.root}`,
        title: 'sidebar.customer.approve.root',
        // icon: 'bi bi-person-check',
        authorities: [SYSTEM_RULES.CUSTOMER_READ],
      },
      {
        path: `${ROUTER_UTILS.customer.root}/${ROUTER_UTILS.customer.accountNoTransaction.root}`,
        title: 'sidebar.customer.accountNoTransaction.title',
        authorities: [SYSTEM_RULES.NO_TRANSACTION_READ],
      },
      {
        path: `${ROUTER_UTILS.customer.root}/${ROUTER_UTILS.customer.activateAccountManual.root}`,
        title: 'sidebar.customer.activateAccountManual.title',
        authorities: [SYSTEM_RULES.MANUAL_ACTIVATION_READ],
      },
      {
        path: `${ROUTER_UTILS.customer.root}/${ROUTER_UTILS.customer.dotpManagement.root}`,
        title: 'sidebar.customer.dotpManagement.root',
        authorities: [SYSTEM_RULES.DOTP_REGISTRATION_READ],
      },
    ],
  },
  {
    path: `${ROUTER_UTILS.sysManage.root}`,
    title: 'sidebar.sysManage.root',
    icon: 'bi bi-cpu',
    root: true,
    authorities: [
      SYSTEM_RULES.ROLE_READ,
      SYSTEM_RULES.POSITION_READ,
      SYSTEM_RULES.DEPARTMENT_READ,
      SYSTEM_RULES.FEE_READ,
      SYSTEM_RULES.INFORMATION_TEMPLATE_READ,
      SYSTEM_RULES.SAVING_CONFIGURATION_READ,
      SYSTEM_RULES.IMAGE_LOGIN_APP_READ,
      SYSTEM_RULES.REQUEST_CUSTOMER_SUPPORT_READ,
      SYSTEM_RULES.MONITOR_LOG_READ,
      SYSTEM_RULES.SMS_BALANCE_READ,
    ],
    child: [
      {
        path: ROUTER_UTILS.sysManage.backgroundLogin.root,
        title: 'sidebar.sysManage.backgroundLogin',
        authorities: [SYSTEM_RULES.IMAGE_LOGIN_APP_READ],
      },
      {
        path: ROUTER_UTILS.sysManage.customerSupport.root,
        title: 'sidebar.sysManage.customerSupport',
        authorities: [SYSTEM_RULES.REQUEST_CUSTOMER_SUPPORT_READ],
      },
      {
        path: ROUTER_UTILS.sysManage.role.root,
        title: 'sidebar.sysManage.role.root',
        // icon: 'bi bi-shield-plus',
        authorities: [SYSTEM_RULES.ROLE_READ],
      },
      // {
      //   path: ROUTER_UTILS.sysManage.errorCode.root,
      //   title: 'sidebar.sysManage.errorCode.root',
      //   icon: 'bi bi-journal-code',
      //   authorities: [],
      // },
      {
        path: ROUTER_UTILS.sysManage.position.root,
        title: 'sidebar.position',
        // icon: 'bi bi-person-lines-fill',
        authorities: [SYSTEM_RULES.POSITION_READ],
      },
      {
        path: ROUTER_UTILS.sysManage.department.root,
        title: 'sidebar.department.root',
        // icon: 'bi bi-building',
        authorities: [SYSTEM_RULES.DEPARTMENT_READ],
      },
      {
        path: `${ROUTER_UTILS.fee.root}`,
        title: 'sidebar.fee.root',
        // icon: 'bi bi-currency-exchange',
        authorities: [SYSTEM_RULES.FEE_READ],
      },
      // {
      //   path: `${ROUTER_UTILS.sysManage.setting}`,
      //   title: 'sidebar.sysManage.setting',
      //   icon: 'bi bi-gear',
      //   authorities: [],
      // },
      {
        path: ROUTER_UTILS.sysManage.informationTemplate.root,
        title: 'sidebar.informationTemplate.root',
        icon: 'bi bi-info-circle-fill',
        authorities: [SYSTEM_RULES.INFORMATION_TEMPLATE_READ],
      },
      {
        path: ROUTER_UTILS.sysManage.campaign.root,
        title: 'sidebar.campaign.root',
        icon: 'bi bi-file-earmark-text-fill',
        authorities: [SYSTEM_RULES.CAMPAIGN_READ],
      },
      {
        path: ROUTER_UTILS.sysManage.configTransaction.root,
        title: 'sidebar.configTransaction.root',
        authorities: [SYSTEM_RULES.TRANSACTION_LIMIT_READ],
      },
      {
        path: ROUTER_UTILS.sysManage.configSaving.root,
        title: 'sidebar.configSaving.root',
        authorities: [SYSTEM_RULES.SAVING_CONFIGURATION_READ],
      },
      {
        path: ROUTER_UTILS.monitorLog.root,
        title: 'sidebar.monitorLog',
        authorities: [SYSTEM_RULES.MONITOR_LOG_READ],
      },
      {
        path: ROUTER_UTILS.currency.root,
        title: 'sidebar.currency',
        authorities: [SYSTEM_RULES.CURRENCY_READ],
      },
    ],
  },
  // {
  //   path: ROUTER_UTILS.client.root,
  //   title: 'client.root',
  //   icon: 'bi bi-people-fill',
  //   root: true,
  //   authorities: [SYSTEM_RULES.CLIENT_READ, SYSTEM_RULES.SERVICE_PACK_READ],
  //   child: [
  //     {
  //       path: ROUTER_UTILS.client.root,
  //       title: 'client.root',
  //       // icon: 'bi bi-wallet2',
  //       authorities: [SYSTEM_RULES.CLIENT_READ],
  //     },
  //     {
  //       path: ROUTER_UTILS.servicePack.root,
  //       title: 'servicePack.root',
  //       // icon: 'bi bi-wallet',
  //       authorities: [SYSTEM_RULES.SERVICE_PACK_READ],
  //     },
  //   ],
  // },
  {
    path: ROUTER_UTILS.merchant.masterMerchant,
    title: 'sidebar.merchant.root',
    icon: 'bi bi-wallet-fill',
    root: true,
    authorities: [
      SYSTEM_RULES.MERCHANT_READ,
      SYSTEM_RULES.MASTER_MERCHANT_READ,
      SYSTEM_RULES.CASHOUT_ACCOUNT_READ,
    ],
    child: [
      {
        path: ROUTER_UTILS.merchant.masterMerchant.root,
        title: 'sidebar.merchant.masterMerchant.root',
        // icon: 'bi bi-wallet',
        authorities: [SYSTEM_RULES.MASTER_MERCHANT_READ],
      },
      {
        path: ROUTER_UTILS.merchant.root,
        title: 'sidebar.merchant.root',
        // icon: 'bi bi-wallet2',
        authorities: [SYSTEM_RULES.MERCHANT_READ],
      },
      {
        path: ROUTER_UTILS.payment.root,
        title: 'sidebar.managePayment.root',
        authorities: [SYSTEM_RULES.CASHOUT_ACCOUNT_READ],
      },
    ],
  },
  {
    path: ROUTER_UTILS.debitAccount.root,
    title: 'sidebar.debitAccount.root',
    icon: 'bi bi-wallet-fill',
    root: true,
    authorities: [
      SYSTEM_RULES.DEBIT_ACCOUNT_READ,
      SYSTEM_RULES.DEBIT_DEPOSIT_TRANSACTION_READ,
      SYSTEM_RULES.DEBIT_DEPOSIT_ASYNC_READ,
    ],
    child: [
      {
        path: ROUTER_UTILS.debitAccount.root,
        title: 'sidebar.debitAccount.debit',
        // icon: 'bi bi-wallet',
        authorities: [SYSTEM_RULES.DEBIT_ACCOUNT_READ],
      },
      {
        path: ROUTER_UTILS.debitAccount.history,
        title: 'sidebar.debitAccount.historyTransfer',
        // icon: 'bi bi-wallet2',
        authorities: [SYSTEM_RULES.DEBIT_DEPOSIT_TRANSACTION_READ],
      },
      {
        path: ROUTER_UTILS.debitAccount.report,
        title: 'sidebar.debitAccount.report',
        authorities: [SYSTEM_RULES.DEBIT_DEPOSIT_ASYNC_READ],
      },
    ],
  },
  {
    path: `${ROUTER_UTILS.loanOnline.root}`,
    title: 'sidebar.loanOnline.root',
    icon: 'bi bi-cash-coin',
    authorities: [SYSTEM_RULES.LOAN_ONLINE_READ],
  },
  {
    title: 'sidebar.merchant.transactionHistory.root',
    icon: 'bi bi-book-fill',
    root: true,
    authorities: [
      SYSTEM_RULES.TRANSACTION_READ,
      SYSTEM_RULES.UMONEY_TRANSACTION_READ,
      SYSTEM_RULES.REPORT_NUMBER_TRANSACTION_READ,
      SYSTEM_RULES.UTILITY_TRANSACTION_READ,
      SYSTEM_RULES.NOTIFICATION_LIMIT_READ,
      SYSTEM_RULES.TRANSACTION_QRPAY_READ,
    ],
    child: [
      {
        path: `${ROUTER_UTILS.transaction.transactionOther.root}`,
        title: 'sidebar.merchant.transactionHistory.transactionOther',
        // icon: 'bi bi-wallet2',
        authorities: [SYSTEM_RULES.TRANSACTION_READ],
      },
      {
        path: `${ROUTER_UTILS.transaction.internationalTransaction.root}`,
        title: 'sidebar.merchant.transactionHistory.internationalTransaction',
        authorities: [SYSTEM_RULES.INTERNATIONAL_PAYMENT_READ],
      },
      {
        path: `${ROUTER_UTILS.transaction.transMMoney.root}`,
        title: 'sidebar.merchant.transactionHistory.transactionMMoney',
        // icon: 'bi bi-wallet',
        authorities: [SYSTEM_RULES.UTILITY_TRANSACTION_READ],
      },
      {
        path: `${ROUTER_UTILS.transaction.transactionUmoney.root}`,
        title: 'sidebar.merchant.transactionHistory.transactionUmoney',
        // icon: 'bi bi-wallet',
        authorities: [SYSTEM_RULES.UMONEY_TRANSACTION_READ],
      },
      {
        path: `${ROUTER_UTILS.transaction.transQuantity.root}`,
        title: 'sidebar.manageTransQuantity.root',
        // icon: 'bi bi-wallet',
        authorities: [SYSTEM_RULES.REPORT_NUMBER_TRANSACTION_READ],
      },
      {
        path: `${ROUTER_UTILS.transaction.transactionNotificationLimit.root}`,
        title: 'sidebar.transactionExceedLimit.root',
        authorities: [SYSTEM_RULES.NOTIFICATION_LIMIT_READ],
      },
      {
        path: `${ROUTER_UTILS.transaction.transactionQrPay.root}`,
        title: 'sidebar.transactionQrPay',
        authorities: [SYSTEM_RULES.TRANSACTION_QRPAY_READ],
      },
    ],
  },
  {
    title: 'sidebar.premiumAccountNumber.root',
    icon: 'bi bi-credit-card',
    root: true,
    authorities: [
      SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_STRUCTURE_READ,
      SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_READ,
      SYSTEM_RULES.SPECIAL_PREMIUM_ACCOUNT_NUMBER_READ,
      // SYSTEM_RULES.NUMBER_GROUP_READ
    ],
    child: [
      {
        path: `${ROUTER_UTILS.premiumAccountNumber.root}/${ROUTER_UTILS.premiumAccountNumber.interest.root}`,
        title: 'sidebar.premiumAccountNumberInterest.root',
        authorities: [SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_READ],
      },
      {
        path: `${ROUTER_UTILS.premiumAccountNumber.root}/${ROUTER_UTILS.premiumAccountNumber.numberStructure.root}`,
        title: 'sidebar.structureAccount.root',
        authorities: [SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_STRUCTURE_READ],
      },
      {
        path: `${ROUTER_UTILS.premiumAccountNumber.root}/${ROUTER_UTILS.premiumAccountNumber.special.root}`,
        title: 'sidebar.specialAccountNumber.root',
        authorities: [SYSTEM_RULES.SPECIAL_PREMIUM_ACCOUNT_NUMBER_READ],
      },
      //{
      //  path: `${ROUTER_UTILS.transaction.premiumAccountRevertNotificationLimit.root}`,
      //  title: 'premiumAccountRevert.root',
      //  authorities: [SYSTEM_RULES.NOTIFICATION_HISTORY_READ],
      //},
      {
        path: `${ROUTER_UTILS.premiumAccountNumber.root}/${ROUTER_UTILS.premiumAccountNumber.create}`,
        title: 'sidebar.premiumAccountNumberHasPaymentAccount.root',
        authorities: [SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_CREATE],
      },
      {
        path: `${ROUTER_UTILS.transaction.premiumAccountRevertNotificationLimit.root}`,
        title: 'premiumAccountRevert.root',
        authorities: [SYSTEM_RULES.NOTIFICATION_HISTORY_READ],
      },
      // {
      //   path: `${ROUTER_UTILS.premiumAccountNumber.root}/${ROUTER_UTILS.numberGroup.root}`,
      //   title: 'sidebar.numberGroup',
      //   authorities: [SYSTEM_RULES.NUMBER_GROUP_READ],
      // },
    ],
  },
  {
    path: `${ROUTER_UTILS.notification.root}`,
    title: 'sidebar.notification.root',
    icon: 'bi bi-bell-fill',
    authorities: [
      SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_STRUCTURE_READ,
      SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_READ,
      SYSTEM_RULES.SPECIAL_PREMIUM_ACCOUNT_NUMBER_READ,
    ],
  },
  {
    title: 'sidebar.smsBalance.root',
    icon: 'bi bi-chat-dots',
    root: true,
    authorities: [
      SYSTEM_RULES.SMS_BALANCE_CHARGE_FAIL,
      SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_READ,
    ],
    child: [
      {
        path: ROUTER_UTILS.smsBalance.root,
        title: 'sidebar.smsBalance.list',
        authorities: [SYSTEM_RULES.SMS_BALANCE_READ],
      },
      {
        path: `${ROUTER_UTILS.managementServiceFeeFailed.managementSMSBalanceFeeFailed.root}`,
        title: 'managementSMSBalanceFeeFailed.root',
        authorities: [SYSTEM_RULES.SMS_BALANCE_CHARGE_FAIL],
      },
    ],
  },
  {
    path: `${ROUTER_UTILS.news.root}`,
    title: 'sidebar.news.root',
    icon: 'bi bi-newspaper',
    authorities: [SYSTEM_RULES.NEWS_READ],
  },
  {
    path: `${ROUTER_UTILS.feeSchedule.root}`,
    title: 'sidebar.feeSchedule.root',
    icon: 'bi bi-calendar3',
    authorities: [SYSTEM_RULES.FEE_SCHEDULE_READ],
  },
  {
    path: `${ROUTER_UTILS.bank.root}`,
    title: 'sidebar.bank.root',
    icon: 'bi bi-bank2',
    authorities: [SYSTEM_RULES.BANK_READ],
  },
  {
    path: ROUTER_UTILS.user.root,
    title: 'sidebar.user.root',
    icon: 'bi bi-people-fill',
    authorities: [SYSTEM_RULES.USER_READ],
  },
  {
    path: `${ROUTER_UTILS.lapnet.root}`,
    title: 'sidebar.lapnet.root',
    icon: 'bi bi-bar-chart-line-fill',
    authorities: [SYSTEM_RULES.LAPNET_REPORT_READ],
  },
  {
    path: `${ROUTER_UTILS.referral.root}`,
    title: 'sidebar.referral.root',
    icon: 'bi bi-share-fill',
    authorities: [SYSTEM_RULES.REFERRAL_READ],
  },
  {
    path: `${ROUTER_UTILS.version.root}`,
    title: 'sidebar.versionManage.root',
    icon: 'bi bi-app-indicator',
    authorities: [SYSTEM_RULES.VERSION_READ],
  },
  {
    path: `${ROUTER_UTILS.insurance.root}`,
    title: 'sidebar.insuranceManage.root',
    icon: 'bi bi-heart-pulse',
    authorities: [SYSTEM_RULES.INSURANCE_READ],
  },
  {
    path: `${ROUTER_UTILS.smsManage.root}`,
    title: 'sidebar.smsManage.root',
    icon: 'bi bi-chat-right-text',
    authorities: [SYSTEM_RULES.SMS_READ],
  },
  {
    path: `${ROUTER_UTILS.saving.root}`,
    title: 'sidebar.manageSaving.root',
    icon: 'bi bi-currency-exchange',
    authorities: [SYSTEM_RULES.SAVING_ACCOUNT_READ],
  },
  {
    path: `${ROUTER_UTILS.rate.root}`,
    title: 'sidebar.rate.root',
    icon: 'bi bi-currency-dollar',
    authorities: [SYSTEM_RULES.INTEREST_RATE_READ],
  },
];
