<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="backToList()" />
      <h5>
        {{ "sidebar.bank.detail" | translate }}
      </h5>
    </div>
    <form [formGroup]="formBank" *ngIf="isUpdate ? bank?.bankId : !isUpdate">
      <div class="col-md-12">
        <div class="row">
          <h2 class="title-create">
            {{ "bank.titleDetailInforBank" | translate }}
          </h2>
        </div>
        <div class="row border-create">
          <h3>{{ "bank.titleInforBank" | translate }}</h3>
          <div class="col-3">
            <div class="col-12">
              <label
                >{{ "common.image" | translate
                }}<span class="text-danger">*</span></label
              >
              <div class="bank-upload">
                <app-upload-images
                  [imageUrls]="imageUrls"
                  class="box-image"
                  [checkDisabled]="true"
                >
                </app-upload-images>
              </div>
            </div>
          </div>
          <div class="col-9">
            <div class="col-md-12">
              <div class="row">
                <div class="col-md-4">
                  <div class="form-group">
                    <label
                      >{{ "model.bank.codeNumber" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      trim
                      formControlName="bankCodeNumber"
                      type="text"
                      class="w-100"
                      class="form-control"
                      placeholder="{{ 'model.bank.codeNumber' | placeholder }}"
                    />
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <label
                      >{{ "model.bank.code" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      trim
                      formControlName="bankCode"
                      type="text"
                      class="w-100"
                      class="form-control"
                      placeholder="{{ 'model.bank.code' | placeholder }}"
                    />
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <label
                      >{{ "model.bank.order" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      trim
                      formControlName="order"
                      type="number"
                      class="w-100"
                      class="form-control"
                      placeholder="{{ 'model.bank.order' | placeholder }}"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div class="col-12">
              <div class="row">
                <div class="form-group col-md-4">
                  <label>{{
                    "common.nation" | translate
                  }}</label>
                  <ng-select
                    appearance="outline"
                    [searchable]="false"
                    [clearable]="true"
                    formControlName="nation"
                    placeholder="{{
                      'common.nation' | translate
                    }}"
                  >
                    <ng-option
                      [value]="item.code"
                      *ngFor="let item of NATION_CODES"
                    >
                      {{ item.label | translate }}
                    </ng-option>
                  </ng-select>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <label
                      >{{ "common.status" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <ng-select
                      appearance="outline"
                      [searchable]="false"
                      [clearable]="false"
                      formControlName="status"
                    >
                      <ng-option
                        [value]="item.code"
                        *ngFor="let item of ENTITY_STATUS"
                      >
                        {{ item.label | translate }}
                      </ng-option>
                    </ng-select>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-12">
              <div class="row">
                <div class="col-md-12">
                  <div class="form-group">
                    <label
                      >{{ "model.bank.name" | translate }}
                      <span class="text-danger">*</span></label
                    >
                    <input
                      trim
                      formControlName="bankName"
                      type="text"
                      class="w-100"
                      class="form-control"
                      placeholder="{{ 'model.bank.name' | placeholder }}"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="d-block text-center mb-5 mt-4">
          <button
            class="btn btn-white mr-2"
            (click)="goToUpdate()"
            *hasPrivileges="SYSTEM_RULES.BANK_WRITE"
          >
            {{ "common.action.update" | translate }}
          </button>
          <button
            class="btn btn-red"
            data-toggle="modal"
            (click)="backToList()"
            type="button"
          >
            {{ "common.action.back" | translate }}
          </button>
        </div>
      </div>
    </form>
  </div>
</section>
