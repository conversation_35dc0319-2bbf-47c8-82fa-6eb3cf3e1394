<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="backToDetail()" />
      <h5>
        {{
          isShowEdit
            ? ("role.titleUpdate" | translate)
            : ("role.title" | translate)
        }}
      </h5>
    </div>
    <form [formGroup]="createRole">
      <div class="col-md-12">
        <div class="row">
          <h2 class="title-create">
            {{ (isShowEdit ? "role.titleUpdate" : "role.title") | translate }}
          </h2>
        </div>
        <div class="row border-create">
          <h3>{{ "role.infor" | translate }}</h3>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label
                    >{{ "model.role.name" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    trim
                    type="text"
                    formControlName="name"
                    class="w-100"
                    class="form-control"
                    [maxLength]="VALIDATORS.LENGTH.NAME_ROLE_MAX_LENGTH"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      createRole.get('name')?.errors?.required &&
                      createRole.get('name')?.touched
                    "
                  >
                    {{ "error.role.required.name" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      createRole.get('name')?.errors?.maxlength &&
                      createRole.get('name')?.touched
                    "
                  >
                    {{
                      "error.role.maxLength.name"
                        | translate
                          : {
                              length: VALIDATORS.LENGTH.NAME_ROLE_MAX_LENGTH
                            }
                    }}
                  </small>
                </div>
              </div>
              <div class="col-md-9">
                <div class="form-group">
                  <label>{{ "model.role.description" | translate }}</label>
                  <input
                    trim
                    type="text"
                    formControlName="description"
                    class="w-100"
                    class="form-control"
                    [maxLength]="VALIDATORS.LENGTH.DESCRIPTION_ROLE_MAX_LENGTH"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      createRole.get('description')?.errors?.maxlength &&
                      createRole.get('description')?.touched
                    "
                  >
                    {{
                      "error.role.maxLength.description"
                        | translate
                          : {
                              length:
                                VALIDATORS.LENGTH.DESCRIPTION_ROLE_MAX_LENGTH
                            }
                    }}
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
    <div class="col-md-12">
      <div class="row mt-4">
        <cdk-accordion [multi]="true" class="example-accordion">
          <cdk-accordion-item
            *ngFor="let item of groupPrivileges; let index = index"
            #accordionItem="cdkAccordionItem"
            class="example-accordion-item"
            role="button"
            tabindex="0"
            [attr.id]="'accordion-header-' + index"
            [attr.aria-expanded]="accordionItem.expanded"
            [attr.aria-controls]="'accordion-body-' + index"
          >
            <div
              class="example-accordion-item-header"
              (click)="accordionItem.toggle()"
            >
              <mat-checkbox
                class="check-box-group-role"
                [(ngModel)]="item.checked"
                (click)="selectAllPrivileges($event, item)"
              >
                <h5 class="font-weight-bold">{{ item?.name }}</h5>
              </mat-checkbox>

              <span class="example-accordion-item-description">
                <i
                  class="fas fa-{{
                    accordionItem.expanded ? 'angle-up' : 'angle-down'
                  }}"
                ></i>
              </span>
            </div>
            <div
              class="example-accordion-item-body"
              role="region"
              [style.display]="accordionItem.expanded ? '' : 'none'"
              [attr.id]="'accordion-body-' + index"
              [attr.aria-labelledby]="'accordion-header-' + index"
            >
              <div class="row">
                <div
                  class="col-md-3"
                  *ngFor="let privilege of item.privileges; let i = index"
                >
                  <mat-checkbox
                    class="check-box-role"
                    (click)="selectOnePrivileges($event, privilege, item)"
                    [(ngModel)]="privilege.checked"
                    >{{ privilege?.description }}</mat-checkbox
                  >
                </div>
              </div>
            </div>
          </cdk-accordion-item>
        </cdk-accordion>
      </div>
    </div>
    <div class="d-block text-center mb-5 mt-4">
      <button class="btn btn-white mr-2" (click)="backToDetail()">
        {{ "common.action.back" | translate }}
      </button>
      <button
        class="btn btn-red"
        (click)="onSave()"
        *hasPrivileges="
          isShowEdit ? SYSTEM_RULES.ROLE_WRITE : SYSTEM_RULES.ROLE_CREATE
        "
      >
        {{
          (isShowEdit ? "common.action.update" : "common.action.create")
            | translate
        }}
      </button>
    </div>
  </div>
</section>
