import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  FILE_EXTENSION,
  LIMIT_LENGTH_WORD_CONST,
  MODAL_ACTION,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import {
  APPROVAL_STATUS_CONST,
  CUSTOMER_STATUS_CONST,
  CUSTOMER_TYPE,
  CUSTOMER_TYPE_MAP,
  NO_TRANSACTION_STATUS,
  NO_TRANSACTION_STATUS_MAP,
} from '@shared/constants/customer.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ICustomer } from '@shared/models/customer.model';
import { IEventSearch } from '@shared/models/request/event.search';
import { CustomerService } from '@shared/services/customer.service';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import CommonUtils from '@shared/utils/common-utils';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils/router.utils';

@Component({
  selector: 'app-no-transaction',
  templateUrl: './account-no-transaction.component.html',
  styleUrls: ['./account-no-transaction.component.scss'],
})
export class AccountNoTransactionComponent implements OnInit {
  data: ICustomer[] = [];
  eventSearch: IEventSearch = {};
  pageSizeOptions = PAGINATION.OPTIONS;
  NO_TRANSACTION_STATUS = NO_TRANSACTION_STATUS;
  NO_TRANSACTION_STATUS_MAP = NO_TRANSACTION_STATUS_MAP;
  LIMIT_LENGTH_WORD_CONST = LIMIT_LENGTH_WORD_CONST;
  APPROVAL_STATUS_CONST = APPROVAL_STATUS_CONST;
  CUSTOMER_STATUS_CONST = CUSTOMER_STATUS_CONST;
  SYSTEM_RULES = SYSTEM_RULES;
  VALIDATORS = VALIDATORS;
  CUSTOMER_TYPE = CUSTOMER_TYPE;
  CUSTOMER_TYPE_MAP = CUSTOMER_TYPE_MAP;
  checked = false;
  indeterminate = false;
  setOfCheckedId = new Set();
  listOfCurrentPageData: readonly ICustomer[] = [];
  onShowButtonAndCol = true;

  constructor(
    private fb: FormBuilder,
    private toastService: ToastrCustomService,
    private router: Router,
    private modalService: ModalService,
    private customerService: CustomerService,
    private translateService: TranslateService,
    private downloadService: DownloadService
  ) {}

  formSearch = this.fb.group({
    cif: ['', [Validators.pattern(VALIDATORS.PATTERN.CIF_SEARCH)]],
    phoneNumber: ['', [Validators.pattern(VALIDATORS.PATTERN.PHONE_SEARCH)]],
    idCardNumber: '',
    email: '',
    accountType: null,
    status: null,
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    hasPageable: true,
  });

  ngOnInit(): void {
    this.onSearch();
  }

  onSearch(): void {
    const body = this.formSearch.value;
    if (this.formSearch.valid) {
      this.customerService
        .searchNoTransaction(body)
        .subscribe((res: any): void => {
          this.data = res.body.content;
          this.onShowButtonAndCol = res?.body?.content?.some(
            (item: ICustomer) => {
              return (
                ((item?.approvalStatus ===
                  APPROVAL_STATUS_CONST.APPROVAL.value &&
                  !item?.referenceId) ||
                  item?.approvalStatus === APPROVAL_STATUS_CONST.DENY.value) &&
                item?.status === CUSTOMER_STATUS_CONST.ACTIVE.value
              );
            }
          );
          this.formSearch.controls.length.setValue(res.body.totalElements);
        });
    }
  }

  onDetail(customer: ICustomer) {
    this.router.navigate([
      ROUTER_UTILS.customer.root,
      customer.customerId,
      ROUTER_ACTIONS.view,
    ]);
  }

  onSearchSubmit(): void {
    this.onSearch();
  }

  onChangePage(page: any) {
    this.formSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  onReset(): void {
    this.formSearch.controls.cif.reset();
    this.formSearch.controls.phoneNumber.reset();
    this.formSearch.controls.idCardNumber.reset();
    this.formSearch.controls.email.reset();
    this.formSearch.controls.accountType.reset();
    this.formSearch.controls.status.reset();
    this.formSearch.controls.hasPageable.setValue(true);
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formSearch.value.pageIndex,
      this.formSearch.value.pageSize
    );
  }

  onActiveAccount(data?: any): void {
    const customerIds =
      data instanceof Set ? Array.from(data) : [data?.customerId];
    if (customerIds.length > 0) {
      const modalData = {
        title: 'model.customer.accountNoTransaction.requestTitle',
        content: 'model.customer.accountNoTransaction.requestContent',
      };
      this.modalService.confirm(modalData).then((result) => {
        if (result === MODAL_ACTION.CONFIRM.code) {
          this.customerService
            .cancelNoTransaction(customerIds)
            .subscribe((): void => {
              this.toastService.success('common.action.requestSuccess');
              this.onSearch();
            });
        }
      });
    } else {
      this.toastService.warning(
        'model.customer.accountNoTransaction.alertNotification'
      );
    }
  }

  exportFile(): void {
    const bodySearch = this.formSearch.getRawValue();
    if (this.formSearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formSearch);
    } else {
      const fileName = this.translateService.instant(
        'template.accountNoTransaction'
      );
      const obFile = this.customerService.exportNoTransaction(bodySearch);
      this.downloadService.downloadFileWithObservableAndName(
        obFile,
        fileName,
        FILE_EXTENSION.XLSX
      );
    }
  }

  /**
   * Tick one customer and Tick all customer
   */

  onCurrentPageDataChange(listOfCurrentPageData: readonly ICustomer[]): void {
    this.listOfCurrentPageData = listOfCurrentPageData;
    this.refreshCheckedStatus();
  }

  refreshCheckedStatus(): void {
    const listOfEnabledData = this.listOfCurrentPageData.filter(
      (item) =>
        ((item?.approvalStatus === APPROVAL_STATUS_CONST.APPROVAL.value &&
          !item?.referenceId) ||
          item?.approvalStatus === APPROVAL_STATUS_CONST.DENY.value) &&
        item?.status === CUSTOMER_STATUS_CONST.ACTIVE.value
    );
    this.checked = listOfEnabledData?.every(({ customerId }) =>
      this.setOfCheckedId.has(customerId || 0)
    );
    this.indeterminate =
      listOfEnabledData.some(({ customerId }) =>
        this.setOfCheckedId.has(customerId || 0)
      ) && !this.checked;
  }

  onItemChecked(customerId?: number, checked?: boolean): void {
    if (customerId) {
      this.updateCheckedSet(customerId, checked);
      this.refreshCheckedStatus();
    }
  }

  onAllChecked(checked?: boolean): void {
    this.indeterminate = false;
    this.data.forEach((item: ICustomer) => {
      if (
        ((item?.approvalStatus === APPROVAL_STATUS_CONST.APPROVAL.value &&
          !item?.referenceId) ||
          item?.approvalStatus === APPROVAL_STATUS_CONST.DENY.value) &&
        item?.status === CUSTOMER_STATUS_CONST.ACTIVE.value
      ) {
        this.updateCheckedSet(item.customerId || 0, checked);
      }
    });
    this.refreshCheckedStatus();
  }

  updateCheckedSet(customerId: number, checked?: boolean): void {
    if (checked) {
      this.setOfCheckedId.add(customerId);
    } else {
      this.setOfCheckedId.delete(customerId);
    }
  }
}
