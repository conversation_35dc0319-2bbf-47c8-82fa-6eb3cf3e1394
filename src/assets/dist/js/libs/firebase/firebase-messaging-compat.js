!(function (e, t) {
  "object" == typeof exports && "undefined" != typeof module
    ? t(require("@firebase/app-compat"), require("@firebase/app"))
    : "function" == typeof define && define.amd
    ? define(["@firebase/app-compat", "@firebase/app"], t)
    : t(
        (e = "undefined" != typeof globalThis ? globalThis : e || self)
          .firebase,
        e.firebase.INTERNAL.modularAPIs
      );
})(this, function (Ft, qt) {
  "use strict";
  try {
    !function () {
      function e(e) {
        return e && "object" == typeof e && "default" in e ? e : { default: e };
      }
      var t = e(Ft);
      function n() {
        return "object" == typeof indexedDB;
      }
      class o extends Error {
        constructor(e, t, n) {
          super(t),
            (this.code = e),
            (this.customData = n),
            (this.name = "FirebaseError"),
            Object.setPrototypeOf(this, o.prototype),
            Error.captureStackTrace &&
              Error.captureStackTrace(this, i.prototype.create);
        }
      }
      class i {
        constructor(e, t, n) {
          (this.service = e), (this.serviceName = t), (this.errors = n);
        }
        create(e, ...t) {
          var i,
            n = t[0] || {},
            t = `${this.service}/${e}`,
            e = this.errors[e],
            e = e
              ? ((i = n),
                e.replace(a, (e, t) => {
                  var n = i[t];
                  return null != n ? String(n) : `<${t}?>`;
                }))
              : "Error",
            e = `${this.serviceName}: ${e} (${t}).`;
          return new o(t, e, n);
        }
      }
      const a = /\{\$([^}]+)}/g;
      function r(e) {
        return e && e._delegate ? e._delegate : e;
      }
      var s =
        ((c.prototype.setInstantiationMode = function (e) {
          return (this.instantiationMode = e), this;
        }),
        (c.prototype.setMultipleInstances = function (e) {
          return (this.multipleInstances = e), this;
        }),
        (c.prototype.setServiceProps = function (e) {
          return (this.serviceProps = e), this;
        }),
        (c.prototype.setInstanceCreatedCallback = function (e) {
          return (this.onInstanceCreated = e), this;
        }),
        c);
      function c(e, t, n) {
        (this.name = e),
          (this.instanceFactory = t),
          (this.type = n),
          (this.multipleInstances = !1),
          (this.serviceProps = {}),
          (this.instantiationMode = "LAZY"),
          (this.onInstanceCreated = null);
      }
      function u(n) {
        return new Promise(function (e, t) {
          (n.onsuccess = function () {
            e(n.result);
          }),
            (n.onerror = function () {
              t(n.error);
            });
        });
      }
      function p(n, i, o) {
        var a,
          e = new Promise(function (e, t) {
            u((a = n[i].apply(n, o))).then(e, t);
          });
        return (e.request = a), e;
      }
      function d(e, n, t) {
        t.forEach(function (t) {
          Object.defineProperty(e.prototype, t, {
            get: function () {
              return this[n][t];
            },
            set: function (e) {
              this[n][t] = e;
            },
          });
        });
      }
      function l(t, n, i, e) {
        e.forEach(function (e) {
          e in i.prototype &&
            (t.prototype[e] = function () {
              return p(this[n], e, arguments);
            });
        });
      }
      function f(t, n, i, e) {
        e.forEach(function (e) {
          e in i.prototype &&
            (t.prototype[e] = function () {
              return this[n][e].apply(this[n], arguments);
            });
        });
      }
      function g(e, i, t, n) {
        n.forEach(function (n) {
          n in t.prototype &&
            (e.prototype[n] = function () {
              return (
                (e = this[i]),
                (t = p(e, n, arguments)).then(function (e) {
                  if (e) return new w(e, t.request);
                })
              );
              var e, t;
            });
        });
      }
      function h(e) {
        this._index = e;
      }
      function w(e, t) {
        (this._cursor = e), (this._request = t);
      }
      function m(e) {
        this._store = e;
      }
      function y(n) {
        (this._tx = n),
          (this.complete = new Promise(function (e, t) {
            (n.oncomplete = function () {
              e();
            }),
              (n.onerror = function () {
                t(n.error);
              }),
              (n.onabort = function () {
                t(n.error);
              });
          }));
      }
      function b(e, t, n) {
        (this._db = e), (this.oldVersion = t), (this.transaction = new y(n));
      }
      function v(e) {
        this._db = e;
      }
      function k(e, t, n) {
        var t = p(indexedDB, "open", [e, t]),
          i = t.request;
        return (
          i &&
            (i.onupgradeneeded = function (e) {
              n && n(new b(i.result, e.oldVersion, i.transaction));
            }),
          t.then(function (e) {
            return new v(e);
          })
        );
      }
      function I(e) {
        return p(indexedDB, "deleteDatabase", [e]);
      }
      d(h, "_index", ["name", "keyPath", "multiEntry", "unique"]),
        l(h, "_index", IDBIndex, [
          "get",
          "getKey",
          "getAll",
          "getAllKeys",
          "count",
        ]),
        g(h, "_index", IDBIndex, ["openCursor", "openKeyCursor"]),
        d(w, "_cursor", ["direction", "key", "primaryKey", "value"]),
        l(w, "_cursor", IDBCursor, ["update", "delete"]),
        ["advance", "continue", "continuePrimaryKey"].forEach(function (n) {
          n in IDBCursor.prototype &&
            (w.prototype[n] = function () {
              var t = this,
                e = arguments;
              return Promise.resolve().then(function () {
                return (
                  t._cursor[n].apply(t._cursor, e),
                  u(t._request).then(function (e) {
                    if (e) return new w(e, t._request);
                  })
                );
              });
            });
        }),
        (m.prototype.createIndex = function () {
          return new h(this._store.createIndex.apply(this._store, arguments));
        }),
        (m.prototype.index = function () {
          return new h(this._store.index.apply(this._store, arguments));
        }),
        d(m, "_store", ["name", "keyPath", "indexNames", "autoIncrement"]),
        l(m, "_store", IDBObjectStore, [
          "put",
          "add",
          "delete",
          "clear",
          "get",
          "getAll",
          "getKey",
          "getAllKeys",
          "count",
        ]),
        g(m, "_store", IDBObjectStore, ["openCursor", "openKeyCursor"]),
        f(m, "_store", IDBObjectStore, ["deleteIndex"]),
        (y.prototype.objectStore = function () {
          return new m(this._tx.objectStore.apply(this._tx, arguments));
        }),
        d(y, "_tx", ["objectStoreNames", "mode"]),
        f(y, "_tx", IDBTransaction, ["abort"]),
        (b.prototype.createObjectStore = function () {
          return new m(this._db.createObjectStore.apply(this._db, arguments));
        }),
        d(b, "_db", ["name", "version", "objectStoreNames"]),
        f(b, "_db", IDBDatabase, ["deleteObjectStore", "close"]),
        (v.prototype.transaction = function () {
          return new y(this._db.transaction.apply(this._db, arguments));
        }),
        d(v, "_db", ["name", "version", "objectStoreNames"]),
        f(v, "_db", IDBDatabase, ["close"]),
        ["openCursor", "openKeyCursor"].forEach(function (o) {
          [m, h].forEach(function (e) {
            o in e.prototype &&
              (e.prototype[o.replace("open", "iterate")] = function () {
                var e = ((n = arguments), Array.prototype.slice.call(n)),
                  t = e[e.length - 1],
                  n = this._store || this._index,
                  i = n[o].apply(n, e.slice(0, -1));
                i.onsuccess = function () {
                  t(i.result);
                };
              });
          });
        }),
        [h, m].forEach(function (e) {
          e.prototype.getAll ||
            (e.prototype.getAll = function (e, n) {
              var i = this,
                o = [];
              return new Promise(function (t) {
                i.iterateCursor(e, function (e) {
                  e
                    ? (o.push(e.value),
                      void 0 === n || o.length != n ? e.continue() : t(o))
                    : t(o);
                });
              });
            });
        });
      var S = "@firebase/installations",
        T = "0.5.2";
      const _ = 1e4,
        C = `w:${T}`,
        j = "FIS_v2",
        O = "https://firebaseinstallations.googleapis.com/v1",
        D = 36e5;
      var P, A, E, K;
      const M = new i("installations", "Installations", {
        "missing-app-config-values":
          'Missing App configuration value: "{$valueName}"',
        "not-registered": "Firebase Installation is not registered.",
        "installation-not-found": "Firebase Installation not found.",
        "request-failed":
          '{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',
        "app-offline": "Could not process request. Application offline.",
        "delete-pending-registration":
          "Can't delete installation while there is a pending registration request.",
      });
      function N(e) {
        return e instanceof o && e.code.includes("request-failed");
      }
      function x({ projectId: e }) {
        return `${O}/projects/${e}/installations`;
      }
      function $(e) {
        return {
          token: e.token,
          requestStatus: 2,
          expiresIn: ((e = e.expiresIn), Number(e.replace("s", "000"))),
          creationTime: Date.now(),
        };
      }
      async function B(e, t) {
        t = (await t.json()).error;
        return M.create("request-failed", {
          requestName: e,
          serverCode: t.code,
          serverMessage: t.message,
          serverStatus: t.status,
        });
      }
      function F({ apiKey: e }) {
        return new Headers({
          "Content-Type": "application/json",
          Accept: "application/json",
          "x-goog-api-key": e,
        });
      }
      function q(e, { refreshToken: t }) {
        const n = F(e);
        return n.append("Authorization", ((t = t), `${j} ${t}`)), n;
      }
      async function L(e) {
        var t = await e();
        return 500 <= t.status && t.status < 600 ? e() : t;
      }
      function V(t) {
        return new Promise((e) => {
          setTimeout(e, t);
        });
      }
      const R = /^[cdef][\w-]{21}$/,
        H = "";
      function W() {
        try {
          const t = new Uint8Array(17),
            n = self.crypto || self.msCrypto;
          n.getRandomValues(t), (t[0] = 112 + (t[0] % 16));
          var e = (function (e) {
            const t = (function (e) {
              const t = btoa(String.fromCharCode(...e));
              return t.replace(/\+/g, "-").replace(/\//g, "_");
            })(e);
            return t.substr(0, 22);
          })(t);
          return R.test(e) ? e : H;
        } catch (e) {
          return H;
        }
      }
      function U(e) {
        return `${e.appName}!${e.appId}`;
      }
      const G = new Map();
      function J(e, t) {
        e = U(e);
        z(e, t),
          (function (e, t) {
            const n = (function () {
              !Y &&
                "BroadcastChannel" in self &&
                ((Y = new BroadcastChannel("[Firebase] FID Change")),
                (Y.onmessage = (e) => {
                  z(e.data.key, e.data.fid);
                }));
              return Y;
            })();
            n && n.postMessage({ key: e, fid: t });
            0 === G.size && Y && (Y.close(), (Y = null));
          })(e, t);
      }
      function z(e, t) {
        e = G.get(e);
        if (e) for (const n of e) n(t);
      }
      let Y = null;
      const Q = "firebase-installations-store";
      let Z = null;
      function X() {
        return (
          (Z =
            Z ||
            k("firebase-installations-database", 1, (e) => {
              0 === e.oldVersion && e.createObjectStore(Q);
            })),
          Z
        );
      }
      async function ee(e, t) {
        var n = U(e);
        const i = await X(),
          o = i.transaction(Q, "readwrite"),
          a = o.objectStore(Q);
        var r = await a.get(n);
        return (
          await a.put(t, n),
          await o.complete,
          (r && r.fid === t.fid) || J(e, t.fid),
          t
        );
      }
      async function te(e) {
        e = U(e);
        const t = await X(),
          n = t.transaction(Q, "readwrite");
        await n.objectStore(Q).delete(e), await n.complete;
      }
      async function ne(e, t) {
        var n = U(e);
        const i = await X(),
          o = i.transaction(Q, "readwrite"),
          a = o.objectStore(Q);
        var r = await a.get(n),
          t = t(r);
        return (
          void 0 === t ? await a.delete(n) : await a.put(t, n),
          await o.complete,
          !t || (r && r.fid === t.fid) || J(e, t.fid),
          t
        );
      }
      async function ie(t) {
        let n;
        var e = await ne(t, (e) => {
          (e = (function (e) {
            e = e || { fid: W(), registrationStatus: 0 };
            return ae(e);
          })(e)),
            (e = (function (e, t) {
              {
                if (0 !== t.registrationStatus)
                  return 1 === t.registrationStatus
                    ? {
                        installationEntry: t,
                        registrationPromise: (async function (e) {
                          let t = await oe(e);
                          for (; 1 === t.registrationStatus; )
                            await V(100), (t = await oe(e));
                          if (0 !== t.registrationStatus) return t;
                          {
                            var {
                              installationEntry: n,
                              registrationPromise: i,
                            } = await ie(e);
                            return i || n;
                          }
                        })(e),
                      }
                    : { installationEntry: t };
                if (!navigator.onLine) {
                  var n = Promise.reject(M.create("app-offline"));
                  return { installationEntry: t, registrationPromise: n };
                }
                (t = {
                  fid: t.fid,
                  registrationStatus: 1,
                  registrationTime: Date.now(),
                }),
                  (e = (async function (t, n) {
                    try {
                      var e = await (async function (e, { fid: t }) {
                        const n = x(e);
                        var i = F(e),
                          e = {
                            fid: t,
                            authVersion: j,
                            appId: e.appId,
                            sdkVersion: C,
                          };
                        const o = {
                            method: "POST",
                            headers: i,
                            body: JSON.stringify(e),
                          },
                          a = await L(() => fetch(n, o));
                        if (a.ok) {
                          e = await a.json();
                          return {
                            fid: e.fid || t,
                            registrationStatus: 2,
                            refreshToken: e.refreshToken,
                            authToken: $(e.authToken),
                          };
                        }
                        throw await B("Create Installation", a);
                      })(t, n);
                      return ee(t, e);
                    } catch (e) {
                      throw (
                        (N(e) && 409 === e.customData.serverCode
                          ? await te(t)
                          : await ee(t, { fid: n.fid, registrationStatus: 0 }),
                        e)
                      );
                    }
                  })(e, t));
                return { installationEntry: t, registrationPromise: e };
              }
            })(t, e));
          return (n = e.registrationPromise), e.installationEntry;
        });
        return e.fid === H
          ? { installationEntry: await n }
          : { installationEntry: e, registrationPromise: n };
      }
      function oe(e) {
        return ne(e, (e) => {
          if (!e) throw M.create("installation-not-found");
          return ae(e);
        });
      }
      function ae(e) {
        return 1 === (t = e).registrationStatus &&
          t.registrationTime + _ < Date.now()
          ? { fid: e.fid, registrationStatus: 0 }
          : e;
        var t;
      }
      async function re({ appConfig: e, platformLoggerProvider: t }, n) {
        const i =
          (([o, a] = [e, n["fid"]]), `${x(o)}/${a}/authTokens:generate`);
        var o, a;
        const r = q(e, n),
          s = t.getImmediate({ optional: !0 });
        s && r.append("x-firebase-client", s.getPlatformInfoString());
        t = { installation: { sdkVersion: C } };
        const c = { method: "POST", headers: r, body: JSON.stringify(t) },
          u = await L(() => fetch(i, c));
        if (u.ok) return $(await u.json());
        throw await B("Generate Auth Token", u);
      }
      async function se(i, o = !1) {
        let a;
        var e = await ne(i.appConfig, (e) => {
          if (!ue(e)) throw M.create("not-registered");
          var t,
            n = e.authToken;
          if (
            o ||
            2 !== (t = n).requestStatus ||
            (function (e) {
              var t = Date.now();
              return t < e.creationTime || e.creationTime + e.expiresIn < t + D;
            })(t)
          ) {
            if (1 === n.requestStatus)
              return (
                (a = (async function (e, t) {
                  let n = await ce(e.appConfig);
                  for (; 1 === n.authToken.requestStatus; )
                    await V(100), (n = await ce(e.appConfig));
                  var i = n.authToken;
                  return 0 === i.requestStatus ? se(e, t) : i;
                })(i, o)),
                e
              );
            if (!navigator.onLine) throw M.create("app-offline");
            n =
              ((t = e),
              (n = { requestStatus: 1, requestTime: Date.now() }),
              Object.assign(Object.assign({}, t), { authToken: n }));
            return (
              (a = (async function (t, n) {
                try {
                  var e = await re(t, n),
                    i = Object.assign(Object.assign({}, n), { authToken: e });
                  return await ee(t.appConfig, i), e;
                } catch (e) {
                  throw (
                    (!N(e) ||
                    (401 !== e.customData.serverCode &&
                      404 !== e.customData.serverCode)
                      ? ((n = Object.assign(Object.assign({}, n), {
                          authToken: { requestStatus: 0 },
                        })),
                        await ee(t.appConfig, n))
                      : await te(t.appConfig),
                    e)
                  );
                }
              })(i, n)),
              n
            );
          }
          return e;
        });
        return a ? await a : e.authToken;
      }
      function ce(e) {
        return ne(e, (e) => {
          if (!ue(e)) throw M.create("not-registered");
          var t = e.authToken;
          return 1 === (t = t).requestStatus && t.requestTime + _ < Date.now()
            ? Object.assign(Object.assign({}, e), {
                authToken: { requestStatus: 0 },
              })
            : e;
        });
      }
      function ue(e) {
        return void 0 !== e && 2 === e.registrationStatus;
      }
      async function pe(e, t = !1) {
        var n = e;
        return (
          await ((e = (await ie((e = n.appConfig))).registrationPromise) &&
            (await e)),
          (await se(n, t)).token
        );
      }
      function de(e) {
        return M.create("missing-app-config-values", { valueName: e });
      }
      const le = "installations",
        fe = (e) => {
          e = e.getProvider("app").getImmediate();
          return {
            app: e,
            appConfig: (function (e) {
              if (!e || !e.options) throw de("App Configuration");
              if (!e.name) throw de("App Name");
              for (const t of ["projectId", "apiKey", "appId"])
                if (!e.options[t]) throw de(t);
              return {
                appName: e.name,
                projectId: e.options.projectId,
                apiKey: e.options.apiKey,
                appId: e.options.appId,
              };
            })(e),
            platformLoggerProvider: qt._getProvider(e, "platform-logger"),
            _delete: () => Promise.resolve(),
          };
        },
        ge = (e) => {
          e = e.getProvider("app").getImmediate();
          const t = qt._getProvider(e, le).getImmediate();
          return {
            getId: () =>
              (async function (e) {
                const { installationEntry: t, registrationPromise: n } =
                  await ie(e.appConfig);
                return (n || se(e)).catch(console.error), t.fid;
              })(t),
            getToken: (e) => pe(t, e),
          };
        };
      qt._registerComponent(new s(le, fe, "PUBLIC")),
        qt._registerComponent(new s("installations-internal", ge, "PRIVATE")),
        qt.registerVersion(S, T),
        qt.registerVersion(S, T, "esm2017");
      const he = "/firebase-messaging-sw.js",
        we = "/firebase-cloud-messaging-push-scope",
        me =
          "BDOU99-h67HcA6JeFXHbSNMu7e2yNNu3RzoMj8TM4W88jITfq7ZmPvIM1Iv-4_l2LxQcYwhqby2xGpWwzjfAnG4",
        ye = "https://fcmregistrations.googleapis.com/v1",
        be = "google.c.a.c_id",
        ve = "google.c.a.e";
      function ke(e) {
        e = new Uint8Array(e);
        const t = btoa(String.fromCharCode(...e));
        return t.replace(/=/g, "").replace(/\+/g, "-").replace(/\//g, "_");
      }
      ((K = K = K || {})[(K.DATA_MESSAGE = 1)] = "DATA_MESSAGE"),
        (K[(K.DISPLAY_NOTIFICATION = 3)] = "DISPLAY_NOTIFICATION"),
        ((K = P = P || {}).PUSH_RECEIVED = "push-received"),
        (K.NOTIFICATION_CLICKED = "notification-clicked");
      const Ie = "fcm_token_details_db",
        Se = "fcm_token_object_Store";
      async function Te(a) {
        if ("databases" in indexedDB) {
          const t = await indexedDB.databases(),
            n = t.map((e) => e.name);
          if (!n.includes(Ie)) return null;
        }
        let r = null;
        const e = await k(Ie, 5, async (e) => {
          var t;
          if (!(e.oldVersion < 2) && e.objectStoreNames.contains(Se)) {
            const o = e.transaction.objectStore(Se);
            var n,
              i = await o.index("fcmSenderId").get(a);
            await o.clear(),
              i &&
                (2 === e.oldVersion
                  ? (n = i).auth &&
                    n.p256dh &&
                    n.endpoint &&
                    (r = {
                      token: n.fcmToken,
                      createTime:
                        null !== (t = n.createTime) && void 0 !== t
                          ? t
                          : Date.now(),
                      subscriptionOptions: {
                        auth: n.auth,
                        p256dh: n.p256dh,
                        endpoint: n.endpoint,
                        swScope: n.swScope,
                        vapidKey:
                          "string" == typeof n.vapidKey
                            ? n.vapidKey
                            : ke(n.vapidKey),
                      },
                    })
                  : 3 === e.oldVersion
                  ? ((n = i),
                    (r = {
                      token: n.fcmToken,
                      createTime: n.createTime,
                      subscriptionOptions: {
                        auth: ke(n.auth),
                        p256dh: ke(n.p256dh),
                        endpoint: n.endpoint,
                        swScope: n.swScope,
                        vapidKey: ke(n.vapidKey),
                      },
                    }))
                  : 4 === e.oldVersion &&
                    ((i = i),
                    (r = {
                      token: i.fcmToken,
                      createTime: i.createTime,
                      subscriptionOptions: {
                        auth: ke(i.auth),
                        p256dh: ke(i.p256dh),
                        endpoint: i.endpoint,
                        swScope: i.swScope,
                        vapidKey: ke(i.vapidKey),
                      },
                    })));
          }
        });
        return (
          e.close(),
          await I(Ie),
          await I("fcm_vapid_details_db"),
          await I("undefined"),
          (function (e) {
            if (!e || !e.subscriptionOptions) return !1;
            var t = e["subscriptionOptions"];
            return (
              "number" == typeof e.createTime &&
              0 < e.createTime &&
              "string" == typeof e.token &&
              0 < e.token.length &&
              "string" == typeof t.auth &&
              0 < t.auth.length &&
              "string" == typeof t.p256dh &&
              0 < t.p256dh.length &&
              "string" == typeof t.endpoint &&
              0 < t.endpoint.length &&
              "string" == typeof t.swScope &&
              0 < t.swScope.length &&
              "string" == typeof t.vapidKey &&
              0 < t.vapidKey.length
            );
          })(r)
            ? r
            : null
        );
      }
      const _e = "firebase-messaging-database",
        Ce = 1,
        je = "firebase-messaging-store";
      let Oe = null;
      function De() {
        return (
          (Oe =
            Oe ||
            k(_e, Ce, (e) => {
              0 === e.oldVersion && e.createObjectStore(je);
            })),
          Oe
        );
      }
      async function Pe(e) {
        var t = Ee(e);
        const n = await De();
        t = await n.transaction(je).objectStore(je).get(t);
        if (t) return t;
        t = await Te(e.appConfig.senderId);
        return t ? (await Ae(e, t), t) : void 0;
      }
      async function Ae(e, t) {
        e = Ee(e);
        const n = await De(),
          i = n.transaction(je, "readwrite");
        return await i.objectStore(je).put(t, e), await i.complete, t;
      }
      function Ee({ appConfig: e }) {
        return e.appId;
      }
      const Ke = new i("messaging", "Messaging", {
        "missing-app-config-values":
          'Missing App configuration value: "{$valueName}"',
        "only-available-in-window":
          "This method is available in a Window context.",
        "only-available-in-sw":
          "This method is available in a service worker context.",
        "permission-default":
          "The notification permission was not granted and dismissed instead.",
        "permission-blocked":
          "The notification permission was not granted and blocked instead.",
        "unsupported-browser":
          "This browser doesn't support the API's required to use the firebase SDK.",
        "indexed-db-unsupported":
          "This browser doesn't support indexedDb.open() (ex. Safari iFrame, Firefox Private Browsing, etc)",
        "failed-service-worker-registration":
          "We are unable to register the default service worker. {$browserErrorMessage}",
        "token-subscribe-failed":
          "A problem occurred while subscribing the user to FCM: {$errorInfo}",
        "token-subscribe-no-token":
          "FCM returned no token when subscribing the user to push.",
        "token-unsubscribe-failed":
          "A problem occurred while unsubscribing the user from FCM: {$errorInfo}",
        "token-update-failed":
          "A problem occurred while updating the user from FCM: {$errorInfo}",
        "token-update-no-token":
          "FCM returned no token when updating the user to push.",
        "use-sw-after-get-token":
          "The useServiceWorker() method may only be called once and must be called before calling getToken() to ensure your service worker is used.",
        "invalid-sw-registration":
          "The input to useServiceWorker() must be a ServiceWorkerRegistration.",
        "invalid-bg-handler":
          "The input to setBackgroundMessageHandler() must be a function.",
        "invalid-vapid-key": "The public VAPID key must be a string.",
        "use-vapid-key-after-get-token":
          "The usePublicVapidKey() method may only be called once and must be called before calling getToken() to ensure your VAPID key is used.",
      });
      async function Me(e, t) {
        var n = { method: "DELETE", headers: await xe(e) };
        try {
          const a = await fetch(`${Ne(e.appConfig)}/${t}`, n);
          var i = await a.json();
          if (i.error) {
            var o = i.error.message;
            throw Ke.create("token-unsubscribe-failed", { errorInfo: o });
          }
        } catch (e) {
          throw Ke.create("token-unsubscribe-failed", { errorInfo: e });
        }
      }
      function Ne({ projectId: e }) {
        return `${ye}/projects/${e}/registrations`;
      }
      async function xe({ appConfig: e, installations: t }) {
        t = await t.getToken();
        return new Headers({
          "Content-Type": "application/json",
          Accept: "application/json",
          "x-goog-api-key": e.apiKey,
          "x-goog-firebase-installations-auth": `FIS ${t}`,
        });
      }
      function $e({ p256dh: e, auth: t, endpoint: n, vapidKey: i }) {
        const o = { web: { endpoint: n, auth: t, p256dh: e } };
        return i !== me && (o.web.applicationPubKey = i), o;
      }
      const Be = 6048e5;
      async function Fe(e) {
        const t = await (async function (e, t) {
          var n = await e.pushManager.getSubscription();
          if (n) return n;
          return e.pushManager.subscribe({
            userVisibleOnly: !0,
            applicationServerKey: (function (e) {
              e = (e + "=".repeat((4 - (e.length % 4)) % 4))
                .replace(/\-/g, "+")
                .replace(/_/g, "/");
              const t = atob(e),
                n = new Uint8Array(t.length);
              for (let e = 0; e < t.length; ++e) n[e] = t.charCodeAt(e);
              return n;
            })(t),
          });
        })(e.swRegistration, e.vapidKey);
        var n,
          i,
          o,
          a,
          r = {
            vapidKey: e.vapidKey,
            swScope: e.swRegistration.scope,
            endpoint: t.endpoint,
            auth: ke(t.getKey("auth")),
            p256dh: ke(t.getKey("p256dh")),
          },
          s = await Pe(e.firebaseDependencies);
        if (s) {
          if (
            ((n = s.subscriptionOptions),
            (i = r.vapidKey === n.vapidKey),
            (o = r.endpoint === n.endpoint),
            (a = r.auth === n.auth),
            (n = r.p256dh === n.p256dh),
            i && o && a && n)
          )
            return Date.now() >= s.createTime + Be
              ? (async function (t, e) {
                  try {
                    var n = await (async function (e, t) {
                        var n = await xe(e),
                          i = $e(t.subscriptionOptions),
                          i = {
                            method: "PATCH",
                            headers: n,
                            body: JSON.stringify(i),
                          };
                        let o;
                        try {
                          const a = await fetch(
                            `${Ne(e.appConfig)}/${t.token}`,
                            i
                          );
                          o = await a.json();
                        } catch (e) {
                          throw Ke.create("token-update-failed", {
                            errorInfo: e,
                          });
                        }
                        if (o.error) {
                          i = o.error.message;
                          throw Ke.create("token-update-failed", {
                            errorInfo: i,
                          });
                        }
                        if (!o.token) throw Ke.create("token-update-no-token");
                        return o.token;
                      })(t.firebaseDependencies, e),
                      i = Object.assign(Object.assign({}, e), {
                        token: n,
                        createTime: Date.now(),
                      });
                    return await Ae(t.firebaseDependencies, i), n;
                  } catch (e) {
                    throw (await qe(t), e);
                  }
                })(e, {
                  token: s.token,
                  createTime: Date.now(),
                  subscriptionOptions: r,
                })
              : s.token;
          try {
            await Me(e.firebaseDependencies, s.token);
          } catch (e) {
            console.warn(e);
          }
          return Le(e.firebaseDependencies, r);
        }
        return Le(e.firebaseDependencies, r);
      }
      async function qe(e) {
        var t = await Pe(e.firebaseDependencies);
        t &&
          (await Me(e.firebaseDependencies, t.token),
          await (async function (e) {
            e = Ee(e);
            const t = await De(),
              n = t.transaction(je, "readwrite");
            await n.objectStore(je).delete(e), await n.complete;
          })(e.firebaseDependencies));
        const n = await e.swRegistration.pushManager.getSubscription();
        return !n || n.unsubscribe();
      }
      async function Le(e, t) {
        t = {
          token: await (async function (e, t) {
            var n = await xe(e),
              t = $e(t),
              t = { method: "POST", headers: n, body: JSON.stringify(t) };
            let i;
            try {
              const o = await fetch(Ne(e.appConfig), t);
              i = await o.json();
            } catch (e) {
              throw Ke.create("token-subscribe-failed", { errorInfo: e });
            }
            if (i.error) {
              t = i.error.message;
              throw Ke.create("token-subscribe-failed", { errorInfo: t });
            }
            if (!i.token) throw Ke.create("token-subscribe-no-token");
            return i.token;
          })(e, t),
          createTime: Date.now(),
          subscriptionOptions: t,
        };
        return await Ae(e, t), t.token;
      }
      function Ve(e) {
        var t,
          n,
          i,
          o = {
            from: e.from,
            collapseKey: e.collapse_key,
            messageId: e.fcm_message_id,
          };
        return (
          (n = o),
          (i = e).notification &&
            ((n.notification = {}),
            (t = i.notification.title) && (n.notification.title = t),
            (t = i.notification.body) && (n.notification.body = t),
            (i = i.notification.image) && (n.notification.image = i)),
          (n = o),
          (i = e).data && (n.data = i.data),
          (n = o),
          (i = e).fcmOptions &&
            ((n.fcmOptions = {}),
            (e = i.fcmOptions.link) && (n.fcmOptions.link = e),
            (i = i.fcmOptions.analytics_label) &&
              (n.fcmOptions.analyticsLabel = i)),
          o
        );
      }
      function Re(t, n) {
        const i = [];
        for (let e = 0; e < t.length; e++)
          i.push(t.charAt(e)), e < n.length && i.push(n.charAt(e));
        return i.join("");
      }
      function He(e) {
        return Ke.create("missing-app-config-values", { valueName: e });
      }
      Re("hts/frbslgigp.ogepscmv/ieo/eaylg", "tp:/ieaeogn-agolai.o/1frlglgc/o"),
        Re("AzSCbw63g1R0nCw85jG8", "Iaya3yLKwmgvh7cF0q4");
      class We {
        constructor(e, t, n) {
          (this.deliveryMetricsExportedToBigQueryEnabled = !1),
            (this.onBackgroundMessageHandler = null),
            (this.onMessageHandler = null),
            (this.logEvents = []),
            (this.isLogServiceStarted = !1);
          var i = (function (e) {
            if (!e || !e.options) throw He("App Configuration Object");
            if (!e.name) throw He("App Name");
            var t = e["options"];
            for (const n of [
              "projectId",
              "apiKey",
              "appId",
              "messagingSenderId",
            ])
              if (!t[n]) throw He(n);
            return {
              appName: e.name,
              projectId: t.projectId,
              apiKey: t.apiKey,
              appId: t.appId,
              senderId: t.messagingSenderId,
            };
          })(e);
          this.firebaseDependencies = {
            app: e,
            appConfig: i,
            installations: t,
            analyticsProvider: n,
          };
        }
        _delete() {
          return Promise.resolve();
        }
      }
      async function Ue(e) {
        try {
          (e.swRegistration = await navigator.serviceWorker.register(he, {
            scope: we,
          })),
            e.swRegistration.update().catch(() => {});
        } catch (e) {
          throw Ke.create("failed-service-worker-registration", {
            browserErrorMessage: e.message,
          });
        }
      }
      async function Ge(e, t) {
        if (!navigator) throw Ke.create("only-available-in-window");
        if (
          ("default" === Notification.permission &&
            (await Notification.requestPermission()),
          "granted" !== Notification.permission)
        )
          throw Ke.create("permission-blocked");
        var n, i;
        return (
          (n = e),
          await ((i = null == t ? void 0 : t.vapidKey)
            ? (n.vapidKey = i)
            : n.vapidKey || (n.vapidKey = me)),
          await (async function (e, t) {
            if (
              (t || e.swRegistration || (await Ue(e)), t || !e.swRegistration)
            ) {
              if (!(t instanceof ServiceWorkerRegistration))
                throw Ke.create("invalid-sw-registration");
              e.swRegistration = t;
            }
          })(e, null == t ? void 0 : t.serviceWorkerRegistration),
          Fe(e)
        );
      }
      async function Je(e, t, n) {
        t = (function (e) {
          switch (e) {
            case P.NOTIFICATION_CLICKED:
              return "notification_open";
            case P.PUSH_RECEIVED:
              return "notification_foreground";
            default:
              throw new Error();
          }
        })(t);
        const i = await e.firebaseDependencies.analyticsProvider.get();
        i.logEvent(t, {
          message_id: n[be],
          message_name: n["google.c.a.c_l"],
          message_time: n["google.c.a.ts"],
          message_device_time: Math.floor(Date.now() / 1e3),
        });
      }
      async function ze(e, t) {
        var n,
          i = t.data;
        i.isFirebaseMessaging &&
          (e.onMessageHandler &&
            i.messageType === P.PUSH_RECEIVED &&
            ("function" == typeof e.onMessageHandler
              ? e.onMessageHandler(Ve(i))
              : e.onMessageHandler.next(Ve(i))),
          (n = i.data),
          "object" == typeof (t = n) &&
            t &&
            be in t &&
            "1" === n[ve] &&
            (await Je(e, i.messageType, n)));
      }
      const Ye = "@firebase/messaging",
        Qe = (e) => {
          const t = new We(
            e.getProvider("app").getImmediate(),
            e.getProvider("installations-internal").getImmediate(),
            e.getProvider("analytics-internal")
          );
          return (
            navigator.serviceWorker.addEventListener("message", (e) =>
              ze(t, e)
            ),
            t
          );
        },
        Ze = (e) => {
          const t = e.getProvider("messaging").getImmediate();
          return { getToken: (e) => Ge(t, e) };
        };
      function Xe(e) {
        return (async function (e) {
          if (!navigator) throw Ke.create("only-available-in-window");
          return e.swRegistration || (await Ue(e)), qe(e);
        })((e = r(e)));
      }
      function et(e, t) {
        return (function (e, t) {
          if (!navigator) throw Ke.create("only-available-in-window");
          return (
            (e.onMessageHandler = t),
            () => {
              e.onMessageHandler = null;
            }
          );
        })((e = r(e)), t);
      }
      qt._registerComponent(new s("messaging", Qe, "PUBLIC")),
        qt._registerComponent(new s("messaging-internal", Ze, "PRIVATE")),
        qt.registerVersion(Ye, "0.9.2"),
        qt.registerVersion(Ye, "0.9.2", "esm2017");
      const tt =
          "BDOU99-h67HcA6JeFXHbSNMu7e2yNNu3RzoMj8TM4W88jITfq7ZmPvIM1Iv-4_l2LxQcYwhqby2xGpWwzjfAnG4",
        nt = "https://fcmregistrations.googleapis.com/v1",
        it = "FCM_MSG",
        ot = "google.c.a.c_id",
        at = 3,
        rt = 1;
      function st(e) {
        e = new Uint8Array(e);
        const t = btoa(String.fromCharCode(...e));
        return t.replace(/=/g, "").replace(/\+/g, "-").replace(/\//g, "_");
      }
      ((K = A = A || {})[(K.DATA_MESSAGE = 1)] = "DATA_MESSAGE"),
        (K[(K.DISPLAY_NOTIFICATION = 3)] = "DISPLAY_NOTIFICATION"),
        ((K = E = E || {}).PUSH_RECEIVED = "push-received"),
        (K.NOTIFICATION_CLICKED = "notification-clicked");
      const ct = "fcm_token_details_db",
        ut = "fcm_token_object_Store";
      async function pt(a) {
        if ("databases" in indexedDB) {
          const t = await indexedDB.databases(),
            n = t.map((e) => e.name);
          if (!n.includes(ct)) return null;
        }
        let r = null;
        const e = await k(ct, 5, async (e) => {
          var t;
          if (!(e.oldVersion < 2) && e.objectStoreNames.contains(ut)) {
            const o = e.transaction.objectStore(ut);
            var n,
              i = await o.index("fcmSenderId").get(a);
            await o.clear(),
              i &&
                (2 === e.oldVersion
                  ? (n = i).auth &&
                    n.p256dh &&
                    n.endpoint &&
                    (r = {
                      token: n.fcmToken,
                      createTime:
                        null !== (t = n.createTime) && void 0 !== t
                          ? t
                          : Date.now(),
                      subscriptionOptions: {
                        auth: n.auth,
                        p256dh: n.p256dh,
                        endpoint: n.endpoint,
                        swScope: n.swScope,
                        vapidKey:
                          "string" == typeof n.vapidKey
                            ? n.vapidKey
                            : st(n.vapidKey),
                      },
                    })
                  : 3 === e.oldVersion
                  ? ((n = i),
                    (r = {
                      token: n.fcmToken,
                      createTime: n.createTime,
                      subscriptionOptions: {
                        auth: st(n.auth),
                        p256dh: st(n.p256dh),
                        endpoint: n.endpoint,
                        swScope: n.swScope,
                        vapidKey: st(n.vapidKey),
                      },
                    }))
                  : 4 === e.oldVersion &&
                    ((i = i),
                    (r = {
                      token: i.fcmToken,
                      createTime: i.createTime,
                      subscriptionOptions: {
                        auth: st(i.auth),
                        p256dh: st(i.p256dh),
                        endpoint: i.endpoint,
                        swScope: i.swScope,
                        vapidKey: st(i.vapidKey),
                      },
                    })));
          }
        });
        return (
          e.close(),
          await I(ct),
          await I("fcm_vapid_details_db"),
          await I("undefined"),
          (function (e) {
            if (!e || !e.subscriptionOptions) return !1;
            var t = e["subscriptionOptions"];
            return (
              "number" == typeof e.createTime &&
              0 < e.createTime &&
              "string" == typeof e.token &&
              0 < e.token.length &&
              "string" == typeof t.auth &&
              0 < t.auth.length &&
              "string" == typeof t.p256dh &&
              0 < t.p256dh.length &&
              "string" == typeof t.endpoint &&
              0 < t.endpoint.length &&
              "string" == typeof t.swScope &&
              0 < t.swScope.length &&
              "string" == typeof t.vapidKey &&
              0 < t.vapidKey.length
            );
          })(r)
            ? r
            : null
        );
      }
      const dt = "firebase-messaging-database",
        lt = 1,
        ft = "firebase-messaging-store";
      let gt = null;
      function ht() {
        return (
          (gt =
            gt ||
            k(dt, lt, (e) => {
              0 === e.oldVersion && e.createObjectStore(ft);
            })),
          gt
        );
      }
      async function wt(e) {
        var t = yt(e);
        const n = await ht();
        t = await n.transaction(ft).objectStore(ft).get(t);
        if (t) return t;
        t = await pt(e.appConfig.senderId);
        return t ? (await mt(e, t), t) : void 0;
      }
      async function mt(e, t) {
        e = yt(e);
        const n = await ht(),
          i = n.transaction(ft, "readwrite");
        return await i.objectStore(ft).put(t, e), await i.complete, t;
      }
      function yt({ appConfig: e }) {
        return e.appId;
      }
      const bt = new i("messaging", "Messaging", {
        "missing-app-config-values":
          'Missing App configuration value: "{$valueName}"',
        "only-available-in-window":
          "This method is available in a Window context.",
        "only-available-in-sw":
          "This method is available in a service worker context.",
        "permission-default":
          "The notification permission was not granted and dismissed instead.",
        "permission-blocked":
          "The notification permission was not granted and blocked instead.",
        "unsupported-browser":
          "This browser doesn't support the API's required to use the firebase SDK.",
        "indexed-db-unsupported":
          "This browser doesn't support indexedDb.open() (ex. Safari iFrame, Firefox Private Browsing, etc)",
        "failed-service-worker-registration":
          "We are unable to register the default service worker. {$browserErrorMessage}",
        "token-subscribe-failed":
          "A problem occurred while subscribing the user to FCM: {$errorInfo}",
        "token-subscribe-no-token":
          "FCM returned no token when subscribing the user to push.",
        "token-unsubscribe-failed":
          "A problem occurred while unsubscribing the user from FCM: {$errorInfo}",
        "token-update-failed":
          "A problem occurred while updating the user from FCM: {$errorInfo}",
        "token-update-no-token":
          "FCM returned no token when updating the user to push.",
        "use-sw-after-get-token":
          "The useServiceWorker() method may only be called once and must be called before calling getToken() to ensure your service worker is used.",
        "invalid-sw-registration":
          "The input to useServiceWorker() must be a ServiceWorkerRegistration.",
        "invalid-bg-handler":
          "The input to setBackgroundMessageHandler() must be a function.",
        "invalid-vapid-key": "The public VAPID key must be a string.",
        "use-vapid-key-after-get-token":
          "The usePublicVapidKey() method may only be called once and must be called before calling getToken() to ensure your VAPID key is used.",
      });
      async function vt(e, t) {
        var n = { method: "DELETE", headers: await It(e) };
        try {
          const a = await fetch(`${kt(e.appConfig)}/${t}`, n);
          var i = await a.json();
          if (i.error) {
            var o = i.error.message;
            throw bt.create("token-unsubscribe-failed", { errorInfo: o });
          }
        } catch (e) {
          throw bt.create("token-unsubscribe-failed", { errorInfo: e });
        }
      }
      function kt({ projectId: e }) {
        return `${nt}/projects/${e}/registrations`;
      }
      async function It({ appConfig: e, installations: t }) {
        t = await t.getToken();
        return new Headers({
          "Content-Type": "application/json",
          Accept: "application/json",
          "x-goog-api-key": e.apiKey,
          "x-goog-firebase-installations-auth": `FIS ${t}`,
        });
      }
      function St({ p256dh: e, auth: t, endpoint: n, vapidKey: i }) {
        const o = { web: { endpoint: n, auth: t, p256dh: e } };
        return i !== tt && (o.web.applicationPubKey = i), o;
      }
      async function Tt(e) {
        const t = await (async function (e, t) {
          var n = await e.pushManager.getSubscription();
          if (n) return n;
          return e.pushManager.subscribe({
            userVisibleOnly: !0,
            applicationServerKey: (function (e) {
              e = (e + "=".repeat((4 - (e.length % 4)) % 4))
                .replace(/\-/g, "+")
                .replace(/_/g, "/");
              const t = atob(e),
                n = new Uint8Array(t.length);
              for (let e = 0; e < t.length; ++e) n[e] = t.charCodeAt(e);
              return n;
            })(t),
          });
        })(e.swRegistration, e.vapidKey);
        var n,
          i,
          o,
          a,
          r = {
            vapidKey: e.vapidKey,
            swScope: e.swRegistration.scope,
            endpoint: t.endpoint,
            auth: st(t.getKey("auth")),
            p256dh: st(t.getKey("p256dh")),
          },
          s = await wt(e.firebaseDependencies);
        if (s) {
          if (
            ((n = s.subscriptionOptions),
            (i = r.vapidKey === n.vapidKey),
            (o = r.endpoint === n.endpoint),
            (a = r.auth === n.auth),
            (n = r.p256dh === n.p256dh),
            i && o && a && n)
          )
            return Date.now() >= s.createTime + 6048e5
              ? (async function (t, e) {
                  try {
                    var n = await (async function (e, t) {
                        var n = await It(e),
                          i = St(t.subscriptionOptions),
                          i = {
                            method: "PATCH",
                            headers: n,
                            body: JSON.stringify(i),
                          };
                        let o;
                        try {
                          const a = await fetch(
                            `${kt(e.appConfig)}/${t.token}`,
                            i
                          );
                          o = await a.json();
                        } catch (e) {
                          throw bt.create("token-update-failed", {
                            errorInfo: e,
                          });
                        }
                        if (o.error) {
                          i = o.error.message;
                          throw bt.create("token-update-failed", {
                            errorInfo: i,
                          });
                        }
                        if (!o.token) throw bt.create("token-update-no-token");
                        return o.token;
                      })(t.firebaseDependencies, e),
                      i = Object.assign(Object.assign({}, e), {
                        token: n,
                        createTime: Date.now(),
                      });
                    return await mt(t.firebaseDependencies, i), n;
                  } catch (e) {
                    throw (await _t(t), e);
                  }
                })(e, {
                  token: s.token,
                  createTime: Date.now(),
                  subscriptionOptions: r,
                })
              : s.token;
          try {
            await vt(e.firebaseDependencies, s.token);
          } catch (e) {
            console.warn(e);
          }
          return Ct(e.firebaseDependencies, r);
        }
        return Ct(e.firebaseDependencies, r);
      }
      async function _t(e) {
        var t = await wt(e.firebaseDependencies);
        t &&
          (await vt(e.firebaseDependencies, t.token),
          await (async function (e) {
            e = yt(e);
            const t = await ht(),
              n = t.transaction(ft, "readwrite");
            await n.objectStore(ft).delete(e), await n.complete;
          })(e.firebaseDependencies));
        const n = await e.swRegistration.pushManager.getSubscription();
        return !n || n.unsubscribe();
      }
      async function Ct(e, t) {
        t = {
          token: await (async function (e, t) {
            var n = await It(e),
              t = St(t),
              t = { method: "POST", headers: n, body: JSON.stringify(t) };
            let i;
            try {
              const o = await fetch(kt(e.appConfig), t);
              i = await o.json();
            } catch (e) {
              throw bt.create("token-subscribe-failed", { errorInfo: e });
            }
            if (i.error) {
              t = i.error.message;
              throw bt.create("token-subscribe-failed", { errorInfo: t });
            }
            if (!i.token) throw bt.create("token-subscribe-no-token");
            return i.token;
          })(e, t),
          createTime: Date.now(),
          subscriptionOptions: t,
        };
        return await mt(e, t), t.token;
      }
      async function jt(e, t) {
        t = (function (e, t) {
          const n = {};
          e.from && (n.project_number = e.from);
          e.fcm_message_id && (n.message_id = e.fcm_message_id);
          (n.instance_id = t),
            e.notification
              ? (n.message_type = A.DISPLAY_NOTIFICATION.toString())
              : (n.message_type = A.DATA_MESSAGE.toString());
          (n.sdk_platform = at.toString()),
            (n.package_name = self.origin.replace(/(^\w+:|^)\/\//, "")),
            e.collapse_key && (n.collapse_key = e.collapse_key);
          (n.event = rt.toString()),
            null !== (t = e.fcmOptions) &&
              void 0 !== t &&
              t.analytics_label &&
              (n.analytics_label =
                null === (e = e.fcmOptions) || void 0 === e
                  ? void 0
                  : e.analytics_label);
          return n;
        })(t, await e.firebaseDependencies.installations.getId());
        !(function (e, t) {
          const n = {};
          (n.event_time_ms = Math.floor(Date.now()).toString()),
            (n.source_extension_json_proto3 = JSON.stringify(t)),
            e.logEvents.push(n);
        })(e, t);
      }
      function Ot(t, n) {
        const i = [];
        for (let e = 0; e < t.length; e++)
          i.push(t.charAt(e)), e < n.length && i.push(n.charAt(e));
        return i.join("");
      }
      async function Dt(e, t) {
        var n = (function ({ data: e }) {
          if (!e) return null;
          try {
            return e.json();
          } catch (e) {
            return null;
          }
        })(e);
        if (n) {
          t.deliveryMetricsExportedToBigQueryEnabled && (await jt(t, n));
          var i,
            o,
            a = await At();
          if (
            a.some(
              (e) =>
                "visible" === e.visibilityState &&
                !e.url.startsWith("chrome-extension://")
            )
          )
            return (function (e, t) {
              (t.isFirebaseMessaging = !0), (t.messageType = E.PUSH_RECEIVED);
              for (const n of e) n.postMessage(t);
            })(a, n);
          n.notification &&
            (await (function (e) {
              var t = e["actions"],
                n = Notification["maxActions"];
              t &&
                n &&
                t.length > n &&
                console.warn(
                  `This browser only supports ${n} actions. The remaining actions will not be displayed.`
                );
              return self.registration.showNotification(
                null !== (n = e.title) && void 0 !== n ? n : "",
                e
              );
            })(
              (function (e) {
                const t = Object.assign({}, e.notification);
                return (t.data = { [it]: e }), t;
              })(n)
            )),
            t &&
              t.onBackgroundMessageHandler &&
              ((o = {
                from: (i = n).from,
                collapseKey: i.collapse_key,
                messageId: i.fcm_message_id,
              }),
              (e = o),
              (a = i).notification &&
                ((e.notification = {}),
                (n = a.notification.title) && (e.notification.title = n),
                (n = a.notification.body) && (e.notification.body = n),
                (a = a.notification.image) && (e.notification.image = a)),
              (e = o),
              (a = i).data && (e.data = a.data),
              (e = o),
              (a = i).fcmOptions &&
                ((e.fcmOptions = {}),
                (i = a.fcmOptions.link) && (e.fcmOptions.link = i),
                (a = a.fcmOptions.analytics_label) &&
                  (e.fcmOptions.analyticsLabel = a)),
              (o = o),
              "function" == typeof t.onBackgroundMessageHandler
                ? t.onBackgroundMessageHandler(o)
                : t.onBackgroundMessageHandler.next(o));
        }
      }
      async function Pt(e) {
        const t =
          null ===
            (o =
              null === (n = e.notification) || void 0 === n
                ? void 0
                : n.data) || void 0 === o
            ? void 0
            : o[it];
        if (t && !e.action) {
          e.stopImmediatePropagation(), e.notification.close();
          var n = (function (e) {
            var t;
            var n =
              null !==
                (t =
                  null === (t = e.fcmOptions) || void 0 === t
                    ? void 0
                    : t.link) && void 0 !== t
                ? t
                : null === (n = e.notification) || void 0 === n
                ? void 0
                : n.click_action;
            if (n) return n;
            return (function (e) {
              return "object" == typeof e && e && ot in e;
            })(e.data)
              ? self.location.origin
              : null;
          })(t);
          if (n) {
            var i,
              o = new URL(n, self.location.href),
              e = new URL(self.location.origin);
            if (o.host === e.host) {
              let e = await (async function (e) {
                var t = await At();
                for (const i of t) {
                  var n = new URL(i.url, self.location.href);
                  if (e.host === n.host) return i;
                }
                return null;
              })(o);
              if (
                (e
                  ? (e = await e.focus())
                  : ((e = await self.clients.openWindow(n)),
                    (i = 3e3),
                    await new Promise((e) => {
                      setTimeout(e, i);
                    })),
                e)
              )
                return (
                  (t.messageType = E.NOTIFICATION_CLICKED),
                  (t.isFirebaseMessaging = !0),
                  e.postMessage(t)
                );
            }
          }
        }
      }
      function At() {
        return self.clients.matchAll({
          type: "window",
          includeUncontrolled: !0,
        });
      }
      function Et(e) {
        return bt.create("missing-app-config-values", { valueName: e });
      }
      Ot("hts/frbslgigp.ogepscmv/ieo/eaylg", "tp:/ieaeogn-agolai.o/1frlglgc/o"),
        Ot("AzSCbw63g1R0nCw85jG8", "Iaya3yLKwmgvh7cF0q4");
      class Kt {
        constructor(e, t, n) {
          (this.deliveryMetricsExportedToBigQueryEnabled = !1),
            (this.onBackgroundMessageHandler = null),
            (this.onMessageHandler = null),
            (this.logEvents = []),
            (this.isLogServiceStarted = !1);
          var i = (function (e) {
            if (!e || !e.options) throw Et("App Configuration Object");
            if (!e.name) throw Et("App Name");
            var t = e["options"];
            for (const n of [
              "projectId",
              "apiKey",
              "appId",
              "messagingSenderId",
            ])
              if (!t[n]) throw Et(n);
            return {
              appName: e.name,
              projectId: t.projectId,
              apiKey: t.apiKey,
              appId: t.appId,
              senderId: t.messagingSenderId,
            };
          })(e);
          this.firebaseDependencies = {
            app: e,
            appConfig: i,
            installations: t,
            analyticsProvider: n,
          };
        }
        _delete() {
          return Promise.resolve();
        }
      }
      const Mt = (e) => {
        const t = new Kt(
          e.getProvider("app").getImmediate(),
          e.getProvider("installations-internal").getImmediate(),
          e.getProvider("analytics-internal")
        );
        return (
          self.addEventListener("push", (e) => {
            e.waitUntil(Dt(e, t));
          }),
          self.addEventListener("pushsubscriptionchange", (e) => {
            e.waitUntil(
              (async function (e, t) {
                (e = e["newSubscription"])
                  ? ((e = await wt(t.firebaseDependencies)),
                    await _t(t),
                    (t.vapidKey =
                      null !==
                        (e =
                          null ===
                            (e = null == e ? void 0 : e.subscriptionOptions) ||
                          void 0 === e
                            ? void 0
                            : e.vapidKey) && void 0 !== e
                        ? e
                        : tt),
                    await Tt(t))
                  : await _t(t);
              })(e, t)
            );
          }),
          self.addEventListener("notificationclick", (e) => {
            e.waitUntil(Pt(e));
          }),
          t
        );
      };
      function Nt(e, t) {
        return (function (e, t) {
          if (void 0 !== self.document) throw bt.create("only-available-in-sw");
          return (
            (e.onBackgroundMessageHandler = t),
            () => {
              e.onBackgroundMessageHandler = null;
            }
          );
        })((e = r(e)), t);
      }
      qt._registerComponent(new s("messaging-sw", Mt, "PUBLIC"));
      class xt {
        constructor(e, t) {
          (this.app = e),
            (this._delegate = t),
            (this.app = e),
            (this._delegate = t);
        }
        async getToken(e) {
          return (async function (e, t) {
            return Ge((e = r(e)), t);
          })(this._delegate, e);
        }
        async deleteToken() {
          return Xe(this._delegate);
        }
        onMessage(e) {
          return et(this._delegate, e);
        }
        onBackgroundMessage(e) {
          return Nt(this._delegate, e);
        }
      }
      const $t = (e) =>
          self && "ServiceWorkerGlobalScope" in self
            ? new xt(
                e.getProvider("app-compat").getImmediate(),
                e.getProvider("messaging-sw").getImmediate()
              )
            : new xt(
                e.getProvider("app-compat").getImmediate(),
                e.getProvider("messaging").getImmediate()
              ),
        Bt = {
          isSupported: function () {
            return self && "ServiceWorkerGlobalScope" in self
              ? n() &&
                  "PushManager" in self &&
                  "Notification" in self &&
                  ServiceWorkerRegistration.prototype.hasOwnProperty(
                    "showNotification"
                  ) &&
                  PushSubscription.prototype.hasOwnProperty("getKey")
              : "undefined" != typeof window &&
                  n() &&
                  !(
                    "undefined" == typeof navigator || !navigator.cookieEnabled
                  ) &&
                  "serviceWorker" in navigator &&
                  "PushManager" in window &&
                  "Notification" in window &&
                  "fetch" in window &&
                  ServiceWorkerRegistration.prototype.hasOwnProperty(
                    "showNotification"
                  ) &&
                  PushSubscription.prototype.hasOwnProperty("getKey");
          },
        };
      t.default.INTERNAL.registerComponent(
        new s("messaging-compat", $t, "PUBLIC").setServiceProps(Bt)
      ),
        t.default.registerVersion("@firebase/messaging-compat", "0.1.2");
    }.apply(this, arguments);
  } catch (e) {
    throw (
      (console.error(e),
      new Error(
        "Cannot instantiate firebase-messaging-compat.js - be sure to load firebase-app.js first."
      ))
    );
  }
});
//# sourceMappingURL=firebase-messaging-compat.js.map
