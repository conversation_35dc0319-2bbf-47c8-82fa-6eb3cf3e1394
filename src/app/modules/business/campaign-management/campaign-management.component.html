<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formCampaignSearch" (submit)="onSearch()">
        <div class="row">
          <div class="col-md-6 col-lg-3">
            <div class="form-group">
              <label>{{ "model.campaign.name" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="campaignName"
                class="w-100"
                class="form-control"
                placeholder="{{ 'model.campaign.name' | placeholder }}"
              />
            </div>
          </div>
          <div class="col-md-6 col-lg-2">
            <div class="form-group">
              <label>{{ "common.status" | translate }}</label>
              <!-- <select
                formControlName="status"
                class="w-100"
                class="form-control"
                appSelectOption
              >
                <option *ngFor="let item of ENTITY_STATUS" [value]="item.code">
                  {{ item.label | translate }}
                </option>
              </select> -->
              <ng-select
                placeholder="{{ 'common.appSelectOption.status' | translate }}"
                [searchable]="false"
                formControlName="status"
                [clearable]="true"
              >
                <ng-option
                  [value]="item.code"
                  *ngFor="let item of ENTITY_STATUS"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-8 col-lg-4">
            <div class="date-picker-container form-group row">
              <div class="col-md-6">
                <label>{{ "model.campaign.startDateFrom" | translate }}</label>
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="startDateFrom"
                    formControlName="startDateFrom"
                    placeholder="DD/MM/YYYY"
                    (change)="validateDate()"
                    (dateInput)="validateDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="startDateFrom"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #startDateFrom></mat-datepicker>
                </mat-form-field>
              </div>
              <div class="col-md-6">
                <label>{{ "model.campaign.endDateTo" | translate }}</label>
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="startDateTo"
                    formControlName="startDateTo"
                    placeholder="DD/MM/YYYY"
                    min="{{
                      formCampaignSearch.controls['startDateFrom'].value
                        | date : 'yyyy-MM-dd'
                    }}"
                    [max]="MAX_DATE_CONST"
                    (change)="validateDate()"
                    (dateInput)="validateDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="startDateTo"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #startDateTo></mat-datepicker>
                </mat-form-field>
              </div>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
            >
              <div class="btn-reset">
                <i
                  class="bi bi-arrow-clockwise"
                  type="button"
                  (click)="onReset()"
                >
                </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" type="submit">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-3 mt-4">
          <button
            type="button"
            class="btn btn-white"
            *hasPrivileges="SYSTEM_RULES.CAMPAIGN_EXPORT"
            (click)="onExport()"
          >
            {{ "common.action.export" | translate }}
          </button>
          <button
            type="button"
            class="btn btn-red"
            [routerLink]="[ROUTER_UTILS.sysManage.campaign.create]"
            *hasPrivileges="SYSTEM_RULES.CAMPAIGN_CREATE"
          >
            {{ "common.action.create" | translate }}
          </button>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th class="text-center" scope="col">
                {{ "common.no" | translate }}
              </th>
              <th class="text-left" scope="col" [width]="'250px'">
                {{ "model.campaign.name" | translate }}
              </th>
              <th class="text-left" scope="col">
                {{ "model.campaign.position" | translate }}
              </th>
              <th class="text-left" scope="col" [width]="'350px'">
                {{ "model.campaign.description" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "model.campaign.startDateFrom" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "model.campaign.endDateTo" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "model.campaign.status" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of data; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td title="{{ dataItem.campaignName }}">
                {{ dataItem.campaignName | limitWord }}
              </td>
              <td>
                <span
                  *ngIf="dataItem.position === SCREEN_SHOW_BANNER.LOGIN.label"
                >
                  {{ "model.campaign.loginScreen" | translate }}
                </span>
                <span
                  *ngIf="
                    dataItem.position === SCREEN_SHOW_BANNER.HOMEPAGE.label
                  "
                >
                  {{ "model.campaign.homepageScreen" | translate }}
                </span>
                <span
                  *ngIf="dataItem.position === SCREEN_SHOW_BANNER.OTHER.label"
                >
                  {{ "model.campaign.other" | translate }}
                </span>
              </td>
              <td title="{{ dataItem.description }}">
                {{ dataItem.description | limitWord }}
              </td>
              <td class="text-center">{{ dataItem.startDate }}</td>
              <td class="text-center">{{ dataItem.endDate }}</td>
              <td class="text-center">
                <span
                  class="badge"
                  [ngClass]="ENTITY_STATUS_MAP[dataItem.status || 0].style"
                  >{{
                    ENTITY_STATUS_MAP[dataItem.status || 0].label | translate
                  }}</span
                >
              </td>
              <td class="text-center">
                <button
                  ngbTooltip="{{ 'common.action.detail' | translate }}"
                  class="btn px-1 py-0"
                  (click)="detail(dataItem.campaignId)"
                >
                  <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                </button>
                <ng-container *hasPrivileges="SYSTEM_RULES.CAMPAIGN_WRITE">
                  <button
                    *ngIf="
                      dataItem.status === ENTITY_STATUS_CONST.INACTIVE.code
                    "
                    ngbTooltip="{{ 'common.action.update' | translate }}"
                    class="btn px-1 py-0"
                    data-toggle="modal"
                    (click)="update(dataItem?.campaignId)"
                  >
                    <i class="fa fa-edit mb-color" aria-hidden="true"></i>
                  </button>
                </ng-container>
                <ng-container>
                  <button
                    [ngbTooltip]="
                      (dataItem.status === ENTITY_STATUS_CONST.ACTIVE.code
                        ? 'common.action.lock'
                        : 'common.action.unlock'
                      ) | translate
                    "
                    *hasPrivileges="
                      dataItem.status === ENTITY_STATUS_CONST.ACTIVE.code
                        ? SYSTEM_RULES.CAMPAIGN_UNLOCK
                        : SYSTEM_RULES.CAMPAIGN_LOCK
                    "
                    class="btn px-1 py-0"
                    (click)="lockAndUnlock(dataItem)"
                  >
                    <i
                      [className]="
                        dataItem.status === ENTITY_STATUS_CONST.ACTIVE.code
                          ? 'fa fa-lock mb-color'
                          : 'fa fa-unlock mb-color'
                      "
                      aria-hidden="true"
                    ></i>
                  </button>
                </ng-container>
                <ng-container *hasPrivileges="SYSTEM_RULES.CAMPAIGN_DELETE">
                  <button
                    *ngIf="
                      dataItem.status === ENTITY_STATUS_CONST.INACTIVE.code
                    "
                    ngbTooltip="{{ 'common.action.delete' | translate }}"
                    class="btn px-1 py-0"
                    (click)="delete(dataItem)"
                  >
                    <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                  </button>
                </ng-container>
              </td>
            </tr>
          </tbody>
        </table>
        <div
          class="row d-block text-center m-0"
          *ngIf="data?.length === ENTITY_STATUS_CONST.INACTIVE.code"
        >
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
        <div *ngIf="data.length">
          <mat-paginator
            [length]="formCampaignSearch.value.length"
            [pageSize]="formCampaignSearch.value.pageSize"
            [pageSizeOptions]="pageSizeOptions"
            (page)="onChangePage($event)"
            aria-label="Select page"
          >
          </mat-paginator>
        </div>
      </div>
    </div>
  </div>
</section>
