### STAGE 1:BUILD ###
FROM node:14-alpine AS dev

WORKDIR /app

# RUN npm cache clean --force

# RUN npm install -D typescript@4.3.5

COPY . .
RUN npm install
RUN npm run build:qa

### STAGE 2:RUN ###
FROM nginx:latest AS nginx

COPY --from=dev /app/dist/mb-lao /usr/share/nginx/html
COPY /.nginx.conf  /etc/nginx/conf.d/default.conf

# FROM caddy:alpine

# COPY ./Caddyfile /etc/caddy/Caddyfile
# COPY --from=dev /app/dist/mb-lao /usr/share/caddy/html

EXPOSE 80
