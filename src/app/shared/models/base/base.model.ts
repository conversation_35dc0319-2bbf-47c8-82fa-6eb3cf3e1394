import { IPageable } from './pageable.model';

export interface IBaseResponseModel<T> {
  content?: T;
  page?: IPageable;
}

// export class BaseResponse implements IBaseResponse {
//   constructor(
//     public success?: boolean,
//     public code?: number | string,
//     public data?: [] | {},
//     public message?: string,
//     public page?: IPageable,
//     public timestamp?: string | number | any,
//   ) {
//     this.success = success;
//     this.code = code;
//     this.data = data;
//     this.message = message;
//     this.page = page;
//     this.timestamp = timestamp;
//   }
// }
