import { Component, HostListener, Input, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { MODAL_ACTION } from '@shared/constants/app.constants';
import { MODEL_MAP_ITEM_COMMON } from '@shared/models/model.type';

@Component({
  selector: 'app-confirm-modal',
  templateUrl: './confirm-modal.component.html',
  styleUrls: ['./confirm-modal.component.scss'],
})
export class ConfirmModalComponent implements OnInit {
  public title = 'common.action.confirm';
  public content = '';
  public interpolateParams: object = {};
  public isHiddenBtnClose = false;
  public action!: MODEL_MAP_ITEM_COMMON;

  // Thêm các thuộc tính cho nút
  @Input() confirmButtonText: string | undefined;
  @Input() cancelButtonText: string | undefined;
  @Input() buttonOrder: 'confirm-first' | 'cancel-first' | undefined;
  @Input() confirmBtnClass: string | undefined; // Lớp CSS cho nút xác nhận

  hasFilter = false;

  MODAL_ACTION = MODAL_ACTION;

  constructor(public activeModal: NgbActiveModal, public translateService: TranslateService) {}

  ngOnInit(): void {}

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.activeModal.close();
  }

  showContent(): string {
    return this.translateService.instant(this.content, this.interpolateParams);
  }
}
