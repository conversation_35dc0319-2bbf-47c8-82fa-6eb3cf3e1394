<div class="container-fluid">
  <button
    *ngIf="
      ((position === this.SCREEN_SHOW_BANNER.HOMEPAGE.label ||
        position === this.SCREEN_SHOW_BANNER.OTHER.label) &&
        action === ROUTER_ACTIONS.update) ||
      ((position === this.SCREEN_SHOW_BANNER.HOMEPAGE.label ||
        position === this.SCREEN_SHOW_BANNER.OTHER.label) &&
        action === ROUTER_ACTIONS.create)
    "
    class="btn btn-white mr-2 mb-4"
    (click)="addFormImage()"
  >
    {{ "model.campaign.addImage" | translate }}
  </button>
  <form [formGroup]="formImageGroup" class="row">
    <div class="row col-md-12 container-form-image" formArrayName="linkImage">
      <div
        class="col-md-4 mb-4"
        *ngFor="let item of getLinkGroupControl().controls; let i = index"
        [formGroupName]="i"
      >
        <!-- [nzPreview]="handlePreview" -->
        <div class="container-upload">
          <span class="header-image"
            >{{ "model.campaign.titleImage" | translate
            }}<small class="text-danger">*</small></span
          >
          <nz-upload
            nzListType="picture-card"
            [nzIconRender]="iconRender"
            [nzShowUploadList]="false"
            [nzBeforeUpload]="beforeUpload"
            (nzChange)="handleChange($event, i)"
            [nzDisabled]="isEnrtyId"
            [nzCustomRequest]="handleCustomUploadReq"
            (mouseenter)="getItem(i)"
            class="list-img"
          >
            <ng-template #iconRender>
              {{ "common.uploading" | translate }}
            </ng-template>
            <div *ngIf="!item.get('src')?.value">
              <!-- <i class="bi bi-plus"></i> -->
              <img src="assets/dist/img/noImage.png" />
              <div class="no-image-content">
                {{ "common.uploadImage" | translate }}
              </div>
            </div>

            <img
              *ngIf="item.get('src')?.value"
              [src]="item.get('src')?.value"
              [class]="
                position === this.SCREEN_SHOW_BANNER.OTHER.label
                  ? 'brand-image overflow-auto img-9-16'
                  : 'brand-image overflow-auto'
              "
            />
          </nz-upload>
          <div class="icon">
            <i
              *ngIf="item.get('src')?.value"
              class="bi bi-eye"
              (click)="handlePreview($event, i)"
            ></i>
            <ng-container
              *ngIf="
                action === ROUTER_ACTIONS.update ||
                action === ROUTER_ACTIONS.create
              "
            >
              <i
                class="fa fa-times"
                (click)="onRemovePic(getLinkGroupControl().controls[i], i)"
              ></i>
            </ng-container>
          </div>
          <div class="col-md-12 input-container">
            <div class="form-group">
              <label
                >{{ "model.campaign.linkBanner" | translate
                }}<span class="text-danger">*</span></label
              >
              <input
                [readOnly]="action === ROUTER_ACTIONS.detail"
                (blur)="getEmbedLink(i)"
                type="text"
                class="w-100 form-control"
                formControlName="embedLink"
              />
              <small
                class="text-danger"
                *ngIf="
                  item.get('embedLink')?.errors?.required &&
                  item.get('embedLink')?.touched
                "
                >{{ "error.campaign.required.embedLink" | translate }}</small
              >
              <small
                class="text-danger"
                *ngIf="
                  item.get('embedLink')?.errors?.pattern &&
                  item.get('embedLink')?.touched
                "
                >{{ "error.campaign.pattern.embedLink" | translate }}</small
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>
