import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  ENTITY_STATUS_CONST,
  FILE_EXTENSION,
  MODAL_ACTION,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IBaseResponseModel } from '@shared/models/base/base.model';
import { IDotp, ISearchDotp } from '@shared/models/dotp.model';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr.service';
import { DotpService } from '@shared/services/dotp.service';
import CommonUtils from '@shared/utils/common-utils';
import { ROUTER_UTILS } from '@shared/utils/router.utils';

@Component({
  selector: 'dotp-management',
  templateUrl: './dotp-management.component.html',
  styleUrls: ['./dotp-management.component.scss'],
})
export class DotpManagementComponent implements OnInit {
  searchForm: FormGroup = new FormGroup({});
  searchResults: IBaseResponseModel<IDotp[]> = {};
  data: IDotp[] = [];
  
  // Constants
  PAGINATION = PAGINATION;
  SYSTEM_RULES = SYSTEM_RULES;
  VALIDATORS = VALIDATORS;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  MODAL_ACTION = MODAL_ACTION;
  
  // Status options
  statusOptions = [
    { value: null, label: 'common.all' },
    { value: ENTITY_STATUS_CONST.ACTIVE.code, label: 'common.status.active' },
    { value: ENTITY_STATUS_CONST.INACTIVE.code, label: 'common.status.inactive' },
  ];

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private dotpService: DotpService,
    private translateService: TranslateService,
    private downloadService: DownloadService,
    private toastrService: ToastrCustomService,
    private modalService: NgbModal
  ) {
    this.initForm();
  }

  ngOnInit(): void {
    this.onSearch();
  }

  private initForm(): void {
    this.searchForm = this.fb.group({
      keyword: [''],
      status: [null],
      pageIndex: [PAGINATION.PAGE_NUMBER_DEFAULT],
      pageSize: [PAGINATION.PAGE_SIZE_DEFAULT],
      length: [0],
      hasPageable: [true],
    });
  }

  onSearch(): void {
    if (this.searchForm.valid) {
      const searchData: ISearchDotp = {
        ...this.searchForm.value,
      };

      this.dotpService.search(searchData).subscribe((res: any) => {
        if (res?.body) {
          this.data = res.body.content || [];
          this.searchForm.controls.length.setValue(res.body.totalElements || 0);
          this.searchResults = res.body;
        }
      });
    }
  }

  onReset(): void {
    this.searchForm.controls.keyword.reset();
    this.searchForm.controls.status.reset();
    this.onSearch();
  }

  onPageChange(event: PageEvent): void {
    this.searchForm.controls.pageIndex.setValue(event.pageIndex);
    this.searchForm.controls.pageSize.setValue(event.pageSize);
    this.onSearch();
  }

  onExport(): void {
    const searchData: ISearchDotp = {
      ...this.searchForm.value,
      hasPageable: false,
    };

    this.dotpService.export(searchData).subscribe((res: any) => {
      if (res?.body) {
        this.downloadService.downloadFileFromBlob(
          res.body,
          `DOTP_Management_${CommonUtils.getCurrentDateTimeString()}.${FILE_EXTENSION.EXCEL}`
        );
        this.toastrService.success('common.export.success');
      }
    });
  }

  onDelete(item: IDotp): void {
    if (item.status === ENTITY_STATUS_CONST.ACTIVE.code) {
      // Show confirmation modal
      const modalRef = this.modalService.open(/* ConfirmDeleteModal */, {
        size: 'md',
        keyboard: true,
        centered: true,
        backdrop: 'static',
      });
      
      modalRef.componentInstance.message = this.translateService.instant(
        'dotp.confirm.delete',
        { customerName: item.customerName }
      );
      
      modalRef.result.then((result) => {
        if (result === MODAL_ACTION.CONFIRM.code) {
          this.dotpService.delete({ dotpId: item.dotpId }).subscribe(() => {
            this.toastrService.success('dotp.delete.success');
            this.onSearch();
          });
        }
      });
    }
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.searchForm.value.pageIndex,
      this.searchForm.value.pageSize
    );
  }

  getStatusLabel(status: number): string {
    return status === ENTITY_STATUS_CONST.ACTIVE.code
      ? 'common.status.active'
      : 'common.status.inactive';
  }

  getStatusClass(status: number): string {
    return status === ENTITY_STATUS_CONST.ACTIVE.code
      ? 'badge-success'
      : 'badge-danger';
  }

  canDelete(item: IDotp): boolean {
    return item.status === ENTITY_STATUS_CONST.ACTIVE.code;
  }
}
