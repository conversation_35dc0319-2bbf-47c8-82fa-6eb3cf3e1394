<div class="modal-content">
  <div class="modal-header">
    <h5 class="modal-title">
      {{
        "model.report.transaction.configAutoReportLapNetUmoney.titleReport"
          | translate
      }}
    </h5>
    <button
      type="button"
      class="close"
      data-dismiss="modal"
      aria-label="Close"
      (click)="activeModal.close()"
    >
      <span aria-hidden="true"><i class="bi bi-x"></i></span>
    </button>
  </div>
  <div class="modal-body">
    <form
      [formGroup]="formCreate"
      *ngIf="formCreate"
      (keydown.enter)="$event.preventDefault()"
    >
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-12">
            <div class="form-group">
              <label>
                {{
                  "model.report.transaction.configAutoReport.title" | translate
                }}<span class="text-danger small-text"> *</span></label
              >
              <input
                trim
                placeholder="{{
                  'model.report.transaction.configAutoReport.title' | translate
                }}"
                type="text"
                class="form-control w-100"
                formControlName="title"
                [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
              />
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formCreate.get('title')?.errors?.required &&
                  formCreate.get('title')?.touched
                "
              >
                {{
                  "model.report.transaction.configAutoReport.error.required.title"
                    | translate
                }}
              </small>
            </div>
            <div class="form-group">
              <label
                >{{
                  "model.report.transaction.configAutoReport.content"
                    | translate
                }}<span class="text-danger small-text"> *</span></label
              >
              <textarea
                class="w-100 form-control"
                rows="5"
                formControlName="content"
                placeholder="{{
                  'model.report.transaction.configAutoReport.content'
                    | translate
                }}"
                [maxLength]="VALIDATORS.LENGTH.MEDIUM_TEXT_MAX_LENGTH"
              ></textarea>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formCreate.get('content')?.errors?.required &&
                  formCreate.get('content')?.touched
                "
              >
                {{
                  "model.report.transaction.configAutoReport.error.required.content"
                    | translate
                }}
              </small>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label
                    >{{
                      "model.report.transaction.configAutoReport.time"
                        | translate
                    }}<span class="text-danger small-text"> *</span></label
                  >
                  <nz-time-picker
                    class="form-control w-100"
                    nzPlaceHolder="{{
                      'model.report.transaction.configAutoReport.placeholder.time'
                        | translate
                    }}"
                    formControlName="sendTime"
                    [nzFormat]="'HH:mm'"
                  ></nz-time-picker>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formCreate.get('sendTime')?.errors?.required &&
                      formCreate.get('sendTime')?.touched
                    "
                  >
                    {{
                      "model.report.transaction.configAutoReport.error.required.time"
                        | translate
                    }}
                  </small>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label
                    >{{
                      "model.report.transaction.configAutoReport.status"
                        | translate
                    }}<span class="text-danger small-text"> *</span></label
                  >
                  <ng-select
                    appearance="outline"
                    [searchable]="false"
                    placeholder="{{
                      'model.report.transaction.configAutoReport.status'
                        | placeholder : 'select'
                    }}"
                    [clearable]="true"
                    formControlName="status"
                  >
                    <ng-option
                      [value]="item.code"
                      *ngFor="let item of ENTITY_STATUS"
                    >
                      {{ item.label | translate }}
                    </ng-option>
                  </ng-select>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formCreate.get('status')?.errors?.required &&
                      formCreate.get('status')?.touched
                    "
                  >
                    {{
                      "model.report.transaction.configAutoReport.error.required.status"
                        | translate
                    }}
                  </small>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <label
              >{{
                "model.report.transaction.configAutoReport.titleMail"
                  | translate
              }}<span class="text-danger small-text"> *</span></label
            >

            <div
              [ngClass]="
                emails.controls.length <=
                CONFIG_AUTO_REPORT_CONST.MAIL_SCROLL_LENGTH
                  ? ''
                  : 'scroll-list-mail'
              "
            >
              <div
                formArrayName="emails"
                class="form-group"
                *ngFor="let group of emails.controls; index as i"
              >
                <div class="row" [formGroupName]="i">
                  <div class="col-md-6">
                    <input
                      placeholder="{{ 'common.email' | translate }}"
                      type="text"
                      class="form-control w-100"
                      formControlName="email"
                      [maxLength]="VALIDATORS.LENGTH.EMAIL_MAX_LENGTH"
                    />
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="
                        group.get('email')?.errors?.required &&
                        group.get('email')?.touched
                      "
                    >
                      {{
                        "model.report.transaction.configAutoReport.error.required.mail"
                          | translate
                      }}
                    </small>
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="
                        group.get('email')?.errors?.pattern &&
                        group.get('email')?.touched
                      "
                    >
                      {{ "error.customerCreate.pattern.email" | translate }}
                    </small>
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="group.get('email')?.errors?.duplicate"
                    >
                      {{
                        "model.report.transaction.configAutoReport.error.pattern.emailDuplicate"
                          | translate
                      }}
                    </small>
                  </div>
                  <div class="col-md-2">
                    <button
                      ngbTooltip="{{ 'common.action.delete' | translate }}"
                      class="btn px-1 py-0 mt-1"
                      (click)="onDeleteEmail(i)"
                      *ngIf="isShowDeleteMail[i]"
                    >
                      <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div class="">
              <button
                class="btn px-1 py-0 mt-1 add-mail"
                (click)="addEmail()"
                *ngIf="
                  emails.controls.length <
                  CONFIG_AUTO_REPORT_CONST.MAIL_MAX_LENGTH
                "
              >
                <i class="fa fa-plus-circle" aria-hidden="true"></i
                >{{
                  "model.report.transaction.configAutoReport.addMail"
                    | translate
                }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="text-center pb-4">
    <button
      type="button"
      class="btn mb-btn-outline-color mr-3"
      (click)="activeModal.close()"
    >
      {{ "common.action.close" | translate }}
    </button>
    <ng-container
      *hasPrivileges="
        isDataAvailable
          ? SYSTEM_RULES.DEBIT_DEPOSIT_AUTO_CONFIG_UPDATE
          : SYSTEM_RULES.DEBIT_DEPOSIT_AUTO_CONFIG_CREATE
      "
    >
      <button type="button" class="btn mb-btn-color" (click)="sendMail()">
        {{
          (isDataAvailable ? "common.action.update" : "common.action.create")
            | translate
        }}
      </button>
    </ng-container>
  </div>
</div>
