import { Directive, Input, TemplateRef, ViewContainerRef } from '@angular/core';
import { AuthenticationService } from '@shared/services/auth/authentication.service';

/**
 * Check roles
 *
 * <AUTHOR>
 * @date 2021-12-24
 * @export HasRolesDirective
 * @class HasRolesDirective
 * @howToUse
 * ```
 *     <some-element *hasRoles="'ROLE_ADMIN'">...</some-element>
 *
 *     <some-element *hasRoles="['ROLE_ADMIN', 'ROLE_USER']">...</some-element>
 * ```
 */
@Directive({
  selector: '[hasRoles]',
})
export class HasRolesDirective {
  private authorities: string | string[] = [];

  constructor(
    private authenticationService: AuthenticationService,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    private templateRef: TemplateRef<any>,
    public viewContainerRef: ViewContainerRef,
  ) {}

  @Input() set hasRoles(value: string | string[]) {
    this.authorities = value;
    this.updateView();
    // Get notified each time authentication state changes.
    this.authenticationService
      .getAuthenticationState()
      .subscribe(() => this.updateView());
  }

  private updateView(): void {
    const hasAnyAuthority = this.authenticationService.hasAnyPrivileges(
      this.authorities,
    );
    this.viewContainerRef.clear();
    if (hasAnyAuthority) {
      this.viewContainerRef.createEmbeddedView(this.templateRef);
    }
  }
}
