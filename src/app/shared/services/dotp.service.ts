import { Injectable } from '@angular/core';
import { IDotp, ISearchDotp } from '@shared/models/dotp.model';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';

@Injectable({ providedIn: 'root' })
export class DotpService extends AbstractService {
  resource: RESOURCE_TYPE = RESOURCE_CONST.DOTP;

  search(request: ISearchDotp): Observable<EntityResponseType<IDotp>> {
    return super.post<EntityResponseType<IDotp>>(
      RESOURCE_CONST.DOTP.API.SEARCH,
      request
    );
  }

  cancel(request: any): Observable<EntityResponseType<any>> {
    return super.post<EntityResponseType<any>>(
      RESOURCE_CONST.DOTP.API.DELETE,
      request
    );
  }

  export(request: ISearchDotp): Observable<EntityResponseType<IDotp>> {
    return super.postFile(RESOURCE_CONST.DOTP.API.EXPORT, request);
  }
}
