// if you'd like to use it without importing all of bootstrap it requires
@import "../../node_modules/bootstrap/scss/functions";
@import "../../node_modules/bootstrap/scss/variables";
@import "../../node_modules/bootstrap/scss/mixins";
// regular style toast
@import '~ngx-toastr/toastr';
// bootstrap style toast
// or import a bootstrap 4 alert styled design (SASS ONLY)
// should be after your bootstrap imports, it uses bs4 variables, mixins, functions
// @import '~ngx-toastr/toastr-bs4-alert';

@layer base {
  @import '01-base/font.scss';
  @import '01-base/document.scss';
  @import '01-base/headings.scss';
}

@layer components {
  @import '02-components/link.scss';
}

@layer utilities {
}
