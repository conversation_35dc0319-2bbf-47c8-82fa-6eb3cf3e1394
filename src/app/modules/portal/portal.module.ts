import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { SharedModule } from '@shared/shared.module';
import { ActiveUserComponent } from './active-user/active-user.component';
import { ForgotPasswordSetUpComponent } from './forgot-password/forgot-password-set-up/forgot-password-set-up.component';
import { ForgotPasswordVerifyComponent } from './forgot-password/forgot-password-verify/forgot-password-verify.component';
import { ForgotPasswordComponent } from './forgot-password/forgot-password.component';
import { HomeComponent } from './home/<USER>';
import { PortalRoutingModule } from './portal-routing.module';

@NgModule({
  declarations: [
    HomeComponent,
    ActiveUserComponent,
    ForgotPasswordComponent,
    ForgotPasswordVerifyComponent,
    ForgotPasswordSetUpComponent,
  ],
  imports: [CommonModule, SharedModule, PortalRoutingModule],
})
export class PortalModule {}
