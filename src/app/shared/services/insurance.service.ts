import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  IHealthPackage,
  IInsurance,
  IVehiclePackage,
  IVehicleType,
} from '@shared/models/insurance.model';
import { IInsuranceSearch } from '@shared/models/request/insurance.search';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class InsuranceService extends AbstractService {
  public resourceURLGet = '/insurance/lvi';
  public resourceURLSearch = '/transaction';

  constructor(protected http: HttpClient) {
    super(http);
  }

  getInsuranceType(): Observable<EntityResponseType<any>> {
    return super.get<EntityResponseType<any>>(`${this.resourceURLGet}/type`);
  }

  getVehiclePackage(): Observable<EntityResponseType<IVehiclePackage>> {
    return super.get<EntityResponseType<IVehiclePackage>>(
      `${this.resourceURLGet}/vehicle/package`
    );
  }

  getHealthPackage(): Observable<EntityResponseType<IHealthPackage>> {
    return super.get<EntityResponseType<IHealthPackage>>(
      `${this.resourceURLGet}/health/package`
    );
  }

  getVehicleType(): Observable<EntityResponseType<IVehicleType>> {
    return super.get<EntityResponseType<IVehicleType>>(
      `${this.resourceURLGet}/vehicle/type`
    );
  }

  searchInsurance(
    request: IInsuranceSearch
  ): Observable<EntityResponseType<IInsurance>> {
    return super.post<EntityResponseType<IInsurance>>(
      `${this.resourceURLSearch}/search/insurance`,
      request
    );
  }

  exportInsurance(
    request: IInsuranceSearch
  ): Observable<EntityResponseType<IInsurance>> {
    return super.postFile(
      `${this.resourceURLSearch}/export/insurance`,
      request
    );
  }
}
