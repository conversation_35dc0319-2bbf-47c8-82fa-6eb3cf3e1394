<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="backToList()" />
      <h5>{{ "model.campaign.create" | translate }}</h5>
    </div>
    <!-- <form [formGroup]="form" *ngIf="customer?.customerId"></form> -->
    <form [formGroup]="formCampaign" class="mt-4">
      <div class="col-md-12">
        <div class="row border-create">
          <h3>{{ "model.campaign.campaignInfoTitle" | translate }}</h3>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label
                    >{{ "model.campaign.name" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    type="text"
                    class="form-control w-100"
                    trim
                    placeholder="{{ 'model.campaign.name' | placeholder }}"
                    [maxLength]="VALIDATORS.LENGTH.CAMPAIGN_NAME_MAX_LENGTH"
                    formControlName="campaignName"
                  />
                  <small
                    class="text-danger"
                    *ngIf="
                      formCampaign.get('campaignName')?.errors?.required &&
                      formCampaign.get('campaignName')?.touched
                    "
                  >
                    {{
                      "error.campaign.required.campaignName" | translate
                    }}</small
                  >
                  <small
                    class="text-danger"
                    *ngIf="
                      formCampaign.get('campaignName')?.errors?.pattern &&
                      formCampaign.get('campaignName')?.touched
                    "
                  >
                    {{
                      "error.campaign.pattern.campaignName" | translate
                    }}</small
                  >
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label
                    >{{ "model.campaign.startDateFrom" | translate }}
                    <span class="text-danger">*</span></label
                  >
                  <mat-form-field appearance="fill" class="date-picker">
                    <input
                      matInput
                      (dateInput)="validateTimeStart($event)"
                      [matDatepicker]="picker"
                      formControlName="startDate"
                      [placeholder]="DATE_CONSTANT.DDMMYYYY_SLASH_BIG"
                      dateTransform
                      [min]="maxDate"
                      max="{{
                        formCampaign.controls['endDate'].value
                          | date : DATE_CONSTANT.YYYYMMDD_HYPHEN_SMALL
                      }}"
                    />
                    <mat-datepicker-toggle
                      matSuffix
                      [for]="picker"
                    ></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                  </mat-form-field>
                  <small
                    class="text-danger"
                    *ngIf="
                      formCampaign.get('startDate')?.errors?.required &&
                      formCampaign.get('startDate')?.touched
                    "
                  >
                    {{
                      "error.campaign.required.startDateFrom" | translate
                    }}</small
                  >
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label
                    >{{ "model.campaign.endDateTo" | translate }}
                    <span class="text-danger">*</span></label
                  >
                  <mat-form-field appearance="fill" class="date-picker">
                    <input
                      matInput
                      [matDatepicker]="pickerEndDate"
                      formControlName="endDate"
                      [placeholder]="DATE_CONSTANT.DDMMYYYY_SLASH_BIG"
                      dateTransform
                      [min]="maxDate"
                      min="{{
                        startDateMin
                          | date : DATE_CONSTANT.YYYYMMDD_HYPHEN_SMALL
                      }}"
                    />
                    <mat-datepicker-toggle
                      matSuffix
                      [for]="pickerEndDate"
                    ></mat-datepicker-toggle>
                    <mat-datepicker #pickerEndDate></mat-datepicker>
                  </mat-form-field>
                  <small
                    class="text-danger"
                    *ngIf="
                      formCampaign.get('endDate')?.errors?.required &&
                      formCampaign.get('endDate')?.touched
                    "
                    >{{
                      "error.campaign.required.endDateTo" | translate
                    }}</small
                  >
                </div>
              </div>
            </div>

            <label class="label-position"
              >{{ "model.campaign.showScreen" | translate }}
              <span class="text-danger">*</span></label
            >

            <div class="row">
              <div class="col-md-6 row">
                <div class="col-md-4">
                  <div class="form-check">
                    <input
                      (change)="getPlacesShowCampaign($event)"
                      class="form-check-input"
                      type="radio"
                      name="position"
                      [id]="SCREEN_SHOW_BANNER.LOGIN.label"
                      [value]="SCREEN_SHOW_BANNER.LOGIN.label"
                    />
                    <label
                      class="form-check-label"
                      [for]="SCREEN_SHOW_BANNER.LOGIN.label"
                    >
                      {{ "model.campaign.loginScreen" | translate }}
                    </label>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-check">
                    <input
                      (change)="getPlacesShowCampaign($event)"
                      class="form-check-input"
                      type="radio"
                      name="position"
                      [id]="SCREEN_SHOW_BANNER.HOMEPAGE.label"
                      [value]="SCREEN_SHOW_BANNER.HOMEPAGE.label"
                    />
                    <label
                      class="form-check-label"
                      [for]="SCREEN_SHOW_BANNER.HOMEPAGE.label"
                    >
                      {{ "model.campaign.homepageScreen" | translate }}
                    </label>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-check">
                    <input
                      (change)="getPlacesShowCampaign($event)"
                      class="form-check-input"
                      type="radio"
                      name="position"
                      [id]="SCREEN_SHOW_BANNER.OTHER.label"
                      [value]="SCREEN_SHOW_BANNER.OTHER.label"
                    />
                    <label
                      class="form-check-label"
                      [for]="SCREEN_SHOW_BANNER.OTHER.label"
                    >
                      {{ "model.campaign.other" | translate }}
                    </label>
                  </div>
                </div>
              </div>
            </div>
            <small
              class="text-danger d-block"
              *ngIf="
                formCampaign.get('position')?.errors?.required &&
                formCampaign.get('position')?.touched
              "
              >{{ "error.campaign.required.position" | translate }}</small
            >

            <div class="row mt-3">
              <div class="col-md-12">
                <label>{{ "model.campaign.description" | translate }}</label>
                <textarea
                  class="w-100 form-control"
                  rows="6"
                  formControlName="description"
                  placeholder="{{ 'model.campaign.description' | placeholder }}"
                  [maxLength]="
                    VALIDATORS.LENGTH.CAMPAIGN_DESCRIPTION_MAX_LENGTH
                  "
                ></textarea>
              </div>
            </div>
          </div>
          <div class="col-md-12 mt-3" *ngIf="this.position">
            <span class="title-banner">{{
              "model.campaign.banners" | translate
            }}</span
            ><br />
            <ng-container
              *ngIf="this.position === SCREEN_SHOW_BANNER.LOGIN.label"
            >
              <span>{{
                "model.campaign.textBannersLogin" | translate
              }}</span></ng-container
            >
            <ng-container
              *ngIf="this.position === SCREEN_SHOW_BANNER.HOMEPAGE.label"
            >
              <span>{{
                "model.campaign.textBannersHomepage" | translate
              }}</span></ng-container
            >
            <ng-container
              *ngIf="this.position === SCREEN_SHOW_BANNER.OTHER.label"
            >
              <span>{{
                "model.campaign.textBannersOther" | translate
              }}</span></ng-container
            >
            <div class="row">
              <div class="col-md-12">
                <app-upload-multi-images
                  [fileExtension]="FILE_UPLOAD_EXTENSIONS.BANNER"
                  (imageUploaded)="onUploadPics($event)"
                  (imageRemoved)="onRemovePics($event)"
                  (addImage)="onAddImage($event)"
                  (addImage)="onAddImage($event)"
                  [position]="position"
                  [action]="action"
                ></app-upload-multi-images>
              </div>
            </div>
          </div>
        </div>
        <div class="d-block text-center mb-5 mt-4">
          <button class="btn btn-white mr-2" (click)="backToList()">
            {{ "common.action.back" | translate }}
          </button>

          <button
            class="btn btn-red"
            (click)="onSave()"
            *hasPrivileges="SYSTEM_RULES.CAMPAIGN_CREATE"
          >
            {{ "common.action.create" | translate }}
          </button>
        </div>
      </div>
    </form>
  </div>
</section>
