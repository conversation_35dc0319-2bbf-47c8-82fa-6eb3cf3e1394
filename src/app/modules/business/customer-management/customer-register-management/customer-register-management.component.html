<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="form" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-12">
            <div class="row">
              <div class="form-group col-md-4 col-lg-3">
                <label>{{
                  "customerRegisterManagement.cif" | translate
                  }}</label>
                <input trim type="text" placeholder="{{
                    'customerRegisterManagement.placeholder.cifNumber'
                      | translate
                  }}" formControlName="cif" class="w-100" class="form-control" />
                <small class="form-text text-danger noti-small" *ngIf="
                    form.get('cif')?.errors?.pattern && form.get('cif')?.touched
                  ">
                  {{ "error.cif" | translate }}
                </small>
              </div>
              <div class="form-group col-md-4 col-lg-3">
                <label>{{
                  "customerRegisterManagement.idCardNumber" | translate
                  }}</label>
                <input trim type="text" [maxLength]="VALIDATORS.LENGTH.IDENTIFICATION_MAX_LENGTH" placeholder="{{
                    'customerRegisterManagement.placeholder.idCardNumber'
                      | translate
                  }}" formControlName="idCardNumber" class="w-100" class="form-control" />
                <small class="form-text text-danger noti-small" *ngIf="
                    form.get('idCardNumber')?.errors?.pattern &&
                    form.get('idCardNumber')?.touched
                  ">
                  {{ "error.idCardNumber" | translate }}
                </small>
              </div>
              <div class="form-group col-md-4 col-lg-3">
                <label>{{ "common.activeStatus" | translate }}</label>
                <ng-select placeholder="{{
                    'common.activeStatus' | placeholder : 'select'
                  }}" [searchable]="false" formControlName="activeStatus" [clearable]="true">
                  <ng-option [value]="item.value" *ngFor="let item of CUSTOMER_ACTIVE_STATUS">
                    {{ item.label | translate }}
                  </ng-option>
                </ng-select>
              </div>
              <div class="form-group col-md-4 col-lg-3">
                <label>{{
                  "customerRegisterManagement.status" | translate
                  }}</label>
                <ng-select placeholder="{{
                    'common.appSelectOption.status' | translate
                  }}" [searchable]="false" formControlName="status" [clearable]="true">
                  <ng-option [value]="item.value" *ngFor="let item of CUSTOMER_STATUS">
                    {{ item.label | translate }}
                  </ng-option>
                </ng-select>
              </div>
              <div class="form-group col-md-4 col-lg-3">
                <label>{{
                  "customerRegisterManagement.phoneNumberSearch" | translate
                  }}</label>
                <input numbersOnly type="text" [maxLength]="VALIDATORS.LENGTH.SMALL_TEXT_MAX_LENGTH" placeholder="{{
                    'customerRegisterManagement.placeholder.phoneNumberOtp'
                      | translate
                  }}" formControlName="phoneNumber" class="w-100" class="form-control" />
                <small class="form-text text-danger noti-small" *ngIf="
                    form.get('phoneNumber')?.errors?.pattern &&
                    form.get('phoneNumber')?.touched
                  ">
                  {{ "error.phoneNumber" | translate }}
                </small>
              </div>
              <div class="form-group col-md-4 col-lg-3">
                <label>{{
                  "customerRegisterManagement.email" | translate
                  }}</label>
                <input trim type="text" placeholder="{{
                    'customerRegisterManagement.placeholder.email' | translate
                  }}" formControlName="email" class="w-100" class="form-control" />
              </div>
              <div class="form-group col-lg-6 col-md-12">
                <div class="datep-picker-container row">
                  <div class="col-md-6">
                    <label>{{
                      "customerRegisterManagement.fromDate" | translate
                      }}</label>
                    <!-- <input
                      placeholder="dd-mm-yyyy"
                      max="{{ maxDate | date: 'yyyy-MM-dd' }}"
                      type="date"
                      formControlName="createdDateStartAt"
                      class="w-100"
                      class="form-control"
                    /> -->
                    <mat-form-field appearance="fill" class="date-picker">
                      <input matInput [matDatepicker]="createdDateStartAt" formControlName="createdDateStartAt"
                        placeholder="DD/MM/YYYY" [max]="maxDate" dateTransform />
                      <mat-datepicker-toggle matSuffix [for]="createdDateStartAt"></mat-datepicker-toggle>
                      <mat-datepicker #createdDateStartAt></mat-datepicker>
                    </mat-form-field>
                    <small class="form-text text-danger noti-small" *ngIf="
                        form.get('createdDateStartAt')?.errors?.required &&
                        form.get('createdDateStartAt')?.touched
                      ">
                      {{ "error.required.fromDateExcel" | translate }}
                    </small>
                    <small class="form-text text-danger noti-small" *ngIf="
                        form.get('createdDateStartAt')?.errors?.invalidDate &&
                        form.get('createdDateStartAt')?.touched
                      ">
                      {{ "error.required.inValidDate" | translate }}
                    </small>
                    <small class="form-text text-danger noti-small" *ngIf="
                        form.get('createdDateStartAt')?.errors
                          ?.invalidMaxDate &&
                        form.get('createdDateStartAt')?.touched
                      ">
                      {{
                      "error.maxDateCurrent"
                      | translate
                      : {
                      param: "common.action.fromDate" | translate
                      }
                      }}
                    </small>
                  </div>
                  <div class="col-md-6">
                    <label>{{
                      "customerRegisterManagement.toDate" | translate
                      }}</label>
                    <!-- <input
                      max="{{ maxToDate | date: 'yyyy-MM-dd' }}"
                      min="{{
                        form.controls['createdDateStartAt'].value | date: 'yyyy-MM-dd'
                      }}"
                      (change)="validateDate()"
                      type="date"
                      formControlName="createdDateEndAt"
                      class="w-100"
                      class="form-control"
                    /> -->
                    <mat-form-field appearance="fill" class="date-picker">
                      <input matInput [matDatepicker]="createdDateEndAt" formControlName="createdDateEndAt"
                        placeholder="DD/MM/YYYY" [max]="maxToDate" min="{{
                          form.controls['createdDateStartAt'].value
                            | date : 'yyyy-MM-dd'
                        }}" dateTransform />
                      <mat-datepicker-toggle matSuffix [for]="createdDateEndAt"></mat-datepicker-toggle>
                      <mat-datepicker #createdDateEndAt></mat-datepicker>
                    </mat-form-field>
                    <small class="form-text text-danger noti-small" *ngIf="
                        form.get('createdDateEndAt')?.errors?.required &&
                        form.get('createdDateEndAt')?.touched
                      ">
                      {{ "error.required.toDateExcel" | translate }}
                    </small>
                    <small class="form-text text-danger noti-small" *ngIf="
                        form.get('createdDateEndAt')?.errors?.invalidMaxDate &&
                        form.get('createdDateEndAt')?.touched
                      ">
                      {{
                      "error.maxDateCurrent"
                      | translate
                      : {
                      param: "common.action.toDate" | translate
                      }
                      }}
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-12">
            <div class="row">
              <div class="form-group col-md-6 col-lg-3">
                <label ngbTooltip="{{
                    'model.customer.customerSectorId' | translate
                  }}">{{
                  "model.customer.customerSectorId"
                  | translate
                  | limitWord : 26
                  }}</label>
                <ng-select placeholder="{{
                    'model.customer.customerSectorId' | placeholder : 'select'
                  }}" [title]="
                    form.get('customerSectorId')?.value
                      ? (CUSTOMER_SECTOR_MAP[
                          form.get('customerSectorId')?.value
                        ].label | translate)
                      : ''
                  " [searchable]="false" formControlName="customerSectorId" [clearable]="true">
                  <ng-option *ngFor="let item of CUSTOMER_SECTOR" [value]="item.value">
                    <div [title]="item.label | translate">
                      {{ item.label | translate | limitWord : 40 }}
                    </div>
                  </ng-option>
                </ng-select>
              </div>
              <div class="form-group col-md-6 col-lg-3">
                <label ngbTooltip="{{
                    'model.customer.customerType.customer' | translate
                  }}">{{
                  "model.customer.customerType.customer"
                  | translate
                  | limitWord : 26
                  }}</label>
                <ng-select placeholder="{{
                    'model.customer.customerType.customer'
                      | placeholder : 'select'
                  }}" [searchable]="false" formControlName="accountType" [clearable]="true">
                  <ng-option *ngFor="let item of CUSTOMER_TYPE" [value]="item.value">
                    <div [title]="item.label | translate">
                      {{ item.label | translate | limitWord : 40 }}
                    </div>
                  </ng-option>
                </ng-select>
              </div>
              <div class="col-md-2 col-lg-2 mt-4 d-flex">
                <div class="col-btn-reset" ngbTooltip="{{ 'common.action.reset' | translate }}">
                  <div class="btn-reset">
                    <i class="bi bi-arrow-clockwise" type="button" (click)="onReset()">
                    </i>
                  </div>
                </div>
                <div class="">
                  <button class="btn btn-search mr-2" type="submit">
                    {{ "common.action.search" | translate }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-2 mt-4">
          <button class="btn btn-white mb-2" type="button" (click)="exportFile()"
            *hasPrivileges="SYSTEM_RULES.CUSTOMER_EXPORT">
            {{ "common.action.export" | translate }}
          </button>
          <button class="btn btn-red mb-2" type="button" (click)="onCreate()"
            [routerLink]="[ROUTER_UTILS.customer.registration.register]" [routerLinkActive]="['active']"
            *hasPrivileges="SYSTEM_RULES.CUSTOMER_CREATE">
            {{ "common.action.create" | translate }}
          </button>
          <button class="btn btn-red mb-2" type="button" (click)="syncCustomerInfo()"
            *hasPrivileges="SYSTEM_RULES.CUSTOMER_SYNC">
            {{ "common.action.sync" | translate }}
          </button>
          <button class="btn btn-red mb-2" type="button" (click)="updateSectors()"
            *hasPrivileges="SYSTEM_RULES.CUSTOMER_UPDATE_SECTOR">
            {{ "common.action.updateSector" | translate }}
          </button>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table table-customer">
          <thead>
            <tr>
              <th *hasPrivileges="[
                  SYSTEM_RULES.CUSTOMER_UPDATE_SECTOR,
                  SYSTEM_RULES.CUSTOMER_SYNC
                ]" class="text-center" [nzChecked]="checked" [className]="onShowButtonAndCol ? '' : 'hide'"
                [nzIndeterminate]="indeterminate" (nzCheckedChange)="onAllChecked($event)"></th>
              <th class="text-center" scope="col">
                {{ "customerRegisterManagement.no" | translate }}
              </th>
              <th scope="col">
                {{ "customerRegisterManagement.cifNumber" | translate }}
              </th>
              <th scope="col">
                {{ "customerRegisterManagement.fullname" | translate }}
              </th>
              <th scope="col">
                {{ "customerRegisterManagement.dateOfBirth" | translate }}
              </th>
              <th scope="col">
                {{ "model.customer.gender" | translate }}
              </th>
              <th scope="col">
                {{ "customerRegisterManagement.phoneNumber" | translate }}
              </th>
              <th scope="col" [width]="'200px'" class="text-left">
                {{ "customerRegisterManagement.email" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "customerRegisterManagement.sector" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "model.customer.customerType.customer" | translate }}
              </th>
              <th scope="col">
                {{ "model.referral.referralCodeUser" | translate }}
              </th>
              <th scope="col">
                {{ "model.referral.referrerName" | translate }}
              </th>
              <th scope="col">
                {{ "model.referral.referrerPhoneNumber" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.activeStatus" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "customerRegisterManagement.status" | translate }}
              </th>
              <th class="text-center freezer" scope="col">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of data; let i = index">
              <ng-container *hasPrivileges="[
                  SYSTEM_RULES.CUSTOMER_UPDATE_SECTOR,
                  SYSTEM_RULES.CUSTOMER_SYNC
                ]">
                <td *ngIf="
                    (dataItem?.approvalStatus !==
                      APPROVAL_STATUS_CONST.PENDING.value &&
                      dataItem?.status ===
                        CUSTOMER_STATUS_CONST.ACTIVE.value) ||
                      (dataItem?.approvalStatus !==
                        APPROVAL_STATUS_CONST.PENDING.value &&
                        dataItem?.status ===
                          CUSTOMER_STATUS_CONST.INACTIVE.value);
                    else emptyCell
                  " [nzChecked]="setOfCheckedId.has(dataItem?.cif)"
                  (nzCheckedChange)="onItemChecked(dataItem?.cif, $event)"></td>
                <ng-template #emptyCell>
                  <td></td>
                </ng-template>
              </ng-container>
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td>{{ dataItem.cif }}</td>
              <td>
                <span class="href-color" title="{{ dataItem.fullname }}"
                  (click)="openGetAllAccountNumberModal(dataItem.customerId)">{{ dataItem.fullname | limitWord }}</span>
              </td>
              <td>{{ dataItem.dateOfBirth | appDate }}</td>
              <td class="text-left">
                {{ GENDER_MAP[dataItem.gender || 0] | translate }}
              </td>
              <td>{{ dataItem.phoneNumber }}</td>
              <td>
                <span title="{{ dataItem.email }}">{{
                  dataItem.email | limitWord
                  }}</span>
              </td>
              <td class="text-center">{{ dataItem.customerSectorId }}</td>
              <td class="text-center">
                <span>{{
                  CUSTOMER_TYPE_MAP[dataItem.accountType] | translate
                  }}</span>
              </td>
              <td>{{ dataItem.referralCode }}</td>
              <td>{{ dataItem.userFullNameReferral }}</td>
              <td>{{ dataItem.phoneNumberReferral }}</td>

              <td class="text-center">
                <span class="badge" [ngClass]="
                    CUSTOMER_ACTIVE_STATUS_MAP[dataItem.activeStatus || 0].style
                  ">{{
                  CUSTOMER_ACTIVE_STATUS_MAP[dataItem.activeStatus || 0].label
                  | translate
                  }}</span>
              </td>
              <td class="text-center">
                <span class="badge" [ngClass]="CUSTOMER_STATUS_MAP[dataItem.status || 0].style">{{
                  CUSTOMER_STATUS_MAP[dataItem.status || 0].label | translate
                  }}</span>
              </td>
              <td class="text-center freezer-td">
                <button ngbTooltip="{{ 'common.action.detail' | translate }}" class="btn px-1 py-0"
                  (click)="viewDetail(dataItem.customerId)" [routerLinkActive]="['active']">
                  <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                </button>
                <ng-container *ngIf="dataItem.status !== CUSTOMER_STATUS_CONST.CANCEL.value">
                  <ng-container *hasPrivileges="SYSTEM_RULES.CUSTOMER_RESET_PW">
                    <button *ngIf="dataItem.status === 1" ngbTooltip="{{
                        'common.action.changePassword' | translate
                      }}" class="btn px-1 py-0" (click)="resetPassword(dataItem)">
                      <i class="bi bi-key mb-color" aria-hidden="true"></i>
                    </button>
                  </ng-container>
                  <ng-container *ngIf="
                      dataItem.approvalStatus !==
                      APPROVAL_STATUS_CONST.PENDING.value
                    ">
                    <button [ngbTooltip]="
                        (dataItem.status === ENTITY_STATUS_CONST.ACTIVE.code
                          ? 'common.action.lock'
                          : 'common.action.unlock'
                        ) | translate
                      " class="btn px-1 py-0" (click)="lockAndUnlock(dataItem)" *hasPrivileges="
                        dataItem.status === 1
                          ? SYSTEM_RULES.CUSTOMER_LOCK
                          : SYSTEM_RULES.CUSTOMER_UNLOCK
                      ">
                      <i [className]="
                          dataItem.status === 1
                            ? 'fa fa-lock mb-color'
                            : 'fa fa-unlock mb-color'
                        " aria-hidden="true"></i>
                    </button>
                  </ng-container>
                  <ng-container *ngIf="
                      dataItem.approvalStatus !==
                      APPROVAL_STATUS_CONST.PENDING.value
                    ">
                    <button class="btn px-1 py-0" ngbTooltip="{{ 'common.action.close' | translate }}"
                      (click)="cancelCustomer(dataItem)" *hasPrivileges="SYSTEM_RULES.CUSTOMER_CANCEL">
                      <i class="bi bi-x-circle-fill mb-color" aria-hidden="true"></i>
                    </button>
                  </ng-container>
                </ng-container>
                <ng-container *ngIf="validateUnclosed(dataItem)">
                  <button class="btn px-1 py-0" ngbTooltip="{{ 'common.action.unclosed' | translate }}"
                    (click)="unclosed(dataItem)" *hasPrivileges="SYSTEM_RULES.CUSTOMER_UNCLOSED">
                    <i class="bi bi-check2-circle mb-color" aria-hidden="true"></i>
                  </button>
                </ng-container>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="row d-block text-center m-0 no-search-result-wrapper" *ngIf="data?.length === 0">
          <img src="/assets/dist/img/icon/empty.svg" height="120" alt="no_search_result" />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
      </div>
      <div *ngIf="data.length">
        <mat-paginator [length]="form.value.length" [pageSize]="form.value.pageSize" [pageIndex]="form.value.pageIndex"
          [pageSizeOptions]="pageSizeOptions" (page)="onChangePage($event)" aria-label="Select page">
        </mat-paginator>
      </div>
    </div>
  </div>
</section>