import { Component, HostListener, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Form<PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import {
  ENTITY_STATUS,
  FILE_UPLOAD_EXTENSIONS,
  NATION_CODES,
  QUANTITY_UPLOAD,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { Bank } from '@shared/models/bank.model';
import { IFileEntrySearch } from '@shared/models/request/file-entry.search';
import { BankService } from '@shared/services/bank.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-bank-create-update',
  templateUrl: './bank-create-update.component.html',
  styleUrls: ['./bank-create-update.component.scss'],
})
export class BankCreateUpdateComponent implements OnInit, OnDestroy {
  // form control data
  formBank: FormGroup = new FormGroup({});
  // input call api
  bankDTO: Bank = new Bank();
  // input data form control
  bank: Bank = new Bank();
  reader: FileReader = new FileReader();
  eKycPicUrls: string[] = [];
  // input: file upload to create
  fileUploads: any[] = [];
  getFile: any[] = [];
  imageUrls: any[] = [];
  fileDelete: any[] = [];
  // default two file upload
  fileRequired: string[] = ['Logo'];

  hasFilter = false;
  isUpdate = false;

  ROUTER_UTILS = ROUTER_UTILS;
  ENTITY_STATUS = ENTITY_STATUS;
  VALIDATORS = VALIDATORS;
  FILE_UPLOAD_EXTENSIONS = FILE_UPLOAD_EXTENSIONS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  NATION_CODES = NATION_CODES;

  validateCustomMessages = {
    bankCodeNumber: {
      pattern: 'error.bank.pattern.bankCodeNumber',
      // max: '...',
      // min: '...',
      // maxlength: '...',
      // minlength: '...',
      // email: '...'
    },
    bankCode: {
      pattern: 'error.bank.pattern.bankCode',
    },
  };

  constructor(
    private formBuilder: FormBuilder,
    private modalService: ModalService,
    private bankService: BankService,
    private routerActive: ActivatedRoute,
    private toastService: ToastrCustomService,
    private router: Router
  ) {
    // get id form paramater
    this.routerActive.paramMap.subscribe((res) => {
      const idParam = res.get('bankId');
      if (idParam) {
        this.bankDTO.bankId = +idParam;
        this.isUpdate = true;
      }
    });
  }

  /**
   * remove storage
   */
  ngOnDestroy(): void {
    if (!this.hasFilter) {
      sessionStorage.removeItem(STORAGE_APP.BANK);
    }
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.hasFilter = true;
  }

  ngOnInit(): void {
    if (this.isUpdate) {
      this.getDetail();
    } else {
      this.initForm();
    }
  }

  /**
   * init form
   *
   * @param bank Bank
   */
  initForm(bank?: Bank): void {
    this.formBank = this.formBuilder.group({
      bankName: [
        bank?.bankName || '',
        [
          Validators.required,
          Validators.minLength(VALIDATORS.LENGTH.BANK_NAME_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
        ],
      ],
      bankCode: [
        bank?.bankCode || '',
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.BANK_CODE),
          Validators.minLength(VALIDATORS.LENGTH.BANK_CODE_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.BANK_CODE_MAX_LENGTH),
        ],
      ],
      bankCodeNumber: [
        bank?.bankCodeNumber || null,
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.NUMBER),
          Validators.minLength(VALIDATORS.LENGTH.BANK_CODE_NUMBER_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.BANK_CODE_NUMBER_MAX_LENGTH),
        ],
      ],
      // status: [bank?.status || '', [Validators.required]],
      order: [
        bank?.order || null,
        [
          Validators.required,
          Validators.min(VALIDATORS.LENGTH.BANK_ORDER_MIN),
          Validators.max(VALIDATORS.LENGTH.BANK_ORDER_MAX),
        ],
      ],
      icon: [bank?.icon || ''],
      files: [null, [Validators.required]],
      nation: [bank?.nation || null, [Validators.required]],
    });
    if (this.isUpdate) {
      this.formBank.controls.bankCode.disable();
      this.formBank.controls.bankCodeNumber.disable();
    }
  }

  /**
   * back to monitor list
   */
  backToList(): void {
    this.hasFilter = true;
    this.router.navigate([ROUTER_UTILS.bank.root]);
  }

  /**
   * get detail bank
   */
  getDetail(): void {
    if (this.bankDTO.bankId) {
      this.bankService.detail(this.bankDTO).subscribe((res: any) => {
        const data = res.body || undefined;
        if (data.icon) {
          const searchFile: IFileEntrySearch = {};
          searchFile.classPk = data.bankId;
          searchFile.fileEntryId = data.icon.id;
          searchFile.normalizeName = data.icon.normalizeName;

          this.bankService.getIcon(searchFile).subscribe((responsive: any) => {
            CommonUtils.blobToBase64(responsive.body).then((base64) => {
              this.imageUrls = [{ src: base64, name: null, id: data.icon.id }];
            });
          });
          // this.getFile = [{ scr: data.iconUrL, name: null }];
        }
        this.bank = data;
        this.initForm(data);
      });
    }
  }

  /**
   * Create: check validate file and data input if valid then call api create
   */
  onCreate() {
    // check file, if is empty then set required file
    if (this.fileUploads.length === 0) {
      this.formBank.controls.files.setValidators(Validators.required);
      this.formBank.controls.files.updateValueAndValidity();
    }
    this.validateFileLength(this.fileUploads);

    // touched form
    if (this.formBank.invalid) {
      CommonUtils.markFormGroupTouched(this.formBank);
    }
    const data = this.formBank.getRawValue();
    //  set data files = map fileUploads
    data.files = this.fileUploads.map((fileX) => fileX.file);

    if (this.formBank.valid) {
      this.bankService.create(data).subscribe((res): void => {
        this.router.navigate([ROUTER_UTILS.bank.root]);
        this.toastService.success('common.action.createSuccess');
      });
    }
  }

  /**
   * Update: check validate file and data input if valid then call api create
   */
  onUpdate() {
    // check file, if is empty then set required file
    if (this.fileUploads.length === 0 && this.imageUrls.length === 0) {
      this.formBank.controls.files.setValidators(Validators.required);
      this.formBank.controls.files.updateValueAndValidity();
    }
    this.validateFileLength(this.fileUploads);

    const data = this.formBank.getRawValue();
    // touched form
    if (this.imageUrls.length > 0 && this.fileDelete.length === 0) {
      this.formBank.controls.files.clearValidators();
      this.formBank.controls.files.updateValueAndValidity();
    }

    if (this.formBank.invalid) {
      CommonUtils.markFormGroupTouched(this.formBank);
    }

    data.bankId = this.bank?.bankId;
    data.files = this.fileUploads.map((fileX) => fileX.file);
    if (this.formBank.valid) {
      this.bankService.update(data).subscribe((res: any) => {
        this.router.navigate([ROUTER_UTILS.bank.root]);
        this.toastService.success('common.action.updateSuccess');
      });
    }
  }

  /**
   * Upload picture
   * set file upload and check validate file
   *
   * @param fileSelected any
   */
  onUploadPics(fileSelected: any): void {
    // set fileUpload
    this.fileUploads = fileSelected;

    // check file, if file have 1 then required file and check have 2 file
    this.validateFileLength(this.fileUploads);
  }

  onRemoveImage(image: any): void {
    this.fileDelete.push(image.id);
  }

  /**
   * check validate file
   *
   * @param files any[]
   */
  private validateFileLength(files: any[] = []): void {
    // check file, if file have 1 then required file and check have 2 file clear required
    const srcFile =
      files.map((file: any) => file.src).filter((src: any) => src != null) ||
      [];
    if (srcFile?.length < QUANTITY_UPLOAD.ICON_BANK) {
      this.formBank.controls.files.setValidators(Validators.required);
      this.formBank.controls.files.updateValueAndValidity();
    } else {
      this.formBank.controls.files.clearValidators();
      this.formBank.controls.files.updateValueAndValidity();
    }
  }
}
