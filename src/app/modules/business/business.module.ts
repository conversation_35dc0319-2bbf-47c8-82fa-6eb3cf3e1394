import { CdkAccordionModule } from '@angular/cdk/accordion';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { SharedModule } from '@shared/shared.module';
import { BackgroundLoginComponent } from './background-login-management/background-login.component';
import { BankCreateUpdateComponent } from './bank-management/bank-create-update/bank-create-update.component';
import { BankDetailComponent } from './bank-management/bank-detail/bank-detail.component';
import { BankComponent } from './bank-management/bank.component';
import { BusinessRoutingModule } from './business-routing.module';
import { CampaignManagementComponent } from './campaign-management/campaign-management.component';
import { CreateCampaignComponent } from './campaign-management/create-campaign/create-campaign.component';
import { DetailUpdateCampaignComponent } from './campaign-management/detail-update-campaign/detail-update-campaign.component';
import { ModalDeleteBannerComponent } from './campaign-management/modal-delete-banner/modal-delete-banner.component';
import { ClientCreateUpdateComponent } from './client-management/client-create-update/client-create-update.component';
import { ClientManagementComponent } from './client-management/client-management.component';
import { ConfigSavingManagementComponent } from './config-saving-management/config-saving-management.component';
import { CreateConfigSavingComponent } from './config-saving-management/create-config-saving/create-config-saving.component';
import { ConfigTransactionManagementComponent } from './config-transaction-management/config-transaction-management.component';
import { CreateConfigComponent } from './config-transaction-management/create-config/create-config.component';
import { DetailUpdateConfigComponent } from './config-transaction-management/detail-update-config/detail-update.component';
import { ModalConfirmUpdateConfigComponent } from './config-transaction-management/modal-confirm-update-config/modal-confirm-update-config.component';
import { CurrencyCrudComponent } from './currency/currency-crud/currency-crud.component';
import { CurrencyManagementComponent } from './currency/currency-management.component';
import { CustomerSupportComponent } from './customer-info-management/customer-support.component';
import { AccountNoTransactionComponent } from './customer-management/account-no-transaction/account-no-transaction.component';
import { DetailAccountTransactionComponent } from './customer-management/account-no-transaction/detail-account-transaction/detail-account-transaction.component';
import { ActiveAccountManualComponent } from './customer-management/activate-account-manual/activate-account-manual.component';
import { CustomerApprovalCreateComponent } from './customer-management/customer-approval-management/customer-approval-create/customer-approval-create.component';
import { CustomerApprovalManagementComponent } from './customer-management/customer-approval-management/customer-approval-management.component';
import { CustomerApprovalModalComponent } from './customer-management/customer-approval-management/customer-approval-modal/customer-approval-modal.component';
import { CustomerApprovalUpdateComponent } from './customer-management/customer-approval-management/customer-approval-update/customer-approval-update.component';
import { ModalImportCustomerComponent } from './customer-management/customer-approval-management/modal-import-customer/modal-import-customer.component';
import { CustomerHistoryInfoComponent } from './customer-management/customer-info-history/customer-history-info/customer-history-info.component';
import { CustomerInfoComponent } from './customer-management/customer-info-history/customer-info/customer-info.component';
import { CustomerUpdateHistoryComponent } from './customer-management/customer-info-history/customer-update-history/customer-update-history.component';
import { AccountNumberModalComponent } from './customer-management/customer-register-management/account-number-modal/account-number-modal.component';
import { CreateCustomerComponent } from './customer-management/customer-register-management/create-customer/create-customer.component';
import { CustomerRegisterManagementComponent } from './customer-management/customer-register-management/customer-register-management.component';
import { DetailCustomerComponent } from './customer-management/customer-register-management/detail-customer/detail-customer.component';
import { SectorPopupComponent } from './customer-management/customer-register-management/sector-popup/sector-popup.component';
import { ConFigAutoReportUmoneyLapnetComponent } from './debit-deposit/lapnet-report/config-auto-report/config-auto-report-umoney-lapnet.component';
import { UmoneyLapnetReportComponent } from './debit-deposit/lapnet-report/umoney-lapnet-report.component';
import { DebitAccountCreateComponent } from './debit-deposit/manage-debit-account/debit-account-create/debit-account-create.component';
import { DebitAccountDetailUpdateComponent } from './debit-deposit/manage-debit-account/debit-account-detail-update/debit-account-detail-update.component';
import { ManageDebitAccountComponent } from './debit-deposit/manage-debit-account/debit-account.component';
import { ConFigAutoDebitReportComponent } from './debit-deposit/transaction-debit-report-umoney/config-auto-debit-report/config-auto-debit-report.component';
import { TransactionDebitReportUmoneyComponent } from './debit-deposit/transaction-debit-report-umoney/transaction-debit-report-umoney.component';
import { DepartmentCreateUpdateComponent } from './department-management/department-create-update/department-create-update.component';
import { DepartmentManagementComponent } from './department-management/department-management.component';
import { ErrorCodeDetailComponent } from './error-code-management/error-code-detail/error-code-detail.component';
import { ErrorCodeManagementComponent } from './error-code-management/error-code-management.component';
import { ConfigurationFeeTypePopupComponent } from './fee-management/configuration-fee-type-popup/configuration-fee-type-popup.component';
import { FeeCreateUpdateComponent } from './fee-management/fee-create-update/fee-create-update.component';
import { FeeManagementComponent } from './fee-management/fee-management.component';
import { FeeRateCreateUpdateComponent } from './fee-management/fee-rate/fee-rate-create-update/fee-rate-create-update.component';
import { FeeRateComponent } from './fee-management/fee-rate/fee-rate.component';
import { FeeScheduleComponent } from './fee-schedule/fee-schedule-crud/fee-schedule.component';
import { FeeScheduleManagementComponent } from './fee-schedule/fee-schedule-management.component';
import { InformationTemplateCreateUpdateComponent } from './information-template/information-template-create-update/information-template-create-update.component';
import { CreateUpdateInformationTemplateComponent } from './information-template/information-template-crud/create-update-information-template.component';
import { InformationTemplateComponent } from './information-template/information-template.component';
import { ConFigAutoReportLapNetComponent } from './lapnet-report/config-auto-report/config-auto-report-lapnet.component';
import { LapnetReportComponent } from './lapnet-report/lapnet-report.component';
import { LoanOnlineDetailComponent } from './loan-online-management/loan-online-detail/loan-online-detail.component';
import { LoanOnlineManagementComponent } from './loan-online-management/loan-online-management.component';
import { InsuranceManagemetComponent } from './lvi-managemet/insurance-managemet.component';
import { ManagePaymentServiceComponent } from './manage-payment-service/manage-payment-service.component';
import { PaymentServiceCreateComponent } from './manage-payment-service/payment-service-create/payment-service-create.component';
import { PaymentServiceDetailUpdateComponent } from './manage-payment-service/payment-service-detail-update/payment-service-detail-update.component';
import { CreateUpdateMasterMerchantComponent } from './master-merchant-management/create-update-master-merchant/create-update-master-merchant.component';
import { DetailMasterMerchantComponent } from './master-merchant-management/detail-master-merchant/detail-master-merchant.component';
import { MasterMerchantManagementComponent } from './master-merchant-management/master-merchant-management.component';
import { CreateUpdateMerchantComponent } from './merchant-management/create-update-merchant/create-update-merchant.component';
import { DetailMerchantComponent } from './merchant-management/detail-merchant/detail-merchant.component';
import { MerchantManagementComponent } from './merchant-management/merchant-management.component';
import { MonitorLogManagementComponent } from './monitor-log/monitor-log-management.component';
import { CreateNewsComponent } from './news-management/create-update-news/create-update-news.component';
import { NewsManagementComponent } from './news-management/news-management.component';
import { ImportCustomerNotificationComponent } from './notification-management/modal-import/modal-import-notification.component';
import { NotificationCreateUpdateComponent } from './notification-management/notification-create-update/notification-create-update.component';
import { NotificationCustomerComponent } from './notification-management/notification-customer/notification-customer.component';
import { NotificationManagementComponent } from './notification-management/notification-management.component';
import { PositionCreateUpdateComponent } from './position-management/position-create-update/position-create-update.component';
import { PositionManagementComponent } from './position-management/position-management.component';
import { NotificationHistoryConFigComponent } from './premium-account-number-error/config-auto-report/notification-history-config.component';
import { PremiumAccountNumberErrorComponent } from './premium-account-number-error/premium-account-number-error.component';
import { CreatePremiumAccNumberComponent } from './premium-account-number/create-premium-acc-number/create-premium-acc-number.component';
import { SendOtpComponent } from './premium-account-number/create-premium-acc-number/modal-send-otp/modal-send-otp.component';
import { NumberGroupCreateUpdateComponent } from './premium-account-number/number-group/number-group-crud/number-group-create-update.component';
import { NumberGroupDeleteComponent } from './premium-account-number/number-group/number-group-delete/number-group-delete.component';
import { NumberGroupManagementComponent } from './premium-account-number/number-group/number-group-management.component';
import { PremiumAccountNumberSearchComponent } from './premium-account-number/premium-account-number-tab/premium-account-number-search/premium-account-number-search.component';
import { PremiumAccountNumberSoldComponent } from './premium-account-number/premium-account-number-tab/premium-account-number-sold/premium-account-number-sold.component';
import { PremiumAccountNumberTabComponent } from './premium-account-number/premium-account-number-tab/premium-account-number-tab.component';
import { ImportStructureAccountComponent } from './premium-account-number/premium-account-structure/modal-import/modal-import-structure.component';
import { PremiumAccountNumberCreateComponent } from './premium-account-number/premium-account-structure/premium-account-number-create/premium-account-number-create.component';
import { PremiumAccountStructureComponent } from './premium-account-number/premium-account-structure/premium-account-structure.component';
import { ImportSpecialAccountNumberComponent } from './premium-account-number/special-account-number/modal-import/modal-import-special-account-number.component';
import { SpecialAccountNumberCreateComponent } from './premium-account-number/special-account-number/special-account-number-create/special-account-number-create.component';
import { SpecialAccountNumberComponent } from './premium-account-number/special-account-number/special-account-number.component';
import { ChangePasswordComponent } from './profile/change-password/change-password.component';
import { ProfileComponent } from './profile/profile.component';
import { CreateRateComponent } from './rate-management/create-rate/create-rate.component';
import { RateManagementComponent } from './rate-management/rate-management.component';
import { ReferralCreateUpdateManagementComponent } from './referral-management/referral-management-create-update/referral-management-create-update.component';
import { ReferralDetailManagementComponent } from './referral-management/referral-management-detail/referral-management-detail.component';
import { ReferralManagementImportComponent } from './referral-management/referral-management-import/referral-management-immport.component';
import { ReferralManagementComponent } from './referral-management/referral-management.component';
import { CreateRoleComponent } from './role-management/create-role/create-role.component';
import { DetailRoleComponent } from './role-management/detail-role/detail-role.component';
import { RoleManagementComponent } from './role-management/role-management.component';
import { SavingDetailComponent } from './saving-management/saving-info-management/saving-detail/saving-detail.component';
import { SavingInfoComponent } from './saving-management/saving-info-management/saving-info.component';
import { SavingTransactionHistoryComponent } from './saving-management/saving-info-management/saving-transaction-history/saving-transaction-history.component';
import { SavingManagementComponent } from './saving-management/saving-management.component';
import { ServicePackCreateUpdateComponent } from './service-pack-management/service-pack-create-update/service-pack-create-update.component';
import { ServicePackManagementComponent } from './service-pack-management/service-pack-management.component';
import { NotificationSMSBalanceFeeFailedConfig } from './sms-balance-fee-failed/config-auto-report/notification-sms-balance-fee-failed-config.component';
import { SMSBalanceFeeFailedComponent } from './sms-balance-fee-failed/sms-balance-fee-failed.component';
import { SmsBalanceManagementComponent } from './sms-balance-management/sms-balance-management.component';
import { SmsManagementComponent } from './sms-management/sms-management.component';
import { TransactionQrPayManagementComponent } from './transaction-qr-pay/transaction-qr-pay-management.component';
import { ManageTransactionQuantityComponent } from './transaction-report/manage-transaction-quantity/manage-transaction-quantity.component';
import { TransactionNotificationLimitComponent } from './transaction-report/transaction-notification-limit/transaction-notification-limit.component';
import { TransactionReportMMoneyComponent } from './transaction-report/transaction-report-mmoney/transaction-report-mmoney.component';
import { ConFigAutoReportComponent } from './transaction-report/transaction-report-umoney/config-auto-report/config-auto-report.component';
import { TransactionReportUmoneyComponent } from './transaction-report/transaction-report-umoney/transaction-report-umoney.component';
import { TransactionReportComponent } from './transaction-report/transaction-report.component';
import { UserCreateUpdateComponent } from './user-management/user-create-update/user-create-update.component';
import { UserManagementComponent } from './user-management/user-management.component';
import { ModalCreateDetailVersionComponent } from './version-management/modal-create-detail/modal-create-detail.component';
import { VersionAndroidManagementComponent } from './version-management/version-android-management/version-android-management.component';
import { VersionIosManagementComponent } from './version-management/version-ios-management/version-ios-management.component';
import { VersionManagementComponent } from './version-management/version-management.component';
import { AsyncDateReport } from './debit-deposit/lapnet-report/asyn-date-report/async-date-report.component';
import { AsyncDateLapnetReport } from './lapnet-report/asyn-date-report/async-date-report.component';
import { InternationalTransactionReportComponent } from './transaction-report/international-transaction-report/international-transaction-report';
@NgModule({
  declarations: [
    CustomerRegisterManagementComponent,
    DetailCustomerComponent,
    ErrorCodeDetailComponent,
    CustomerRegisterManagementComponent,
    ErrorCodeManagementComponent,
    CreateCustomerComponent,
    RoleManagementComponent,
    CreateRoleComponent,
    ModalComponent,
    DetailRoleComponent,
    CustomerApprovalManagementComponent,
    LoanOnlineManagementComponent,
    LoanOnlineDetailComponent,
    BankComponent,
    BankCreateUpdateComponent,
    UserManagementComponent,
    UserCreateUpdateComponent,
    ProfileComponent,
    ChangePasswordComponent,
    PositionManagementComponent,
    PositionCreateUpdateComponent,
    CustomerInfoComponent,
    CustomerApprovalUpdateComponent,
    CustomerApprovalCreateComponent,
    DepartmentManagementComponent,
    DepartmentManagementComponent,
    DepartmentCreateUpdateComponent,
    NotificationManagementComponent,
    NotificationCreateUpdateComponent,
    NotificationCustomerComponent,
    CustomerHistoryInfoComponent,
    CustomerUpdateHistoryComponent,
    CustomerApprovalModalComponent,
    MerchantManagementComponent,
    MasterMerchantManagementComponent,
    CreateUpdateMasterMerchantComponent,
    CreateUpdateMerchantComponent,
    DetailMerchantComponent,
    DetailMasterMerchantComponent,
    FeeManagementComponent,
    ConfigurationFeeTypePopupComponent,
    FeeCreateUpdateComponent,
    FeeRateComponent,
    FeeRateCreateUpdateComponent,
    InformationTemplateComponent,
    InformationTemplateCreateUpdateComponent,
    BankDetailComponent,
    CampaignManagementComponent,
    TransactionReportComponent,
    LapnetReportComponent,
    AccountNumberModalComponent,
    ReferralManagementComponent,
    ReferralDetailManagementComponent,
    ReferralCreateUpdateManagementComponent,
    ReferralManagementImportComponent,
    VersionManagementComponent,
    ModalCreateDetailVersionComponent,
    InsuranceManagemetComponent,
    ConfigTransactionManagementComponent,
    ConfigSavingManagementComponent,
    NewsManagementComponent,
    CreateNewsComponent,
    CreateConfigComponent,
    CreateConfigSavingComponent,
    DetailUpdateConfigComponent,
    ModalConfirmUpdateConfigComponent,
    CreateCampaignComponent,
    DetailUpdateCampaignComponent,
    ModalDeleteBannerComponent,
    SmsManagementComponent,
    TransactionReportUmoneyComponent,
    ModalImportCustomerComponent,
    ManagePaymentServiceComponent,
    PaymentServiceCreateComponent,
    PaymentServiceDetailUpdateComponent,
    ManageTransactionQuantityComponent,
    VersionAndroidManagementComponent,
    VersionIosManagementComponent,
    SavingManagementComponent,
    SavingDetailComponent,
    SavingTransactionHistoryComponent,
    SavingInfoComponent,
    RateManagementComponent,
    CreateRateComponent,
    TransactionReportMMoneyComponent,
    InternationalTransactionReportComponent,
    FeeScheduleComponent,
    FeeScheduleManagementComponent,
    AccountNoTransactionComponent,
    DetailAccountTransactionComponent,
    TransactionNotificationLimitComponent,
    ActiveAccountManualComponent,
    ConFigAutoReportComponent,
    ConFigAutoReportLapNetComponent,
    ImportCustomerNotificationComponent,
    BackgroundLoginComponent,
    CustomerSupportComponent,
    PremiumAccountStructureComponent,
    ImportStructureAccountComponent,
    MonitorLogManagementComponent,
    TransactionQrPayManagementComponent,
    CreateUpdateInformationTemplateComponent,
    ServicePackManagementComponent,
    ServicePackCreateUpdateComponent,
    ClientManagementComponent,
    ClientCreateUpdateComponent,
    SmsBalanceManagementComponent,
    SectorPopupComponent,
    PremiumAccountNumberCreateComponent,
    SpecialAccountNumberComponent,
    SpecialAccountNumberCreateComponent,
    ImportSpecialAccountNumberComponent,
    PremiumAccountNumberTabComponent,
    PremiumAccountNumberSoldComponent,
    PremiumAccountNumberSearchComponent,
    NumberGroupManagementComponent,
    NumberGroupCreateUpdateComponent,
    NumberGroupDeleteComponent,
    CurrencyManagementComponent,
    CurrencyCrudComponent,
    PremiumAccountNumberErrorComponent,
    NotificationHistoryConFigComponent,
    CreatePremiumAccNumberComponent,
    SendOtpComponent,
    SMSBalanceFeeFailedComponent,
    NotificationSMSBalanceFeeFailedConfig,
    ManageDebitAccountComponent,
    DebitAccountDetailUpdateComponent,
    DebitAccountCreateComponent,
    TransactionDebitReportUmoneyComponent,
    ConFigAutoDebitReportComponent,
    UmoneyLapnetReportComponent,
    ConFigAutoReportUmoneyLapnetComponent,
    AsyncDateReport,
    AsyncDateLapnetReport
  ],
  imports: [
    CommonModule,
    SharedModule,
    BusinessRoutingModule,
    MatCheckboxModule,
    MatRadioModule,
    CdkAccordionModule,
    MatDatepickerModule,
    MatSelectModule,
    MatTabsModule,
  ],
})
export class BusinessModule {}
