import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import {
  BG_COLOR_CONST,
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  FILE_UPLOAD_EXTENSIONS,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IBgLogin } from '@shared/models/bg-login.model';
import { IFileEntrySearch } from '@shared/models/request/file-entry.search';
import { BackgroundLoginService } from '@shared/services/background-login.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-background-login',
  templateUrl: './background-login.component.html',
  styleUrls: ['./background-login.component.scss'],
})
export class BackgroundLoginComponent implements OnInit {
  formCreate: FormGroup = new FormGroup({});
  fileUploads: any[] = [];
  imageUrls: any[] = [];
  fileDelete: any[] = [];
  fileRequired: string[] = ['Logo'];
  data: IBgLogin = {};
  ENTITY_STATUS = ENTITY_STATUS;
  VALIDATORS = VALIDATORS;
  FILE_UPLOAD_EXTENSIONS = FILE_UPLOAD_EXTENSIONS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  checkPermission = '';

  constructor(
    private formBuilder: FormBuilder,
    private backgroundLoginService: BackgroundLoginService,
    private toastService: ToastrCustomService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.getDetail();
  }

  initForm(data?: IBgLogin): void {
    this.formCreate = this.formBuilder.group({
      colorCode: [
        data?.colorCode || BG_COLOR_CONST.COLOR,
        [Validators.required],
      ],
      status: [
        data?.status || ENTITY_STATUS_CONST.INACTIVE.code,
        [Validators.required],
      ],
      files: [data?.icon, [Validators.required]],
      imageLoginAppId: data?.imageLoginAppId,
    });
  }

  onUploadPics(fileSelected: any): void {
    this.fileUploads = fileSelected;
    this.formCreate.controls.files.setValue(
      this.fileUploads.map((fileX) => fileX.file)
    );
  }

  onRemoveImage(image: any): void {
    this.fileDelete.push(image.id);
    this.formCreate.controls.files.setValue('');
  }

  getDetail(): void {
    this.backgroundLoginService.detail().subscribe((res: any) => {
      if (res.body !== null) {
        this.data = res.body;
        const searchFile: IFileEntrySearch = {};
        searchFile.classPk = res.body.imageLoginAppId;
        searchFile.fileEntryId = res.body.icon?.id;
        searchFile.normalizeName = res?.body.icon?.normalizeName;
        this.backgroundLoginService
          .getIcon(searchFile)
          .subscribe((responsive: any) => {
            CommonUtils.blobToBase64(responsive.body).then((base64) => {
              this.imageUrls = [
                { src: base64, name: null, id: res.body.icon?.id },
              ];
            });
          });
        this.checkPermission = SYSTEM_RULES.IMAGE_LOGIN_APP_WRITE;
        this.initForm(res.body);
      } else {
        this.checkPermission = SYSTEM_RULES.IMAGE_LOGIN_APP_CREATE;
      }
    });
  }

  onSubmit() {
    if (this.formCreate.invalid) {
      CommonUtils.markFormGroupTouched(this.formCreate);
      return;
    }
    const body = this.formCreate.getRawValue();
    Object.keys(this.data).length === 0
      ? this.onCreate(body)
      : this.onUpdate(body);
  }

  onCreate(body: IBgLogin): void {
    this.backgroundLoginService.create(body).subscribe((responsive: any) => {
      this.toastService.success('common.action.createSuccess');
      this.getDetail();
    });
  }

  onUpdate(body: IBgLogin): void {
    this.backgroundLoginService.update(body).subscribe((res: any) => {
      this.toastService.success('common.action.updateSuccess');
    });
  }
}
