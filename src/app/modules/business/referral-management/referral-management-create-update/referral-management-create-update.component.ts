import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { ENTITY_STATUS, RM_TYPE, RM_TYPE_CONST, RM_TYPE_CREATE } from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { MODEL_MAP_ITEM_COMMON } from '@shared/models/model.type';
import { IReferral, Referral } from '@shared/models/referral.model';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ReferralService } from '@shared/services/referral.service';
import { ROUTER_ACTIONS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
@Component({
  selector: 'referral-create-update',
  templateUrl: './referral-management-create-update.component.html',
  styleUrls: ['./referral-management-create-update.component.scss'],
})
export class ReferralCreateUpdateManagementComponent implements OnInit {
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  VALIDATORS = VALIDATORS;
  SYSTEM_RULES = SYSTEM_RULES;
  ENTITY_STATUS = ENTITY_STATUS;
  RM_TYPE = RM_TYPE_CREATE;
  actionConfirm!: MODEL_MAP_ITEM_COMMON;
  formReferral: FormGroup = new FormGroup({});
  referral: IReferral = {};

  constructor(
    private fb: FormBuilder,
    public translateService: TranslateService,
    public activeModal: NgbActiveModal,
    private referralService: ReferralService,
    private toastService: ToastrCustomService
  ) {}

  ngOnInit(): void {
    this.initForm();
  }

  initForm(referral?: Referral) {
    this.formReferral = this.fb.group({
      userFullName: [
        referral?.userFullName || '',
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.FULLNAME),
          Validators.minLength(VALIDATORS.LENGTH.REFERRAL_FULL_NAME_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.REFERRAL_FULL_NAME_MAX_LENGTH),
        ],
      ],
      userCode: [
        referral?.userCode || '',
        [
          Validators.minLength(
            VALIDATORS.LENGTH.REFERRAL_STAFF_CODE_MIN_LENGTH
          ),
          Validators.maxLength(
            VALIDATORS.LENGTH.REFERRAL_STAFF_CODE_MAX_LENGTH
          ),
        ],
      ],
      phoneNumber: [
        referral?.phoneNumber || '',
        [
          Validators.required,
          Validators.minLength(
            VALIDATORS.LENGTH.REFERRAL_PHONE_NUMBER_MIN_LENGTH
          ),
          Validators.maxLength(
            VALIDATORS.LENGTH.REFERRAL_PHONE_NUMBER_MAX_LENGTH
          ),
          Validators.pattern(VALIDATORS.PATTERN.PHONE),
        ],
      ],
      referralId: '',
      type: [referral?.type, [Validators.required]],
      rmCode: [
        referral?.rmCode || '',
        [
          Validators.maxLength(VALIDATORS.LENGTH.REFERRAL_RM_CODE_MAX_LENGTH),
          Validators.minLength(VALIDATORS.LENGTH.REFERRAL_RM_CODE_MIN_LENGTH),
        ],
      ],
    });
  }

  onCreate(): void {
    if (this.formReferral.invalid) {
      CommonUtils.markFormGroupTouched(this.formReferral);
    }
    const params = this.formReferral.getRawValue();
    if (this.formReferral.valid) {
      this.referralService.create(params).subscribe(() => {
        this.toastService.success('common.action.createSuccess');
        this.activeModal.close(this.actionConfirm.code);
      });
    }
  }
}
