<input type="checkbox" id="toggle-button-checkbox" [checked]="checked" />
<label class="toggle-button-switch" for="" (click)="openPopUp(checked)">
</label>
<div class="toggle-button-text">
  <div
    [ngClass]="
      checked ? 'toggle-button-text-on active' : 'toggle-button-text-on '
    "
  >
    {{ leftText }}
  </div>
  <div
    [ngClass]="
      checked ? 'toggle-button-text-off ' : 'toggle-button-text-off active'
    "
  >
    {{ rightText }}
  </div>
</div>
