export interface IDebitAccount {
  createdBy: string;
  createdDate: string;
  lastModifiedBy: string;
  lastModifiedDate: string;
  id: number;
  accountBank: string;
  accountNumber: string;
  accountName: string;
  accountType: string;
  accountCurrency: string;
  status: number;
  transactionLimit: number;
  cif?: number;
  amountTransferred?: number;
  configurationType?: string;
  accountBalance?: number;
  amountAdded?: number;
}

export interface IDebitAccountCreate {
  accountNumber?: string;
  accountName?: string;
  cif?: string;
  type?: string;
}
