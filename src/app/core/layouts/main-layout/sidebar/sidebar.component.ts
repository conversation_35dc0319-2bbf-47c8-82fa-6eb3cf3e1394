import { Component, OnInit } from '@angular/core';
import { SIDEBAR_CONST } from './sidebar.constant';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
})
export class SidebarComponent implements OnInit {
  SIDEBAR_CONST = SIDEBAR_CONST;
  isShowDropdownMenu: any = [];

  constructor() {}

  ngOnInit(): void {}

  onOpenMenu(i: number) {
    this.isShowDropdownMenu[i] = !this.isShowDropdownMenu[i];
  }

  close(vent: any) {
    for (let i = 0; i < this.SIDEBAR_CONST.length; i++) {
      this.isShowDropdownMenu[i] = false;
    }
  }
}
