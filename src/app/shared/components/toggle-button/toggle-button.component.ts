import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'toggle-button',
  templateUrl: './toggle-button.component.html',
  styleUrls: ['./toggle-button.component.scss'],
})
export class ToggleButtonComponent {
  @Input() checked?: boolean;
  @Input() leftText = '';
  @Input() rightText = '';
  @Output() changed = new EventEmitter<boolean>();
  @Output() openpopup = new EventEmitter();
  openPopUp($event: any) {
    this.openpopup.emit($event);
  }
}
