import { Injectable } from '@angular/core';
import { IActiveToken } from '@shared/models/active-token.model';
import { IUserSearch } from '@shared/models/request/user.search';
import { IUser } from '@shared/models/user.model';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';
@Injectable({
  providedIn: 'root',
})
export class UserService extends AbstractService {
  public resourceUrl = '/cms/user';

  /**
   * search
   *
   * @param request IUser
   * @returns Observable<EntityResponseType<IUser[]>>
   */
  search(request: IUserSearch): Observable<EntityResponseType<IUser[]>> {
    return super.post<EntityResponseType<IUser[]>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  /**
   * searchByKeyword
   *
   * @param request IUser
   * @returns Observable<EntityResponseType<IUser[]>>
   */
  searchByKeyword(
    request: IUserSearch
  ): Observable<EntityResponseType<IUser[]>> {
    return super.post<EntityResponseType<IUser[]>>(
      `${this.resourceUrl}/search-by-keyword`,
      request
    );
  }

  /**
   * API create
   *
   * @param request IUser
   * @returns Observable<EntityResponseType<IUser>>
   */
  create(request: IUser): Observable<EntityResponseType<IUser>> {
    return super.post<EntityResponseType<IUser>>(
      `${this.resourceUrl}/create`,
      request
    );
  }

  /**
   * API update
   *
   * @param request IUser
   * @returns Observable<EntityResponseType<IUser>>
   */
  update(request: IUser): Observable<EntityResponseType<IUser>> {
    return super.post<EntityResponseType<IUser>>(
      `${this.resourceUrl}/update`,
      request
    );
  }

  /**
   * API detail
   *
   * @param request IUser
   * @returns Observable<EntityResponseType<ILoanOnline>>
   */
  detail(request: IUser): Observable<EntityResponseType<IUser>> {
    return super.post<EntityResponseType<IUser>>(
      `${this.resourceUrl}/detail`,
      request
    );
  }

  /**
   * API lock
   *
   * @param request IUser
   * @returns Observable<EntityResponseType<IUser>>
   */
  lock(request: IUser): Observable<EntityResponseType<IUser>> {
    return super.post<EntityResponseType<IUser>>(
      `${this.resourceUrl}/lock`,
      request
    );
  }

  /**
   * API unlock
   *
   * @param request IUser
   * @returns Observable<EntityResponseType<IUser>>
   */
  unlock(request: IUser): Observable<EntityResponseType<IUser>> {
    return super.post<EntityResponseType<IUser>>(
      `${this.resourceUrl}/unlock`,
      request
    );
  }

  /**
   * API delete
   *
   * @param request IUser
   * @returns Observable<EntityResponseType<ILoanOnline>>
   */
  deleteUser(request: IUser): Observable<EntityResponseType<IUser>> {
    return super.post<EntityResponseType<IUser>>(
      `${this.resourceUrl}/delete`,
      request
    );
  }

  /**
   * API resend
   *
   * @param request IActiveToken
   * @returns Observable<EntityResponseType<IActiveToken>>
   */
  resend(request: IUser): Observable<EntityResponseType<IActiveToken>> {
    return super.post<EntityResponseType<IActiveToken>>(
      `${this.resourceUrl}/resend-user-active`,
      request
    );
  }

  /**
   * API reset password
   *
   * @param request IUser
   * @returns Observable<EntityResponseType<IUser>>
   */
  resetPassword(request: IUser): Observable<EntityResponseType<IUser>> {
    return super.post<EntityResponseType<IUser>>(
      `${this.resourceUrl}/resetpw`,
      request
    );
  }
}
