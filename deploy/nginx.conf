##
# Put this file in /etc/nginx/conf.d folder and make sure
# you have a line 'include /etc/nginx/conf.d/*.conf;'
# in your main nginx configuration file
##

##
# Redirect to the same URL with https://
##

server {

  listen 80;
  listen [::]:80;
  gzip on;
  gzip_http_version 1.1;
  gzip_disable      "MSIE [1-6]\.";
  gzip_min_length   256;
  gzip_vary         on;
  gzip_proxied      expired no-cache no-store private auth;
  gzip_types        text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;
  gzip_comp_level   9;

  root /usr/share/nginx/html;
  index index.html index.htm;
  default_type text/html;

  server_name _;

  location / {
    try_files $uri $uri/ /index.html;
  }

 location ~* \.(?:css|js)$ {
   access_log        off;
   log_not_found     off;

   etag on;
 }

   location ~* \.(?:jpg|jpeg|gif|png|ico|xml|svg)$ {
        access_log        off;
        log_not_found     off;

        expires           5m;
        add_header        Cache-Control "public";
    }
   location ~* \.(?:eot|woff|woff2|ttf|otf) {
       access_log        off;
       log_not_found     off;

       expires           5m;
#        add_header        Cache-Control "public";
       add_header        Access-Control-Allow-Origin *;

       types     {font/opentype otf;}
       types     {application/vnd.ms-fontobject eot;}
       types     {font/truetype ttf;}
       types     {application/font-woff woff;}
       types     {font/x-woff woff2;}
   }

}

##
# HTTPS configurations
##

# server {

#   listen 443 ssl;

# # Type your domain name below
#   server_name example.com;

# # Configure the Certificate and Key you got from your CA (e.g. Lets Encrypt)
#   ssl_certificate     /path/to/certificate.crt;
#   ssl_certificate_key /path/to/server.key;

#   ssl_session_timeout 1d;
#   ssl_session_cache shared:SSL:50m;
#   ssl_session_tickets off;

# # Only use TLS v1.2 as Transport Security Protocol
#   ssl_protocols TLSv1.2;

# # Only use ciphersuites that are considered modern and secure by Mozilla
#   ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-SHA384:ECDHE-RSA-AES256-SHA384:ECDHE-ECDSA-AES128-SHA256:ECDHE-RSA-AES128-SHA256';

# # Do not let attackers downgrade the ciphersuites in Client Hello
# # Always use server-side offered ciphersuites
#   ssl_prefer_server_ciphers on;

# # HSTS (ngx_http_headers_module is required) (15768000 seconds = 6 months)
#   add_header Strict-Transport-Security max-age=15768000;

# # Diffie-Hellman parameter for DHE ciphersuites, recommended 2048 bits
# # Uncomment if you want to use your own Diffie-Hellman parameter, which can be generated with: openssl ecparam -genkey -out dhparam.pem -name prime256v1
# # See https://wiki.mozilla.org/Security/Server_Side_TLS#DHE_handshake_and_dhparam
# # ssl_dhparam /path/to/dhparam.pem;


# ## OCSP Configuration START
# # If you want to provide OCSP Stapling, you can uncomment the following lines
# # See https://www.digitalocean.com/community/tutorials/how-to-configure-ocsp-stapling-on-apache-and-nginx for more infos about OCSP and its use case
# # fetch OCSP records from URL in ssl_certificate and cache them

# #ssl_stapling on;
# #ssl_stapling_verify on;

# # verify chain of trust of OCSP response using Root CA and Intermediate certs (you will get this file from your CA)
# #ssl_trusted_certificate /path/to/root_CA_cert_plus_intermediates;

# ## OCSP Configuration END

# # To let nginx use its own DNS Resolver
# # resolver <IP DNS resolver>;


# # Always serve index.html for any request
#   location / {
#     # Set path
#     root /var/www/;
#     try_files $uri /index.html;
#   }

# # Do not cache sw.js, required for offline-first updates.
#   location /sw.js {
#       add_header Cache-Control "no-cache";
#       proxy_cache_bypass $http_pragma;
#       proxy_cache_revalidate on;
#       expires off;
#       access_log off;
#   }

##
# If you want to use Node/Rails/etc. API server
# on the same port (443) config Nginx as a reverse proxy.
# For security reasons use a firewall like ufw in Ubuntu
# and deny port 3000/tcp.
##

# location /api/ {
#
#   proxy_pass http://localhost:3000;
#   proxy_http_version 1.1;
#   proxy_set_header X-Forwarded-Proto https;
#   proxy_set_header Upgrade $http_upgrade;
#   proxy_set_header Connection 'upgrade';
#   proxy_set_header Host $host;
#   proxy_cache_bypass $http_upgrade;
#
# }

# }
