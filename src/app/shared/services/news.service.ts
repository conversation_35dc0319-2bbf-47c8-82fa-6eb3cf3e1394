import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { INews } from '@shared/models/news.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import CommonUtils from '@shared/utils/common-utils';
import { Observable } from 'rxjs/internal/Observable';
import { AbstractDomainService } from './common/abstract.domain.service';
import { EntityResponseType } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class NewsService extends AbstractDomainService<INews> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.NEWS;

  create(request: INews): Observable<EntityResponseType<INews>> {
    const body = CommonUtils.appendObjectToFormData(request, new FormData());
    return super.post<EntityResponseType<INews>>(`/create`, body);
  }

  update(request: INews): Observable<EntityResponseType<INews>> {
    const body = CommonUtils.appendObjectToFormData(request, new FormData());
    return super.post<EntityResponseType<INews>>(`/update`, body);
  }

  detail(request: INews): Observable<EntityResponseType<INews>> {
    return super.post<EntityResponseType<INews>>(`/info`, request);
  }

  lock(request: INews): Observable<EntityResponseType<INews>> {
    return super.post<EntityResponseType<INews>>(`/lock`, request);
  }

  unlock(request: INews): Observable<EntityResponseType<INews>> {
    return super.post<EntityResponseType<INews>>(`/unlock`, request);
  }

  getIcon(search: INews, loading = false, ignoreError = true): any {
    return super.postFile(`/image`, search, {
      loading,
      ignoreError,
    });
  }
}
