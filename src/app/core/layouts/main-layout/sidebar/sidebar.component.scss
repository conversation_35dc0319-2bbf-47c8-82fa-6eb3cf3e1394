.icon-hoa {
  position: absolute;
  bottom: 0px;
  right: 0px;
  opacity: 0.3;
  z-index: -1;
}

.sidebar-dark-primary .nav-item > .nav-link.active,
.sidebar-light-primary .nav-item > .nav-link.active {
  background-color: #f2f7ff !important;
  box-shadow: 0 1px 3px rgb(0 0 0 / 6%), 0 1px 1px rgb(0 0 0 / 14%) !important;
  color: #178cc6 !important;
}

.xxx {
  display: flex !important;
  white-space: break-spaces !important;
  line-height: 30px;
  padding: 7px;
  cursor: pointer;
}

.sidebar .item-menu span p {
  white-space: nowrap;
}

.item-menu {
  position: relative;
}

.nav-treeview {
  display: none !important;
  padding-left: 25px;
  padding-right: 20px;
}

.menu-open .nav-treeview {
  display: block !important;
  // padding-left: 20px;
}

.item-menu .active,
.nav-pills .xxx.active {
  background-color: #f2f7ff !important;
  box-shadow: 0 1px 3px #0000000f, 0 1px 1px #00000024 !important;
  color: #178cc6 !important;
  cursor: pointer;
}

.nav-sidebar .nav-treeview > .nav-item > .nav-link > .nav-icon {
  width: auto !important;
}

.nav-pills .nav-link {
  color: white;
}

.item-menu .nav-icon {
  font-size: 1.2rem;
}

.item-color {
  color: white;
}
.pointer {
  cursor: pointer;
}
