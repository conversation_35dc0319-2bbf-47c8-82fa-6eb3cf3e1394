import { Component, OnInit } from '@angular/core';
import { <PERSON>bstractControl, FormBuilder, Validators } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  CALCULATE_MILLISECONDS_IN_A_YEAR,
  DATE_RANGE_STATISTICAL_LOAN_ONLINE,
  ENTITY_STATUS,
  EVENT_STATUS,
  EVENT_STATUS_CONST,
  EVENT_STATUS_MAP,
  EVENT_TYPE,
  LIMIT_LENGTH_WORD_CONST,
  MAX_DATE_CONST,
  MODAL_ACTION,
  MOMENT_CONST,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { IEvent } from '@shared/models/event.model';
import { IEventSearch } from '@shared/models/request/event.search';
import { EventService } from '@shared/services/event.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';

@Component({
  selector: 'app-notification-management',
  templateUrl: './notification-management.component.html',
  styleUrls: ['./notification-management.component.scss'],
})
export class NotificationManagementComponent implements OnInit {
  events: IEvent[] = [];

  ROUTER_UTILS = ROUTER_UTILS;

  EVENT_STATUS_MAP = EVENT_STATUS_MAP;

  EVENT_TYPE = EVENT_TYPE;

  isErrorStartDateGreaterEndDate = false;

  maxDate = new Date();
  maxToDate = new Date();

  // input search
  eventSearch: IEventSearch = {};
  pageSizeOptions = PAGINATION.OPTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  LIMIT_LENGTH_WORD_CONST = LIMIT_LENGTH_WORD_CONST;
  MAX_DATE_CONST = MAX_DATE_CONST;

  action: any = '';
  recordSelected: any = [];

  ENTITY_STATUS = ENTITY_STATUS;

  EVENT_STATUS = EVENT_STATUS;

  storage: any;

  // default form search
  formEventSearch = this.fb.group({
    title: '',
    fromTime: [null, [Validators.required]],
    toTime: [null, [Validators.required]],
    eventStatus: null,
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
  });

  constructor(
    private eventService: EventService,
    private fb: FormBuilder,
    private router: Router,
    private translateService: TranslateService,
    private modalService: ModalService,
    private toastService: ToastrCustomService
  ) {}

  /**
   * Init data
   */
  ngOnInit(): void {
    // set default value start date and end date
    const startDate = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDate = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.eventSearch.fromTime = startDate;
    this.eventSearch.toTime = endDate;
    this.formEventSearch.controls.fromTime.setValue(
      CommonUtils.reverseDate(startDate)
    );
    this.formEventSearch.controls.toTime.setValue(
      CommonUtils.reverseDate(endDate)
    );
    this.onSearch();
  }

  onChangeValidDate(): void {
    if (this.formEventSearch.controls['toTime'].value) {
      this.maxDate = this.formEventSearch.controls['toTime'].value;
    }
  }

  /**
   * Change page: next page and preview page
   *
   * @param page PageEvent
   */
  onChangePage(page: PageEvent) {
    this.formEventSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formEventSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  /**
   * search when submit
   */
  onSearchSubmit() {
    this.formEventSearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.formEventSearch.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    this.onSearch();
  }

  getEventStatus(eventStatus: any): string {
    if (eventStatus) {
      return EVENT_STATUS_MAP[eventStatus] + '';
    }
    return '';
  }

  getEventBadge(eventStatus: any) {
    if (eventStatus) {
      return EVENT_STATUS_CONST[eventStatus]?.style;
    }
    return '';
  }

  /**
   * search form
   */
  onSearch(): void {
    const body = this.formEventSearch.value;
    if (this.formEventSearch.valid) {
      this.eventService.search(body).subscribe((res: any): void => {
        this.events = res.body.content;
        this.formEventSearch.controls.length.setValue(res.body.totalElements);
      });
    }
  }

  /**
   * index on list event
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formEventSearch.value.pageIndex,
      this.formEventSearch.value.pageSize
    );
  }

  /**
   * button click detail
   *
   * @param eventId number
   */
  detail(eventId?: number): void {
    this.router.navigate([
      ROUTER_UTILS.notification.root,
      eventId,
      ROUTER_ACTIONS.detail,
    ]);
  }

  /**
   * reset form
   */
  onReset(): void {
    // set default value date and reset data
    const startDateReset = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDateReset = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.formEventSearch.controls.eventStatus.reset();
    this.formEventSearch.controls.title.reset();
    this.formEventSearch.controls.fromTime.setValue(
      CommonUtils.reverseDate(startDateReset)
    );
    this.formEventSearch.controls.toTime.setValue(
      CommonUtils.reverseDate(endDateReset)
    );
  }

  /**
   * delete
   *
   * @param IEvent
   */
  delete(event: IEvent): void {
    // open modal
    const modalData = {
      title: 'event.deleteEvent',
      content: 'event.deleteContent',
      interpolateParams: { title: `<b>${event?.title || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.eventService
          .delete({ id: event.eventId })
          .subscribe((res: any) => {
            this.toastService.success('common.success');
            this.onSearch();
          });
      }
    });
  }

  changeEndDate() {
    if (this.formEventSearch.controls['toTime'].value) {
      this.maxDate = this.formEventSearch.controls['toTime'].value;
    }

    const dataSearch = this.formEventSearch.getRawValue();

    if (dataSearch.fromTime && dataSearch.toTime) {
      const startDateSearch = new Date(dataSearch.fromTime);
      const endDateSearch = new Date(dataSearch.toTime);

      this.isErrorStartDateGreaterEndDate =
        startDateSearch.getTime() > endDateSearch.getTime();
    }
  }

  dateValidator(value: number): any {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return value > DATE_RANGE_STATISTICAL_LOAN_ONLINE
        ? { invalidDate: true }
        : null;
    };
  }

  changeValidDate() {
    if (
      this.formEventSearch.controls.fromTime.value &&
      this.formEventSearch.controls.toTime.value
    ) {
      if (this.formEventSearch.controls['toTime'].value) {
        this.maxDate = this.formEventSearch.controls['toTime'].value;
      }
      const startDate = CommonUtils.setStartTime(
        new Date(this.formEventSearch.controls.fromTime.value).getTime()
      );
      const endDate = CommonUtils.setStartTime(
        new Date(this.formEventSearch.controls.toTime.value).getTime()
      );
      const dayMax =
        (endDate - startDate) / CALCULATE_MILLISECONDS_IN_A_YEAR.DAY;
      if (dayMax > DATE_RANGE_STATISTICAL_LOAN_ONLINE) {
        this.formEventSearch.controls.fromTime.setValidators([
          Validators.required,
          this.dateValidator(dayMax),
        ]);
        this.formEventSearch.controls.fromTime.updateValueAndValidity();
      } else {
        this.formEventSearch.controls.fromTime.clearValidators();
        this.formEventSearch.controls.fromTime.setValidators([
          Validators.required,
        ]);
        this.formEventSearch.controls.fromTime.updateValueAndValidity();
      }
    }
    this.changeEndDate();
  }

  getContentHTML(content?: string): string {
    if (content) {
      return CommonUtils.stripHTML(content);
    }
    return '';
  }
}
