<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formSearch" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-md-6 col-lg-3">
            <div class="form-group">
              <label>{{ "model.event.title" | translate }}</label>
              <input
                trim
                type="text"
                placeholder="{{ 'model.event.title' | translate }}"
                formControlName="title"
                class="w-100"
                class="form-control"
              />
            </div>
          </div>
          <div class="col-md-6 col-lg-3">
            <div class="form-group">
              <label>{{ "model.news.categories" | translate }}</label>
              <ng-select
                appearance="outline"
                [searchable]="false"
                placeholder="{{ 'model.news.categories' | translate }}"
                [clearable]="false"
                formControlName="categoryId"
              >
                <ng-option
                  [value]="item?.categoryId"
                  *ngFor="let item of categoryList"
                >
                  {{ item?.name }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-8 col-lg-4">
            <div class="date-picker-container form-group row">
              <div class="col-md-6">
                <label
                  >{{ "common.action.fromDate" | translate
                  }}<span class="text-danger">*</span></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="startDate"
                    formControlName="startDate"
                    placeholder="DD/MM/YYYY"
                    [max]="maxDate | date : 'yyyy-MM-dd'"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="startDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #startDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formSearch.get('startDate')?.errors?.required &&
                    formSearch.get('startDate')?.touched
                  "
                >
                  {{ "error.required.fromDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="isErrorStartDateGreaterEndDate"
                >
                  {{ "error.toDateMustGreatherFromDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="formSearch.get('startDate')?.errors?.invalidDate"
                >
                  {{ "error.required.inValidDate" | translate }}
                </small>
              </div>
              <div class="col-md-6">
                <label
                  >{{ "common.action.toDate" | translate
                  }}<span class="text-danger">*</span></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="endDate"
                    formControlName="endDate"
                    placeholder="DD/MM/YYYY"
                    min="{{
                      formSearch.controls['startDate'].value
                        | date : 'yyyy-MM-dd'
                    }}"
                    [max]="MAX_DATE_CONST"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="endDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #endDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formSearch.get('endDate')?.errors?.required &&
                    formSearch.get('endDate')?.touched
                  "
                >
                  {{ "error.required.toDate" | translate }}
                </small>
              </div>
            </div>
          </div>
          <div class="col-lg-2 col-md-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
            >
              <div class="btn-reset">
                <i
                  class="bi bi-arrow-clockwise"
                  type="button"
                  (click)="onReset()"
                >
                </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" type="submit">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-3 mt-4">
          <button
            type="button"
            [routerLink]="['create']"
            [routerLinkActive]="['active']"
            class="btn btn-red"
            *hasPrivileges="SYSTEM_RULES.NEWS_CREATE"
          >
            {{ "common.action.create" | translate }}
          </button>
        </div>
      </form>
      <div></div>
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th class="text-center">
                {{ "model.news.no" | translate }}
              </th>
              <th class="text-center">
                {{ "model.news.categories" | translate }}
              </th>
              <th class="text-center">
                {{ "model.news.title" | translate }}
              </th>
              <th class="text-center">
                {{ "model.news.content" | translate }}
              </th>
              <th class="text-center">
                {{ "common.createdDate" | translate }}
              </th>
              <th class="text-center">
                {{ "common.lastModifiedDate" | translate }}
              </th>
              <th class="text-center">
                {{ "common.status" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of data; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td class="text-left">
                {{ CATEGORY_MAP[item?.category?.name || ""].label | translate }}
              </td>
              <td class="text-left">
                {{ item?.newsContent?.title }}
              </td>
              <td class="text-left">
                <span
                  title="{{ getContentHTML(item?.newsContent.content) }}"
                  [innerHTML]="
                    item?.newsContent.content
                      | limitWord : LIMIT_LENGTH_WORD_CONST.MEDIUM
                  "
                ></span>
              </td>
              <td class="text-center">
                {{ item?.createdDate }}
              </td>
              <td class="text-center">
                {{ item?.lastModifiedDate }}
              </td>
              <td class="text-center">
                <span
                  class="badge"
                  [ngClass]="ENTITY_STATUS_MAP[item.status || 0].style"
                  >{{
                    ENTITY_STATUS_MAP[item.status || 0].label | translate
                  }}</span
                >
              </td>
              <td class="text-center">
                <button
                  ngbTooltip="{{ 'common.action.detail' | translate }}"
                  class="btn px-1 py-0"
                  (click)="
                    onDetailAndUpdate(item?.newsId, ROUTER_ACTIONS.detail)
                  "
                  *hasPrivileges="SYSTEM_RULES.NEWS_READ"
                >
                  <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                </button>
                <button
                  ngbTooltip="{{ 'common.action.update' | translate }}"
                  class="btn px-1 py-0"
                  (click)="
                    onDetailAndUpdate(item?.newsId, ROUTER_ACTIONS.update)
                  "
                  *hasPrivileges="SYSTEM_RULES.NEWS_WRITE"
                >
                  <i class="fa fa-edit mb-color" aria-hidden="true"></i>
                </button>
                <button
                  [ngbTooltip]="
                    (item?.status === ENTITY_STATUS_CONST.ACTIVE.code
                      ? 'common.action.lock'
                      : 'common.action.unlock'
                    ) | translate
                  "
                  class="btn px-1 py-0"
                  (click)="lockAndUnlock(item)"
                  *hasPrivileges="
                    item.status === 1
                      ? SYSTEM_RULES.NEWS_LOCK
                      : SYSTEM_RULES.NEWS_UNLOCK
                  "
                >
                  <i
                    [className]="
                      item?.status === 1
                        ? 'fa fa-lock mb-color'
                        : 'fa fa-unlock mb-color'
                    "
                    aria-hidden="true"
                  ></i>
                </button>
                <button
                  ngbTooltip="{{ 'common.action.delete' | translate }}"
                  class="btn px-1 py-0"
                  (click)="onDelete(item?.newsId)"
                  *hasPrivileges="SYSTEM_RULES.NEWS_DELETE"
                >
                  <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="row d-block text-center m-0" *ngIf="data?.length === 0">
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
        <div *ngIf="data?.length" class="paginator col-md-12">
          <mat-paginator
            [length]="formSearch.value.length"
            [pageSize]="formSearch.value.pageSize"
            [pageIndex]="formSearch.value.pageIndex"
            [pageSizeOptions]="pageSizeOptions"
            (page)="onChangePage($event)"
            aria-label="Select page"
          >
          </mat-paginator>
        </div>
      </div>
    </div>
  </div>
</section>
