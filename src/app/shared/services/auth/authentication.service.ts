import { HttpClient, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { SUB_ADMIN_SUFFIX } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { IAccount } from '@shared/models/account.model';
import { ROUTER_UTILS } from '@shared/utils';
import { LocalStorageService } from 'ngx-webstorage';
import { Observable, ReplaySubject } from 'rxjs';
import { AbstractService } from '../common/abstract.service';

import { AuthProviderService } from './auth-provider.service';

@Injectable({ providedIn: 'root' })
export class AuthenticationService extends AbstractService {
  public userIdentity: IAccount | null = null;
  private authenticationState = new ReplaySubject<IAccount | null>(1);

  constructor(
    protected http: HttpClient,
    private authProviderService: AuthProviderService,
    private router: Router,
    private localStorage: LocalStorageService
  ) {
    super(http);
  }

  fetch(): Observable<HttpResponse<IAccount>> {
    return super.post<IAccount>('/account/info', {});
  }

  authenticate(identity: IAccount | null): void {
    this.userIdentity = identity;
    this.authenticationState.next(this.userIdentity);
  }

  // isAdministrator(userIdentity: any) {
  //   if (!userIdentity || !userIdentity.privileges) {
  //     return false;
  //   }
  //   return userIdentity.privileges.includes(SYSTEM_ROLE.SUPER_ADMIN);
  // }

  hasAnyAuthority(authorities: string[]): boolean {
    const userIdentity =
      this.userIdentity ?? this.localStorage.retrieve(STORAGE_APP.USER_INFO);
    if (!userIdentity || !userIdentity?.privileges) {
      return false;
    }
    // if (this.isAdministrator(userIdentity)) {
    //   return true;
    // }
    for (const authority of authorities) {
      if (userIdentity.privileges.includes(authority)) {
        return true;
      }
    }
    return false;
  }

  hasAnyPrivileges(privileges: string[] | string): boolean {
    const auth = !Array.isArray(privileges) ? [privileges] : privileges;
    return (
      this.hasAnyAuthority(auth) || this.hasAnyAdministratorPrivilege(auth)
    );
  }

  hasAnyAdministratorPrivilege(privileges: string[]): boolean {
    const _privileges: string[] = [];
    privileges?.forEach((privile) => {
      const privilege = privile.split('_');
      if (privilege && privilege?.length > 0) {
        _privileges.push(`${privilege[0]}_${SUB_ADMIN_SUFFIX}`);
      }
    });
    return this.hasAnyAuthority(_privileges);
  }

  identity(force?: boolean, activeRoute?: string): Promise<IAccount | any> {
    if (force) {
      this.userIdentity = null;
    }

    if (this.userIdentity) {
      if (
        this.userIdentity?.changePwRequired &&
        activeRoute !== ROUTER_UTILS.auth.changePasswordExpire
      ) {
        this.router.navigate([ROUTER_UTILS.auth.changePasswordExpire]);
      }
      return Promise.resolve(this.userIdentity);
    }

    return this.fetch()
      .toPromise()
      .then((response) => {
        const account = response.body;
        if (
          account?.changePwRequired &&
          activeRoute !== ROUTER_UTILS.auth.changePasswordExpire
        ) {
          this.router.navigate([ROUTER_UTILS.auth.changePasswordExpire]);
        }
        if (account) {
          this.userIdentity = account;
        } else {
          this.userIdentity = null;
        }
        this.authenticationState.next(this.userIdentity);
        return this.userIdentity;
      })
      .catch(() => {
        this.userIdentity = null;
        this.authenticationState.next(this.userIdentity);
        this.authProviderService.logout();
        return null;
      });
  }

  isAuthenticated(): boolean {
    return !!this.userIdentity;
  }

  getAuthenticationState(): Observable<IAccount | null> {
    return this.authenticationState.asObservable();
  }

  // hasPrivilege(privilege: string): boolean {
  //   return this.isAdministrator() || this.hasAdministratorPrivilege(privilege) || this.hasAuthority(privilege);
  // }

  // hasAdministratorPrivilege(privilege: string): boolean {
  //   const privileges = privilege.split('_');
  //   if (!privileges || privileges?.length === 0) {
  //     return false;
  //   }
  //   return this.hasAuthority(`${privileges[0]}_${SUB_ADMIN_SUFFIX}`);
  // }

  // hasAuthority(authority: string): boolean {
  //   if (!this.userIdentity || !this.userIdentity?.privileges) {
  //     return false;
  //   }

  //   return this.userIdentity.privileges.includes(authority);
  // }

  // identity(force?: boolean): Observable<IAccount | null> {
  //   // tslint:disable-next-line:no-debugger
  //   debugger;
  //   if (force) {
  //     this.userIdentity = null;
  //   }
  //   if (this.userIdentity) {
  //     return of(this.userIdentity);
  //   }
  //   this.fetch().subscribe(res => {
  //       const account = res.body;
  //       if (account) {
  //         this.userIdentity = account;
  //       } else {
  //         this.userIdentity = null;
  //       }
  //       this.authenticationState.next(this.userIdentity);
  //       return of(this.userIdentity);
  //     },
  //     () => {
  //       this.userIdentity = null;
  //       this.authenticationState.next(this.userIdentity);
  //       this.authProviderService.logout();
  //       return null;
  //     }
  //   );
  //   return of(this.userIdentity);
  // }
}
