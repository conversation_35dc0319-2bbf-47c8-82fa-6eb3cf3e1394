<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formPositionSearch" (submit)="onSearchSubmit()">
        <div class="row">
          <!-- <div class="col-md-3">
            <div class="form-group">
              <label>{{ "model.position.positionName" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="positionName"
                class="w-100"
                class="form-control"
              />
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label>{{ "model.position.shortName" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="shortName"
                class="w-100"
                class="form-control"
              />
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label>{{ "model.position.positionCode" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="positionCode"
                class="w-100"
                class="form-control"
              />
            </div>
          </div> -->
          <div class="col-lg-3 col-md-4">
            <div class="form-group">
              <label>{{ "common.action.searchKeyword" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="keyword"
                class="w-100"
                class="form-control"
                placeholder="{{ 'common.action.searchKeyword' | placeholder }}"
              />
            </div>
          </div>
          <div class="col-lg-3 col-md-4">
            <div class="form-group">
              <label>{{ "common.status" | translate }}</label>
              <ng-select
                appearance="outline"
                [searchable]="false"
                placeholder="{{ 'common.appSelectOption.status' | translate }}"
                [clearable]="true"
                formControlName="status"
              >
                <ng-option
                  [value]="item.code"
                  *ngFor="let item of ENTITY_STATUS"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
            >
              <div class="btn-reset">
                <i
                  class="bi bi-arrow-clockwise"
                  type="button"
                  (click)="onReset()"
                >
                </i>
              </div>
            </div>
            <div class="">
              <button
                class="btn btn-search mr-2"
                (click)="onSearch()"
                *hasPrivileges="SYSTEM_RULES.POSITION_READ"
              >
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-3 mt-4">
          <button
            class="btn btn-red"
            (click)="onCreate()"
            *hasPrivileges="SYSTEM_RULES.POSITION_CREATE"
          >
            {{ "common.action.create" | translate }}
          </button>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th class="text-center" scope="col">
                {{ "common.no" | translate }}
              </th>
              <th scope="col">
                {{ "model.position.positionName" | translate }}
              </th>
              <th scope="col">
                {{ "model.position.shortName" | translate }}
              </th>
              <th scope="col">
                {{ "model.position.positionCode" | translate }}
              </th>
              <th scope="col">
                {{ "model.position.description" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.status" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of positions; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td>
                <span title="{{ dataItem.positionName }}">{{
                  dataItem.positionName | limitWord
                }}</span>
              </td>
              <td>
                <span title="{{ dataItem.shortName }}">{{
                  dataItem.shortName | limitWord
                }}</span>
              </td>
              <td>
                <span title="{{ dataItem.positionCode }}">{{
                  dataItem.positionCode | limitWord
                }}</span>
              </td>
              <td>
                <span title="{{ dataItem.description }}">{{
                  dataItem.description | limitWord
                }}</span>
              </td>
              <td class="text-center">
                <!-- {{ ENTITY_STATUS_MAP[dataItem.status || 0] | translate }} -->
                <span
                  class="badge"
                  [ngClass]="ENTITY_STATUS_MAP[dataItem.status || 0].style"
                  >{{
                    ENTITY_STATUS_MAP[dataItem.status || 0].label | translate
                  }}</span
                >
              </td>
              <td class="text-center">
                <!-- <button
                  *ngIf="
                    dataItem.actionToken ===
                    ACTION_TOKEN_CONST.RESEND_LINK_ACTIVATE
                  "
                  class="btn px-1 py-0"
                  (click)="resend(dataItem)"
                > -->
                <button
                  ngbTooltip="{{ 'common.action.detail' | translate }}"
                  class="btn px-1 py-0"
                  (click)="detail(dataItem.positionId)"
                  *hasPrivileges="SYSTEM_RULES.POSITION_READ"
                >
                  <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                </button>
                <button
                  ngbTooltip="{{ 'common.action.update' | translate }}"
                  class="btn px-1 py-0"
                  (click)="edit(dataItem.positionId)"
                  data-toggle="modal"
                  *hasPrivileges="SYSTEM_RULES.POSITION_WRITE"
                >
                  <i class="fa fa-edit mb-color" aria-hidden="true"></i>
                </button>
                <button
                  [ngbTooltip]="
                    (dataItem.status === ENTITY_STATUS_CONST.ACTIVE.code
                      ? 'common.action.lock'
                      : 'common.action.unlock'
                    ) | translate
                  "
                  class="btn px-1 py-0"
                  (click)="lockAndUnlock(dataItem)"
                  *hasPrivileges="
                    dataItem.status === 1
                      ? SYSTEM_RULES.POSITION_LOCK
                      : SYSTEM_RULES.POSITION_UNLOCK
                  "
                >
                  <i
                    [className]="
                      dataItem.status === 1
                        ? 'fa fa-lock mb-color'
                        : 'fa fa-unlock mb-color'
                    "
                    aria-hidden="true"
                  ></i>
                </button>
                <!--*ngIf="dataItem.approvalStatus === 1 && (dataItem.approvalType === 1 || dataItem.approvalType === 2)"-->
                <button
                  ngbTooltip="{{ 'common.action.delete' | translate }}"
                  class="btn px-1 py-0"
                  (click)="delete(dataItem)"
                  *hasPrivileges="SYSTEM_RULES.POSITION_DELETE"
                >
                  <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        <div
          class="row d-block text-center m-0"
          *ngIf="positions?.length === 0"
        >
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
        <div *ngIf="positions.length">
          <mat-paginator
            [length]="formPositionSearch.value.length"
            [pageSize]="formPositionSearch.value.pageSize"
            [pageIndex]="formPositionSearch.value.pageIndex"
            [pageSizeOptions]="pageSizeOptions"
            (page)="onChangePage($event)"
            aria-label="Select page"
          >
          </mat-paginator>
        </div>
      </div>
    </div>
  </div>
</section>
