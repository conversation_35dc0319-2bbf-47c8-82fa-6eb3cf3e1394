import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { ActivatedRoute, Router } from '@angular/router';
import { FeeRateCreateUpdateComponent } from '@business/fee-management/fee-rate/fee-rate-create-update/fee-rate-create-update.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  CONFIGURATION_FEE_TYPE_CONST,
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  MODAL_ACTION,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { IFeeRate } from '@shared/models/IFeeRate.model';
import { IFeeRateSearch } from '@shared/models/request/fee-rate.search';
import { FeeRateService } from '@shared/services/fee-rate.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import { VALIDATORS } from '@shared/constants/validators.constant';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-fee-rate',
  templateUrl: './fee-rate.component.html',
  styleUrls: ['./fee-rate.component.scss'],
})
export class FeeRateComponent implements OnInit {
  @Input()
  feeId: number | undefined;

  @Input()
  configurationFeeType: string | undefined;

  @Input()
  transactionFeeTypeId: number | undefined;

  CONFIGURATION_FEE_TYPE = CONFIGURATION_FEE_TYPE_CONST;
  VALIDATORS = VALIDATORS;

  feeRates: IFeeRate[] = [];

  ROUTER_UTILS = ROUTER_UTILS;

  ROUTER_ACTIONS = ROUTER_ACTIONS;

  // input search
  feeRateSearch: IFeeRateSearch = {};
  totalRecord = 0;
  pageSize = PAGINATION.PAGE_SIZE_DEFAULT;
  pageIndex = PAGINATION.PAGE_NUMBER_DEFAULT;
  pageSizeOptions = PAGINATION.OPTIONS;
  SYSTEM_RULES = SYSTEM_RULES;

  action = '';
  recordSelected: any = [];
  pageEvent: PageEvent = {
    length: this.totalRecord,
    pageIndex: this.pageIndex,
    pageSize: this.pageSize,
    // previousPageIndex: 0,
  };

  ENTITY_STATUS = ENTITY_STATUS;

  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;

  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;

  storage: any;

  // default form search
  formFeeSearch = this.fb.group({
    keyword: '',
    configurationFeeType: '',
    status: null,
  });

  constructor(
    private feeRateService: FeeRateService,
    private fb: FormBuilder,
    private modalServiceOpen: NgbModal,
    private router: Router,
    private translateService: TranslateService,
    private modalService: ModalService,
    private toastService: ToastrCustomService,
    private activatedRoute: ActivatedRoute
  ) {
    this.activatedRoute.data.subscribe((res) => {
      this.action = res.action;
    });
  }

  /**
   * Init data
   */
  ngOnInit(): void {
    this.loadData(this.pageEvent);
  }

  /**
   * load list data
   *
   * @param page PageEvent
   */
  loadData(page: PageEvent): void {
    this.feeRateSearch.pageIndex = page.pageIndex;
    this.feeRateSearch.pageSize = page.pageSize;
    this.feeRateSearch.feeId = this.feeId;
    this.feeRateSearch.keyword = this.formFeeSearch.controls['keyword'].value;
    this.feeRateService
      .search(this.feeRateSearch)
      .subscribe((res: any): void => {
        if (res.body) {
          this.feeRates = res.body.content;
          this.totalRecord = res.body.totalElements;
        }
      });
  }

  /**
   * Change page: next page and preview page
   *
   * @param page PageEvent
   */
  onChangePage(page: PageEvent) {
    this.pageIndex = page.pageIndex;
    this.pageSize = page.pageSize;
    this.loadData(page);
  }

  /**
   * search form
   */
  onSearch(): void {
    const body = this.formFeeSearch.getRawValue();
    this.feeRateSearch.keyword = body.keyword;
    this.pageEvent.pageSize = PAGINATION.PAGE_SIZE_DEFAULT;
    this.pageEvent.pageIndex = PAGINATION.PAGE_NUMBER_DEFAULT;
    this.loadData(this.pageEvent);
  }

  /**
   * index on list event
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(index, this.pageIndex, this.pageSize);
  }

  /**
   * button click detail
   *
   * @param feeId number
   */
  detail(feeRate: IFeeRate): void {
    const modalRef = this.modalServiceOpen.open(FeeRateCreateUpdateComponent, {
      backdrop: 'static',
      size: 'lg',
      centered: true,
      keyboard: false,
    });

    modalRef.componentInstance.action = ROUTER_ACTIONS.detail;
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;
    modalRef.componentInstance.feeId = this.feeId;
    modalRef.componentInstance.configurationFeeType = this.configurationFeeType;
    modalRef.componentInstance.transactionFeeTypeId = this.transactionFeeTypeId;
    modalRef.componentInstance.feeRate = feeRate;

    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }

  /**
   * Cập nhật cấu hình phí
   *
   * @param feeId
   */
  update(feeRate: IFeeRate): void {
    const modalRef = this.modalServiceOpen.open(FeeRateCreateUpdateComponent, {
      backdrop: 'static',
      size: 'lg',
      centered: true,
      keyboard: false,
    });

    modalRef.componentInstance.action = ROUTER_ACTIONS.update;
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;
    modalRef.componentInstance.feeId = this.feeId;
    modalRef.componentInstance.configurationFeeType = this.configurationFeeType;
    modalRef.componentInstance.transactionFeeTypeId = this.transactionFeeTypeId;
    modalRef.componentInstance.feeRate = feeRate;

    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }

  /**
   *
   *
   * @param fee
   */
  activeAndInactive(feeRate: IFeeRate) {
    if (feeRate.status === ENTITY_STATUS_CONST.INACTIVE.code) {
      this.active(feeRate);
    } else {
      this.inactive(feeRate);
    }
  }

  /**
   * Tạo mới cấu hình phí
   *
   */
  onCreate() {
    const modalRef = this.modalServiceOpen.open(FeeRateCreateUpdateComponent, {
      backdrop: 'static',
      size: 'lg',
      centered: true,
      keyboard: false,
    });

    modalRef.componentInstance.action = ROUTER_ACTIONS.create;
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;
    modalRef.componentInstance.feeId = this.feeId;
    modalRef.componentInstance.configurationFeeType = this.configurationFeeType;
    modalRef.componentInstance.transactionFeeTypeId = this.transactionFeeTypeId;

    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }

  /**
   * Lock fee
   *
   * @param fee IFee
   */
  private inactive(feeRate: IFeeRate) {
    const modalData = {
      title: 'feeRate.inactive',
      content: 'fee.inactiveFeeContent',
      interpolateParams: { feeName: `<b>${feeRate?.feeRateName || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { id: feeRate?.feeRateId };
        this.feeRateService.lock(params).subscribe((res) => {
          this.toastService.success('common.success');
          this.onSearch();
        });
      }
    });
  }

  /**
   * UnLock IFeeRate
   *
   * @param feeRate: IFeeRate
   */
  private active(feeRate: IFeeRate) {
    const modalData = {
      title: 'feeRate.active',
      content: 'fee.activeFeeContent',
      interpolateParams: { feeName: `<b>${feeRate?.feeRateName || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { id: feeRate?.feeRateId };
        this.feeRateService.unlock(params).subscribe((res) => {
          this.toastService.success('common.success');
          this.onSearch();
        });
      }
    });
  }

  /**
   * reset form
   */
  onReset(): void {
    this.formFeeSearch.reset();
    this.formFeeSearch.controls.status.setValue('');
    this.formFeeSearch.controls.configurationFeeType.setValue('');
    this.formFeeSearch.controls.keyword.setValue('');
  }

  /**
   * delete
   *
   * @param IFee
   */
  delete(feeRate: IFeeRate): void {
    // open modal
    const modalData = {
      title: 'feeRate.delete',
      content: 'feeRate.deleteContent',
      interpolateParams: {
        feeRateName: `<b>${feeRate?.feeRateName || ''}</b>`,
      },
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.feeRateService
          .deleteFeeRate({ id: feeRate.feeRateId })
          .subscribe((res: any) => {
            this.toastService.success('common.success');
            this.onSearch();
          });
      }
    });
  }
}
