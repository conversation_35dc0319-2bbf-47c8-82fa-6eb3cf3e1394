import { SearchWithPagination } from '@shared/models/request/base.request.model';

export interface ICustomerSearch extends SearchWithPagination {
  cif?: string;
  idCardNumber?: string;
  username?: string;
  fullname?: string;
  createdDateStartAt?: string;
  createdDateEndAt?: string;
  email?: string;
  phoneNumber?: string;
  status?: number;
  approvalStatus?: number;
  approvalType?: number;
  nationCode?: string;
  provinceCode?: string;
  districtCode?: string;
  wardCode?: string;
  startAt?: string;
  endAt?: string;
  activeStatus?: number;
  customerSectorId?: number;
  accountType?: string;
}

export class CustomerSearch implements ICustomerSearch {
  constructor(
    public cif?: string,
    public idCardNumber?: string,
    public username?: string,
    public fullname?: string,
    public createdDateStartAt?: string,
    public createdDateEndAt?: string,
    public email?: string,
    public phoneNumber?: string,
    public status?: number,
    public approvalStatus?: number,
    public approvalType?: number,
    public nationCode?: string,
    public provinceCode?: string,
    public districtCode?: string,
    public wardCode?: string,
    public keyword?: string,
    public orderByType?: string,
    public orderByColumn?: string,
    public page?: number,
    public size?: number
  ) {
    this.cif = cif;
    this.idCardNumber = idCardNumber;
    this.username = username;
    this.fullname = fullname;
    this.createdDateStartAt = createdDateStartAt;
    this.createdDateEndAt = createdDateEndAt;
    this.email = email;
    this.phoneNumber = phoneNumber;
    this.status = status;
    this.approvalStatus = approvalStatus;
    this.approvalType = approvalType;
    this.nationCode = nationCode;
    this.provinceCode = provinceCode;
    this.districtCode = districtCode;
    this.wardCode = wardCode;
    this.keyword = keyword;
    this.orderByType = orderByType;
    this.orderByColumn = orderByColumn;
    this.page = page;
    this.size = size;
  }
}
