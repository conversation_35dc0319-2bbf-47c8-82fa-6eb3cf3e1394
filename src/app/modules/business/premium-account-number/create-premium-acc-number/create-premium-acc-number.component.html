<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" *ngIf="isBack" (click)="backToList()" />
      <h5>
        {{"sidebar.premiumAccountNumberHasPaymentAccount.create" | translate}}
      </h5>
    </div>
    <form [formGroup]="formCreate">
      <div class="col-md-12">
        <div class="row">
          <h2 class="title-create">
            {{"sidebar.premiumAccountNumberHasPaymentAccount.create" | translate}}
          </h2>
        </div>
        <div class="row border-create">
          <h3>{{ "model.premiumAccountNumber.info" | translate }}</h3>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label>{{ "model.customer.phoneOrFullName" | translate
                    }}<span class="text-danger">*</span></label>
                  <ng-select placeholder="{{ 'model.customer.phoneOrFullName' | placeholder : 'select' }}"
                    [searchable]="true" [clearable]="false" (keyupDelay)="loadListCustomer($event)" appDebounceKeyup
                    formControlName="phoneNumber" (change)="onChangePhoneNumber($event)">
                    <ng-option [value]="item" *ngFor="let item of customers">
                      {{ item?.username + ' - ' + item?.fullname }}
                    </ng-option>
                  </ng-select>
                  <small class="form-text text-danger noti-small" *ngIf="
                    formCreate.get('phoneNumber')?.errors?.required &&
                    formCreate.get('phoneNumber')?.touched
                  ">
                    {{
                    "error.user.required.phoneOrFullName"
                    | translate
                    }}
                  </small>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>{{ "model.customer.cif" | translate
                    }}<span class="text-danger">*</span></label>
                  <input trim formControlName="cif" type="text" class="w-100" class="form-control" placeholder="{{
                    'model.customer.cif' | translate
                  }}" />
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>{{ "model.customer.fullname" | translate
                    }}<span class="text-danger">*</span></label>
                  <input trim formControlName="fullname" type="text" class="w-100" class="form-control" placeholder="{{
                      'model.customer.fullname' | translate
                    }}" />
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label>{{ "customerRegisterManagement.customerApproval.detail.accountType" | translate
                    }}<span class="text-danger">*</span></label>
                  <ng-select placeholder="{{ 'model.customer.accountType' | placeholder : 'select' }}"
                    [searchable]="false" [clearable]="false" formControlName="type"
                    (change)="onChangePremiumAccType($event)">
                    <ng-option [value]="item.code" *ngFor="let item of PREMIUM_ACC_TYPE">
                      {{ item.label | translate }}
                    </ng-option>
                  </ng-select>
                  <small class="form-text text-danger noti-small" *ngIf="
                              formCreate.get('type')?.errors?.required &&
                              formCreate.get('type')?.touched
                            ">
                    {{
                    "premiumAccNumber.error.required.accountType"
                    | translate
                    }}
                  </small>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>{{ "sidebar.premiumAccountNumber.title" | translate
                    }}<span class="text-danger">*</span></label>


                  <input trim formControlName="premiumAccNumber" [maxLength]="
                  VALIDATORS.LENGTH.PREMIUM_ACC_NUMBER_MAX_LENGTH
                " type="text" class="w-100" numbersOnly class="form-control" placeholder="{{
                    'model.merchant.merchantAccountNumber' | placeholder
                  }}" />


                  <small class="form-text text-danger noti-small" *ngIf="
                    formCreate.get('premiumAccNumber')?.errors?.required &&
                    formCreate.get('premiumAccNumber')?.touched
                  ">
                    {{
                    "premiumAccNumber.error.required.premiumAccNumber"
                    | translate
                    }}
                  </small>
                  <small *ngIf="
                  formCreate.get('premiumAccNumber')?.errors?.pattern &&
                  formCreate.get('premiumAccNumber')?.touched
                      " class="text-danger">
                    {{
                    "premiumAccNumber.pattern.premiumAccNumber" | translate
                    }}
                  </small>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>{{ "model.structureAccount.length"| translate
                    }}</label>
                  <input trim formControlName="length" type="text" class="w-100" class="form-control" placeholder="{{
                    'model.structureAccount.length' | translate
                  }}" />
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label>{{ "model.structureAccount.originalPrice" | translate
                    }}</label>
                  <input trim formControlName="listPrice" type="text" class="w-100" class="form-control"
                    appCurrencyFormat />
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>{{ "model.structureAccount.discount" | translate
                    }}</label>
                  <input trim formControlName="discount" type="text" class="w-100" class="form-control" />
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>{{ "model.structureAccount.paymentPrice" | translate
                    }}</label>
                  <input trim formControlName="totalPrice" type="text" class="w-100" class="form-control bold-text"
                    appCurrencyFormat />
                </div>
              </div>

            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label>{{ "customerRegisterManagement.paymentAccount" | translate
                    }}<span class="text-danger">*</span></label>
                  <ng-select placeholder="{{ 'customerRegisterManagement.paymentAccount' | placeholder : 'select' }}"
                    [searchable]="false" [clearable]="false" formControlName="paymentAccNumber"
                    (change)="onChangePaymentAccount($event)">
                    <ng-option [value]="item" *ngFor="let item of paymentAccounts">
                      {{ item?.accountNumber + '' | translate }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>{{ "model.premiumAccountNumber.balance" | translate
                    }}</label>
                  <input trim formControlName="balance" type="text" class="w-100" class="form-control" placeholder="{{
                    'model.premiumAccountNumber.balance' | translate
                  }}" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
    <div class="d-block text-center mb-5 mt-4">
      <button class="btn btn-white mr-2" data-toggle="modal" *ngIf="isBack" (click)="backToList()">
        {{ "common.action.back" | translate }}
      </button>
      <ng-container *hasPrivileges="
      SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_CREATE
    ">
        <button class="btn btn-red" (click)="onResearch()">
          {{ "common.action.research" | translate }}
        </button>
        <button class="btn btn-red" (click)="onSendOtp()" [disabled]="isSendOtp()">
          {{ "common.action.sendOtp" | translate }}
        </button>
      </ng-container>
    </div>
  </div>
</section>
