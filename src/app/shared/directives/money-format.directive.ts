import { Directive, ElementRef, HostListener } from '@angular/core';

@Directive({
  selector: '[appCurrencyFormat]'
})
export class MoneyFormatDirective {
  constructor(private el: ElementRef) {}

  @HostListener('input', ['$event'])
  onInput(event: any) {
    const inputValue = this.el.nativeElement.value;
    const numericValue = inputValue.replace(/[^0-9]/g, '');
    const formattedValue = this.formatCurrency(numericValue);
    this.el.nativeElement.value = formattedValue;
    event.stopPropagation();
  }

  private formatCurrency(value: string): string {
    if (!value) { return ''; }
    const regex = /(\d)(?=(\d{3})+(?!\d))/g;
    let formatted;
    if (value === '0') {
      formatted = value.replace(regex, '$1.');
    } else {
      formatted = value.replace(/^0+/, '').replace(regex, '$1.');
    }
    return formatted;
  }
}
