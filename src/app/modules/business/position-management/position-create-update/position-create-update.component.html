<div class="modal-content">
  <!-- <div class="modal-header" [ngClass]="isHiddenBtnClose ? 'border-0' : ''">
    <h3 class="modal-title">{{title | translate}}</h3>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"
      (click)="activeModal.close(MODAL_ACTION.CLOSE.code)">
      <span aria-hidden="true">&times;</span>
    </button>
  </div> -->
  <div class="modal-header" [ngClass]="isHiddenBtnClose ? 'border-0' : ''">
    <h5 class="modal-title" *ngIf="!(action === ROUTER_ACTIONS.detail)">
      {{
        (action === ROUTER_ACTIONS.update
          ? "position.updateTitle"
          : "position.createTitle"
        ) | translate
      }}
    </h5>
    <h5 class="modal-title" *ngIf="action === ROUTER_ACTIONS.detail">
      {{ "position.titleInforPosition" | translate }}
    </h5>
    <button
      type="button"
      class="close"
      data-dismiss="modal"
      aria-label="Close"
      (click)="activeModal.close()"
    >
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <form
      [formGroup]="formPosition"
      *ngIf="
        action === ROUTER_ACTIONS.create
          ? action === ROUTER_ACTIONS.create
          : position.positionId
      "
    >
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label
                >{{ "model.position.positionName" | translate }}
                <span class="text-danger">*</span></label
              >
              <input
                trim
                placeholder="{{ 'model.position.positionName' | placeholder }}"
                formControlName="positionName"
                type="text"
                class="w-100"
                class="form-control"
                [maxLength]="VALIDATORS.LENGTH.POSITION_NAME_MAX_LENGTH"
              />
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formPosition.get('positionName')?.errors?.required &&
                  formPosition.get('positionName')?.touched
                "
              >
                {{ "error.position.required.positionName" | translate }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formPosition.get('positionName')?.errors?.pattern &&
                  formPosition.get('positionName')?.touched
                "
              >
                {{ "error.position.pattern.positionName" | translate }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formPosition.get('positionName')?.errors?.maxlength &&
                  formPosition.get('positionName')?.touched
                "
              >
                {{
                  "error.position.maxLength.positionName"
                    | translate
                      : {
                          param: VALIDATORS.LENGTH.POSITION_NAME_MAX_LENGTH
                        }
                }}
              </small>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label
                >{{ "model.position.shortName" | translate }}
                <span class="text-danger">*</span></label
              >
              <input
                trim
                placeholder="{{ 'model.position.shortName' | placeholder }}"
                formControlName="shortName"
                type="text"
                class="w-100"
                class="form-control"
                [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
              />
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formPosition.get('shortName')?.errors?.required &&
                  formPosition.get('shortName')?.touched
                "
              >
                {{ "error.position.required.shortName" | translate }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formPosition.get('shortName')?.errors?.pattern &&
                  formPosition.get('shortName')?.touched
                "
              >
                {{ "error.position.pattern.shortName" | translate }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formPosition.get('shortName')?.errors?.minlength &&
                  formPosition.get('shortName')?.touched
                "
              >
                {{
                  "error.position.minLength.shortName"
                    | translate
                      : {
                          param:
                            VALIDATORS.LENGTH.POSITION_SHORT_NAME_MIN_LENGTH
                        }
                }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formPosition.get('shortName')?.errors?.maxlength &&
                  formPosition.get('shortName')?.touched
                "
              >
                {{
                  "error.position.maxLength.shortName"
                    | translate
                      : {
                          param: VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH
                        }
                }}
              </small>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label
                >{{ "model.position.positionCode" | translate }}
                <span class="text-danger">*</span></label
              >
              <input
                trim
                placeholder="{{ 'model.position.positionCode' | placeholder }}"
                formControlName="positionCode"
                type="text"
                class="w-100"
                class="form-control"
                [maxLength]="VALIDATORS.LENGTH.POSITION_CODE_MAX_LENGTH"
              />
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formPosition.get('positionCode')?.errors?.required &&
                  formPosition.get('positionCode')?.touched
                "
              >
                {{ "error.position.required.positionCode" | translate }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formPosition.get('positionCode')?.errors?.maxlength &&
                  formPosition.get('positionCode')?.touched
                "
              >
                {{
                  "error.position.maxLength.positionCode"
                    | translate
                      : {
                          param: VALIDATORS.LENGTH.POSITION_CODE_MAX_LENGTH
                        }
                }}
              </small>
            </div>
          </div>
          <div class="col-md-6" *ngIf="action === ROUTER_ACTIONS.detail">
            <div class="form-group">
              <label>{{ "common.status" | translate }}</label>
              <ng-select
                appearance="outline"
                [searchable]="false"
                id="select-option"
                [clearable]="false"
                formControlName="status"
              >
                <ng-option
                  [value]="item.code"
                  *ngFor="let item of ENTITY_STATUS"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-12">
            <div class="form-group">
              <label>{{ "model.position.description" | translate }} </label>
              <textarea
                trim
                placeholder="{{ 'model.position.description' | placeholder }}"
                formControlName="description"
                [maxLength]="VALIDATORS.LENGTH.MEDIUM_TEXT_MAX_LENGTH"
                type="text"
                class="w-100"
                class="form-control"
                rows="4"
                cols="50"
              >
              </textarea>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formPosition.get('description')?.errors?.maxlength &&
                  formPosition.get('description')?.touched
                "
              >
                {{
                  "error.position.maxLength.description"
                    | translate
                      : {
                          param: VALIDATORS.LENGTH.MEDIUM_TEXT_MAX_LENGTH
                        }
                }}
              </small>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-12" *ngIf="action === ROUTER_ACTIONS.detail">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label>{{ "model.position.createdDate" | translate }} </label>
              <input
                trim
                [value]="position.createdDate"
                type="text"
                class="w-100"
                class="form-control"
                disabled
              />
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label>{{ "model.position.createdBy" | translate }} </label>
              <input
                trim
                formControlName="userCreated"
                type="text"
                class="w-100"
                class="form-control"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-12" *ngIf="action === ROUTER_ACTIONS.detail">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label
                >{{ "model.position.lastModifiedDate" | translate }}
              </label>
              <input
                trim
                [value]="position.lastModifiedDate"
                type="text"
                class="w-100"
                class="form-control"
                disabled
              />
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label>{{ "model.position.lastModifiedBy" | translate }} </label>
              <input
                trim
                formControlName="userLastModified"
                type="text"
                class="w-100"
                class="form-control"
              />
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="text-center pb-4">
    <button
      type="button"
      class="btn mb-btn-outline-color mr-3"
      (click)="activeModal.close()"
    >
      {{ "common.action.close" | translate }}
    </button>
    <ng-container
      *hasPrivileges="
        action === ROUTER_ACTIONS.update && !(action === ROUTER_ACTIONS.detail)
          ? SYSTEM_RULES.POSITION_WRITE
          : SYSTEM_RULES.POSITION_CREATE
      "
    >
      <button
        type="button"
        class="btn mb-btn-color"
        (click)="action === ROUTER_ACTIONS.update ? onUpdate() : onCreate()"
        *ngIf="!(action === ROUTER_ACTIONS.detail)"
      >
        {{
          (action === ROUTER_ACTIONS.update
            ? "common.action.update"
            : "common.action.create"
          ) | translate
        }}
      </button>
    </ng-container>
  </div>
</div>
