import { Component, OnInit } from '@angular/core';
import { BreadcrumbService } from '@shared/services/helpers/breadcrumb.service';

@Component({
  selector: 'app-breadcrumb',
  templateUrl: './breadcrumb.component.html',
  styleUrls: ['./breadcrumb.component.css'],
})
export class BreadcrumbComponent implements OnInit {
  public breadcrumbs: any;

  constructor(private breadCrumbService: BreadcrumbService) {}

  ngOnInit(): void {
    this.breadcrumbs = this.breadCrumbService.breadcrumbs;
    console.log(this.breadcrumbs);
  }

  trickyProcess(str: string, check: boolean): string {
    if (
      (str === 'patient.record.title-detail' ||
        str === 'declaration.medical-declaration-info.title-detail' ||
        str === 'appointment.doctor_appointment.title-detail') &&
      check
    ) {
      return str.replace('title-detail', 'title');
    } else {
      if (str.includes('admin')) {
        return str;
      }
      const str1 = str.split('.').slice(1, str.split('.').length).join('.');
      return str.replace(str1.toString(), 'title');
    }
  }
}
