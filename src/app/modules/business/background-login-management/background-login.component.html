<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <h5>{{ "model.backgroundLogin.title" | translate }}</h5>
    </div>
    <form [formGroup]="formCreate" *ngIf="formCreate">
      <div class="col-md-12">
        <div class="row">
          <h2 class="title-create">
            {{ "model.backgroundLogin.title" | translate }}
          </h2>
        </div>
        <div class="row border-create">
          <h3>{{ "model.backgroundLogin.info" | translate }}</h3>
          <div class="col-4">
            <div class="col-12">
              <label
                >{{ "model.backgroundLogin.bgImage" | translate
                }}<span class="text-danger">*</span></label
              >
              <div class="bank-upload">
                <app-upload-images
                  (imageUploaded)="onUploadPics($event)"
                  [fileRequired]="fileRequired"
                  [fileExtension]="FILE_UPLOAD_EXTENSIONS.ICON"
                  (imageRemoved)="onRemoveImage($event)"
                  [imageUrls]="imageUrls"
                  [type]="ROUTER_ACTIONS.update"
                ></app-upload-images>
              </div>
              <small
                class="form-text text-danger noti-small ml-1"
                *ngIf="
                  formCreate.get('files')?.errors?.required &&
                  formCreate.get('files')?.touched
                "
              >
                {{ "model.backgroundLogin.error.bgImage" | translate }}
              </small>
            </div>
          </div>
          <div class="col-8">
            <div class="col-md-12">
              <div class="row">
                <div class="col-md-5">
                  <div class="form-group">
                    <label
                      >{{ "model.backgroundLogin.colorCode" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      appAutoValidate
                      formControlName="colorCode"
                      type="color"
                      class="w-100"
                      class="form-control"
                      placeholder="{{
                        'model.backgroundLogin.colorCode' | placeholder
                      }}"
                    />
                  </div>
                </div>
                <div class="col-md-5">
                  <div class="form-group">
                    <label>{{
                      "model.backgroundLogin.status" | translate
                    }}</label>
                    <ng-select
                      appearance="outline"
                      [searchable]="false"
                      placeholder="{{
                        'common.status' | placeholder : 'select'
                      }}"
                      [clearable]="true"
                      formControlName="status"
                    >
                      <ng-option
                        [value]="item.code"
                        *ngFor="let item of ENTITY_STATUS"
                      >
                        {{ item.label | translate }}
                      </ng-option>
                    </ng-select>
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="
                        formCreate.get('status')?.errors?.required &&
                        formCreate.get('status')?.touched
                      "
                    >
                      {{ "model.backgroundLogin.error.status" | translate }}
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="d-block text-center mb-5 mt-4">
          <button
            class="btn btn-red"
            (click)="onSubmit()"
            *hasPrivileges="checkPermission"
          >
            {{ "common.action.update" | translate }}
          </button>
        </div>
      </div>
    </form>
  </div>
</section>
