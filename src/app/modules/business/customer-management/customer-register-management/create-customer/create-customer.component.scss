// .col-md-12 {
//   font-size: 12px;
// }

// .form-input-img {
//   border: 1px dashed #00000066;
//   border-radius: 4px;
//   height: 30px;
//   text-align: right;
//   padding-right: 12px;
//   padding-top: 5px
// }

// ::ng-deep .mat-form-field-appearance-fill .mat-form-field-flex {
//   background-color: white;
//   padding: 0px !important;
//   border-radius: 0px !important;
//   border: 1.5px solid #CECECE;
// }

// ::ng-deep .mat-form-field-underline {
//   display: none;
// }

// .mat-form-field-infix {
//   padding: 3px !important;
//   border-top: 5px solid transparent !important;
// }

// ::ng-deep .mat-form-field-infix {
//   padding: 3px !important;
//   border-top: 5px solid transparent !important;
// }

// .mat-form-field {
//   display: block;
// }

// ::ng-deep .mat-select-panel .mat-option.mat-selected:not(.mat-option-multiple),
// ::ng-deep .mat-option.mat-active {
//   background: #EB2D4B !important;
//   border-radius: 4px !important;
//   color: white;
// }

// ::ng-deep .mat-select-panel .mat-option {
//   border-bottom: 1px solid #CECECE;
// }

// ::ng-deep .mat-select-arrow-wrapper {
//   padding-top: 11px;
// }

// .btn-tc {
//   height: 34px;
//   font-size: 14px;
// }

hr {
  border-top: 1px solid #CECECE;
  height: 1px;
  width: 100%;
}

#select-option {
  height: 24px !important;
}

.footer {
  font-style: italic;
  font-weight: bold;
  // color: #e7e012;
}
