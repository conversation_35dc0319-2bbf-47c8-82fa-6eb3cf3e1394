<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formTransactionReportSearch" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-md-4 col-lg-4">
            <div class="form-group">
              <label>{{ "common.action.searchKeyword" | translate }}</label>
              <input trim type="text" formControlName="keyword" class="w-100" class="form-control"
                placeholder="{{ 'common.action.searchKeyword' | placeholder }}" />
            </div>
          </div>
          <div class="col-md-2 col-lg-2">
            <div class="form-group">
              <label>{{ "model.manageTransQuantity.transactionType" | translate }}</label>
              <ng-select placeholder="{{ 'model.manageTransQuantity.transactionType' | placeholder : 'select' }}"
                [searchable]="false" (change)="onChangeTransactionType($event)" [multiple]="true"
                formControlName="drCrs" [clearable]="false">
                <ng-option [value]="item.code" *ngFor="let item of DRCR">
                  {{ item.label | translate }}
                </ng-option>
                <ng-template ng-header-tmp>
                  <div>
                    <button class="btn btn-link" type="button" (click)="onSelectAll(SELECTED_CONST.DR_CR)">
                      {{ "common.action.selectAll" | translate }}
                    </button>
                    <button class="btn btn-link" type="button" (click)="onClearAll(SELECTED_CONST.DR_CR)">
                      {{ "common.action.clearAll" | translate }}
                    </button>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="form-group col-md-4 col-lg-3">
            <label>{{
              "model.manageTransQuantity.transQuantityLess" | translate
              }}</label>
            <input trim numbersOnly formControlName="numberOfTransactionMin" class="form-control w-100" placeholder="{{
                'model.manageTransQuantity.transQuantity' | placeholder
              }}" [maxLength]="VALIDATORS.LENGTH.TRANSACTION_REPORT_MIN" />
          </div>
          <div class="form-group col-md-4 col-lg-3">
            <label>{{
              "model.manageTransQuantity.transQuantityGreater" | translate
              }}</label>
            <input trim numbersOnly formControlName="numberOfTransactionMax" class="form-control w-100" placeholder="{{
                'model.manageTransQuantity.transQuantity' | placeholder
              }}" [maxLength]="VALIDATORS.LENGTH.TRANSACTION_REPORT_MAX" />
          </div>
          <div class="col-lg-4 col-md-8">
            <div class="date-picker-container form-group row">
              <div class="col-md-6">
                <label>{{ "common.action.fromDate" | translate
                  }}<span class="text-danger">*</span></label>
                <mat-form-field appearance="fill" class="date-picker">
                  <input matInput [matDatepicker]="fromDate" formControlName="fromDate" placeholder="DD/MM/YYYY"
                    (change)="changeValidDate()" (dateInput)="changeValidDate()" dateTransform [max]="maxDate" />
                  <mat-datepicker-toggle matSuffix [for]="fromDate"></mat-datepicker-toggle>
                  <mat-datepicker #fromDate></mat-datepicker>
                </mat-form-field>
                <small class="form-text text-danger noti-small" *ngIf="
                    formTransactionReportSearch.get('fromDate')?.errors
                      ?.required &&
                    formTransactionReportSearch.get('fromDate')?.touched
                  ">
                  {{
                  "error.manageTransQuantity.required.fromDate" | translate
                  }}
                </small>
                <small class="form-text text-danger noti-small" *ngIf="
                    formTransactionReportSearch.get('fromDate')?.errors
                      ?.invalidDate &&
                    formTransactionReportSearch.get('fromDate')?.touched
                  ">
                  {{ "error.required.inValidDate" | translate }}
                </small>
                <small class="form-text text-danger noti-small" *ngIf="
                    formTransactionReportSearch.get('fromDate')?.errors
                      ?.invalidMaxDate &&
                    formTransactionReportSearch.get('fromDate')?.touched
                  ">
                  {{
                  "error.maxDateCurrent"
                  | translate
                  : {
                  param: "common.action.fromDate" | translate
                  }
                  }}
                </small>
              </div>
              <div class="col-md-6">
                <label>{{ "common.action.toDate" | translate
                  }}<span class="text-danger">*</span></label>
                <mat-form-field appearance="fill" class="date-picker">
                  <input matInput [matDatepicker]="toDate" formControlName="toDate" placeholder="DD/MM/YYYY"
                    (change)="changeValidDate()" (dateInput)="changeValidDate()" dateTransform [max]="maxToDate" min="{{
                      formTransactionReportSearch.controls['fromDate'].value
                        | date : MOMENT_CONST.FORMAT_DATE_MAT_PICKER
                    }}" />
                  <mat-datepicker-toggle matSuffix [for]="toDate"></mat-datepicker-toggle>
                  <mat-datepicker #toDate></mat-datepicker>
                </mat-form-field>
                <small class="form-text text-danger noti-small" *ngIf="
                    formTransactionReportSearch.get('toDate')?.errors
                      ?.required &&
                    formTransactionReportSearch.get('toDate')?.touched
                  ">
                  {{ "error.manageTransQuantity.required.toDate" | translate }}
                </small>
                <small class="form-text text-danger noti-small" *ngIf="
                    formTransactionReportSearch.get('toDate')?.errors
                      ?.invalidMaxDate &&
                    formTransactionReportSearch.get('toDate')?.touched
                  ">
                  {{
                  "error.maxDateCurrent"
                  | translate
                  : {
                  param: "common.action.toDate" | translate
                  }
                  }}
                </small>
              </div>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 mt-4 d-flex">
            <div class="col-btn-reset" ngbTooltip="{{ 'common.action.reset' | translate }}">
              <div class="btn-reset">
                <i class="bi bi-arrow-clockwise" type="button" (click)="onReset()">
                </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" type="submit">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-4 mt-4">
          <button class="btn btn-red mr-2" type="button" (click)="exportFile()"
            *hasPrivileges="SYSTEM_RULES.REPORT_NUMBER_TRANSACTION_EXPORT">
            {{ "common.action.export" | translate }}
          </button>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table table-transaction">
          <thead>
            <tr>
              <th scope="col" class="text-center">
                {{ "common.no" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.manageTransQuantity.userName" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{ "model.manageTransQuantity.cif" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{ "model.manageTransQuantity.transDebitSuccess" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{ "model.manageTransQuantity.transCreditSuccess" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of transactionReport; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td class="text-left">{{ dataItem.fullname }}</td>
              <td class="text-right">{{ dataItem.cif }}</td>
              <td class="text-right">{{ dataItem.totalTransactionDebit }}</td>
              <td class="text-right">{{ dataItem.totalTransactionCredit }}</td>
            </tr>
          </tbody>
        </table>
        <div class="row d-block text-center m-0 no-search-result-wrapper" *ngIf="transactionReport?.length === 0">
          <img src="/assets/dist/img/icon/empty.svg" height="120" alt="no_search_result" />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
      </div>
      <div *ngIf="transactionReport.length">
        <mat-paginator [length]="formTransactionReportSearch.value.length"
          [pageSize]="formTransactionReportSearch.value.pageSize"
          [pageIndex]="formTransactionReportSearch.value.pageIndex" [pageSizeOptions]="pageSizeOptions"
          (page)="onChangePage($event)" aria-label="Select page">
        </mat-paginator>
      </div>
    </div>
  </div>
</section>