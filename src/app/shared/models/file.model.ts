export interface IFile {
  id?: number;
  name?: string;
  path?: string;
  type?: string;
}

export class File implements IFile {
  constructor(
    public id?: number,
    public name?: string,
    public path?: string,
    public type?: string
  ) {
    this.id = id;
    this.name = name;
    this.path = path;
    this.type = type;
  }
}

export interface IDocument {
  id?: number;
  name?: string;
  path?: string;
  description?: string;
  normalizeName?: string;
  embedLink?: string;
  classPk?: number;
}
