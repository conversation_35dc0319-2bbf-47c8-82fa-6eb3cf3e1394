import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import {
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  MODAL_ACTION,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import {
  ACTION_TOKEN_CONST,
  ENTITY_ACTIVE_USER_STATUS,
  ENTITY_ACTIVE_USER_STATUS_MAP,
  ENTITY_STATUS_USER,
  ENTITY_STATUS_USER_CONST,
  SELECTED_CONST,
} from '@shared/constants/user.constants';
import { IDepartment } from '@shared/models/department.model';
import { IPosition } from '@shared/models/position.model';
import { IUserSearch } from '@shared/models/request/user.search';
import { IRole } from '@shared/models/role.model';
import { IUser } from '@shared/models/user.model';
import { DepartmentService } from '@shared/services/department.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { PositionService } from '@shared/services/position.service';
import { RoleService } from '@shared/services/role.service';
import { UserService } from '@shared/services/user.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-user-management',
  templateUrl: './user-management.component.html',
  styleUrls: ['./user-management.component.scss'],
})
export class UserManagementComponent implements OnInit {
  users: IUser[] = [];
  departments: IDepartment[] = [];
  positions: IPosition[] = [];
  roles: IRole[] = [];
  recordSelected: any = [];
  storage: any;
  action: any = '';

  pageSizeOptions = PAGINATION.OPTIONS;
  ROUTER_UTILS = ROUTER_UTILS;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  ENTITY_STATUS = ENTITY_STATUS;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  ACTION_TOKEN_CONST = ACTION_TOKEN_CONST;
  SELECTED_CONST = SELECTED_CONST;
  SYSTEM_RULES = SYSTEM_RULES;
  ENTITY_ACTIVE_USER_STATUS = ENTITY_ACTIVE_USER_STATUS;
  ENTITY_ACTIVE_USER_STATUS_MAP = ENTITY_ACTIVE_USER_STATUS_MAP;
  ENTITY_STATUS_USER = ENTITY_STATUS_USER;
  ENTITY_STATUS_USER_CONST = ENTITY_STATUS_USER_CONST;

  // default form search
  formUserSearch = this.fb.group({
    keyword: '',
    status: null,
    isActive: null,
    departmentIds: [],
    positionIds: [],
    roleIds: [],
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    // previousPageIndex: 0,
  });

  constructor(
    private userService: UserService,
    private fb: FormBuilder,
    private router: Router,
    private modalService: ModalService,
    private toastService: ToastrCustomService,
    private departmentService: DepartmentService,
    private positionService: PositionService,
    private roleService: RoleService
  ) {
    // get session storage
    this.storage = sessionStorage.getItem(STORAGE_APP.USER);
  }

  /**
   * Init data
   */
  ngOnInit(): void {
    this.getDepartment();

    this.getPosition();
    this.getRoles();
    // check storage
    // (when click back button or back website)
    if (this.storage) {
      // get session storage and parse json
      const filter = JSON.parse(this.storage) as IUserSearch;
      // set value form control
      if (filter.status) {
        const status = +filter.status;
        this.formUserSearch.controls.status.setValue(status);
      }
      this.formUserSearch.controls.isActive.setValue(filter.isActive);
      this.formUserSearch.controls.departmentIds.setValue(filter.departmentIds);
      this.formUserSearch.controls.roleIds.setValue(filter.roleIds);
      this.formUserSearch.controls.positionIds.setValue(filter.positionIds);
      this.formUserSearch.controls.keyword.setValue(filter.keyword);
      this.onSearch();
    } else {
      // set default value start date and end date
      this.onSearch();
    }
    // click form clear storage
    sessionStorage.removeItem(STORAGE_APP.USER);
  }

  /**
   * Change page: next page and preview page
   *
   * @param page PageEventpageSizeOptions
   */
  onChangePage(page: PageEvent) {
    this.formUserSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formUserSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  onSearchSubmit() {
    this.formUserSearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.formUserSearch.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    this.onSearch();
  }

  getRoles(): void {
    this.roleService.getAll().subscribe((response: any) => {
      this.roles = response.body;
    });
  }

  /**
   * search form
   */
  onSearch(): void {
    const body = this.formUserSearch.value;
    if (
      this.formUserSearch.controls.status.value ||
      this.formUserSearch.controls.status.value ===
        ENTITY_STATUS_USER_CONST.INACTIVE.code
    ) {
      if (
        this.formUserSearch.controls.status.value ===
        ENTITY_STATUS_USER_CONST.NOT_ACTIVATED.code
      ) {
        body.isActive = false;
        body.status = ENTITY_STATUS_USER_CONST.INACTIVE.code;
      } else if (
        this.formUserSearch.controls.status.value ===
        ENTITY_STATUS_USER_CONST.INACTIVE.code
      ) {
        body.isActive = true;
        body.status = ENTITY_STATUS_USER_CONST.INACTIVE.code;
      } else {
        body.isActive = null;
      }
    }

    this.userService.search(body).subscribe((res: any): void => {
      this.users = res.body.content;
      this.formUserSearch.controls.length.setValue(res.body.totalElements);
    });
  }

  getDepartment(): void {
    this.departmentService.getAll().subscribe((response: any) => {
      this.departments = response.body;
    });
  }

  getPosition(): void {
    this.positionService.getAll().subscribe((response: any) => {
      this.positions = response.body;
    });
  }

  /**
   * index on list loan online
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formUserSearch.value.pageIndex,
      this.formUserSearch.value.pageSize
    );
  }

  /**
   * button click detail
   *
   * @param userId number
   */
  detail(userId?: number): void {
    sessionStorage.setItem(
      STORAGE_APP.USER,
      JSON.stringify(this.formUserSearch.value)
    );
    this.router.navigate([
      ROUTER_UTILS.user.root,
      userId,
      ROUTER_ACTIONS.detail,
    ]);
  }

  /**
   * button click edit
   *
   * @param userId number
   */
  edit(userId?: number): void {
    sessionStorage.setItem(
      STORAGE_APP.USER,
      JSON.stringify(this.formUserSearch.value)
    );
    this.router.navigate([
      ROUTER_UTILS.user.root,
      userId,
      ROUTER_ACTIONS.update,
    ]);
  }

  /**
   * reset form
   */
  onReset(): void {
    // set default value date and reset data
    this.formUserSearch.controls.positionIds.reset();
    this.formUserSearch.controls.departmentIds.reset();
    this.formUserSearch.controls.roleIds.reset();
    this.formUserSearch.controls.status.reset();
    this.formUserSearch.controls.isActive.reset();
    this.formUserSearch.controls.keyword.reset();
  }

  /**
   * check lock and unlock and call api
   *
   * @param customer ICustomer
   */
  lockAndUnlock(user: IUser): void {
    if (user.status === ENTITY_STATUS_CONST.INACTIVE.code) {
      this.unLockUser(user);
    } else {
      this.lockUser(user);
    }
  }

  /**
   * Lock user register
   *
   * @param user IUser
   */
  private lockUser(user: IUser) {
    const modalData = {
      title: 'user.lock',
      content: 'user.lockUserContent',
      interpolateParams: { fullname: `<b>${user?.fullname || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { userId: user?.userId };
        this.userService.lock(params).subscribe((res) => {
          this.toastService.success('common.action.lockSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * UnLock user register
   *
   * @param user: IUser
   */
  private unLockUser(user: IUser) {
    const modalData = {
      title: 'user.unlock',
      content: 'user.unlockUserContent',
      interpolateParams: { fullname: `<b>${user?.fullname || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { userId: user?.userId };
        this.userService.unlock(params).subscribe((res) => {
          this.toastService.success('common.action.unlockSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * delete
   *
   * @param user IUser
   */
  delete(user: IUser): void {
    // open modal
    const modalData = {
      title: 'user.delete',
      content: 'user.deleteUserContent',
      interpolateParams: { fullname: `<b>${user?.fullname || ''}</b>` },
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { userId: user?.userId };
        this.userService.deleteUser(params).subscribe((res: any) => {
          this.toastService.success('common.action.deleteSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * resend link active
   *
   * @param user: IUser
   */
  resend(user: IUser) {
    const modalData = {
      title: 'user.resend',
      content: 'user.resendUserContent',
      interpolateParams: { fullname: `<b>${user?.fullname || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { userId: user?.userId };
        this.userService.resend(params).subscribe((res) => {
          this.toastService.success('common.action.resendLinkSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * reset password
   *
   * @param user: IUser
   */
  resetPassword(user: IUser) {
    const modalData = {
      title: 'user.resetPassword',
      content: 'user.resetPasswordUserContent',
      interpolateParams: { fullname: `<b>${user?.fullname || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { userId: user?.userId };
        this.userService.resetPassword(params).subscribe((res) => {
          this.toastService.success('common.action.changePasswordSuccess');
          this.onSearch();
        });
      }
    });
  }

  onSelectAll(value?: string) {
    const selectedDepartment = this.departments.map(
      (item) => item.departmentId
    );
    const selectedPosition = this.positions.map((item) => item.positionId);
    switch (value) {
      case SELECTED_CONST.DEPARTMENT:
        this.formUserSearch
          .get('departmentIds')
          ?.patchValue(selectedDepartment);
        break;
      case SELECTED_CONST.POSITION:
        this.formUserSearch.get('positionIds')?.patchValue(selectedPosition);
        break;
    }
  }

  onClearAll(value?: string) {
    switch (value) {
      case SELECTED_CONST.DEPARTMENT:
        this.formUserSearch.get('departmentIds')?.patchValue([]);
        break;
      case SELECTED_CONST.POSITION:
        this.formUserSearch.get('positionIds')?.patchValue([]);
        break;
    }
  }
}
