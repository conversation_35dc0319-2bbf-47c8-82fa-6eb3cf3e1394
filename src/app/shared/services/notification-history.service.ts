import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { IConfigAuto } from '@shared/models/config-auto-transaction.model';
import { INotificationHistoryTransaction } from '@shared/models/notification-history-transaction.model';
import { INotificationHistorySearch } from '@shared/models/request/notification-history.search';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({ providedIn: 'root' })
export class NotificationHistoryService extends AbstractService {
  public resourceUrl = '/notification-history';
  resource: RESOURCE_TYPE = RESOURCE_CONST.NOTIFICATION_HISTORY;

  constructor(protected http: HttpClient) {
    super(http);
  }

  /**
   * API count
   *
   * @param request INotificationHistorySearch
   * @returns Observable<EntityResponseType<INotificationHistoryTransaction>>
   */
  notification(
    request: INotificationHistorySearch,
    loading = false
  ): Observable<EntityResponseType<INotificationHistoryTransaction>> {
    return super.post<EntityResponseType<INotificationHistoryTransaction>>(
      `${this.resourceUrl}/notification`,
      request,
      { loading }
    );
  }

  /**
   * API search
   *
   * @param request INotificationHistorySearch
   * @returns Observable<EntityResponseType<INotificationHistoryTransaction>>
   */
  search(
    request: INotificationHistorySearch,
    loading = false
  ): Observable<EntityResponseType<INotificationHistoryTransaction>> {
    return super.post<EntityResponseType<INotificationHistoryTransaction>>(
      `${this.resourceUrl}/search`,
      request,
      { loading }
    );
  }

  /**
   * API search
   *
   * @param request INotificationHistorySearch
   * @returns Observable<EntityResponseType<INotificationHistoryTransaction>>
   */
  countUnread(
    request: INotificationHistorySearch,
    loading = false
  ): Observable<EntityResponseType<INotificationHistoryTransaction>> {
    return super.post<EntityResponseType<INotificationHistoryTransaction>>(
      `${this.resourceUrl}/number-notification`,
      request,
      { loading }
    );
  }

  /**
   * API export
   *
   * @param request INotificationHistorySearch
   * @returns Observable<any>
   */
  export(request: INotificationHistorySearch): Observable<any> {
    return super.postFile(`${this.resourceUrl}/export`, request);
  }

  /**
   * API lock
   *
   * @param request INotificationHistorySearch
   * @returns Observable<EntityResponseType<INotificationHistorySearch>>
   */
  notContacted(
    request: INotificationHistoryTransaction
  ): Observable<EntityResponseType<INotificationHistoryTransaction>> {
    return super.post<EntityResponseType<INotificationHistoryTransaction>>(
      `${this.resourceUrl}/not-contacted`,
      request
    );
  }

  /**
   * API unlock
   *
   * @param request INotificationHistorySearch
   * @returns Observable<EntityResponseType<INotificationHistorySearch>>
   */
  contacted(
    request: INotificationHistoryTransaction
  ): Observable<EntityResponseType<INotificationHistoryTransaction>> {
    return super.post<EntityResponseType<INotificationHistoryTransaction>>(
      `${this.resourceUrl}/contacted`,
      request
    );
  }

  createConfig(
    request: IConfigAuto
  ): Observable<EntityResponseType<IConfigAuto>> {
    return super.post<EntityResponseType<IConfigAuto>>(
      `${this.resourceUrl}/config-create`,
      request
    );
  }

  updateConfig(
    request: IConfigAuto
  ): Observable<EntityResponseType<IConfigAuto>> {
    return super.post<EntityResponseType<IConfigAuto>>(
      `${this.resourceUrl}/config-update`,
      request
    );
  }

  detailConfig(
    request: IConfigAuto
  ): Observable<EntityResponseType<IConfigAuto>> {
    return super.post<EntityResponseType<IConfigAuto>>(
      `${this.resourceUrl}/config-info`,
      request
    );
  }
}
