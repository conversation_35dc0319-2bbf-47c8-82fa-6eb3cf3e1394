<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form
        [formGroup]="formNotificationHistorySearch"
        (submit)="onSearchSubmit()"
      >
        <div class="row">
          <div class="col-md-4 col-lg-2">
            <div class="form-group">
              <label>{{ "common.action.searchKeyword" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="keyword"
                class="w-100"
                class="form-control"
                placeholder="{{ 'common.action.searchKeyword' | placeholder }}"
                [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
              />
            </div>
          </div>
          <div class="form-group col-md-4 col-lg-2">
            <label>{{
              "model.report.transaction.contactStatus" | translate
            }}</label>
            <ng-select
              placeholder="{{
                'model.report.transaction.contactStatus' | translate
              }}"
              [searchable]="true"
              formControlName="statuses"
              [clearable]="true"
              [multiple]="true"
              appearance="outline"
            >
              <ng-option
                [value]="item.code"
                *ngFor="let item of NOTIFICATION_LIMIT_ENTITY_STATUS"
              >
                {{ item.label | translate }}
              </ng-option>
              <ng-template ng-header-tmp>
                <div>
                  <button
                    class="btn btn-link"
                    type="button"
                    (click)="onSelectAll(SELECTED_CONST.TRANSACTION_STATUS)"
                  >
                    {{ "common.action.selectAll" | translate }}
                  </button>
                  <button
                    class="btn btn-link"
                    type="button"
                    (click)="onClearAll(SELECTED_CONST.TRANSACTION_STATUS)"
                  >
                    {{ "common.action.clearAll" | translate }}
                  </button>
                </div>
              </ng-template>
            </ng-select>
          </div>
          <div class="col-lg-4 col-md-8">
            <div class="date-picker-container form-group row">
              <div class="col-md-6">
                <label
                  >{{ "common.action.fromDate" | translate
                  }}<span class="text-danger">*</span></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="fromDate"
                    formControlName="fromDate"
                    placeholder="DD/MM/YYYY"
                    [max]="maxDate"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="fromDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #fromDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formNotificationHistorySearch.get('fromDate')?.errors
                      ?.required &&
                    formNotificationHistorySearch.get('fromDate')?.touched
                  "
                >
                  {{ "error.required.fromDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formNotificationHistorySearch.get('fromDate')?.errors
                      ?.invalidDate &&
                    formNotificationHistorySearch.get('fromDate')?.touched
                  "
                >
                  {{ "error.required.inValidDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formNotificationHistorySearch.get('fromDate')?.errors
                      ?.invalidMaxDate &&
                    formNotificationHistorySearch.get('fromDate')?.touched
                  "
                >
                  {{
                    "error.maxDateCurrent"
                      | translate
                        : {
                            param: "common.action.fromDate" | translate
                          }
                  }}
                </small>
              </div>
              <div class="col-md-6">
                <label
                  >{{ "common.action.toDate" | translate
                  }}<span class="text-danger">*</span></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="toDate"
                    formControlName="toDate"
                    placeholder="DD/MM/YYYY"
                    min="{{
                      formNotificationHistorySearch.controls['fromDate'].value
                        | date : 'yyyy-MM-dd'
                    }}"
                    [max]="maxToDate"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="toDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #toDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formNotificationHistorySearch.get('toDate')?.errors
                      ?.required &&
                    formNotificationHistorySearch.get('toDate')?.touched
                  "
                >
                  {{ "error.required.toDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formNotificationHistorySearch.get('toDate')?.errors
                      ?.invalidMaxDate &&
                    formNotificationHistorySearch.get('toDate')?.touched
                  "
                >
                  {{
                    "error.maxDateCurrent"
                      | translate
                        : {
                            param: "common.action.toDate" | translate
                          }
                  }}
                </small>
              </div>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
            >
              <div class="btn-reset">
                <i
                  class="bi bi-arrow-clockwise"
                  type="button"
                  (click)="onReset()"
                >
                </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" type="submit">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-4 mt-4">
          <button
            class="btn btn-red mr-2 w-auto"
            (click)="onConfigAutoReport()"
            *hasPrivileges="SYSTEM_RULES.NOTIFICATION_HISTORY_CONFIG_INFO"
          >
            {{
              "model.report.transaction.configAutoNotificationHistory.configAutoButton"
                | translate
            }}
          </button>
          <button
            class="btn btn-red mr-2"
            type="button"
            (click)="exportFile()"
            [disabled]="this.transactionReport.length === 0"
            *hasPrivileges="SYSTEM_RULES.NOTIFICATION_HISTORY_EXPORT"
          >
            {{ "common.action.export" | translate }}
          </button>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table table-transaction">
          <thead>
            <tr>
              <th scope="col" class="text-center">
                {{ "common.no" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "customerRegisterManagement.cif" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "common.accountNumber" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "customerRegisterManagement.phoneNumber" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "model.user.fullname" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "model.merchant.transactionHistory.date" | translate }}
              </th>
              <!-- <th scope="col" class="text-center">
                {{ "model.merchant.transferType" | translate }}
              </th> -->
              <th scope="col" class="text-center">
                {{
                  "model.manageSaving.savingInfo.transactionHistory.money"
                    | translate
                }}
              </th>
              <th scope="col" class="text-center">
                {{ "model.referral.referralCode" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "common.status" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of transactionReport; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>

              <td class="text-center">
                {{ dataItem.customerCif }}
              </td>
              <td class="text-center">
                {{ dataItem.target }}
              </td>
              <td class="text-center">
                {{ dataItem.phoneNumber }}
              </td>
              <td class="text-center">
                {{ dataItem.customerAccountName }}
              </td>
              <td class="text-center">
                {{ dataItem.transactionFinishTime }}
              </td>
              <td class="text-center">
                {{ dataItem.transactionAmount | currencyLak }}
              </td>
              <td class="text-center">
                {{ dataItem.referralCode }}
              </td>
              <td class="text-center">
                <span
                  class="badge"
                  [ngClass]="
                    NOTIFICATION_LIMIT_ENTITY_STATUS_MAP[
                      dataItem.status ||
                        ENTITY_NOTIFICATION_LIMIT_STATUS_CONST.NO_CONTACT_YET
                          .code
                    ].style
                  "
                  >{{
                    NOTIFICATION_LIMIT_ENTITY_STATUS_MAP[
                      dataItem.status ||
                        ENTITY_NOTIFICATION_LIMIT_STATUS_CONST.NO_CONTACT_YET
                          .code
                    ].label | translate
                  }}</span
                >
              </td>
              <td class="text-center">
                <nz-switch
                  [nzControl]="true"
                  (click)="toggleActive(dataItem)"
                  [ngModel]="dataItem.status === CONTACTED"
                  *hasPrivileges="
                    dataItem.status === CONTACTED
                      ? SYSTEM_RULES.NOTIFICATION_HISTORY_NOT_CONTACTED
                      : SYSTEM_RULES.NOTIFICATION_HISTORY_CONTACTED
                  "
                ></nz-switch>
              </td>
            </tr>
          </tbody>
        </table>
        <div
          class="row d-block text-center m-0 no-search-result-wrapper"
          *ngIf="transactionReport?.length === 0"
        >
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
      </div>
      <div *ngIf="transactionReport.length">
        <mat-paginator
          [length]="formNotificationHistorySearch.value.length"
          [pageSize]="formNotificationHistorySearch.value.pageSize"
          [pageIndex]="formNotificationHistorySearch.value.pageIndex"
          [pageSizeOptions]="pageSizeOptions"
          (page)="onChangePage($event)"
          aria-label="Select page"
        >
        </mat-paginator>
      </div>
    </div>
  </div>
</section>
