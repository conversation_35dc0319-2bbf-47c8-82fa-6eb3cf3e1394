import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TransactionFeeTypeSearch } from '@shared/models/request/transaction-fee-type.search';
import { ITransactionFeeType } from '@shared/models/transaction-fee-type.model';
import {
  AbstractService,
  EntityResponseType,
} from '@shared/services/common/abstract.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class TransactionFeeTypeService extends AbstractService {
  public resourceUrl = '/transaction-fee-type';

  constructor(protected http: HttpClient) {
    super(http);
  }

  /**
   * search auto complete transaction fee type
   *
   * @param request
   */
  searchAutoComplete(
    request: TransactionFeeTypeSearch
  ): Observable<EntityResponseType<ITransactionFeeType>> {
    return super.post<EntityResponseType<ITransactionFeeType>>(
      `${this.resourceUrl}/auto-complete`,
      request
    );
  }
}
