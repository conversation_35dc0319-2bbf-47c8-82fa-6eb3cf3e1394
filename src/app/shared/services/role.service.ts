import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IGroupPrivileges } from '@shared/models/group-privileges.model';
import { IRoleSearch } from '@shared/models/request/role.search';
import { IRole } from '@shared/models/role.model';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';
@Injectable({
  providedIn: 'root',
})
export class RoleService extends AbstractService {
  public resourceUrl = '/role';

  constructor(protected http: HttpClient) {
    super(http);
  }

  /**
   * API search
   *
   * @param request IPagination
   * @returns Observable<EntityResponseType<IRole[]>>
   */
  search(request: IRoleSearch): Observable<EntityResponseType<IRole[]>> {
    return super.post<EntityResponseType<IRole[]>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  /**
   * getAll
   *
   * @returns Observable<EntityResponseType<IRole[]>>
   */
  getAll(): Observable<EntityResponseType<IRole[]>> {
    return super.post(`${this.resourceUrl}/all`);
  }

  /**
   * API create
   *
   * @param request IRole
   * @returns Observable<EntityResponseType<IRole>>
   */
  create(request: IRole): Observable<EntityResponseType<IRole>> {
    return super.post<EntityResponseType<IRole>>(
      `${this.resourceUrl}/create`,
      request
    );
  }

  /**
   * API update
   *
   * @param request IRole
   * @returns Observable<EntityResponseType<IRole>>
   */
  update(request: IRole): Observable<EntityResponseType<IRole>> {
    return super.post<EntityResponseType<IRole>>(
      `${this.resourceUrl}/update`,
      request
    );
  }

  /**
   * API list privilege
   *
   * @returns Observable<EntityResponseType<IGroupPrivileges[]>>
   */
  listPrivilege(): Observable<EntityResponseType<IGroupPrivileges[]>> {
    return super.post<EntityResponseType<IGroupPrivileges[]>>(
      `${this.resourceUrl}/group-privileges`
    );
  }

  /**
   * API lock
   *
   * @param params any
   * @returns Observable<EntityResponseType<IRole>>
   */
  lock(params: any): Observable<EntityResponseType<IRole>> {
    return super.post<EntityResponseType<IRole>>(
      `${this.resourceUrl}/lock`,
      params
    );
  }

  /**
   * API unlock
   *
   * @param params any
   * @returns Observable<EntityResponseType<IRole>>
   */
  unlock(params: any): Observable<EntityResponseType<IRole>> {
    return super.post<EntityResponseType<IRole>>(
      `${this.resourceUrl}/unlock`,
      params
    );
  }

  /**
   * API deleteRole
   *
   * @param params any
   * @returns Observable<EntityResponseType<IRole>>
   */
  deleteRole(params: any): Observable<EntityResponseType<IRole>> {
    return super.post<EntityResponseType<IRole>>(
      `${this.resourceUrl}/delete`,
      params
    );
  }

  /**
   * API detail
   *
   * @param params any
   * @returns Observable<EntityResponseType<IRole>>
   */
  detail(params: any): Observable<EntityResponseType<IRole>> {
    return super.post<EntityResponseType<IRole>>(
      `${this.resourceUrl}/detail`,
      params
    );
  }
}
