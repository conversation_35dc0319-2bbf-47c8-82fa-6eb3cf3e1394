import { Component, HostListener, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Form<PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { MatDatepickerInputEvent } from '@angular/material/datepicker';
import { ActivatedRoute, Router } from '@angular/router';
import {
  DATE_CONSTANT,
  ENTITY_STATUS,
  FILE_UPLOAD_EXTENSIONS,
  SCREEN_SHOW_BANNER,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { Campaign, ICampaign } from '@shared/models/campaign.model';
import {
  IFileEntryDelete,
  IFileEntrySearch,
} from '@shared/models/request/file-entry.search';
import { IImageUrls } from '@shared/models/request/image-type.search';
import { CampaignService } from '@shared/services/campaign.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import { Subject, of } from 'rxjs';
import { switchMap, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-detail-campaign',
  templateUrl: './detail-update-campaign.component.html',
  styleUrls: ['./detail-update-campaign.component.scss'],
})
export class DetailUpdateCampaignComponent implements OnInit, OnDestroy {
  formCampaign: FormGroup = new FormGroup({});
  campaignDto: Campaign = new Campaign();
  campaign: Campaign = new Campaign();
  reader: FileReader = new FileReader();
  private ngUnsubscribe = new Subject<void>();

  fileUploads: any[] = [];
  filesRemove: any[] = [];
  bannerList: any[] = [];
  startDateMin?: Date;
  action?: string;
  position?: string;
  maxDate = new Date();
  bannerLength = 0;

  isDisabled?: boolean;
  hasFilter = false;
  isLoadBannerList = false;
  isHasCampaign = false;

  ROUTER_UTILS = ROUTER_UTILS;
  ENTITY_STATUS = ENTITY_STATUS;
  VALIDATORS = VALIDATORS;
  FILE_UPLOAD_EXTENSIONS = FILE_UPLOAD_EXTENSIONS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  DATE_CONSTANT = DATE_CONSTANT;
  SCREEN_SHOW_BANNER = SCREEN_SHOW_BANNER;

  constructor(
    private formBuilder: FormBuilder,
    private campaignService: CampaignService,
    private routerActive: ActivatedRoute,
    private router: Router,
    private toastService: ToastrCustomService
  ) {
    this.routerActive.data.subscribe((data: any) => {
      this.routerActive.paramMap.subscribe((res) => {
        const idParam = res.get('campaignId');
        if (data.action === ROUTER_ACTIONS.update && idParam) {
          this.campaignDto.campaignId = +idParam;
          this.action = ROUTER_ACTIONS.update;
        }
        if (data.action === ROUTER_ACTIONS.detail && idParam) {
          this.campaignDto.campaignId = +idParam;
          this.action = ROUTER_ACTIONS.detail;
        }
      });
    });

    this.initForm();
  }

  ngOnInit(): void {
    this.isDisabled = true;
    if (this.action === ROUTER_ACTIONS.detail) {
      this.getDetailSwitchMap();
      return;
    }
    if (this.action === ROUTER_ACTIONS.update) {
      this.getDetailSwitchMap();
      return;
    }
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.hasFilter = true;
  }

  /**
   * back to monitor list
   */
  backToList(): void {
    this.hasFilter = true;
    this.router.navigate([ROUTER_UTILS.sysManage.campaign.root]);
  }

  initForm(campaign?: Campaign): void {
    this.formCampaign = this.formBuilder.group({
      campaignName: [
        campaign?.campaignName || '',
        [
          Validators.required,
          Validators.maxLength(VALIDATORS.LENGTH.CAMPAIGN_NAME_MAX_LENGTH),
          Validators.pattern(VALIDATORS.PATTERN.CAMPAIGN_NAME),
          Validators.pattern(VALIDATORS.PATTERN.CAMPAIGN_NAME_ASCII),
        ],
      ],
      description: [
        campaign?.description || '',
        [
          Validators.maxLength(
            VALIDATORS.LENGTH.CAMPAIGN_DESCRIPTION_MAX_LENGTH
          ),
        ],
      ],
      status: [campaign?.status || 0],
      startDate: [
        CommonUtils.reverseDate(campaign?.startDate) || null,
        [Validators.required],
      ],
      endDate: [
        CommonUtils.reverseDate(campaign?.endDate) || null,
        [Validators.required],
      ],
      createdBy: campaign?.createdBy || null,
      lastModifiedBy: campaign?.lastModifiedBy || null,
      position: [campaign?.position, [Validators.required]],
      file: [[]],
      banners: [[]],
    });
    if (this.action === ROUTER_ACTIONS.detail) {
      this.formCampaign.disable();
    }
    if (this.action === ROUTER_ACTIONS.update) {
      this.formCampaign.enable();
    }
  }

  /**
   * get detail campaign
   */

  getDetailSwitchMap(): void {
    this.campaignService
      .detail({ campaignId: this.campaignDto.campaignId })
      .subscribe((res: any) => {
        this.isHasCampaign = true;
        this.campaign = res.body;
        this.initForm(this.campaign);
        this.getBanner(this.campaign);
      });
  }

  getBanner(data: ICampaign) {
    const params = { campaignId: this.campaign.campaignId };
    if (data.banners) {
      for (const item of data.banners) {
        const searchFile: IFileEntrySearch = {};
        searchFile.fileEntryId = item.id;
        searchFile.normalizeName = item.normalizeName;
        this.campaignService
          .getBanner(searchFile, params)
          .pipe(takeUntil(this.ngUnsubscribe))
          .subscribe((responsive: any) => {
            CommonUtils.blobToBase64(responsive.body).then((base64) => {
              const imageUrls: IImageUrls = {
                src: base64,
                id: item.id,
                embedLink: item.embedLink,
                classPk: this.campaign.campaignId,
                entryId: item.id,
                file: item.id,
              };
              this.bannerList.push(imageUrls);
              if (data?.banners?.length === this.bannerList.length) {
                this.bannerLength = data.banners.length;
                this.isLoadBannerList = true;
                this.position = this.campaign.position;
                const bannerListSort = this.bannerList.sort(this.sortArrImage);
                this.fileUploads = bannerListSort;
              } else {
                this.isLoadBannerList = false;
              }
            });
          });
      }
    }
  }

  sortArrImage(a: any, b: any): any {
    return a.id - b.id;
  }

  onUploadPics(fileSelected: FormGroup[]): void {
    // set fileUpload
    this.fileUploads = [];
    for (const item of fileSelected) {
      this.fileUploads.push(item.value);
    }
  }

  onRemovePics(event: any): void {
    this.fileUploads = event;
  }

  onPicsRemain(event: any): void {
    this.filesRemove.push(event);
  }

  onAddImage(event: FormGroup[]): void {
    this.fileUploads = [];
    for (const item of event) {
      this.fileUploads.push(item.value);
    }
  }

  onUpdate(): void {
    const data = this.formCampaign.value;
    data.file = [];
    const fileUploadFilter = this.fileUploads.filter((item) => {
      return item.file || item.embedLink;
    });
    const bannerNull = [];
    for (const item of this.fileUploads) {
      if (!item.file && !item.embedLink) {
        bannerNull.push(item);
      }
    }
    if (this.formCampaign.invalid) {
      CommonUtils.markFormGroupTouched(this.formCampaign);
    }

    if (fileUploadFilter.length < 1) {
      this.toastService.error('error.campaign.required.bannerScreen');
      return;
    }
    if (fileUploadFilter.length > 1) {
      if (!this.checkImageValid(fileUploadFilter)) {
        this.toastService.error('error.campaign.required.bannerScreen');
        return;
      }
      if (this.checkEmbedLinkValid(fileUploadFilter)) {
        this.toastService.error('error.campaign.required.embedLinkImage');
        return;
      }
      if (this.checkEmbedPattern(fileUploadFilter)) {
        return;
      }
    }
    if (fileUploadFilter.length === 1) {
      if (!this.checkImageValid(fileUploadFilter)) {
        this.toastService.error('error.campaign.required.bannerScreen');
        return;
      }
      if (this.checkEmbedLinkValid(fileUploadFilter)) {
        this.toastService.error('error.campaign.required.embedLinkImage');
        return;
      }
      if (this.checkEmbedPattern(fileUploadFilter)) {
        return;
      }
    }
    if (bannerNull.length >= 1) {
      this.toastService.error('error.campaign.required.embedLinkImage');
      return;
    }
    const unique2Filter = this.fileUploads.filter((item: IImageUrls) => {
      return item.entryId;
    });

    const idNullFilter = this.fileUploads.filter((item: IImageUrls) => {
      return !item.entryId;
    });

    const fileImage: IImageUrls[] = [];
    const embedLinkImage: string[] = [];

    for (const item of idNullFilter) {
      fileImage.push(item.file);
      embedLinkImage.push(item.embedLink);
    }
    data.campaignId = this.campaignDto.campaignId;
    data.banners = unique2Filter;
    const paramsCreate = {
      position: this.campaign.position,
      campaignId: 0,
      files: fileImage,
      embedLink: embedLinkImage,
    };

    if (idNullFilter.length === 0) {
      if (this.filesRemove.length > 0) {
        const entryIds = [];
        for (const item of this.filesRemove) {
          entryIds.push(item.entryId);
        }
        const params = {
          campaignId: this.campaignDto.campaignId,
          entryId: entryIds,
        };
        this.updateDelete(data, params);
      } else {
        this.updateDelete(data);
      }
    }

    if (idNullFilter.length !== 0) {
      if (this.filesRemove.length > 0) {
        const entryIds = [];
        for (const item of this.filesRemove) {
          entryIds.push(item.entryId);
        }
        const params = {
          campaignId: this.campaignDto.campaignId,
          entryId: entryIds,
        };
        this.updateDeleteCreate(params, data, paramsCreate);
      } else {
        this.updateCreate(data, paramsCreate);
      }
    }
  }

  updateDeleteCreate(
    deletedFiles: IFileEntryDelete,
    data: ICampaign,
    createBanner: ICampaign
  ): void {
    this.campaignService.deleteBanner(deletedFiles).subscribe(() => {});
    this.campaignService
      .update(data)
      .pipe(
        switchMap((resUpdate: any) => {
          createBanner.campaignId = resUpdate.body.campaignId;
          return this.campaignService.createBanner(createBanner);
        })
      )
      .subscribe(() => {
        this.router.navigate([ROUTER_UTILS.sysManage.campaign.root]);
        this.toastService.success('model.campaign.updateSuccess');
      });
  }

  updateCreate(data: ICampaign, createBanner: ICampaign) {
    this.campaignService
      .update(data)
      .pipe(
        switchMap((resUpdate: any) => {
          createBanner.campaignId = resUpdate.body.campaignId;
          return this.campaignService.createBanner(createBanner);
        })
      )
      .subscribe(() => {
        this.router.navigate([ROUTER_UTILS.sysManage.campaign.root]);
        this.toastService.success('model.campaign.updateSuccess');
      });
  }

  updateDelete(data: ICampaign, deletedFiles?: IFileEntryDelete) {
    if (deletedFiles) {
      this.campaignService
        .update(data)
        .pipe(
          switchMap((res: any) => {
            return of(res);
          })
        )
        .subscribe(() => {
          this.campaignService.deleteBanner(deletedFiles).subscribe(() => {
            this.router.navigate([ROUTER_UTILS.sysManage.campaign.root]);
            this.toastService.success('model.campaign.updateSuccess');
          });
        });
    } else {
      this.campaignService.update(data).subscribe(() => {
        this.router.navigate([ROUTER_UTILS.sysManage.campaign.root]);
        this.toastService.success('model.campaign.updateSuccess');
      });
    }
  }

  checkImageValid(files: IImageUrls[]): boolean {
    return files.some((item: IImageUrls) => {
      return item.file;
    });
  }

  checkEmbedLinkValid(files: IImageUrls[]): any {
    return files.find((item: IImageUrls) => {
      return (item.file && !item.embedLink) || (!item.src && item.embedLink);
    });
  }

  checkEmbedPattern(files: IImageUrls[]): any {
    const pattern = new RegExp(VALIDATORS.PATTERN.HTTP_PATTERN);
    return files.find((item: IImageUrls) => {
      if (item.embedLink) {
        return !pattern.test(item.embedLink);
      } else {
        return;
      }
    });
  }

  validateTimeStart(event: MatDatepickerInputEvent<Date>): void {
    if (event.value) {
      const dateMin = new Date(event.value);
      this.startDateMin = new Date(dateMin.setDate(dateMin.getDate() + 1));
    }
  }
}
