import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { ISpecialPremiumAccountNumber } from '@shared/models/premium-account-number.modal';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { Observable } from 'rxjs';
import { AbstractDomainService } from './common/abstract.domain.service';
import { EntityResponseType } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class SpecialAccountService extends AbstractDomainService<ISpecialPremiumAccountNumber> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.SPECIAL_ACCOUNT;

  downloadTemplate() {
    return super.postFile(`/download-template`, {});
  }

  import(file: File, loading = false): Observable<EntityResponseType<ISpecialPremiumAccountNumber[]>> {
    const formData = new FormData();
    formData.append('file', file);
    return super.post(`/import-special-account`, formData, {
      loading,
    });
  }

  /**
   * hide
   *
   * @param id number
   * @returns Observable<EntityResponseType<ISpecialPremiumAccountNumber>>
   */
  hide(data: any): Observable<EntityResponseType<ISpecialPremiumAccountNumber>> {
    return super.post<ISpecialPremiumAccountNumber>(RESOURCE_CONST.COMMON.API.HIDE, data);
  }

  /**
   * unhide
   *
   * @param id number
   * @returns Observable<EntityResponseType<ISpecialPremiumAccountNumber>>
   */
  unhide(data: any): Observable<EntityResponseType<ISpecialPremiumAccountNumber>> {
    return super.post<ISpecialPremiumAccountNumber>(RESOURCE_CONST.COMMON.API.UNHIDE, data);
  }

  exportSpecialAccount(request: ISpecialPremiumAccountNumber): Observable<any> {
    return super.postFile(RESOURCE_CONST.SPECIAL_PREMIUM_ACCOUNT_NUMBER.API.EXPORT_SPECIAL_ACCOUNT, request);
  }
}
