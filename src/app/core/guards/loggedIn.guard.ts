/* eslint-disable @typescript-eslint/no-unused-vars */
import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  CanActivate,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { STORAGE_TOKEN } from '@shared/constants/storage.constants';
import { AuthProviderService } from '@shared/services/auth/auth-provider.service';
import { AuthenticationService } from '@shared/services/auth/authentication.service';
import { ROUTER_UTILS } from '@shared/utils';
import { CookieService } from 'ngx-cookie-service';

@Injectable({
  providedIn: 'root',
})
export class LoggedInGuard implements CanActivate {
  constructor(
    private authenticationService: AuthenticationService,
    private $cookie: CookieService,
    private router: Router,
    private authProviderService: AuthProviderService
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean | Promise<boolean> {
    if (this.$cookie.get(STORAGE_TOKEN.CSRF_TOKEN)) {
      this.authenticationService
        .fetch()
        .toPromise()
        .then(() => {
          this.router.navigate([ROUTER_UTILS.base.home]);
          return false;
        })
        .catch(() => {
          this.authProviderService.logout();
        });
    }
    return true;
  }
}
