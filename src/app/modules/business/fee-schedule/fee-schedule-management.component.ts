import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import {
  CALCULATE_MILLISECONDS_IN_A_YEAR,
  DATE_RANGE_STATISTICAL_LOAN_ONLINE,
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  FILE_TYPE,
  MAX_DATE_CONST,
  MODAL_ACTION,
  MOMENT_CONST,
  PAGINATION,
  RATE_TYPE,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { IFeeSchedule } from '@shared/models/FeeSchedule.model';
import { IEventSearch } from '@shared/models/request/event.search';
import { IFileEntrySearch } from '@shared/models/request/file-entry.search';
import { FeeScheduleService } from '@shared/services/FeeSchedule.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { IconService } from '@shared/services/icon.service';
import CommonUtils from '@shared/utils/common-utils';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils/router.utils';
import moment from 'moment';
import { LocalStorageService } from 'ngx-webstorage';

@Component({
  selector: 'app-fee-schedule-management',
  templateUrl: './fee-schedule-management.component.html',
  styleUrls: ['./fee-schedule-management.component.scss'],
})
export class FeeScheduleManagementComponent implements OnInit {
  pageSizeOptions = PAGINATION.OPTIONS;
  MAX_DATE_CONST = MAX_DATE_CONST;
  ROUTER_UTILS = ROUTER_UTILS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  RATE_TYPE = RATE_TYPE;
  isErrorStartDateGreaterEndDate = false;
  maxDate = new Date();
  maxToDate = new Date();
  data?: IFeeSchedule[] = [];
  eventSearch: IEventSearch = {};

  constructor(
    private formBuilder: FormBuilder,
    private feeScheduleService: FeeScheduleService,
    private modalService: ModalService,
    private toastService: ToastrCustomService,
    private localStorage: LocalStorageService,
    private router: Router,
    private iconService: IconService
  ) {}
  ENTITY_STATUS = ENTITY_STATUS;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  formSearch = this.formBuilder.group({
    title: '',
    length: 0,
    fromDate: [null, [Validators.required]],
    toDate: [null, [Validators.required]],
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    language: '',
    status: null,
  });

  ngOnInit(): void {
    const startDate = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDate = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.eventSearch.fromTime = startDate;
    this.eventSearch.toTime = endDate;
    this.formSearch.controls.fromDate.setValue(
      CommonUtils.reverseDate(startDate)
    );
    this.formSearch.controls.toDate.setValue(CommonUtils.reverseDate(endDate));
    this.onSearch();
  }

  onSearch(): void {
    if (this.formSearch.valid) {
      const params = this.formSearch.value;
      this.feeScheduleService.search(params).subscribe((res: any) => {
        this.data = res.body.content;
        res?.body?.content?.forEach((data: IFeeSchedule) => {
          if (data?.feeScheduleDetailDTO) {
            const searchFile: IFileEntrySearch = {};
            searchFile.className = FILE_TYPE.FEE_SCHEDULE;
            searchFile.resource = FILE_TYPE.FEE_SCHEDULE;
            searchFile.classPk = data?.feeScheduleDetailDTO?.icon?.classPk;
            searchFile.fileEntryId = data?.feeScheduleDetailDTO?.icon?.id;
            searchFile.normalizeName =
              data?.feeScheduleDetailDTO?.icon?.normalizeName;
            data.iconUrL = this.iconService.getIconUri(searchFile);
          }
        });
        this.formSearch.controls.length.setValue(res.body.totalElements);
      });
    }
  }

  onSearchSubmit(): void {
    this.formSearch.controls.pageIndex.setValue(PAGINATION.PAGE_NUMBER_DEFAULT);
    this.formSearch.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    this.onSearch();
  }

  onChangePage(page: any) {
    this.formSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  onReset(): void {
    const startDateReset = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDateReset = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.formSearch.controls.status.reset();
    this.formSearch.controls.title.reset();
    this.formSearch.controls.fromDate.setValue(
      CommonUtils.reverseDate(startDateReset)
    );
    this.formSearch.controls.toDate.setValue(
      CommonUtils.reverseDate(endDateReset)
    );
    this.formSearch.controls.fromDate.clearValidators();
    this.formSearch.controls.fromDate.setValidators([Validators.required]);
    this.formSearch.controls.fromDate.updateValueAndValidity();
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formSearch.value.pageIndex,
      this.formSearch.value.pageSize
    );
  }

  changeValidDate() {
    if (
      this.formSearch.controls.fromDate.value &&
      this.formSearch.controls.toDate.value
    ) {
      if (this.formSearch.controls['toDate'].value) {
        this.maxDate = this.formSearch.controls['toDate'].value;
      }
      const fromDate = CommonUtils.setStartTime(
        new Date(this.formSearch.controls.fromDate.value).getTime()
      );
      const toDate = CommonUtils.setStartTime(
        new Date(this.formSearch.controls.toDate.value).getTime()
      );
      const dayMax = (toDate - fromDate) / CALCULATE_MILLISECONDS_IN_A_YEAR.DAY;
      if (dayMax > DATE_RANGE_STATISTICAL_LOAN_ONLINE) {
        this.formSearch.controls.fromDate.setValidators([
          Validators.required,
          this.dateValidator(dayMax),
        ]);
        this.formSearch.controls.fromDate.updateValueAndValidity();
      } else {
        this.formSearch.controls.fromDate.clearValidators();
        this.formSearch.controls.fromDate.setValidators([Validators.required]);
        this.formSearch.controls.fromDate.updateValueAndValidity();
      }
    }
    this.changeEndDate();
  }

  changeEndDate() {
    if (this.formSearch.controls['toDate'].value) {
      this.maxDate = this.formSearch.controls['toDate'].value;
    }
    const dataSearch = this.formSearch.getRawValue();
    if (dataSearch.fromDate && dataSearch.toDate) {
      const startDateSearch = new Date(dataSearch.fromDate);
      const endDateSearch = new Date(dataSearch.toDate);
      this.isErrorStartDateGreaterEndDate =
        startDateSearch.getTime() > endDateSearch.getTime();
    }
  }

  dateValidator(value: number): any {
    return (control: AbstractControl): { [key: string]: any } | null => {
      return value > DATE_RANGE_STATISTICAL_LOAN_ONLINE
        ? { invalidDate: true }
        : null;
    };
  }

  onDetail(feeScheduleId?: number, action?: string) {
    this.router.navigate([
      ROUTER_UTILS.feeSchedule.root,
      feeScheduleId,
      action,
    ]);
  }

  onDelete(feeScheduleId?: number) {
    const modalData = {
      title: 'model.feeSchedule.modal.delete',
      content: 'model.feeSchedule.modal.deleteRateContent',
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { feeScheduleId };
        this.feeScheduleService.delete(params).subscribe((res: any) => {
          this.toastService.success('common.action.deleteSuccess');
          if (this.data?.length === 1) {
            this.formSearch.controls.pageIndex.setValue(
              this.formSearch.controls.pageIndex.value === 0
                ? ''
                : Number(this.formSearch.controls.pageIndex.value) - 1
            );
          }
          this.onSearch();
        });
      }
    });
  }

  lockAndUnlock(data: IFeeSchedule): void {
    const modalData = {
      title:
        data.status === 1
          ? 'model.feeSchedule.lock'
          : 'model.feeSchedule.unlock',
      content:
        data.status === 1
          ? 'model.feeSchedule.lockContent'
          : 'model.feeSchedule.unlockContent',
      interpolateParams: { title: `<b> ${data?.title || ''} </b>` },
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { feeScheduleId: data.feeScheduleId };
        if (data.status === 1) {
          this.feeScheduleService.lock(params).subscribe((res) => {
            this.toastService.success('common.action.lockSuccess');
            this.onSearch();
          });
        } else {
          this.feeScheduleService.unlock(params).subscribe((res) => {
            this.toastService.success('common.action.unlockSuccess');
            this.onSearch();
          });
        }
      }
    });
  }
}
