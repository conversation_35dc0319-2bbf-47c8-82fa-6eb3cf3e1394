import { Component, Input, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import {
  PERIOD_TYPE_CONST,
  SAVING_TYPE,
} from '@shared/constants/app.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ConfigSaving } from '@shared/models/config-saving.model';
import { MODEL_MAP_ITEM_COMMON } from '@shared/models/model.type';
import { ConfigSavingService } from '@shared/services/config-saving.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import CommonUtils from '@shared/utils/common-utils';
import { ROUTER_ACTIONS } from '@shared/utils/router.utils';
@Component({
  selector: 'create-config-saving',
  templateUrl: './create-config-saving.component.html',
  styleUrls: ['./create-config-saving.component.scss'],
})
export class CreateConfigSavingComponent implements OnInit {
  SAVING_TYPE = SAVING_TYPE;
  actionConfirm!: MODEL_MAP_ITEM_COMMON;
  formCreate: FormGroup = new FormGroup({});
  VALIDATORS = VALIDATORS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  @Input() data?: ConfigSaving;
  @Input() type?: string;
  constructor(
    private modalService: NgbModal,
    private fb: FormBuilder,
    public activeModal: NgbActiveModal,
    private toastService: ToastrCustomService,
    private configSavingService: ConfigSavingService
  ) {}

  initForm() {
    this.formCreate = this.fb.group({
      savingType: [
        {
          value: this.data?.savingType,
          disabled: this.type === ROUTER_ACTIONS.view,
        },
        [Validators.required],
      ],
      tenor: [
        {
          value: this.data?.tenor,
          disabled: this.type === ROUTER_ACTIONS.view,
        },
        [Validators.required, Validators.pattern(VALIDATORS.PATTERN.NUMBER)],
      ],
      tenorPeriod: [PERIOD_TYPE_CONST.M.value, []],
    });
  }

  ngOnInit(): void {
    this.initForm();
  }

  onSubmit() {
    const body = {
      id: this.data?.id,
      ...this.formCreate.value,
    };
    if (this.formCreate.invalid) {
      CommonUtils.markFormGroupTouched(this.formCreate);
      return;
    }
    let serviceEvent =
      this.type === ROUTER_ACTIONS.update
        ? this.configSavingService.update(body)
        : this.configSavingService.create(body);
    serviceEvent.subscribe((res) => {
      this.toastService.success(
        this.type === ROUTER_ACTIONS.update
          ? 'common.action.updateSuccess'
          : 'common.action.createSuccess'
      );
      this.activeModal.close(this.actionConfirm.code);
    });
  }
}
