<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="backToList()" />
      <h5>{{ "sidebar.merchant.masterMerchant.detail" | translate }}</h5>
    </div>
    <!-- <form [formGroup]="form" *ngIf="customer?.customerId"></form> -->
    <form
      [formGroup]="formMerchant"
      *ngIf="isUpdate ? merchant?.merchantId : !isUpdate"
    >
      <div class="col-md-12">
        <div class="row">
          <h2 class="title-create">
            {{ "merchant.titleInforMerchant" | translate }}
          </h2>
        </div>
        <div class="row border-create">
          <h3>{{ "sidebar.merchant.infor" | translate }}</h3>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label
                    >{{ "model.merchant.masterMerchant.serviceType" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <ng-select
                    formControlName="serviceType"
                    [clearable]="false"
                    [searchable]="false"
                    appearance="outline"
                    class="w-100"
                    placeholder="{{
                      'model.merchant.masterMerchant.serviceType'
                        | placeholder : 'select'
                    }}"
                  >
                    <ng-option
                      *ngFor="let item of MASTER_MERCHANT_SERVICE"
                      [value]="item.value"
                    >
                      {{ item.label | translate }}</ng-option
                    ></ng-select
                  >
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <ng-container *ngIf="isOther">
                    <label
                      >{{ "model.merchant.merchantCode" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      trim
                      formControlName="merchantCode"
                      type="text"
                      class="w-100"
                      class="form-control"
                      placeholder="{{
                        'model.merchant.merchantCode' | placeholder
                      }}"
                    />
                  </ng-container>
                  <ng-container *ngIf="!isOther">
                    <label
                      >{{ "model.merchant.merchantCode" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <ng-select
                      formControlName="merchantCode"
                      class="w-100"
                      [searchable]="false"
                      [clearable]="false"
                      appearance="outline"
                      placeholder="{{
                        'model.merchant.merchantCode' | placeholder
                      }}"
                    >
                      <ng-container
                        *ngIf="
                          formMerchant.get('serviceType')?.value ===
                          MASTER_MERCHANT_SERVICE_ENUM.TOPUP
                        "
                      >
                        <ng-option
                          *ngFor="let item of MASTER_MERCHANT_TYPE_TOPUP"
                          [value]="item.value"
                        >
                          {{ item.value }}
                        </ng-option>
                      </ng-container>
                      <ng-container
                        *ngIf="
                          formMerchant.get('serviceType')?.value ===
                          MASTER_MERCHANT_SERVICE_ENUM.BILLING
                        "
                      >
                        <ng-option
                          *ngFor="let item of MASTER_MERCHANT_TYPE_BILLING"
                          [value]="item.value"
                        >
                          {{ item.value }}
                        </ng-option>
                      </ng-container>
                    </ng-select>
                  </ng-container>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label
                    >{{ "model.merchant.merchantName" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    trim
                    formControlName="merchantName"
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{
                      'model.merchant.merchantName' | placeholder
                    }}"
                  />
                </div>
              </div>

              <div class="col-md-4">
                <div class="form-group">
                  <label>{{
                    "model.merchant.merchantAccountNumber" | translate
                  }}</label>
                  <input
                    trim
                    formControlName="merchantAccountNumber"
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{
                      'model.merchant.merchantAccountNumber' | placeholder
                    }}"
                  />
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>{{
                    "customerRegisterManagement.cif" | translate
                  }}</label>
                  <input
                    trim
                    formControlName="cif"
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{
                      'customerRegisterManagement.cif' | placeholder
                    }}"
                  />
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>{{
                    "customerRegisterManagement.phoneNumber" | translate
                  }}</label>
                  <input
                    trim
                    formControlName="phoneNumber"
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{
                      'customerRegisterManagement.phoneNumber' | placeholder
                    }}"
                  />
                </div>
              </div>
              <ng-container
                *ngIf="!merchant.balance; else inputBalance"
              ></ng-container>
              <ng-template #inputBalance>
                <div class="col-md-4">
                  <label
                    >{{ "model.merchant.merchantBalanceAccount" | translate
                    }}<small class="text-danger">*</small></label
                  >
                  <div class="form-group form-container-money">
                    <input
                      [disabled]="true"
                      [value]="money ? money : '0'"
                      trim
                      type="text"
                      class="form-control-money"
                    />
                    <div class="currentcy">
                      <span>{{ "common.unit" | translate }}</span>
                    </div>
                  </div>
                </div>
              </ng-template>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-12">
                <div class="form-group">
                  <label>{{ "model.merchant.description" | translate }}</label>
                  <textarea
                    formControlName="description"
                    type="text"
                    class="w-100"
                    class="form-control"
                    rows="4"
                    cols="50"
                    placeholder="{{
                      'model.merchant.description' | placeholder
                    }}"
                  >
                  </textarea>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row border-create mt-4">
          <h3>
            {{ "sidebar.merchant.listChilMerchant" | translate }}
          </h3>
          <div class="col-12">
            <form
              [formGroup]="formListChildSearch"
              (submit)="onSearchListChildMerChantSubmit()"
            >
              <div class="row">
                <div class="col-md-3">
                  <div class="form-group">
                    <label>{{
                      "common.action.searchKeyword" | translate
                    }}</label>
                    <input
                      trim
                      type="text"
                      formControlName="keyword"
                      placeholder="{{
                        'common.action.searchKeyword' | placeholder
                      }}"
                      class="w-100"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="col-md-3">
                  <!-- <button class="btn btn-red mr-2" type="button" (click)="onReset()">
                      {{ "common.action.reset" | translate }}
                    </button> -->
                  <button
                    class="btn btn-search b btn-search-child"
                    type="submit"
                  >
                    {{ "common.action.search" | translate }}
                  </button>
                </div>
              </div>
            </form>
          </div>
          <div class="border-bottom-search mt-3"></div>
          <div class="col-12">
            <div class="d-block text-right mb-3">
              <button
                class="btn btn-red mr-2"
                type="button"
                [disabled]="merchantChild?.length === 0"
                (click)="exportMerchant()"
                *hasPrivileges="SYSTEM_RULES.EXPORT_MERCHANT"
              >
                {{ "common.action.export" | translate }}
              </button>
            </div>
            <div class="table-responsive">
              <table class="table">
                <thead>
                  <tr>
                    <th scope="col" class="text-center">
                      {{ "common.no" | translate }}
                    </th>
                    <th scope="col">
                      {{ "model.merchant.merchantCode" | translate }}
                    </th>
                    <th scope="col">
                      {{ "model.merchant.merchantName" | translate }}
                    </th>
                    <th scope="col">
                      {{ "model.merchant.merchantAccountNumber" | translate }}
                    </th>
                    <th scope="col">
                      {{ "model.merchant.description" | translate }}
                    </th>
                    <th class="text-center" scope="col">
                      {{ "common.status" | translate }}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let dataItem of merchantChild; let i = index">
                    <td class="text-center">{{ fillIndexItemChild(i) }}</td>
                    <td>{{ dataItem.merchantCode }}</td>
                    <td title="{{ dataItem.merchantName }}">
                      {{ dataItem.merchantName | limitWord }}
                    </td>
                    <td>{{ dataItem.merchantAccountNumber }}</td>
                    <td>
                      <span title="{{ dataItem.description }}">{{
                        dataItem.description | limitWord
                      }}</span>
                    </td>
                    <td class="text-center">
                      <span
                        class="badge"
                        [ngClass]="
                          ENTITY_STATUS_MAP[dataItem.status || 0].style
                        "
                        >{{
                          ENTITY_STATUS_MAP[dataItem.status || 0].label
                            | translate
                        }}</span
                      >
                    </td>
                  </tr>
                </tbody>
              </table>
              <div
                class="row d-block text-center m-0"
                *ngIf="merchantChild?.length === 0"
              >
                <img
                  src="/assets/dist/img/icon/empty.svg"
                  height="120"
                  alt="no_search_result"
                />
                <p class="text-center mb-5">
                  {{ "common.no_search_result" | translate }}
                </p>
              </div>
              <div *ngIf="merchantChild.length">
                <mat-paginator
                  [length]="formListChildSearch.value.length"
                  [pageSize]="formListChildSearch.value.pageSize"
                  [pageIndex]="formListChildSearch.value.pageIndex"
                  [pageSizeOptions]="pageSizeOptionsChild"
                  (page)="onChangePageListChild($event)"
                  aria-label="Select page"
                >
                </mat-paginator>
              </div>
            </div>
          </div>
        </div>
        <br />
        <ng-container *hasPrivileges="SYSTEM_RULES.FEE_RATE_READ">
          <app-fee-rate
            *ngIf="isOther"
            [feeId]="feeId"
            [configurationFeeType]="configurationFeeType"
          ></app-fee-rate>
        </ng-container>

        <div
          class="row border-create mt-4"
          *hasPrivileges="
            SYSTEM_RULES.MASTER_MERCHANT_QUERY_TRANSACTION_HISTORY
          "
        >
          <h3>
            {{ "sidebar.merchant.transactionHistory.root" | translate }}
          </h3>
          <div class="col-12">
            <form
              [formGroup]="formMerchantHistorySearch"
              (submit)="onSearchHistorySubmit()"
            >
              <div class="row">
                <div class="col-md-3">
                  <div class="form-group">
                    <label>{{
                      "common.action.searchKeyword" | translate
                    }}</label>
                    <input
                      trim
                      type="text"
                      formControlName="keywordMerchant"
                      placeholder="{{
                        'common.action.searchKeyword' | placeholder
                      }}"
                      class="w-100"
                      class="form-control"
                    />
                  </div>
                </div>

                <!-- placeholder="{{ 'common.appSelectOption.status' | translate }}" -->

                <div class="col-md-3">
                  <div class="form-group">
                    <label
                      >{{
                        "model.merchant.transactionHistory.fromDate"
                          | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <mat-form-field appearance="fill" class="date-picker">
                      <input
                        matInput
                        [matDatepicker]="transactionDateStart"
                        formControlName="transactionDateStart"
                        placeholder="DD/MM/YYYY"
                        [max]="maxDate"
                        (change)="changeValidDate()"
                        (dateInput)="changeValidDate()"
                        dateTransform
                      />
                      <mat-datepicker-toggle
                        matSuffix
                        [for]="transactionDateStart"
                      ></mat-datepicker-toggle>
                      <mat-datepicker #transactionDateStart></mat-datepicker>
                    </mat-form-field>
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="
                        formMerchantHistorySearch.get('transactionDateStart')
                          ?.errors?.required &&
                        formMerchantHistorySearch.get('transactionDateStart')
                          ?.touched
                      "
                    >
                      {{ "error.required.fromDate" | translate }}
                    </small>
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="
                        formMerchantHistorySearch.get('transactionDateStart')
                          ?.errors?.invalidDate &&
                        formMerchantHistorySearch.get('transactionDateStart')
                          ?.touched
                      "
                    >
                      {{ "error.required.inValidDate" | translate }}
                    </small>
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="
                        formMerchantHistorySearch.get('transactionDateStart')
                          ?.errors?.invalidMaxDate &&
                        formMerchantHistorySearch.get('transactionDateStart')
                          ?.touched
                      "
                    >
                      {{
                        "error.maxDateCurrent"
                          | translate
                            : {
                                param: "common.action.fromDate" | translate
                              }
                      }}
                    </small>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label
                      >{{ "common.action.toDate" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <!-- <input
                        trim
                        type="date"
                        formControlName="transactionDateEnd"
                        placeholder="{{ 'common.action.toDate' | placeholder }}"
                        class="w-100"
                        class="form-control"
                        max="{{ maxDate | date: 'yyyy-MM-dd' }}"
                      /> -->
                    <mat-form-field appearance="fill" class="date-picker">
                      <input
                        matInput
                        [matDatepicker]="transactionDateEnd"
                        formControlName="transactionDateEnd"
                        placeholder="DD/MM/YYYY"
                        [min]="
                          formMerchantHistorySearch.controls[
                            'transactionDateStart'
                          ].value | date : 'yyyy-MM-dd'
                        "
                        [max]="maxToDate"
                        (change)="changeValidDate()"
                        (dateInput)="changeValidDate()"
                        dateTransform
                      />
                      <mat-datepicker-toggle
                        matSuffix
                        [for]="transactionDateEnd"
                      ></mat-datepicker-toggle>
                      <mat-datepicker #transactionDateEnd></mat-datepicker>
                    </mat-form-field>
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="
                        formMerchantHistorySearch.get('transactionDateEnd')
                          ?.errors?.required &&
                        formMerchantHistorySearch.get('transactionDateEnd')
                          ?.touched
                      "
                    >
                      {{ "error.required.toDate" | translate }}
                    </small>
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="
                        formMerchantHistorySearch.get('transactionDateEnd')
                          ?.errors?.invalidMaxDate &&
                        formMerchantHistorySearch.get('transactionDateEnd')
                          ?.touched
                      "
                    >
                      {{
                        "error.maxDateCurrent"
                          | translate
                            : {
                                param: "common.action.toDate" | translate
                              }
                      }}
                    </small>
                  </div>
                </div>
                <div class="col-md-3">
                  <!-- <button class="btn btn-red mr-2" type="button" (click)="onReset()">
                      {{ "common.action.reset" | translate }}
                    </button> -->
                  <button
                    class="btn btn-search btn-search-child"
                    type="submit"
                    *hasPrivileges="
                      SYSTEM_RULES.MASTER_MERCHANT_QUERY_TRANSACTION_HISTORY
                    "
                  >
                    {{ "common.action.search" | translate }}
                  </button>
                </div>
              </div>
              <div class="border-bottom-search mt-3"></div>
              <div class="d-block text-right mb-3 mt-4">
                <button
                  class="btn btn-red mr-2"
                  type="button"
                  [disabled]="merchantHistory?.length === 0"
                  (click)="exportFile()"
                  *hasPrivileges="
                    SYSTEM_RULES.MASTER_MERCHANT_EXPORT_TRANSACTION_HISTORY
                  "
                >
                  {{ "common.action.export" | translate }}
                </button>
              </div>
            </form>
          </div>
          <div class="col-12">
            <div class="table-responsive">
              <table class="table">
                <thead>
                  <tr>
                    <th scope="col">{{ "common.no" | translate }}</th>
                    <th scope="col">
                      {{ "model.merchant.merchantCode" | translate }}
                    </th>
                    <th scope="col">
                      {{ "model.merchant.merchantName" | translate }}
                    </th>
                    <th scope="col">
                      {{ "model.merchant.transactionHistory.code" | translate }}
                    </th>
                    <th scope="col">
                      {{ "model.merchant.transferType" | translate }}
                    </th>
                    <th scope="col">
                      {{ "model.merchant.transactionHistory.cif" | translate }}
                    </th>
                    <th scope="col">
                      {{ "model.merchant.transactionHistory.name" | translate }}
                    </th>
                    <th scope="col">
                      {{ "model.merchant.merchantAccountNumber" | translate }}
                    </th>
                    <th scope="col">
                      {{ "model.merchant.monney" | translate }}
                    </th>
                    <th scope="col">
                      {{ "model.merchant.fee" | translate }}
                    </th>
                    <th scope="col">
                      {{
                        "model.merchant.transactionHistory.totalAmount"
                          | translate
                      }}
                    </th>
                    <th scope="col">
                      {{ "model.merchant.transactionHistory.date" | translate }}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let dataItem of merchantHistory; let i = index">
                    <td class="text-center">{{ fillIndexItemHistory(i) }}</td>
                    <td>{{ dataItem.merchantCode }}</td>
                    <td title="{{ dataItem.merchantName }}">
                      {{ dataItem.merchantName | limitWord }}
                    </td>
                    <td>{{ dataItem.transactionId }}</td>
                    <td>{{ dataItem.transferTypeStr }}</td>
                    <td>{{ dataItem.customerCif }}</td>
                    <td title="{{ dataItem.customerFullName }}">
                      {{ dataItem.customerFullName | limitWord }}
                    </td>
                    <td class="text-center">
                      {{ dataItem.customerAccountNumber }}
                    </td>
                    <td class="text-center">
                      {{ dataItem.transactionAmount | currencyLak }}
                    </td>
                    <td class="text-center">
                      {{ dataItem.transactionFee | currencyLak }}
                    </td>
                    <td class="text-center">
                      {{
                        totalAmount(
                          dataItem.transactionAmount,
                          dataItem.transactionFee
                        ) | currencyLak
                      }}
                    </td>
                    <td>{{ dataItem.transactionDateStr }}</td>
                  </tr>
                </tbody>
              </table>
              <div
                class="row d-block text-center m-0"
                *ngIf="merchantHistory?.length === 0"
              >
                <img
                  src="/assets/dist/img/icon/empty.svg"
                  height="120"
                  alt="no_search_result"
                />
                <p class="text-center mb-5">
                  {{ "common.no_search_result" | translate }}
                </p>
              </div>
              <div *ngIf="merchantHistory.length">
                <mat-paginator
                  [length]="formMerchantHistorySearch.value.length"
                  [pageSize]="formMerchantHistorySearch.value.pageSize"
                  [pageIndex]="formMerchantHistorySearch.value.pageIndex"
                  [pageSizeOptions]="pageSizeOptions"
                  (page)="onChangePageHistory($event)"
                  aria-label="Select page"
                >
                </mat-paginator>
              </div>
            </div>
          </div>
        </div>
        <div class="d-block text-center mb-5 mt-4">
          <button
            class="btn btn-red mr-2"
            data-toggle="modal"
            (click)="backToList()"
          >
            {{ "common.action.back" | translate }}
          </button>
        </div>
      </div>
    </form>
  </div>
</section>
