import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IAnnouncementType } from '@shared/models/annoucement-type.model';
import { ISearch } from '@shared/models/request/base.request.model';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class AnnouncementTypeService extends AbstractService {
  public resourceUrl = '/announcement-type';

  constructor(protected http: HttpClient) {
    super(http);
  }

  searchAutoComplete(
    request: ISearch
  ): Observable<EntityResponseType<IAnnouncementType>> {
    return super.post<EntityResponseType<IAnnouncementType>>(
      `${this.resourceUrl}/auto-complete`,
      request
    );
  }
}
