import {
  AfterViewInit,
  Component,
  ComponentFactoryResolver,
  ComponentRef,
  HostListener,
  OnInit,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { MODAL_ACTION } from '@shared/constants/app.constants';
import { MODEL_MAP_ITEM_COMMON } from '@shared/models/types/model.type';

@Component({
  selector: 'app-content-modal',
  templateUrl: './content-modal.component.html',
  styleUrls: ['./content-modal.component.scss'],
})
export class ContentModalComponent implements OnInit, AfterViewInit {
  @ViewChild('dynamicComponent', { read: ViewContainerRef, static: true })
  containerRef: ViewContainerRef | undefined;

  componentRef: ComponentRef<ContentModalComponent> | undefined;

  public title = 'common.action.confirm';
  public content = '';
  public interpolateParams: object = {};
  public action!: MODEL_MAP_ITEM_COMMON;
  public component: any;
  public dataComponent: any;

  MODAL_ACTION = MODAL_ACTION;

  constructor(
    public activeModal: NgbActiveModal,
    public translateService: TranslateService
  ) {}

  ngOnInit(): void {}

  ngAfterViewInit(): void {
    this.renderComponent();
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.activeModal.close();
  }

  renderComponent(): void {
    const container = this.containerRef;
    container?.clear();
    const injector = container?.injector;

    if (injector) {
      const cfr: ComponentFactoryResolver = injector.get(
        ComponentFactoryResolver
      );
      const componentFactory = cfr.resolveComponentFactory(this.component);
      const componentRef = container.createComponent(
        componentFactory,
        0,
        injector
      ) as any;

      componentRef.instance.data = this.dataComponent;

      this.componentRef = componentRef;
    }
  }
}
