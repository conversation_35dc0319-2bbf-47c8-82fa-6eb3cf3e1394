<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="backToList()" />
      <h5>
        {{
        action === ROUTER_ACTIONS.detail
        ? ("servicePack.detailTitle" | translate)
        : action === ROUTER_ACTIONS.update
        ? ("servicePack.updateTitle" | translate)
        : ("servicePack.createTitle" | translate)
        }}
      </h5>
    </div>
    <!-- <form [formGroup]="form" *ngIf="customer?.customerId"></form> -->
    <form [formGroup]="form" *ngIf="servicePack?.clientId || action === ROUTER_ACTIONS.create">
      <div class="col-md-12">
        <div class="row">
          <h5 class="modal-title" *ngIf="!(action === ROUTER_ACTIONS.detail)">
            {{
            (action === ROUTER_ACTIONS.update
            ? "servicePack.updateTitle"
            : "servicePack.createTitle"
            ) | translate
            }}
          </h5>
          <h5 class="modal-title" *ngIf="action === ROUTER_ACTIONS.detail">
            {{ "servicePack.titleInforUser" | translate }}
          </h5>
        </div>
        <div class="row border-create">
          <h3>{{ "servicePack.titleInforUser" | translate }}</h3>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label>{{ "servicePack.name" | translate
                    }}<span class="text-danger">*</span></label>
                  <input trim formControlName="name" type="text" class="w-100" class="form-control"
                    placeholder="{{ 'servicePack.name' | placeholder }}"
                    [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH" />
                  <small class="form-text text-danger noti-small" *ngIf="
                      form.get('name')?.errors?.required &&
                      form.get('name')?.touched
                    ">
                    {{ "servicePack.error.required.name" | translate }}
                  </small>
                  <small class="form-text text-danger noti-small" *ngIf="
                      form.get('name')?.errors?.pattern &&
                      form.get('name')?.touched
                    ">
                    {{ "servicePack.error.pattern.name" | translate }}
                  </small>
                  <small class="form-text text-danger noti-small" *ngIf="
                      form.get('name')?.errors?.maxlength &&
                      form.get('name')?.touched
                    ">
                    {{
                    "servicePack.error.maxLength.name"
                    | translate
                    : {
                    param: VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH
                    }
                    }}
                  </small>
                </div>
              </div>
              <div class="col-md-6" *ngIf="action !== ROUTER_ACTIONS.create">
                <div class="form-group">
                  <label>{{ "servicePack.code" | translate}}</label>
                  <input trim formControlName="code" type="text" class="w-100" class="form-control"
                    placeholder="{{ 'servicePack.code' | placeholder }}" />
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="form-group" *ngIf="action !== ROUTER_ACTIONS.detail">
                  <label>{{ "servicePack.clientId" | translate
                    }}<span class="text-danger">*</span></label>
                  <ng-select [items]="clients" bindLabel="fullName" bindValue="clientId" [searchFn]="customSearchFn"
                    (change)="changeServicePackType($event)" formControlName="clientId" [clearable]="true"
                    [searchable]="true" appearance="outline" class="w-100" placeholder="{{
                      'servicePack.clientId' | placeholder : 'select'
                    }}">
                    <ng-option *ngFor="let item of clients" [value]="item.clientId">
                      {{ item.clientId }} - {{ item.name }}
                    </ng-option>
                  </ng-select>
                  <small class="form-text text-danger noti-small" *ngIf="
                      form.get('clientId')?.errors?.required &&
                      form.get('clientId')?.touched
                    ">
                    {{ "servicePack.error.required.clientId" | translate }}
                  </small>
                </div>
                <div class="form-group" *ngIf="action === ROUTER_ACTIONS.detail">
                  <label>{{ "servicePack.clientId" | translate
                    }}<span class="text-danger">*</span></label>
                  <input trim type="text" disabled class="w-100" class="form-control" [value]="servicePack?.fullName" />
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label>{{ "servicePack.type" | translate
                    }}<span class="text-danger" *ngIf="isRequiredType">*</span></label>
                  <ng-select formControlName="type" [clearable]="true" [searchable]="false" appearance="outline"
                    (change)="getTransactionCarrieType($event)" class="w-100" placeholder="{{
                      'servicePack.type' | placeholder : 'select'
                    }}">
                    <ng-option *ngFor="let item of servicePackTypes" [value]="item.transactionFeeTypeCode">
                      {{ item.transactionFeeTypeName }}
                    </ng-option>
                  </ng-select>
                  <small class="form-text text-danger noti-small" *ngIf="
                      form.get('type')?.errors?.required &&
                      form.get('type')?.touched
                    ">
                    {{ "servicePack.error.required.type" | translate }}
                  </small>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label>{{ "servicePack.carrieType" | translate }}
                    <span class="text-danger" *ngIf="isRequiredCarrieType">*</span></label>
                  <ng-select appAutoValidate appearance="outline" [searchable]="false" formControlName="carrierType"
                    placeholder="{{
                      'servicePack.carrieType' | placeholder : 'select'
                    }}">
                    <ng-option *ngFor="let item of telcos" [value]="item.telcoCode">
                      {{ item.telcoName }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12" *ngIf="action === ROUTER_ACTIONS.detail">
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.role.creator" | translate }}</label>
                  <input trim type="text" disabled class="w-100" class="form-control"
                    [value]="servicePack?.createdBy || ''" />
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.role.fromDate" | translate }}</label>
                  <input trim type="text" [value]="servicePack?.createdDate || ''" disabled class="w-100"
                    class="form-control" />
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.role.updater" | translate }}</label>
                  <input trim type="text" disabled class="w-100" class="form-control"
                    [value]="servicePack?.lastModifiedBy || ''" />
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.role.lastModifiedDate" | translate }}</label>
                  <input trim type="text" disabled [value]="servicePack?.lastModifiedDate || ''" class="w-100"
                    class="form-control" />
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-3" *ngIf="action === ROUTER_ACTIONS.detail">
                <div class="form-group">
                  <label>{{ "common.status" | translate
                    }}<span class="text-danger">*</span></label>
                  <ng-select appearance="outline" [searchable]="false" [clearable]="false" formControlName="status">
                    <ng-option [value]="item.code" *ngFor="let item of ENTITY_STATUS">
                      {{ item.label | translate }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <label>
              {{ "servicePack.listAPI" | translate
              }}<span class="text-danger small-text">*</span></label>
            <div>
              <div formArrayName="servicePackFunctions" class="form-group"
                *ngFor="let group of servicePackFunctions?.controls; index as i">
                <div class="row" [formGroupName]="i">
                  <div class="col-md-1 text-center">
                    <p>{{ i + 1 }}</p>
                  </div>
                  <div class="col-md-4">
                    <input placeholder="{{ 'servicePack.nameAPI' | placeholder }}" type="text" trim
                      [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH" class="form-control w-100"
                      formControlName="name" />
                    <small class="form-text text-danger noti-small" *ngIf="
                        group.get('name')?.errors?.required &&
                        group.get('name')?.touched
                      ">
                      {{ "servicePack.error.required.nameAPI" | translate }}
                    </small>
                    <small class="form-text text-danger noti-small" *ngIf="
                        group.get('name')?.errors?.pattern &&
                        group.get('name')?.touched
                      ">
                      {{ "servicePack.error.pattern.nameAPI" | translate }}
                    </small>
                  </div>
                  <div class="col-md-6">
                    <input placeholder="{{ 'servicePack.url' | placeholder }}" type="text" trim
                      [maxLength]="VALIDATORS.LENGTH.SMALL_TEXT_MAX_LENGTH" class="form-control w-100"
                      formControlName="url" />
                    <small class="form-text text-danger noti-small" *ngIf="
                        group.get('url')?.errors?.required &&
                        group.get('url')?.touched
                      ">
                      {{ "servicePack.error.required.url" | translate }}
                    </small>
                  </div>
                  <div class="col-md-1" *hasPrivileges="
                      SYSTEM_RULES.REQUEST_CUSTOMER_SUPPORT_UPDATE
                    ">
                    <button type="button" ngbTooltip="{{ 'common.action.delete' | translate }}"
                      class="btn px-1 py-0 mt-1" (click)="onDelete(i, group)" *ngIf="action !== ROUTER_ACTIONS.detail">
                      <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div class="">
              <button type="button" class="btn px-1 py-0 mt-1 add-mail" (click)="addServicePackFunction()"
                *ngIf="hiddenButtonAdd()">
                <i class="fa fa-plus-circle" aria-hidden="true"></i>
                {{ "model.customerSupport.add" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="d-block text-center mb-5 mt-4">
          <button type="button" class="btn btn-white mr-2" data-toggle="modal" (click)="backToList()">
            {{ "common.action.back" | translate }}
          </button>
          <ng-container *ngIf="action === ROUTER_ACTIONS.detail">
            <button class="btn btn-red mr-2" (click)="onEdit()" *hasPrivileges="SYSTEM_RULES.SERVICE_PACK_UPDATE">
              {{ "common.action.update" | translate }}
            </button>
          </ng-container>
          <ng-container *hasPrivileges="
              action === ROUTER_ACTIONS.update
                ? SYSTEM_RULES.SERVICE_PACK_UPDATE
                : SYSTEM_RULES.SERVICE_PACK_CREATE
            ">
            <button type="button" *ngIf="!(action === ROUTER_ACTIONS.detail)" class="btn btn-red" (click)="
                action === ROUTER_ACTIONS.update ? onUpdate() : onCreate()
              ">
              {{
              (action === ROUTER_ACTIONS.update
              ? "common.action.update"
              : "common.action.create"
              ) | translate
              }}
            </button>
          </ng-container>
        </div>
      </div>
    </form>
  </div>
</section>