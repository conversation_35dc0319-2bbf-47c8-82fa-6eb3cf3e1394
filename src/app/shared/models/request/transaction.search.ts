import { SearchWithPagination } from '@shared/models/request/base.request.model';

export interface ITransactionSearch extends SearchWithPagination {
  fromDate?: string;
  toDate?: string;
  transactionCode?: string;
  hasPageable?: boolean;
  transferTypes?: [];
  timeZoneStr?: string;
  transactionStatus?: [];
  billingTypes?: [];
  statuses?: [];
  nationCodes?: [];
  isIgnoreInternationalPayment?: boolean;
}
