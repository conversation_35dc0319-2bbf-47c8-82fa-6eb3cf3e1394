import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import {
  BOOLEAN_STRING,
  SERVER_API_URL
} from '@shared/constants/app.constants';
import { HTTP_HEADERS } from '@shared/constants/http.constants';
import { STORAGE_APP, STORAGE_TOKEN } from '@shared/constants/storage.constants';
import { ILogin } from '@shared/models/login.model';
import { IToken } from '@shared/models/response/token.response.model';
import { ROUTER_UTILS } from '@shared/utils';
import jwtDecode, { JwtPayload } from 'jwt-decode';
import { CookieService } from 'ngx-cookie-service';
import { LocalStorageService } from 'ngx-webstorage';
import { Observable, throwError } from 'rxjs';
import { map, tap } from 'rxjs/operators';

@Injectable({ providedIn: 'root' })
export class AuthProviderService {
  constructor(
    private http: HttpClient,
    private $cookie: CookieService,
    private router: Router,
    private localStorageService: LocalStorageService
  ) {}

  login(login: ILogin): Observable<any> {
    const data = {
      username: login.username,
      password: login.password,
      rememberMe: login.rememberMe,
      deviceToken: login.deviceToken,
    };
    if (login?.captcha) {
      return this.http
        .post<IToken>(SERVER_API_URL + '/authenticate/login', data, {
          observe: 'response',
          headers: {
            [HTTP_HEADERS.X_CAPTCHA_HEADER]: login.captcha,
            [HTTP_HEADERS.X_TRANSACTION_ID]: login.transactionId ?? '',
            [HTTP_HEADERS.X_LOADING]: BOOLEAN_STRING.TRUE,
          },
        })
        .pipe(
          map(this.authenticateSuccess.bind(this, login.rememberMe ?? false))
        );
    } else {
      return this.http
        .post<IToken>(SERVER_API_URL + '/authenticate/login', data, {
          observe: 'response',
          headers: {
            [HTTP_HEADERS.X_LOADING]: BOOLEAN_STRING.TRUE,
          },
        })
        .pipe(
          map(this.authenticateSuccess.bind(this, login.rememberMe ?? false))
        );
    }
  }

  logout(): void {
    this.localStorageService.clear(STORAGE_APP.USER_INFO);
    this.http
      .post<any>(
        SERVER_API_URL + '/account/logout',
        {},
        {
          observe: 'response',
          headers: {
            [HTTP_HEADERS.X_LOADING]: BOOLEAN_STRING.TRUE,
          },
        }
      )
      .pipe((res) => {
        this.$cookie.delete(STORAGE_TOKEN.CSRF_TOKEN, '/');
        // this.$cookie.delete(STORAGE_TOKEN.REFRESH_TOKEN, '/');
        this.$cookie.delete(STORAGE_TOKEN.REMEMBER_ME, '/');
        // this.$localStorage.clear(STORAGE_TOKEN.PRINCIPAL);
        return res;
      })
      .subscribe((res) => {
        this.router.navigate([ROUTER_UTILS.auth.login]);
      });
  }

  refreshToken() {
    return this.http
      .post<any>(
        SERVER_API_URL + '/authenticate/refresh-token',
        {},
        {
          headers: {
            [HTTP_HEADERS.X_LOADING]: BOOLEAN_STRING.TRUE,
          },
        }
      )
      .pipe(
        tap(
          (resp) => {
            this.storeAccessToken(resp.token, true);
          },
          (err) => throwError(err)
        )
      );
  }

  tokenDecode(): JwtPayload | undefined {
    const token = this.getToken();
    if (token) {
      return jwtDecode<JwtPayload>(token);
    }
    return undefined;
  }

  getToken(type?: string): string {
    return this.$cookie.get(type ?? STORAGE_TOKEN.CSRF_TOKEN);
  }

  private authenticateSuccess(rememberMe: boolean, resp: any): string {
    const csrfToken = resp.body.token;
    // const principal = resp.headers.get('Authorization');

    this.clearToken();

    this.storeAccessToken(csrfToken, rememberMe);
    // this.$localStorage.store(STORAGE_TOKEN.ACCESS_TOKEN, principal);

    return resp.body;
  }

  private storeAccessToken(csrfToken: string, rememberMe: boolean) {
    this.$cookie.set(STORAGE_TOKEN.CSRF_TOKEN, csrfToken, undefined, '/');
    // this.$cookie.set(STORAGE_TOKEN.REFRESH_TOKEN, refreshToken, undefined, '/');
    this.$cookie.set(
      STORAGE_TOKEN.REMEMBER_ME,
      rememberMe + '',
      undefined,
      '/'
    );
  }

  private clearToken(): void {
    this.$cookie.delete(STORAGE_TOKEN.CSRF_TOKEN, '/');
    // this.$cookie.delete(STORAGE_TOKEN.REFRESH_TOKEN, '/');
    this.$cookie.delete(STORAGE_TOKEN.REMEMBER_ME, '/');

    // this.$localStorage.clear(STORAGE_TOKEN.PRINCIPAL);
  }
}
