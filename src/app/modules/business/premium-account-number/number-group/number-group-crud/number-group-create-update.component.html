<div class="modal-content">
  <div class="modal-header">
    <h5 class="modal-title">
      {{
      action === ROUTER_ACTIONS.detail
      ? ("numberGroup.detailTitle" | translate)
      : action === ROUTER_ACTIONS.update
      ? ("numberGroup.updateTitle" | translate)
      : ("numberGroup.createTitle" | translate)
      }}
    </h5>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="activeModal.close()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="form" *ngIf="numberGroup?.numberGroupId || action === ROUTER_ACTIONS.create">
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-3" *ngIf="action !== ROUTER_ACTIONS.create">
                <div class="form-group">
                  <label>{{ "numberGroup.code" | translate}}</label>
                  <input formControlName="code" type="text" class="w-100 form-control"
                    placeholder="{{ 'numberGroup.code' | placeholder }}" />
                </div>
              </div>
              <div [class]="action === ROUTER_ACTIONS.create ? 'col-md-4' : 'col-md-3'">
                <div class="form-group">
                  <label>{{ "numberGroup.name" | translate
                    }}<span class="text-danger">*</span></label>
                  <input formControlName="name" type="text" class="w-100 form-control"
                    placeholder="{{ 'numberGroup.name' | placeholder }}" [maxLength]="VALIDATORS.LENGTH.TEXT_MAX_LENGTH"
                    appAutoValidate />
                </div>
              </div>
              <div [class]="action === ROUTER_ACTIONS.create ? 'col-md-4' : 'col-md-3'">
                <div class="form-group">
                  <label>{{ "numberGroup.minPrice" | translate
                    }}</label>
                  <div id="text-unit">
                    <input trim numbersOnly formControlName="minPrice" type="text" class="w-100 form-control"
                      placeholder="{{ 'numberGroup.minPrice' | placeholder }}"
                      [maxLength]="VALIDATORS.LENGTH.NUMBER_MAX_LENGTH" (ngModelChange)="changeValidate()"
                      appCurrencyFormat />
                    <div id="text-unit-right">
                      <span>{{ "loanOnline.unit.lak" | translate }}</span>
                    </div>
                  </div>
                  <small class="form-text text-danger noti-small" *ngIf="
                      form.get('minPrice')?.errors?.invalidNumber &&
                      !form.get('minPrice')?.errors?.invalidMax &&
                      form.get('minPrice')?.touched
                    ">
                    {{ "numberGroup.error.required.invalidNumber" | translate }}
                  </small>
                  <small class="form-text text-danger noti-small" *ngIf="
                      form.get('minPrice')?.errors?.invalidMax &&
                      form.get('minPrice')?.touched
                    ">
                    {{ "appError.minPrice" | translate }}
                  </small>
                </div>
              </div>
              <div [class]="action === ROUTER_ACTIONS.create ? 'col-md-4' : 'col-md-3'">
                <div class="form-group">
                  <label>{{ "numberGroup.maxPrice" | translate
                    }}</label>
                  <div id="text-unit">
                    <input trim numbersOnly formControlName="maxPrice" type="text" class="w-100 form-control"
                      placeholder="{{ 'numberGroup.maxPrice' | placeholder }}"
                      [maxLength]="VALIDATORS.LENGTH.NUMBER_MAX_LENGTH" (ngModelChange)="changeValidate()"
                      appCurrencyFormat />
                    <div id="text-unit-right">
                      <span>{{ "loanOnline.unit.lak" | translate }}</span>
                    </div>
                  </div>
                  <small class="form-text text-danger noti-small" *ngIf="
                      form.controls.maxPrice.errors?.invalidMax &&
                      form.controls.maxPrice.touched
                    ">
                    {{ "appError.maxPrice" | translate }}
                  </small>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12" *ngIf="action === ROUTER_ACTIONS.detail">
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.role.creator" | translate }}</label>
                  <input trim type="text" disabled class="w-100" class="form-control"
                    [value]="numberGroup?.createdBy || ''" />
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.role.fromDate" | translate }}</label>
                  <input trim type="text" [value]="numberGroup?.createdDate || ''" disabled class="w-100"
                    class="form-control" />
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.role.updater" | translate }}</label>
                  <input trim type="text" disabled class="w-100" class="form-control"
                    [value]="numberGroup?.lastModifiedBy || ''" />
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.role.lastModifiedDate" | translate }}</label>
                  <input trim type="text" disabled [value]="numberGroup?.lastModifiedDate || ''" class="w-100"
                    class="form-control" />
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-12">
                <div class="form-group">
                  <label>{{
                    "model.role.description"
                    | translate
                    }} <span class="text-danger">*</span></label>
                  <textarea class="w-100 form-control" rows="5" formControlName="description" placeholder="{{
                    'model.role.description'
                      | placeholder
                  }}" [maxLength]="VALIDATORS.LENGTH.MEDIUM_TEXT_MAX_LENGTH" appAutoValidate></textarea>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12" *ngIf="hasFilter || isShowButton">
            <div class="row">
              <div class="col-md-12">
                <div class="warning-mess" [innerHTML]="textWarningChange()"></div>
                <div class="form-group">
                  <mat-checkbox formControlName="hided" (click)="checkHided()" class="check-box-group px-0 py-0 mt-2">
                  </mat-checkbox>
                  <label class="px-2 py-0 mt-2">{{
                    "numberGroup.message.understand"
                    | translate
                    }}</label>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </form>
  </div>
  <div class="text-center pb-4">
    <button type="button" class="btn mb-btn-outline-color mr-3" (click)="activeModal.close()">
      {{ "common.action.cancel" | translate }}
    </button>
    <ng-container *hasPrivileges="SYSTEM_RULES.NUMBER_GROUP_UPDATE">
      <button type="button" *ngIf="action === ROUTER_ACTIONS.detail" class="btn mb-btn-color mr-3"
        (click)="actionOnUpdate()">
        {{ "common.action.update" | translate }}
      </button>
    </ng-container>
    <ng-container *hasPrivileges="
        action === ROUTER_ACTIONS.update
          ? SYSTEM_RULES.NUMBER_GROUP_UPDATE
          : SYSTEM_RULES.NUMBER_GROUP_CREATE
      ">
      <button *ngIf="
          action === ROUTER_ACTIONS.update || action === ROUTER_ACTIONS.create
        " type="button" class="btn mb-btn-color" (click)="action === ROUTER_ACTIONS.create ? onCreate() : onUpdate()"
        [disabled]="hasFilter">
        {{
        (action === ROUTER_ACTIONS.update
        ? "common.action.update"
        : "common.action.add"
        ) | translate
        }}
      </button>
    </ng-container>
  </div>
</div>