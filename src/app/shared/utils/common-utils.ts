import {
  AbstractControl,
  FormArray,
  FormControl,
  FormGroup,
  ValidatorFn,
} from '@angular/forms';
// import { PAGINATION } from '@angular/material/paginator';
import { MOMENT_CONST, PAGINATION } from '@shared/constants/app.constants';
import * as _ from 'lodash';
import * as moment from 'moment';
import { Observable, Observer } from 'rxjs';
import { DATE_RANGE_STATISTICAL } from '../constants/app.constants';

export type FileLoadErrorType = 'not.image' | 'could.not.extract';

export interface FileLoadError {
  message: string;
  key: FileLoadErrorType;
  params?: any;
}

/**
 * CommonUtils class
 *
 * <AUTHOR>
 * @date 2021-12-24
 * @export CommonUtils
 * @class CommonUtils
 */
export default class CommonUtils {
  /**
   * Optimal object
   *
   * @param object any | any[]
   * @returns any | any[]
   */
  static optimalObjectParams = (object?: any | any[]): any | any[] => {
    if (object) {
      if (object instanceof FormData) {
        const formData = new FormData();
        for (let [key, value] of object) {
          if (value !== undefined && value !== null && value !== '') {
            if (typeof value === 'string') {
              value = value.trim();
              if (value === '') {
                continue;
              }
              // check string type date match YYYY-MM-DD then format reverse DD-MM-YYYY
              if (value && value.match(/^\d{4}\-\d{2}\-\d{2}$/)) {
                value = CommonUtils.reverseDate(value);
              }
            }
            formData.append(key, value);
          }
        }
        return formData;
      } else if (Array.isArray(object)) {
        object.forEach((item) => CommonUtils.optimalObjectParams(item));
      } else if (object instanceof Date) {
        return CommonUtils.formatDate(object);
      } else if (typeof object === 'string') {
        object = object.replace(/ + /g, ' ').trim();
      } else {
        Object.keys(object).forEach((k) => {
          // else if (object[k] instanceof Date) {
          //   object[k] = CommonUtils.formatDate(object[k]);
          // }
          if (
            object[k] === null ||
            object[k] === undefined ||
            object[k] === ''
          ) {
            delete object[k];
          } else if (
            typeof object[k] === 'object' ||
            Array.isArray(object[k])
          ) {
            object[k] = CommonUtils.optimalObjectParams(object[k]);
          } else if (typeof object[k] === 'string') {
            object[k] = object[k].replace(/ + /g, ' ').trim();
            if (object[k] === '') {
              delete object[k];
            }
            // check string type date match YYYY-MM-DD then format reverse DD-MM-YYYY
            if (object[k] && object[k].match(/^\d{4}\-\d{2}\-\d{2}$/)) {
              object[k] = CommonUtils.reverseDate(object[k]);
            }
          }
        });
      }
    }

    return object;
  };
  // static optimalObjectParams = (object?: any | any[]): any | any[] => {
  //   if (object) {
  //     if (object instanceof FormData) {
  //       const formData = new FormData();
  //       for (let [key, value] of object) {
  //         if (value !== undefined && value !== null && value !== '') {
  //           if (typeof value === 'string') {
  //             value = value.trim();
  //             if (value === '') {
  //               continue;
  //             }
  //             if (value && value.match(/^\d{4}\-\d{2}\-\d{2}$/)) {
  //               value = CommonUtils.reverseDate(value);
  //             }
  //           }
  //           formData.append(key, value);
  //         }
  //       }
  //       return formData;
  //     } else if (Array.isArray(object)) {
  //       object.forEach((item) => CommonUtils.optimalObjectParams(item));
  //     } else if (typeof object === 'string') {
  //       object = object.replace(/ + /g, ' ').trim();
  //     } else if (typeof object === 'object') {
  //       Object.keys(object).forEach((k) => {
  //         if (
  //           object[k] === null ||
  //           object[k] === undefined ||
  //           object[k] === ''
  //         ) {
  //           delete object[k];
  //         } else if (
  //           typeof object[k] === 'object' ||
  //           Array.isArray(object[k])
  //         ) {
  //           CommonUtils.optimalObjectParams(object[k]);
  //         } else if (typeof object[k] === 'string') {
  //           object[k] = object[k].trim();
  //           if (object[k] === '') {
  //             delete object[k];
  //           }
  //           if (object[k] && object[k].match(/^\d{4}\-\d{2}\-\d{2}$/)) {
  //             object[k] = CommonUtils.reverseDate(object[k]);
  //           }
  //         }
  //       });
  //     }
  //   }
  //   return object;
  // };

  /**
   * objectIsEmpty
   *
   * @param object any
   * @returns boolean
   */
  static objectIsEmpty(object: any): boolean {
    if (
      object === undefined ||
      object === null ||
      object === {} ||
      Object.keys(object)?.length === 0
    ) {
      return true;
    }

    for (const key of Object.keys(CommonUtils.optimalObjectParams(object))) {
      if (
        object[key] != null &&
        object[key] !== undefined &&
        object[key] !== ''
      ) {
        return false;
      }
    }
    return true;
  }

  static getIndex(
    index: number,
    page: number = PAGINATION.PAGE_NUMBER_DEFAULT,
    size: number = PAGINATION.PAGE_SIZE_DEFAULT
  ): number {
    return page * size + index + 1;
  }

  /**
   *
   *
   * <AUTHOR>
   * @date 2021-12-24
   * @static
   * @param {string} fieldName
   * @param {*} data
   * @param {FormData} [_formData]
   * @returns {FormData}
   * @memberof CommonUtils
   */
  static appendDataToFormData = (
    fieldName: string,
    data: any,
    _formData?: FormData
  ): FormData => {
    const formData = _formData ? _formData : new FormData();
    if (data !== null && data !== undefined) {
      if (Array.isArray(data)) {
        if (data.length > 0) {
          CommonUtils.appendArrayToFormData(fieldName, data, formData);
        }
      } else if (typeof data === 'object') {
        CommonUtils.appendObjectToFormDataWithFieldName(
          fieldName,
          data,
          formData
        );
      } else if (typeof data === 'string') {
        if (data.trim().length > 0) {
          formData.append(fieldName, data.trim());
        }
      } else {
        formData.append(fieldName, data);
      }
    }
    return formData;
  };

  static appendRawDataToFormData = (
    fieldName: string,
    data: any,
    formData: FormData
  ): void => {
    if (data !== null && data !== undefined) {
      if (Array.isArray(data)) {
        if (data.length > 0) {
          CommonUtils.appendRawArrayToFormData(fieldName, data, formData);
        }
      } else if (typeof data === 'object') {
        CommonUtils.appendRawObjectToFormData(fieldName, data, formData);
      } else if (typeof data === 'string') {
        if (data.trim().length > 0) {
          formData.append(fieldName, data);
        }
      } else {
        formData.append(fieldName, data);
      }
    }
  };

  static markFormGroupTouched = (form: FormGroup): void => {
    Object.values(form?.controls).forEach((control) => {
      control.markAsTouched();

      if ((control as any).controls) {
        CommonUtils.markFormGroupTouched(control as FormGroup);
      }
    });
  };

  static markFormGroupTouchedArray = (form: FormArray): void => {
    form.controls.forEach((group: any) => {
      group.markAsTouched();
      if ((group as any).controls) {
        CommonUtils.markFormGroupTouched(group as FormGroup);
      }
    });
  };

  static limitWord = (stringRaw: string, numberLimit: number): string => {
    let stringLimit = '';
    if (stringRaw) {
      if (stringRaw.length <= numberLimit) {
        stringLimit = stringRaw;
      } else {
        stringLimit = stringRaw.slice(0, numberLimit) + '...';
      }
    }
    return stringLimit;
  };

  static linkVideoCkeditorToLinkEmbedYoutube = (element: any): any => {
    if (element !== null && element !== undefined) {
      let videoLink = null;
      videoLink = element
        .slice(
          element.indexOf('<oembed url="') + 13,
          element.indexOf('"></oembed>')
        )
        .replace('&amp;', '?')
        .replace('&amp;', '?')
        .replace('watch?v=', 'embed/');
      if (videoLink.indexOf('embed/') === -1) {
        videoLink = CommonUtils.insertStr(
          videoLink,
          videoLink.indexOf('youtu.be/') + 'youtu.be/'.length,
          'embed/'
        ).replace('//youtu.be/', '//www.youtube.com/');
      }
      return videoLink;
    }
  };

  static insertStr = (str: string, index: number, value: string): string => {
    return str.substring(0, index) + value + str.substring(index);
  };

  // static overridePaginatorIntl = (): MatPaginatorIntl => {
  //   const paginatorIntl = new MatPaginatorIntl();
  //   paginatorIntl.itemsPerPageLabel = 'Hiển thị:';
  //   return paginatorIntl;
  // };

  static encodeURI = (value: string): string => {
    let returnStr = encodeURI(value);
    returnStr = returnStr.replace('(', '%28');
    returnStr = returnStr.replace(')', '%29');

    return returnStr;
  };

  /**
   * Check all item on list
   *
   * <AUTHOR>
   * @date 2021-12-24
   * @static
   * @param {any[]} listIds danh sách id
   * @param {any[]} list danh sách item
   * @param {boolean} [checked] true - check, false | null - uncheck
   * @param {boolean} [getIdsOnPage] có trả về danh sách id tại trang hiện tại hay không
   * @returns {*}
   * @memberof CommonUtils
   */
  static checkAllItemOnLists = (
    listIds: any[],
    list: any[],
    checked?: boolean,
    getIdsOnPage?: boolean
  ): any => {
    // initial array
    const listCheckedArrays = listIds;
    const dataArrays = list.map((value) => value.id);
    const dataMap: { [x: string]: any } = {};

    // let variable temp
    let isCheckAllLists = false;
    let disableBtnDeleteAll = false;

    // initial value default
    listCheckedArrays.forEach((i) => (dataMap[i] = false));
    dataArrays.forEach((i) => dataMap[i] === false && (dataMap[i] = true));

    // result after checked
    const resultArray = Object.keys(dataMap).map((k) => ({
      id: +k,
      matched: dataMap[k],
    }));

    // result final
    const listIdsOnPage = resultArray
      .filter((value) => value.matched === true)
      .map((data) => data.id);

    if (getIdsOnPage) {
      return listIdsOnPage;
    }

    if (checked) {
      list.forEach((item) => {
        listIdsOnPage.forEach((id) => {
          if (item.id === id) {
            item.checked = true;
            return;
          }
        });
      });
      isCheckAllLists = dataArrays.length === listIdsOnPage.length;
      if (listIdsOnPage.length === 0 && dataArrays.length === 0) {
        isCheckAllLists = false;
      }
      disableBtnDeleteAll = listIdsOnPage.length <= 0;
      return {
        check: isCheckAllLists,
        disable: disableBtnDeleteAll,
      };
    } else {
      listIdsOnPage.forEach((id) => {
        listIds.splice(listIds.indexOf(id), 1);
      });
      isCheckAllLists = false;
      disableBtnDeleteAll = true;
      return {
        check: isCheckAllLists,
        disable: disableBtnDeleteAll,
      };
    }
  };

  /**
   *
   *
   * <AUTHOR>
   * @date 2021-12-24
   * @static
   * @param {any[]} list danh sách item
   * @param {number} ids id sẽ được check/uncheck
   * @param {boolean} checked true - check, false - uncheck
   * @memberof CommonUtils
   */
  static checkItemsOnList = (
    list: any[] = [],
    ids: number[] = [],
    checked: boolean
  ): void => {
    list
      .filter((item) => ids.includes(item.id))
      ?.map((item) => (item.checked = checked));
  };

  /**
   * Convert text from VI to EN
   *
   * @static
   * @memberof CommonUtils
   */
  static cleanAccents = (str: string): string => {
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
    str = str.replace(/đ/g, 'd');
    str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, 'A');
    str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, 'E');
    str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, 'I');
    str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, 'O');
    str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, 'U');
    str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, 'Y');
    str = str.replace(/Đ/g, 'D');
    // Combining Diacritical Marks
    str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ''); // huyền, sắc, hỏi, ngã, nặng
    str = str.replace(/\u02C6|\u0306|\u031B/g, ''); // mũ â (ê), mũ ă, mũ ơ (ư)
    // Remove extra spaces
    // Bỏ các khoảng trắng liền nhau
    str = str.replace(/ + /g, ' ');
    str = str.trim();
    // Remove punctuations
    // Bỏ dấu câu, kí tự đặc biệt
    str = str.replace(
      /!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g,
      ' '
    );

    return str;
  };

  /**
   * In hoa đầu mỗi chữ
   *
   * @static
   * @memberof CommonUtils
   */
  static toTitleCase = (str: string): string => {
    return str.replace(
      /\w\S*/g,
      (txt) => txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase()
    );
  };

  static toFirstCharacterCase = (str: string): string => {
    return str
      .split(' ')
      .map((item) => item.charAt(0).toUpperCase())
      .join('');
  };

  static dayOffInCurrentYear = (): any[] => {
    const currentYear = moment(new Date()).format('YYYY');
    const configHolidays = [
      { month: 1, day: 1 },
      { month: 4, day: 30 },
      { month: 5, day: 1 },
      { month: 9, day: 2 },
    ];
    const holidays: any[] = [];
    configHolidays.forEach((val) => {
      holidays.push(
        moment(new Date(Number(currentYear), val.month - 1, val.day)).format(
          'YYYY-MM-DD'
        )
      );
    });
    return holidays;
  };

  /**
   * Method to find the byte size of the string provides
   */
  static byteSize = (base64String: string): string => {
    return CommonUtils.formatAsBytes(CommonUtils.size(base64String));
  };

  /**
   * Method to open file
   */
  static openFile = (
    data: string,
    contentType: string | null | undefined
  ): void => {
    contentType = contentType ?? '';

    const byteCharacters = atob(data);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], {
      type: contentType,
    });
    const fileURL = window.URL.createObjectURL(blob);
    const win = window.open(fileURL);
    if (win) {
      win.onload = () => {
        URL.revokeObjectURL(fileURL);
      };
    }
  };

  /**
   * Sets the base 64 data & file type of the 1st file on the event (event.target.files[0]) in the passed entity object
   * and returns an observable.
   *
   * @param event the object containing the file (at event.target.files[0])
   * @param editForm the form group where the input field is located
   * @param field the field name to set the file's 'base 64 data' on
   * @param isImage boolean representing if the file represented by the event is an image
   * @returns an observable that loads file to form field and completes if sussessful
   *      or returns error as FileLoadError on failure
   */
  static loadFileToForm = (
    event: Event,
    editForm: FormGroup,
    field: string,
    isImage: boolean
  ): Observable<void> => {
    return new Observable((observer: Observer<void>) => {
      const eventTarget: HTMLInputElement | null =
        event.target as HTMLInputElement | null;
      if (eventTarget?.files?.[0]) {
        const file: File = eventTarget.files[0];
        if (isImage && !file.type.startsWith('image/')) {
          const error: FileLoadError = {
            message: `File was expected to be an image but was found to be '${file.type}'`,
            key: 'not.image',
            params: { fileType: file.type },
          };
          observer.error(error);
        } else {
          const fieldContentType: string = field + 'ContentType';
          CommonUtils.toBase64(file, (base64Data: string) => {
            editForm.patchValue({
              [field]: base64Data,
              [fieldContentType]: file.type,
            });
            observer.next();
            observer.complete();
          });
        }
      } else {
        const error: FileLoadError = {
          message: 'Could not extract file',
          key: 'could.not.extract',
          params: { event },
        };
        observer.error(error);
      }
    });
  };

  static covertToSelectOptionData = (
    data: any[],
    bindValueId?: any,
    bindValueText?: any
  ): any => {
    if (data && data.length > 0) {
      data.forEach((d) => {
        d.id = bindValueId ? d[bindValueId] : d.id;
        d.text = bindValueText ? d[bindValueText] : d.text;
        d.text = d.text ? d.text : d.id;
      });
    }
    return data;
  };

  static covertToSelectOptionDataArea = (
    data: any[],
    bindValueId?: any,
    bindValueText?: any,
    latitudeValueText?: any,
    longitudeValueText?: any
  ): any => {
    if (data && data.length > 0) {
      data.forEach((d) => {
        d.id = bindValueId ? d[bindValueId] : d.id;
        d.text = bindValueText ? d[bindValueText] : d.text;
        d.latitude = latitudeValueText ? d[latitudeValueText] : d.latitude;
        d.longitude = longitudeValueText ? d[longitudeValueText] : d.longitude;
      });
    }
    return data;
  };

  static translateRelationshipName = (value: string): string => {
    if (value === null) {
      return '';
    }
    const name = value.toUpperCase();
    if (name === 'ME') {
      return 'Tôi';
    }
    if (name === 'FATHER') {
      return 'Bố';
    }
    if (name === 'MOTHER') {
      return 'Mẹ';
    }
    if (name === 'GRANDFATHER') {
      return 'Ông';
    }
    if (name === 'GRANDMOTHER') {
      return 'Bà';
    }
    if (name === 'WIFE') {
      return 'Vợ';
    }
    if (name === 'HUSBAND') {
      return 'Chồng';
    }
    if (name === 'SON') {
      return 'Con trai';
    }
    if (name === 'DAUGHTER') {
      return 'Con gái';
    }
    if (name === 'OLDER_BROTHER') {
      return 'Anh';
    }
    if (name === 'OLDER_SISTER') {
      return 'Chị';
    }
    if (name === 'YOUNGER_BROTHER') {
      return 'Em trai';
    }
    if (name === 'YOUNGER_SISTER') {
      return 'Em gái';
    }
    if (name === 'UNCLE') {
      return 'Cậu/Chú/Bác trai';
    }
    if (name === 'AUNT') {
      return 'Cô/Dì/Bác gái';
    }
    if (name === 'OTHER') {
      return 'khác';
    }
    return name;
  };

  static validateNumericKeyPress = (event: any): void => {
    const pattern = /[0-9]/;
    const inputChar = event.key;

    if (!pattern.test(inputChar)) {
      // invalid character, prevent input
      event.preventDefault();
    }
  };

  static removeComma(data: any): any {
    Object.keys(data).forEach(
      (k) =>
        (data[k] =
          typeof data[k] === 'string' ? data[k].replace(/,/gi, '') : data[k])
    );

    return data;
  }

  static moneyFormat(value: string): string {
    return value
      .toString()
      .replace(/\D/g, '')
      .replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  }

  static moneyFormatNumber(value: number): string {
    return value
      .toString()
      .replace(/\D/g, '')
      .replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  }

  static stripHTML(htmlText: any): string {
    return htmlText.replace(/<\/?[^>]+(>|$)/g, '');
  }

  static fillIndexItem(
    index: number,
    page: number = PAGINATION.PAGE_NUMBER_DEFAULT,
    size: number = PAGINATION.PAGE_SIZE_DEFAULT
  ): number {
    return page * size + index + 1;
  }

  /**
   * convert Blob to base64
   *
   * @param blob any
   * @returns blobToBase64
   */
  static blobToBase64(blob: any) {
    return new Promise((resolve, _) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.readAsDataURL(blob);
    });
  }

  /**
   * Reverse date format
   *
   * @param date
   * @returns
   */
  static reverseDate(date?: string): string {
    if (date) {
      const reverseDateDMY = moment(
        date,
        MOMENT_CONST.FORMAT_DEFAULT
      ).isValid();
      const reverseDateYMD = moment(date, MOMENT_CONST.FORMAT_YEAR).isValid();
      if (reverseDateDMY || reverseDateYMD) {
        return date.split('-').reverse().join('-');
      }
      return date;
    }
    return '';
  }

  /**
   * Reverse date format
   *
   * @param date
   * @returns
   */
  static reverseDateTimeZone(date?: string): string {
    if (date) {
      const reverseDateDMY = moment(
        date.substring(0, 10),
        MOMENT_CONST.FORMAT_DEFAULT
      ).isValid();
      if (reverseDateDMY) {
        return (
          date.substring(0, 10).split('-').reverse().join('-') +
          date.substring(10, 19)
        );
      }
      return date;
    }
    return '';
  }

  /**
   * Method trim object
   */
  static trim(obj: any): object {
    return Object.keys(obj).reduce((acc: any, curr: string) => {
      if (typeof obj[curr] === 'string') {
        acc[curr] = obj[curr].trim();
      } else {
        acc[curr] = obj[curr];
      }
      return acc;
    }, {});
  }

  static splitDate(str: string): string {
    const date = str.split('/');
    return date[1] + '/' + date[0] + '/' + date[2];
  }

  /** @deprecated  */
  static newDate(dateValue: string): Date {
    const date = dateValue.trim();
    if (date.includes('/')) {
      return new Date(this.splitDate(date));
    } else if (date.length === 8 || date.length === 7) {
      if (date.length === 8) {
        const day = date.slice(0, 2);
        const month = date.slice(2, 4);
        const year = date.slice(4, 8);
        return new Date(day + '/' + month + '/' + year);
      } else if (date.length === 7) {
        const dayFirst = date.slice(0, 2);
        const daySecond = date.slice(0, 1);
        const monthFirst = date.slice(2, 3);
        const monthSecond = date.slice(1, 3);
        const year = date.slice(3, 7);

        const dateFirst = new Date(dayFirst + '/' + monthFirst + '/' + year);
        const dateSecond = new Date(daySecond + '/' + monthSecond + '/' + year);

        if (dateFirst.toString() !== 'Invalid Date') {
          return dateFirst;
        } else if (dateSecond.toString() !== 'Invalid Date') {
          return dateSecond;
        } else {
          return new Date(date);
        }
      } else {
        return new Date(date);
      }
    } else {
      return new Date(date);
    }
  }

  static stringToDate(date?: string): Date | undefined {
    if (!date) {
      return undefined;
    }
    const newDate = CommonUtils.newDate(date);
    if (newDate?.toString() === 'Invalid Date') {
      return undefined;
    }
    return newDate;
  }

  /**
   *
   * ==================================================================== Private method ====================================================================
   */

  private static appendRawObjectToFormData = (
    fieldName: string,
    data: any,
    formData: FormData
  ): void => {
    Object.keys(data).forEach((key) => {
      CommonUtils.appendRawDataToFormData(
        fieldName + '[' + key + ']',
        data[key],
        formData
      );
    });
  };

  private static appendRawArrayToFormData = (
    fieldName: string,
    array: any,
    formData: FormData
  ): void => {
    array?.forEach((value: any, i: number) => {
      if (value !== null && value !== undefined) {
        if (typeof value === 'object') {
          CommonUtils.appendRawSingleArrayObjectToFormData(
            fieldName,
            value,
            i,
            formData
          );
        } else if (typeof value === 'string') {
          if (value.trim().length > 0) {
            formData.append(fieldName + '[' + i + ']', value);
          }
        } else {
          formData.append(fieldName + '[' + i + ']', value);
        }
      }
    });
  };

  private static appendRawSingleArrayObjectToFormData = (
    fieldName: string,
    data: any,
    index: number,
    formData: FormData
  ): void => {
    Object.keys(data).forEach((key) => {
      const value = data[key];
      if (value !== null && value !== undefined) {
        if (typeof value === 'object') {
          CommonUtils.appendRawObjectToFormData(
            fieldName + '[' + index + '][' + key + ']',
            value,
            formData
          );
        } else if (typeof value === 'string') {
          if (value.trim().length > 0) {
            formData.append(fieldName + '[' + index + '][' + key + ']', value);
          }
        } else {
          formData.append(fieldName + '[' + index + '][' + key + ']', value);
        }
      }
    });
  };

  static appendObjectToFormData = (data: any, formData: FormData) => {
    if (data) {
      if (Array.isArray(data)) {
        data.forEach((value) => {
          CommonUtils.appendObjectToFormData(value, formData);
        });
      } else if (typeof data === 'object') {
        Object.keys(data).forEach((key) => {
          if (
            data[key] !== null &&
            data[key] !== undefined &&
            data[key] !== ''
          ) {
            if (typeof data[key] === 'string') {
              data[key].trim();
              if (data[key].match(/^\d{4}\-\d{2}\-\d{2}$/)) {
                data[key] = CommonUtils.reverseDate(data[key]);
              }
              formData.append(key, data[key]);
            } else if (data[key] instanceof File) {
              formData.append(key, data[key]);
            } else if (data[key] instanceof Date) {
              formData.append(key, CommonUtils.formatDate(data[key]));
            } else if (
              typeof data[key] === 'object' &&
              !(data[key] instanceof Date)
            ) {
              if (CommonUtils.checkFilesArray(data[key])) {
                data[key]?.forEach((file: any) => {
                  formData.append(key, file);
                });
              } else {
                if (Array.isArray(data[key])) {
                  data[key].forEach((array: any) => {
                    if (
                      typeof array === 'number' ||
                      typeof array === 'string'
                    ) {
                      formData.append(key, array as any);
                    }
                  });
                }
                CommonUtils.appendObjectToFormData(data[key], formData);
              }
            } else if (Array.isArray(data[key])) {
              data[key].forEach((dataArray: any) => {
                if (dataArray instanceof File) {
                  formData.append(key, dataArray);
                } else {
                  CommonUtils.appendObjectToFormData(dataArray, formData);
                }
              });
            } else {
              formData.append(key, data[key]);
            }
          }
        });
      }
    }
    return formData;
  };

  static getStartOfDay(milliseconds: number): number {
    const start = new Date(milliseconds);
    return this.setStartTime(start.getTime());
  }

  static getEndOfDay(milliseconds: number): number {
    const end = new Date(milliseconds);
    return this.setEndTime(end.getTime());
  }

  static setStartTime(milliseconds: number): number {
    return moment(milliseconds).startOf('day').valueOf();
  }

  static setEndTime(milliseconds: number): number {
    return moment(milliseconds).endOf('day').valueOf();
  }

  public static formatDate(date: Date, displayFormat?: any): string {
    return moment(date).format(displayFormat || 'DD-MM-YYYY');
  }

  public static isValidMaxDate = (control: FormControl) => {
    const dateFormControl = new Date(control.value).getTime();
    const maxDate = new Date().getTime();
    return dateFormControl > maxDate ? { invalidMaxDate: true } : null;
  };

  public static dateValidator(value: number): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return value > DATE_RANGE_STATISTICAL ? { invalidDate: true } : null;
    };
  }

  public static resolveRequestBody<T>(body: T): T {
    if (body) {
      this.convertLocalDates(body);
    }
    return _.cloneDeep(body);
  }

  private static convertLocalDates<T>(body: T): T {
    if (body) {
      this.converseLocalDates(body);
    }
    return body;
  }

  private static converseLocalDates(body: any): void {
    if (body) {
      if (body instanceof Array) {
        body.forEach((item) => {
          this.converseLocalDates(item);
        });
      } else if (body instanceof Object) {
        Object.keys(body).forEach((key) => {
          if (body[key] instanceof Date) {
            body[key] = body[key].toLocaleDateString('fr-CA');
          } else {
            this.converseLocalDates(body[key]);
          }
        });
      }
    }
  }

  private static checkFilesArray(arrayFiles: any): boolean {
    let isFileArray = true;
    Object.values(arrayFiles).forEach((file: any) => {
      if (!(file instanceof File)) {
        isFileArray = false;
      }
    });
    return isFileArray;
  }

  private static appendObjectToFormDataWithFieldName = (
    fieldName: string,
    data: any,
    formData: FormData
  ): void => {
    Object.keys(data).forEach((key) => {
      CommonUtils.appendDataToFormData(
        fieldName + '[' + key + ']',
        data[key],
        formData
      );
    });
  };

  private static appendArrayToFormData = (
    fieldName: string,
    array: any,
    formData: FormData
  ): void => {
    array?.forEach((value: any, i: number) => {
      if (value !== null && value !== undefined) {
        if (typeof value === 'object') {
          CommonUtils.appendSingleArrayObjectToFormData(
            fieldName,
            value,
            i,
            formData
          );
        } else if (typeof value === 'string') {
          if (value.trim().length > 0) {
            formData.append(fieldName + '[' + i + ']', value.trim());
          }
        } else {
          formData.append(fieldName + '[' + i + ']', value);
        }
      }
    });
  };

  private static appendSingleArrayObjectToFormData = (
    fieldName: string,
    data: any,
    index: number,
    formData: FormData
  ): void => {
    Object.keys(data).forEach((key) => {
      const value = data[key];
      if (value !== null && value !== undefined) {
        if (typeof value === 'object') {
          CommonUtils.appendObjectToFormDataWithFieldName(
            fieldName + '[' + index + '][' + key + ']',
            value,
            formData
          );
        } else if (typeof value === 'string') {
          if (value.trim().length > 0) {
            formData.append(
              fieldName + '[' + index + '][' + key + ']',
              value.trim()
            );
          }
        } else {
          formData.append(fieldName + '[' + index + '][' + key + ']', value);
        }
      }
    });
  };

  /**
   * Method to convert the file to base64
   */
  private static toBase64 = (
    file: File,
    callback: (base64Data: string) => void
  ): void => {
    const fileReader: FileReader = new FileReader();
    fileReader.onload = (e: ProgressEvent<FileReader>) => {
      if (typeof e.target?.result === 'string') {
        const base64Data: string = e.target.result.substring(
          e.target.result.indexOf('base64,') + 'base64,'.length
        );
        callback(base64Data);
      }
    };
    fileReader.readAsDataURL(file);
  };

  private static endsWith = (suffix: string, str: string): boolean => {
    return str.includes(suffix, str.length - suffix.length);
  };

  private static paddingSize = (value: string): number => {
    if (CommonUtils.endsWith('==', value)) {
      return 2;
    }
    if (CommonUtils.endsWith('=', value)) {
      return 1;
    }
    return 0;
  };

  private static size = (value: string): number => {
    return (value.length / 4) * 3 - CommonUtils.paddingSize(value);
  };

  private static formatAsBytes = (size: number): string => {
    return size.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ' ') + ' bytes';
  };

  public static getDateString(dateTime: string) {
    const [dateValues, timeValues] = dateTime.split(' ');
    const [day, month, year] = dateValues.split('-');
    const [hours, minutes, seconds] = timeValues.split(':');

    return moment(
      new Date(+year, +month - 1, +day, +hours, +minutes, +seconds)
    ).format(MOMENT_CONST.DATE_TIME_MOMENT_FORMAT);
  }

  public static getValueFormString(content: string): number {
    const split = content.match(/\d+/);
    if (split) {
      return Number(split[0]);
    }
    return 0;
  }

  public static checkFormGroupInvalid(
    formArray: FormArray,
    controlName: string
  ) {
    return formArray.controls.find((item) => {
      return !item.get(controlName)?.errors?.required && item.invalid;
    });
  }

  public static getDateStringISO(dateTime: string): any {
    const [dateValues, timeValues] = dateTime.split(' ');
    const [day, month, year] = dateValues.split('-');
    const [hours, minutes] = timeValues.split(':');
    const releaseTime = moment(
      new Date(+year, +month - 1, +day, +hours, +minutes)
    ).format(MOMENT_CONST.DATE_TIME_MOMENT_FORMAT);
    return releaseTime;
  }

  public static checkFormGroupRequired(
    formArray: FormArray,
    controlName: string
  ) {
    return formArray.controls.find((item) => {
      return item.get(controlName)?.errors?.required;
    });
  }

  public static replaceString(value?: string): number {
    if (value) {
      return +value.replace(/[^0-9]*/g, '');
    }
    return 0;
  }

  public static formatWithThousandSeparator(value: string | number | null): string {
    if (value === null || value === undefined) {
      return '';
    }

    const strValue = value.toString();

    // Tách phần nguyên và phần thập phân
    const [integerPart, decimalPart] = strValue.split('.');

    // Format phần nguyên bằng dấu phẩy
    const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    // Gộp lại nếu có phần thập phân
    return decimalPart !== undefined
      ? `${formattedInteger}.${decimalPart}`
      : formattedInteger;
  }

  public static parseNumberFromFormattedString(value: string | number): number {
    if (value === null || value === undefined) {
      return NaN;
    }

    const strValue = value.toString().replace(/,/g, '');

    const parsed = parseFloat(strValue);

    return isNaN(parsed) ? NaN : parsed;
  }
}
