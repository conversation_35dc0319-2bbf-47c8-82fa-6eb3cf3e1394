.modal-header {
  background: var(--mb-color);
  min-height: 45px;
  align-items: center;
}

.modal-title {
  color: #fff;
  word-wrap: break-word;
  width: calc(100% - 40px)
}

.close {
  padding: 9px 14px;
  font-size: 20px;
  color: white;
  opacity: 1;
  width: 40px !important;
}

.btn-secondary {
  border: 1px solid #eb2d4b;
  background: white;
  color: #eb2d4b;
  border-radius: 8px;
  font-size: 14px;
  padding: 8px 25px;
}

.btn-danger {
  background: #eb2d4b;
  border-color: #eb2d4b;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  padding: 8px 25px;
}

.modal-body h3 {
  font-weight: 500;
  font-size: 20px;
}

.modal-body p {
  color: #282828;
  font-size: 16px;
  opacity: 0.5;
}

.modal-body {
  padding: 24px 24px 18px 24px;
}

.preventLongTextFix {
  overflow-wrap: break-word;
}
