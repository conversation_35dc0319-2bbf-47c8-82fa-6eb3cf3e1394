<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="searchForm">
        <div class="row">
          <div class="col-md-4 col-lg-3">
            <div class="form-group">
              <label>{{ "model.role.name" | translate }}</label>
              <input
                trim
                formControlName="name"
                placeholder="{{ 'model.role.name' | translate }}"
                type="text"
                class="w-100"
                class="form-control"
              />
            </div>
          </div>
          <div class="col-md-4 col-lg-3">
            <div class="form-group">
              <label>{{ "common.status" | translate }}</label>
              <ng-select
                appearance="outline"
                [searchable]="false"
                placeholder="{{ 'common.appSelectOption.status' | translate }}"
                [clearable]="true"
                formControlName="status"
              >
                <ng-option
                  [value]="item.code"
                  *ngFor="let item of ENTITY_STATUS"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
            >
              <div class="btn-reset">
                <i
                  class="bi bi-arrow-clockwise"
                  type="button"
                  (click)="onReset()"
                >
                </i>
              </div>
            </div>
            <div class="col-md-8">
              <button
                class="btn btn-search mr-2"
                (click)="onSearch()"
                *hasPrivileges="SYSTEM_RULES.ROLE_READ"
              >
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
          <hr />
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-3 mt-4">
          <button
            class="btn btn-red"
            [routerLink]="[ROUTER_UTILS.sysManage.role.create]"
            [routerLinkActive]="['active']"
            *hasPrivileges="SYSTEM_RULES.ROLE_CREATE"
          >
            {{ "common.action.create" | translate }}
          </button>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th class="text-center" scope="col">
                {{ "common.no" | translate }}
              </th>
              <th scope="col">
                {{ "model.role.name" | translate }}
              </th>
              <th scope="col">{{ "model.role.description" | translate }}</th>
              <th scope="col">{{ "model.role.creator" | translate }}</th>
              <th scope="col">{{ "model.role.fromDate" | translate }}</th>
              <th scope="col" class="text-center">
                {{ "common.status" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of listRole; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td>
                <span title="{{ item.name }}">
                  {{ item?.name | limitWord }}
                </span>
              </td>
              <td>
                <span title="{{ item.description }}">
                  {{ item?.description | limitWord }}
                </span>
              </td>
              <td>{{ item?.createdBy }}</td>
              <td>{{ item?.createdDate }}</td>
              <td
                class="text-center"
                *ngIf="item?.status === ENTITY_STATUS_CONST.ACTIVE.code"
              >
                <span class="badge" [ngClass]="'badge-success'">{{
                  "common.used" | translate
                }}</span>
              </td>
              <td
                class="text-center"
                *ngIf="item?.status === ENTITY_STATUS_CONST.INACTIVE.code"
              >
                <span class="badge" [ngClass]="'badge-danger'">{{
                  "common.stop" | translate
                }}</span>
              </td>
              <td class="text-center">
                <button
                  class="btn px-1 py-0"
                  ngbTooltip="{{ 'common.action.detail' | translate }}"
                  (click)="viewDetail(item.roleId)"
                  [routerLinkActive]="['active']"
                  *hasPrivileges="SYSTEM_RULES.ROLE_READ"
                >
                  <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                </button>
                <button
                  class="btn px-1 py-0"
                  [ngbTooltip]="
                    item.status === 0
                      ? ('common.action.unlock' | translate)
                      : ('common.action.lock' | translate)
                  "
                  (click)="lockAndUnlock(item)"
                  *hasPrivileges="
                    item.status === ENTITY_STATUS_CONST.ACTIVE.code
                      ? SYSTEM_RULES.ROLE_UNLOCK
                      : SYSTEM_RULES.ROLE_LOCK
                  "
                >
                  <i
                    [className]="
                      item.status === ENTITY_STATUS_CONST.ACTIVE.code
                        ? 'fa fa-lock mb-color'
                        : 'fa fa-unlock mb-color'
                    "
                    aria-hidden="true"
                  ></i>
                </button>
                <button
                  class="btn px-1 py-0"
                  ngbTooltip="{{ 'common.action.delete' | translate }}"
                  (click)="deleteRole(item)"
                  *hasPrivileges="SYSTEM_RULES.ROLE_DELETE"
                >
                  <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="row d-block text-center m-0" *ngIf="listRole?.length === 0">
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
        <div *ngIf="listRole.length">
          <mat-paginator
            [length]="totalRecord"
            [pageSize]="pageSize"
            [pageSizeOptions]="pageSizeOptions"
            (page)="onChangePage($event)"
            aria-label="Select page"
          >
          </mat-paginator>
        </div>
      </div>
    </div>
  </div>
</section>
