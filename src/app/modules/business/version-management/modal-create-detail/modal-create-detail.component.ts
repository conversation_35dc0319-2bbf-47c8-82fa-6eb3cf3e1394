import { Component, OnInit } from '@angular/core';
import {
  <PERSON>bstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  MOMENT_CONST,
  PUBLISHED_STATUS_CONST,
  STATUS_BUTTON_OPTION,
  TYPE_OPERATE_SYSTEM,
  URL_STORE_CONST,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { MODEL_MAP_ITEM_COMMON } from '@shared/models/model.type';
import { IVersion } from '@shared/models/version.model';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { VersionService } from '@shared/services/version.service';
import { ROUTER_ACTIONS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';

@Component({
  selector: 'app-modal-create-detail',
  templateUrl: './modal-create-detail.component.html',
  styleUrls: ['./modal-create-detail.component.scss'],
})
export class ModalCreateDetailVersionComponent implements OnInit {
  versionItem!: IVersion;
  actionConfirm!: MODEL_MAP_ITEM_COMMON;
  typeOperateArr: any[] = [];
  isTypeUpdateChecked = false;
  formVersion: FormGroup = new FormGroup({});
  events: string[] = [];
  minDate = new Date();
  urlStore = '';
  action = '';
  operate = '';

  ROUTER_ACTION = ROUTER_ACTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  VALIDATORS = VALIDATORS;
  PUBLISHED_STATUS_CONST = PUBLISHED_STATUS_CONST;
  TYPE_OPERATE_SYSTEM = TYPE_OPERATE_SYSTEM;
  URL_STORE_CONST = URL_STORE_CONST;
  MOMENT_CONST = MOMENT_CONST;
  STATUS_BUTTON_OPTION = STATUS_BUTTON_OPTION;

  constructor(
    private fb: FormBuilder,
    public activeModal: NgbActiveModal,
    public translateService: TranslateService,
    private toastService: ToastrCustomService,
    private versionService: VersionService
  ) {}

  ngOnInit(): void {
    this.initForm(this.versionItem);
  }

  /// init form
  initForm(versionItem?: IVersion) {
    this.formVersion = this.fb.group({
      operatingSystem: [
        this.operate === TYPE_OPERATE_SYSTEM.IOS.value
          ? TYPE_OPERATE_SYSTEM.IOS.value
          : TYPE_OPERATE_SYSTEM.ANDROID.value,
      ],
      name: [
        versionItem?.name || null,
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.VERSION_NAME),
          Validators.maxLength(VALIDATORS.LENGTH.VERSION_NAME_LENGTH),
        ],
      ],
      code: [
        versionItem?.code || null,
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.VERSION_CODE),
          Validators.maxLength(VALIDATORS.LENGTH.VERSION_CODE_LENGTH),
        ],
      ],
      forceUpdate: [
        versionItem?.forceUpdate || STATUS_BUTTON_OPTION.NOT_CHECKED.value,
      ],
      manualUpdate: [
        versionItem?.manualUpdate || STATUS_BUTTON_OPTION.NOT_CHECKED.value,
      ],
      forceNotification: [
        versionItem?.forceNotification ||
          STATUS_BUTTON_OPTION.NOT_CHECKED.value,
      ],
      urlStore: [
        this.operate === TYPE_OPERATE_SYSTEM.ANDROID.value
          ? URL_STORE_CONST.ANDROID.value
          : URL_STORE_CONST.IOS.value,
        [Validators.required],
      ],
      description: [
        versionItem?.description || null,
        [
          Validators.required,
          Validators.maxLength(VALIDATORS.LENGTH.VERSION_CONTENT),
        ],
      ],
      releaseDate: [
        versionItem?.releaseDate
          ? this.getDateString(versionItem?.releaseDate + '')
          : null,
        [Validators.required, this.validateDateCreate],
      ],
    });
    if (versionItem && this.action === ROUTER_ACTIONS.detail) {
      this.formVersion.disable();
      return;
    }
    if (this.action === ROUTER_ACTIONS.update) {
      if (versionItem?.status === PUBLISHED_STATUS_CONST.PUBLISHED.value) {
        this.formVersion.disable();
        this.formVersion.controls.urlStore.disable();
      } else {
        this.formVersion.enable();
        this.formVersion.controls.urlStore.disable();
        return;
      }
    }
    if (this.action === ROUTER_ACTIONS.create) {
      this.formVersion.controls.urlStore.disable();
      return;
    }
  }

  ///// create version
  onCreate(): void {
    if (this.formVersion.invalid) {
      CommonUtils.markFormGroupTouched(this.formVersion);
      return;
    }
    if (this.operate === TYPE_OPERATE_SYSTEM.ANDROID.value) {
      this.urlStore = URL_STORE_CONST.ANDROID.value;
    }
    if (this.operate === TYPE_OPERATE_SYSTEM.IOS.value) {
      this.urlStore = URL_STORE_CONST.IOS.value;
    }
    const releaseDate = this.formVersion
      ?.get('releaseDate')
      ?.value.toISOString();

    const params = this.formVersion.value;
    params.releaseDate = releaseDate;
    params.operatingSystem = this.operate;
    params.urlStore = this.urlStore;
    this.versionService.create(params).subscribe((res) => {
      this.toastService.success('common.action.createSuccess');
      this.activeModal.close(this.actionConfirm.code);
    });
  }

  /// Update version
  onUpdate(): void {
    if (this.formVersion.invalid) {
      CommonUtils.markFormGroupTouched(this.formVersion);
      return;
    }

    if (this.operate === TYPE_OPERATE_SYSTEM.ANDROID.value) {
      this.urlStore = URL_STORE_CONST.ANDROID.value;
    }
    if (this.operate === TYPE_OPERATE_SYSTEM.IOS.value) {
      this.urlStore = URL_STORE_CONST.IOS.value;
    }
    const releaseDate = new Date(this.formVersion?.get('releaseDate')?.value);
    const params = this.formVersion.value;
    params.releaseDate = releaseDate.toISOString();
    params.versionId = this.versionItem.versionId;
    params.urlStore = this.urlStore;
    if (this.formVersion.valid) {
      this.versionService.update(params).subscribe((res) => {
        this.toastService.success('common.action.updateSuccess');
        this.activeModal.close(this.actionConfirm.code);
      });
    }
  }

  validateDateCreate(control: AbstractControl): ValidationErrors | null {
    const onlyDateCurrent = moment(new Date()).format(MOMENT_CONST.FORMAT_YEAR);
    const onlyDateCreate = moment(control.value).format(
      MOMENT_CONST.FORMAT_YEAR
    );
    if (onlyDateCreate === onlyDateCurrent) {
      if (new Date(control.value).getHours() < new Date().getHours()) {
        return { invalidCreateTime: true };
      }
      if (new Date(control.value).getHours() === new Date().getHours()) {
        if (
          new Date(control.value).getMinutes() <
          new Date().getMinutes() + 2
        ) {
          return { invalidCreateMinutesTime: true };
        }
      }
      return null;
    } else if (onlyDateCurrent > onlyDateCurrent) {
      return null;
    }
    return null;
  }

  getDateString(dateTime: string): any {
    const [dateValues, timeValues] = dateTime.split(' ');
    const [day, month, year] = dateValues.split('-');
    const [hours, minutes] = timeValues.split(':');
    const releaseTime = moment(
      new Date(+year, +month - 1, +day, +hours, +minutes)
    ).format(MOMENT_CONST.DATE_TIME_MOMENT_FORMAT);
    return releaseTime;
  }

  /// check type operate system
  checkCheckforceNotification(e: any) {
    const checked = e.target.checked;
    if (checked) {
      this.formVersion
        .get('forceNotification')
        ?.setValue(STATUS_BUTTON_OPTION.CHECKED.value);
    } else {
      this.formVersion
        .get('forceNotification')
        ?.setValue(STATUS_BUTTON_OPTION.NOT_CHECKED.value);
      return;
    }
  }

  checkCheckManualUpdate(e: any) {
    const checked = e.target.checked;
    if (checked) {
      this.formVersion
        .get('manualUpdate')
        ?.setValue(STATUS_BUTTON_OPTION.CHECKED.value);
    } else {
      this.formVersion
        .get('manualUpdate')
        ?.setValue(STATUS_BUTTON_OPTION.NOT_CHECKED.value);

      return;
    }
  }

  checkCheckForceUpdate(e: any) {
    const checked = e.target.checked;
    if (checked) {
      this.formVersion
        .get('forceUpdate')
        ?.setValue(STATUS_BUTTON_OPTION.CHECKED.value);
    } else {
      this.formVersion
        .get('forceUpdate')
        ?.setValue(STATUS_BUTTON_OPTION.NOT_CHECKED.value);
      return;
    }
  }
}
