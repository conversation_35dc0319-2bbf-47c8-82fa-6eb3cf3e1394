import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IConfigAuto } from '@shared/models/config-auto-transaction.model';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { UmoneyLapnetService } from '@shared/services/umoney-lapnet.service';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-async-date-report',
  templateUrl: './async-date-report.component.html',
  styleUrls: ['./async-date-report.component.scss'],
})
export class AsyncDateReport implements OnInit {
  SYSTEM_RULES = SYSTEM_RULES;
  VALIDATORS = VALIDATORS;
  form: FormGroup = new FormGroup({});
  data: IConfigAuto = {};
  maxDate = new Date();

  constructor(
    private fb: FormBuilder,
    public activeModal: NgbActiveModal,
    private toastService: ToastrCustomService,
    private configAutoService: UmoneyLapnetService
  ) {}

  ngOnInit(): void {
    this.initForm();
  }

  initForm() {
    this.form = this.fb.group({
      syncDate: [null, [Validators.required]],
    });
  }

  syncReport() {
    if (this.form.invalid) {
      CommonUtils.markFormGroupTouched(this.form);
      return;
    }
    const body: IConfigAuto = {
      asyncDate: this.form.controls.syncDate.value,
    };

    this.configAutoService.syncReport(body).subscribe((res: any) => {
      if (res.body) {
        if (res.body.success) {
          this.toastService.success('common.action.syncReportSuccess');
          this.activeModal.close();
        } else {
          this.toastService.error('common.action.syncReportFails');
        }
      } else {
        this.toastService.error('common.action.syncReportFails');
      }
    });
  }
}
