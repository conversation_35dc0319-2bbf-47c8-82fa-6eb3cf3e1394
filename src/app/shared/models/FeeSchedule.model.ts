import { IDocument } from './file.model';
export interface IFeeSchedule {
  feeScheduleId?: number;
  title?: string;
  createDate?: string;
  status?: number;
  toDate?: string;
  fromDate?: string;
  lastModifiedDate?: string;
  createdDate?: string;
  feeScheduleDetailDTO?: IFeeScheduleDetail;
  iconUrL?: any;
  details?: IFeeScheduleDetail[];
}

export interface IFeeScheduleDetail {
  feeScheduleDetailId?: number;
  feeScheduleId?: string;
  icon?: IDocument;
  language?: string;
  title?: string;
  createdDate?: string;
}

export class FeeSchedule implements IFeeSchedule {
  constructor(data?: IFeeSchedule) {
    Object.assign(this, data);
  }
}
