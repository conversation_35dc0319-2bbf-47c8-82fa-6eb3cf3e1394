import { Injectable } from '@angular/core';
import { SectorPopupComponent } from '@business/customer-management/customer-register-management/sector-popup/sector-popup.component';
import { ConfigurationFeeTypePopupComponent } from '@business/fee-management/configuration-fee-type-popup/configuration-fee-type-popup.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmModalComponent } from '@shared/components/confirm-modal/confirm-modal.component';
import { ModalAddItemComponent } from '@shared/components/modal-add-item/modal-add-item.component';
import { MODAL_ACTION } from '@shared/constants/app.constants';
import { ModelDialog } from '@shared/models/model.type';

@Injectable()
export class ModalService {
  constructor(private modalService: NgbModal) {}

  public confirm(modelDialog: ModelDialog): Promise<string> {
    const modalRef = this.modalService.open(ConfirmModalComponent, {
      backdrop: 'static',
      size: modelDialog.dialogSize,
      centered: true,
      keyboard: false,
    });
    modalRef.componentInstance.title = modelDialog.title;
    modalRef.componentInstance.content = modelDialog.content;
    modalRef.componentInstance.action = modelDialog.action || MODAL_ACTION.CONFIRM;
    modalRef.componentInstance.interpolateParams = modelDialog.interpolateParams;

    // Bổ sung thêm các thuộc tính cho nút
    modalRef.componentInstance.confirmButtonText = modelDialog.confirmButtonText; // Text cho nút xác nhận
    modalRef.componentInstance.cancelButtonText = modelDialog.cancelButtonText; // Text cho nút hủy
    modalRef.componentInstance.buttonOrder = modelDialog.buttonOrder; // Thứ tự nút
    modalRef.componentInstance.confirmBtnClass = modelDialog.confirmBtnClass; // Class cho nút xác nhận

    return modalRef.result;
  }

  public dismissAll() {
    this.modalService.dismissAll();
  }

  public addItem(modelDialog: ModelDialog): Promise<string> {
    const modalRef = this.modalService.open(ModalAddItemComponent, {
      backdrop: 'static',
      size: modelDialog.dialogSize,
      centered: true,
      keyboard: false,
    });
    modalRef.componentInstance.title = modelDialog.title;
    modalRef.componentInstance.content = modelDialog.content;
    modalRef.componentInstance.action = modelDialog.action || MODAL_ACTION.CONFIRM;
    modalRef.componentInstance.interpolateParams = modelDialog.interpolateParams;

    return modalRef.result;
  }

  public createFee(modelDialog: ModelDialog): Promise<string> {
    const modalRef = this.modalService.open(ConfigurationFeeTypePopupComponent, {
      backdrop: 'static',
      size: modelDialog.dialogSize,
      centered: true,
      keyboard: false,
    });

    modalRef.componentInstance.title = modelDialog.title;
    modalRef.componentInstance.action = modelDialog.action || MODAL_ACTION.CREATE;

    return modalRef.result;
  }

  public updateSectorModal(modelDialog: ModelDialog): Promise<string> {
    const modalRef = this.modalService.open(
      SectorPopupComponent,
      {
        backdrop: 'static',
        size: modelDialog.dialogSize,
        centered: true,
        keyboard: false,
      }
    );

    modalRef.componentInstance.title = modelDialog.title;
    modalRef.componentInstance.content = modelDialog.content;
    modalRef.componentInstance.setOfCheckedId = modelDialog.interpolateParams.setOfCheckedId;
    modalRef.componentInstance.action =
      modelDialog.action || MODAL_ACTION.CONFIRM;
    modalRef.componentInstance.interpolateParams =
      modelDialog.interpolateParams;

    return modalRef.result;
  }
}
