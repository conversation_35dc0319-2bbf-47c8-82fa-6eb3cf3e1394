import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ILapnetReport } from '@shared/models/lapnet-report.model';
import { ILapnetSearch } from '@shared/models/request/lapnet.search';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';
import { IConfigAuto } from '@shared/models/config-auto-transaction.model';

@Injectable({ providedIn: 'root' })
export class UmoneyLapnetService extends AbstractService {
  public resourceUrl = '/umoney-report';

  constructor(protected http: HttpClient) {
    super(http);
  }

  /**
   * API search
   *
   * @param request ILapnetSearch
   * @returns Observable<EntityResponseType<ILapnetReport>>
   */
  search(
    request: ILapnetSearch
  ): Observable<EntityResponseType<ILapnetReport>> {
    return super.post<EntityResponseType<ILapnetReport>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  /**
   * API export
   *
   * @param request ILapnetSearch
   * @returns Observable<any>
   */
  export(request: ILapnetSearch): Observable<any> {
    return super.postFile(`${this.resourceUrl}/export`, request);
  }

  create(request: IConfigAuto): Observable<EntityResponseType<IConfigAuto>> {
    return super.post<EntityResponseType<IConfigAuto>>(
      `${this.resourceUrl}/configure-automatic/create`,
      request
    );
  }

  update(request: IConfigAuto): Observable<EntityResponseType<IConfigAuto>> {
    return super.post<EntityResponseType<IConfigAuto>>(
      `${this.resourceUrl}/configure-automatic/update`,
      request
    );
  }

  info(request: IConfigAuto): Observable<EntityResponseType<IConfigAuto>> {
    return super.post<EntityResponseType<IConfigAuto>>(
      `${this.resourceUrl}/configure-automatic/info`,
      request
    );
  }

  syncReport(request: IConfigAuto): Observable<EntityResponseType<IConfigAuto>> {
    return super.post<EntityResponseType<IConfigAuto>>(
      `${this.resourceUrl}/sync`,
      request
    );
  }
}
