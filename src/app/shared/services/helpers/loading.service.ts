/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class LoadingService {
  public isLoading = new BehaviorSubject<boolean>(false);

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  constructor() {}

  /**
   * Hàm set hiển thị spinner
   *
   * <AUTHOR>
   * @date 2021-07-27
   * @param {boolean} isLoading
   * @memberof LoadingService
   */
  show() {
    this.isLoading.next(true);
  }

  /**
   * Hàm set ẩn hiển thị spinner
   *
   * <AUTHOR>
   * @date 2021-07-27
   * @param {boolean} isLoading
   * @memberof LoadingService
   */
  hide() {
    this.isLoading.next(false);
  }
}
