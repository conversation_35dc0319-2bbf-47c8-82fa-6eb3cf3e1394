import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IDebitAccountCreate } from '@shared/models/debit-account.model';
import { MODEL_MAP_ITEM_COMMON } from '@shared/models/model.type';
import { DebitAccountService } from '@shared/services/debit-account.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-debit-account-create',
  templateUrl: './debit-account-create.component.html',
  styleUrls: ['./debit-account-create.component.scss'],
})
export class DebitAccountCreateComponent implements OnInit {
  formCreate: FormGroup = new FormGroup({});

  SYSTEM_RULES = SYSTEM_RULES;
  VALIDATORS = VALIDATORS;
  actionConfirm!: MODEL_MAP_ITEM_COMMON;
  constructor(
    public activeModal: NgbActiveModal,
    private fb: FormBuilder,
    private paymentService: DebitAccountService,
    private toastService: ToastrCustomService
  ) {
    this.formCreate = this.fb.group({
      accountNumber: [
        '',
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.MERCHANT_NUMBER),
          Validators.maxLength(
            VALIDATORS.LENGTH.CASHOUT_ACCOUNT_NUMBER_MAX_LENGTH
          ),
        ],
      ],
      transactionLimit: [
        '',
        [
          Validators.required,
          Validators.maxLength(VALIDATORS.LENGTH.TRANSACTION_LIMIT_MAX_LENGTH),
        ],
      ],
      accountName: ['', [Validators.required]],
      cif: [
        '',
        [Validators.required, Validators.pattern(VALIDATORS.PATTERN.NUMBER)],
      ],
    });
  }

  ngOnInit(): void {}

  onCreate() {
    const body = {
      ...this.formCreate.value,
      transactionLimit: this.formCreate.value.transactionLimit
        ?.split(',')
        .join(''),
    } as IDebitAccountCreate;
    if (this.formCreate.invalid) {
      CommonUtils.markFormGroupTouched(this.formCreate);
      return;
    }
    this.paymentService.create(body).subscribe((res) => {
      this.toastService.success('common.action.createSuccess');
      this.activeModal.close(this.actionConfirm.code);
    });
  }
}
