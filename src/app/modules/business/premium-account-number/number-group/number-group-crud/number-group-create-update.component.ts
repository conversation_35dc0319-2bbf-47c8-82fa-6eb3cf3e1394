import { Component, Input, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  Validators
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  ENTITY_STATUS,
  MODAL_ACTION
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { INumberGroup } from '@shared/models/number-group.model';
import {
  IClient
} from '@shared/models/service-pack.model';
import { ITransactionFeeType } from '@shared/models/transaction-fee-type.model';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { NumberGroupService } from '@shared/services/number-group.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-number-group-create-update',
  templateUrl: './number-group-create-update.component.html',
  styleUrls: ['./number-group-create-update.component.scss'],
})
export class NumberGroupCreateUpdateComponent implements OnInit {
  @Input() numberGroupId = 0;
  form: FormGroup = new FormGroup({});

  // input call api
  numberGroup: INumberGroup = {};
  reader: FileReader = new FileReader();
  action = '';
  hasFilter = false;
  isShowButton = false;
  clients: IClient[] = [];
  servicePackTypes: ITransactionFeeType[] = [];

  ROUTER_UTILS = ROUTER_UTILS;
  ENTITY_STATUS = ENTITY_STATUS;
  VALIDATORS = VALIDATORS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  SYSTEM_RULES = SYSTEM_RULES;

  constructor(
    private formBuilder: FormBuilder,
    private numberGroupService: NumberGroupService,
    private toastService: ToastrCustomService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private modalService: ModalService,
    public activeModal: NgbActiveModal,
    public translateService: TranslateService,
  ) {
    this.activatedRoute.data.subscribe((res) => {
      this.action = res.action;
    });
  }

  ngOnInit(): void {
    if (this.action === ROUTER_ACTIONS.create) {
      this.initForm();
    } else {
      this.getDetail();
    }
  }

  /**
   * init form
   *
   * @param user User
   */
  initForm(numberGroup?: INumberGroup): void {
    this.form = this.formBuilder.group({
      code: [
        {
          value: numberGroup?.numberGroupCode || null,
          disabled: true,
        },
      ],
      name: [
        {
          value: numberGroup?.name || '',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.NO_SPECIAL_CHARACTERS),
          Validators.minLength(VALIDATORS.LENGTH.NAME_NUMBER_GROUP_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.TEXT_MAX_LENGTH),
        ],
      ],
      minPrice: [
        {
          value: numberGroup?.minPrice ? CommonUtils.moneyFormat(numberGroup.minPrice.toString())?.toString() : '0',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [
          Validators.max(VALIDATORS.LENGTH.NUMBER_MAX_AMOUNT),
        ],
      ],
      maxPrice: [
        {
          value: numberGroup?.maxPrice ? CommonUtils.moneyFormat(numberGroup.maxPrice.toString())?.toString() : '0',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [
          Validators.max(VALIDATORS.LENGTH.NUMBER_MAX_AMOUNT),
        ],
      ],
      description: [
        {
          value: numberGroup?.description || '',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.NO_SPECIAL_CHARACTERS),
          Validators.minLength(VALIDATORS.LENGTH.DESCRIPTION_NUMBER_GROUP_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.MEDIUM_TEXT_MAX_LENGTH),
        ],
      ],
      hided: false
    });
  }

  /**
   * back to monitor list
   */
  backToList(): void {
    this.activeModal.close();
  }

  /**
   * get detail user
   */
  getDetail(): void {
    if (this.numberGroupId) {
      this.numberGroupService.detail({id: this.numberGroupId}).subscribe((res: any) => {
        this.numberGroup = res.body;
        const data = res.body || undefined;
        this.initForm(data);
      });
    }
  }

  /**
   * Create: check validate file and data input if valid then call api create
   */
  onCreate(): void {
    // touched form
    if (this.form.invalid) {
      CommonUtils.markFormGroupTouched(this.form);
    }
    const data = this.form.getRawValue();
    data.minPrice = data.minPrice.toString().split('.').join('');
    data.maxPrice = data.maxPrice.toString().split('.').join('');
    if (this.form.valid) {
      this.numberGroupService.create({ ...data }).subscribe((res): void => {
        this.toastService.success('common.action.createSuccess');
        this.activeModal.close(MODAL_ACTION.CONFIRM.code);
      });
    }
  }

  /**
   * Update: check validate file and data input if valid then call api create
   */
  onUpdate(): void {
    // touched form
    if (this.form.invalid) {
      CommonUtils.markFormGroupTouched(this.form);
    }
    const data = this.form.getRawValue();
    data.numberGroupId = this.numberGroup?.numberGroupId;
    data.minPrice = data.minPrice.toString().split('.').join('');
    data.maxPrice = data.maxPrice.toString().split('.').join('');
    if (this.form.valid) {
      this.numberGroupService.update(data).subscribe((res): void => {
        this.toastService.success('common.action.updateSuccess');
        this.activeModal.close(MODAL_ACTION.CONFIRM.code);
      });
    }
  }

  onEdit() {
    this.router.navigate([
      ROUTER_UTILS.numberGroup.root,
      this.numberGroup.numberGroupId,
      ROUTER_ACTIONS.update,
    ]);
  }

  dateValidator(value: number, value2: number): any {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return +value > +value2 ? { invalidNumber: true } : null;
    };
  }

  maxValidator(value: number): any {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return +value > VALIDATORS.LENGTH.NUMBER_MAX_AMOUNT ? { invalidMax: true } : null;
    };
  }

  textWarningChange(): void {
    const name = this.form.get('name')?.value;
    return this.translateService.instant('numberGroup.message.text', {
      name,
    });
  }

  changeValidate(): void {
    const minPrice = this.form.controls.minPrice.value.toString().split('.').join('') || 0;
    const maxPrice = this.form.controls.maxPrice.value.toString().split('.').join('') || 0;
    if (minPrice && maxPrice && minPrice !== 0 && maxPrice !== 0 && +minPrice > +maxPrice) {
      this.form.get('minPrice')?.setValidators([this.dateValidator(minPrice, maxPrice)]);
      this.form.get('minPrice')?.updateValueAndValidity();
      CommonUtils.markFormGroupTouched(this.form);
    } else {
      this.form.get('minPrice')?.setValidators([]);
      this.form.get('minPrice')?.updateValueAndValidity();
      this.form.get('maxPrice')?.setValidators([]);
      this.form.get('maxPrice')?.updateValueAndValidity();
    }

    if (minPrice && minPrice > VALIDATORS.LENGTH.NUMBER_MAX_AMOUNT) {
      this.form.get('minPrice')?.setValidators([this.maxValidator(maxPrice), this.dateValidator(minPrice, maxPrice)]);
      this.form.get('minPrice')?.updateValueAndValidity();
    }

    if (maxPrice && maxPrice > VALIDATORS.LENGTH.NUMBER_MAX_AMOUNT) {
      this.form.get('maxPrice')?.setValidators([this.maxValidator(maxPrice)]);
      this.form.get('maxPrice')?.updateValueAndValidity();
    } else {
      this.form.get('maxPrice')?.setValidators([]);
      this.form.get('maxPrice')?.updateValueAndValidity();
    }

    if (this.action === ROUTER_ACTIONS.update) {
      this.hasFilter = true;
      this.isShowButton = true;
    }
  }

  checkHided(): void {
    this.form?.controls.hided.valueChanges.subscribe(value => {
      if (value) {
        this.hasFilter = false;
      } else {
        this.hasFilter = true;
      }
    });
  }

  actionOnUpdate(): void {
    this.action = ROUTER_ACTIONS.update;
    this.form.controls.name.enable();
    this.form.controls.minPrice.enable();
    this.form.controls.maxPrice.enable();
    this.form.controls.description.enable();
  }
}
