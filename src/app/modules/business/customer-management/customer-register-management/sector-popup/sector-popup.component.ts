import { Component, Input, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { MODAL_ACTION } from '@shared/constants/app.constants';
import {
  CUSTOMER_SECTOR,
  CUSTOMER_SECTOR_CONST,
} from '@shared/constants/customer.constants';
import { ICustomer } from '@shared/models/customer.model';
import { CustomerService } from '@shared/services/customer.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-sector-popup',
  templateUrl: './sector-popup.component.html',
  styleUrls: ['./sector-popup.component.scss'],
})
export class SectorPopupComponent implements OnInit {
  @Input() setOfCheckedId: Set<string> = new Set<string>();
  form: FormGroup = new FormGroup({});
  options = [
    { value: CUSTOMER_SECTOR_CONST.SECTOR_1890.value },
    { value: CUSTOMER_SECTOR_CONST.SECTOR_1891.value }
  ];

  MODAL_ACTION = MODAL_ACTION;
  CUSTOMER_SECTOR = CUSTOMER_SECTOR;
  CUSTOMER_SECTOR_CONST = CUSTOMER_SECTOR_CONST;

  constructor(
    public activeModal: NgbActiveModal,
    private fb: FormBuilder,
    private customerService: CustomerService,
    private toastService: ToastrCustomService
  ) { }

  ngOnInit(): void {
    this.initForm();
  }

  initForm() {
    this.form = this.fb.group({
      customerSectorId: [null, [Validators.required]],
    });
  }

  updateSectors() {
    if (this.form.invalid) {
      CommonUtils.markFormGroupTouched(this.form);
      return;
    }

    const customerCifs: string[] = [];
    if (this.setOfCheckedId.size > 0) {
      this.setOfCheckedId.forEach((item: any) => {
        customerCifs.push(item);
      });
      const data = {
        customerSectorId: this.form.get('customerSectorId')?.value,
        cifs: customerCifs,
      };

      if (this.form.valid) {
        this.customerService.updateSectors(data).subscribe((res: any) => {

          if (res.body) {
            const customerSuccess = res.body.customerSuccess;
            const customerFails = res.body.customerFails;
            if (customerSuccess.length > 0) {
              const cifs = customerSuccess.map((item: ICustomer) => item.cif);
              let fullname;
              for (const value of this.setOfCheckedId) {
                if (cifs.includes(value + '')) {
                  this.setOfCheckedId.delete(value);
                }
              }

              if (customerSuccess.length > 5) {
                const fullnameTop5 = customerSuccess.slice(0, 5);
                fullname = fullnameTop5.map((customer: ICustomer) => customer.fullname).join(', ');
                fullname += '...';
              } else if (customerSuccess.length >= 1 && customerSuccess.length <= 5) {
                fullname = customerSuccess.map((customer: ICustomer) => customer.fullname).join(', ');
              }

              this.toastService.successCustom('common.action.updateSectorSuccess', { name: fullname });
            }

            if (customerFails.length > 0) {
              let fullNames;
              fullNames = customerFails.map((item: ICustomer) => item.fullname);
              if (customerFails.length > 5) {
                fullNames = customerFails.map((customer: ICustomer) => customer?.fullname).splice(0, 5).join(', ');
                fullNames += '...';
              } else if (customerFails.length >= 1 && customerFails.length <= 5) {
                fullNames = customerFails.map((customer: ICustomer) => customer.fullname).join(', ');
              }

              this.toastService.errorCustom('common.action.updateSectorFails', { name: fullNames });
            }

            if (this.setOfCheckedId.size === 0) {
              this.setOfCheckedId = new Set();
            }
          }
          this.activeModal.close(MODAL_ACTION.CONFIRM.code);
        });
      }
    }
  }
}
