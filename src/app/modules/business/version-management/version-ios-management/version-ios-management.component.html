<form [formGroup]="formSearch" (submit)="onSubmitSearch()">
  <div class="row">
    <div class="col-md-6 col-lg-3">
      <div class="form-group">
        <label>{{ "common.action.searchKeyword" | translate }}</label>
        <input
          trim
          type="text"
          formControlName="keyword"
          placeholder="{{ 'common.action.searchKeyword' | placeholder }}"
          class="w-100"
          class="form-control"
        />
      </div>
    </div>
    <div class="col-md-6 col-lg-2">
      <div class="form-group">
        <label>{{ "common.status" | translate }}</label>
        <ng-select
          appearance="outline"
          [searchable]="false"
          placeholder="{{ 'common.status' | placeholder : 'select' }}"
          [clearable]="true"
          formControlName="status"
        >
          <ng-option [value]="item.value" *ngFor="let item of PUBLISHED_STATUS">
            {{ item.label | translate }}
          </ng-option>
        </ng-select>
      </div>
    </div>
    <div class="col-md-8 col-lg-4">
      <div class="form-group date-picker-container row">
        <div class="col-md-6">
          <label class="">
            {{ "model.versionManage.fromDate" | translate }}
          </label>
          <mat-form-field appearance="fill" class="date-picker">
            <input
              matInput
              [matDatepicker]="fromDate"
              formControlName="fromDate"
              [placeholder]="MOMENT_CONST.FORMAT_DATE_PLACEHOLDER"
              max="{{
                formSearch.controls['toDate'].value
                  ? (formSearch.controls['toDate'].value
                    | date : MOMENT_CONST.FORMAT_DATE_MAT_PICKER)
                  : (maxDate | date : MOMENT_CONST.FORMAT_DATE_MAT_PICKER)
              }}"
              dateTransform
            />
            <mat-datepicker-toggle
              matSuffix
              [for]="fromDate"
            ></mat-datepicker-toggle>
            <mat-datepicker #fromDate></mat-datepicker>
          </mat-form-field>
          <small
            class="text-danger"
            *ngIf="
              formSearch?.get('fromDate')?.errors?.invalidFromDateCurrent &&
              formSearch.get('fromDate')?.touched
            "
            >{{
              "error.versionManage.dateTime.invalidFromDate" | translate
            }}</small
          >
        </div>
        <div class="col-md-6">
          <label class="">
            {{ "model.versionManage.toDate" | translate }}
          </label>
          <mat-form-field appearance="fill" class="date-picker">
            <input
              matInput
              [matDatepicker]="toDate"
              formControlName="toDate"
              [placeholder]="MOMENT_CONST.FORMAT_DATE_PLACEHOLDER"
              dateTransform
              [max]="maxDate"
              min="{{
                formSearch.controls['fromDate'].value
                  | date : MOMENT_CONST.FORMAT_DATE_MAT_PICKER
              }}"
            />
            <mat-datepicker-toggle
              matSuffix
              [for]="toDate"
            ></mat-datepicker-toggle>
            <mat-datepicker #toDate></mat-datepicker>
          </mat-form-field>
          <small
            class="text-danger"
            *ngIf="
              formSearch?.get('toDate')?.errors?.invalidToDate &&
              formSearch.get('toDate')?.touched
            "
            >{{
              "error.versionManage.dateTime.invalidDateSearch" | translate
            }}</small
          >
          <small
            class="text-danger"
            *ngIf="
              formSearch?.get('toDate')?.errors?.invalidToDateCurrent &&
              formSearch.get('toDate')?.touched
            "
            >{{
              "error.versionManage.dateTime.invalidToDate" | translate
            }}</small
          >
        </div>
      </div>
    </div>
    <div class="col-md-2 col-lg-2 mt-4 d-flex">
      <div
        class="col-btn-reset"
        ngbTooltip="{{ 'common.action.reset' | translate }}"
      >
        <div class="btn-reset" (click)="reset()">
          <i class="bi bi-arrow-clockwise" type="button"> </i>
        </div>
      </div>
      <div class="">
        <button
          class="btn btn-search mr-2"
          type="submit"
          *hasPrivileges="SYSTEM_RULES.VERSION_READ"
        >
          {{ "common.action.search" | translate }}
        </button>
      </div>
    </div>
  </div>
  <div class="border-bottom-search"></div>
  <div class="d-block text-right mb-2 mt-4">
    <button
      class="btn btn-white mb-2"
      type="button"
      (click)="exportVersion()"
      *hasPrivileges="SYSTEM_RULES.VERSION_EXPORT"
    >
      {{ "common.action.export" | translate }}
    </button>
    <button
      [disabled]="isReleased"
      class="btn btn-red mb-2"
      type="button"
      *hasPrivileges="SYSTEM_RULES.VERSION_CREATE"
      (click)="createVersion(TYPE_OPERATE_SYSTEM.IOS.value)"
    >
      {{ "common.action.create" | translate }}
    </button>
  </div>
</form>
<div class="table-responsive">
  <table class="table">
    <thead>
      <tr>
        <th class="text-center" scope="col">
          {{ "model.versionManage.no" | translate }}
        </th>
        <th scope="col">
          {{ "model.versionManage.versionName" | translate }}
        </th>
        <th scope="col" class="text-left">
          {{ "model.versionManage.versionCode" | translate }}
        </th>
        <th scope="col" class="text-center">
          {{ "model.versionManage.forceUpdate" | translate }}
        </th>
        <th scope="col" class="text-center">
          {{ "model.versionManage.handUpdate" | translate }}
        </th>
        <th scope="col" class="text-center">
          {{ "model.versionManage.noti" | translate }}
        </th>
        <th scope="col">
          {{ "model.versionManage.installed" | translate }}
        </th>
        <th scope="col">
          {{ "model.versionManage.urlStore" | translate }}
        </th>
        <th scope="col" class="text-center">
          {{ "model.versionManage.createdDate" | translate }}
        </th>
        <th class="text-center" scope="col">
          {{ "common.status" | translate }}
        </th>
        <th class="text-center" scope="col">
          {{ "model.versionManage.action" | translate }}
        </th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let item of versionItems; let i = index">
        <td class="text-center">{{ fillIndexItem(i) }}</td>
        <td>
          <span title="{{ item.name }}">{{ item?.name }}</span>
        </td>
        <td>
          <span title="{{ item.code }}">{{ item.code }}</span>
        </td>
        <td class="text-center">
          <input
            class="form-check-input"
            type="checkbox"
            [checked]="item.forceUpdate"
          />
        </td>
        <td class="text-center">
          <input
            class="form-check-input"
            type="checkbox"
            [checked]="item.manualUpdate"
          />
        </td>
        <td class="text-center">
          <input
            class="form-check-input"
            type="checkbox"
            [checked]="item.forceNotification"
          />
        </td>
        <td class="text-center">
          <span>{{ item.installed }}</span>
        </td>
        <td>
          <a
            href="{{ item.urlStore }}"
            target="_blank"
            title="{{ item.urlStore }}"
            >{{ item.urlStore | limitWord }}
          </a>
          <i
            *ngIf="item.urlStore"
            class="fa fa-clone copy-text"
            (click)="copyText(item.urlStore)"
            aria-hidden="true"
          ></i>
        </td>
        <td class="text-center">
          {{ item?.createdDate }}
        </td>
        <td class="text-center">
          <span
            [ngClass]="PUBLISHED_STATUS_MAP[item.status || 0].style"
            class="badge"
            title="{{ item.status }}"
          >
            {{ PUBLISHED_STATUS_MAP[item.status || 0].label | translate }}
          </span>
        </td>
        <td class="text-center">
          <button
            ngbTooltip="{{ 'common.action.detail' | translate }}"
            class="btn px-1 py-0"
            (click)="detail(item, TYPE_OPERATE_SYSTEM.IOS.value)"
            *hasPrivileges="SYSTEM_RULES.REFERRAL_READ"
          >
            <i class="bi bi-eye mb-color" aria-hidden="true"></i>
          </button>
          <ng-container
            *ngIf="item.status === PUBLISHED_STATUS_CONST.UNRELEASED.value"
          >
            <button
              ngbTooltip="{{ 'common.action.update' | translate }}"
              class="btn px-1 py-0"
              (click)="edit(item, TYPE_OPERATE_SYSTEM.IOS.value)"
              data-toggle="modal"
              *hasPrivileges="SYSTEM_RULES.VERSION_WRITE"
            >
              <i class="fa fa-edit mb-color" aria-hidden="true"></i>
            </button>
          </ng-container>
        </td>
      </tr>
    </tbody>
  </table>
  <div class="row d-block text-center m-0" *ngIf="versionItems?.length === 0">
    <img
      src="/assets/dist/img/icon/empty.svg"
      height="120"
      alt="no_search_result"
    />
    <p class="text-center mb-5">
      {{ "common.no_search_result" | translate }}
    </p>
  </div>
  <div *ngIf="versionItems.length">
    <mat-paginator
      [length]="formSearch.value.length"
      [pageSize]="formSearch.value.pageSize"
      [pageIndex]="formSearch.value.pageIndex"
      [pageSizeOptions]="pageSizeOptions"
      (page)="onChangePage($event)"
      aria-label="Select page"
    >
    </mat-paginator>
  </div>
</div>
