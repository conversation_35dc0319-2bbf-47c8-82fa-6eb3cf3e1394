<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="backToList()" />
      <h5>
        {{
          action === ROUTER_ACTIONS.detail
            ? ("user.detailTitle" | translate)
            : isUpdate
            ? ("user.updateTitle" | translate)
            : ("user.createTitle" | translate)
        }}
      </h5>
    </div>
    <!-- <form [formGroup]="form" *ngIf="customer?.customerId"></form> -->
    <form [formGroup]="formUser" *ngIf="isUpdate ? user?.userId : !isUpdate">
      <div class="col-md-12">
        <div class="row">
          <h5 class="modal-title" *ngIf="!(action === ROUTER_ACTIONS.detail)">
            {{
              (action === ROUTER_ACTIONS.update
                ? "user.updateTitle"
                : "user.createTitle"
              ) | translate
            }}
          </h5>
          <h5 class="modal-title" *ngIf="action === ROUTER_ACTIONS.detail">
            {{ "user.titleInforUser" | translate }}
          </h5>
        </div>
        <div class="row border-create">
          <h3>{{ "user.titleInforUser" | translate }}</h3>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label
                    >{{ "model.user.fullname" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    trim
                    formControlName="fullname"
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{ 'model.user.fullname' | placeholder }}"
                    [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formUser.get('fullname')?.errors?.required &&
                      formUser.get('fullname')?.touched
                    "
                  >
                    {{ "error.user.required.fullname" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formUser.get('fullname')?.errors?.pattern &&
                      formUser.get('fullname')?.touched
                    "
                  >
                    {{ "error.user.pattern.fullname" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formUser.get('fullname')?.errors?.maxlength &&
                      formUser.get('fullname')?.touched
                    "
                  >
                    {{
                      "error.user.maxLength.fullname"
                        | translate
                          : {
                              param: VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH
                            }
                    }}
                  </small>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label
                    >{{ "model.customer.phoneNumber" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    trim
                    formControlName="phoneNumber"
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{ 'model.user.phoneNumber' | placeholder }}"
                    [maxLength]="VALIDATORS.LENGTH.PHONE_NUMBER_MAX_LENGTH"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formUser.get('phoneNumber')?.errors?.required &&
                      formUser.get('phoneNumber')?.touched
                    "
                  >
                    {{ "error.user.required.phoneNumber" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formUser.get('phoneNumber')?.errors?.minlength &&
                      formUser.get('phoneNumber')?.touched
                    "
                  >
                    {{
                      "error.user.minLength.phoneNumber"
                        | translate
                          : {
                              param: VALIDATORS.LENGTH.PHONE_NUMBER_MIN_LENGTH
                            }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formUser.get('phoneNumber')?.errors?.maxlength &&
                      formUser.get('phoneNumber')?.touched
                    "
                  >
                    {{
                      "error.user.minLength.phoneNumber"
                        | translate
                          : {
                              param: VALIDATORS.LENGTH.PHONE_NUMBER_MAX_LENGTH
                            }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formUser.get('phoneNumber')?.errors?.pattern &&
                      formUser.get('phoneNumber')?.touched
                    "
                  >
                    {{ "error.user.pattern.phoneNumber" | translate }}
                  </small>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label
                    >{{ "model.user.email" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    trim
                    (change)="onChangeGetUsername($event)"
                    formControlName="email"
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{ 'model.user.email' | placeholder }}"
                    [maxLength]="VALIDATORS.LENGTH.EMAIL_USER_MAX_LENGTH"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formUser.get('email')?.errors?.required &&
                      formUser.get('email')?.touched
                    "
                  >
                    {{ "error.user.required.email" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formUser.get('email')?.errors?.maxlength &&
                      formUser.get('email')?.touched
                    "
                  >
                    {{
                      "error.user.maxLength.email"
                        | translate
                          : {
                              param: VALIDATORS.LENGTH.EMAIL_USER_MAX_LENGTH
                            }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formUser.get('email')?.errors?.pattern &&
                      formUser.get('email')?.touched
                    "
                  >
                    {{ "error.user.pattern.email" | translate }}
                  </small>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.user.username" | translate }}</label>
                  <input
                    trim
                    formControlName="username"
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{ 'model.user.username' | placeholder }}"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label
                    >{{ "model.user.dateOfBirth" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <!-- <input
                    formControlName="dateOfBirth"
                    type="date"
                    class="w-100"
                    class="form-control border-input"
                    max="{{ maxDateOfBirth | date: 'yyyy-MM-dd' }}"
                  /> -->
                  <mat-form-field appearance="fill" class="date-picker">
                    <input
                      matInput
                      [matDatepicker]="picker"
                      formControlName="dateOfBirth"
                      placeholder="DD/MM/YYYY"
                      [max]="maxDateOfBirth"
                      dateTransform
                    />
                    <mat-datepicker-toggle
                      matSuffix
                      [for]="picker"
                    ></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                  </mat-form-field>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formUser.get('dateOfBirth')?.errors?.required &&
                      formUser.get('dateOfBirth')?.touched
                    "
                  >
                    {{ "error.user.required.dob" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formUser.get('dateOfBirth')?.errors?.invalidMaxDate &&
                      formUser.get('dateOfBirth')?.touched
                    "
                  >
                    {{
                      "error.maxDateCurrent"
                        | translate
                          : {
                              param: "model.user.dateOfBirth" | translate
                            }
                    }}
                  </small>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.customer.gender" | translate }}</label>
                  <ng-select
                    appearance="outline"
                    [searchable]="false"
                    [clearable]="false"
                    formControlName="gender"
                    placeholder="{{
                      'model.user.gender' | placeholder : 'select'
                    }}"
                  >
                    <ng-option
                      [value]="item.code"
                      *ngFor="let item of GENDER_LIST"
                    >
                      {{ item.label | translate }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label
                    >{{ "model.user.department" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <ng-select
                    [multiple]="false"
                    appearance="outline"
                    bindLabel="departmentName"
                    bindValue="departmentId"
                    [items]="departments"
                    [searchFn]="onSearchKeywordDepartment"
                    [clearable]="true"
                    formControlName="departmentId"
                    placeholder="{{
                      'model.user.department' | placeholder : 'select'
                    }}"
                  >
                    <ng-option
                      [value]="item.departmentId"
                      *ngFor="let item of departments"
                    >
                      {{ item.departmentName }}
                    </ng-option>
                  </ng-select>
                  <small
                    class="form-option text-danger noti-small"
                    *ngIf="
                      formUser.get('departmentId')?.errors?.required &&
                      formUser.get('departmentId')?.touched
                    "
                  >
                    {{ "error.user.required.department" | translate }}
                  </small>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label
                    >{{ "model.user.position" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <ng-select
                    [multiple]="false"
                    appearance="outline"
                    bindLabel="positionName"
                    bindValue="positionId"
                    [items]="positions"
                    [searchFn]="onSearchKeywordPosition"
                    [clearable]="true"
                    formControlName="positionId"
                    placeholder="{{
                      'model.user.position' | placeholder : 'select'
                    }}"
                  >
                    <ng-option
                      [value]="item.positionId"
                      *ngFor="let item of positions"
                    >
                      {{ item.positionName }}
                    </ng-option>
                  </ng-select>
                  <small
                    class="form-option text-danger noti-small"
                    *ngIf="
                      formUser.get('positionId')?.errors?.required &&
                      formUser.get('positionId')?.touched
                    "
                  >
                    {{ "error.user.required.position" | translate }}
                  </small>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label
                    >{{ "model.user.role" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <!-- [ngModel]="currentValue" -->

                  <ng-select
                    [multiple]="true"
                    appearance="outline"
                    bindLabel="name"
                    bindValue="roleId"
                    [items]="roles"
                    [searchFn]="onSearchKeywordRole"
                    [clearable]="true"
                    formControlName="roleIds"
                    placeholder="{{
                      'model.user.role' | placeholder : 'select'
                    }}"
                  >
                    <ng-option [value]="role.roleId" *ngFor="let role of roles">
                      {{ role.name }}
                    </ng-option>
                  </ng-select>
                  <small
                    class="form-option text-danger noti-small"
                    *ngIf="
                      formUser.get('roleIds')?.errors?.required &&
                      formUser.get('roleIds')?.touched
                    "
                  >
                    {{ "error.user.required.role" | translate }}
                  </small>
                </div>
              </div>
              <div class="col-md-3" *ngIf="action === ROUTER_ACTIONS.detail">
                <div class="form-group">
                  <label
                    >{{ "common.status" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <ng-select
                    appearance="outline"
                    [searchable]="false"
                    [clearable]="false"
                    formControlName="status"
                  >
                    <ng-option
                      [value]="item.code"
                      *ngFor="let item of ENTITY_STATUS"
                    >
                      {{ item.label | translate }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="d-block text-center mb-5 mt-4">
          <button
            type="button"
            class="btn btn-white mr-2"
            data-toggle="modal"
            (click)="backToList()"
          >
            {{ "common.action.back" | translate }}
          </button>
          <ng-container
            *hasPrivileges="
              isUpdate ? SYSTEM_RULES.USER_WRITE : SYSTEM_RULES.USER_CREATE
            "
          >
            <button
              *ngIf="!(action === ROUTER_ACTIONS.detail)"
              class="btn btn-red"
              (click)="isUpdate ? onUpdate() : onCreate()"
            >
              {{
                (isUpdate ? "common.action.update" : "common.action.create")
                  | translate
              }}
            </button>
          </ng-container>
        </div>
      </div>
    </form>
  </div>
</section>
