import {
  Directive,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { fromEvent, Subject, timer } from 'rxjs';
import { debounce, distinctUntilChanged, takeUntil } from 'rxjs/operators';

@Directive({
  selector: '[appDebounceKeyup]',
})
export class DebounceKeyupDirective implements OnInit, OnDestroy {
  /**
   * Biến năng nghe khi không có event trong 1 khoảng time (delayTime)
   * thì nó sẽ giúp unsubscribe observable out value ra để keyupDelay emit
   *
   * @private
   * @memberof DebounceKeyupDirective
   */
  private destroy = new Subject<void>();

  /**
   * Khoảng time delay trước khi nhả value, mặc định để 0.4s
   *
   * @memberof DebounceKeyupDirective
   */
  @Input() delayTime = 400;

  /**
   * Event out value sẽ dùng thay thế hàm (keyup)
   *
   * @memberof DebounceKeyupDirective
   */
  @Output() keyupDelay = new EventEmitter<Event>();

  constructor(private elementRef: ElementRef<HTMLInputElement>) {}

  ngOnInit(): void {
    fromEvent(this.elementRef.nativeElement, 'keyup')
      .pipe(
        debounce(() => timer(this.delayTime)),
        distinctUntilChanged(
          null as any,
          (event: Event) => (event.target as HTMLInputElement).value
        ),
        takeUntil(this.destroy)
      )
      .subscribe((e) => this.keyupDelay.emit(e));
  }

  ngOnDestroy(): void {
    this.destroy.next();
  }
}
