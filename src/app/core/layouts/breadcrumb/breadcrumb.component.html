<ol class="breadcrumb float-sm-right mr-cus">
  <li class="breadcrumb-item">
    <a routerLink="/">Trang chủ</a>
  </li>
  <ng-template *ngFor let-breadcrumb [ngForOf]="breadcrumbs | async" let-last=last>
    <li class="breadcrumb-item" *ngIf="breadcrumb.label.title" [ngClass]="{active: last}">
      <a class="small" *ngIf="!last" [routerLink]="breadcrumb.url">{{ trickyProcess(breadcrumb.label.title, false) |
        translate }}</a>
      <span *ngIf="last" [routerLink]="breadcrumb.url">{{trickyProcess(breadcrumb.label.title, true) | translate
        }}</span>
    </li>
  </ng-template>
</ol>
