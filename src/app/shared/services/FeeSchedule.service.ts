import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { IFeeSchedule } from '@shared/models/FeeSchedule.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import CommonUtils from '@shared/utils/common-utils';
import { Observable } from 'rxjs/internal/Observable';
import { AbstractDomainService } from './common/abstract.domain.service';
import { EntityResponseType } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class FeeScheduleService extends AbstractDomainService<IFeeSchedule> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.FEE_SCHEDULE;

  create(request: IFeeSchedule): Observable<EntityResponseType<IFeeSchedule>> {
    const body = CommonUtils.appendObjectToFormData(request, new FormData());
    return super.post<EntityResponseType<IFeeSchedule>>(`/create`, body);
  }

  update(request: IFeeSchedule): Observable<EntityResponseType<IFeeSchedule>> {
    const body = CommonUtils.appendObjectToFormData(request, new FormData());
    return super.post<EntityResponseType<IFeeSchedule>>(`/update`, body);
  }

  lock(request: IFeeSchedule): Observable<EntityResponseType<IFeeSchedule>> {
    return super.post<EntityResponseType<IFeeSchedule>>(`/lock`, request);
  }

  unlock(request: IFeeSchedule): Observable<EntityResponseType<IFeeSchedule>> {
    return super.post<EntityResponseType<IFeeSchedule>>(`/unlock`, request);
  }
}
