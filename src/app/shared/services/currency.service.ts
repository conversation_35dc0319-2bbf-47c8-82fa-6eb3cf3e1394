import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { ICurrency } from '@shared/models/currency.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { AbstractDomainService } from './common/abstract.domain.service';

@Injectable({
  providedIn: 'root',
})
export class CurrencyService extends AbstractDomainService<ICurrency> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.CURRENCY;
}
