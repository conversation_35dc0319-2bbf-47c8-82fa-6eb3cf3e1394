<div class="clearfix">
  <div class="col-md-12">
    <div class="row">
      <div class="col-md-6" *ngFor="let item of imageUrls; let i = index">
        <!-- [nzPreview]="handlePreview" -->
        <nz-upload
          nzListType="picture-card"
          [nzName]="item.name"
          [nzIconRender]="iconRender"
          [nzShowUploadList]="false"
          [nzBeforeUpload]="beforeUpload"
          (nzChange)="handleChange($event, i)"
          [nzDisabled]="checkDisabled"
          [nzCustomRequest]="handleCustomUploadReq"
          class="list-img"
        >
          <div *ngIf="!item.src">
            <i class="bi bi-plus"></i>
            <div style="margin-top: 8px">
              {{ "common.uploadImage" | translate }}
            </div>
          </div>
          <ng-template #iconRender>
            {{ "common.uploading" | translate }}
          </ng-template>
          <img
            *ngIf="item.src"
            [src]="item.src"
            class="brand-image overflow-auto"
          />

          <div class="icon">
            <i
              *ngIf="item.src"
              class="bi bi-eye"
              (click)="handlePreview($event, i)"
            ></i>
            <i
              *ngIf="item.src && !checkDisabled"
              class="fa fa-times"
              (click)="onRemovePic($event, i)"
            ></i>
          </div>
        </nz-upload>
      </div>
    </div>
  </div>
</div>
