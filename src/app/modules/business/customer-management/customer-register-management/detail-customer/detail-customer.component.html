<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="onBackToList()" />
      <h5>{{ "customerRegisterManagement.detailTitle" | translate }}</h5>
    </div>
    <div class="col-md-12">
      <div class="row">
        <h2 class="title-create">
          {{ "customerRegisterManagement.customerDetail.title" | translate }}
        </h2>
      </div>
      <app-customer-info [customerInfo]="customer"> </app-customer-info>
      <app-customer-history-info [customerInfo]="customer">
      </app-customer-history-info>
      <app-customer-update-history
        [customerInfo]="customer"
      ></app-customer-update-history>
      <div class="d-block text-center mb-5 mt-4">
        <button
          type="button"
          class="btn btn-white mr-2"
          (click)="onBackToList()"
        >
          {{ "common.action.back" | translate }}
        </button>
        <ng-container
          *ngIf="
            customer.approvalStatus !== APPROVAL_STATUS_CONST.PENDING.value &&
            customer.status !== CUSTOMER_STATUS_CONST.CANCEL.value
          "
          ><button
            type="button"
            class="btn btn-red mr-2"
            (click)="onOpenUpdate()"
            *hasPrivileges="SYSTEM_RULES.CUSTOMER_WRITE"
          >
            {{ "common.action.update" | translate }}
          </button></ng-container
        >
      </div>
    </div>
  </div>
</section>
