import { Component, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  Validators,
} from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { TranslateService } from '@ngx-translate/core';
import {
  CALCULATE_MILLISECONDS_IN_A_YEAR,
  DATE_RANGE_STATISTICAL_LOAN_ONLINE,
  ENTITY_STATUS,
  ENTITY_TRANSACTION_STATUS_CONST,
  ENTITY_TRANSACTION_TYPE_CONST,
  FILE_EXTENSION,
  MOMENT_CONST,
  PAGINATION,
  TRANSACTION_ENTITY_STATUS,
  TRANSACTION_ENTITY_STATUS_MAP,
  TRANSACTION_ENTITY_TYPE_MAP,
  TRANSACTION_MMONEY_ENTITY_TYPE,
  TRANSACTION_TYPE_TRANSFER_MONEY_CONST,
  TRANSACTION_UMONEY_E<PERSON>ITY_TYPE,
  TRANSACTION_UMONEY_ENTITY_TYPE_CONST,
  TRANSACTION_UMONEY_ENTITY_TYPE_MAP,
  TYPE_BANK_CODE_MB_CONST,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { SELECTED_CONST } from '@shared/constants/user.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IMerchantTransactionHistory } from '@shared/models/merchant-transaction-history.model';
import { ITransactionSearch } from '@shared/models/request/transaction.search';
import { DownloadService } from '@shared/services/helpers/download.service';
import { TransactionService } from '@shared/services/transaction.service';
import { ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';

@Component({
  selector: 'app-transaction-report-mmoney',
  templateUrl: './transaction-report-mmoney.component.html',
  styleUrls: ['./transaction-report-mmoney.component.scss'],
})
export class TransactionReportMMoneyComponent implements OnInit {
  // transactionReport: ILoanOnline[] = [];
  transactionReport: IMerchantTransactionHistory[] = [];
  action: any = '';
  storage: any;
  maxDate = new Date();
  maxToDate = new Date();

  ROUTER_UTILS = ROUTER_UTILS;
  // input search
  pageSizeOptions = PAGINATION.OPTIONS;
  ENTITY_STATUS = ENTITY_STATUS;
  TRANSACTION_ENTITY_STATUS_MAP = TRANSACTION_ENTITY_STATUS_MAP;
  SYSTEM_RULES = SYSTEM_RULES;
  VALIDATORS = VALIDATORS;
  ENTITY_TRANSACTION_STATUS_CONST = ENTITY_TRANSACTION_STATUS_CONST;
  TRANSACTION_ENTITY_TYPE_MAP = TRANSACTION_ENTITY_TYPE_MAP;
  ENTITY_TRANSACTION_TYPE_CONST = ENTITY_TRANSACTION_TYPE_CONST;
  TYPE_BANK_CODE_MB_CONST = TYPE_BANK_CODE_MB_CONST;
  TRANSACTION_MMONEY_ENTITY_TYPE = TRANSACTION_MMONEY_ENTITY_TYPE;
  TRANSACTION_TYPE_TRANSFER_MONEY_CONST = TRANSACTION_TYPE_TRANSFER_MONEY_CONST;
  MOMENT_CONST = MOMENT_CONST;
  TRANSACTION_ENTITY_STATUS = TRANSACTION_ENTITY_STATUS;
  SELECTED_CONST = SELECTED_CONST;
  TRANSACTION_UMONEY_ENTITY_TYPE = TRANSACTION_UMONEY_ENTITY_TYPE;
  TRANSACTION_UMONEY_ENTITY_TYPE_MAP = TRANSACTION_UMONEY_ENTITY_TYPE_MAP;
  TRANSACTION_UMONEY_ENTITY_TYPE_CONST = TRANSACTION_UMONEY_ENTITY_TYPE_CONST;

  isValidMaxDate = (control: FormControl) => {
    const dateOfBirth = new Date(control.value).getTime();
    const maxDateOfBirth = new Date().getTime();
    return dateOfBirth > maxDateOfBirth ? { invalidMaxDate: true } : null;
  };

  // default form search
  formTransactionReportSearch = this.fb.group({
    keyword: '',
    fromDate: [null, [Validators.required]],
    toDate: [null, [Validators.required, this.isValidMaxDate]],
    billingTypes: [],
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    // bankcode: '',
    hasPageable: true,
    timeZoneStr: '',
    transactionStatus: [],
    // previousPageIndex: 0,
  });

  constructor(
    private transactionService: TransactionService,
    private fb: FormBuilder,
    private downloadService: DownloadService,
    private translateService: TranslateService
  ) {
    // get session storage
    this.storage = sessionStorage.getItem(STORAGE_APP.TRANSACTION_REPORT);
  }

  /**
   * Init data
   */
  ngOnInit(): void {
    // check storage
    // (when click back button or back website)
    this.formTransactionReportSearch
      .get('timeZoneStr')
      ?.setValue(Intl.DateTimeFormat().resolvedOptions().timeZone);
    if (this.storage) {
      // get session storage and parse json
      const filter = JSON.parse(this.storage) as ITransactionSearch;
      // set value form control
      this.formTransactionReportSearch.controls.keyword.setValue(
        filter.keyword
      );
      this.formTransactionReportSearch.controls.fromDate.setValue(
        filter.fromDate
      );
      this.formTransactionReportSearch.controls.toDate.setValue(filter.toDate);
      this.formTransactionReportSearch.controls.billingTypes.setValue(
        filter.billingTypes
      );
      this.formTransactionReportSearch.controls.transactionStatus.setValue(
        filter.transactionStatus
      );
      this.onSearch();
    } else {
      // set default value start date and end date
      const startDate = moment()
        .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
        .format(MOMENT_CONST.FORMAT_DEFAULT);
      const endDate = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
      // this.loanOnlineSearch.fromDate = startDate;
      // this.loanOnlineSearch.toDate = endDate;
      this.formTransactionReportSearch.controls.fromDate.setValue(
        CommonUtils.reverseDate(startDate)
      );
      this.formTransactionReportSearch.controls.toDate.setValue(
        CommonUtils.reverseDate(endDate)
      );
      this.onSearch();
    }
    // click form clear storage
    sessionStorage.removeItem(STORAGE_APP.TRANSACTION_REPORT);
  }

  /**
   * export file
   */
  exportFile(): void {
    // get value form control
    const bodySearch = this.formTransactionReportSearch.getRawValue();
    if (this.formTransactionReportSearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formTransactionReportSearch);
    } else {
      // get file name
      const fileName = this.translateService.instant(
        'template.transactionMMoneyReport'
      );
      const obFile = this.transactionService.exportMMoney({
        fromDate: bodySearch.fromDate,
        toDate: bodySearch.toDate,
        keyword: bodySearch.keyword,
        billingTypes: bodySearch.billingTypes,
        transactionStatus: bodySearch.transactionStatus,
        timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
      });

      this.downloadService.downloadFileWithObservableAndName(
        obFile,
        fileName,
        FILE_EXTENSION.XLSX
      );
    }
  }

  /**
   * Change page: next page and preview page
   *
   * @param page PageEvent
   */
  onChangePage(page: PageEvent) {
    this.formTransactionReportSearch.controls.pageIndex.setValue(
      page.pageIndex
    );
    this.formTransactionReportSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  dateValidator(value: number): any {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return value > DATE_RANGE_STATISTICAL_LOAN_ONLINE
        ? { invalidDate: true }
        : null;
    };
  }

  changeValidDate() {
    if (
      this.formTransactionReportSearch.controls.fromDate.value &&
      this.formTransactionReportSearch.controls.toDate.value
    ) {
      if (this.formTransactionReportSearch.controls['toDate'].value) {
        this.maxDate =
          this.formTransactionReportSearch.controls['toDate'].value;
      }
      const startDate = CommonUtils.setStartTime(
        new Date(
          this.formTransactionReportSearch.controls.fromDate.value
        ).getTime()
      );
      const endDate = CommonUtils.setStartTime(
        new Date(
          this.formTransactionReportSearch.controls.toDate.value
        ).getTime()
      );
      const dayMax =
        (endDate - startDate) / CALCULATE_MILLISECONDS_IN_A_YEAR.DAY;
      if (dayMax > DATE_RANGE_STATISTICAL_LOAN_ONLINE) {
        this.formTransactionReportSearch.controls.fromDate.setValidators([
          Validators.required,
          this.dateValidator(dayMax),
          this.isValidMaxDate,
        ]);
        this.formTransactionReportSearch.controls.fromDate.updateValueAndValidity();
      } else {
        this.formTransactionReportSearch.controls.fromDate.clearValidators();
        this.formTransactionReportSearch.controls.fromDate.setValidators([
          Validators.required,
          this.isValidMaxDate,
        ]);
        this.formTransactionReportSearch.controls.fromDate.updateValueAndValidity();
      }
    }
  }

  onSearchSubmit() {
    this.formTransactionReportSearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.formTransactionReportSearch.controls.length.setValue(
      PAGINATION.LENGTH_DEFAULT
    );
    this.onSearch();
  }

  /**
   * search form
   */
  onSearch(): void {
    if (this.formTransactionReportSearch.valid) {
      this.formTransactionReportSearch.controls.hasPageable.setValue(true);
      this.transactionService
        .searchMMoney(this.formTransactionReportSearch.value)
        .subscribe((res: any): void => {
          this.transactionReport = res.body.content;
          this.formTransactionReportSearch.controls.length.setValue(
            res.body.totalElements
          );
        });
    }
  }

  /**
   * index on list loan online
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formTransactionReportSearch.value.pageIndex,
      this.formTransactionReportSearch.value.pageSize
    );
  }

  // checkValidateDate() {
  //   this.maxDate = this.formTransactionReportSearch.controls['toDate'].value;
  // }

  /**
   * reset form
   */
  onReset(): void {
    // set default value date and reset data
    const startDateReset = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDateReset = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.formTransactionReportSearch.controls.keyword.reset();
    this.formTransactionReportSearch.controls.fromDate.setValue(
      CommonUtils.reverseDate(startDateReset)
    );
    this.formTransactionReportSearch.controls.toDate.setValue(
      CommonUtils.reverseDate(endDateReset)
    );
    this.formTransactionReportSearch.controls.fromDate.clearValidators();
    this.formTransactionReportSearch.controls.fromDate.setValidators([
      Validators.required,
    ]);
    this.formTransactionReportSearch.controls.fromDate.updateValueAndValidity();
    this.formTransactionReportSearch.controls.billingTypes.setValue(null);
    this.formTransactionReportSearch.controls.transactionStatus.setValue(null);
    this.formTransactionReportSearch
      .get('timeZoneStr')
      ?.setValue(Intl.DateTimeFormat().resolvedOptions().timeZone);
  }

  totalAmount(value = 0, fee = 0): number {
    return +value + +fee;
  }

  // onSelectAll(): void {
  //   const selectedTransferTypes = this.TRANSACTION_ENTITY_TYPE.map(
  //     (item) => item.code
  //   );

  //   this.formTransactionReportSearch
  //     .get('billingTypes')
  //     ?.patchValue(selectedTransferTypes);
  // }

  // onClearAll(): void {
  //   this.formTransactionReportSearch.get('billingTypes')?.patchValue([]);
  // }

  onSelectAll(value?: string) {
    const selectedTransferTypes = this.TRANSACTION_MMONEY_ENTITY_TYPE.map(
      (item) => item.code
    );
    const selectedTransactionStatus = this.TRANSACTION_ENTITY_STATUS.map(
      (item) => item.code
    );
    switch (value) {
      case SELECTED_CONST.TRANSACTION_TYPE:
        this.formTransactionReportSearch
          .get('billingTypes')
          ?.patchValue(selectedTransferTypes);
        break;
      case SELECTED_CONST.TRANSACTION_STATUS:
        this.formTransactionReportSearch
          .get('transactionStatus')
          ?.patchValue(selectedTransactionStatus);
        break;
    }
  }

  onClearAll(value?: string) {
    switch (value) {
      case SELECTED_CONST.TRANSACTION_TYPE:
        this.formTransactionReportSearch.get('billingTypes')?.patchValue([]);
        break;
      case SELECTED_CONST.TRANSACTION_STATUS:
        this.formTransactionReportSearch
          .get('transactionStatus')
          ?.patchValue([]);
        break;
    }
  }
}
