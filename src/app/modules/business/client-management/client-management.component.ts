import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import {
  CLIENT_TYPE,
  CLIENT_TYPE_CONST,
  CLIENT_TYPE_MAP,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  MODAL_ACTION,
  MOMENT_CONST,
  PAGINATION,
  SAVING_ACCOUNT_STATUS_MAP,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { LANGUAGES } from '@shared/constants/language.constants';

import { Router } from '@angular/router';
import { AbstractDomainManageComponent } from '@core/components/abstract-domain-manage/abstract-domain-manage.component';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IBaseResponseModel } from '@shared/models/base/base.model';
import { IClient } from '@shared/models/service-pack.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { ClientService } from '@shared/services/client.service';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-client-management',
  templateUrl: './client-management.component.html',
  styleUrls: ['./client-management.component.scss'],
})
export class ClientManagementComponent
  extends AbstractDomainManageComponent<any>
  implements OnInit
{
  resource: RESOURCE_TYPE = RESOURCE_CONST.SERVICE_PACK;
  searchForm: FormGroup = new FormGroup({});
  searchResults: IBaseResponseModel<IClient[]> = {};
  maxDate = new Date();
  data: IClient[] = [];

  pageSizeOptions = PAGINATION.OPTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  LANGUAGES = LANGUAGES;
  MOMENT_CONST = MOMENT_CONST;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  SAVING_ACCOUNT_STATUS_MAP = SAVING_ACCOUNT_STATUS_MAP;
  CLIENT_TYPE_CONST = CLIENT_TYPE_CONST;
  CLIENT_TYPE_MAP = CLIENT_TYPE_MAP;
  CLIENT_TYPE = CLIENT_TYPE;
  VALIDATORS = VALIDATORS;

  constructor(
    private fb: FormBuilder,
    protected router: Router,
    private downloadService: DownloadService,
    private clientService: ClientService,
    private translateService: TranslateService,
    private toastService: ToastrCustomService
  ) {
    super(clientService);
    this.searchForm = this.fb.group({
      keyword: '',
      types: [],
      status: null,
      fromDate: null,
      toDate: null,
      pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
      pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
      length: 0,
      hasPageable: true,
    });
  }

  ngOnInit(): void {
    super.ngOnInit();
  }

  onParseValueSearchForm(): void {
    super.onParseValueSearchForm();
  }

  onSearch() {
    if (this.searchForm.invalid) {
      CommonUtils.markFormGroupTouched(this.searchForm);
      return;
    }
    this.clientService.search(this.searchForm.value).subscribe((res: any) => {
      this.data = res.body.content;
      this.searchForm.controls.length.setValue(res.body.totalElements, {
        emitEvent: false,
      });
    });
  }

  onReset() {
    this.searchForm.controls.keyword.reset();
    this.searchForm.controls.fromDate.reset();
    this.searchForm.controls.toDate.reset();
    this.searchForm.controls.types.setValue(null);
    this.searchForm.controls.status.reset();
  }

  detail(id?: string) {
    this.router.navigate([ROUTER_UTILS.client.root, id, ROUTER_ACTIONS.detail]);
  }

  onUpdate(id?: string) {
    this.router.navigate([ROUTER_UTILS.client.root, id, ROUTER_ACTIONS.update]);
  }

  onCreate() {
    this.router.navigate([ROUTER_UTILS.client.root, ROUTER_ACTIONS.create]);
  }

  /**
   * check lock and unlock and call api
   *
   * @param customer ICustomer
   */
  lockAndUnlock(client?: IClient): void {
    if (client) {
      if (client.status === ENTITY_STATUS_CONST.INACTIVE.code) {
        this.unLock(client);
      } else {
        this.lock(client);
      }
    }
  }

  /**
   * button click edit
   *
   * @param userId number
   */
  edit(id?: string): void {
    this.router.navigate([ROUTER_UTILS.client.root, id, ROUTER_ACTIONS.update]);
  }

  /**
   * Lock user register
   *
   * @param user IUser
   */
  private lock(client?: IClient) {
    const modalData = {
      title: 'client.lock',
      content: 'client.lockContent',
      interpolateParams: { name: `<b>${client?.clientId || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { clientId: client?.clientId };
        this.clientService.lock(params).subscribe((res) => {
          this.toastService.success('common.action.lockSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * UnLock user register
   *
   * @param user: IUser
   */
  private unLock(client?: IClient) {
    const modalData = {
      title: 'client.unlock',
      content: 'client.unlockContent',
      interpolateParams: { name: `<b>${client?.clientId || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { clientId: client?.clientId };
        this.clientService.unlock(params).subscribe((res) => {
          this.toastService.success('common.action.unlockSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * delete
   *
   * @param user IUser
   */
  delete(client?: IClient): void {
    // open modal
    const modalData = {
      title: 'client.delete',
      content: 'client.deleteContent',
      interpolateParams: { name: `<b>${client?.clientId || ''}</b>` },
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { clientId: client?.clientId };
        this.clientService.delete(params).subscribe((res: any) => {
          this.toastService.success('common.action.deleteSuccess');
          this.onSearch();
        });
      }
    });
  }
}
