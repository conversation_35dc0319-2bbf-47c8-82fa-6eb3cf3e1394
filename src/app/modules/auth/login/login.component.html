<main class="bg-login d-flex align-items-center min-vh-100 py-3 py-md-0">
  <app-change-language></app-change-language>
  <div class="wrap-login">
    <div class="login-card">
      <div class="row no-gutters">
        <div class="col-lg-12 col-md-12">
          <div class="card-body">
            <div class="brand-wrapper text-center">
              <img src="../../assets/dist/img/mblogo.png" alt="logo login" />
            </div>
            <form role="form" [formGroup]="loginForm" (ngSubmit)="login()">
              <div class="form-group">
                <label>{{ "login.userName" | translate }}</label>
                <input
                  trim
                  #username
                  type="text"
                  class="w-100"
                  name="username"
                  formControlName="username"
                  class="form-control"
                  placeholder="{{ 'login.userName' | translate }}"
                />
                <div
                  *ngIf="
                    loginForm.get('username')?.invalid &&
                    (loginForm.get('username')?.dirty ||
                      loginForm.get('username')?.touched)
                  "
                >
                  <small
                    class="form-text text-danger"
                    *ngIf="loginForm.get('username')?.errors?.required"
                  >
                    {{ "login.error.userNameEmpty" | translate }}
                  </small>
                </div>
              </div>
              <div class="form-group position-relative">
                <label>{{ "login.password" | translate }}</label>
                <input
                  trim
                  #password
                  [type]="passwordVisible ? 'text' : 'password'"
                  name="password"
                  formControlName="password"
                  class="form-control"
                  placeholder="***********"
                />
                <span
                  class="icon-eye cursor-pointer"
                  (click)="onChangPasswordVisible()"
                >
                  <i
                    [classList]="passwordVisible ? 'bi-eye-slash' : 'bi bi-eye'"
                  ></i>
                </span>
                <div
                  *ngIf="
                    loginForm.get('password')?.invalid &&
                    (loginForm.get('password')?.dirty ||
                      loginForm.get('password')?.touched)
                  "
                >
                  <small
                    class="form-text text-danger"
                    *ngIf="loginForm.get('password')?.errors?.required"
                  >
                    {{ "login.error.passwordEmpty" | translate }}
                  </small>
                </div>
              </div>
              <!-- <div
                                class="row"
                                *ngIf="captchaImageSource"
                            > -->
              <div
                class="form-group position-relative"
                *ngIf="captchaImageSource"
              >
                <div class="d-code">
                  <div class="form-group">
                    <label for>{{ "login.captcha" | translate }}</label>
                    <input
                      trim
                      type="text"
                      name
                      id
                      class="form-control"
                      placeholder
                      formControlName="captcha"
                    />
                    <div
                      class="noti-small"
                      *ngIf="
                        loginForm.get('captcha')!.invalid &&
                        (loginForm.get('captcha')!.dirty ||
                          loginForm.get('captcha')!.touched)
                      "
                    >
                      <small
                        class="form-text text-danger noti-small"
                        *ngIf="loginForm.get('captcha')?.errors?.required"
                      >
                        {{ "login.error.captchaEmpty" | translate }}
                      </small>
                    </div>
                    <!-- <small id="helpId" class="text-muted">Help text</small> -->
                  </div>
                  <span class="btn-refresh">
                    <img [src]="captchaImageSource" alt />
                    <button
                      class="btn"
                      style="margin-top: 20px; background: white"
                      (click)="getCaptcha()"
                    >
                      <i class="fas fa-sync"></i>
                    </button>
                  </span>
                </div>
              </div>
              <button class="btn btn-block login-btn mb-4 mt-5" type="submit">
                {{ "login.loginButton" | translate }}
              </button>
              <div class="text-center">
                <div>
                  <a
                    class="title-link"
                    [href]="[ROUTER_UTILS.portal.forgotPassword.root]"
                    >{{ "public.forgotPassword.title" | translate }}?</a
                  >
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>
