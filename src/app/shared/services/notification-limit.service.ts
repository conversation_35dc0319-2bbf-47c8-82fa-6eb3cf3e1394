import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  INotificationLimitTransaction,
  INotificationTransaction,
} from '@shared/models/notification-limit-transaction.model';
import { INotificationLimitSearch } from '@shared/models/request/notification-limit.search';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({ providedIn: 'root' })
export class NotificationLimitService extends AbstractService {
  public resourceUrl = '/notification-limit';

  constructor(protected http: HttpClient) {
    super(http);
  }

  /**
   * API search
   *
   * @param request INotificationLimitSearch
   * @returns Observable<EntityResponseType<INotificationLimitTransaction>>
   */
  search(
    request: INotificationLimitSearch,
    loading = false
  ): Observable<EntityResponseType<INotificationLimitTransaction>> {
    return super.post<EntityResponseType<INotificationLimitTransaction>>(
      `${this.resourceUrl}/search`,
      request,
      { loading }
    );
  }

  /**
   * API search
   *
   * @param request INotificationLimitSearch
   * @returns Observable<EntityResponseType<INotificationLimitTransaction>>
   */
  countUnread(
    request: INotificationLimitSearch,
    loading = false
  ): Observable<EntityResponseType<INotificationTransaction>> {
    return super.post<EntityResponseType<INotificationTransaction>>(
      `${this.resourceUrl}/number-notification`,
      request,
      { loading }
    );
  }

  /**
   * API export
   *
   * @param request INotificationLimitSearch
   * @returns Observable<any>
   */
  export(request: INotificationLimitSearch): Observable<any> {
    return super.postFile(`${this.resourceUrl}/export`, request);
  }

  /**
   * API lock
   *
   * @param request INotificationLimitSearch
   * @returns Observable<EntityResponseType<INotificationLimitSearch>>
   */
  notContacted(
    request: INotificationLimitTransaction
  ): Observable<EntityResponseType<INotificationLimitTransaction>> {
    return super.post<EntityResponseType<INotificationLimitTransaction>>(
      `${this.resourceUrl}/not-contacted`,
      request
    );
  }

  /**
   * API unlock
   *
   * @param request INotificationLimitSearch
   * @returns Observable<EntityResponseType<INotificationLimitSearch>>
   */
  contacted(
    request: INotificationLimitTransaction
  ): Observable<EntityResponseType<INotificationLimitTransaction>> {
    return super.post<EntityResponseType<INotificationLimitTransaction>>(
      `${this.resourceUrl}/contacted`,
      request
    );
  }
}
