<div class="modal-content">
  <div class="modal-header">
    <h5 class="modal-title">
      {{ "common.action.syncReport" | translate }}
    </h5>
    <button
      type="button"
      class="close"
      data-dismiss="modal"
      aria-label="Close"
      (click)="activeModal.close()"
    >
      <span aria-hidden="true"><i class="bi bi-x"></i></span>
    </button>
  </div>
  <div class="modal-body">
    <form
      [formGroup]="form"
      *ngIf="form"
      (keydown.enter)="$event.preventDefault()"
    >
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-12">
            <div class="date-picker-container form-group row">
              <div class="col-md-8">
                <label
                  >{{ "common.action.syncDate" | translate
                  }}<span class="text-danger">*</span></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="syncDate"
                    formControlName="syncDate"
                    placeholder="DD/MM/YYYY"
                    [max]="maxDate"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="syncDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #syncDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    form.get('syncDate')?.errors?.required &&
                    form.get('syncDate')?.touched
                  "
                >
                  {{
                    "model.report.transaction.configAutoReport.error.required.syncDate"
                      | translate
                  }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    form.get('syncDate')?.errors?.invalidDate &&
                    form.get('syncDate')?.touched
                  "
                >
                  {{ "error.required.inValidDate" | translate }}
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="text-center pb-4">
    <button
      type="button"
      class="btn mb-btn-outline-color mr-3"
      (click)="activeModal.close()"
    >
      {{ "common.action.close" | translate }}
    </button>
    <ng-container>
      <button
        type="button "
        [disabled]="this.form.invalid"
        class="btn mb-btn-color"
        (click)="syncReport()"
      >
        {{ "common.action.syncReport" | translate }}
      </button>
    </ng-container>
  </div>
</div>
