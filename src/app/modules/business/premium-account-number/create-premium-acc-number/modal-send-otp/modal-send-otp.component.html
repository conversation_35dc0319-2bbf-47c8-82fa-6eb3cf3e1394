<div class="modal-content">
  <div class="modal-header">
    <h5 class="modal-title">
      {{ "common.action.confirmOtp" | translate }}
    </h5>
    <h5 class="modal-title"></h5>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="activeModal.close()">
      <span aria-hidden="true"><i class="bi bi-x"></i></span>
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="formConfirm">
      <div class="col-md-12">
        <label class="mb-3 mr-2">
          {{
          "model.premiumAccountNumber.otpExpried" | translate
          }}
        </label>
        <span class="countdown-timer" *ngIf="timeLeft">{{ timeLeft }}</span>
      </div>
      <div class="col-md-12 mb-2">
        <label class="mb-3 mr-2">
          {{
          "model.customer.otp" | translate
          }}
        </label>
        <input trim formControlName="otpValue" numbersOnly [maxLength]="VALIDATORS.LENGTH.OTP_MAX_LENGTH" type="text"
               class="w-100" class="form-control" placeholder="{{
            'model.customer.otp' | translate
          }}" />
        <small class="form-text text-danger noti-small" *ngIf="
          formConfirm.get('otpValue')?.errors?.required &&
          formConfirm.get('otpValue')?.touched
        ">
          {{
          "premiumAccNumber.error.required.otpValue"
          | translate
          }}
        </small>
        <small class="form-text text-danger noti-small" *ngIf="
        formConfirm.get('otpValue')?.errors?.minlength &&
        formConfirm.get('otpValue')?.touched
                    ">
          {{
          "premiumAccNumber.error.minLength.otp"
          | translate
          }}
        </small>
      </div>
      <a *ngIf="isHidden" href="#" class="col-md-12 mb-2"
         (click)="resendOTP($event)"
         [class.disabled]="isLoading">
        Gửi lại mã OTP
      </a>
      <span *ngIf="isLoading">Đang gửi...</span>
    </form>
  </div>
  <div class="text-center pb-4">
    <button type="button" class="btn mb-btn-outline-color mr-3" (click)="activeModal.close()">
      {{ "common.action.close" | translate }}
    </button>
    <ng-container>
      <button type="button" class="btn mb-btn-color" (click)="onConfirmOtp()" [disabled]="formConfirm.invalid"
              *hasPrivileges="SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_CREATE">
        {{ "common.action.confirmOtp" | translate }}
      </button>
    </ng-container>
  </div>
</div>
