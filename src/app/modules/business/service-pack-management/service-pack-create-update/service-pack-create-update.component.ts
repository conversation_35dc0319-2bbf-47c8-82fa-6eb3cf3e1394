import { <PERSON>mpo<PERSON>, HostL<PERSON>ener, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import {
  CLIENT_TYPE_CONST,
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  FILE_UPLOAD_EXTENSIONS,
  GENDER,
  MODAL_ACTION,
  TRANCSACTION_FEE_CONST
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ISearchWithPagination } from '@shared/models/base/base-request.model';
import { TransactionFeeTypeSearch } from '@shared/models/request/transaction-fee-type.search';
import {
  IClient,
  IServicePack,
  IServicePackFunction
} from '@shared/models/service-pack.model';
import { ITelco } from '@shared/models/telco.model';
import { ITransactionFeeType } from '@shared/models/transaction-fee-type.model';
import { ClientService } from '@shared/services/client.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { MerchantService } from '@shared/services/merchant.service';
import { ServicePackService } from '@shared/services/service-pack.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import { TelcoService } from './../../../../shared/services/telco.service';

@Component({
  selector: 'app-service-pack-create-update',
  templateUrl: './service-pack-create-update.component.html',
  styleUrls: ['./service-pack-create-update.component.scss'],
})
export class ServicePackCreateUpdateComponent implements OnInit, OnDestroy {
  form: FormGroup = new FormGroup({});

  // input call api
  servicePack: IServicePack = {};
  reader: FileReader = new FileReader();
  action = '';
  hasFilter = false;
  isRequiredCarrieType = false;
  isRequiredType = false;
  maxDateOfBirth = new Date();
  clients: IClient[] = [];
  servicePackTypes: ITransactionFeeType[] = [];
  telcos: ITelco[] = [];
  transactionFeeSearch: TransactionFeeTypeSearch = {};
  // servicePackFunctions: FormArray = new FormArray([]);

  ROUTER_UTILS = ROUTER_UTILS;
  ENTITY_STATUS = ENTITY_STATUS;
  VALIDATORS = VALIDATORS;
  FILE_UPLOAD_EXTENSIONS = FILE_UPLOAD_EXTENSIONS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  GENDER_LIST = GENDER;
  SYSTEM_RULES = SYSTEM_RULES;

  constructor(
    private formBuilder: FormBuilder,
    private servicePackService: ServicePackService,
    private clientService: ClientService,
    private toastService: ToastrCustomService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private modalService: ModalService,
    private merchantService: MerchantService,
    private telcoService: TelcoService,
  ) {
    this.activatedRoute.data.subscribe((res) => {
      this.action = res.action;
    });
    // get id form paramater
    this.activatedRoute.paramMap.subscribe((res) => {
      const idParam = res.get('id');
      if (idParam) {
        this.servicePack.servicePackId = +idParam;
      }
    });
  }

  /**
   * remove storage
   */
  ngOnDestroy(): void {
    if (!this.hasFilter) {
      sessionStorage.removeItem(STORAGE_APP.USER);
    }
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.hasFilter = true;
  }

  ngOnInit(): void {
    this.getClient();
    if (this.action === ROUTER_ACTIONS.create) {
      this.initForm();
    } else {
      this.getDetail();
    }
  }

  /**
   * init form
   *
   * @param user User
   */
  initForm(servicePack?: IServicePack): void {
    this.form = this.formBuilder.group({
      clientId: [
        {
          value: servicePack?.clientId || null,
          disabled: false,
        },
        [Validators.required, Validators.pattern(VALIDATORS.PATTERN.USERNAME)],
      ],
      name: [
        {
          value: servicePack?.name || '',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.NO_SPECIAL_CHARACTERS),
        ],
      ],
      code: [
        {
          value: servicePack?.code || '',
          disabled: this.action !== ROUTER_ACTIONS.create,
        }
      ],
      type: [
        {
          value: servicePack?.type || null,
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [Validators.required],
      ],
      carrierType: [
        {
          value: servicePack?.carrierType || null,
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
      ],
      status: [
        {
          value: servicePack?.status || 0,
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
      ],
      servicePackFunctions: new FormArray([]),
    });
    if (this.action === ROUTER_ACTIONS.create) {
      this.addServicePackFunction();
      this.form.controls.type.disable();
      this.form.controls.carrierType.disable();
    }
  }

  get servicePackFunctions(): FormArray | undefined {
    return this.form?.get('servicePackFunctions') as FormArray;
  }

  addServicePackFunction(): void {
    this.servicePackFunctions?.push(this.createServicePackFunction());
    // this.onShowDelete();
  }

  createServicePackFunction(item?: IServicePackFunction) {
    return this.formBuilder.group({
      url: [
        {
          value: item?.url || '',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [Validators.required],
      ],
      name: [
        {
          value: item?.name || '',
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
        [Validators.required, Validators.pattern(VALIDATORS.PATTERN.NO_SPECIAL_CHARACTERS)],
      ],
      servicePackId: [
        {
          value: item?.servicePackId || null,
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
      ],
      servicePackFunctionId: [
        {
          value: item?.servicePackFunctionId || null,
          disabled: this.action === ROUTER_ACTIONS.detail,
        },
      ],
    });
  }

  onDelete(index: number, group: AbstractControl) {
    if (group.value?.name !== '' || group.value?.url !== '') {
      const modalData = {
        title: 'model.customerSupport.titleConfirm',
        content: 'servicePack.deleteContentUrl',
        interpolateParams: { name: `<b>${group.get('name')?.value || ''}</b>` },
      };
      this.modalService.confirm(modalData).then((result) => {
        if (result === MODAL_ACTION.CONFIRM.code) {
          this.servicePackFunctions?.removeAt(index);
        }
      });
    } else {
      this.servicePackFunctions?.removeAt(index);
    }
  }

  getClient(): void {
    const params = {
      status: ENTITY_STATUS_CONST.ACTIVE.code,
      hasPageable: false,
    };
    this.clientService.search(params as ISearchWithPagination).subscribe((response: any) => {
      this.clients = response.body.content;
      this.clients.map((i) => { i.fullName = i.clientId + ' - ' + (i.name ? i.name : ''); return i; });
    });
  }

  getServicePackTypes(): void {
    this.servicePackService
      .getAllServicePackType(this.transactionFeeSearch)
      .subscribe((response: any) => {
        this.servicePackTypes = response.body?.content;
      });
  }

  /**
   * back to monitor list
   */
  backToList(): void {
    this.hasFilter = true;
    this.router.navigate([ROUTER_UTILS.servicePack.root]);
  }

  /**
   * get detail user
   */
  getDetail(): void {
    if (this.servicePack?.servicePackId) {
      this.servicePackService.detail(this.servicePack).subscribe((res: any) => {
        this.servicePack = res.body;
        const data = res.body || undefined;
        this.initForm(data);
        this.servicePack.fullName = (this.servicePack?.clientId + ' - ' + (this.servicePack?.client?.name || '')) || '';
        this.patchValueToForm(data);
        this.servicePack?.servicePackFunctions?.forEach((item) => {
          this.servicePackFunctions?.push(this.createServicePackFunction(item));
        });
      });
    }
  }

  /**
   * Create: check validate file and data input if valid then call api create
   */
  onCreate(): void {
    // touched form
    if (this.form.invalid) {
      CommonUtils.markFormGroupTouched(this.form);
    }
    const data = this.form.getRawValue();
    if (this.form.valid) {
      this.servicePackService.create({ ...data }).subscribe((res): void => {
        this.router.navigate([ROUTER_UTILS.servicePack.root]);
        this.toastService.success('common.action.createSuccess');
      });
    }
  }

  /**
   * Update: check validate file and data input if valid then call api create
   */
  onUpdate(): void {
    // touched form
    if (this.form.invalid) {
      CommonUtils.markFormGroupTouched(this.form);
    }
    const data = this.form.getRawValue();
    data.servicePackId = this.servicePack?.servicePackId;
    if (this.form.valid) {
      this.servicePackService.update(data).subscribe((res): void => {
        this.router.navigate([ROUTER_UTILS.servicePack.root]);
        this.toastService.success('common.action.updateSuccess');
      });
    }
  }

  customSearchFn(term: string, item: IClient) {
    if (item.clientId) {
      term = term.replace(/ + /g, ' ');
      term = term.trim();
      term = term.toLocaleLowerCase();
      return item.clientId.toLocaleLowerCase().indexOf(term) > -1;
    }
    return '';
  }

  getTransactionCarrieType(event: any): void {
    // this.transactionFeeTypeId = event;
    const search = {
      billingType: 0
    };
    if (event === TRANCSACTION_FEE_CONST.TOPUP.name
      || event === TRANCSACTION_FEE_CONST.INTERNET_BILLING.name
      || event === TRANCSACTION_FEE_CONST.POST_PAID_BILLING.name
      || event === TRANCSACTION_FEE_CONST.PSTN_BILLING.name) {
      this.isRequiredCarrieType = true;
      if (event === TRANCSACTION_FEE_CONST.TOPUP.name) {
        search.billingType = 3;
      } else if (event === TRANCSACTION_FEE_CONST.INTERNET_BILLING.name) {
        search.billingType = 2;
      } else if (event === TRANCSACTION_FEE_CONST.POST_PAID_BILLING.name) {
        search.billingType = 0;
      } else if (event === TRANCSACTION_FEE_CONST.PSTN_BILLING.name) {
        search.billingType = 1;
      }

      this.telcoService.search(search).subscribe((res: any) => {
        this.telcos = res.body?.content;
      });
      this.form.controls.carrierType.setValidators([
        Validators.required,
      ]);
      this.form.controls.carrierType.enable();
      this.form.controls.carrierType.updateValueAndValidity();
    } else {
      this.isRequiredCarrieType = false;
      this.telcos = [];
      this.form.controls.carrierType.disable();
      this.form.controls.carrierType.reset();
      this.form.controls.carrierType.clearValidators();
      this.form.controls.carrierType.updateValueAndValidity();
    }
  }

  patchValueToForm(servicePack: IServicePack) {
    const search = {
      billingType: 0
    };

    if (servicePack?.type !== undefined && servicePack?.type !== null && servicePack.clientId !== 'mbbank' && servicePack.clientId !== 'umoney') {
      this.getServicePackTypes();
      this.isRequiredType = true;
      this.form.controls.type.enable();
    } else {
      this.form.controls.type.disable();
      this.form.controls.type.reset();
      this.form.controls.type.clearValidators();
      this.form.controls.type.updateValueAndValidity();
    }

    if (servicePack?.type === TRANCSACTION_FEE_CONST.TOPUP.name
      || servicePack?.type === TRANCSACTION_FEE_CONST.INTERNET_BILLING.name
      || servicePack?.type === TRANCSACTION_FEE_CONST.POST_PAID_BILLING.name
      || servicePack?.type === TRANCSACTION_FEE_CONST.PSTN_BILLING.name) {
      if (servicePack?.type === TRANCSACTION_FEE_CONST.TOPUP.name) {
        search.billingType = 3;
      } else if (servicePack?.type === TRANCSACTION_FEE_CONST.INTERNET_BILLING.name) {
        search.billingType = 2;
      } else if (servicePack?.type === TRANCSACTION_FEE_CONST.POST_PAID_BILLING.name) {
        search.billingType = 0;
      } else if (servicePack?.type === TRANCSACTION_FEE_CONST.PSTN_BILLING.name) {
        search.billingType = 1;
      }
      this.isRequiredCarrieType = true;
      this.telcoService.search(search).subscribe((res: any) => {
        this.telcos = res.body?.content;
      });
      this.form.controls.carrierType.enable();
    } else {
      this.isRequiredCarrieType = false;
      this.telcos = [];
      this.form.controls.carrierType.disable();
    }
  }

  changeServicePackType(client: IClient): void {
    if (client.type === CLIENT_TYPE_CONST.INTERNAL.code) {
      this.getServicePackTypes();
      this.isRequiredType = true;
      this.form.controls.type.setValidators([
        Validators.required,
      ]);
      this.form.controls.type.enable();
      this.form.controls.type.updateValueAndValidity();
    } else {
      this.isRequiredType = false;
      this.isRequiredCarrieType = false;
      this.form.controls.type.disable();
      this.form.controls.type.reset();
      this.form.controls.type.clearValidators();
      this.form.controls.type.updateValueAndValidity();
      this.form.controls.carrierType.reset();
      this.form.controls.carrierType.disable();
      this.form.controls.carrierType.clearValidators();
      this.form.controls.carrierType.updateValueAndValidity();
      this.telcos = [];
      this.servicePackTypes = [];
    }
  }

  onEdit() {
    this.router.navigate([
      ROUTER_UTILS.servicePack.root,
      this.servicePack.servicePackId,
      ROUTER_ACTIONS.update,
    ]);
  }

  hiddenButtonAdd(): boolean {
    if (this.action !== ROUTER_ACTIONS.detail && this.servicePackFunctions && this.servicePackFunctions.length < 15) {
      return true;
    }
    return false;
  }
}
