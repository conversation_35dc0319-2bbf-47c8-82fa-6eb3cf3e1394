/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
import { Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

/**
 * Append character
 *
 * <AUTHOR>
 * @date 2021-12-24
 * @export
 * @class JoinPipe
 * @implements {PipeTransform}
 * @howToUse
 * ```
 *     <some-element>{{ user?.positionNames | join: ', ' }}</some-element>
 * ```
 */
@Pipe({
  name: 'placeholder',
})
export class PlaceholderTranslatePipe implements PipeTransform {
  constructor(private translateService: TranslateService) {}

  transform(input?: any, character?: string): string {
    if (character === 'select') {
      return this.translateService.instant('common.placeholder.select', {
        params: this.translateService.instant(input).toLowerCase(),
      });
    }
    return this.translateService.instant('common.placeholder.input', {
      params: this.translateService.instant(input).toLowerCase(),
    });
  }
}
