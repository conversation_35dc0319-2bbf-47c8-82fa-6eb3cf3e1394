export interface IConfigAuto {
  title?: string;
  content?: string;
  transactionStatuses?: string;
  status?: number;
  sendTime?: any;
  emails?: [];
  intervalMinute?: number;
  configureAutomaticReportId?: number;
  type?: string;
  emailRecipients?: any;
  asyncDate?: string;
}
export class ConfigAuto implements IConfigAuto {
  constructor(
    public title?: string,
    public content?: string,
    public transactionStatuses?: string,
    public status?: number,
    public sendTime?: any,
    public emails?: [],
    public configureAutomaticReportId?: number,
    public type?: string,
    public intervalMinute?: number,
    public emailRecipients?: any,
    public asyncDate?: string
  ) {}
}
