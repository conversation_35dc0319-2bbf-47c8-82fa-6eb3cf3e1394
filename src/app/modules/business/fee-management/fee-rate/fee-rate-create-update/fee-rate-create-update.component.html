<div class="modal-content">
  <div class="modal-header" [ngClass]="isHiddenBtnClose ? 'border-0' : ''">
    <h5 class="modal-title" *ngIf="!(action === ROUTER_ACTIONS.detail)">
      {{
        (action === ROUTER_ACTIONS.update
          ? "feeRate.updateTitle"
          : "feeRate.createTitle"
        ) | translate
      }}
    </h5>
    <h5 class="modal-title" *ngIf="action === ROUTER_ACTIONS.detail">
      {{ "feeRate.detailTitle" | translate }}
    </h5>
    <button
      type="button"
      class="close"
      data-dismiss="modal"
      aria-label="Close"
      (click)="activeModal.close()"
    >
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <form
      [formGroup]="feeRateForm"
      *ngIf="
        action === ROUTER_ACTIONS.create
          ? action === ROUTER_ACTIONS.create
          : feeRate.feeRateId
      "
    >
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-12">
            <div class="form-group">
              <label
                >{{ "feeRate.feeRateName" | translate }}
                <span class="text-danger">*</span></label
              >
              <input
                trim
                formControlName="feeRateName"
                placeholder="{{ 'feeRate.feeRateName' | translate }}"
                type="text"
                class="w-100"
                class="form-control"
                [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
              />
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  feeRateForm.get('feeRateName')?.errors?.required &&
                  feeRateForm.get('feeRateName')?.touched
                "
              >
                {{ "error.feeRate.required.feeRateName" | translate }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  feeRateForm.get('feeRateName')?.errors?.pattern &&
                  feeRateForm.get('feeRateName')?.touched
                "
              >
                {{ "error.feeRate.pattern.feeRateName" | translate }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  feeRateForm.get('feeRateName')?.errors?.maxlength &&
                  feeRateForm.get('feeRateName')?.touched
                "
              >
                {{
                  "error.feeRate.maxLength.feeRateName"
                    | translate
                      : {
                          param: VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH
                        }
                }}
              </small>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-6">
                <div class="form-group">
                  <label
                    >{{ "currency.root" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <ng-select
                    placeholder="{{ 'currency.root' | placeholder : 'select' }}"
                    [searchable]="false"
                    formControlName="currency"
                    [clearable]="true"
                    appAutoValidate
                  >
                    <ng-option
                      [value]="item?.code"
                      *ngFor="let item of currencies"
                    >
                      {{ item.name + "" || "" | translate }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-12">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label
                    >{{ "feeRate.transactionAmountLakMin" | translate }}
                    <span class="text-danger">*</span></label
                  >
                  <input
                    formControlName="transactionAmountMin"
                    (ngModelChange)="procesAccountAmount($event)"
                    type="text"
                    placeholder="{{
                      'feeRate.transactionAmountMin' | translate
                    }}"
                    [maxLength]="VALIDATORS.LENGTH.AMOUNT_DECIMAL_MAX_LENGTH"
                    class="w-100"
                    class="form-control"
                    (change)="validateFeeRate()"
                    [min]="MAX_MIN_VALUE_CONST.MIN"
                    appNumberInputDecimal
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      feeRateForm.get('transactionAmountMin')?.touched &&
                      feeRateForm.get('transactionAmountMin')?.errors?.required
                    "
                  >
                    {{
                      "error.feeRate.required.transactionAmountMin" | translate
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      feeRateForm.get('transactionAmountMin')?.value >
                        VALIDATORS.LENGTH.TRANSACTION_AMOUNT_MAX_LENGTH &&
                      feeRateForm.get('transactionAmountMin')?.touched
                    "
                  >
                    {{
                      "error.feeRate.required.transactionAmountMinLength"
                        | translate
                    }}
                  </small>

                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      isErrorAmountMinMustSmallerAmountMax &&
                      !feeRateForm.get('transactionAmountMin')?.errors?.required
                    "
                  >
                    {{
                      "error.feeRate.required.amountMinMustSmallerAmountMax"
                        | translate
                    }}
                  </small>
                </div>
              </div>

              <div class="col-md-6">
                <div class="form-group">
                  <label
                    >{{ "feeRate.transactionAmountLakMax" | translate }}
                    <span class="text-danger">*</span></label
                  >
                  <input
                    formControlName="transactionAmountMax"
                    (ngModelChange)="procesAccountAmount($event)"
                    type="text"
                    placeholder="{{
                      'feeRate.transactionAmountMax' | translate
                    }}"
                    [maxLength]="VALIDATORS.LENGTH.AMOUNT_DECIMAL_MAX_LENGTH"
                    [min]="1"
                    class="w-100"
                    class="form-control"
                    (change)="validateFeeRate()"
                    appNumberInputDecimal
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      feeRateForm.get('transactionAmountMax')?.errors
                        ?.required &&
                      feeRateForm.get('transactionAmountMax')?.touched
                    "
                  >
                    {{
                      "error.feeRate.required.transactionAmountMax" | translate
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      feeRateForm.get('transactionAmountMax')?.errors?.min &&
                      feeRateForm.get('transactionAmountMax')?.touched
                    "
                  >
                    {{ "error.feeRate.min.transactionAmountMax" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      feeRateForm.get('transactionAmountMax')?.value >
                        VALIDATORS.LENGTH.TRANSACTION_AMOUNT_MAX_LENGTH &&
                      feeRateForm.get('transactionAmountMax')?.touched
                    "
                  >
                    {{
                      "error.feeRate.required.transactionAmountMaxLength"
                        | translate
                    }}
                  </small>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-12">
            <div class="row">
              <div
                class="col-md-6"
                *ngIf="configurationFeeType === CONFIGURATION_FEE_TYPE.FEE.code"
              >
                <div class="form-group">
                  <label
                    >{{ "feeRate.amountFee" | translate }}
                    <span class="text-danger">*</span></label
                  >
                  <input
                    formControlName="feeAmount"
                    type="text"
                    (ngModelChange)="procesAccountAmount($event)"
                    placeholder="{{ 'feeRate.amountFee' | translate }}"
                    [maxLength]="VALIDATORS.LENGTH.AMOUNT_DECIMAL_MAX_LENGTH"
                    [min]="MAX_MIN_VALUE_CONST.MIN"
                    class="w-100"
                    class="form-control"
                    (change)="validateFeeRate()"
                    appNumberInputDecimal
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      configurationFeeType ===
                        CONFIGURATION_FEE_TYPE.FEE.code &&
                      (feeRateForm.get('feeAmount')?.value < 0 ||
                        feeRateForm.get('feeAmount')?.value === null ||
                        feeRateForm.get('feeAmount')?.value === '') &&
                      feeRateForm.get('feeAmount')?.touched
                    "
                  >
                    {{ "error.feeRate.required.feeAmount" | translate }}
                  </small>

                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="isErrorAmountFeeMustSmallerAmountMax"
                  >
                    {{
                      "error.feeRate.required.amountFeeMustSmallerAmountMax"
                        | translate
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      feeRateForm.get('feeAmount')?.value >
                        VALIDATORS.LENGTH.TRANSACTION_AMOUNT_MAX_LENGTH &&
                      feeRateForm.get('feeAmount')?.touched &&
                      !isErrorAmountFeeMustSmallerAmountMax
                    "
                  >
                    {{
                      "error.feeRate.required.transactionAmountMaxLength"
                        | translate
                    }}
                  </small>
                </div>
              </div>

              <div
                class="col-md-6"
                *ngIf="configurationFeeType === CONFIGURATION_FEE_TYPE.FEE.code"
              >
                <div class="form-group">
                  <label
                    >{{ "feeRate.VAT" | translate }}
                    <span class="text-danger">*</span></label
                  >
                  <input
                    formControlName="vat"
                    type="number"
                    (ngModelChange)="procesAccountAmount($event)"
                    placeholder="{{ 'feeRate.VAT' | translate }}"
                    class="w-100"
                    class="form-control"
                    [max]="MAX_MIN_VALUE_CONST.MAX"
                    [min]="MAX_MIN_VALUE_CONST.MIN"
                    (change)="validateFeeRate()"
                    appNumberInput
                    trim
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      configurationFeeType ===
                        CONFIGURATION_FEE_TYPE.FEE.code &&
                      (feeRateForm.get('vat')?.value === '' ||
                        feeRateForm.get('vat')?.value === null) &&
                      feeRateForm.get('vat')?.touched
                    "
                  >
                    {{ "error.feeRate.required.vat" | translate }}
                  </small>

                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      configurationFeeType ===
                        CONFIGURATION_FEE_TYPE.FEE.code &&
                      feeRateForm.get('vat')?.value < 0 &&
                      feeRateForm.get('vat')?.touched
                    "
                  >
                    {{ "error.feeRate.required.vatMin" | translate }}
                  </small>

                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      feeRateForm.get('vat')?.value >
                        VALIDATORS.LENGTH.PERCENT &&
                      feeRateForm.get('vat')?.touched
                    "
                  >
                    {{ "error.feeRate.required.vatMinLength" | translate }}
                  </small>
                </div>
              </div>
              <div
                class="col-md-6"
                *ngIf="
                  configurationFeeType === CONFIGURATION_FEE_TYPE.DISCOUNT.code
                "
              >
                <div class="form-group">
                  <label
                    >{{ "feeRate.percentDiscount" | translate }}
                    <span class="text-danger">*</span></label
                  >
                  <input
                    formControlName="discountPercent"
                    type="number"
                    [max]="MAX_MIN_VALUE_CONST.MAX"
                    [min]="MAX_MIN_VALUE_CONST.MIN"
                    (ngModelChange)="procesAccountAmount($event)"
                    placeholder="{{ 'feeRate.percentDiscount' | translate }}"
                    (change)="validateFeeRate()"
                    class="w-100"
                    class="form-control"
                    appNumberFormatter
                    appNumberMaxMin
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      configurationFeeType ===
                        CONFIGURATION_FEE_TYPE.DISCOUNT.code &&
                      !feeRateForm.get('discountPercent')?.value &&
                      feeRateForm.get('discountPercent')?.touched
                    "
                  >
                    {{ "error.feeRate.required.discountPercent" | translate }}
                  </small>

                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      feeRateForm.get('discountPercent')?.value >
                        VALIDATORS.LENGTH.PERCENT &&
                      feeRateForm.get('discountPercent')?.touched
                    "
                  >
                    {{
                      "error.feeRate.required.discountPercentMinLength"
                        | translate
                    }}
                  </small>
                </div>
              </div>
              <div
                class="col-md-6"
                *ngIf="
                  configurationFeeType ===
                    CONFIGURATION_FEE_TYPE.DISCOUNT.code &&
                  (transactionFeeTypeId === 5 || transactionFeeTypeId === 6)
                "
              >
                <div class="form-group">
                  <label>{{ "feeRate.fixedDiscount" | translate }}</label>
                  <input
                    formControlName="discountFixed"
                    placeholder="{{ 'feeRate.fixedDiscount' | translate }}"
                    class="w-100"
                    class="form-control"
                    [maxlength]="VALIDATORS.LENGTH.NUMBER_MONEY_MAX_LENGTH"
                    (ngModelChange)="procesAccountAmount($event)"
                    appNumberFormatter
                  />
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-12">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label>{{
                    "feeRate.moneyMinusAccountMin" | translate
                  }}</label>
                  <input
                    type="text"
                    formControlName="moneyMinusAccountMin"
                    readonly
                    placeholder="{{ 'feeRate.from' | translate }}"
                    class="w-100"
                    class="form-control"
                  />
                </div>
              </div>

              <div class="col-md-6">
                <div class="form-group">
                  <label>{{
                    "feeRate.moneyMinusAccountMax" | translate
                  }}</label>
                  <input
                    type="text"
                    formControlName="moneyMinusAccountMax"
                    readonly
                    placeholder="{{ 'feeRate.to' | translate }}"
                    class="w-100"
                    class="form-control"
                  />
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-12">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label
                    >{{ "feeRate.startEffectTime" | translate }}
                    <span class="text-danger">*</span></label
                  >
                  <!-- <input
                    type="datetime-local"
                    formControlName="effectiveAt"
                    class="w-100"
                    class="form-control"
                    max="{{ MAX_DATE_CONST }}"
                    (change)="changeValidDate()"
                  /> -->
                  <mat-form-field appearance="fill" class="date-picker">
                    <input
                      matInput
                      formControlName="effectiveAt"
                      [ngxMatDatetimePicker]="effectiveAt"
                      placeholder="HH:mm:ss, DD/MM/YYYY"
                      [max]="MAX_DATE_CONST"
                      (change)="changeValidDate()"
                      (dateInput)="changeValidDate()"
                      readonly
                    />
                    <mat-datepicker-toggle
                      matSuffix
                      [for]="$any(effectiveAt)"
                    ></mat-datepicker-toggle>
                    <ngx-mat-datetime-picker #effectiveAt [showSeconds]="true">
                    </ngx-mat-datetime-picker>
                  </mat-form-field>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      feeRateForm.get('effectiveAt')?.errors?.required &&
                      feeRateForm.get('effectiveAt')?.touched
                    "
                  >
                    {{ "error.feeRate.required.effectiveAt" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      feeRateForm.get('effectiveAt')?.errors?.invalidMaxDate &&
                      feeRateForm.get('effectiveAt')?.touched
                    "
                  >
                    {{ "error.startTimeMustGreatherEndTime" | translate }}
                  </small>
                </div>
              </div>

              <div class="col-md-6">
                <div class="form-group">
                  <label
                    >{{ "feeRate.endEffectTime" | translate }}
                    <span class="text-danger">*</span></label
                  >
                  <!-- <input
                    type="datetime-local"
                    formControlName="expiredAt"
                    class="w-100"
                    class="form-control"
                    max="{{ MAX_DATE_CONST }}"
                    (change)="changeValidDate()"
                  /> -->
                  <mat-form-field appearance="fill" class="date-picker">
                    <input
                      matInput
                      formControlName="expiredAt"
                      [ngxMatDatetimePicker]="expiredAt"
                      placeholder="HH:mm:ss, DD/MM/YYYY"
                      [max]="MAX_DATE_CONST"
                      (change)="changeValidDate()"
                      (dateInput)="changeValidDate()"
                      readonly
                    />
                    <mat-datepicker-toggle
                      matSuffix
                      [for]="$any(expiredAt)"
                    ></mat-datepicker-toggle>
                    <ngx-mat-datetime-picker #expiredAt [showSeconds]="true">
                    </ngx-mat-datetime-picker>
                  </mat-form-field>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      feeRateForm.get('expiredAt')?.errors?.required &&
                      feeRateForm.get('expiredAt')?.touched
                    "
                  >
                    {{ "error.feeRate.required.expiredAt" | translate }}
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="text-right pb-4 mr-3">
    <button
      type="button"
      class="btn mb-btn-outline-color mr-3"
      (click)="activeModal.close()"
    >
      {{ "common.action.close" | translate }}
    </button>
    <ng-container
      *hasPrivileges="
        action === ROUTER_ACTIONS.update && !(action === ROUTER_ACTIONS.detail)
          ? SYSTEM_RULES.FEE_RATE_WRITE
          : SYSTEM_RULES.FEE_RATE_CREATE
      "
    ></ng-container>
    <button
      type="button"
      class="btn mb-btn-color"
      (click)="action === ROUTER_ACTIONS.update ? onUpdate() : onCreate()"
      *ngIf="!(action === ROUTER_ACTIONS.detail)"
    >
      {{
        (action === ROUTER_ACTIONS.update
          ? "common.action.update"
          : "common.action.create"
        ) | translate
      }}
    </button>
  </div>
</div>
