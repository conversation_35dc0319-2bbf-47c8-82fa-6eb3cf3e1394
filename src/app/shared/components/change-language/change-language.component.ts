import { Component, OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { LANGUAGES } from '@shared/constants/language.constants';
import { STORAGE_LANGUAGES } from '@shared/constants/storage.constants';
import { LanguageService } from '@shared/services/helpers/language.service';
import { LocalStorageService } from 'ngx-webstorage';
@Component({
  selector: 'app-change-language',
  templateUrl: './change-language.component.html',
  styleUrls: ['./change-language.component.scss'],
})
export class ChangeLanguageComponent implements OnInit {
  currentLanguage = '';
  pageTitle = '';
  LANGUAGES = Object.values(LANGUAGES);

  constructor(
    private languageHelper: LanguageService,
    private router: Router,
    private localStorage: LocalStorageService
  ) {}

  ngOnInit(): void {
    this.currentLanguage =
      this.localStorage.retrieve(STORAGE_LANGUAGES) || LANGUAGES.VI.code;

    this.pageTitle = this.languageHelper.getPageTitle(
        this.router.routerState.snapshot.root
      );

    this.router.events.subscribe((event: any) => {
        if (event instanceof NavigationEnd) {
          this.pageTitle = this.languageHelper.getPageTitle(
            this.router.routerState.snapshot.root
          );
        }
      });
  }

  /**
   * Change lang
   *
   * @param {string} lang
   * @memberof NavbarComponent
   */
  onChangeLanguage(lang: string): void {
    if (lang !== this.currentLanguage) {
      this.localStorage.store(STORAGE_LANGUAGES, lang);
      location.reload();
    }
  }
}
