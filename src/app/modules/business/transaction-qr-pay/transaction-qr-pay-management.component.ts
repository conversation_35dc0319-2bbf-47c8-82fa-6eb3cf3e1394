import { Component, OnInit } from '@angular/core';
import {
  A<PERSON>tractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import {
  CALCULATE_MILLISECONDS_IN_A_YEAR,
  DATE_RANGE_STATISTICAL_LOAN_ONLINE,
  ENTITY_TRANSACTION_STATUS_CONST,
  FILE_EXTENSION,
  MOMENT_CONST,
  PAGINATION,
  TRANSACTION_ENTITY_STATUS,
  TRANSACTION_ENTITY_STATUS_MAP,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { LANGUAGES } from '@shared/constants/language.constants';

import { Router } from '@angular/router';
import { AbstractDomainManageComponent } from '@core/components/abstract-domain-manage/abstract-domain-manage.component';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { SELECTED_CONST } from '@shared/constants/user.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IBaseResponseModel } from '@shared/models/base/base.model';
import { ITransactionQrPay } from '@shared/models/transaction-qr-pay.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { DownloadService } from '@shared/services/helpers/download.service';
import { TransactionQrPayService } from '@shared/services/transaction-qr-pay.service';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';
import { NzTableQueryParams } from 'ng-zorro-antd/table';

@Component({
  selector: 'app-transaction-qr-pay-management',
  templateUrl: './transaction-qr-pay-management.component.html',
  styleUrls: ['./transaction-qr-pay-management.component.scss'],
})
export class TransactionQrPayManagementComponent
  extends AbstractDomainManageComponent<any>
  implements OnInit
{
  resource: RESOURCE_TYPE = RESOURCE_CONST.TRANSACTION_QR_PAY;
  searchForm: FormGroup = new FormGroup({});
  searchResults: IBaseResponseModel<ITransactionQrPay[]> = {};
  maxDate = new Date();
  data: ITransactionQrPay[] = [];
  maxToDate = new Date();

  pageSizeOptions = PAGINATION.OPTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  LANGUAGES = LANGUAGES;
  MOMENT_CONST = MOMENT_CONST;
  TRANSACTION_ENTITY_STATUS = TRANSACTION_ENTITY_STATUS;
  TRANSACTION_ENTITY_STATUS_MAP = TRANSACTION_ENTITY_STATUS_MAP;
  ENTITY_TRANSACTION_STATUS_CONST = ENTITY_TRANSACTION_STATUS_CONST;
  SELECTED_CONST = SELECTED_CONST;
  VALIDATORS = VALIDATORS;

  isValidMaxDate = (control: FormControl) => {
    const dateOfBirth = new Date(control.value).getTime();
    const maxDateOfBirth = new Date().getTime();
    return dateOfBirth > maxDateOfBirth ? { invalidMaxDate: true } : null;
  };

  constructor(
    private fb: FormBuilder,
    protected router: Router,
    private downloadService: DownloadService,
    private transactionQrPayService: TransactionQrPayService,
    private translateService: TranslateService
  ) {
    super(transactionQrPayService);
    const startDate = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDate = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.searchForm = this.fb.group({
      keyword: '',
      partnerId: '',
      transactionStatus: [],
      types: [],
      fromDate: [CommonUtils.reverseDate(startDate), [Validators.required]],
      toDate: [
        CommonUtils.reverseDate(endDate),
        [Validators.required, this.isValidMaxDate],
      ],
      pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
      pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
      orderByType: '',
      length: 0,
      hasPageable: true,
    });
  }

  ngOnInit(): void {
    super.ngOnInit();
  }

  onParseValueSearchForm(): void {
    super.onParseValueSearchForm();
  }

  onSearch() {
    if (this.searchForm.invalid) {
      CommonUtils.markFormGroupTouched(this.searchForm);
      return;
    }
    this.transactionQrPayService
      .search(this.searchForm.value)
      .subscribe((res: any) => {
        this.data = res.body.content;
        this.searchForm.controls.length.setValue(res.body.totalElements, {
          emitEvent: false,
        });
      });
  }

  onReset() {
    this.searchForm.controls.keyword.reset();
    this.searchForm.controls.transactionStatus.reset();
    this.searchForm.controls.partnerId.reset();
    const startDateReset = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDateReset = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.searchForm.controls.fromDate.setValue(
      CommonUtils.reverseDate(startDateReset)
    );
    this.searchForm.controls.toDate.setValue(
      CommonUtils.reverseDate(endDateReset)
    );
    this.searchForm.controls.fromDate.clearValidators();
    this.searchForm.controls.fromDate.setValidators([Validators.required]);
    this.searchForm.controls.fromDate.updateValueAndValidity();
  }

  onChangePageSort(params: NzTableQueryParams): void {
    this.transactionQrPayService
      .search(this.searchForm.value)
      .subscribe((res: any) => {
        this.data = res.body.content;
        this.searchForm.controls.length.setValue(res.body.totalElements, {
          emitEvent: false,
        });
      });
  }

  dateValidator(value: number): any {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return value > DATE_RANGE_STATISTICAL_LOAN_ONLINE
        ? { invalidDate: true }
        : null;
    };
  }

  changeValidDate() {
    if (
      this.searchForm.controls.fromDate.value &&
      this.searchForm.controls.toDate.value
    ) {
      if (this.searchForm.controls['toDate'].value) {
        this.maxDate = this.searchForm.controls['toDate'].value;
      }
      const startDate = CommonUtils.setStartTime(
        new Date(this.searchForm.controls.fromDate.value).getTime()
      );
      const endDate = CommonUtils.setStartTime(
        new Date(this.searchForm.controls.toDate.value).getTime()
      );
      const dayMax =
        (endDate - startDate) / CALCULATE_MILLISECONDS_IN_A_YEAR.DAY;
      if (dayMax > DATE_RANGE_STATISTICAL_LOAN_ONLINE) {
        this.searchForm.controls.fromDate.setValidators([
          Validators.required,
          this.dateValidator(dayMax),
          this.isValidMaxDate,
        ]);
        this.searchForm.controls.fromDate.updateValueAndValidity();
      } else {
        this.searchForm.controls.fromDate.clearValidators();
        this.searchForm.controls.fromDate.setValidators([
          Validators.required,
          this.isValidMaxDate,
        ]);
        this.searchForm.controls.fromDate.updateValueAndValidity();
      }
    }
  }

  onSelectAll(value?: string) {
    const selectedTransactionStatus = this.TRANSACTION_ENTITY_STATUS.map(
      (item) => item.code
    );
    switch (value) {
      case SELECTED_CONST.TRANSACTION_STATUS:
        this.searchForm
          .get('transactionStatus')
          ?.patchValue(selectedTransactionStatus);
        break;
    }
  }

  onClearAll(value?: string) {
    switch (value) {
      case SELECTED_CONST.TRANSACTION_STATUS:
        this.searchForm.get('transactionStatus')?.patchValue([]);
        break;
    }
  }

  /**
   * export file
   */
  exportFile(): void {
    // get value form control
    const bodySearch = this.searchForm.getRawValue();
    if (this.searchForm.invalid) {
      CommonUtils.markFormGroupTouched(this.searchForm);
    } else {
      // get file name
      const fileName = this.translateService.instant(
        'template.transactionQrpay'
      );
      const obFile = this.transactionQrPayService.export({
        fromDate: bodySearch.fromDate,
        toDate: bodySearch.toDate,
        keyword: bodySearch.keyword,
        partnerId: bodySearch.partnerId,
        transactionStatus: bodySearch.transactionStatus,
      });

      this.downloadService.downloadFileWithObservableAndName(
        obFile,
        fileName,
        FILE_EXTENSION.XLSX
      );
    }
  }
}
