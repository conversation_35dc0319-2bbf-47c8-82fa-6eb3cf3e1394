<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formSearch" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-md-6 col-lg-4">
            <div class="form-group">
              <label>{{ "common.action.searchKeyword" | translate }}</label>
              <input trim type="text" placeholder="{{ 'common.action.searchKeyword' | placeholder }}"
                formControlName="keyword" class="w-100" class="form-control"
                [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH" />
            </div>
          </div>
          <div class="col-md-6 col-lg-4">
            <div class="form-group">
              <label>{{ "common.status" | translate }}</label>
              <ng-select placeholder="{{ 'common.status' | placeholder : 'select' }}" [searchable]="false"
                formControlName="status" [clearable]="true">
                <ng-option [value]="item.code" *ngFor="let item of SPECIAL_ACCOUNT_NUMBER_STATUS">
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-1">
            <div class="form-group">
              <mat-checkbox class="check-box-group" formControlName="hided">
                <label class="font-weight-bold">
                  {{ "model.specialAccountNumber.hiddenNumber" | translate }}
                </label>
              </mat-checkbox>
            </div>
          </div>
          <div class="col-lg-2 col-md-2 mt-4 d-flex">
            <div class="col-btn-reset" ngbTooltip="{{ 'common.action.reset' | translate }}">
              <div class="btn-reset">
                <i class="bi bi-arrow-clockwise" type="button" (click)="onReset()">
                </i>
              </div>
            </div>
            <div>
              <button class="btn btn-search mr-2" type="submit">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-flex justify-content-between align-items-end text-right mb-3 mt-4">
          <h5 class="title-table text-uppercase mb-4">
            {{ "model.specialAccountNumber.list" | translate }}
          </h5>
          <div class="d-block text-right mb-3 mt-4">
            <button type="button" class="btn btn-red mr-2" (click)="onOpenSpecialAccount()" *hasPrivileges="
            SYSTEM_RULES.SPECIAL_PREMIUM_ACCOUNT_NUMBER_CREATE
          ">
              {{ "common.action.create" | translate }}
            </button>
            <button type="button" class="btn btn-white mr-2" (click)="importFile()" *hasPrivileges="
                SYSTEM_RULES.SPECIAL_PREMIUM_ACCOUNT_NUMBER_IMPORT
              ">
              {{ "common.action.uploadFiles" | translate }}
            </button>
            <button type="button" class="btn btn-white" (click)="onExport()"
              *hasPrivileges="SYSTEM_RULES.SPECIAL_PREMIUM_ACCOUNT_NUMBER_EXPORT">
              {{ "common.action.export" | translate }}
            </button>
          </div>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table table-customer">
          <thead>
            <tr>
              <th class="text-center">{{ "model.rate.no" | translate }}</th>
              <th class="text-left">
                {{ "common.accountNumber" | translate }}
              </th>
              <th class="text-right">
                {{ "model.structureAccount.listPrice" | translate }}
              </th>
              <th class="text-right">
                {{ "model.structureAccount.discount" | translate }}
              </th>
              <th class="text-right">
                {{ "model.structureAccount.paymentPrice" | translate }}
              </th>
              <th class="text-left">
                {{ "model.campaign.lastModifiedBy" | translate }}
              </th>
              <th class="text-center">
                {{ "model.campaign.lastModifiedDate" | translate }}
              </th>
              <th class="text-left">
                {{ "model.campaign.createdBy" | translate }}
              </th>
              <th class="text-center">
                {{ "model.versionManage.createdDate" | translate }}
              </th>
              <th class="text-center hidden-freezer" [width]="'140px'" scope="col">
                {{ "model.specialAccountNumber.numberType" | translate }}
              </th>
              <th class="text-center status-freezer" scope="col">
                {{ "common.status" | translate }}
              </th>
              <th class="text-center freezer" scope="col">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of data; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td class="text-left">
                {{ item?.specialPremiumAccountNumber }}
              </td>
              <td class="text-right">
                {{ item?.price | currencyLak }}
              </td>
              <td class="text-right">
                {{ item?.discount }}
              </td>
              <td class="text-right">{{ item?.totalPrice | currencyLak }}</td>
              <td class="text-left">
                {{ item?.lastModifiedBy }}
              </td>
              <td class="text-center">
                {{ item?.lastModifiedDate }}
              </td>
              <td class="text-left">{{ item?.createdBy }}</td>
              <td class="text-center">{{ item?.createdDate }}</td>
              <td class="text-center hidden-freezer-td">
                <!-- <span class="badge" [ngClass]="HIDDEN_ACCOUNT_NUMBER_MAP[item?.hided || 0]?.style">{{
                  HIDDEN_ACCOUNT_NUMBER_MAP[item?.hided || 0]?.label
                  | translate
                  }}</span> -->
                <i class="item-hidden bi bi-award-fill h4" *ngIf="item?.hided"></i>
              </td>
              <td class="text-center status-freezer-td">
                <span class="badge" *ngIf="SPECIAL_ACCOUNT_NUMBER_STATUS_MAP[item.status || 0]"
                  [ngClass]="SPECIAL_ACCOUNT_NUMBER_STATUS_MAP[item.status || 0].style">{{
                  SPECIAL_ACCOUNT_NUMBER_STATUS_MAP[item.status || 0].label | translate
                  }}</span>
              </td>
              <td class="text-left freezer-td">
                <ng-container>
                  <button ngbTooltip="{{ 'common.action.detail' | translate }}" class="btn px-1 py-0"
                    (click)="detail(item.specialPremiumAccountNumberId)"
                    *hasPrivileges="SYSTEM_RULES.SPECIAL_PREMIUM_ACCOUNT_NUMBER_READ">
                    <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                  </button>
                  <ng-container *ngIf="item.status != SPECIAL_ACCOUNT_NUMBER_STATUS_CONST.SOLD.code">
                    <button ngbTooltip="{{ 'common.action.update' | translate }}" class="btn px-1 py-0" *hasPrivileges="
                      SYSTEM_RULES.SPECIAL_PREMIUM_ACCOUNT_NUMBER_UPDATE
                    " (click)="update(item?.specialPremiumAccountNumberId)">
                      <i class="fa fa-edit mb-color" aria-hidden="true"></i>
                    </button>
                    <button [ngbTooltip]="
                      (item.hided
                        ? 'common.action.unhide'
                        : 'common.action.hide'
                      ) | translate
                    " class="btn px-1 py-0" (click)="hideAndUnHide(item)" *hasPrivileges="
                      item.hided
                        ? SYSTEM_RULES.SPECIAL_PREMIUM_ACCOUNT_NUMBER_UNHIDE
                        : SYSTEM_RULES.SPECIAL_PREMIUM_ACCOUNT_NUMBER_HIDE
                    ">
                      <i [className]="
                        item.hided
                          ? 'fa fa-unlock mb-color'
                          : 'fa fa-lock mb-color'
                      " aria-hidden="true"></i>
                    </button>
                    <button ngbTooltip="{{ 'common.action.delete' | translate }}" class="btn px-1 py-0" *hasPrivileges="
                      SYSTEM_RULES.SPECIAL_PREMIUM_ACCOUNT_NUMBER_DELETE
                    " (click)="onDelete(item)">
                      <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                    </button>
                  </ng-container>

                </ng-container>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="row d-block text-center m-0 no-search-result-wrapper" *ngIf="data?.length === 0">
          <img src="/assets/dist/img/icon/empty.svg" height="120" alt="no_search_result" />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
      </div>
      <div *ngIf="data?.length" class="paginator col-md-12">
        <mat-paginator [length]="formSearch.value.length" [pageSize]="formSearch.value.pageSize"
          [pageIndex]="formSearch.value.pageIndex" [pageSizeOptions]="pageSizeOptions" (page)="onChangePage($event)"
          aria-label="Select page">
        </mat-paginator>
      </div>
    </div>
  </div>
</section>