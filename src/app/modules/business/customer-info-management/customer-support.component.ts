import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import {
  CONFIG_PHONE_CONST,
  MODAL_ACTION
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ICustomerSupport } from '@shared/models/customer-support.model';
import { CustomerSupportService } from '@shared/services/customer-support.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-customer-support',
  templateUrl: './customer-support.component.html',
  styleUrls: ['./customer-support.component.scss'],
})
export class CustomerSupportComponent implements OnInit {
  CONFIG_PHONE_CONST = CONFIG_PHONE_CONST;
  SYSTEM_RULES = SYSTEM_RULES;
  VALIDATORS = VALIDATORS;
  formCreate: FormGroup = new FormGroup({});
  phoneNumbers: FormArray = new FormArray([]);
  isShowDelete: boolean[] = [];
  data: ICustomerSupport = {};

  constructor(
    private fb: FormBuilder,
    private customerSupportService: CustomerSupportService,
    private toastService: ToastrCustomService,
    private modalService: ModalService,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.customerSupportService.detail({}).subscribe((res: any) => {
      if (Object.keys(res.body).length !== 0) {
        this.data = res.body;
        this.getPhoneNumber(res.body);
      } else {
        this.addPhone();
        this.isShowDelete[CONFIG_PHONE_CONST.PHONE_DEFAULT] = false;
      }
    });
  }

  getPhoneNumber(data?: ICustomerSupport) {
    this.formCreate.get('email')?.setValue(data?.email);
    this.formCreate.get('address')?.setValue(data?.address);

    this.phoneNumbers = this.formCreate.get('phoneNumbers') as FormArray;

    this.data?.customerSupportPhoneNumbers?.forEach((item: any) => {
      this.phoneNumbers.push(this.createPhone(item.phoneNumber));
    });
    if (
      this.data?.customerSupportPhoneNumbers?.length ===
      CONFIG_PHONE_CONST.PHONE_DEFAULT_LENGTH
    ) {
      this.isShowDelete[CONFIG_PHONE_CONST.PHONE_DEFAULT] = false;
    } else {
      this.onShowDelete();
    }
  }

  initForm() {
    this.formCreate = this.fb.group({
      address: ['', [Validators.required]],
      email: [
        '',
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.EMAIL_REPORT),
        ],
      ],
      phoneNumbers: new FormArray([]),
    });
  }

  addPhone(): void {
    this.phoneNumbers = this.formCreate.get('phoneNumbers') as FormArray;
    this.phoneNumbers.push(this.createPhone());
    this.onShowDelete();
  }

  createPhone(phone?: any) {
    return this.fb.group({
      phone: [
        phone,
        [
          Validators.required,
          Validators.pattern(
            VALIDATORS.PATTERN.PHONE_NUMBER_CUSTOMER_SUPPORT_REGEX
          ),
        ],
      ],
    });
  }

  onDelete(index: number, group: AbstractControl) {
    if (
      this.phoneNumbers.controls.length >
      CONFIG_PHONE_CONST.PHONE_DEFAULT_LENGTH
    ) {
      if (group.value.phone !== null) {
        const modalData = {
          title: 'model.customerSupport.titleConfirm',
          content: 'model.customerSupport.contentConfirm',
        };
        this.modalService.confirm(modalData).then((result) => {
          if (result === MODAL_ACTION.CONFIRM.code) {
            this.phoneNumbers.removeAt(index);
            this.onHideDelete();
          }
        });
      } else {
        this.phoneNumbers.removeAt(index);
      }
      this.onHideDelete();
    }
  }

  onHideDelete() {
    if (this.phoneNumbers.controls.length === CONFIG_PHONE_CONST.PHONE_DEFAULT_LENGTH
    ) {
      this.isShowDelete[CONFIG_PHONE_CONST.PHONE_DEFAULT] = false;
    }
  }

  onShowDelete() {
    this.phoneNumbers.controls.forEach((item, i: number) => {
      this.isShowDelete[i] = true;
    });
  }

  onSubmit() {
    if (this.formCreate.invalid) {
      CommonUtils.markFormGroupTouched(this.formCreate);
      return;
    }
    const phoneNumbers: number[] = [];
    this.phoneNumbers.controls.forEach((element) => {
      phoneNumbers.push(element.value.phone);
    });
    const body = {
      ...this.formCreate.value,
      phoneNumbers,
    };
    const serviceEvent =
      Object.keys(this.data).length === 0
        ? this.onCreate(body)
        : this.onUpdate(body);
  }

  onCreate(body: ICustomerSupport): void {
    this.customerSupportService.create(body).subscribe((responsive: any) => {
      this.toastService.success('common.action.createSuccess');
      this.customerSupportService.detail({}).subscribe((res: any) => {
        if (Object.keys(res.body).length !== 0) {
          this.data = res.body;
        }
      });
    });
  }

  onUpdate(body: ICustomerSupport): void {
    this.customerSupportService.update(body).subscribe((res: any) => {
      this.toastService.success('common.action.updateSuccess');
    });
  }
}
