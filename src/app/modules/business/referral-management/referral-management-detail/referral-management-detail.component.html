<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="backToList()" />
      <h5>
        {{ "referral.titleDetail" | translate }}
      </h5>
    </div>
    <form [formGroup]="formReferralDetail">
      <div class="col-md-12">
        <div class="row">
          <h5 class="modal-title">
            {{ "referral.titleDetail" | translate }}
          </h5>
        </div>
        <div class="row border-create">
          <h3>{{ "referral.titleInfoDetail" | translate }}</h3>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.referral.userFullName" | translate }}</label>
                  <input
                    trim
                    formControlName="userFullName"
                    type="text"
                    class="w-100"
                    class="form-control"
                  />
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.customer.phoneNumber" | translate }}</label>
                  <input
                    trim
                    formControlName="phoneNumber"
                    type="text"
                    class="w-100"
                    class="form-control border-input"
                  />
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.referral.type" | translate }}</label>
                  <input
                    formControlName="type"
                    type="text"
                    class="w-100"
                    class="form-control"
                  />
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.referral.userCode" | translate }}</label>
                  <input
                    trim
                    formControlName="userCode"
                    type="text"
                    class="w-100"
                    class="form-control"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.referral.rmCode" | translate }}</label>
                  <input
                    trim
                    formControlName="rmCode"
                    type="text"
                    class="w-100"
                    class="form-control"
                  />
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.referral.referralCode" | translate }}</label>
                  <input
                    trim
                    formControlName="referralCode"
                    type="text"
                    class="w-100"
                    class="form-control"
                  />
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.referral.rmLink" | translate }}</label>
                  <div class="box">
                    <input
                      trim
                      formControlName="rmLink"
                      class="form-control"
                      id="input"
                    />
                    <button
                      class="btn btn-secondary"
                      id="copy"
                      (click)="copyText()"
                    >
                      <i class="fa fa-copy"></i>
                    </button>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>{{ "model.referral.quantity" | translate }}</label>
                  <input
                    trim
                    formControlName="quantity"
                    type="text"
                    class="w-100"
                    class="form-control"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12" *ngIf="qrCode">
            <div class="row">
              <div class="col-md-12">
                <div class="form-group" #qrElement>
                  <div class="qrcode-left">
                    <qrcode
                      #qrCodeData
                      [qrdata]="qrCode"
                      [width]="256"
                      [errorCorrectionLevel]="'M'"
                      (imageSrc)="getImage($event)"
                      (qrCodeURL)="onChangeURL($event)"
                      id="printQrcode"
                      class="printQrcodeClass"
                    ></qrcode>
                  </div>

                  <button
                    class="bi bi-printer btn-action bi-bi-printer btn btn-white col-lg-2 mr-2"
                    nz-button
                    nzType="default"
                    (click)="this.generatePdf(BUTTON_ACTION_CONST.PRINT)"
                  >
                    <span>
                      {{ "common.action.print" | translate }}</span
                    ></button
                  ><button
                    class="bi bi-download btn-action btn btn-white col-lg-2"
                    nz-button
                    nzType="default"
                    (click)="this.generatePdf(BUTTON_ACTION_CONST.DOWNLOAD)"
                  >
                    <span> {{ "common.action.download" | translate }}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="d-block text-center mb-5 mt-4">
          <button
            type="button"
            class="btn btn-white mr-2"
            data-toggle="modal"
            (click)="backToList()"
          >
            {{ "common.action.back" | translate }}
          </button>
        </div>
      </div>
    </form>
  </div>
</section>
