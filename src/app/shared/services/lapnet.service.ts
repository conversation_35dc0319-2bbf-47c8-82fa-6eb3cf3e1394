import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ILapnetReport } from '@shared/models/lapnet-report.model';
import { ILapnetSearch } from '@shared/models/request/lapnet.search';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({ providedIn: 'root' })
export class LapnetService extends AbstractService {
  public resourceUrl = '/lapnet-report';

  constructor(protected http: HttpClient) {
    super(http);
  }

  /**
   * API search
   *
   * @param request ILapnetSearch
   * @returns Observable<EntityResponseType<ILapnetReport>>
   */
  search(
    request: ILapnetSearch
  ): Observable<EntityResponseType<ILapnetReport>> {
    return super.post<EntityResponseType<ILapnetReport>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  /**
   * API export
   *
   * @param request ILapnetSearch
   * @returns Observable<any>
   */
  export(request: ILapnetSearch): Observable<any> {
    return super.postFile(`${this.resourceUrl}/export`, request);
  }

  /**
   * API syncReport
   *
   * @param request ILapnetSearch
   * @returns Observable<any>
   */
  syncReport(request: ILapnetSearch): Observable<ILapnetReport> {
    return super.postFile(`${this.resourceUrl}/sync`, request);
  }
}
