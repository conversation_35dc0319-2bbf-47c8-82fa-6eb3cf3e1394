import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  FILE_EXTENSION,
  MAX_DATE_CONST,
  NUMBER_TYPE_STATUS,
  NUMBER_TYPE_STATUS_MAP,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { PREMIUM_ACCOUNT_NUMBER_OBJECT } from '@shared/constants/customer.constants';
import { SELECTED_CONST } from '@shared/constants/user.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ISearchWithPagination } from '@shared/models/base/base-request.model';
import { INumberGroup } from '@shared/models/number-group.model';
import { IQueryPremiumAccNumber } from '@shared/models/premium-acc-number.modal';
import { ISpecialPremiumAccountNumber } from '@shared/models/premium-account-number.modal';
import { DownloadService } from '@shared/services/helpers/download.service';
import { PremiumAccNumberService } from '@shared/services/premium-acc-number.service';
import { NumberStructureService } from '@shared/services/premium-account-number.serice';
import { SpecialAccountService } from '@shared/services/special-account.service';
import { ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-premium-account-number-search',
  templateUrl: './premium-account-number-search.component.html',
  styleUrls: ['./premium-account-number-search.component.scss'],
})
export class PremiumAccountNumberSearchComponent implements OnInit {
  VALIDATORS = VALIDATORS;
  SYSTEM_RULES = SYSTEM_RULES;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  pageSizeOptions = PAGINATION.OPTIONS;
  ENTITY_STATUS = ENTITY_STATUS;
  SELECTED_CONST = SELECTED_CONST;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  NUMBER_TYPE_STATUS = NUMBER_TYPE_STATUS;
  NUMBER_TYPE_STATUS_MAP = NUMBER_TYPE_STATUS_MAP;
  MAX_DATE_CONST = MAX_DATE_CONST;
  itemSelect: string[] = [];
  specialPremiumAccNumbers: ISpecialPremiumAccountNumber[] = [];
  numberGroups: INumberGroup[] = [];
  specialPremiumAccNumbersHidden: ISpecialPremiumAccountNumber[] = [];

  formSearch: FormGroup = new FormGroup({});

  data: IQueryPremiumAccNumber[] = [];
  isErrorStartDateGreaterEndDate = false;
  maxDate = new Date();
  maxColumns = 3;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private premiumAccNumberService: PremiumAccNumberService,
    private specialPremiumAccountNumber: SpecialAccountService,
    private downloadService: DownloadService,
    private translateService: TranslateService,
    private premiumAccountStructureService: NumberStructureService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.getSpecialAccountNumber();
    this.getNumberGroupAccountNumber();
  }

  initForm() {
    this.formSearch = this.formBuilder.group({
      numberRecord: PAGINATION.LARGE_SIZE_DEFAULT,
      expectedString: '',
      accountLengths: [],
      hideAccountNumbers: [],
      hidedAccount: null,
      accountNumber: '',
      numberTypes: [],
      length: 0,
      pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
      pageSize: PAGINATION.LARGE_SIZE_DEFAULT,
      hasPageable: true,
    });
  }

  enterAddAvoidNumber(): void {
    if (this.formSearch.controls.accountNumber.value) {
      this.itemSelect.push(this.formSearch.controls.accountNumber.value);
      this.formSearch.controls.hideAccountNumbers.setValue([
        ...this.itemSelect,
      ]);
      this.formSearch.controls.accountNumber.reset();
    }
  }

  selectAddAvoidNumber(event: ISpecialPremiumAccountNumber): void {
    if (event) {
      this.itemSelect.push(event.specialPremiumAccountNumber || '');
      this.formSearch.controls.hideAccountNumbers.setValue([
        ...this.itemSelect,
      ]);
      this.formSearch.controls.hidedAccount.reset();
      this.specialPremiumAccNumbers = this.specialPremiumAccNumbers.filter(
        (item) =>
          item.specialPremiumAccountNumber !== event.specialPremiumAccountNumber
      );
    }
  }

  onAvoidNumber(event: string[]): void {
    this.itemSelect = event;
    this.specialPremiumAccNumbers = this.specialPremiumAccNumbersHidden.filter(
      (item) =>
        item.specialPremiumAccountNumber &&
        !event.includes(item.specialPremiumAccountNumber)
    );
  }

  onReset() {
    this.formSearch.controls.expectedString.reset();
    this.formSearch.controls.accountLengths.reset();
    this.formSearch.controls.hidedAccount.reset();
    this.formSearch.controls.hideAccountNumbers.reset();
    this.formSearch.controls.accountNumber.reset();
    this.formSearch.controls.numberTypes.reset();
    this.itemSelect = [];
    this.formSearch.controls.hided.setValue(true);
  }

  onSearch(): void {
    if (this.formSearch.valid) {
      const params = this.formSearch.value;
      if (params.numberTypes === 0) {
        params.numberTypes = [0, 1, 2];
      } else if (params.numberTypes && params.numberTypes !== 0) {
        params.numberTypes = [params.numberTypes];
      }
      params.hideAccountNumber = this.itemSelect;
      this.premiumAccNumberService
        .searchPremiumAccNumber(params)
        .subscribe((res: any) => {
          this.data = res.body;
          this.formSearch.controls.length.setValue(res.body.totalElements);
        });
    }
  }

  getSpecialAccountNumber(): void {
    const params = {
      hasPageable: false,
      hided: true,
    };
    this.specialPremiumAccountNumber
      .search(params as ISearchWithPagination)
      .subscribe((response: any) => {
        this.specialPremiumAccNumbers = response.body.content;
        this.specialPremiumAccNumbers = this.specialPremiumAccNumbers.filter(
          (item) => item.hided
        );
        this.specialPremiumAccNumbersHidden =
          this.specialPremiumAccNumbers.filter((item) => item.hided);
      });
  }

  getNumberGroupAccountNumber(): void {
    this.premiumAccountStructureService
      .getLength()
      .subscribe((response: any) => {
        this.numberGroups = response.body;
      });
  }

  onSearchSubmit() {
    this.formSearch.controls.pageIndex.setValue(PAGINATION.PAGE_NUMBER_DEFAULT);
    this.formSearch.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    this.onSearch();
  }

  changeValidDate() {}

  onExport(): void {
    const bodySearch = this.formSearch.getRawValue();
    if (this.formSearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formSearch);
    } else {
      if (bodySearch.numberTypes === 0) {
        bodySearch.numberTypes = [0, 1, 2];
      } else if (bodySearch.numberTypes && bodySearch.numberTypes !== 0) {
        bodySearch.numberTypes = [bodySearch.numberTypes];
      }

      // get file name
      const fileName = this.translateService.instant(
        'template.premiumAccountNumberReport'
      );
      const obFile = this.premiumAccNumberService.exportPremiumNumber({
        numberRecord: bodySearch.numberRecord,
        expectedString: bodySearch.expectedString,
        accountLengths: bodySearch.accountLengths,
        hideAccountNumber: this.itemSelect,
        numberTypes: bodySearch.numberTypes,
        timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
      });

      this.downloadService.downloadFileWithObservableAndName(
        obFile,
        fileName,
        FILE_EXTENSION.XLSX
      );
    }
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formSearch.value.pageIndex,
      this.formSearch.value.pageSize
    );
  }

  onNotification(data?: ISpecialPremiumAccountNumber) {}

  onChangePage(page: PageEvent) {
    this.formSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  customSpecialAccNumberSearchFn(
    term: string,
    item: ISpecialPremiumAccountNumber
  ) {
    if (item.specialPremiumAccountNumber) {
      term = term.replace(/ + /g, ' ');
      term = term.trim();
      term = term.toLocaleLowerCase();
      return (
        item.specialPremiumAccountNumber.toLocaleLowerCase().indexOf(term) > -1
      );
    }
    return '';
  }

  onSelectAll(value?: string) {
    const selectedCode = this.numberGroups.map((item) => item.code);
    switch (value) {
      case SELECTED_CONST.NUMBER_GROUP:
        this.formSearch.get('accountLengths')?.patchValue(selectedCode);
        break;
    }
  }

  onClearAll(value?: string) {
    switch (value) {
      case SELECTED_CONST.NUMBER_GROUP:
        this.formSearch.get('accountLengths')?.patchValue([]);
        break;
    }
  }

  onNegativeCustomer(data: IQueryPremiumAccNumber) {
    this.router
      .navigate([ROUTER_UTILS.customer.router.register], {
        queryParams: {
          [PREMIUM_ACCOUNT_NUMBER_OBJECT.ACCOUNT_NUMBER]: data.premiumAccNumber,
          [PREMIUM_ACCOUNT_NUMBER_OBJECT.PRICE]: data.totalPrice,
        },
      })
      .then();
  }

  onCreatePremiumAccNumber(data: IQueryPremiumAccNumber) {
    this.router
      .navigate(
        [
          ROUTER_UTILS.premiumAccountNumber.root,
          ROUTER_UTILS.premiumAccountNumber.create,
        ],
        {
          queryParams: {
            [PREMIUM_ACCOUNT_NUMBER_OBJECT.ACCOUNT_NUMBER]:
              data.premiumAccNumber,
            [PREMIUM_ACCOUNT_NUMBER_OBJECT.PRICE]: data.originalPrice,
            [PREMIUM_ACCOUNT_NUMBER_OBJECT.DISCOUNT]: data.discount,
            [PREMIUM_ACCOUNT_NUMBER_OBJECT.TOTAL_PRICE]: data.totalPrice,
          },
        }
      )
      .then();
  }
}
