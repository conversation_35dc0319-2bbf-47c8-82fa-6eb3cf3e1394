import {
  Directive,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  Output,
} from '@angular/core';

/**
 * Add default option to select
 */
@Directive({
  selector: 'input[accentsSpecial]',
})
export class AppCleanSpecialDirective {
  @Input() accents = '';

  @Output() clickOutside = new EventEmitter<void>();

  constructor(private elementRef: ElementRef) {}

  @HostListener('keyup', ['$event.type', '$event.target.value'])
  onKeydownHandler(event: string, value: string) {
    this.cleanAccents(value);
  }
  // /^[=+-@]]+$/
  cleanAccents(str: string): string {
    if (/(^[-=+@])/g.test(str[0])) {
      str = '';
      this.elementRef.nativeElement.value = str;
      return str;
    }
    this.elementRef.nativeElement.value = str;
    return str;
  }
}
