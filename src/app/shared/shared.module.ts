import {
  NgxMatDatetimePickerModule,
  NgxMatNativeDateModule,
  NgxMatTimepickerModule,
} from '@angular-material-components/datetime-picker';
import { CommonModule } from '@angular/common';
import { Injector, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MAT_MOMENT_DATE_FORMATS } from '@angular/material-moment-adapter';
import { MAT_DATE_LOCALE, MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSelectModule } from '@angular/material/select';
import { RouterModule } from '@angular/router';
import { ServiceLocator } from '@core/components/service-locator/service-locator.service';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { EditorComponent } from '@shared/components/editor/editor.component';
import { StoresModule } from '@stores/stores.module';
import { QRCodeModule } from 'angularx-qrcode';
import { NzImageModule } from 'ng-zorro-antd/image';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzTimePickerModule } from 'ng-zorro-antd/time-picker';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { NgxSpinnerModule } from 'ngx-spinner';
import { ChangeLanguageComponent } from './components/change-language/change-language.component';
import { ConfirmModalComponent } from './components/confirm-modal/confirm-modal.component';
import { ModalAddItemComponent } from './components/modal-add-item/modal-add-item.component';
import { PageLoaderImageComponent } from './components/page-loader-images/page-loader-image.component';
import { ToggleButtonComponent } from './components/toggle-button/toggle-button.component';
import { UploadFileComponent } from './components/upload-file/upload-file.component';
import { UploadImagesComponent } from './components/upload-images/upload-images.component';
import { UploadMultiImagesComponent } from './components/upload-multi-images/upload-multi-images.component';
import { UploadSingleImagesComponent } from './components/upload-single-images/upload-single-images.component';
import { APP_FORMAT_DATE } from './constants/app.constants';
import { AutoValidateModule } from './directives/auto-validate/auto-validate.module';
import { DirectiveModule } from './directives/directive.module';
import { PipeModule } from './pipes/pipe.module';
import { ModalService } from './services/helpers/modal.service';
@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    TranslateModule,
    DirectiveModule,
    PipeModule,
    NgxSpinnerModule,
    RouterModule,
    NgbModule,
    MatPaginatorModule,
    NzImageModule,
    NzUploadModule,
    StoresModule,
    MatSelectModule,
    NgSelectModule,
    QRCodeModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatInputModule,
    NgxMatTimepickerModule,
    NgxMatDatetimePickerModule,
    NgxMatNativeDateModule,
    AutoValidateModule,
    NzTabsModule,
    NzTableModule,
    NzSwitchModule,
    NzPopoverModule,
    NzTimePickerModule,
  ],
  exports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    TranslateModule,
    DirectiveModule,
    PipeModule,
    NgxSpinnerModule,
    RouterModule,
    NgbModule,
    MatPaginatorModule,
    UploadImagesComponent,
    UploadSingleImagesComponent,
    UploadMultiImagesComponent,
    PageLoaderImageComponent,
    StoresModule,
    MatSelectModule,
    NgSelectModule,
    EditorComponent,
    ToggleButtonComponent,
    QRCodeModule,
    ChangeLanguageComponent,
    MatInputModule,
    NgxMatTimepickerModule,
    NgxMatDatetimePickerModule,
    AutoValidateModule,
    NzTabsModule,
    NzTableModule,
    NzSwitchModule,
    NzPopoverModule,
    NzTimePickerModule,
  ],
  providers: [
    ModalService,
    MatDatepickerModule,
    { provide: MAT_DATE_LOCALE, useValue: 'vi-VN' },
    { provide: MAT_MOMENT_DATE_FORMATS, useValue: APP_FORMAT_DATE },
    // { provide: MAT_DATE_FORMATS, useValue: CUSTOM_DATE_FORMATS },
    // { provide: NGX_MAT_DATE_FORMATS, useValue: DateFormats },
    // { provide: DateAdapter, useClass: AppDateAdapter },
  ],
  declarations: [
    UploadImagesComponent,
    UploadSingleImagesComponent,
    ConfirmModalComponent,
    ToggleButtonComponent,
    EditorComponent,
    ModalAddItemComponent,
    UploadFileComponent,
    ChangeLanguageComponent,
    UploadMultiImagesComponent,
    PageLoaderImageComponent,
  ],
})
export class SharedModule {
  constructor(private injector: Injector) {
    ServiceLocator.injector = this.injector;
  }
}
