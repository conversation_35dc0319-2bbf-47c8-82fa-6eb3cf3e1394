import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  FILE_EXTENSION,
  MASTER_MERCHANT_SERVICE,
  MASTER_MERCHANT_SERVICE_MAP,
  MODAL_ACTION,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { IMerchant } from '@shared/models/merchant.model';
import { IMerchantSearch } from '@shared/models/request/merchant.search';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { MerchantService } from '@shared/services/merchant.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
@Component({
  selector: 'app-master-merchant-management',
  templateUrl: './master-merchant-management.component.html',
  styleUrls: ['./master-merchant-management.component.scss'],
})
export class MasterMerchantManagementComponent implements OnInit {
  merchants: IMerchant[] = [];
  recordSelected: any = [];
  action: any = '';
  storage: any;
  maxDate = new Date();
  maxToDate = new Date();

  ENTITY_STATUS = ENTITY_STATUS;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  ROUTER_UTILS = ROUTER_UTILS;
  pageSizeOptions = PAGINATION.OPTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  MASTER_MERCHANT_SERVICE = MASTER_MERCHANT_SERVICE;
  MASTER_MERCHANT_SERVICE_MAP = MASTER_MERCHANT_SERVICE_MAP;

  // default form search
  formMasterMerchantSearch = this.fb.group({
    // merchantName: '',
    // shortName: '',
    // MerchantCode: '',
    keyword: '',
    serviceType: null,
    status: null,
    length: 0,
    fromDate: [null],
    toDate: [null],
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    timeZoneStr: '',
    // previousPageIndex: 0,
  });

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private modalService: ModalService,
    private toastService: ToastrCustomService,
    private merchantService: MerchantService,
    private translateService: TranslateService,
    private downloadService: DownloadService
  ) {
    // get session storage
    this.storage = sessionStorage.getItem(STORAGE_APP.MASTER_MERCHANT);
  }

  /**
   * Init data
   */
  ngOnInit(): void {
    // check storage
    // (when click back button or back website)
    this.formMasterMerchantSearch
      .get('timeZoneStr')
      ?.setValue(Intl.DateTimeFormat().resolvedOptions().timeZone);
    if (this.storage) {
      // get session storage and parse json
      const filter = JSON.parse(this.storage) as IMerchantSearch;
      // set value form control
      if (filter.status) {
        const status = +filter.status;
        this.formMasterMerchantSearch.controls.status.setValue(status);
      }
      this.formMasterMerchantSearch.controls.keyword.setValue(filter.keyword);
      // this.formMasterMerchantSearch.controls.merchantName.setValue(
      //   filter.merchantName
      // );
      // this.formMasterMerchantSearch.controls.shortName.setValue(filter.shortName);
      // this.formMasterMerchantSearch.controls.MerchantCode.setValue(
      //   filter.MerchantCode
      // );
      this.onSearch();
    } else {
      // set default value start date and end date
      this.onSearch();
    }
    // click form clear storage
    sessionStorage.removeItem(STORAGE_APP.MASTER_MERCHANT);
  }

  // /**
  //  * load list data Loan Online
  //  *
  //  * @param page PageEvent
  //  */
  // loadData(page: PageEvent): void {
  //   this.merchantSearch.pageIndex = page.pageIndex;
  //   this.merchantSearch.pageSize = page.pageSize;
  //   this.merchantService
  //     .searchMaster(this.merchantSearch)
  //     .subscribe((res: any): void => {
  //       this.merchants = res.body.content;
  //       this.totalRecord = res.body.totalElements;
  //     });
  // }

  /**
   * Change page: next page and preview page
   *
   * @param page PageEvent
   */
  onChangePage(page: PageEvent) {
    this.formMasterMerchantSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formMasterMerchantSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  onSearchSubmit() {
    this.formMasterMerchantSearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.formMasterMerchantSearch.controls.length.setValue(
      PAGINATION.LENGTH_DEFAULT
    );
    this.onSearch();
  }

  /**
   * search form
   */
  onSearch(): void {
    // this.merchantSearch.merchantName = body.merchantName;
    // this.merchantSearch.shortName = body.shortName;
    // this.merchantSearch.MerchantCode = body.MerchantCode;
    // this.merchantSearch.keyword = body.keyword;
    // this.merchantSearch.status = body.status;

    this.merchantService
      .searchMaster(this.formMasterMerchantSearch.value)
      .subscribe((res: any): void => {
        this.merchants = res.body.content;
        this.formMasterMerchantSearch.controls.length.setValue(
          res.body.totalElements
        );
      });
  }

  /**
   * index on list loan online
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formMasterMerchantSearch.value.pageIndex,
      this.formMasterMerchantSearch.value.pageSize
    );
  }

  /**
   * button click detail
   *
   * @param merchantId number
   */
  detail(merchantId?: number): void {
    sessionStorage.setItem(
      STORAGE_APP.MASTER_MERCHANT,
      JSON.stringify(this.formMasterMerchantSearch.value)
    );
    this.router.navigate([
      ROUTER_UTILS.merchant.masterMerchant.root,
      merchantId,
      ROUTER_ACTIONS.detail,
    ]);
  }

  /**
   * button click edit
   *
   * @param merchantId number
   */
  edit(merchantId?: number): void {
    sessionStorage.setItem(
      STORAGE_APP.MASTER_MERCHANT,
      JSON.stringify(this.formMasterMerchantSearch.value)
    );
    this.router.navigate([
      ROUTER_UTILS.merchant.masterMerchant.root,
      merchantId,
      ROUTER_ACTIONS.update,
    ]);
  }

  /**
   * button click create
   *
   * @param merchantId number
   */
  create(): void {
    sessionStorage.setItem(
      STORAGE_APP.MASTER_MERCHANT,
      JSON.stringify(this.formMasterMerchantSearch.value)
    );
    this.router.navigate([
      ROUTER_UTILS.merchant.masterMerchant.root,
      ROUTER_ACTIONS.create,
    ]);
  }

  /**
   * reset form
   */
  onReset(): void {
    // set default value date and reset data
    this.formMasterMerchantSearch.controls.keyword.reset();
    this.formMasterMerchantSearch.controls.serviceType.reset();
    this.formMasterMerchantSearch.controls.status.reset();
    this.formMasterMerchantSearch.controls.fromDate.reset();
    this.formMasterMerchantSearch.controls.toDate.reset();
    this.maxDate = new Date();
    this.maxToDate = new Date();
  }

  /**
   * check lock and unlock and call api
   *
   * @param merchant IMerchant
   */
  lockAndUnlock(merchant: IMerchant): void {
    if (merchant.status === ENTITY_STATUS_CONST.INACTIVE.code) {
      this.unLockMerchant(merchant);
    } else {
      this.lockMerchant(merchant);
    }
  }

  /**
   * Lock merchant register
   *
   * @param merchant IMerchant
   */
  private lockMerchant(merchant: IMerchant) {
    const modalData = {
      title: 'merchant.lock',
      content: 'merchant.masterMerchant.lockMerchantContent',
      interpolateParams: {
        merchantName: `<b>${merchant?.merchantName || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { merchantId: merchant?.merchantId };
        this.merchantService.lock(params).subscribe((res) => {
          this.toastService.success('common.action.lockSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * UnLock merchant register
   *
   * @param merchant: IMerchant
   */
  private unLockMerchant(merchant: IMerchant) {
    const modalData = {
      title: 'merchant.unlock',
      content: 'merchant.masterMerchant.unlockMerchantContent',
      interpolateParams: {
        merchantName: `<b>${merchant?.merchantName || ''}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { merchantId: merchant?.merchantId };
        this.merchantService.unlock(params).subscribe((res) => {
          this.toastService.success('common.action.unlockSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * delete
   *
   * @param merchant IMerchant
   */
  delete(merchant: IMerchant): void {
    // open modal
    const modalData = {
      title: 'merchant.delete',
      content: 'merchant.masterMerchant.deleteMerchantContent',
      interpolateParams: {
        merchantName: `<b>${merchant?.merchantName || ''}</b>`,
      },
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { merchantId: merchant?.merchantId };
        this.merchantService.deleteMerchant(params).subscribe((res: any) => {
          this.toastService.success('common.action.deleteSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * export file
   */
  exportFile(): void {
    // get value form control
    const bodySearch = this.formMasterMerchantSearch.getRawValue();
    if (this.formMasterMerchantSearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formMasterMerchantSearch);
    } else {
      // get file name
      const fileName = this.translateService.instant('template.masterMerchant');
      const obFile = this.merchantService.exportMasterMerchant({
        keyword: bodySearch.keyword,
        status: bodySearch.status,
        serviceType: bodySearch.serviceType,
        fromDate: bodySearch.fromDate,
        toDate: bodySearch.toDate,
        timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
      });

      this.downloadService.downloadFileWithObservableAndName(
        obFile,
        fileName,
        FILE_EXTENSION.XLSX
      );
    }
  }

  changeValidDate() {
    if (this.formMasterMerchantSearch.controls['toDate'].value) {
      this.maxDate = this.formMasterMerchantSearch.controls['toDate'].value;
    }
    if (
      this.formMasterMerchantSearch.controls.fromDate.value &&
      this.formMasterMerchantSearch.controls.toDate.value
    ) {
      this.formMasterMerchantSearch.controls.fromDate.clearValidators();
      this.formMasterMerchantSearch.controls.fromDate.updateValueAndValidity();
    }
  }
}
