import { Clipboard } from '@angular/cdk/clipboard';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
} from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  FILE_EXTENSION,
  MODAL_ACTION,
  MOMENT_CONST,
  PAGINATION,
  PUBLISHED_STATUS,
  PUBLISHED_STATUS_CONST,
  PUBLISHED_STATUS_MAP,
  TYPE_OPERATE_SYSTEM,
  UPDATE_TYPE,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { IVersionSearch } from '@shared/models/request/version.search';
import { IVersion } from '@shared/models/version.model';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { VersionService } from '@shared/services/version.service';
import { ROUTER_ACTIONS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';
import { Subject, from } from 'rxjs';
import { first, takeUntil } from 'rxjs/operators';
import { ModalCreateDetailVersionComponent } from '../modal-create-detail/modal-create-detail.component';

@Component({
  selector: 'app-version-android-management',
  templateUrl: './version-android-management.component.html',
  styleUrls: ['./version-android-management.component.scss'],
})
export class VersionAndroidManagementComponent implements OnInit, OnDestroy {
  formSearch: FormGroup = new FormGroup({});
  sourceZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  versionItems: IVersion[] = [];
  maxDate = new Date();
  isReleased = false;
  destroy$ = new Subject();

  TYPE_OPERATE_SYSTEM = TYPE_OPERATE_SYSTEM;
  pageSizeOptions = PAGINATION.OPTIONS;
  PAGINATION = PAGINATION;
  PUBLISHED_STATUS = PUBLISHED_STATUS;
  PUBLISHED_STATUS_MAP = PUBLISHED_STATUS_MAP;
  PUBLISHED_STATUS_CONST = PUBLISHED_STATUS_CONST;
  SYSTEM_RULES = SYSTEM_RULES;
  UIPDATE_TYPE = UPDATE_TYPE;
  FILE_EXTENSION = FILE_EXTENSION;
  MOMENT_CONST = MOMENT_CONST;

  constructor(
    private fb: FormBuilder,
    public modalServiceOpen: NgbModal,
    public modalService: ModalService,
    private clipboard: Clipboard,
    private translateService: TranslateService,
    private versionService: VersionService,
    private downloadService: DownloadService
  ) {
    this.initForm();
  }

  ngOnInit(): void {
    this.onSearch();
  }

  ngOnDestroy(): void {
    console.log('destroy');
    this.destroy$.next();
    this.destroy$.complete();
  }

  initForm() {
    this.formSearch = this.fb.group({
      operatingSystem: TYPE_OPERATE_SYSTEM.ANDROID.value,
      keyword: '',
      status: null,
      fromDate: [null, [this.fromDateInvalid]],
      toDate: [null, [this.toDateInvalid]],
      length: PAGINATION.LENGTH_DEFAULT,
      pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
      pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
      sourceZone: this.sourceZone,
    });
  }

  fromDateInvalid = (control: AbstractControl): ValidationErrors | null => {
    if (control?.value) {
      if (new Date(control.value) > new Date()) {
        return { invalidFromDateCurrent: true };
      }
    }
    return null;
  };

  toDateInvalid = (control: AbstractControl): ValidationErrors | null => {
    const fromDate = this.formSearch.get('fromDate')?.value;
    if (fromDate && control.value) {
      if (new Date(fromDate) > new Date(control.value)) {
        return { invalidToDate: true };
      }
      if (new Date(control.value) > new Date()) {
        return { invalidToDateCurrent: true };
      }
    }
    if (!fromDate && control.value) {
      if (new Date(control.value) > new Date()) {
        return { invalidToDateCurrent: true };
      }
    }
    return null;
  };

  toDateSearch(formSearch: IVersionSearch): string | null {
    const toDate = formSearch.toDate;
    if (toDate) {
      const dateFormat = moment(toDate).format(MOMENT_CONST.FORMAT_DEFAULT);
      return dateFormat;
    } else {
      return null;
    }
  }

  fromDateSearch(formSearch: IVersionSearch): string | null {
    const fromDate = formSearch.fromDate;
    if (fromDate) {
      const dateFormat = moment(fromDate).format(MOMENT_CONST.FORMAT_DEFAULT);
      return dateFormat;
    } else {
      return null;
    }
  }

  copyText(textToCopy: any): void {
    this.clipboard.copy(textToCopy);
  }

  onSearch(): void {
    const formSearch = this.formSearch.value;
    const toDate = this.toDateSearch(formSearch);
    const fromDate = this.fromDateSearch(formSearch);
    formSearch.toDate = toDate;
    formSearch.fromDate = fromDate;
    if (this.formSearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formSearch);
      return;
    }
    this.versionService.search(formSearch).subscribe((res: any) => {
      if (
        this.formSearch.get('pageIndex')?.value ===
        PAGINATION.PAGE_NUMBER_DEFAULT
      ) {
        from(res.body.content)
          .pipe(takeUntil(this.destroy$))
          .pipe(first())
          .subscribe((firstE: any) => {
            if (firstE.status === PUBLISHED_STATUS_CONST.UNRELEASED.value) {
              this.isReleased = true;
            } else {
              this.isReleased = false;
            }
          });
      }
      this.versionItems = res.body.content;
      this.formSearch.controls.length.setValue(res.body.totalElements);
    });
  }

  onSubmitSearch(): void {
    this.formSearch.controls.pageIndex.setValue(PAGINATION.PAGE_NUMBER_DEFAULT);
    this.formSearch.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    this.onSearch();
  }

  reset(): void {
    this.formSearch.controls.keyword.reset();
    this.formSearch.controls.status.reset();
    this.formSearch.controls.fromDate.reset();
    this.formSearch.controls.toDate.reset();
    this.formSearch.controls.operatingSystem.setValue(
      TYPE_OPERATE_SYSTEM.ANDROID.value
    );
  }

  exportVersion(): void {
    const formSearch = this.formSearch.value;
    const toDate = this.toDateSearch(formSearch);
    const fromDate = this.fromDateSearch(formSearch);
    formSearch.toDate = toDate;
    formSearch.fromDate = fromDate;
    const fileName = this.translateService.instant('template.android');
    const obFile = this.versionService.exportVersion(formSearch);
    this.downloadService.downloadFileWithObservableAndName(
      obFile,
      fileName,
      FILE_EXTENSION.XLSX
    );
    return;
  }

  createVersion(typeOperate: string): void {
    const modalRef = this.modalServiceOpen.open(
      ModalCreateDetailVersionComponent,
      {
        backdrop: 'static',
        centered: true,
        size: 'lg',
        keyboard: false,
      }
    );
    modalRef.componentInstance.action = ROUTER_ACTIONS.create;
    modalRef.componentInstance.operate = typeOperate;
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;
    modalRef.componentInstance.versionItem = this.versionItems;
    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.formSearch.controls.pageIndex.setValue(
          PAGINATION.PAGE_NUMBER_DEFAULT
        );
        this.onSearch();
      }
    });
  }

  edit(versionItem: IVersion, typeOperate: string): void {
    const modalRef = this.modalServiceOpen.open(
      ModalCreateDetailVersionComponent,
      {
        backdrop: 'static',
        centered: true,
        size: 'lg',
        keyboard: false,
      }
    );
    modalRef.componentInstance.action = ROUTER_ACTIONS.update;
    modalRef.componentInstance.operate = typeOperate;
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;
    modalRef.componentInstance.versionItem = versionItem;

    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }

  detail(versionItem: IVersion, typeOperate: string) {
    const modalRef = this.modalServiceOpen.open(
      ModalCreateDetailVersionComponent,
      {
        backdrop: 'static',
        centered: true,
        size: 'lg',
        keyboard: false,
      }
    );
    modalRef.componentInstance.action = ROUTER_ACTIONS.detail;
    modalRef.componentInstance.operate = typeOperate;
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;
    modalRef.componentInstance.versionItem = versionItem;
  }

  onChangePage(page: PageEvent) {
    this.formSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formSearch.value.pageIndex,
      this.formSearch.value.pageSize
    );
  }
}
