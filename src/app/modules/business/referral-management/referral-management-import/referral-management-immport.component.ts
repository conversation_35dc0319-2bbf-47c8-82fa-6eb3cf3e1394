import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { FILE_EXTENSION } from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ReferralService } from '@shared/services/referral.service';
import { ROUTER_ACTIONS } from '@shared/utils';
@Component({
  selector: 'app-referral-management-import',
  templateUrl: './referral-management-import.component.html',
  styleUrls: ['./referral-management-import.component.scss'],
})
export class ReferralManagementImportComponent implements OnInit {
  formImport: FormGroup = new FormGroup({});
  selectedFileName = '';
  fileImport: any;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  SYSTEM_RULES = SYSTEM_RULES;

  ngOnInit(): void {
    this.initForm();
  }

  constructor(
    private fb: FormBuilder,
    public translateService: TranslateService,
    public activeModal: NgbActiveModal,
    private referralService: ReferralService,
    private toastService: ToastrCustomService,
    private downloadService: DownloadService
  ) {}

  initForm() {
    this.formImport = this.fb.group({
      uploadFile: ['', Validators.required],
    });
  }

  downloadTemplateImport(): void {
    const fileName = this.translateService.instant('template.referralTemplate');
    const obFile = this.referralService.downloadTemplate();
    this.downloadService.downloadFileWithObservableAndName(
      obFile,
      fileName,
      FILE_EXTENSION.XLSX
    );
  }

  onFileSelected(event: any) {
    this.selectedFileName = event?.target?.files[0].name;
    this.fileImport = event?.target?.files;
  }

  importFile(): void {
    const [fileImport] = this.fileImport;
    if (!fileImport) {
      return;
    }
    this.referralService.importDataTemplate(fileImport).subscribe(
      () => {
        this.toastService.success('common.action.uploadSuccess');
        this.activeModal.close();
      }
    );
  }
}
