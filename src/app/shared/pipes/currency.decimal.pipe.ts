import { Pipe, PipeTransform } from '@angular/core';

/**
 * Display VND
 *
 * <AUTHOR>
 * @date 2021-12-24
 * @export
 * @class CurrencyLakPipe
 * @implements {PipeTransform}
 * @howToUse
 * ```
 *     <some-element>{{ product?.price | currencyLak }}</some-element>
 * ```
 */
@Pipe({ name: 'currencyDecimalLak' })
export class CurrencyDecimalLakPipe implements PipeTransform {
  symbol = '';

  transform(value?: any): string {
    if (value) {
      const strValue = value.toString();
      const [integerPart, decimalPart] = strValue.split('.');

      const formattedInteger = integerPart.replace(
        /\B(?=(\d{3})+(?!\d))/g,
        ','
      );

      return decimalPart !== undefined
        ? `${formattedInteger}.${decimalPart}`
        : formattedInteger;
    }
    if (value === 0) {
      return value;
    }
    return '';
  }

  isDecimal(num: number) {
    return +num !== Math.floor(num);
  }
}
