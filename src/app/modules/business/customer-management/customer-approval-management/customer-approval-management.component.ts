import { Component, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  DATE_CONSTANT,
  MODAL_ACTION,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import {
  APPROVAL_STATUS,
  APPROVAL_STATUS_CONST,
  APPROVAL_STATUS_MAP,
  APPROVAL_TYPE,
  APPROVAL_TYPE_CONST,
  APPROVAL_TYPE_MAP,
} from '@shared/constants/customer.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ICustomer } from '@shared/models/customer.model';
import { ICustomerSearch } from '@shared/models/request/customer.search';
import { CustomerService } from '@shared/services/customer.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import { ModalImportCustomerComponent } from './modal-import-customer/modal-import-customer.component';

@Component({
  selector: 'app-customer-approval-management',
  templateUrl: './customer-approval-management.component.html',
  styleUrls: ['./customer-approval-management.component.scss'],
})
export class CustomerApprovalManagementComponent implements OnInit {
  ROUTER_UTILS = ROUTER_UTILS;
  pageSizeOptions = PAGINATION.OPTIONS;
  APPROVAL_STATUS = APPROVAL_STATUS;
  APPROVAL_STATUS_VALUE = APPROVAL_STATUS.filter(
    (status: any) => status.value !== APPROVAL_STATUS_CONST.EXPIRED.value
  );
  APPROVAL_STATUS_MAP = APPROVAL_STATUS_MAP;
  APPROVAL_TYPE = APPROVAL_TYPE;
  APPROVAL_TYPE_MAP = APPROVAL_TYPE_MAP;
  VALIDATORS = VALIDATORS;
  SYSTEM_RULES = SYSTEM_RULES;

  customer: ICustomer[] = [];
  currentDate = new Date();
  data: any[] = [];
  action: any = '';
  recordSelected: any = [];
  storage: any;

  // default value form
  form = this.fb.group({
    cif: ['', [Validators.pattern(VALIDATORS.PATTERN.CIF_SEARCH)]],
    idCardNumber: [
      '',
      [Validators.pattern(VALIDATORS.PATTERN.ID_CARD_NUMBER_SEARCH)],
    ],
    phoneNumber: ['', [Validators.pattern(VALIDATORS.PATTERN.PHONE_SEARCH)]],
    email: '',
    createdDateStartAt: [null],
    createdDateEndAt: [null],
    approvalStatus: null,
    approvalType: null,
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    // previousPageIndex: 0,
  });

  constructor(
    private customerService: CustomerService,
    private fb: FormBuilder,
    private router: Router,
    private toastService: ToastrCustomService,
    private modalService: ModalService,
    private translate: TranslateService,
    private modal: NgbModal
  ) {
    // get storage
    this.storage = sessionStorage.getItem(STORAGE_APP.CUSTOMER_APPROVAL);
  }

  /**
   * init data:
   * check storage (when click back button or back website) and set value in storage
   */
  ngOnInit(): void {
    if (this.storage) {
      // get storage and parse json
      const filter = JSON.parse(this.storage) as ICustomerSearch;
      // set value in form
      if (filter.approvalStatus) {
        const status = +filter.approvalStatus;
        this.form.controls.approvalStatus.setValue(status);
      }
      if (filter.approvalType) {
        const type = +filter.approvalType;
        this.form.controls.approvalType.setValue(type);
      }
      this.form.controls.cif.setValue(filter.cif);
      this.form.controls.idCardNumber.setValue(filter.idCardNumber);
      this.form.controls.phoneNumber.setValue(filter.phoneNumber);
      this.form.controls.email.setValue(filter.email);
      if (filter.createdDateStartAt) {
        this.form.controls.createdDateStartAt.setValue(
          CommonUtils.formatDate(
            CommonUtils.newDate(filter.createdDateStartAt),
            DATE_CONSTANT.YYYYMMDD_HYPHEN_BIG
          )
        );
      }
      if (filter.createdDateEndAt) {
        this.form.controls.createdDateEndAt.setValue(
          CommonUtils.formatDate(
            CommonUtils.newDate(filter.createdDateEndAt),
            DATE_CONSTANT.YYYYMMDD_HYPHEN_BIG
          )
        );
      }
      const search = { ...this.form.value };
      delete search.length;
      delete search.pageSize;
      // delete body.previousPageIndex;
      delete search.pageIndex;
      if (!CommonUtils.objectIsEmpty(search)) {
        this.onSearch();
      }
    }
    // remove storage
    sessionStorage.removeItem(STORAGE_APP.CUSTOMER_APPROVAL);
    // this.loadData(this.pageEvent);
  }

  /**
   * next page and preview page
   *
   * @param page PageEvent
   */
  onChangePage(page: PageEvent) {
    this.form.controls.pageIndex.setValue(page.pageIndex);
    this.form.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  onSearchSubmit() {
    this.form.controls.pageIndex.setValue(PAGINATION.PAGE_NUMBER_DEFAULT);
    this.form.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    this.onSearch();
  }

  /**
   * search form
   */
  onSearch(): void {
    const body = { ...this.form.value };
    delete body.length;
    delete body.pageSize;
    // delete body.previousPageIndex;
    delete body.pageIndex;
    if (CommonUtils.objectIsEmpty(body)) {
      this.toastService.warning('common.searchEmpty');
      return;
    }

    if (this.form.valid) {
      this.customerService
        .searchApproval(this.form.value)
        .subscribe((res: any): void => {
          this.data = res.body.content;
          this.form.controls.length.setValue(res.body.totalElements);
        });
    }
  }

  validateDate() {
    if (this.form.controls['createdDateEndAt'].value) {
      this.currentDate = this.form.controls['createdDateEndAt'].value;
    }
  }
  /**
   * index on list
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.form.value.pageIndex,
      this.form.value.pageSize
    );
  }

  /**
   *
   * checkEnableDeleteBtn
   *
   * @param approvalStatus
   * @returns
   */
  checkEnableDeleteBtn(approvalStatus: number): boolean {
    if (
      Array.of(
        APPROVAL_STATUS_CONST.PENDING.value,
        APPROVAL_STATUS_CONST.APPROVAL_ERROR.value,
        APPROVAL_STATUS_CONST.CANCEL.value
      ).includes(approvalStatus)
    ) {
      return true;
    }

    return false;
  }

  /**
   * go to page detail
   *
   * @param customerId number
   */
  detail(customer: ICustomer): void {
    sessionStorage.setItem(
      STORAGE_APP.CUSTOMER_APPROVAL,
      JSON.stringify(this.form.value)
    );
    if (customer.approvalType === APPROVAL_TYPE_CONST.CREATE.value) {
      this.router.navigate([
        ROUTER_UTILS.customer.root,
        ROUTER_UTILS.customer.approve.root,
        customer.customerId,
        ROUTER_ACTIONS.view,
      ]);
    } else {
      this.router.navigate([
        ROUTER_UTILS.customer.root,
        ROUTER_UTILS.customer.approve.root,
        customer.customerId,
        ROUTER_ACTIONS.update,
      ]);
    }
  }

  /**
   * Delete customer approval
   *
   * @param customer ICustomer
   */
  public deleteCustomerApproval(customer: ICustomer) {
    const modalData = {
      title: 'customerRegisterManagement.deleteApproval',
      content: 'customerRegisterManagement.deleteCustomerApproval',
      interpolateParams: {
        fullname: `<b>${customer?.fullname || ''}</b>`,
        approvalType: `<b>${this.translate.instant(
          APPROVAL_TYPE_MAP[customer.approvalType || 0]
        )}</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { customerId: customer?.customerId };
        this.customerService.approvalDeleteCustomer(params).subscribe((res) => {
          this.toastService.success('common.action.deleteSuccess');
          this.onSearch();
        });
      }
    });
  }

  // /**
  //  * go to page edit
  //  *
  //  * @param customerId number
  //  */
  // edit(customerId: ICustomer): void {
  //   sessionStorage.setItem(
  //     STORAGE_APP.CUSTOMER,
  //     JSON.stringify(this.customerSearch)
  //   );
  //   this.router.navigate([
  //     ROUTER_UTILS.customer.root,
  //     ROUTER_UTILS.customer.approve.root,
  //     customerId,
  //     ROUTER_ACTIONS.update,
  //   ]);
  // }

  /**
   * reset form search
   */
  onReset(): void {
    this.form.controls.cif.reset();
    this.form.controls.idCardNumber.reset();
    this.form.controls.approvalType.reset();
    this.form.controls.approvalStatus.reset();
    this.form.controls.phoneNumber.reset();
    this.form.controls.email.reset();
    this.form.controls.createdDateStartAt.reset();
    this.form.controls.createdDateEndAt.reset();
    this.currentDate = new Date();
    // this.form.controls.approvalStatus.setValue('');
    // this.form.controls.approvalType.setValue('');
  }

  delete(): void {
    console.log('xoa this.recordSelected');
  }

  lock(): void {
    console.log('khoa this.recordSelected');
  }

  importFile() {
    const modalRef = this.modal.open(ModalImportCustomerComponent, {
      backdrop: 'static',
      centered: true,
      size: 'lg',
      keyboard: true,
    });

    modalRef.componentInstance.action = MODAL_ACTION.CONFIRM;

    modalRef.result.then(() => {});
  }
}
