<div class="modal-content">
  <!-- <div class="modal-header" [ngClass]="isHiddenBtnClose ? 'border-0' : ''">
    <h3 class="modal-title">{{title | translate}}</h3>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"
      (click)="activeModal.close(MODAL_ACTION.CLOSE.code)">
      <span aria-hidden="true">&times;</span>
    </button>
  </div> -->
  <div class="modal-header" [ngClass]="isHiddenBtnClose ? 'border-0' : ''">
    <h5 class="modal-title">
      {{ "common.action.changePassword" | translate }}
    </h5>
    <button
      type="button"
      class="close"
      data-dismiss="modal"
      aria-label="Close"
      (click)="activeModal.close()"
    >
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="userChangePasswordForm">
      <div class="input mb-3">
        <div class="form-group position-relative">
          <label
            >{{ "model.user.currentPassword" | translate
            }}<span class="text-danger">*</span></label
          >
          <input
            trim
            #currentPassword
            [type]="currentPasswordVisible ? 'text' : 'password'"
            class="w-100"
            formControlName="currentPassword"
            class="form-control"
            placeholder="{{ 'model.user.currentPassword' | translate }}"
          />
          <span
            class="icon-eye cursor-pointer"
            (click)="onChangCurrentPasswordVisible()"
          >
            <i
              [classList]="
                currentPasswordVisible ? 'bi-eye-slash' : 'bi bi-eye'
              "
            ></i>
          </span>
        </div>
        <small
          class="form-text text-danger noti-small"
          *ngIf="
            userChangePasswordForm.get('currentPassword')?.errors?.required &&
            userChangePasswordForm.get('currentPassword')?.touched
          "
        >
          {{ "error.user.required.currentPassword" | translate }}
        </small>
      </div>
      <div class="input mb-3">
        <div class="form-group position-relative">
          <label
            >{{ "model.user.newPassword" | translate
            }}<span class="text-danger">*</span></label
          >
          <input
            trim
            #password
            [type]="passwordVisible ? 'text' : 'password'"
            class="w-100"
            formControlName="password"
            class="form-control"
            placeholder="{{ 'model.user.newPassword' | translate }}"
          />
          <span
            class="icon-eye cursor-pointer"
            (click)="onChangPasswordVisible()"
          >
            <i [classList]="passwordVisible ? 'bi-eye-slash' : 'bi bi-eye'"></i>
          </span>
        </div>
        <small
          class="form-text text-danger noti-small"
          *ngIf="
            userChangePasswordForm.get('password')?.errors?.required &&
            userChangePasswordForm.get('password')?.touched
          "
        >
          {{ "error.user.required.newPassword" | translate }}
        </small>
        <small
          class="form-text text-danger noti-small"
          *ngIf="
            userChangePasswordForm.get('password')?.errors?.minlength &&
            userChangePasswordForm.get('password')?.touched
          "
        >
          {{
            "error.user.minLength.newPassword"
              | translate
                : {
                    param: VALIDATORS.LENGTH.PASSWORD_MIN_LENGTH
                  }
          }}
        </small>
        <small
          class="form-text text-danger noti-small"
          *ngIf="
            userChangePasswordForm.get('password')?.errors?.maxlength &&
            userChangePasswordForm.get('password')?.touched
          "
        >
          {{
            "error.user.maxLength.newPassword"
              | translate
                : {
                    param: VALIDATORS.LENGTH.PASSWORD_MAX_LENGTH
                  }
          }}
        </small>
        <small
          class="form-text text-danger noti-small"
          *ngIf="
            userChangePasswordForm.get('password')?.errors?.pattern &&
            userChangePasswordForm.get('password')?.touched
          "
        >
          {{ "error.user.pattern.newPassword" | translate }}
        </small>
      </div>
      <div class="input">
        <div class="form-group position-relative">
          <label
            >{{ "model.user.confirmPassword" | translate
            }}<span class="text-danger">*</span></label
          >
          <input
            trim
            #confirmPassword
            [type]="confirmPasswordVisible ? 'text' : 'password'"
            class="w-100"
            formControlName="confirmPassword"
            class="form-control"
            placeholder="{{ 'model.user.confirmPassword' | translate }}"
          />
          <span
            class="icon-eye cursor-pointer"
            (click)="onChangConfirmPasswordVisible()"
          >
            <i
              [classList]="
                confirmPasswordVisible ? 'bi-eye-slash' : 'bi bi-eye'
              "
            ></i>
          </span>
        </div>
        <small
          class="form-text text-danger noti-small"
          *ngIf="
            userChangePasswordForm.get('confirmPassword')?.errors?.required &&
            userChangePasswordForm.get('confirmPassword')?.touched
          "
        >
          {{ "error.user.required.confirmPassword" | translate }}
        </small>
        <small
          class="form-text text-danger noti-small"
          *ngIf="
            userChangePasswordForm.get('confirmPassword')?.errors?.minlength &&
            userChangePasswordForm.get('confirmPassword')?.touched
          "
        >
          {{
            "error.user.minLength.confirmPassword"
              | translate
                : {
                    param: VALIDATORS.LENGTH.PASSWORD_MIN_LENGTH
                  }
          }}
        </small>
        <small
          class="form-text text-danger noti-small"
          *ngIf="
            userChangePasswordForm.get('confirmPassword')?.errors?.maxlength &&
            userChangePasswordForm.get('confirmPassword')?.touched
          "
        >
          {{
            "error.user.maxLength.confirmPassword"
              | translate
                : {
                    param: VALIDATORS.LENGTH.PASSWORD_MAX_LENGTH
                  }
          }}
        </small>
        <small
          class="form-text text-danger noti-small"
          *ngIf="
            userChangePasswordForm.get('confirmPassword')?.errors?.pattern &&
            userChangePasswordForm.get('confirmPassword')?.touched
          "
        >
          {{ "error.user.pattern.confirmPassword" | translate }}
        </small>
        <span class="text-danger" *ngIf="f.confirmPassword.errors?.matching">
          <small>{{
            "public.forgotPassword.error.notMatch" | translate
          }}</small>
        </span>
      </div>
      <div class="text-left mb-5 mt-3">
        <div
          class="footer"
          [innerHTML]="
            displayContent('public.forgotPassword.footerChangePassword')
          "
        ></div>
      </div>
    </form>
  </div>
  <div class="text-center pb-4">
    <button
      type="button"
      class="btn mb-btn-outline-color mr-3"
      (click)="activeModal.close()"
    >
      {{ "common.action.close" | translate }}
    </button>
    <button type="button" class="btn mb-btn-color" (click)="changePassword()">
      {{ "common.action.changePassword" | translate }}
    </button>
  </div>
</div>
