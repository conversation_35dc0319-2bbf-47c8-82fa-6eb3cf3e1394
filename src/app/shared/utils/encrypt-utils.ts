import * as JsEncryptModule from 'jsencrypt';
import { environment } from 'src/environments/environment';

const crypt = new JsEncryptModule.JSEncrypt({});

crypt.setPublicKey(environment.rsaPublicKey);

export const encrypt = (text: string): string | boolean => {
  return crypt.encrypt(text);
};

export const createCsrfToken = (principle: string): string | boolean => {
  const now = Date.now();
  return crypt.encrypt(`${now}-${principle}`);
};
