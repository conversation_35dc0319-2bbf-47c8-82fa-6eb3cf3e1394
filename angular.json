{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false}, "version": 1, "newProjectRoot": "projects", "projects": {"mb-lao": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "deploy/mb-laos", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/firebase-messaging-sw.js", "src/manifest.json", {"glob": "**/*", "input": "./node_modules/@ant-design/icons-angular/src/inline-svg/", "output": "/assets/"}], "styles": ["node_modules/admin-lte/plugins/fontawesome-free/css/all.min.css", "node_modules/admin-lte/plugins/overlayScrollbars/css/OverlayScrollbars.min.css", "node_modules/admin-lte/plugins/select2/css/select2.min.css", "node_modules/admin-lte/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css", "src/styles.scss", "src/assets/dist/css/custom.css", "node_modules/ng-zorro-antd/ng-zorro-antd.min.css"], "scripts": ["node_modules/admin-lte/plugins/jquery/jquery.js", "node_modules/admin-lte/plugins/bootstrap/js/bootstrap.bundle.js", "node_modules/admin-lte/plugins/select2/js/select2.full.min.js", "node_modules/admin-lte/plugins/overlayScrollbars/js/jquery.overlayScrollbars.js", "node_modules/admin-lte/dist/js/adminlte.js", "node_modules/admin-lte/plugins/select2/js/i18n/vi.js", "src/assets/dist/js/libs/pdfmake/pdfmake.min.js", "src/assets/dist/js/libs/pdfmake/vfs_fonts.min.js"]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "live": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.live.ts"}], "outputHashing": "all"}, "qa": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.qa.ts"}]}, "dev": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}]}, "qa-bk": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.qa.ts"}]}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "mb-lao:build:production"}, "qa": {"browserTarget": "mb-lao:build:qa"}, "dev": {"browserTarget": "mb-lao:build:dev"}, "qa-bk": {"browserTarget": "mb-lao:build:qa-bk"}, "development": {"browserTarget": "mb-lao:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "mb-lao:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "defaultProject": "mb-lao"}