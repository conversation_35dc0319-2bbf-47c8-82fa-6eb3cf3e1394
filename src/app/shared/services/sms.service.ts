import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ISms } from '@shared/models/sms.model';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class SmsManageService extends AbstractService {
  public resourceUrl = '/sms';

  constructor(protected http: HttpClient) {
    super(http);
  }

  search(request: any): Observable<EntityResponseType<ISms[]>> {
    return super.post<EntityResponseType<ISms[]>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  export(request: ISms): Observable<EntityResponseType<ISms>> {
    return super.postFile(`${this.resourceUrl}/export`, request);
  }
}
