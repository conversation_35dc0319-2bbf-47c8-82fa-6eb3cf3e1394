<section class="box-content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-6 back-container">
        <img src="assets/dist/img/icon/back.png" (click)="backToList()" />
        <h5>
          {{
          action === ROUTER_ACTIONS.create
          ? ("model.structureAccount.createTitle" | translate)
          : action === ROUTER_ACTIONS.update
          ? ("model.structureAccount.updateTitle" | translate)
          : ("model.structureAccount.detailTitle" | translate)
          }}
        </h5>
      </div>
    </div>
    <form [formGroup]="formCreate" *ngIf="numberStructure?.name || action === ROUTER_ACTIONS.create">
      <div class="col-md-12">
        <div class="row ">
          <h2 class="title-create">
            {{ "model.structureAccount.titleStructure" | translate }}
          </h2>
        </div>
        <div class="row border-create">
          <h3>
            {{ "model.structureAccount.titleStructure" | translate }}
          </h3>
          <div class="col-md-12">
            <div class="row">
              <div class="col-5">
                <div class="form-group">
                  <label>{{ "model.structureAccount.name" | translate
                    }}<span class="text-danger">*</span></label>
                  <input trim formControlName="name" type="text" appAutoValidate placeholder="{{
                      'model.structureAccount.name' | translate
                    }}" class="w-100" class="form-control" [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH" />
                </div>
              </div>
              <div class="col-5">
                <div class="form-group">
                  <label>{{ "model.structureAccount.numberStructure" | translate
                    }}<span class="text-danger">*</span></label>
                  <input trim appAutoValidate formControlName="numberStructure" type="text" placeholder="{{
                      'model.structureAccount.numberStructure' | translate
                    }}" class="w-100" class="form-control" [maxLength]="VALIDATORS.LENGTH.NUMBER_STRUCTURE" />
                </div>
              </div>
              <div class="col-2">
                <div class="form-group">
                  <label>{{ "model.structureAccount.length" | translate
                    }}<span class="text-danger">*</span></label>
                  <ng-select placeholder="{{ 'model.structureAccount.length' | placeholder : 'select' }}"
                    [searchable]="false" formControlName="length" [clearable]="true">
                    <ng-option [value]="item?.code" *ngFor="let item of lengths">
                      {{ item.code + "" || '' | translate }}
                    </ng-option>
                  </ng-select>
                  <small class="form-text text-danger noti-small" *ngIf="
                    formCreate.get('length')?.errors?.required &&
                    formCreate.get('length')?.touched
                  ">
                    {{ "model.structureAccount.error.length" | translate }}
                  </small>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-4">
                <div class="form-group">
                  <label>{{ "model.structureAccount.originalPrice" | translate
                    }}<span class="text-danger">*</span></label>
                  <div id="text-unit">
                    <input type="text" class="form-control w-100" formControlName="price" appCurrencyFormat
                      (ngModelChange)="procesAccountAmount($event)" [maxLength]="VALIDATORS.LENGTH.NUMBER_MAX_LENGTH" />
                    <div id="text-unit-right">
                      <span>{{ "loanOnline.unit.lak" | translate }}</span>
                    </div>
                    <small class="form-text text-danger noti-small" *ngIf="
                            formCreate.get('price')?.errors?.required &&
                            formCreate.get('price')?.touched
                          ">
                      {{
                      "model.structureAccount.error.price"
                      | translate
                      }}
                    </small>
                    <small class="form-text text-danger noti-small" *ngIf="
                    formCreate.controls.price.errors?.min ||
                    formCreate.controls.price.errors?.max">
                      {{
                      "model.structureAccount.error.errorPrice"
                      | translate
                      }}
                    </small>
                  </div>
                </div>
              </div>
              <div class="col-4">
                <div class="form-group">
                  <label>{{ "fee.discount" | translate
                    }}<span class="text-danger">*</span></label>
                  <div id="text-unit">
                    <input type="text" class="form-control w-100" formControlName="discount" appNumberInput trim
                      (ngModelChange)="procesAccountAmount($event)" />
                    <div id="text-unit-right">
                      <span>{{ "common.percent" | translate }}</span>
                    </div>
                    <small class="form-text text-danger noti-small" *ngIf="
                              formCreate.get('discount')?.errors?.required &&
                              formCreate.get('discount')?.touched
                            ">
                      {{
                      "model.specialAccountNumber.error.discount"
                      | translate
                      }}
                    </small>
                  </div>
                </div>
              </div>
              <div class="col-4">
                <div class="form-group">
                  <label>{{ "model.structureAccount.totalPrice" | translate
                    }}</label>
                  <div id="text-unit">
                    <input type="text" class="form-control w-100" formControlName="totalPrice" />
                    <div id="text-unit-right">
                      <span>{{ "loanOnline.unit.lak" | translate }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-12">
                <div class="form-group">
                  <label>{{ "model.structureAccount.formatPattern" | translate
                    }}<span class="text-danger">*</span></label>
                  <textarea formControlName="pattern" appAutoValidate
                    [maxLength]="VALIDATORS.LENGTH.MEDIUM_TEXT_MAX_LENGTH" type="text" class="w-100" trim
                    class="form-control" rows="4" cols="50" placeholder="{{
                      'model.structureAccount.formatPattern' | placeholder
                    }}">
                  </textarea>
                  <small class="form-text text-danger noti-small" *ngIf="
                      formCreate.get('pattern')?.errors?.maxlength &&
                      formCreate.get('pattern')?.touched
                    ">
                    {{
                    "error.structureAccount.error.pattern"
                    | translate
                    : {
                    param: VALIDATORS.LENGTH.MEDIUM_TEXT_MAX_LENGTH
                    }
                    }}
                  </small>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12" *ngIf="action === ROUTER_ACTIONS.detail">
            <div class="row">
              <div class="col-3">
                <div class="form-group">
                  <label>{{ "model.campaign.createdBy" | translate}}</label>
                  <input formControlName="createdBy" type="text" class="w-100" class="form-control" />
                </div>
              </div>
              <div class="col-3">
                <div class="form-group">
                  <label>{{ "common.createdDate" | translate}}</label>
                  <input formControlName="createdDate" type="text" class="w-100" class="form-control" />
                </div>
              </div>
              <div class="col-3">
                <div class="form-group">
                  <label>{{ "model.campaign.lastModifiedBy" | translate}}</label>
                  <input formControlName="lastModifiedBy" type="text" class="w-100" class="form-control" />
                </div>
              </div>
              <div class="col-3">
                <div class="form-group">
                  <label>{{ "common.lastModifiedDate" | translate}}</label>
                  <input formControlName="lastModifiedDate" type="text" class="w-100" class="form-control" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="d-block text-center mb-5 mt-4">
        <button type="button" class="btn btn-white mr-2" data-toggle="modal" (click)="backToList()">
          {{ (action === ROUTER_ACTIONS.detail
          ? "common.action.back"
          : "common.action.cancel"
          ) | translate }}
        </button>
        <ng-container>
          <button *ngIf="!(action === ROUTER_ACTIONS.detail)" class="btn btn-red"
            (click)="action === ROUTER_ACTIONS.update ? onUpdate() : onCreate()">
            {{
            (action === ROUTER_ACTIONS.update
            ? "common.action.update"
            : "common.action.create"
            ) | translate
            }}
          </button>
          <button class="btn btn-red" *ngIf="action === ROUTER_ACTIONS.detail" (click)="onOpenUpdate()">
            {{"common.action.update" | translate}}
          </button>
        </ng-container>
      </div>
    </form>
  </div>
</section>