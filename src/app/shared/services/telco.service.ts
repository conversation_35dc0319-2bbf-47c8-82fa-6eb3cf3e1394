import { Injectable } from '@angular/core';
import { IMerchantSearch } from '@shared/models/request/merchant.search';
import { ITelco } from '@shared/models/telco.model';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';
@Injectable({
  providedIn: 'root',
})
export class TelcoService extends AbstractService {
  public resourceUrl = '/telco';

  /**
   * search
   *
   * @param request ITelco
   * @returns Observable<EntityResponseType<ITelco[]>>
   */
  search(
    request: IMerchantSearch
  ): Observable<EntityResponseType<ITelco[]>> {
    return super.post<EntityResponseType<ITelco[]>>(
      `${this.resourceUrl}/search`,
      request
    );
  }
}
