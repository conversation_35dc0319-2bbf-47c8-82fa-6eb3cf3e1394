import { Component, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { AccountService } from '@shared/services/account.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss'],
})
export class ForgotPasswordComponent implements OnInit {
  userForgotPasswordForm = this.fb.group({
    email: [
      '',
      [
        Validators.required,
        Validators.pattern(VALIDATORS.PATTERN.EMAIL_MB),
        Validators.minLength(VALIDATORS.LENGTH.EMAIL_MIN_LENGTH),
        Validators.maxLength(VALIDATORS.LENGTH.EMAIL_MAX_LENGTH),
      ],
    ],
  });

  VALIDATORS = VALIDATORS;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private translate: TranslateService,
    private accountService: AccountService
  ) {
    // const email = localStorage.getItem('email');
    // if (email) {
    //   this.userForgotPasswordForm.controls.email.setValue(email);
    // }
  }

  ngOnInit(): void {
    // get token from paramater
  }

  /**
   * onReSend: call api sendEmailVerify and go to link verify
   */
  onSend(): void {
    if (this.userForgotPasswordForm.invalid) {
      CommonUtils.markFormGroupTouched(this.userForgotPasswordForm);
    }
    const data = this.userForgotPasswordForm.getRawValue();
    if (this.userForgotPasswordForm.valid) {
      // localStorage.setItem('email', data.email);
      this.accountService.sendEmailVerify(data).subscribe((res: any) => {
        this.router.navigate([
          ROUTER_UTILS.portal.forgotPassword.root,
          res.body.token,
          ROUTER_ACTIONS.verify,
        ]);
        // this.isResendSucces = true;
      });
    }
  }

  /**
   * displayContent: display content in file i18n
   *
   * @param content string
   * @returns string
   */
  displayContent(content: string): string {
    return this.translate.instant(content);
  }
}
