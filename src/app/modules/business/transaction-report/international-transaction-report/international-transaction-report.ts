import { Component, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  Validators,
} from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { TranslateService } from '@ngx-translate/core';
import {
  CALCULATE_MILLISECONDS_IN_A_YEAR,
  CROSS_BORDER_NATION_CODES,
  DATE_RANGE_STATISTICAL_LOAN_ONLINE,
  ENTITY_TRANSACTION_STATUS_CONST,
  FILE_EXTENSION,
  MOMENT_CONST,
  PAGINATION,
  TRANSACTION_ENTITY_STATUS,
  TRANSACTION_ENTITY_STATUS_MAP,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { SELECTED_CONST } from '@shared/constants/user.constants';
import { IMerchantTransactionHistory } from '@shared/models/merchant-transaction-history.model';
import { ITransactionSearch } from '@shared/models/request/transaction.search';
import { DownloadService } from '@shared/services/helpers/download.service';
import { TransactionService } from '@shared/services/transaction.service';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';
@Component({
  selector: 'app-international-transaction-report',
  templateUrl: './international-transaction-report.component.html',
  styleUrls: ['./international-transaction-report.scss'],
})
export class InternationalTransactionReportComponent implements OnInit {
  // transactionReport: ILoanOnline[] = [];
  transactionReport: IMerchantTransactionHistory[] = [];
  action: any = '';
  storage: any;
  maxDate = new Date();
  maxToDate = new Date();

  // input search
  pageSizeOptions = PAGINATION.OPTIONS;
  TRANSACTION_ENTITY_STATUS_MAP = TRANSACTION_ENTITY_STATUS_MAP;
  SYSTEM_RULES = SYSTEM_RULES;
  ENTITY_TRANSACTION_STATUS_CONST = ENTITY_TRANSACTION_STATUS_CONST;
  TRANSACTION_ENTITY_STATUS = TRANSACTION_ENTITY_STATUS;
  SELECTED_CONST = SELECTED_CONST;
  CROSS_BORDER_NATION_CODES = CROSS_BORDER_NATION_CODES;

  isValidMaxDate = (control: FormControl) => {
    const dateOfBirth = new Date(control.value).getTime();
    const maxDateOfBirth = new Date().getTime();
    return dateOfBirth > maxDateOfBirth ? { invalidMaxDate: true } : null;
  };

  // default form search
  formTransactionReportSearch = this.fb.group({
    keyword: '',
    fromDate: [null, [Validators.required]],
    toDate: [null, [Validators.required, this.isValidMaxDate]],
    nationCodes: [],
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    hasPageable: true,
    timeZoneStr: '',
    transactionStatus: [],
  });

  constructor(
    private transactionService: TransactionService,
    private fb: FormBuilder,
    private downloadService: DownloadService,
    private translateService: TranslateService
  ) {}

  /**
   * Init data
   */
  ngOnInit(): void {
    // check storage
    // (when click back button or back website)
    this.formTransactionReportSearch
      .get('timeZoneStr')
      ?.setValue(Intl.DateTimeFormat().resolvedOptions().timeZone);
    if (this.storage) {
      // get session storage and parse json
      const filter = JSON.parse(this.storage) as ITransactionSearch;
      // set value form control
      this.formTransactionReportSearch.controls.keyword.setValue(
        filter.keyword
      );
      this.formTransactionReportSearch.controls.fromDate.setValue(
        filter.fromDate
      );
      this.formTransactionReportSearch.controls.toDate.setValue(filter.toDate);
      this.formTransactionReportSearch.controls.nationCodes.setValue(
        filter.nationCodes
      );
      this.formTransactionReportSearch.controls.transactionStatus.setValue(
        filter.transactionStatus
      );
      this.onSearch();
    } else {
      // set default value start date and end date
      const startDate = moment()
        .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
        .format(MOMENT_CONST.FORMAT_DEFAULT);
      const endDate = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
      this.formTransactionReportSearch.controls.fromDate.setValue(
        CommonUtils.reverseDate(startDate)
      );
      this.formTransactionReportSearch.controls.toDate.setValue(
        CommonUtils.reverseDate(endDate)
      );
      this.onSearch();
    }
  }

  /**
   * export file
   */
  exportFile(): void {
    // get value form control
    const bodySearch = this.formTransactionReportSearch.getRawValue();
    if (this.formTransactionReportSearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formTransactionReportSearch);
    } else {
      // get file name
      const fileName = this.translateService.instant(
        'template.internationalPaymentTemplate'
      );
      const obFile = this.transactionService.exportInternationalTransaction({
        fromDate: bodySearch.fromDate,
        toDate: bodySearch.toDate,
        keyword: bodySearch.keyword,
        nationCodes: bodySearch.nationCodes,
        transactionStatus: bodySearch.transactionStatus,
        timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
      });

      this.downloadService.downloadFileWithObservableAndName(
        obFile,
        fileName,
        FILE_EXTENSION.XLSX
      );
    }
  }

  /**
   * Change page: next page and preview page
   *
   * @param page PageEvent
   */
  onChangePage(page: PageEvent) {
    this.formTransactionReportSearch.controls.pageIndex.setValue(
      page.pageIndex
    );
    this.formTransactionReportSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  dateValidator(value: number): any {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return value > DATE_RANGE_STATISTICAL_LOAN_ONLINE
        ? { invalidDate: true }
        : null;
    };
  }

  changeValidDate() {
    if (
      this.formTransactionReportSearch.controls.fromDate.value &&
      this.formTransactionReportSearch.controls.toDate.value
    ) {
      if (this.formTransactionReportSearch.controls['toDate'].value) {
        this.maxDate =
          this.formTransactionReportSearch.controls['toDate'].value;
      }
      const startDate = CommonUtils.setStartTime(
        new Date(
          this.formTransactionReportSearch.controls.fromDate.value
        ).getTime()
      );
      const endDate = CommonUtils.setStartTime(
        new Date(
          this.formTransactionReportSearch.controls.toDate.value
        ).getTime()
      );
      const dayMax =
        (endDate - startDate) / CALCULATE_MILLISECONDS_IN_A_YEAR.DAY;
      if (dayMax > DATE_RANGE_STATISTICAL_LOAN_ONLINE) {
        this.formTransactionReportSearch.controls.fromDate.setValidators([
          Validators.required,
          this.dateValidator(dayMax),
          this.isValidMaxDate,
        ]);
        this.formTransactionReportSearch.controls.fromDate.updateValueAndValidity();
      } else {
        this.formTransactionReportSearch.controls.fromDate.clearValidators();
        this.formTransactionReportSearch.controls.fromDate.setValidators([
          Validators.required,
          this.isValidMaxDate,
        ]);
        this.formTransactionReportSearch.controls.fromDate.updateValueAndValidity();
      }
    }
  }

  onSearchSubmit() {
    this.formTransactionReportSearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.formTransactionReportSearch.controls.length.setValue(
      PAGINATION.LENGTH_DEFAULT
    );
    this.onSearch();
  }

  /**
   * search form
   */
  onSearch(): void {
    if (this.formTransactionReportSearch.valid) {
      this.formTransactionReportSearch.controls.hasPageable.setValue(true);
      this.transactionService
        .searchInternationalTransaction(this.formTransactionReportSearch.value)
        .subscribe((res: any): void => {
          this.transactionReport = res.body.content;
          this.formTransactionReportSearch.controls.length.setValue(
            res.body.totalElements
          );
        });
    }
  }

  /**
   * index on list loan online
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formTransactionReportSearch.value.pageIndex,
      this.formTransactionReportSearch.value.pageSize
    );
  }

  /**
   * reset form
   */
  onReset(): void {
    // set default value date and reset data
    const startDateReset = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDateReset = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.formTransactionReportSearch.controls.keyword.reset();
    this.formTransactionReportSearch.controls.fromDate.setValue(
      CommonUtils.reverseDate(startDateReset)
    );
    this.formTransactionReportSearch.controls.toDate.setValue(
      CommonUtils.reverseDate(endDateReset)
    );
    this.formTransactionReportSearch.controls.fromDate.clearValidators();
    this.formTransactionReportSearch.controls.fromDate.setValidators([
      Validators.required,
    ]);
    this.formTransactionReportSearch.controls.fromDate.updateValueAndValidity();
    this.formTransactionReportSearch.controls.transactionStatus.setValue(null);
    this.formTransactionReportSearch.controls.nationCodes.setValue(null);
    this.formTransactionReportSearch
      .get('timeZoneStr')
      ?.setValue(Intl.DateTimeFormat().resolvedOptions().timeZone);
  }

  onSelectAll(value?: string) {
    const selectedNationCodes = this.CROSS_BORDER_NATION_CODES.map(
      (item) => item.code
    );
    const selectedTransactionStatus = this.TRANSACTION_ENTITY_STATUS.map(
      (item) => item.code
    );
    switch (value) {
      case SELECTED_CONST.CROSS_BORDER_NATION_CODES:
        this.formTransactionReportSearch
          .get('nationCodes')
          ?.patchValue(selectedNationCodes);
        break;
      case SELECTED_CONST.TRANSACTION_STATUS:
        this.formTransactionReportSearch
          .get('transactionStatus')
          ?.patchValue(selectedTransactionStatus);
        break;
    }
  }

  onClearAll(value?: string) {
    switch (value) {
      case SELECTED_CONST.CROSS_BORDER_NATION_CODES:
        this.formTransactionReportSearch.get('nationCodes')?.patchValue([]);
        break;
      case SELECTED_CONST.TRANSACTION_STATUS:
        this.formTransactionReportSearch
          .get('transactionStatus')
          ?.patchValue([]);
        break;
    }
  }
}
