export interface IDotp {
  dotpId?: number;
  customerId?: number;
  cif?: string;
  encKey: string;
  token: string;
  fullname?: string;
  phoneNumber?: string;
  transData: string;
  deviceId?: string;
  deviceName?: string;
  signUpDate?: string;
  cancellationDate?: string;
  status?: number;
}

export interface ISearchDotp {
  keyword?: string;
  status?: number;
  pageIndex?: number;
  pageSize?: number;
}

export class Dotp implements IDotp {
  constructor(
    public encKey: string,
    public token: string,
    public transData: string,
    public dotpId?: number,
    public customerId?: number,
    public cif?: string,
    public fullname?: string,
    public phoneNumber?: string,
    public deviceId?: string,
    public deviceName?: string,
    public signUpDate?: string,
    public cancellationDate?: string,
    public status?: number
  ) {
    this.encKey = encKey;
    this.token = token;
    this.transData = transData;
    this.dotpId = dotpId;
    this.customerId = customerId;
    this.cif = cif;
    this.fullname = fullname;
    this.phoneNumber = phoneNumber;
    this.deviceId = deviceId;
    this.deviceName = deviceName;
    this.signUpDate = signUpDate;
    this.cancellationDate = cancellationDate;
    this.status = status;
  }
}
