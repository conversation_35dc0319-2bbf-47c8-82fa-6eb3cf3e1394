import { Component, HostListener, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ENTITY_STATUS, MODAL_ACTION } from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IDepartment } from '@shared/models/department.model';
import { DepartmentService } from '@shared/services/department.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-department-create-update',
  templateUrl: './department-create-update.component.html',
  styleUrls: ['./department-create-update.component.scss'],
})
export class DepartmentCreateUpdateComponent implements OnInit {
  department!: IDepartment;

  isUpdate = false;

  isDetail = false;

  isCreate = false;

  departmentId!: number;

  formDepartment!: FormGroup;

  MODAL_ACTION = MODAL_ACTION;

  ENTITY_STATUS = ENTITY_STATUS;

  VALIDATORS = VALIDATORS;
  SYSTEM_RULES = SYSTEM_RULES;

  isHiddenBtnClose = false;

  constructor(
    private formBuilder: FormBuilder,
    private departmentService: DepartmentService,
    private toastService: ToastrCustomService,
    public activeModal: NgbActiveModal
  ) {}

  ngOnInit(): void {
    if (this.isCreate) {
      this.initForm();
    } else {
      this.initForm(this.department);
    }
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.activeModal.close();
  }

  initForm(department?: IDepartment) {
    this.formDepartment = this.formBuilder.group({
      departmentCode: [
        department?.departmentCode,
        [
          Validators.required,
          Validators.minLength(VALIDATORS.LENGTH.DEPARTMENT_CODE_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.DEPARTMENT_CODE_MAX_LENGTH),
        ],
      ],
      departmentName: [
        department?.departmentName,
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.PATTERN_NAME),
          Validators.minLength(VALIDATORS.LENGTH.DEPARTMENT_NAME_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.DEPARTMENT_NAME_MAX_LENGTH),
        ],
      ],
      shortName: [
        department?.shortName,
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.PATTERN_NAME),
          Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
        ],
      ],
      description: [
        department?.description,
        [
          Validators.maxLength(
            VALIDATORS.LENGTH.DEPARTMENT_DESCRIPTION_MAX_LENGTH
          ),
        ],
      ],
      departmentId: [department?.departmentId],
      createdDate: department?.createdDate || '',
      userCreated: department?.createdByFullname || '',
      lastModifiedDate: department?.lastModifiedDate || '',
      userLastModified: department?.lastModifiedByFullname || '',
    });

    if (this.isDetail) {
      this.formDepartment.disable();
    }
  }

  onSave() {
    if (!this.isUpdate && this.formDepartment.valid) {
      this.departmentService
        .create(this.formDepartment.value)
        .subscribe((result) => {
          if (result.ok && result.body) {
            this.activeModal.close(MODAL_ACTION.RESET.code);
            this.toastService.success('common.action.createSuccess');
          }
        });
    } else if (this.isUpdate && this.formDepartment.valid) {
      this.departmentService
        .update(this.formDepartment.value)
        .subscribe((result) => {
          if (result.ok && result.body) {
            this.activeModal.close(MODAL_ACTION.RESET.code);
            this.toastService.success('common.action.updateSuccess');
          }
        });
    } else if (this.formDepartment.invalid) {
      CommonUtils.markFormGroupTouched(this.formDepartment);
    }
  }
}
