<div class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formSearch" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-md-6 col-lg-3">
            <div class="form-group">
              <label>{{ "common.action.searchKeyword" | translate }}</label>
              <input trim formControlName="keyword" type="text" class="w-100 form-control"
                placeholder="{{ 'common.action.searchKeyword' | placeholder }}" />
            </div>
          </div>
          <div class="col-lg-3">
            <div class="form-group">
              <label>{{ "common.status" | translate }}</label>
              <ng-select appearance="outline" [searchable]="false" [clearable]="true" formControlName="status"
                placeholder="{{ 'common.status' | translate }}">
                <ng-option *ngFor="let item of CONFIG_TRANS_STATUS" [value]="item.value">
                  <span>{{ item.label | translate }}</span>
                </ng-option></ng-select>
            </div>
          </div>
          <div class="col-lg-3 row">
            <div class="col-lg-6">
              <div class="form-group">
                <label>{{
                  "model.configTransaction.fromDate" | translate
                  }}</label>
                <mat-form-field appearance="fill" class="date-picker">
                  <input matInput [matDatepicker]="fromDate" formControlName="fromDate"
                    [placeholder]="DATE_CONSTANT.DDMMYYYY_SLASH_BIG" max="{{
                      formSearch.controls['toDate'].value
                        | date : DATE_CONSTANT.YYYYMMDD_HYPHEN_SMALL
                    }}" dateTransform />
                  <mat-datepicker-toggle matSuffix [for]="fromDate"></mat-datepicker-toggle>
                  <mat-datepicker #fromDate></mat-datepicker>
                </mat-form-field>
              </div>
            </div>
            <div class="col-lg-6">
              <div class="form-group">
                <label>{{
                  "model.configTransaction.toDate" | translate
                  }}</label>
                <mat-form-field appearance="fill" class="date-picker">
                  <input matInput [matDatepicker]="toDate" formControlName="toDate"
                    [placeholder]="DATE_CONSTANT.DDMMYYYY_SLASH_BIG" dateTransform min="{{
                      formSearch.controls['fromDate'].value
                        | date : DATE_CONSTANT.YYYYMMDD_HYPHEN_SMALL
                    }}" />
                  <mat-datepicker-toggle matSuffix [for]="toDate"></mat-datepicker-toggle>
                  <mat-datepicker #toDate></mat-datepicker>
                </mat-form-field>
              </div>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 mt-4 d-flex">
            <div class="col-btn-reset" ngbTooltip="{{ 'common.action.reset' | translate }}">
              <div class="btn-reset" (click)="onReset()">
                <i class="bi bi-arrow-clockwise" type="button"> </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" type="submit" *hasPrivileges="SYSTEM_RULES.TRANSACTION_LIMIT_READ">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-flex justify-content-between align-items-center text-right mb-2 mt-4">
          <h5>{{ "model.configTransaction.title" | translate }}</h5>
          <div class="">
            <button class="btn btn-white mb-2" type="button" (click)="onExport()"
              *hasPrivileges="SYSTEM_RULES.TRANSACTION_LIMIT_EXPORT">
              {{ "common.action.export" | translate }}
            </button>
            <button class="btn btn-red mb-2" type="button" *hasPrivileges="SYSTEM_RULES.TRANSACTION_LIMIT_CREATE"
              [routerLink]="[ROUTER_UTILS.sysManage.configTransaction.create]" [routerLinkActive]="['active']">
              {{ "common.action.create" | translate }}
            </button>
          </div>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table table-config-transaction">
          <thead>
            <tr>
              <th class="text-center" rowspan="2" [width]="'50px'">
                {{ "model.configTransaction.no" | translate }}
              </th>
              <th class="" rowspan="2" [width]="'200px'">
                {{ "model.configTransaction.packageName" | translate }}
              </th>
              <th rowspan="2" class="text-right" [width]="'90px'">
                {{ "model.configTransaction.sectorType" | translate }}
              </th>
              <th rowspan="2" class="text-left" [width]="'100px'">
                {{ "model.managePayment.currency" | translate }}
              </th>
              <th [width]="'180px'" class="text-center">
                {{ "model.configTransaction.fromDate" | translate }}
              </th>
              <th [width]="'180px'" class="text-center">
                {{ "model.configTransaction.toDate" | translate }}
              </th>
              <th rowspan="2" [width]="'180px'" class="text-center">
                {{ "model.configTransaction.createdDate" | translate }}
              </th>
              <th rowspan="2" [width]="'200px'" class="text-center">
                {{ "model.configTransaction.approvedBy" | translate }}
              </th>
              <th rowspan="2" [width]="'140px'" class="text-center">
                {{ "model.configTransaction.approvedDate" | translate }}
              </th>
              <th rowspan="2" [width]="'150px'" class="text-center">
                {{ "model.configTransaction.status" | translate }}
              </th>
              <th rowspan="2" class="text-center" [width]="'100px'">
                {{ "model.configTransaction.action" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of data; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td>{{ item.serviceName }}</td>
              <td class="text-right">{{ item.sectorId }}</td>
              <td class="text-left">{{ item.currency }}</td>
              <td class="text-right">{{ item.startDate }}</td>
              <td class="text-right">{{ item.endDate }}</td>
              <td class="text-right">
                <span>{{ item.createdDate }}</span>
              </td>
              <td class="text-center">{{ item.approvalBy }}</td>
              <td class="text-right">{{ item.approvalDate }}</td>
              <td class="text-center">
                <span class="badge" [ngClass]="CONFIG_TRANS_STATUS_MAP[item.status || 0].style">{{
                  CONFIG_TRANS_STATUS_MAP[item.status || 0].label | translate
                  }}</span>
              </td>
              <td class="text-center">
                <button ngbTooltip="{{ 'common.action.detail' | translate }}" class="btn px-1 py-0"
                  *hasPrivileges="SYSTEM_RULES.TRANSACTION_LIMIT_READ" (click)="onDetail(item.transactionLimitId)">
                  <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                </button>
                <ng-container *ngIf="
                    item.status === CONFIG_TRANS_STATUS_CONST.WAITING.value
                  ">
                  <button ngbTooltip="{{ 'common.action.update' | translate }}" class="btn px-1 py-0"
                    *hasPrivileges="SYSTEM_RULES.TRANSACTION_LIMIT_UPDATE" (click)="onUpdate(item.transactionLimitId)">
                    <i class="fa fa-edit mb-color" aria-hidden="true"></i>
                  </button>
                </ng-container>
                <ng-container *ngIf="
                    item.status === CONFIG_TRANS_STATUS_CONST.WAITING.value
                  ">
                  <button ngbTooltip="{{ 'common.action.delete' | translate }}" class="btn px-1 py-0"
                    *hasPrivileges="SYSTEM_RULES.TRANSACTION_LIMIT_DELETE" (click)="onDelete(item.transactionLimitId)">
                    <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                  </button>
                </ng-container>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="row d-block text-center m-0 no-search-result-container" *ngIf="data?.length === 0">
          <img src="/assets/dist/img/icon/empty.svg" height="120" alt="no_search_result" />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
      </div>
      <div *ngIf="data?.length" class="paginator col-md-12">
        <mat-paginator [length]="formSearch.value.length" [pageSize]="formSearch.value.pageSize"
          [pageIndex]="formSearch.value.pageIndex" [pageSizeOptions]="pageSizeOptions" (page)="onChangePage($event)"
          aria-label="Select page">
        </mat-paginator>
      </div>
    </div>
  </div>
</div>