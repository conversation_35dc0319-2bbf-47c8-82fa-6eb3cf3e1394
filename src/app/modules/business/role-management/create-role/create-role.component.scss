.btn-tool {
  float: right !important;
  margin-right: -0.625rem;
  margin-top: 0px;
}

.card-header {
  background: #f3ebdd;
  padding: 6px 40px;
}

.card {
  margin-bottom: 0px;
}

.example-accordion {
  display: block;
  width: 100%;
}

.example-accordion-item {
  display: block;
}

.example-accordion-item-header {
  background: #f3ebdd;
  border-radius: 4px;
}

.example-accordion-item+.example-accordion-item {
  border-top: none;
}

.example-accordion-item-header {
  display: flex;
  align-content: center;
  justify-content: space-between;
}

.example-accordion-item-description {
  font-size: 0.85em;
  color: var(--mb-color);
}

.example-accordion-item-header {
  padding: 12px;
  border: 0.5px solid #cecece;
  margin-bottom: 8px;
  background: rgb(243, 245, 255);
}

.example-accordion-item-body {
  padding: 10px 10px;
}

.example-accordion-item-description i {
  font-size: 18px;
}

.example-accordion-item-header h5 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 0px;
}

::ng-deep .mat-checkbox-frame {
  border: 2px solid var(--mb-color);
}

::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
  background-color: var(--mb-color);
}

.mat-checkbox-inner-container {
  margin-top: 5px !important;
}

::ng-deep .mat-checkbox-layout {
  margin: 0;
}

::ng-deep .mat-checkbox .mat-checkbox-layout .mat-checkbox-inner-container {
  margin-top: 3px !important;
}

::ng-deep .check-box-group-role.mat-checkbox .mat-checkbox-layout .mat-checkbox-inner-container {
  margin-top: 1px !important;
}

::ng-deep .mat-checkbox .mat-checkbox-layout {
  white-space: inherit !important;
}
