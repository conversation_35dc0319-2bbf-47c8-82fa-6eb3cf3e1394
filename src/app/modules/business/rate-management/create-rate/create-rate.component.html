<section class="box-content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-6 back-container">
        <img src="assets/dist/img/icon/back.png" (click)="backToList()" />
        <h5>
          {{
            action === ROUTER_ACTIONS.create
              ? ("model.rate.create" | translate)
              : action === ROUTER_ACTIONS.update
              ? ("model.rate.update" | translate)
              : ("model.rate.detail" | translate)
          }}
        </h5>
      </div>
      <div class="col-md-6">
        <div class="tab-list">
          <div class="col-3 text-labels">
            {{ "common.textLanguage" | translate }}
          </div>
          <div class="tab-labels">
            <div class="" *ngFor="let lang of LANGUAGES">
              <div
                [ngClass]="
                  language === lang.code ? 'tab-group action' : 'tab-group'
                "
                (click)="onChangeLang(lang.code)"
              >
                {{ lang.label | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <form [formGroup]="formCreate">
      <div class="col-md-12">
        <div class="row">
          <h2 class="title-create">
            {{ "model.rate.information" | translate }}
          </h2>
        </div>
        <div class="row border-create">
          <h3>{{ "model.rate.information" | translate }}</h3>
          <div class="w-100">
            <div class="col-md-12">
              <div class="row">
                <div class="col-12">
                  <div class="form-group">
                    <label
                      >{{ "model.event.title" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      trim
                      formControlName="title"
                      type="text"
                      placeholder="{{ 'model.event.title' | translate }}"
                      class="w-100"
                      class="form-control"
                      [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
                    />
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="
                        formCreate.get('title')?.errors?.required &&
                        formCreate.get('title')?.touched
                      "
                    >
                      {{ "error.event.required.title" | translate }}
                    </small>
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="
                        formCreate.get('title')?.errors?.maxlength &&
                        formCreate.get('title')?.touched
                      "
                    >
                      {{
                        "error.event.maxLength.title"
                          | translate
                            : {
                                param: VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH
                              }
                      }}
                    </small>
                  </div>
                </div>
                <div class="col-3">
                  <div class="col-12">
                    <label>{{ "common.image" | translate }}</label>
                    <div class="bank-upload">
                      <app-upload-images
                        (imageUploaded)="onUploadPics($event)"
                        [fileRequired]="fileRequired"
                        [fileExtension]="FILE_UPLOAD_EXTENSIONS.AVATAR"
                        (imageRemoved)="onRemoveImage($event)"
                        [checkDisabled]="action === ROUTER_ACTIONS.detail"
                        [imageUrls]="imageUrls"
                        [type]="action"
                      ></app-upload-images>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="d-block text-center mb-5 mt-4">
              <ng-container>
                <button
                  class="btn btn-white mr-2"
                  data-toggle="modal"
                  (click)="backToList()"
                >
                  {{ "common.action.back" | translate }}
                </button>
                <button
                  class="btn btn-red"
                  (click)="onSubmit()"
                  *ngIf="action !== ROUTER_ACTIONS.detail"
                >
                  {{
                    (action === ROUTER_ACTIONS.update
                      ? "common.action.update"
                      : "common.action.create"
                    ) | translate
                  }}
                </button>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</section>
