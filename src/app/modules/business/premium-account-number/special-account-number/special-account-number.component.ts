import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  FILE_EXTENSION,
  HIDDEN_ACCOUNT_NUMBER_MAP,
  MODAL_ACTION,
  PAGINATION,
  SPECIAL_ACCOUNT_NUMBER_STATUS,
  SPECIAL_ACCOUNT_NUMBER_STATUS_CONST,
  SPECIAL_ACCOUNT_NUMBER_STATUS_MAP,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { SELECTED_CONST } from '@shared/constants/user.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ISpecialPremiumAccountNumber } from '@shared/models/premium-account-number.modal';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { SpecialAccountService } from '@shared/services/special-account.service';
import { ROUTER_ACTIONS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import { ImportSpecialAccountNumberComponent } from './modal-import/modal-import-special-account-number.component';
import { SpecialAccountNumberCreateComponent } from './special-account-number-create/special-account-number-create.component';
@Component({
  selector: 'app-special-account-number',
  templateUrl: './special-account-number.component.html',
  styleUrls: ['./special-account-number.component.scss'],
})
export class SpecialAccountNumberComponent implements OnInit {
  VALIDATORS = VALIDATORS;
  SYSTEM_RULES = SYSTEM_RULES;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  SPECIAL_ACCOUNT_NUMBER_STATUS_CONST = SPECIAL_ACCOUNT_NUMBER_STATUS_CONST;
  SPECIAL_ACCOUNT_NUMBER_STATUS = SPECIAL_ACCOUNT_NUMBER_STATUS;
  SPECIAL_ACCOUNT_NUMBER_STATUS_MAP = SPECIAL_ACCOUNT_NUMBER_STATUS_MAP;
  pageSizeOptions = PAGINATION.OPTIONS;
  FILE_EXTENSION = FILE_EXTENSION;
  ENTITY_STATUS = ENTITY_STATUS;
  SELECTED_CONST = SELECTED_CONST;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  HIDDEN_ACCOUNT_NUMBER_MAP = HIDDEN_ACCOUNT_NUMBER_MAP;

  data: ISpecialPremiumAccountNumber[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private modal: NgbModal,
    private modalService: ModalService,
    private toastService: ToastrCustomService,
    private specialPremiumAccountNumberService: SpecialAccountService,
    private translateService: TranslateService,
    private downloadService: DownloadService
  ) {}

  ngOnInit(): void {
    this.onSearch();
  }

  formSearch = this.formBuilder.group({
    keyword: '',
    status: null,
    hided: [true],
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    hasPageable: true,
  });

  onReset() {
    this.formSearch.controls.keyword.reset();
    this.formSearch.controls.status.reset();
    this.formSearch.controls.hided.setValue(true);
  }

  onSearch(): void {
    if (this.formSearch.valid) {
      const params = this.formSearch.value;
      this.specialPremiumAccountNumberService.search(params).subscribe((res: any) => {
        this.data = res.body.content;
        this.formSearch.controls.length.setValue(res.body.totalElements);
      });
    }
  }

  uploadFile() {}

  onSearchSubmit() {
    this.formSearch.controls.pageIndex.setValue(PAGINATION.PAGE_NUMBER_DEFAULT);
    this.formSearch.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    this.onSearch();
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(index, this.formSearch.value.pageIndex, this.formSearch.value.pageSize);
  }

  detail(id?: number) {
    const modalRef = this.modal.open(SpecialAccountNumberCreateComponent, {
      backdrop: 'static',
      size: 'xl',
      centered: true,
      keyboard: false,
    });
    modalRef.componentInstance.id = id;
    modalRef.componentInstance.action = ROUTER_ACTIONS.detail;
  }

  update(id?: number) {
    const modalRef = this.modal.open(SpecialAccountNumberCreateComponent, {
      backdrop: 'static',
      size: 'xl',
      centered: true,
      keyboard: false,
      windowClass: 'modal-max'
    });
    modalRef.componentInstance.id = id;
    modalRef.componentInstance.action = ROUTER_ACTIONS.update;
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;

    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }

  hideAndUnHide(data: ISpecialPremiumAccountNumber): void {
    const modalData = {
      title: data.hided ? 'model.specialAccountNumber.unlock' : 'model.specialAccountNumber.lock',
      content: data.hided ? 'model.specialAccountNumber.unlockContent' : 'model.specialAccountNumber.lockContent',
      interpolateParams: {
        title: `<b>${data?.specialPremiumAccountNumber || ''}</b>`,
      },
      confirmButtonText: 'common.action.agree',
      cancelButtonText: 'common.action.cancel',
      buttonOrder: 'cancel-first',
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = {
          id: data.specialPremiumAccountNumberId,
        };
        if (data.hided) {
          this.specialPremiumAccountNumberService.unhide(params).subscribe((res) => {
            this.toastService.success('common.action.unhideSuccess');
            this.onSearch();
          });
        } else {
          this.specialPremiumAccountNumberService.hide(params).subscribe((res) => {
            this.toastService.success('common.action.hideSuccess');
            this.onSearch();
          });
        }
      }
    });
  }

  onDelete(data?: ISpecialPremiumAccountNumber) {
    const modalData = {
      title: 'model.specialAccountNumber.delete',
      content: 'model.specialAccountNumber.deleteConfig',
      interpolateParams: {
        title: `<b>${data?.specialPremiumAccountNumber || ''}</b>`,
      },
      confirmButtonText: 'common.action.delete', // Đổi text nút xác nhận thành "Xóa"
      cancelButtonText: 'common.action.cancel', // Đổi text nút hủy nếu cần
      buttonOrder: 'cancel-first', // Hoặc 'confirm-first' tùy theo thứ tự bạn muốn
      confirmBtnClass: 'btn-danger',
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = {
          id: data?.specialPremiumAccountNumberId,
        };
        this.specialPremiumAccountNumberService.delete(params).subscribe(() => {
          this.toastService.success('common.action.deleteSuccess');
          if (this.data?.length === 1) {
            this.formSearch.controls.pageIndex.setValue(
              this.formSearch.controls.pageIndex.value === 0 ? '' : Number(this.formSearch.controls.pageIndex.value) - 1
            );
          }
          this.onSearch();
        });
      }
    });
  }

  onChangePage(page: PageEvent) {
    this.formSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  onOpenSpecialAccount() {
    const modalRef = this.modal.open(SpecialAccountNumberCreateComponent, {
      size: 'xl',
      keyboard: true,
      backdrop: 'static',
      centered: true,
    });

    modalRef.componentInstance.action = ROUTER_ACTIONS.create;
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;

    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }

  calculatePaymentPrice(listPrice: any, discount: any) {
    if (listPrice && discount >= 0) { return listPrice - (listPrice * discount) / 100; }
    return listPrice;
  }

  importFile() {
    const modalRef = this.modal.open(ImportSpecialAccountNumberComponent, {
      backdrop: 'static',
      centered: true,
      size: 'lg',
      keyboard: true,
    });

    modalRef.componentInstance.action = MODAL_ACTION.CONFIRM;

    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }

  onExport(): void {
    const formSearch = this.formSearch.value;
    const fileName = this.translateService.instant('template.specialAccountNumber');
    const obFile = this.specialPremiumAccountNumberService.exportSpecialAccount(formSearch);
    this.downloadService.downloadFileWithObservableAndName(obFile, fileName, FILE_EXTENSION.XLSX);
    return;
  }
}
