import { Component, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { ACTION_TOKEN_CONST } from '@shared/constants/user.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ActiveToken, IActiveToken } from '@shared/models/active-token.model';
import { AccountService } from '@shared/services/account.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';

@Component({
  selector: 'app-forgot-password-verify',
  templateUrl: './forgot-password-verify.component.html',
  styleUrls: ['./forgot-password-verify.component.scss'],
})
export class ForgotPasswordVerifyComponent implements OnInit {
  userForgotPasswordForm = this.fb.group({
    email: [
      '',
      [
        Validators.required,
        Validators.pattern(VALIDATORS.PATTERN.EMAIL_MB),
        Validators.minLength(VALIDATORS.LENGTH.EMAIL_MIN_LENGTH),
        Validators.maxLength(VALIDATORS.LENGTH.EMAIL_MAX_LENGTH),
      ],
    ],
  });

  maxtime = 0;

  hidevalue = false;

  timer: any;

  VALIDATORS = VALIDATORS;

  activeTokenDTO: IActiveToken = {};

  ACTION_TOKEN_CONST = ACTION_TOKEN_CONST;

  // action from link: RESEND_LINK_ACTIVATE, ACTIVATED
  // if action = RESEND_LINK_ACTIVATE then resend link, ACTIVATED alert text activated
  action = '';

  constructor(
    private fb: FormBuilder,
    private toastService: ToastrCustomService,
    private translate: TranslateService,
    private accountService: AccountService,
    private activatedRoute: ActivatedRoute,
    private router: Router
  ) {
    this.activatedRoute.paramMap.subscribe((res) => {
      const token = res.get('token');
      if (token) {
        this.activeTokenDTO.token = token;
      }
    });
  }

  ngOnInit(): void {
    // get token from paramater
    this.onVerify();
  }

  /**
   * onVerify: call api Verify
   */
  onVerify(): void {
    const params = { token: this.activeTokenDTO.token };
    this.accountService.verify(params).subscribe(
      (res: any) => {
        const activeToken = res.body as ActiveToken;
        // set action = tokenAction and action is RESEND_LINK_ACTIVATE, ACTIVATED
        if (activeToken.tokenAction) {
          this.action = activeToken.tokenAction;
        }
        if (activeToken.timeCountdown) {
          this.maxtime = activeToken.timeCountdown;
          this.StartTimer();
        }
      },
      (error) => {
        window.location.replace(ROUTER_UTILS.auth.login);
        // location.assign(
        //   ROUTER_UTILS.auth.login
        // );
      }
    );
  }

  /**
   * onResend: call api resend mail
   */
  onResend(): void {
    const params = { token: this.activeTokenDTO.token };
    this.accountService.resend(params).subscribe((res: any) => {
      // this.toastService.success('common.success');
      const activeToken = res.body as ActiveToken;
      if (activeToken.tokenAction) {
        this.action = activeToken.tokenAction;
        this.hidevalue = false;
      }
      if (activeToken.token) {
        window.location.replace(
          `${ROUTER_UTILS.portal.forgotPassword.root}/${activeToken.token}/${ROUTER_ACTIONS.verify}`
        );
        // location.assign(
        //   `${ROUTER_UTILS.portal.forgotPassword.root}/${activeToken.token}/${ROUTER_ACTIONS.verify}`
        // );
      }
    });
  }

  /**
   * displayContent: display content in file i18n
   *
   * @param content string
   * @returns string
   */
  displayContent(content: string): string {
    return this.translate.instant(content);
  }

  StartTimer() {
    setTimeout(() => {
      this.maxtime -= 1;

      if (this.maxtime > 0) {
        this.hidevalue = false;
        this.StartTimer();
      } else {
        this.hidevalue = true;
      }
      this.timer = this.maxtime;
    }, 1000);
  }
}
