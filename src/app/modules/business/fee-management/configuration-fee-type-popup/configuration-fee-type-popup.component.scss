.modal-header {
  background: #F4F5FA;
  height: 45px;
  align-items: center;
}

.modal-title {
  color: rgba(33, 33, 33, 0.85);
  opacity: 0.6;
}

.close {
  padding: 9px 14px;
  font-size: 20px;
  color: white;
  opacity: 1;
}
.btn-secondary {
  border: 1px solid #eb2d4b;
  background: white;
  color: #eb2d4b;
  border-radius: 8px;
  font-size: 14px;
  padding: 8px 25px;
}
.btn-danger {
  background:  var(--mb-color);
  border-radius: 71px;
  color: white;
  font-size: 14px;
  padding: 8px 25px;
}

.btn-white-gray {
  background: #F4F5FA;
  border-radius: 71px;
  color: rgba(158, 158, 158, 0.9);
  font-size: 14px;
  padding: 8px 25px;
  box-shadow: 2px 4px 5px rgba(186, 231, 254, 0.15);
  justify-content: center;
  align-items: center;
}
.modal-body h3 {
  font-weight: 500;
  font-size: 20px;
}
.modal-body p {
  color: #282828;
  font-size: 16px;
  opacity: 0.5;
}

.modal-body {
  padding: 24px 24px 18px 24px;
}

mat-radio-button {
  margin-right: 30px !important;
}

::ng-deep .mat-radio-button.mat-accent .mat-radio-inner-circle {
  background-color:  var(--mb-color);
}

::ng-deep .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
  border-color:  var(--mb-color);
}
