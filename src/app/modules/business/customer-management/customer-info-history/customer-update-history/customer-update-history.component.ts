import {
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges
} from '@angular/core';
import { GENDER_MAP } from '@shared/constants/app.constants';
import {
  MARITAL_STATUS_MAP,
  RESIDENT_STATUS_MAP
} from '@shared/constants/customer.constants';
import { Customer, ICustomer } from '@shared/models/customer.model';
import { IFileEntrySearch } from '@shared/models/request/file-entry.search';
import { CustomerService } from '@shared/services/customer.service';
import { ROUTER_ACTIONS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-customer-update-history',
  templateUrl: './customer-update-history.component.html',
  styleUrls: ['./customer-update-history.component.scss'],
})
export class CustomerUpdateHistoryComponent implements OnInit, OnChanges {
  @Input()
  customerInfo: ICustomer = new Customer();
  imageUrls = new Map();

  ROUTER_ACTIONS = ROUTER_ACTIONS;
  GENDER_MAP = GENDER_MAP;
  RESIDENT_STATUS_MAP = RESIDENT_STATUS_MAP;
  MARITAL_STATUS_MAP = MARITAL_STATUS_MAP;

  constructor(private customerService: CustomerService) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    this.getIdCardFile();
  }

  private getIdCardFile(): void {
    if (this.customerInfo?.oldVersions) {
      this.customerInfo?.oldVersions?.forEach((customer) => {
        const image: any = [];
        customer?.idCardFiles?.forEach((item: any) => {
          const searchFile: IFileEntrySearch = {};
          searchFile.classPk = customer.customerId;
          searchFile.fileEntryId = item.id;
          searchFile.normalizeName = item.normalizeName;

          this.customerService
            .getImage(searchFile)
            .subscribe((responsive: any) => {
              CommonUtils.blobToBase64(responsive.body).then((base64) => {
                image.push({
                  src: base64,
                  name: null,
                  id: item.id,
                  file: null,
                });
              });
            });
        });

        this.imageUrls.set(customer.customerId, image);
      });
    }
  }
}
