<div class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formSearch" (submit)="onsearchSubmit()">
        <div class="d-flex justify-content-end mb-3 mt-4">
          <button
            class="btn btn-red mb-2"
            type="button"
            *hasPrivileges="SYSTEM_RULES.DEBIT_ACCOUNT_CREATE"
            (click)="openCreateModal()"
          >
            {{ "common.action.create" | translate }}
          </button>
        </div>
      </form>

      <div class="table-responsive">
        <table class="table table-customer">
          <thead>
            <tr>
              <th class="text-center">
                {{ "model.managePayment.no" | translate }}
              </th>
              <th class="text-left">
                {{ "model.managePayment.accountNumber" | translate }}
              </th>
              <th class="text-right">
                {{ "model.manageSaving.cif" | translate }}
              </th>
              <th>{{ "model.managePayment.accountName" | translate }}</th>
              <th class="text-left">
                {{ "model.managePayment.bankName" | translate }}
              </th>
              <th class="text-left">
                {{ "model.managePayment.currency" | translate }}
              </th>
              <th class="text-right">
                {{ "managePayment.limit" | translate }}
              </th>
              <th class="text-right">
                {{ "model.managePayment.balance" | translate }}
              </th>
              <th class="text-right">
                {{ "model.managePayment.usageBalance" | translate }}
              </th>
              <th class="text-right">
                {{ "managePayment.addedTransfer" | translate }}
              </th>
              <th class="text-center status-freezer">
                {{ "model.managePayment.status" | translate }}
              </th>
              <th class="text-center freezer">
                {{ "model.managePayment.action" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of paymentData; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td class="text-left">{{ item?.accountNumber }}</td>
              <td class="text-right">{{ item?.cif }}</td>
              <td title="{{ item?.accountName }}">
                {{ item?.accountName | limitWord : 30 }}
              </td>
              <td class="text-left">{{ item?.accountBank }}</td>
              <td class="text-left">{{ item?.accountCurrency }}</td>
              <td class="text-right">
                {{ item?.transactionLimit | currencyDecimalLak }}
              </td>
              <td class="text-right">
                {{ item?.accountBalance || 0 | currencyDecimalLak }}
              </td>
              <td class="text-right">
                {{ item?.amountTransferred || 0 | currencyLak }}
              </td>
              <td class="text-right">
                {{ item?.amountAdded || 0 | currencyLak }}
              </td>
              <td class="text-center status-freezer-td">
                <span
                  class="badge"
                  [ngClass]="ENTITY_STATUS_MAP[item.status].style"
                  >{{ ENTITY_STATUS_MAP[item.status].label | translate }}</span
                >
              </td>
              <td class="text-center freezer-td">
                <button
                  ngbTooltip="{{ 'common.action.detail' | translate }}"
                  class="btn px-1 py-0"
                  (click)="
                    openUpdateDetailModal(ROUTER_ACTIONS.detail, item.id)
                  "
                >
                  <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                </button>
                <ng-container *ngIf="item.status !== 1">
                  <button
                    ngbTooltip="{{ 'common.action.update' | translate }}"
                    class="btn px-1 py-0"
                    (click)="
                      openUpdateDetailModal(ROUTER_ACTIONS.update, item.id)
                    "
                    data-toggle="modal"
                    *hasPrivileges="SYSTEM_RULES.DEBIT_ACCOUNT_WRITE"
                  >
                    <i class="fa fa-edit mb-color" aria-hidden="true"></i>
                  </button>
                </ng-container>
                <button
                  [ngbTooltip]="
                    (item.status === ENTITY_STATUS_CONST.ACTIVE.code
                      ? 'common.action.lock'
                      : 'common.action.unlock'
                    ) | translate
                  "
                  class="btn px-1 py-0"
                  (click)="lockAndUnlock(item)"
                  *hasPrivileges="
                    item.status === 1
                      ? SYSTEM_RULES.DEBIT_ACCOUNT_DEACTIVATE
                      : SYSTEM_RULES.DEBIT_ACCOUNT_ACTIVATE
                  "
                >
                  <i
                    [className]="
                      item.status === 1
                        ? 'fa fa-lock mb-color'
                        : 'fa fa-unlock mb-color'
                    "
                    aria-hidden="true"
                  ></i>
                </button>
                <ng-container *ngIf="item.status !== 1">
                  <button
                    ngbTooltip="{{ 'common.action.delete' | translate }}"
                    class="btn px-1 py-0"
                    (click)="delete(item)"
                    *hasPrivileges="SYSTEM_RULES.DEBIT_ACCOUNT_DELETE"
                  >
                    <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                  </button>
                </ng-container>
              </td>
            </tr>
          </tbody>
        </table>
        <div
          class="row d-block text-center m-0 no-search-result-wrapper"
          *ngIf="paymentData?.length === 0"
        >
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
      </div>
      <!-- <div *ngIf="paymentData.length">
        <mat-paginator
          [length]="formSearch.value.length"
          [pageSize]="formSearch.value.pageSize"
          [pageIndex]="formSearch.value.pageIndex"
          [pageSizeOptions]="pageSizeOptions"
          (page)="onChangePage($event)"
          aria-label="Select page"
        >
        </mat-paginator>
      </div> -->
    </div>
  </div>
</div>
