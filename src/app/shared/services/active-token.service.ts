import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IActiveToken } from '@shared/models/active-token.model';
import { Observable } from 'rxjs';

import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({ providedIn: 'root' })
export class ActiveTokenService extends AbstractService {
  public resourceUrl = '/public/active-token';

  constructor(protected http: HttpClient) {
    super(http);
  }

  /**
   * verify
   *
   * @param params
   * @returns Observable<EntityResponseType<IActiveToken>>
   */
  verify(params: any): Observable<EntityResponseType<IActiveToken>> {
    return super.post<EntityResponseType<IActiveToken>>(
      `${this.resourceUrl}/verify`,
      params
    );
  }

  /**
   * resend
   *
   * @param params
   * @returns Observable<EntityResponseType<IActiveToken>>
   */
  resend(params: any): Observable<EntityResponseType<IActiveToken>> {
    return super.post<EntityResponseType<IActiveToken>>(
      `${this.resourceUrl}/resend`,
      params
    );
  }
}
