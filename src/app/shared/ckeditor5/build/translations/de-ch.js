(function(e){const n=e["de-ch"]=e["de-ch"]||{};n.dictionary=Object.assign(n.dictionary||{},{"%0 of %1":"","Align center":"Zen<PERSON><PERSON>","Align left":"Linksbündig","Align right":"Rechtsbündig",Aquamarine:"",Big:"Gross",Black:"","Block quote":"Blockzitat",Blue:"",Bold:"Fett",Cancel:"Abbrechen",Code:"Code",Column:"Spalte",Default:"Standard","Delete column":"Spalte löschen","Delete row":"Zeile löschen","Dim grey":"","Document colors":"Farben des Dokuments","Dropdown toolbar":"","Edit block":"","Editor toolbar":"","Font Background Color":"Hintergrundfarbe der Schrift","Font Color":"Schriftfarbe","Font Family":"Schriftfami<PERSON>","Font Size":"Schriftgrösse",Green:"",Grey:"","Header column":"<PERSON>pfspalte","Header row":"<PERSON>pfspalte",Huge:"<PERSON><PERSON>ig","Insert code block":"Code-Block einfügen","Insert column left":"","Insert column right":"","Insert row above":"Zeile oben einfügen","Insert row below":"Zeile unten einfügen","Insert table":"Tabelle einfügen",Italic:"Kursiv",Justify:"Blocksatz","Light blue":"","Light green":"","Light grey":"","Merge cell down":"Zelle unten verbinden","Merge cell left":"Zelle links verbinden","Merge cell right":"Zele rechts verbinden","Merge cell up":"Zelle oben verbinden","Merge cells":"Zellen verbinden",Next:"",Orange:"","Plain text":"Nur Text",Previous:"",Purple:"",Red:"",Redo:"Wiederherstellen","Remove color":"Farbe entfernen","Rich Text Editor":"Rich-Text-Edito","Rich Text Editor, %0":"Rich-Text-Editor, %0",Row:"Zeile",Save:"Speichern","Saving changes":"Änderungen werden gespeichert","Select column":"","Select row":"","Show more items":"",Small:"Klein","Split cell horizontally":"Zelle horizontal teilen","Split cell vertically":"Zelle vertikal teilen",Strikethrough:"Durchgestrichen","Table toolbar":"","Text alignment":"Textausrichtung","Text alignment toolbar":"Text-Ausrichtung Toolbar",Tiny:"Winzig",Turquoise:"",Underline:"Unterstrichen",Undo:"Rückgängig","Upload in progress":"Upload läuft",White:"",Yellow:""});n.getPluralForm=function(e){return e!=1}})(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));