.list-img img {
  width: 100%;
  height: 100%;
}

.list-img .img-9-16 {
  width: 150px;
  height: 240px;
  display: block;
  transform: rotate(360deg);
}
.form-group {
  margin-bottom: 0 !important;
}

.icon {
  position: absolute;
  right: 20px;
  top: 5px;
  cursor: pointer;
  color: black;
  font-size: 1rem;
}

.bi-eye {
  padding-right: 10px;
}

::ng-deep .ant-upload.ant-upload-select-picture-card {
  height: 100%;
  width: 100%;
}
::ng-deep .ant-upload-picture-card-wrapper {
  height: 250px;
  max-width: 350px;
}

.input-container {
  min-height: 100px;
  padding: 0 24px;
}
// ::ng-deep .cdk-drag-handle .ant-image-preview-img .ng-tns-c178-9 .ng-star-inserted {
//   height: auto !important;
// }
.container-upload {
  border: 1px solid #ccc;
  padding-top: 35px;
  border-radius: 10px;
  flex-direction: column;
  display: flex;
  gap: 15px;
  align-items: center;
  justify-content: center;
  height: fit-content;
}
.button {
  height: 50px;
}
.container-form-image {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.no-image-content {
  margin-top: 8px;
}
.header-image {
  margin-bottom: 0;
  position: relative;
  left: -145px;
  bottom: -10px;
}
