import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { ICustomerSupport } from '@shared/models/customer-support.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { AbstractDomainService } from './common/abstract.domain.service';

@Injectable({
  providedIn: 'root',
})
export class CustomerSupportService extends AbstractDomainService<ICustomerSupport> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.CUSTOMER_SUPPORT;
}
