<!-- <div *ngIf="this.activeToken.token">
  <p *ngIf="!activeToken.user?.active && !expire24Hour">
    active-user works! check mail send user and password
  </p>
  <p *ngIf="!active && !expire24Hour">
    active-user works! check mail send user and password
  </p>
  <p *ngIf="activeToken.user?.active && !expire24Hour">
    user already is active
  </p>
  <p *ngIf="active && !expire24Hour">user already is active</p>
  <div class="d-block text-center mb-3 mt-4" *ngIf="expire24Hour && !active">
    <button class="btn btn-white" (click)="onReSend()">
      {{ "public.resend" | translate }}
    </button>
  </div>
</div> -->

<main class="bg-login d-flex align-items-center min-vh-100 py-3 py-md-0">
  <app-change-language></app-change-language>
  <div class="wrap-login">
    <div class="login-card">
      <div class="row no-gutters">
        <div class="col-lg-12 col-md-12">
          <div class="card-body">
            <div class="brand-wrapper text-center">
              <strong class="title">{{
                "public.forgotPassword.title" | translate
              }}</strong>
            </div>
            <form [formGroup]="userForgotPasswordForm">
              <div class="input mb-5">
                <div class="form-group">
                  <label
                    >{{ "public.forgotPassword.email" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    trim
                    type="text"
                    class="w-100"
                    formControlName="email"
                    class="form-control"
                    placeholder="{{
                      'public.forgotPassword.placeholder.email' | translate
                    }}"
                    autocomplete="off"
                  />
                </div>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userForgotPasswordForm.get('email')?.errors?.required &&
                    userForgotPasswordForm.get('email')?.touched
                  "
                >
                  {{ "public.forgotPassword.error.required.email" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userForgotPasswordForm.get('email')?.errors?.maxlength &&
                    userForgotPasswordForm.get('email')?.touched
                  "
                >
                  {{
                    "public.forgotPassword.error.maxLength.email"
                      | translate
                        : {
                            param: VALIDATORS.LENGTH.EMAIL_MAX_LENGTH
                          }
                  }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    userForgotPasswordForm.get('email')?.errors?.pattern &&
                    userForgotPasswordForm.get('email')?.touched
                  "
                >
                  {{ "public.forgotPassword.error.pattern.email" | translate }}
                </small>
              </div>

              <button class="btn btn-block login-btn mb-4" (click)="onSend()">
                {{ "common.action.next" | translate }}
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>
