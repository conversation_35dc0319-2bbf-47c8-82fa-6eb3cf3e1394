import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Customer } from '@shared/models/customer.model';
import { CustomerService } from '@shared/services/customer.service';

@Component({
  selector: 'detail-account-transaction',
  templateUrl: './detail-account-transaction.component.html',
  styleUrls: ['./detail-account-transaction.component.scss'],
})
export class DetailAccountTransactionComponent implements OnInit {
  customerInfo: Customer = new Customer();

  constructor(
    private customerService: CustomerService,
    private routerActive: ActivatedRoute
  ) {
    this.routerActive.paramMap.subscribe((res) => {
      const idParam = res.get('customerId');
      if (idParam) {
        this.customerInfo.customerId = +idParam;
      }
    });
  }

  ngOnInit(): void {
    this.getDetail();
  }

  /**
   * get detail customer
   */
  getDetail(): void {
    if (this.customerInfo.customerId) {
      this.customerService.info(this.customerInfo).subscribe((res: any) => {
        this.customerInfo = res.body;
      });
    }
  }

  /**
   * back
   */
  onBack(): void {
    window.history.back();
  }
}
