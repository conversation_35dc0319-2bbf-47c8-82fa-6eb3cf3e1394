<form [formGroup]="formDetail" *ngIf="detailData?.savingAccountId">
  <div class="row">
    <div class="col-md-3">
      <div class="form-group">
        <label>{{ "model.manageSaving.accountNumber" | translate }}</label>
        <input
          type="text"
          class="form-control w-100"
          formControlName="savingAccountNumber"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{ "model.manageSaving.accountOwner" | translate }}</label>
        <input
          type="text"
          class="form-control w-100"
          formControlName="fullname"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{ "model.manageSaving.cif" | translate }}</label>
        <input type="text" class="form-control w-100" formControlName="cif" />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{ "model.manageSaving.gttt" | translate }}</label>
        <input
          type="text"
          class="form-control w-100"
          formControlName="idCardNumber"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{ "model.manageSaving.phoneNumber" | translate }}</label>
        <input
          type="text"
          class="form-control w-100"
          formControlName="phoneNumber"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{ "model.manageSaving.status" | translate }}</label>
        <input
          type="text"
          class="form-control w-100"
          formControlName="status"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{ "model.manageSaving.savingType" | translate }}</label>
        <input type="text" class="form-control w-100" formControlName="type" />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{ "model.manageSaving.money" | translate }}</label>
        <input
          type="text"
          class="form-control w-100 bold-text"
          formControlName="savingAmount"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{
          "model.manageSaving.savingInfo.savingDetail.startTime" | translate
        }}</label>
        <input
          type="text"
          class="form-control w-100"
          formControlName="startTime"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{
          "model.manageSaving.savingInfo.savingDetail.settlementDueTime"
            | translate
        }}</label>
        <input
          type="text"
          class="form-control w-100"
          formControlName="settlementDueTime"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{
          "model.manageSaving.savingInfo.savingDetail.rate" | translate
        }}</label>
        <input
          trim
          type="text"
          class="form-control w-100"
          formControlName="interestRate"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{
          "model.manageSaving.savingInfo.savingDetail.frequencyPayment"
            | translate
        }}</label>
        <input type="text" class="form-control w-100" formControlName="tenor" />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{
          "model.manageSaving.savingInfo.savingDetail.rateCurrent" | translate
        }}</label>
        <input
          type="text"
          class="form-control w-100"
          formControlName="interestAmt"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{
          "model.manageSaving.savingInfo.savingDetail.expectedRate" | translate
        }}</label>
        <input
          type="text"
          class="form-control w-100"
          formControlName="interestAmtEnd"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{
          "model.manageSaving.savingInfo.savingDetail.moneyDueDate" | translate
        }}</label>
        <input
          type="text"
          class="form-control w-100"
          formControlName="amtAfterSettlement"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{
          "model.manageSaving.savingInfo.savingDetail.dueDateType" | translate
        }}</label>
        <input
          type="text"
          class="form-control w-100"
          formControlName="finalizationMethod"
        />
      </div>
    </div>
    <div class="d-block text-center mb-5 mt-4 col-md-12">
      <button class="btn btn-red" data-toggle="modal" (click)="backToList()">
        {{ "common.action.back" | translate }}
      </button>
    </div>
  </div>
</form>
