import { Component, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  Validators,
} from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  CALCULATE_MILLISECONDS_IN_A_YEAR,
  DATE_RANGE_STATISTICAL_LOAN_ONLINE,
  ENTITY_TRANSACTION_STATUS_CONST,
  FILE_EXTENSION,
  MODAL_ACTION,
  MOMENT_CONST,
  PAGINATION,
  TRANSACTION_ENTITY_STATUS,
  TRANSACTION_ENTITY_STATUS_MAP,
  TRANSACTION_TYPE_TRANSFER_MONEY_CONST,
  TRANSACTION_UMONEY_ENTITY_TYPE,
  TRANSACTION_UMONEY_ENTITY_TYPE_MAP,
  TYPE_BANK_CODE_MB_CONST,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { SELECTED_CONST } from '@shared/constants/user.constants';
import { IMerchantTransactionHistory } from '@shared/models/merchant-transaction-history.model';
import { DownloadService } from '@shared/services/helpers/download.service';
import { TransactionService } from '@shared/services/transaction.service';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';
import { ConFigAutoReportComponent } from './config-auto-report/config-auto-report.component';

@Component({
  selector: 'app-transaction-report-umoney',
  templateUrl: './transaction-report-umoney.component.html',
  styleUrls: ['./transaction-report-umoney.component.scss'],
})
export class TransactionReportUmoneyComponent implements OnInit {
  maxDate = new Date();
  maxToDate = new Date();
  transactionReport: IMerchantTransactionHistory[] = [];

  pageSizeOptions = PAGINATION.OPTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  SELECTED_CONST = SELECTED_CONST;
  TRANSACTION_ENTITY_STATUS = TRANSACTION_ENTITY_STATUS;
  TRANSACTION_ENTITY_STATUS_MAP = TRANSACTION_ENTITY_STATUS_MAP;
  ENTITY_TRANSACTION_STATUS_CONST = ENTITY_TRANSACTION_STATUS_CONST;
  TRANSACTION_UMONEY_ENTITY_TYPE = TRANSACTION_UMONEY_ENTITY_TYPE;
  TRANSACTION_UMONEY_ENTITY_TYPE_MAP = TRANSACTION_UMONEY_ENTITY_TYPE_MAP;
  TYPE_BANK_CODE_MB_CONST = TYPE_BANK_CODE_MB_CONST;
  TRANSACTION_TYPE_TRANSFER_MONEY_CONST = TRANSACTION_TYPE_TRANSFER_MONEY_CONST;
  PAGINATION = PAGINATION;
  MOMENT_CONST = MOMENT_CONST;

  isValidMaxDate = (control: FormControl) => {
    const dateOfBirth = new Date(control.value).getTime();
    const maxDateOfBirth = new Date().getTime();
    return dateOfBirth > maxDateOfBirth ? { invalidMaxDate: true } : null;
  };

  formTransactionReportSearch = this.fb.group({
    keyword: '',
    fromDate: [null, [Validators.required]],
    toDate: [null, [Validators.required, this.isValidMaxDate]],
    transferTypes: [],
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    hasPageable: true,
    transactionStatus: [],
    timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
  });
  constructor(
    private fb: FormBuilder,
    private translateService: TranslateService,
    private downloadService: DownloadService,
    private transactionService: TransactionService,
    private modal: NgbModal
  ) {}

  ngOnInit(): void {
    const startDate = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDate = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.formTransactionReportSearch.controls.fromDate.setValue(
      CommonUtils.reverseDate(startDate)
    );
    this.formTransactionReportSearch.controls.toDate.setValue(
      CommonUtils.reverseDate(endDate)
    );
    this.onSearch();
  }

  onSearchSubmit() {
    this.formTransactionReportSearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.formTransactionReportSearch.controls.length.setValue(
      PAGINATION.LENGTH_DEFAULT
    );
    this.onSearch();
  }

  onSelectAll(type: string) {
    const selectTransactionTypeUmoney = this.TRANSACTION_UMONEY_ENTITY_TYPE.map(
      (item) => item.code
    );
    const selectTransactionTypeStatus = this.TRANSACTION_ENTITY_STATUS.map(
      (item) => item.code
    );
    switch (type) {
      case this.SELECTED_CONST.TRANSACTION_TYPE:
        this.formTransactionReportSearch
          .get('transferTypes')
          ?.patchValue(selectTransactionTypeUmoney);
        break;
      case this.SELECTED_CONST.TRANSACTION_STATUS:
        this.formTransactionReportSearch
          .get('transactionStatus')
          ?.patchValue(selectTransactionTypeStatus);
        break;
    }
  }

  onClearAll(type: string) {
    switch (type) {
      case this.SELECTED_CONST.TRANSACTION_TYPE:
        this.formTransactionReportSearch.get('transferTypes')?.patchValue([]);
        break;
      case this.SELECTED_CONST.TRANSACTION_STATUS:
        this.formTransactionReportSearch
          .get('transactionStatus')
          ?.patchValue([]);
        break;
    }
  }

  dateValidator(value: number): any {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return value > DATE_RANGE_STATISTICAL_LOAN_ONLINE
        ? { invalidDate: true }
        : null;
    };
  }

  changeValidDate() {
    if (
      this.formTransactionReportSearch.controls.fromDate.value &&
      this.formTransactionReportSearch.controls.toDate.value
    ) {
      if (this.formTransactionReportSearch.controls['toDate'].value) {
        this.maxDate =
          this.formTransactionReportSearch.controls['toDate'].value;
      }
      const startDate = CommonUtils.setStartTime(
        new Date(
          this.formTransactionReportSearch.controls.fromDate.value
        ).getTime()
      );
      const endDate = CommonUtils.setStartTime(
        new Date(
          this.formTransactionReportSearch.controls.toDate.value
        ).getTime()
      );
      const dayMax =
        (endDate - startDate) / CALCULATE_MILLISECONDS_IN_A_YEAR.DAY;
      if (dayMax > DATE_RANGE_STATISTICAL_LOAN_ONLINE) {
        this.formTransactionReportSearch.controls.fromDate.setValidators([
          Validators.required,
          this.dateValidator(dayMax),
          this.isValidMaxDate,
        ]);
        this.formTransactionReportSearch.controls.fromDate.updateValueAndValidity();
      } else {
        this.formTransactionReportSearch.controls.fromDate.clearValidators();
        this.formTransactionReportSearch.controls.fromDate.setValidators([
          Validators.required,
          this.isValidMaxDate,
        ]);
        this.formTransactionReportSearch.controls.fromDate.updateValueAndValidity();
      }
    }
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formTransactionReportSearch.value.pageIndex,
      this.formTransactionReportSearch.value.pageSize
    );
  }

  onSearch() {
    if (this.formTransactionReportSearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formTransactionReportSearch);
      return;
    }
    this.formTransactionReportSearch.controls.hasPageable.setValue(true);
    this.transactionService
      .searchUmoney(this.formTransactionReportSearch.value)
      .subscribe((res: any): void => {
        this.transactionReport = res.body.content;
        this.formTransactionReportSearch.controls.length.setValue(
          res.body.totalElements
        );
      });
  }

  onReset() {
    const startDateReset = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDateReset = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.formTransactionReportSearch.controls.keyword.reset();
    this.formTransactionReportSearch.controls.fromDate.setValue(
      CommonUtils.reverseDate(startDateReset)
    );
    this.formTransactionReportSearch.controls.toDate.setValue(
      CommonUtils.reverseDate(endDateReset)
    );
    this.formTransactionReportSearch.controls.fromDate.clearValidators();
    this.formTransactionReportSearch.controls.fromDate.setValidators([
      Validators.required,
    ]);
    this.formTransactionReportSearch.controls.fromDate.updateValueAndValidity();
    this.formTransactionReportSearch.controls.transferTypes.setValue(null);
    this.formTransactionReportSearch.controls.transactionStatus.setValue(null);
    this.formTransactionReportSearch
      .get('timeZoneStr')
      ?.setValue(Intl.DateTimeFormat().resolvedOptions().timeZone);
  }

  onChangePage(page: PageEvent) {
    this.formTransactionReportSearch.controls.pageIndex.setValue(
      page.pageIndex
    );
    this.formTransactionReportSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  exportFile() {
    const bodySearch = this.formTransactionReportSearch.getRawValue();
    bodySearch.hasPageable = false;
    if (this.formTransactionReportSearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formTransactionReportSearch);
    } else {
      // get file name
      const fileName = this.translateService.instant(
        'template.transactionReportUmoney'
      );
      const obFile = this.transactionService.exportUmoney({
        fromDate: bodySearch.fromDate,
        toDate: bodySearch.toDate,
        keyword: bodySearch.keyword,
        transferTypes: bodySearch.transferTypes,
        transactionStatus: bodySearch.transactionStatus,
        timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
      });

      this.downloadService.downloadFileWithObservableAndName(
        obFile,
        fileName,
        FILE_EXTENSION.XLSX
      );
    }
  }

  onConfigAutoReport() {
    const modalRef = this.modal.open(ConFigAutoReportComponent, {
      size: 'lg',
      keyboard: true,
      backdrop: 'static',
      centered: true,
    });
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;
    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }
}
