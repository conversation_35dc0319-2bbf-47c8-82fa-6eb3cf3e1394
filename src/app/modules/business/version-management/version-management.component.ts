import { Component, OnInit } from '@angular/core';
import {
  TYPE_OPERATE_SYSTEM,
  tabVersionActive,
} from '@shared/constants/app.constants';

@Component({
  selector: 'app-version-management',
  templateUrl: './version-management.component.html',
  styleUrls: ['./version-management.component.scss'],
})
export class VersionManagementComponent implements OnInit {
  isTabAndroid = true;

  TYPE_OPERATE_SYSTEM = TYPE_OPERATE_SYSTEM;
  constructor() {}

  ngOnInit(): void {}

  selectTab(event: any): void {
    if (event === tabVersionActive.ANDROID) {
      this.isTabAndroid = true;
    } else {
      this.isTabAndroid = false;
    }
  }
}
