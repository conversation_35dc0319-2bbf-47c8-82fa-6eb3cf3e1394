import { Directive, Input, TemplateRef, ViewContainerRef } from '@angular/core';
import { AuthenticationService } from '@shared/services/auth/authentication.service';

/**
 * Check exc roles
 *
 * <AUTHOR>
 * @date 2021-12-24
 * @export ExcludeRolesDirective
 * @class ExcludeRolesDirective
 * @howToUse
 * ```
 *     <some-element *appExcludeRoles="'USER_READ'">...</some-element>
 *
 *     <some-element *appExcludeRoles="['USER_READ', 'USER_CREATE']">...</some-element>
 * ```
 */
@Directive({
  selector: '[appExcludeRoles]',
})
export class ExcludeRolesDirective {
  private authorities: string | string[] = [];

  constructor(
    private authenticationService: AuthenticationService,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    private templateRef: TemplateRef<any>,
    private viewContainerRef: ViewContainerRef,
  ) {}

  @Input() set appExcludeRoles(value: string | string[]) {
    this.authorities = value;
    this.updateView();
    // Get notified each time authentication state changes.
    this.authenticationService
      .getAuthenticationState()
      .subscribe(() => this.updateView());
  }

  private updateView(): void {
    const hasAnyAuthority = this.authenticationService.hasAnyPrivileges(
      this.authorities,
    );
    this.viewContainerRef.clear();
    if (!hasAnyAuthority) {
      this.viewContainerRef.createEmbeddedView(this.templateRef);
    }
  }
}
