<div class="modal-content">
  <div class="modal-header">
    <h5 class="modal-title">{{ title | translate }}</h5>
    <button
      type="button"
      class="close"
      data-dismiss="modal"
      aria-label="Close"
      (click)="activeModal.close(MODAL_ACTION.CLOSE.code)"
    >
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body" [formGroup]="formFeeType">
    <div class="col-12 text-center">
      <div>
        <span>
          <mat-radio-group formControlName="configurationFeeType">
            <mat-radio-button
              *ngFor="
                let configurationFeeType of CONFIGURATION_FEE_TYPE_RADIO;
                let i = index
              "
              class="mr-2"
              [value]="configurationFeeType.value"
            >
              {{ configurationFeeType.label | translate }}
            </mat-radio-button>
          </mat-radio-group>
        </span>
      </div>
    </div>
  </div>
  <div class="text-right pb-4">
    <button
      type="button"
      class="btn btn-white-gray"
      (click)="activeModal.close(MODAL_ACTION.CANCEL.code)"
    >
      {{ MODAL_ACTION.CANCEL.label | translate }}
    </button>

    <button
      type="button"
      class="btn btn-danger mr-3 ml-2"
      (click)="
        activeModal.close(formFeeType.get('configurationFeeType')?.value)
      "
    >
      {{ action.label | translate }}
    </button>
  </div>
</div>
