<div class="modal-content">
  <!-- <div class="modal-header" [ngClass]="isHiddenBtnClose ? 'border-0' : ''">
    <h3 class="modal-title">{{title | translate}}</h3>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"
      (click)="activeModal.close(MODAL_ACTION.CLOSE.code)">
      <span aria-hidden="true">&times;</span>
    </button>
  </div> -->
  <div class="modal-header" [ngClass]="isHiddenBtnClose ? 'border-0' : ''">
    <h5 class="modal-title">{{ title | translate }}</h5>
    <button
      type="button"
      class="close"
      data-dismiss="modal"
      aria-label="Close"
      (click)="activeModal.close(MODAL_ACTION.CLOSE.code)"
    >
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="col-12">
      <!-- <span
        class="preventLongTextFix"
        [innerHTML]="showContent() | translate"
      ></span> -->
      <div class="form-group">
        <label>{{ "event.usernameOrPhoneNumber" | translate }}</label>
        <ng-select
          [multiple]="true"
          [items]="customers"
          appDebounceKeyup
          [searchFn]="customSearchFn"
          [searchWhileComposing]="false"
          (keyupDelay)="loadListCustomer($event)"
          bindLabel="fullname"
          bindValue="customerId"
          [(ngModel)]="item"
          (add)="getItem($event)"
          (remove)="removeItem($event)"
          placeholder="{{ 'common.action.searchKeyword' | placeholder }}"
        >
        </ng-select>
      </div>
    </div>
  </div>
  <div class="text-center pb-4">
    <button
      type="button"
      class="btn mb-btn-outline-color"
      (click)="activeModal.close(MODAL_ACTION.CANCEL.code)"
    >
      {{ MODAL_ACTION.CANCEL.label | translate }}
    </button>
    <button
      type="button"
      [hidden]="isHiddenBtnClose"
      class="btn mb-btn-color ml-3"
      (click)="activeModal.close(selectedValue)"
    >
      {{ action.label | translate }}
    </button>
  </div>
</div>
