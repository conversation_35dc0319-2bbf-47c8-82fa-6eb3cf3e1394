<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formUserSearch" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-md-4 col-lg-2">
            <div class="form-group">
              <label>{{ "common.action.searchKeyword" | translate }}</label>
              <input
                type="text"
                formControlName="keyword"
                trim
                class="w-100"
                class="form-control"
                placeholder="{{ 'common.action.searchKeyword' | placeholder }}"
              />
            </div>
          </div>
          <div class="col-md-4 col-lg-2">
            <div class="form-group">
              <label>{{ "common.status" | translate }}</label>
              <ng-select
                placeholder="{{ 'common.appSelectOption.status' | translate }}"
                [searchable]="false"
                formControlName="status"
                [clearable]="true"
              >
                <ng-option
                  [value]="item.code"
                  *ngFor="let item of ENTITY_STATUS_USER"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-4 col-lg-2">
            <div class="form-group">
              <label>{{ "model.user.department" | translate }}</label>
              <ng-select
                [multiple]="true"
                appearance="outline"
                [searchable]="true"
                [clearable]="true"
                class="example"
                formControlName="departmentIds"
                placeholder="{{
                  'model.user.department' | placeholder : 'select'
                }}"
              >
                <ng-option
                  [value]="item.departmentId"
                  *ngFor="let item of departments"
                >
                  {{ item.departmentName }}
                </ng-option>
                <ng-template ng-header-tmp>
                  <div>
                    <button
                      class="btn btn-link"
                      type="button"
                      (click)="onSelectAll(SELECTED_CONST.DEPARTMENT)"
                    >
                      {{ "common.action.selectAll" | translate }}
                    </button>
                    <button
                      class="btn btn-link"
                      type="button"
                      (click)="onClearAll(SELECTED_CONST.DEPARTMENT)"
                    >
                      {{ "common.action.clearAll" | translate }}
                    </button>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="col-md-4 col-lg-2">
            <div class="form-group">
              <label>{{ "model.user.position" | translate }}</label>
              <ng-select
                [multiple]="true"
                appearance="outline"
                [searchable]="true"
                [clearable]="true"
                formControlName="positionIds"
                placeholder="{{
                  'model.user.position' | placeholder : 'select'
                }}"
              >
                <ng-option
                  [value]="item.positionId"
                  *ngFor="let item of positions"
                >
                  {{ item.positionName }}
                </ng-option>
                <ng-template ng-header-tmp>
                  <div>
                    <button
                      class="btn btn-link"
                      type="button"
                      (click)="onSelectAll(SELECTED_CONST.POSITION)"
                    >
                      {{ "common.action.selectAll" | translate }}
                    </button>
                    <button
                      class="btn btn-link"
                      type="button"
                      (click)="onClearAll(SELECTED_CONST.POSITION)"
                    >
                      {{ "common.action.clearAll" | translate }}
                    </button>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="col-md-4 col-lg-2">
            <div class="form-group">
              <label
                >{{ "model.user.role" | translate
                }}<span class="text-danger">*</span></label
              >
              <ng-select
                [multiple]="true"
                appearance="outline"
                [searchable]="true"
                [clearable]="true"
                formControlName="roleIds"
                placeholder="{{ 'model.user.role' | placeholder : 'select' }}"
              >
                <ng-option [value]="role.roleId" *ngFor="let role of roles">
                  {{ role.name }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-2 mt-4 d-flex col-lg-2">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
            >
              <div class="btn-reset">
                <i
                  class="bi bi-arrow-clockwise"
                  type="button"
                  (click)="onReset()"
                >
                </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" type="submit">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-3 mt-4">
          <button
            type="button"
            class="btn btn-red"
            [routerLink]="[ROUTER_UTILS.user.create]"
            [routerLinkActive]="['active']"
            *hasPrivileges="SYSTEM_RULES.USER_CREATE"
          >
            {{ "common.action.create" | translate }}
          </button>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th class="text-center" scope="col">
                {{ "common.no" | translate }}
              </th>
              <th scope="col">{{ "model.user.fullname" | translate }}</th>
              <th scope="col">
                {{ "model.user.phoneNumber" | translate }}
              </th>
              <th scope="col">
                {{ "model.user.username" | translate }}
              </th>
              <th scope="col">
                {{ "model.user.email" | translate }}
              </th>
              <th scope="col">
                {{ "model.user.department" | translate }}
              </th>
              <th scope="col">
                {{ "model.user.position" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "model.user.role" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.status" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of users; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td>
                <span title="{{ dataItem.fullname }}">{{
                  dataItem.fullname | limitWord
                }}</span>
              </td>
              <td>{{ dataItem.phoneNumber }}</td>
              <td>{{ dataItem.username }}</td>
              <td>
                <span title="{{ dataItem.email }}">{{
                  dataItem.email | limitWord
                }}</span>
              </td>
              <td>
                <span title="{{ dataItem?.departmentName }}">{{
                  dataItem?.departmentName | limitWord
                }}</span>
              </td>
              <td>
                <span title="{{ dataItem?.positionName }}">{{
                  dataItem?.positionName | limitWord
                }}</span>
              </td>
              <td>
                <span title="{{ dataItem?.roleName }}">{{
                  dataItem?.roleName | limitWord
                }}</span>
              </td>
              <!-- <td class="text-center">
                <span
                  class="badge"
                  [ngClass]="
                    ENTITY_ACTIVE_USER_STATUS_MAP[
                      dataItem.active === true ? 1 : 0
                    ].style
                  "
                  >{{
                    ENTITY_ACTIVE_USER_STATUS_MAP[
                      dataItem.active === true ? 1 : 0
                    ].label | translate
                  }}</span
                >
              </td> -->
              <td
                class="text-center"
                *ngIf="
                  !(
                    dataItem.active === false &&
                    dataItem.status !== ENTITY_STATUS_CONST.ACTIVE.code
                  )
                "
              >
                <!-- {{ ENTITY_STATUS_MAP[dataItem.status || 0] | translate }} -->
                <span
                  class="badge"
                  [ngClass]="ENTITY_STATUS_MAP[dataItem.status || 0].style"
                  >{{
                    ENTITY_STATUS_MAP[dataItem.status || 0].label | translate
                  }}</span
                >
              </td>
              <td
                class="text-center"
                *ngIf="
                  dataItem.active === false &&
                  dataItem.status !== ENTITY_STATUS_CONST.ACTIVE.code
                "
              >
                <span class="badge" [ngClass]="'bg-secondary'">{{
                  "common.notActivated" | translate
                }}</span>
              </td>

              <td class="text-left">
                <!-- <button
                  *ngIf="
                    dataItem.actionToken ===
                    ACTION_TOKEN_CONST.RESEND_LINK_ACTIVATE
                  "
                  class="btn px-1 py-0"
                  (click)="resend(dataItem)"
                > -->

                <button
                  ngbTooltip="{{ 'common.action.detail' | translate }}"
                  class="btn px-1 py-0"
                  (click)="detail(dataItem.userId)"
                >
                  <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                </button>
                <button
                  ngbTooltip="{{ 'common.action.update' | translate }}"
                  class="btn px-1 py-0"
                  (click)="edit(dataItem.userId)"
                  data-toggle="modal"
                  *hasPrivileges="SYSTEM_RULES.USER_WRITE"
                >
                  <i class="fa fa-edit mb-color" aria-hidden="true"></i>
                </button>
                <ng-container *hasPrivileges="SYSTEM_RULES.USER_LOCK">
                  <button
                    [ngbTooltip]="
                      (dataItem.status === ENTITY_STATUS_CONST.ACTIVE.code
                        ? 'common.action.lock'
                        : 'common.action.unlock'
                      ) | translate
                    "
                    class="btn px-1 py-0"
                    (click)="lockAndUnlock(dataItem)"
                    *ngIf="
                      !(
                        dataItem.actionToken ===
                        ACTION_TOKEN_CONST.RESEND_LINK_ACTIVATE
                      )
                    "
                  >
                    <i
                      [className]="
                        dataItem.status === ENTITY_STATUS_CONST.ACTIVE.code
                          ? 'fa fa-lock mb-color'
                          : 'fa fa-unlock mb-color'
                      "
                      aria-hidden="true"
                    ></i>
                  </button>
                </ng-container>
                <!--*ngIf="dataItem.approvalStatus === 1 && (dataItem.approvalType === 1 || dataItem.approvalType === 2)"-->
                <ng-container *hasPrivileges="SYSTEM_RULES.USER_RESET_PW">
                  <button
                    *ngIf="
                      !(
                        dataItem.actionToken ===
                        ACTION_TOKEN_CONST.RESEND_LINK_ACTIVATE
                      ) && dataItem.status === ENTITY_STATUS_CONST.ACTIVE.code
                    "
                    ngbTooltip="{{
                      'common.action.changePassword' | translate
                    }}"
                    class="btn px-1 py-0"
                    (click)="resetPassword(dataItem)"
                  >
                    <i class="bi bi-key mb-color" aria-hidden="true"></i>
                  </button>
                </ng-container>
                <ng-container *hasPrivileges="SYSTEM_RULES.USER_RESEND_LINK">
                  <button
                    ngbTooltip="{{ 'public.resend' | translate }}"
                    class="btn px-1 py-0"
                    *ngIf="
                      dataItem.actionToken ===
                        ACTION_TOKEN_CONST.RESEND_LINK_ACTIVATE &&
                      dataItem.status === ENTITY_STATUS_CONST.INACTIVE.code
                    "
                    (click)="resend(dataItem)"
                  >
                    <i
                      class="fa fa-paper-plane mb-color"
                      aria-hidden="true"
                    ></i>
                  </button>
                </ng-container>
                <ng-container *hasPrivileges="SYSTEM_RULES.USER_DELETE">
                  <button
                    ngbTooltip="{{ 'common.action.delete' | translate }}"
                    class="btn px-1 py-0"
                    (click)="delete(dataItem)"
                  >
                    <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                  </button>
                </ng-container>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="row d-block text-center m-0" *ngIf="users?.length === 0">
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
        <div *ngIf="users.length">
          <mat-paginator
            [length]="formUserSearch.value.length"
            [pageSize]="formUserSearch.value.pageSize"
            [pageIndex]="formUserSearch.value.pageIndex"
            [pageSizeOptions]="pageSizeOptions"
            (page)="onChangePage($event)"
            aria-label="Select page"
          >
          </mat-paginator>
        </div>
      </div>
    </div>
  </div>
</section>
