import { Directive, HostListener } from '@angular/core';
import { NgControl } from '@angular/forms';
import { VALIDATORS } from '@shared/constants/validators.constant';

@Directive({
  selector: '[appNumberInputDecimal]',
})
export class NumberInputDecimalDirective {
  constructor(private ngControl: NgControl) {}

  @HostListener('input', ['$event'])
  onInputChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    let value = input.value;

    // Chỉ cho phép số và dấu chấm
    value = value.replace(/[^0-9.]/g, '');

    // Nếu bắt đầu bằng dấu chấm → thêm 0 phía trước
    if (value.startsWith('.')) {
      value = '0' + value;
    }

    // Chỉ cho phép 1 dấu chấm duy nhất
    const firstDotIndex = value.indexOf('.');
    if (firstDotIndex !== -1) {
      const beforeDot = value.substring(0, firstDotIndex + 1);
      const afterDot = value.substring(firstDotIndex + 1).replace(/\./g, '');
      value = beforeDot + afterDot;
    }

    // Tách phần nguyên và phần thập phân
    let integerPart = value;
    let decimalPart = '';
    const dotIndex = value.indexOf('.');
    if (dotIndex >= 0) {
      integerPart = value.substring(0, dotIndex);
      decimalPart = value.substring(dotIndex + 1, dotIndex + 3); // Giới hạn 2 chữ số
    }

    // Giữ số 0 đầu tiên nếu đang nhập 0.x
    if (!(value.startsWith('0.') || value === '0')) {
      integerPart = integerPart.replace(/^0+/, '') || '0';
    }
    // Thêm dấu phẩy vào phần nguyên (format hàng nghìn)
    integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    // Gộp lại
    if (dotIndex >= 0 && decimalPart === '') {
      value = `${integerPart}.`; // Giữ dấu chấm nếu chưa nhập phần thập phân
    } else {
      value = decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
    }

    this.ngControl.control?.setValue(value);
  }
}
