import { Component, HostListener, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { SafeUrl } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import {
  CONFIGURATION_FEE_TYPE_CONST,
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  GENDER,
  MASTER_MERCHANT_SERVICE,
  MASTER_MERCHANT_SERVICE_CONST,
  MASTER_MERCHANT_SERVICE_ENUM,
  MASTER_MERCHANT_TYPE_BILLING,
  MASTER_MERCHANT_TYPE_BILLING_CONST,
  MASTER_MERCHANT_TYPE_TOPUP,
  MASTER_MERCHANT_TYPE_TOPUP_CONST,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ICurrency } from '@shared/models/currency.model';
import { IMerchantTransactionHistory } from '@shared/models/merchant-transaction-history.model';
import { IMerchant, IMerchantCode } from '@shared/models/merchant.model';
import { CurrencyService } from '@shared/services/currency.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { MerchantService } from '@shared/services/merchant.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-create-update-master-merchant',
  templateUrl: './create-update-master-merchant.component.html',
  styleUrls: ['./create-update-master-merchant.component.scss'],
})
export class CreateUpdateMasterMerchantComponent implements OnInit, OnDestroy {
  formMerchant: FormGroup = new FormGroup({});

  // input call api
  merchantDto: IMerchant = {};
  // input data form control
  merchant: IMerchant = {};
  // master Merchant
  masterMerchant: IMerchant[] = [];
  // master Merchant
  merchantHistory: IMerchantTransactionHistory[] = [];
  currencies: ICurrency[] = [];

  merchantCode: IMerchantCode[] = [];
  action = '';
  currentValue = '';
  merchantAccountNumber = '';
  qrCode = '';
  qrCodeDownloadLink: SafeUrl = '';
  hasFilter = false;
  isUpdate = false;
  isOther = false;
  maxDateOfBirth = new Date();
  feeId: number | undefined;
  DISCOUNT_CONFIGURATION_TYPE = '';
  

  pageSize = PAGINATION.PAGE_SIZE_DEFAULT;
  pageIndex = PAGINATION.PAGE_NUMBER_DEFAULT;
  ROUTER_UTILS = ROUTER_UTILS;
  ENTITY_STATUS = ENTITY_STATUS;
  VALIDATORS = VALIDATORS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  GENDER_LIST = GENDER;
  SYSTEM_RULES = SYSTEM_RULES;
  MASTER_MERCHANT_SERVICE_CONST = MASTER_MERCHANT_SERVICE_CONST;
  MASTER_MERCHANT_SERVICE_ENUM = MASTER_MERCHANT_SERVICE_ENUM;
  MASTER_MERCHANT_SERVICE = MASTER_MERCHANT_SERVICE;
  MASTER_MERCHANT_TYPE_TOPUP_CONST = MASTER_MERCHANT_TYPE_TOPUP_CONST;
  MASTER_MERCHANT_TYPE_TOPUP = MASTER_MERCHANT_TYPE_TOPUP;
  MASTER_MERCHANT_TYPE_BILLING_CONST = MASTER_MERCHANT_TYPE_BILLING_CONST;
  MASTER_MERCHANT_TYPE_BILLING = MASTER_MERCHANT_TYPE_BILLING;
 

  // default form search
  formMerchantHistorySearch = this.fb.group({
    // merchantName: '',
    // shortName: '',
    // MerchantCode: '',
    keyword: '',
    status: null,
  });

  constructor(
    private fb: FormBuilder,
    private formBuilder: FormBuilder,
    private merchantService: MerchantService,
    private toastService: ToastrCustomService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private currencyService: CurrencyService
  ) {
    this.activatedRoute.data.subscribe((res) => {
      this.action = res.action;
    });
    // get id form paramater
    this.activatedRoute.paramMap.subscribe((res) => {
      const idParam = res.get('merchantId');
      if (idParam) {
        this.merchantDto.merchantId = +idParam;
        this.isUpdate = true;
      }
    });
  }

  /**
   * remove storage
   */
  ngOnDestroy(): void {
    if (!this.hasFilter) {
      sessionStorage.removeItem(STORAGE_APP.MASTER_MERCHANT);
    }
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.hasFilter = true;
  }

  ngOnInit(): void {
    this.DISCOUNT_CONFIGURATION_TYPE = CONFIGURATION_FEE_TYPE_CONST.DISCOUNT.code + '';
    // this.getCurrency();
    if (this.isUpdate) {
      this.getDetail();
      // this.mappingRoles(this.roles);
    } else {
      this.initForm();
    }
  }

  /**
   * init form
   *
   * @param merchant User
   */
  initForm(merchant?: IMerchant): void {
    this.formMerchant = this.formBuilder.group({
      serviceType: [merchant?.serviceType || null, [Validators.required]],
      merchantCode: [
        merchant?.merchantCode || null,
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.MERCHANT_CODE_NAME),
        ],
      ],
      merchantName: [
        merchant?.merchantName || '',

        [
          Validators.required,
          Validators.maxLength(VALIDATORS.LENGTH.MERCHANT_NAME_MAX_LENGTH),
        ],
      ],
      merchantAccountNumber: [
        merchant?.merchantAccountNumber || '',

        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.MERCHANT_NUMBER),
          Validators.maxLength(VALIDATORS.LENGTH.ACCOUNT_NUMBER_MAX_LENGTH),
        ],
      ],
      status: [merchant?.status || ENTITY_STATUS_CONST.ACTIVE.code],
      merchantId: [merchant?.merchantId || null],
      description: [
        merchant?.description || '',
        [Validators.maxLength(VALIDATORS.LENGTH.MEDIUM_TEXT_MAX_LENGTH)],
      ],
      // currency: [merchant?.currency || null, [Validators.required]],
      cif: [
        merchant?.cif || null,
        [Validators.required, Validators.pattern(VALIDATORS.PATTERN.NUMBER)],
      ],
      phoneNumber: [merchant?.phoneNumber || null],
    });
    this.formMerchant.get('phoneNumber')?.disable();
    if (!this.merchantDto.merchantId) {
      this.formMerchant.get('merchantCode')?.disable();
      this.formMerchant.get('merchantName')?.disable();
      this.formMerchant.get('merchantAccountNumber')?.disable();
      this.formMerchant.get('description')?.disable();
    }
    if (
      merchant?.serviceType === MASTER_MERCHANT_SERVICE_ENUM.BILLING ||
      merchant?.serviceType === MASTER_MERCHANT_SERVICE_ENUM.TOPUP
    ) {
      this.isOther = false;
      if (merchant?.serviceType === MASTER_MERCHANT_SERVICE_ENUM.BILLING) {
        const data = { serviceType: MASTER_MERCHANT_SERVICE_ENUM.BILLING };
        this.merchantService.getMerchantCode(data).subscribe((res: any) => {
          this.merchantCode = res.body;
        });
      }
      if (merchant?.serviceType === MASTER_MERCHANT_SERVICE_ENUM.TOPUP) {
        const data = { serviceType: MASTER_MERCHANT_SERVICE_ENUM.TOPUP };
        this.merchantService.getMerchantCode(data).subscribe((res: any) => {
          this.merchantCode = res.body;
        });
      }
    }
    if (merchant?.serviceType === MASTER_MERCHANT_SERVICE_ENUM.OTHER) {
      this.isOther = true;
    }
  }

  /**
   * back to monitor list
   */
  backToList(): void {
    this.hasFilter = true;
    this.router.navigate([ROUTER_UTILS.merchant.masterMerchant.root]);
  }

  /**
   * get detail merchant
   */
  getDetail(): void {
    if (this.merchantDto.merchantId) {
      this.merchantService.detail(this.merchantDto).subscribe((res: any) => {
        this.merchant = res.body;
        this.feeId = this.merchant.feeId;

        const data = res.body as IMerchant;
        if (data.qrCode?.qrCodeValue) {
          this.qrCode = data.qrCode?.qrCodeValue;
        }

        this.initForm(data);
      });
    }
  }

  getMerchantCode(event: any) {
    this.formMerchant.enable();
    if (event === this.MASTER_MERCHANT_SERVICE_ENUM.TOPUP) {
      this.isOther = false;
      this.formMerchant.get('merchantCode')?.reset();
      const data = { serviceType: this.MASTER_MERCHANT_SERVICE_ENUM.TOPUP };
      this.merchantService.getMerchantCode(data).subscribe((res: any) => {
        this.merchantCode = res.body;
      });
    }
    if (event === this.MASTER_MERCHANT_SERVICE_ENUM.BILLING) {
      this.isOther = false;

      this.formMerchant.get('merchantCode')?.reset();
      const data = { serviceType: this.MASTER_MERCHANT_SERVICE_ENUM.BILLING };
      this.merchantService.getMerchantCode(data).subscribe((res: any) => {
        this.merchantCode = res.body;
      });
    }
    if (event === this.MASTER_MERCHANT_SERVICE_ENUM.OTHER) {
      this.isOther = true;
      if (!this.isUpdate) {
        this.formMerchant.get('merchantCode')?.setValidators([]);
        this.formMerchant.get('merchantCode')?.updateValueAndValidity();
        this.merchantCode = [];
      } else {
        this.formMerchant
          .get('merchantCode')
          ?.setValue(this.merchant?.merchantCode || '');
      }
    } else {
      this.formMerchant
        .get('merchantCode')
        ?.setValidators([
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.MERCHANT_CODE_NAME),
        ]);
      this.formMerchant.get('merchantCode')?.updateValueAndValidity();
    }
    this.formMerchant.get('phoneNumber')?.disable();
  }

  /**
   * index on list
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(index, this.pageIndex, this.pageSize);
  }

  // /**
  //  * get Transaction History
  //  *
  //  * @param merchantId number
  //  */
  // getTransactionHistory(merchantId: number): void {
  //   const params = {
  //     merchantId,
  //   };
  //   this.merchantService
  //     .transactionHistory(params)
  //     .subscribe((res: any): void => {
  //       this.merchantHistory = res.body.content;
  //     });
  // }

  /**
   * onChange Get Merchant Account Number
   *
   * @param value any
   */
  onChangeGetMerchantAccountNumber(value: any): void {
    this.masterMerchant.forEach((element) => {
      if (element.merchantId === value) {
        this.formMerchant.controls.merchantAccountNumber.setValue(
          element.merchantAccountNumber
        );
      }
    });
  }

  /**
   * Create: check validate file and data input if valid then call api create
   */
  onCreate() {
    // touched form
    if (this.formMerchant.invalid) {
      CommonUtils.markFormGroupTouched(this.formMerchant);
      return;
    }
    const data = this.formMerchant.getRawValue();
    //  set data files = map fileUploads
    // data.files = this.fileUploads.map((fileX) => fileX.file);
    if (this.formMerchant.valid) {
      this.merchantService.createMaster(data).subscribe((res): void => {
        this.router.navigate([ROUTER_UTILS.merchant.masterMerchant.root]);
        this.toastService.success('common.action.createSuccess');
      });
    }
  }

  /**
   * Update: check validate file and data input if valid then call api update
   */
  onUpdate() {
    if (this.formMerchant.invalid) {
      CommonUtils.markFormGroupTouched(this.formMerchant);
      return;
    }
    const data = this.formMerchant.getRawValue();
    //  set data files = map fileUploads
    // data.files = this.fileUploads.map((fileX) => fileX.file);
    // data.merchantId = this.merchant?.merchantId;
    if (this.formMerchant.valid) {
      this.merchantService.updateMaster(data).subscribe((res): void => {
        this.router.navigate([ROUTER_UTILS.merchant.masterMerchant.root]);
        this.toastService.success('common.action.updateSuccess');
      });
    }
  }

  getCurrency() {
    this.currencyService
      .search({ hasPageable: false })
      .subscribe((res: any) => {
        if (res.body.content.length) {
          this.currencies = res.body.content;
        }
      });
  }
}
