import { Component, HostListener, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  ENTITY_STATUS,
  EVENT_STATUS,
  EVENT_TYPE,
  FILE_EXTENSION,
  FILE_UPLOAD_EXTENSIONS,
  MODAL_ACTION,
  MOMENT_CONST,
  PAGINATION,
  RECEIVER_TYPE,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IAnnouncementType } from '@shared/models/annoucement-type.model';
import { ICustomer } from '@shared/models/customer.model';
import { IEvent } from '@shared/models/event.model';
import { IAnnouncementTypeSearch } from '@shared/models/request/announcement-type.search';
import { ICustomerSearch } from '@shared/models/request/customer.search';
import { IFileEntrySearch } from '@shared/models/request/file-entry.search';
import { AnnouncementTypeService } from '@shared/services/announcement-type.service';
import { CustomerService } from '@shared/services/customer.service';
import { EventService } from '@shared/services/event.service';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import CommonUtils from '@shared/utils/common-utils';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils/router.utils';
import * as moment from 'moment';
import { ImportCustomerNotificationComponent } from '../modal-import/modal-import-notification.component';

@Component({
  selector: 'app-notification-create-update',
  templateUrl: './notification-create-update.component.html',
  styleUrls: ['./notification-create-update.component.scss'],
})
export class NotificationCreateUpdateComponent implements OnInit {
  customers: ICustomer[] = [];
  customerIds: any[] = [];

  isMaxLengthContent = false;

  maxLengthContent = VALIDATORS.LENGTH.CONTENT_NOTIFICATION_MAX;

  customerSearch: ICustomerSearch = {};

  minDate = new Date();

  // form control data
  formEvent: FormGroup = new FormGroup({});

  reader: FileReader = new FileReader();

  announcementSearch: IAnnouncementTypeSearch = {};

  event: IEvent = {};
  eventBackup: IEvent = {};
  announcementTypes: IAnnouncementType[] = [];
  fileUploads: any[] = [];
  fileRequired: string[] = ['Image'];
  fileDelete: any[] = [];
  imageUrls: any[] = [];

  eventId: number | undefined;

  hasFilter = false;
  isUpdate = false;
  isBackup = false;

  isWaitingOrSend = false;

  pageSize = PAGINATION.PAGE_SIZE_DEFAULT;
  pageIndex = PAGINATION.PAGE_NUMBER_DEFAULT;

  ENTITY_STATUS = ENTITY_STATUS;
  EVENT_STATUS = EVENT_STATUS;
  RECEIVER_TYPE = RECEIVER_TYPE;
  ROUTER_UTILS = ROUTER_UTILS;
  EVENT_TYPE = EVENT_TYPE;
  VALIDATORS = VALIDATORS;
  SYSTEM_RULES = SYSTEM_RULES;
  FILE_UPLOAD_EXTENSIONS = FILE_UPLOAD_EXTENSIONS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;

  constructor(
    private formBuilder: FormBuilder,
    private modalService: ModalService,
    private eventService: EventService,
    private downloadService: DownloadService,
    private announcementTypeService: AnnouncementTypeService,
    private routerActive: ActivatedRoute,
    private translateService: TranslateService,
    private toastService: ToastrCustomService,
    private customerService: CustomerService,
    private ngbModal: NgbModal,
    private router: Router
  ) {
    // get id form paramater
    this.routerActive.paramMap.subscribe((res) => {
      const idParam = res.get('eventId');
      if (idParam) {
        this.eventId = +idParam;
        this.isUpdate = true;
      }
      if (res.get('action')) {
        this.isBackup = true;
      }
    });
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.hasFilter = true;
  }

  ngOnInit(): void {
    this.loadListAnnouncement();
    this.loadListCustomer();
    if (this.isUpdate) {
      setTimeout(() => this.getDetail(), 500);
    } else {
      const actionBackup = this.routerActive.snapshot.paramMap.get('action');
      if (actionBackup) {
        const eventId =
          this.routerActive.snapshot.paramMap.get('eventBackupId');
        if (eventId) {
          this.eventService.detail({ id: eventId }).subscribe((res: any) => {
            this.eventBackup = res.body;
            const data = res.body || undefined;
            this.initForm(data);
          });
        }
      }
      this.initForm();
    }
  }

  /**
   * load list customer
   */
  loadListCustomer() {
    this.customerService.searchAutoComplete({}).subscribe((res: any) => {
      const customers = res.body.content;
      this.customers = customers.filter((item: ICustomer) =>
        this.customerIds.includes(item?.customerId)
      );
    });
  }

  laodDataCustomer(result: any) {
    const customerConcat = this.customers.concat(result);
    this.customers = [
      ...new Map(
        customerConcat
          .filter((customer) => !!customer.customerId)
          .map((item) => [item.customerId, item])
      ).values(),
    ];
  }

  onSearchAutoComplete(customers: ICustomer[]) {}

  /**
   * init form
   *
   * @param iEvent IEvent
   */
  initForm(iEvent?: IEvent): void {
    this.formEvent = this.formBuilder.group({
      title: [
        iEvent?.title || '',
        [
          Validators.required,
          Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
        ],
      ],
      content: [
        iEvent?.content || '',
        [
          Validators.required,
          Validators.maxLength(VALIDATORS.LENGTH.CONTENT_NOTIFICATION_MAX),
        ],
      ],
      announcementTypeId: [
        iEvent?.announcementTypeId || null,
        [Validators.required],
      ],
      eventStatus: [iEvent?.eventStatus || null],
      expectedNotificationAt: [
        // [
        //   (iEvent?.expectedNotificationAt
        //     ? this.getDateString(iEvent?.expectedNotificationAt + '')
        //     : null) || null,
        // ],
        iEvent?.expectedNotificationAt
          ? this.getDateString(iEvent?.expectedNotificationAt + '')
          : null,
        [Validators.required],
      ],
      receiverType: [iEvent?.receiverType || RECEIVER_TYPE[0].value],
      eventId: [iEvent?.eventId || null],
      sender: [iEvent?.createdBy || null],
      icon: [iEvent?.icon || ''],
      files: [null],
    });
  }

  // '2022-07-16T16:32'
  /**
   * set content to form when change data
   *
   * @param type
   * @param content
   */
  onChangeData(type: string, content: any): void {
    this.invalidContent(content);
    this.formEvent.get(type)?.setValue(content);
  }

  /**
   * back to monitor list
   */
  backToList(): void {
    this.hasFilter = true;
    this.router.navigate([ROUTER_UTILS.notification.root]);
  }

  /**
   * Backup event
   */
  backUpEvent(): void {
    this.router.navigate([
      ROUTER_UTILS.notification.root,
      ROUTER_UTILS.notification.create,
      { action: ROUTER_ACTIONS.backup, eventBackupId: this.eventId },
    ]);
  }

  /**
   * get detail event
   */
  getDetail(): void {
    this.eventService.detail({ id: this.eventId }).subscribe((res: any) => {
      this.event = res.body;
      const data = res.body || undefined;
      if (data) {
        if (
          data.eventStatus === EVENT_TYPE.DONE ||
          data.eventStatus === EVENT_TYPE.WAITING
        ) {
          this.isWaitingOrSend = true;
        }
        if (data.icon) {
          const searchFile: IFileEntrySearch = {};
          searchFile.classPk = data.eventId;
          searchFile.fileEntryId = data.icon.id;
          searchFile.normalizeName = data.icon.normalizeName;

          this.eventService.getIcon(searchFile).subscribe((responsive: any) => {
            CommonUtils.blobToBase64(responsive.body).then((base64) => {
              this.imageUrls = [{ src: base64, name: null, id: data.icon.id }];
            });
          });
          // this.getFile = [{ scr: data.iconUrL, name: null }];
        }
      }
      this.initForm(data);
      this.customers = this.event?.customerDTOS as any;
    });
  }

  loadListAnnouncement() {
    this.announcementSearch.pageIndex = this.pageIndex;
    this.announcementSearch.pageSize = this.pageSize;
    this.announcementTypeService
      .searchAutoComplete(this.announcementSearch)
      .subscribe((res: any) => {
        this.announcementTypes = res.body.content;
      });
  }

  getDateString(dateTime: string) {
    const [dateValues, timeValues] = dateTime.split(' ');
    const [day, month, year] = dateValues.split('-');
    const [hours, minutes, seconds] = timeValues.split(':');

    return moment(
      new Date(+year, +month - 1, +day, +hours, +minutes, +seconds)
    ).format(MOMENT_CONST.DATE_TIME_MOMENT_FORMAT);
  }

  /**
   * Create: check validate file and data input if valid then call api create
   */
  onCreate(eventType: string) {
    // touched form
    this.invalidContent();
    if (this.formEvent.invalid) {
      CommonUtils.markFormGroupTouched(this.formEvent);
      return;
    }

    const data = this.formEvent.getRawValue();

    // format expected time
    data.expectedNotificationAt = moment(
      new Date(data.expectedNotificationAt)
    ).format(MOMENT_CONST.LOCAL_DATE_TIME_FORMAT);

    data.eventType = eventType;

    data.customerIds = this.customers.map((customer) => customer.customerId);
    //  set data files = map fileUploads
    data.files = this.fileUploads.map((fileX) => fileX.file);

    this.eventService.create(data).subscribe((res): void => {
      this.router.navigate([ROUTER_UTILS.notification.root]);
      this.toastService.success('common.action.createSuccess');
    });
  }

  /**
   * Update: check validate file and data input if valid then call api create
   */
  onUpdate() {
    this.invalidContent();
    if (this.formEvent.invalid) {
      CommonUtils.markFormGroupTouched(this.formEvent);
    }
    const data = this.formEvent.getRawValue();

    data.expectedNotificationAt = moment(
      new Date(data.expectedNotificationAt)
    ).format(MOMENT_CONST.LOCAL_DATE_TIME_FORMAT);

    // data.customerIds = this.customerIds;
    data.customerIds = this.customers.map((customer) => customer.customerId);

    data.files = this.fileUploads.map((fileX) => fileX.file);

    if (this.formEvent.valid) {
      this.eventService.update(data).subscribe((res): void => {
        this.router.navigate([ROUTER_UTILS.notification.root]);
        this.toastService.success('common.action.updateSuccess');
      });
    }
  }

  onSend() {
    // touched form
    this.invalidContent();
    if (this.formEvent.invalid) {
      CommonUtils.markFormGroupTouched(this.formEvent);
    }
    const data = this.formEvent.getRawValue();

    data.expectedNotificationAt = moment(
      new Date(data.expectedNotificationAt)
    ).format(MOMENT_CONST.LOCAL_DATE_TIME_FORMAT);

    data.customerIds = this.customers.map((customer) => customer.customerId);

    data.files = this.fileUploads.map((fileX) => fileX.file);
    if (this.formEvent.valid) {
      this.eventService.send(data).subscribe((res) => {
        this.router.navigate([ROUTER_UTILS.notification.root]);
        this.toastService.success('common.action.updateSuccess');
      });
    }
  }

  // uploadFile() {
  //   const modalRef = this.ngbModal.open(UploadFileComponent, {
  //     backdrop: 'static',
  //     size: 'xl',
  //     centered: true,
  //     keyboard: false,
  //   });

  //   modalRef.componentInstance.action = MODAL_ACTION.UPLOAD;
  //   modalRef.componentInstance.acceptTypeFiles = ['excel'];
  //   modalRef.componentInstance.hasBtnDownloadTemplate = true;
  //   modalRef.componentInstance.emitter.subscribe((res: any) => {
  //     if (res === MODAL_ACTION.DOWNLOAD_TEMPLATE.code) {
  //       const obFile = this.eventService.downloadTemplate();

  //       const fileName = this.translateService.instant(
  //         'template.customerEvent'
  //       );

  //       this.downloadService.downloadFileWithObservableAndName(
  //         obFile,
  //         fileName,
  //         FILE_EXTENSION.XLSX
  //       );
  //     }
  //   });

  //   modalRef.result.then((result) => {
  //     if (result?.length) {
  //       this.eventService
  //         .import(result[0]?.originFileObj)
  //         .subscribe((res: any) => {
  //           if (res.body) {
  //             const customerUploads = res.body as ICustomer[];
  //             customerUploads.forEach((item) =>
  //               this.customerIds.push(item.customerId)
  //             );
  //             this.loadListCustomer();
  //           }
  //         });
  //     }
  //   });
  // }

  exportFileReceiver() {
    const fileName = this.translateService.instant('template.eventReceiver');
    const obFile = this.eventService.export({ id: this.event.eventId });

    this.downloadService.downloadFileWithObservableAndName(
      obFile,
      fileName,
      FILE_EXTENSION.XLSX
    );
  }

  addCustomer() {
    const modalData = {
      title: 'event.addCustomerTitle',
      content: 'event.usernameOrPhoneNumber',
      interpolateParams: {},
    };

    this.modalService.addItem(modalData).then((result: any) => {
      // this.customerIds = Array.from(new Set(this.customerIds.concat(result)));
      this.laodDataCustomer(result);
    });
  }

  getTemplate(data: string) {
    this.formEvent.patchValue({
      content: data.split(VALIDATORS.PATTERN.SPACE_HTML).join(' '),
    });
  }

  /**
   * Upload picture
   * set file upload and check validate file
   *
   * @param fileSelected any
   */
  onUploadPics(fileSelected: any): void {
    // set fileUpload
    this.fileUploads = fileSelected;
  }

  onRemoveImage(image: any): void {
    this.fileDelete.push(image.id);
  }

  getCustomerEmit(customers: ICustomer[]): void {
    this.customers = customers;
  }

  invalidContent(text?: string): void {
    let content = CommonUtils.stripHTML(
      this.formEvent.get('content')?.value
    ) as any;
    if (text) {
      content = text;
    }

    if (
      content.replace(VALIDATORS.PATTERN.SPACE_HTML, ' ').length >
      VALIDATORS.LENGTH.CONTENT_NOTIFICATION_MAX
    ) {
      this.formEvent
        .get('content')
        ?.setValidators([
          Validators.required,
          Validators.maxLength(VALIDATORS.LENGTH.CONTENT_NOTIFICATION_MAX),
        ]);
      this.formEvent.get('content')?.updateValueAndValidity();
      this.isMaxLengthContent = true;
    } else {
      this.formEvent.get('content')?.setValidators([Validators.required]);
      this.formEvent.get('content')?.updateValueAndValidity();
      this.isMaxLengthContent = false;
    }
  }

  uploadFile() {
    const modalRef = this.ngbModal.open(ImportCustomerNotificationComponent, {
      backdrop: 'static',
      centered: true,
      size: 'lg',
      keyboard: true,
    });
    modalRef.componentInstance.action = MODAL_ACTION.CONFIRM;
    modalRef.result.then((res) => {
      if (res) {
        this.customers = res.success;
      }
    });
  }
}
