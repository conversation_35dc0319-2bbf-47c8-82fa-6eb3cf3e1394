import { Directive, HostListener } from '@angular/core';
import { NgControl } from '@angular/forms';
import { VALIDATORS } from '@shared/constants/validators.constant';

@Directive({
  selector: '[appNumberInput]',
})
export class NumberInputDirective {
  constructor(private ngControl: NgControl) {}

  @HostListener('input', ['$event'])
  onInputChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    let value = input.value;

    // Allow only numbers and decimal points
    const regex = VALIDATORS.PATTERN.NUMBER_AND_DECIMAL;
    if (!regex.test(value)) {
      value = value.replace(/[^0-9.]/g, '');
    }

    // Nếu không phải 0. và bắt đầu bằng nhiều số 0
    if (/^0\d+/.test(value)) {
      // Nếu toàn bộ chuỗi chỉ chứa số 0, gi<PERSON> lại 1 số 0
      if (/^0+$/.test(value)) {
        value = '0';
      } else {
        value = value.replace(/^0+/, '');
      }
    }

    // Limit to 2 decimal places
    const decimalIndex = value.indexOf('.');
    if (decimalIndex >= 0) {
      const beforeDecimal = value.substring(0, decimalIndex);
      let afterDecimal = value.substring(decimalIndex + 1, value.length);
      if (afterDecimal.length > 2) {
        afterDecimal = afterDecimal.substring(0, 2);
      }
      value = beforeDecimal + '.' + afterDecimal;
    }

    // Limit integer part to 100
    if (decimalIndex === -1 && +value > 100) {
      value = '100';
    }

    this.ngControl.control?.setValue(value);
  }
}
