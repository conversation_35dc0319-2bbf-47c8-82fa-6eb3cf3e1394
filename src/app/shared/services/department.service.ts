import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ICustomer } from '@shared/models/customer.model';
import { IDepartmentSearch } from '@shared/models/request/department.search';
import { AbstractService } from '@shared/services/common/abstract.service';
import { Observable } from 'rxjs';
import { IDepartment } from './../models/department.model';
import { EntityResponseType } from './common/abstract.service';

@Injectable({ providedIn: 'root' })
export class DepartmentService extends AbstractService {
  resourceUrl = `/department`;

  constructor(http: HttpClient) {
    super(http);
  }

  /**
   *
   * @param request
   * @returns
   */
  search(
    request: IDepartmentSearch
  ): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer[]>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  /**
   *
   * @param request
   * @returns
   */
  searchByKeyWord(
    request: IDepartmentSearch
  ): Observable<EntityResponseType<IDepartment>> {
    return super.post<EntityResponseType<IDepartment[]>>(
      `${this.resourceUrl}/search-by-keyword`,
      request
    );
  }

  /**
   * getAll
   *
   * @returns Observable<EntityResponseType<IDepartment[]>>
   */
  getAll(): Observable<EntityResponseType<IDepartment[]>> {
    return super.post(`${this.resourceUrl}/all`);
  }

  /**
   *
   * @param request
   * @returns
   */
  create(request: IDepartment): Observable<EntityResponseType<IDepartment>> {
    return super.post<EntityResponseType<IDepartment>>(
      `${this.resourceUrl}/create`,
      request
    );
  }

  /**
   *
   * @param request
   * @returns
   */
  unlock(request: IDepartment): Observable<EntityResponseType<boolean>> {
    return super.post<EntityResponseType<boolean>>(
      `${this.resourceUrl}/unlock`,
      request
    );
  }

  /**
   *
   * @param request
   * @returns
   */
  lock(request: IDepartment): Observable<EntityResponseType<boolean>> {
    return super.post<EntityResponseType<boolean>>(
      `${this.resourceUrl}/lock`,
      request
    );
  }

  /**
   *
   * @param request
   * @returns
   */
  deleteDepartment(
    request: IDepartment
  ): Observable<EntityResponseType<IDepartment>> {
    return super.post<EntityResponseType<IDepartment>>(
      `${this.resourceUrl}/delete`,
      request
    );
  }

  /**
   *
   * @param request
   * @returns
   */
  detail(request: IDepartment): Observable<EntityResponseType<IDepartment>> {
    return super.post<IDepartment>(`${this.resourceUrl}/detail`, request);
  }

  /**
   *
   * @param request
   * @returns
   */
  update(request: IDepartment): Observable<EntityResponseType<IDepartment>> {
    return super.post<IDepartment>(`${this.resourceUrl}/update`, request);
  }
}
