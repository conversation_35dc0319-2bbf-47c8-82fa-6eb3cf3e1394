import { HttpStatusCode } from '@angular/common/http';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  FILE_EXTENSION,
  FILE_TYPE_ACCEPT_CONST,
  FILE_TYPE_CONST,
  customErrorCode,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import {
  FILE_TEXT_PLAIN,
  MAX_FILE_SIZE_10MB,
} from '@shared/constants/file.constant';
import { EventService } from '@shared/services/event.service';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS } from '@shared/utils';
import { saveAs } from 'file-saver';

@Component({
  selector: 'app-import-customer',
  templateUrl: './modal-import-notification.component.html',
  styleUrls: ['./modal-import-notification.component.scss'],
})
export class ImportCustomerNotificationComponent implements OnInit {
  formImport: FormGroup = new FormGroup({});
  selectedFileName = '';
  fileImport: any;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  FILE_TYPE_ACCEPT_CONST = FILE_TYPE_ACCEPT_CONST;
  @ViewChild('fileInput') fileInput: any;
  acceptTypeFiles: string[] = FILE_TYPE_CONST;

  constructor(
    private fb: FormBuilder,
    public translateService: TranslateService,
    public activeModal: NgbActiveModal,
    private eventService: EventService,
    private toastService: ToastrCustomService,
    private downloadService: DownloadService
  ) {}

  ngOnInit(): void {
    this.initForm();
  }

  initForm() {
    this.formImport = this.fb.group({
      uploadFile: ['', Validators.required],
    });
  }

  downloadTemplateImport(): void {
    const fileName = this.translateService.instant('template.customerTemplate');
    const obFile = this.eventService.downloadTemplate();
    this.downloadService.downloadFileWithObservableAndName(
      obFile,
      fileName,
      FILE_EXTENSION.XLSX
    );
  }

  onFileSelected(event: any) {
    if (
      event?.target?.files[0].size > MAX_FILE_SIZE_10MB &&
      !this.acceptTypeFiles.includes(event?.target?.files[0].type)
    ) {
      this.toastService.warning('common.maxSizeFile');
      this.formImport.controls.uploadFile.setValue('');
    } else if (event?.target?.files[0].size > MAX_FILE_SIZE_10MB) {
      this.toastService.warning('common.maxSize');
      this.formImport.controls.uploadFile.setValue('');
    } else if (!this.acceptTypeFiles.includes(event?.target?.files[0].type)) {
      this.toastService.warning('common.fileTypeError');
      this.formImport.controls.uploadFile.setValue('');
    } else if (event?.target?.files.length > 0) {
      this.selectedFileName = event?.target?.files[0].name;
      this.fileImport = event?.target?.files;
    } else {
      return;
    }
  }

  importFile(): void {
    const [fileImport] = this.fileImport;
    if (!fileImport) {
      return;
    }
    this.eventService.import(fileImport).subscribe(
      (res: any) => {
        this.toastService.success('common.action.uploadSuccess');
        this.activeModal.close({
          success: res?.body,
          value: true,
        });
      },
      (err) => {
        if (
          err.status === HttpStatusCode.BadRequest &&
          err.error.errorCode === customErrorCode.errorCodeImportCustomer
        ) {
          this.saveStringToFile(
            err.error.title,
            'template.errorImportCustomer'
          );
        }
      }
    );
  }

  private saveStringToFile(content: string, fileName: string): void {
    const blob = new Blob([content], { type: FILE_TEXT_PLAIN });
    const fileNameTranslate = this.translateService.instant(fileName);
    saveAs(blob, fileNameTranslate);
    return;
  }

  deleteFile() {
    // Clear the file input
    this.fileInput.nativeElement.value = '';
    this.selectedFileName = '';
    this.fileImport = null;
    this.formImport.controls.uploadFile.setValue('');
  }
}
