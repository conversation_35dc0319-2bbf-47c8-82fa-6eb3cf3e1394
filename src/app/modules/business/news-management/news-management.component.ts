import { Component, OnInit } from '@angular/core';
import { <PERSON>bstractControl, FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import {
  CALCULATE_MILLISECONDS_IN_A_YEAR,
  CATEGORY_MAP,
  DATE_RANGE_STATISTICAL_LOAN_ONLINE,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  EVENT_TYPE,
  LIMIT_LENGTH_WORD_CONST,
  MAX_DATE_CONST,
  MODAL_ACTION,
  MOMENT_CONST,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { ICategory } from '@shared/models/category.model';
import { INews } from '@shared/models/news.model';
import { CategoryService } from '@shared/services/category.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { NewsService } from '@shared/services/news.service';
import CommonUtils from '@shared/utils/common-utils';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils/router.utils';
import moment from 'moment';

@Component({
  selector: 'app-news-management',
  templateUrl: './news-management.component.html',
  styleUrls: ['./news-management.component.scss'],
})
export class NewsManagementComponent implements OnInit {
  data?: INews[] = [];
  maxDate = new Date();
  maxendDate = new Date();
  eventSearch: INews = {};
  isErrorStartDateGreaterEndDate = false;
  pageSizeOptions = PAGINATION.OPTIONS;
  LIMIT_LENGTH_WORD_CONST = LIMIT_LENGTH_WORD_CONST;
  MAX_DATE_CONST = MAX_DATE_CONST;
  ROUTER_UTILS = ROUTER_UTILS;
  EVENT_TYPE = EVENT_TYPE;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  SYSTEM_RULES = SYSTEM_RULES;
  CATEGORY_MAP = CATEGORY_MAP;
  categoryList?: ICategory[] = [];

  constructor(
    private fb: FormBuilder,
    private newsService: NewsService,
    private toastService: ToastrCustomService,
    private modalService: ModalService,
    private router: Router,
    private categoryService: CategoryService
  ) {}
  formSearch = this.fb.group({
    title: '',
    startDate: [null, [Validators.required]],
    endDate: [null, [Validators.required]],
    categoryId: null,
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
  });

  ngOnInit(): void {
    const startDate = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDate = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.eventSearch.fromTime = startDate;
    this.eventSearch.toTime = endDate;
    this.formSearch.controls.startDate.setValue(
      CommonUtils.reverseDate(startDate)
    );
    this.formSearch.controls.endDate.setValue(CommonUtils.reverseDate(endDate));
    this.onSearch();
    this.getCategory();
  }

  onSearch(): void {
    const body = this.formSearch.value;
    if (this.formSearch.valid) {
      this.newsService.search(body).subscribe((res: any): void => {
        this.data = res.body.content;
        this.formSearch.controls.length.setValue(res.body.totalElements);
      });
    }
  }

  getCategory(): void {
    this.categoryService.category().subscribe((res: any): void => {
      this.categoryList = res?.body;
    });
  }

  onChangePage(page: any) {
    this.formSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  onReset(): void {
    const startDateReset = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDateReset = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.formSearch.controls.title.reset(null);
    this.formSearch.controls.categoryId.reset(null);
    this.formSearch.controls.startDate.setValue(
      CommonUtils.reverseDate(startDateReset)
    );
    this.formSearch.controls.endDate.setValue(
      CommonUtils.reverseDate(endDateReset)
    );
    this.formSearch.controls.startDate.clearValidators();
    this.formSearch.controls.startDate.setValidators([Validators.required]);
    this.formSearch.controls.startDate.updateValueAndValidity();
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formSearch.value.pageIndex,
      this.formSearch.value.pageSize
    );
  }

  changeValidDate() {
    if (
      this.formSearch.controls.startDate.value &&
      this.formSearch.controls.endDate.value
    ) {
      if (this.formSearch.controls['endDate'].value) {
        this.maxDate = this.formSearch.controls['endDate'].value;
      }
      const startDate = CommonUtils.setStartTime(
        new Date(this.formSearch.controls.startDate.value).getTime()
      );
      const endDate = CommonUtils.setStartTime(
        new Date(this.formSearch.controls.endDate.value).getTime()
      );
      const dayMax =
        (endDate - startDate) / CALCULATE_MILLISECONDS_IN_A_YEAR.DAY;
      if (dayMax > DATE_RANGE_STATISTICAL_LOAN_ONLINE) {
        this.formSearch.controls.startDate.setValidators([
          Validators.required,
          this.dateValidator(dayMax),
        ]);
        this.formSearch.controls.startDate.updateValueAndValidity();
      } else {
        this.formSearch.controls.startDate.clearValidators();
        this.formSearch.controls.startDate.setValidators([Validators.required]);
        this.formSearch.controls.startDate.updateValueAndValidity();
      }
    }
    this.changeEndDate();
  }

  changeEndDate() {
    if (this.formSearch.controls['endDate'].value) {
      this.maxDate = this.formSearch.controls['endDate'].value;
    }
    const dataSearch = this.formSearch.getRawValue();
    if (dataSearch.startDate && dataSearch.endDate) {
      const startDateSearch = new Date(dataSearch.startDate);
      const endDateSearch = new Date(dataSearch.endDate);
      this.isErrorStartDateGreaterEndDate =
        startDateSearch.getTime() > endDateSearch.getTime();
    }
  }

  dateValidator(value: number): any {
    return (control: AbstractControl): { [key: string]: any } | null => {
      return value > DATE_RANGE_STATISTICAL_LOAN_ONLINE
        ? { invalidDate: true }
        : null;
    };
  }

  onSearchSubmit(): void {
    this.formSearch.controls.pageIndex.setValue(PAGINATION.PAGE_NUMBER_DEFAULT);
    this.formSearch.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    this.onSearch();
  }

  lockAndUnlock(data: any): void {
    const modalData = {
      title: data.status === 1 ? 'model.news.lock' : 'model.news.unlock',
      content:
        data.status === 1
          ? 'model.news.lockContent'
          : 'model.news.unlockContent',
      interpolateParams: { title: `<b>${data?.title || ''}</b>` },
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { newsId: data.newsId };
        if (data.status === 1) {
          this.newsService.lock(params).subscribe((res) => {
            this.toastService.success('common.action.lockSuccess');
            this.onSearch();
          });
        } else {
          this.newsService.unlock(params).subscribe((res) => {
            this.toastService.success('common.action.unlockSuccess');
            this.onSearch();
          });
        }
      }
    });
  }

  onDetailAndUpdate(newID?: number, action?: string) {
    this.router.navigate([ROUTER_UTILS.news.root, newID, action]);
  }

  onDelete(newsId?: number): void {
    const modalData = {
      title: 'model.news.modal.delete',
      content: 'model.news.modal.deleteNewsContent',
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { newsId };
        this.newsService.delete(params).subscribe((res: any) => {
          this.toastService.success('common.action.deleteSuccess');
          if (this.data?.length === 1) {
            this.formSearch.controls.pageIndex.setValue(
              this.formSearch.controls.pageIndex.value === 0
                ? ''
                : Number(this.formSearch.controls.pageIndex.value) - 1
            );
          }
          this.onSearch();
        });
      }
    });
  }

  getContentHTML(content?: string): string {
    if (content) {
      return CommonUtils.stripHTML(content);
    }
    return '';
  }
}
