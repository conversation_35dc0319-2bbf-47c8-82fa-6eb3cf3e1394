<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="backToList()" />
      <h5 *ngIf="isUpdate">{{ "event.detailTitle" | translate }}</h5>
      <h5 *ngIf="!isUpdate && isBackup">
        {{ "event.backUpTitle" | translate }}
      </h5>
      <h5 *ngIf="!isUpdate && !isBackup">
        {{ "event.createTitle" | translate }}
      </h5>
    </div>
    <!-- <form [formGroup]="form" *ngIf="customer?.customerId"></form> -->
    <form [formGroup]="formEvent" *ngIf="isUpdate ? event?.eventId : !isUpdate">
      <div class="col-md-12">
        <div class="row">
          <h2 class="title-create">
            {{
              (isUpdate ? "event.detailTitle" : "event.createTitle") | translate
            }}
          </h2>
        </div>
        <div class="row border-ckeditor">
          <h3 class="border-ckeditor-h3">
            {{ "event.information" | translate }}
          </h3>
          <div class="w-100">
            <div class="col-md-12">
              <div class="row">
                <div class="col-md-3" *ngIf="isUpdate">
                  <div class="form-group">
                    <label>{{ "common.status" | translate }}</label>
                    <ng-select
                      appearance="outline"
                      [readonly]="isUpdate"
                      placeholder="{{ 'common.status' | translate }}"
                      [clearable]="false"
                      formControlName="eventStatus"
                    >
                      <ng-option
                        [value]="item?.code"
                        *ngFor="let item of EVENT_STATUS"
                      >
                        {{ item.label | translate }}
                      </ng-option>
                    </ng-select>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label
                      >{{ "model.event.announcementTypeId" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <ng-select
                      appearance="outline"
                      [searchable]="false"
                      [readonly]="isWaitingOrSend"
                      placeholder="{{
                        'model.event.announcementTypeId' | translate
                      }}"
                      [clearable]="false"
                      formControlName="announcementTypeId"
                    >
                      <ng-option
                        [value]="announcementType?.announcementTypeId"
                        *ngFor="let announcementType of announcementTypes"
                      >
                        {{ announcementType?.announcementTypeName }}
                      </ng-option>
                    </ng-select>
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="
                        formEvent.get('announcementTypeId')?.errors?.required &&
                        formEvent.get('announcementTypeId')?.touched
                      "
                    >
                      {{
                        "error.event.required.announcementTypeId" | translate
                      }}
                    </small>
                  </div>
                </div>
                <div class="col-12">
                  <div class="form-group">
                    <label
                      >{{ "model.event.title" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      trim
                      formControlName="title"
                      type="text"
                      [readonly]="isWaitingOrSend"
                      placeholder="{{ 'model.event.title' | translate }}"
                      class="w-100"
                      class="form-control"
                      [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
                    />
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="
                        formEvent.get('title')?.errors?.required &&
                        formEvent.get('title')?.touched
                      "
                    >
                      {{ "error.event.required.title" | translate }}
                    </small>
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="
                        formEvent.get('title')?.errors?.maxlength &&
                        formEvent.get('title')?.touched
                      "
                    >
                      {{
                        "error.event.maxLength.title"
                          | translate
                            : {
                                param: VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH
                              }
                      }}
                    </small>
                  </div>
                </div>
                <div class="col-3">
                  <div class="col-12">
                    <label>{{ "common.image" | translate }}</label>
                    <div class="bank-upload">
                      <app-upload-images
                        (imageUploaded)="onUploadPics($event)"
                        [fileRequired]="fileRequired"
                        [fileExtension]="FILE_UPLOAD_EXTENSIONS.AVATAR"
                        (imageRemoved)="onRemoveImage($event)"
                        [imageUrls]="imageUrls"
                        [type]="
                          eventId
                            ? ROUTER_ACTIONS.update
                            : ROUTER_ACTIONS.create
                        "
                      ></app-upload-images>
                    </div>
                  </div>
                </div>
                <div class="col-12">
                  <div class="form-group">
                    <label
                      >{{ "model.event.content" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <mb-editor
                      [placeholder]="'model.event.content' | translate"
                      [value]="formEvent?.get('content')?.value || ''"
                      [maxLength]="maxLengthContent"
                      [isReadOnly]="isWaitingOrSend"
                      (HTMLdata)="getTemplate($event)"
                      (data)="onChangeData('content', $event)"
                      trim
                    >
                    </mb-editor>
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="
                        formEvent.get('content')?.errors?.required &&
                        formEvent.get('content')?.touched
                      "
                    >
                      {{ "error.event.required.content" | translate }}
                    </small>
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="isMaxLengthContent"
                    >
                      {{ "error.event.maxLength.content" | translate }}
                    </small>
                  </div>
                </div>
                <div
                  class="col-md-3"
                  *ngIf="
                    event.eventStatus === EVENT_TYPE.DONE ||
                    event.eventStatus === EVENT_TYPE.WAITING
                  "
                >
                  <div class="form-group">
                    <label>{{ "event.sender" | translate }} </label>
                    <input
                      type="text"
                      formControlName="sender"
                      class="w-100"
                      [readonly]="isWaitingOrSend"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label
                      >{{ "model.event.expectedNotificationAt" | translate }}
                      <span class="text-danger">*</span></label
                    >
                    <!-- <input
                      type="datetime-local"
                      formControlName="expectedNotificationAt"
                      class="w-100"
                      [readonly]="isWaitingOrSend"
                      [min]="minDate"
                      class="form-control"
                    /> -->
                    <mat-form-field appearance="fill" class="date-picker">
                      <input
                        matInput
                        formControlName="expectedNotificationAt"
                        [ngxMatDatetimePicker]="picker"
                        placeholder="HH:mm:ss, DD/MM/YYYY"
                        [min]="minDate"
                        [disabled]="isWaitingOrSend"
                        readonly
                      />
                      <mat-datepicker-toggle
                        matSuffix
                        [for]="$any(picker)"
                      ></mat-datepicker-toggle>
                      <ngx-mat-datetime-picker #picker [showSeconds]="true">
                      </ngx-mat-datetime-picker>
                    </mat-form-field>
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="
                        formEvent.get('expectedNotificationAt')?.errors
                          ?.required &&
                        formEvent.get('expectedNotificationAt')?.touched
                      "
                    >
                      {{
                        "error.event.required.expectedNotificationAt"
                          | translate
                      }}
                    </small>
                  </div>
                </div>

                <div class="col-12">
                  <div class="form-group">
                    <label>{{ "event.receiver" | translate }}</label>
                    <div>
                      <span>
                        <mat-radio-group formControlName="receiverType">
                          <mat-radio-button
                            [disabled]="isWaitingOrSend"
                            *ngFor="
                              let receiverType of RECEIVER_TYPE;
                              let i = index
                            "
                            class="mr-2"
                            [value]="receiverType.value"
                          >
                            {{ receiverType.label | translate }}
                          </mat-radio-button>
                        </mat-radio-group>
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!--              Danh sách người nhận-->
              <div
                class="row"
                *ngIf="formEvent.get('receiverType')?.value === 'EACH_CUSTOMER'"
              >
                <div class="col-12">
                  <div class="col-6" *ngIf="isUpdate">
                    <h4>
                      {{ "event.receiverList" | translate }}
                    </h4>
                  </div>
                </div>
                <div class="col-12 d-flex">
                  <div class="col-6" *ngIf="!isUpdate">
                    <h4>
                      {{ "event.receiverList" | translate }}
                    </h4>
                  </div>
                  <div
                    class="col-6 d-flex"
                    *ngIf="
                      event.eventStatus === EVENT_TYPE.DONE ||
                      event.eventStatus === EVENT_TYPE.WAITING
                    "
                  >
                    <div
                      class="col-3"
                      *ngIf="event.eventStatus === EVENT_TYPE.DONE"
                    >
                      <h4 class="text-success">
                        {{ ("common.success" | translate) + ": " }}
                        {{ event.totalSuccess || 0 }}
                      </h4>
                    </div>
                    <div
                      class="col-3"
                      *ngIf="event.eventStatus === EVENT_TYPE.DONE"
                    >
                      <h4 class="text-danger">
                        {{ ("common.fail" | translate) + ": " }}
                        {{ event.totalFail || 0 }}
                      </h4>
                    </div>
                    <div class="col-3">
                      <h4 class="text-primary">
                        {{ ("event.numberOfReceiver" | translate) + ": " }}
                        {{ customers.length || 0 }}
                      </h4>
                    </div>
                  </div>
                  <div
                    class="col-6"
                    *ngIf="event.eventStatus === EVENT_TYPE.DRAFT"
                  >
                    <h4 class="text-primary">
                      {{ ("event.numberOfCustomer" | translate) + ": " }}
                      {{ customers.length || 0 }}
                    </h4>
                  </div>
                  <div
                    class="col-6"
                    *ngIf="
                      event.eventStatus === EVENT_TYPE.DONE ||
                      event.eventStatus === EVENT_TYPE.WAITING
                    "
                  >
                    <div class="float-right">
                      <button
                        (click)="exportFileReceiver()"
                        [disabled]="event.eventStatus === EVENT_TYPE.WAITING"
                        class="mb-2"
                        [ngClass]="
                          event.eventStatus === EVENT_TYPE.WAITING
                            ? 'btn btn-secondary'
                            : 'btn btn-danger'
                        "
                      >
                        {{ "common.action.export" | translate }}
                      </button>
                    </div>
                  </div>
                  <div
                    class="col-6"
                    *ngIf="event.eventStatus === EVENT_TYPE.DRAFT || !isUpdate"
                  >
                    <div class="float-right">
                      <button
                        (click)="uploadFile()"
                        data-toggle="modal"
                        class="upload-file-customer"
                        *hasPrivileges="SYSTEM_RULES.NOTIFICATION_IMPORT"
                      >
                        <span class="text-decoration">{{
                          "event.downloadFileEvent" | translate
                        }}</span>
                      </button>
                      <span *hasPrivileges="SYSTEM_RULES.NOTIFICATION_IMPORT"
                        >|</span
                      >
                      <button
                        (click)="addCustomer()"
                        class="create-customer"
                        *hasPrivileges="[
                          SYSTEM_RULES.NOTIFICATION_CREATE,
                          SYSTEM_RULES.NOTIFICATION_WRITE
                        ]"
                      >
                        <span class="text-decoration">{{
                          "event.addNew" | translate
                        }}</span>
                      </button>
                    </div>
                  </div>
                </div>

                <div class="col-12">
                  <app-notification-customer
                    [isWaitingOrSend]="isWaitingOrSend"
                    [customers]="customers"
                    (customerEmit)="getCustomerEmit($event)"
                  ></app-notification-customer>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="d-block text-center mb-5 mt-4">
          <ng-container
            *hasPrivileges="
              isUpdate
                ? SYSTEM_RULES.NOTIFICATION_WRITE
                : SYSTEM_RULES.NOTIFICATION_CREATE
            "
          >
            <button
              class="btn btn-white mr-2"
              data-toggle="modal"
              *ngIf="event.eventStatus === EVENT_TYPE.DRAFT || !isUpdate"
              (click)="isUpdate ? onUpdate() : onCreate(EVENT_TYPE.DRAFT)"
            >
              {{ "common.action.save" | translate }}
            </button>
          </ng-container>
          <button
            *ngIf="isUpdate"
            class="btn btn-white mr-2"
            data-toggle="modal"
            (click)="backToList()"
          >
            {{ "common.action.back" | translate }}
          </button>
          <button
            *ngIf="
              event.eventStatus === EVENT_TYPE.WAITING ||
              event.eventStatus === EVENT_TYPE.DONE
            "
            class="btn btn-red"
            data-toggle="modal"
            (click)="backUpEvent()"
          >
            {{ "common.action.backup" | translate }}
          </button>
          <ng-container
            *hasPrivileges="
              isUpdate
                ? SYSTEM_RULES.NOTIFICATION_SEND
                : SYSTEM_RULES.NOTIFICATION_CREATE
            "
          >
            <!-- [disabled]="formEvent.invalid" -->
            <button
              class="btn btn-red"
              *ngIf="event.eventStatus === EVENT_TYPE.DRAFT || !isUpdate"
              (click)="isUpdate ? onSend() : onCreate(EVENT_TYPE.WAITING)"
            >
              {{ "common.action.send" | translate }}
            </button>
          </ng-container>
        </div>
      </div>
    </form>
  </div>
</section>
