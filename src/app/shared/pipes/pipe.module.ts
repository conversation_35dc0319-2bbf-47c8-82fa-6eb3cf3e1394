import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { StringToDatePipe } from '@shared/pipes/datetime.pipe';
import { AppDatePipe } from './app.date.pipe';
import { BytesPipe } from './bytes.pipe';
import { CurrencyDecimalLakPipe } from './currency.decimal.pipe';
import { CurrencyLakPipe } from './currency.pipe';
import { FormatTimePipe } from './format.time.pipe';
import { GenderPipe } from './gender.pipe';
import { JoinPipe } from './join.pipe';
import { LimitWordPipe } from './limit.word.pipe';
import { PlaceholderTranslatePipe } from './placeholder.translate.pipe';
import { SafeHTML } from './safe.html.pipe';

@NgModule({
  imports: [CommonModule],
  declarations: [
    CurrencyLakPipe,
    CurrencyDecimalLakPipe,
    JoinPipe,
    BytesPipe,
    GenderPipe,
    StringToDatePipe,
    AppDatePipe,
    LimitWordPipe,
    FormatTimePipe,
    PlaceholderTranslatePipe,
    SafeHTML,
  ],
  exports: [
    CommonModule,
    CurrencyLakPipe,
    CurrencyDecimalLakPipe,
    JoinPipe,
    BytesPipe,
    GenderPipe,
    StringToDatePipe,
    AppDatePipe,
    LimitWordPipe,
    FormatTimePipe,
    PlaceholderTranslatePipe,
    SafeHTML,
  ],
})
export class PipeModule {}
