export interface ICashout {
  createdBy: string;
  createdDate: string;
  lastModifiedBy: string;
  lastModifiedDate: string;
  id: number;
  accountBank: string;
  accountNumber: string;
  accountName: string;
  accountType: string;
  accountCurrency: string;
  status: number;
  transactionLimit: number;
  cif?: number;
  amountTransferred?: number;
  configurationType?: string;
  accountBalance?: number;
}

export interface ICashoutCreate {
  accountNumber?: string;
  accountName?: string;
  transactionLimit?: number;
  type?: string;
}
