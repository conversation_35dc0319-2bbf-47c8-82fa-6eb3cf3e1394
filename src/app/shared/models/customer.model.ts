import { IFile } from './file.model';
import { IMoneyAccount } from './money-account.model';
import { IPremiumAccNumber } from './premium-acc-number.modal';

export interface ICustomer {
  customerId?: number;
  referenceId?: number;
  customerDraftId?: number;
  username?: string;
  fullname?: string;
  dateOfBirth?: string;
  email?: string;
  phoneNumber?: string;
  status?: number;
  gender?: number;
  description?: string;
  idCardTypeId?: number;
  idCardTypeName?: string;
  idCardNumber?: string;
  nationCode?: string;
  nationCodeOther?: string;
  nationName?: string;
  nationOtherName?: string;
  customerSectorId?: number;
  customerOldSectorId?: number;
  cif?: string;
  limitPerTransaction?: number;
  limitPerDay?: number;
  address?: string;
  approvalStatus?: number;
  reason?: string;
  issueDate?: string;
  issuePlace?: string;
  placeOfResidence?: string;
  placeOfOrigin?: string;
  approvalType?: number;
  createdDate?: string;
  approvedBy?: string;
  placeOfResidenceOutCountry?: string;
  currentAddress?: string;
  phoneContact?: string;
  maritalStatus?: number;
  job?: string;
  position?: string;
  staffCode?: string;
  companyID?: string;
  residentStatus?: string;
  lastModifiedDate?: string;
  files?: File;
  idCardFiles?: IFile[];
  signatureFiles?: IFile[];
  ekycFiles?: IFile[];
  idCardFileDelete?: any[];
  oldVersions?: ICustomer[];
  imageUrls?: any[];
  signatureImageUrls?: any[];
  notificationStatus?: string;
  moneyAccounts?: IMoneyAccount[];
  workplace?: string;
  referralCode?: string;
  userFullNameReferral?: string;
  phoneNumberReferral?: string;
  accountType?: number;
  activeStatus?: number;
  manualActivationTime?: string;
  cifs?: string[];
  revertPremiumAccNumber?: boolean;
  premiumAccountNumber?: string;
  premiumAccountNumberCache?: IPremiumAccNumber;
  transactionId?: string;
  totalPrice?: number;
  currency?: string;
  hasAccountNumber?: boolean;
}

export class Customer implements ICustomer {
  constructor(
    public customerId?: number,
    public customerDraftId?: number,
    public username?: string,
    public fullname?: string,
    public dateOfBirth?: string,
    public email?: string,
    public phoneNumber?: string,
    public status?: number,
    public gender?: number,
    public description?: string,
    public idCardTypeId?: number,
    public idCardTypeName?: string,
    public idCardNumber?: string,
    public nationCode?: string,
    public nationCodeOther?: string,
    public nationName?: string,
    public nationOtherName?: string,
    public customerSectorId?: number,
    public customerOldSectorId?: number,
    public cif?: string,
    public limitPerTransaction?: number,
    public limitPerDay?: number,
    public address?: string,
    public approvalStatus?: number,
    public reason?: string,
    public issueDate?: string,
    public issuePlace?: string,
    public placeOfResidence?: string,
    public placeOfOrigin?: string,
    public approvalType?: number,
    public createdDate?: string,
    public approvedBy?: string,
    public placeOfResidenceOutCountry?: string,
    public currentAddress?: string,
    public phoneContact?: string,
    public maritalStatus?: number,
    public job?: string,
    public position?: string,
    public staffCode?: string,
    public companyID?: string,
    public residentStatus?: string,
    public lastModifiedDate?: string,
    public files?: File,
    public idCardFiles?: IFile[],
    public signatureFiles?: IFile[],
    public ekycFiles?: IFile[],
    public idCardFileDelete?: any[],
    public oldVersions?: ICustomer[],
    public imageUrls?: any[],
    public signatureImageUrls?: any[],
    public workplace?: string,
    public referralCode?: string,
    public userFullNameReferral?: string,
    public phoneNumberReferral?: string,
    public accountType?: number,
    public activeStatus?: number,
    public manualActivationTime?: string,
    public cifs?: string[],
    public revertPremiumAccNumber?: boolean,
    public premiumAccountNumberCache?: IPremiumAccNumber,
    public hasAccountNumber?: boolean
  ) {
    this.customerId = customerId;
    this.customerDraftId = customerDraftId;
    this.username = username;
    this.fullname = fullname;
    this.dateOfBirth = dateOfBirth;
    this.email = email;
    this.phoneNumber = phoneNumber;
    this.status = status;
    this.gender = gender;
    this.description = description;
    this.idCardTypeId = idCardTypeId;
    this.idCardTypeName = idCardTypeName;
    this.idCardNumber = idCardNumber;
    this.nationCode = nationCode;
    this.nationCodeOther = nationCodeOther;
    this.nationName = nationName;
    this.nationOtherName = nationOtherName;
    this.customerSectorId = customerSectorId;
    this.customerOldSectorId = customerOldSectorId;
    this.cif = cif;
    this.limitPerTransaction = limitPerTransaction;
    this.limitPerDay = limitPerDay;
    this.address = address;
    this.approvalStatus = approvalStatus;
    this.reason = reason;
    this.issueDate = issueDate;
    this.issuePlace = issuePlace;
    this.placeOfResidence = placeOfResidence;
    this.placeOfOrigin = placeOfOrigin;
    this.approvalType = approvalType;
    this.createdDate = createdDate;
    this.approvedBy = approvedBy;
    this.placeOfResidenceOutCountry = placeOfResidenceOutCountry;
    this.currentAddress = currentAddress;
    this.phoneContact = phoneContact;
    this.maritalStatus = maritalStatus;
    this.job = job;
    this.position = position;
    this.staffCode = staffCode;
    this.companyID = companyID;
    this.residentStatus = residentStatus;
    this.lastModifiedDate = lastModifiedDate;
    this.files = files;
    this.idCardFiles = idCardFiles;
    this.signatureFiles = signatureFiles;
    this.ekycFiles = ekycFiles;
    this.idCardFileDelete = idCardFileDelete;
    this.oldVersions = oldVersions;
    this.imageUrls = imageUrls;
    this.signatureImageUrls = signatureImageUrls;
    this.workplace = workplace;
    this.referralCode = referralCode;
    this.userFullNameReferral = userFullNameReferral;
    this.phoneNumberReferral = phoneNumberReferral;
    this.accountType = accountType;
    this.activeStatus = activeStatus;
    this.manualActivationTime = manualActivationTime;
    this.cifs = cifs;
    this.revertPremiumAccNumber = revertPremiumAccNumber;
    this.premiumAccountNumberCache = premiumAccountNumberCache;
    this.hasAccountNumber = hasAccountNumber;
  }
}
