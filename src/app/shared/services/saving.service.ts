import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { ISms } from '@shared/models/sms.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { Observable } from 'rxjs';
import { AbstractDomainService } from './common/abstract.domain.service';
import { EntityResponseType } from './common/abstract.service';
import { ISaving } from '@shared/models/saving.model';

@Injectable({
  providedIn: 'root',
})
export class SavingService extends AbstractDomainService<ISaving> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.SAVING;

  export(request: ISms): Observable<EntityResponseType<ISms>> {
    return super.postFile(RESOURCE_CONST.SAVING.API.EXPORT, request);
  }
}
