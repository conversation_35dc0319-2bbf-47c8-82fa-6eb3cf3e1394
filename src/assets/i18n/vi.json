{"common": {"unit": "LAK", "title": "MB Lào", "spinningTip": "<PERSON><PERSON> thống đang hoạt động ...", "success": "<PERSON><PERSON><PERSON><PERSON> công", "fail": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "info": "Thông tin", "warning": "<PERSON><PERSON><PERSON> b<PERSON>o", "error": "Lỗi", "blank": "", "used": "<PERSON><PERSON> ho<PERSON>t động", "deleted": "Đã khóa", "stop": "Dừng hoạt động", "notification": "<PERSON><PERSON><PERSON><PERSON> báo", "notificationContent": "<PERSON><PERSON><PERSON> dung thông báo", "note": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "activeStatus": "<PERSON>r<PERSON><PERSON> thái k<PERSON>ch ho<PERSON>", "required": "Trư<PERSON><PERSON> không đư<PERSON><PERSON> để trống", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "revert": "<PERSON><PERSON> h<PERSON>", "day": "<PERSON><PERSON><PERSON>", "unread": "Chưa xem", "read": "Đã xem", "month": "<PERSON><PERSON><PERSON><PERSON>", "year": "Năm", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "sold": "<PERSON><PERSON> bán", "number": "số", "suspended": "<PERSON><PERSON> treo", "failed": "<PERSON><PERSON><PERSON><PERSON> hiện lỗi", "processing": "<PERSON><PERSON><PERSON>n", "percent": "%", "minute": "<PERSON><PERSON><PERSON>", "timeout": "Time out", "closed": "<PERSON><PERSON> đóng", "pattern": "<PERSON><PERSON><PERSON> dạng không hợp lệ", "accountNumber": "Số tài <PERSON>n", "nation": "Quốc gia", "hello": "<PERSON><PERSON>, ", "vi": "Tiếng <PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "lo": "<PERSON><PERSON><PERSON><PERSON>", "uploadText": "Nhấp hoặc kéo tệp vào khu vực này để tải lên", "uploadHint": "Hỗ trợ tải lên đơn lẻ hoặc nhiều tệp.", "maxFileSize": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON> tệp không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá {{size}} MB", "fileAccept": "Bạn chỉ có thể tải lên tệp {{type}}!", "fileTypeError": "File không đúng định dạng", "maxUploadFile": "<PERSON><PERSON><PERSON> lên tối đa {{count}} tệp", "attachedFiles": "<PERSON> đ<PERSON> k<PERSON>m", "chooseFile": "<PERSON><PERSON><PERSON>", "building": "Tòa nhà", "floor": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>ng ty", "customer": "<PERSON><PERSON><PERSON><PERSON>", "email": "Email", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "buildingCode": "Mã tòa", "floorCode": "<PERSON><PERSON> tầng", "unitCode": "Mã mặt bằng", "image": "<PERSON><PERSON><PERSON>", "sender": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>i", "show": "<PERSON><PERSON><PERSON> thị", "confirmSave": "Bạn có muốn lưu thông tin này không?", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "noDataResult": "<PERSON><PERSON><PERSON><PERSON> có bản ghi nào", "notBlank": " kh<PERSON><PERSON> đ<PERSON><PERSON><PERSON> để trống", "advancedSearch": "<PERSON><PERSON><PERSON> kiếm nâng cao", "uploadImage": "<PERSON><PERSON><PERSON><PERSON>nh của bạn vào đây", "maxImage": "<PERSON>ọn tối đa 5 ảnh", "male": "Nam", "female": "<PERSON><PERSON>", "other": "K<PERSON><PERSON><PERSON>", "identityCard": "CCCD", "passport": "<PERSON><PERSON> ch<PERSON>", "no_search_result": "Không có kết quả phù hợp", "no_search_result_error": "<PERSON><PERSON><PERSON><PERSON> có bản <PERSON>, có thể có lỗi hệ thống, vui lòng liên hệ quản trị hệ thống", "no_list_account_result": "<PERSON><PERSON><PERSON> co<PERSON> danh sách tài <PERSON>n", "no_list_history_active_result": "<PERSON><PERSON><PERSON> ch<PERSON> k<PERSON>", "no_list_history_update_result": "Chưa co<PERSON> lịch sử cập nhật dịch vụ", "no_history_update_result": "<PERSON><PERSON><PERSON> co<PERSON> lịch sử cập nhật", "married": "<PERSON><PERSON> kết hôn", "unmarried": "<PERSON><PERSON><PERSON> kế<PERSON> hôn", "no": "STT", "stt": "No.", "not": "K<PERSON>ô<PERSON>", "login": "<PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "fileUploadInvalid": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> lên không hợp lệ", "upload": "<PERSON><PERSON><PERSON> lên", "uploadFile": "<PERSON><PERSON><PERSON>", "uploading": "<PERSON><PERSON> tải tệp lên...", "myInfo": "Thông tin cá nhân", "myProfile": "<PERSON><PERSON> sơ cá nhân", "searchEmpty": "<PERSON><PERSON> lòng nhập tiêu chí tìm kiếm", "reason": "Lý do", "maxSizeFile": "XLSX or XLS dung lượng file không được quá 10MB", "maxSize": "Dung lượng file không đ<PERSON><PERSON><PERSON> quá 10MB", "selectFileXlsx": "Select file or drag and drop here", "activated": "Đ<PERSON> kích ho<PERSON>", "activate": "<PERSON><PERSON><PERSON>", "notActivated": "<PERSON><PERSON><PERSON> k<PERSON>", "no-rotation": "Không quay vòng", "root-rotation": "Tự động quay vòng gốc", "rotation-of-principal-and-interest": "Tự động quay vòng gốc và lãi", "settlement": "Tất to<PERSON> toàn bộ", "settled": "<PERSON><PERSON> tất toán", "deposit-more": "<PERSON><PERSON><PERSON> thêm tiền", "saving-account": "Mở tài khoản tiết kiệm", "fixed": "<PERSON><PERSON>", "accumulated": "<PERSON><PERSON><PERSON>", "createdDate": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "lastModifiedDate": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON><PERSON> nh<PERSON>t", "textLanguage": "<PERSON><PERSON><PERSON> chọn ngôn ngữ", "contacted": "<PERSON><PERSON> liên hệ", "notContacted": "<PERSON><PERSON><PERSON> li<PERSON> h<PERSON>", "addMore": "<PERSON><PERSON>", "appSelectOption": {"select": "<PERSON><PERSON><PERSON>", "approvalType": "<PERSON><PERSON>n phân lo<PERSON>i", "approvalStatus": "<PERSON><PERSON><PERSON> trạng thái phê du<PERSON>t", "idCardType": "<PERSON><PERSON><PERSON> lo<PERSON>i gi<PERSON>y tờ tùy thân", "status": "<PERSON><PERSON><PERSON> trạng thái"}, "action": {"searchNumber": "số tìm kiếm", "createVersion": "<PERSON><PERSON><PERSON> nhận tạo mới", "label": "<PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON> mới", "update": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "import": "<PERSON><PERSON><PERSON> lên từ Excel", "downloadTemplate": "Tải file mẫu", "uploadFiles": "<PERSON><PERSON><PERSON> file lên", "export": "Xuất file", "print": "In", "print_code": "In mã", "filter": "<PERSON><PERSON><PERSON>", "fromDate": "<PERSON><PERSON> ngày", "toDate": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "agree": "Đồng ý", "cancel": "<PERSON><PERSON><PERSON>", "close": "Đ<PERSON><PERSON>", "unclosed": "Mở đóng", "detail": "<PERSON> ti<PERSON>", "send": "<PERSON><PERSON><PERSON>", "back": "Quay lại", "sync": "Đồng bộ thông tin", "syncReport": "Đồng bộ báo cáo", "updateSector": "Cập nhật sector", "searchOption": "<PERSON><PERSON><PERSON> hình thức tìm kiếm", "createInternal": "Tạo mới tài k<PERSON>n nội bộ", "createLdap": "<PERSON><PERSON>o mới tài k<PERSON>n kh<PERSON>ch hàng", "permission": "<PERSON><PERSON> quyền", "outputFile": "Xuất file", "evaluationDate": "<PERSON><PERSON><PERSON> giá", "syncDate": "<PERSON><PERSON><PERSON> bộ", "link": "<PERSON><PERSON><PERSON> kết tài khoản người dùng", "lock": "Khóa", "unlock": "Mở khóa", "hide": "Ẩn", "unhide": "Bỏ ẩn", "show": "<PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "Ngừng hoạt động", "addRequest": "<PERSON><PERSON><PERSON> mới yêu cầu", "confirmDelete": "Bạn có chắc chắn xóa tầng này?", "reset": "Đặt lại", "draft": "Nháp", "pending": "<PERSON><PERSON> phê <PERSON>", "deny": "<PERSON><PERSON> chối", "approval": "<PERSON><PERSON>", "approved": "<PERSON><PERSON> p<PERSON>", "sendApproval": "<PERSON><PERSON><PERSON>", "cancelApproval": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "approvalError": "<PERSON><PERSON> lỗi", "add": "<PERSON><PERSON><PERSON><PERSON>", "sendOtp": "Gửi OTP", "resendCode": "Gửi lại mã OTP", "confirmOtp": "<PERSON><PERSON><PERSON> thực otp", "search": "<PERSON><PERSON><PERSON>", "searchKeyword": "<PERSON>ừ khóa tìm kiếm", "dismiss": "<PERSON><PERSON><PERSON>", "research": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "researchAgain": "<PERSON><PERSON> c<PERSON><PERSON> lại", "createSuccess": "<PERSON><PERSON><PERSON> mới thành công", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t thành công", "syncReportSuccess": "<PERSON><PERSON><PERSON> bộ báo cáo thành công", "syncReportFails": "<PERSON><PERSON><PERSON> bộ báo cáo không thành công", "sendApprovalSuccess": "<PERSON><PERSON><PERSON> thành công", "syncSuccess": "<PERSON><PERSON>ng bộ thông tin khách hàng {{name}} thành công", "syncCustomerSuccess": "Đồng bộ thông tin khách hàng thành công", "syncFails": "<PERSON><PERSON>ng bộ thông tin khách hàng {{name}} không thành công", "updateSectorSuccess": "Cập nhật sector khách hàng {{name}} thành công", "updateSectorFails": "Cập nhật sector khách hàng {{name}} không thành công", "deleteSuccess": "<PERSON><PERSON><PERSON> thành công", "notification": "<PERSON><PERSON><PERSON> thông báo", "notificationSuccess": "<PERSON><PERSON><PERSON> thông báo thành công", "cancelSuccess": "<PERSON><PERSON><PERSON> thành công", "lockSuccess": "<PERSON><PERSON><PERSON><PERSON> thành công", "unlockSuccess": "Mở khóa thành công", "hideSuccess": "Ẩn thành công", "unhideSuccess": "Bỏ <PERSON>n thành công", "requestSuccess": "<PERSON><PERSON><PERSON> yêu cầu thành công", "activateSuccess": "<PERSON><PERSON><PERSON> ho<PERSON>t tài kho<PERSON>n thành công", "next": "<PERSON><PERSON><PERSON><PERSON>", "changePassword": "<PERSON><PERSON><PERSON> mật kh<PERSON>u", "changePasswordSuccess": "<PERSON><PERSON><PERSON> mật kh<PERSON>u thành công", "uploadFile": "<PERSON><PERSON><PERSON>", "download-template": "Tải file mẫu", "uploadSuccess": "Tải file lên thành công", "selectAll": "<PERSON><PERSON><PERSON> tất cả", "clearAll": "<PERSON><PERSON><PERSON> tất cả", "backup": "T<PERSON><PERSON> bản sao", "resendLinkSuccess": "Gửi link kích hoạt tài khoản thành công", "addBanner": "<PERSON><PERSON><PERSON><PERSON> banner", "deleteBanner": "Xóa banner", "auto": "<PERSON><PERSON> động", "manual": "<PERSON><PERSON><PERSON> công", "contactedSuccess": "Chuyển trạng thái thành đã liên hệ thành công", "notContactedSuccess": "<PERSON>y<PERSON>n trạng thái thành chưa liên hệ thành công", "perform": "<PERSON><PERSON><PERSON><PERSON>"}, "confirm": {"reset": "<PERSON><PERSON><PERSON> nhận đặt lại", "resetContent": "Dữ liệu sẽ bị mất, bạn có chắc chắn muốn đặt lại?", "exitScreen": "<PERSON><PERSON><PERSON> nhận thoát khỏi màn hiện tại", "exitScreenContent": "Dữ liệu sẽ bị mất, bạn có chắc chắn muốn thoát ra?"}, "placeholder": {"gender": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> t<PERSON>h", "idCardTypeId": "<PERSON><PERSON><PERSON> lo<PERSON>i gi<PERSON>y tờ tùy thân", "residentStatus": "<PERSON><PERSON><PERSON> tình trạng cư trú", "maritalStatus": "<PERSON><PERSON><PERSON> tình trạng hôn nhân", "select": "<PERSON><PERSON><PERSON> {{params}}", "input": "Nhập {{params}}"}}, "appError": {"msg": "Lỗi", "formInvalid": "<PERSON><PERSON> liệu trên form không hợp lệ", "email": "<PERSON><PERSON> kh<PERSON>ng đúng định dạng", "phoneNumber": "<PERSON><PERSON> điện tho<PERSON>i không đúng định dạng", "login": "<PERSON><PERSON><PERSON> thực thất bại, vui lòng thử lại", "editorMaxLength": "<PERSON><PERSON> dài {{fieldName}} ph<PERSON>i nhỏ hơn {{length}} kí tự", "required": "{{fieldName}} kh<PERSON><PERSON> đ<PERSON><PERSON>c để trống", "minlength": "{{fieldName}} có độ dài tối thiểu {{param}} ký tự", "maxlength": "{{fieldName}} có độ dài tối đa {{param}} ký tự", "pattern": "{{fieldName}} kh<PERSON>ng đúng định dạng", "min": "{{fieldName}} ph<PERSON>i lớn hơn {{param}}", "max": "{{fieldName}} tối đa là {{param}}", "warningChoiceFee": "Vui lòng chọn thông tin bên thuê!", "auth": "Bạn không có quyền vào màn hình này", "minPrice": "<PERSON><PERSON><PERSON> thấp nhất ph<PERSON>i nhỏ hơn 99.999.999.999.999", "maxPrice": "<PERSON><PERSON><PERSON> cao nhất ph<PERSON>i nhỏ hơn 99.999.999.999.999", "listPrice": "<PERSON><PERSON><PERSON> yết ph<PERSON>i nhỏ hơn 99.999.999.999.999"}, "public": {"resend": "<PERSON><PERSON><PERSON> lại đường dẫn kích hoạt", "resendLink": "Resend Link", "titleResend": "<p class=\"title\">Link kích hoạt đã <span class=\"text-primary-red\" style=\"color: red\">hết hạn</span> vui lòng nhấn 'Resend link' để nhận link kích hoạt tài khoản mới.</p>", "titleResendPw": "<p class=\"title\"><PERSON> kích hoạt đã <span class=\"text-primary-red\" style=\"color: red\">hết hạn</span> vui lòng click 'Resend link' để nhận link kích hoạt mới.</p>", "titleResendSuccess": "Resend link kích ho<PERSON>t tài k<PERSON>ản thành công \n Vui lòng kiểm tra Email.", "titleActive": "Bạn đã kích hoạt tài khoản thành công vui lòng kiểm tra thông tin gửi về Email.", "forgotPassword": {"title": "<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "titleChangePassword": "<PERSON><PERSON><PERSON><PERSON> lập mật kh<PERSON>u", "footerChangePassword": "<i> <PERSON><PERSON><PERSON> khẩu mới chứa 8 đến 20 ký tự </i><p><i> <PERSON><PERSON> gồ<PERSON> chữ hoa, chữ thường, số và các ký tự đặc biệt </i></p>", "confirmPassword": "<PERSON><PERSON><PERSON> nh<PERSON>n mật khẩu mới", "password": "<PERSON><PERSON><PERSON> mới", "email": "Email", "placeholder": {"email": "<PERSON><PERSON><PERSON><PERSON> email đúng định dạng @mbbank.com.vn"}, "success": {"title": "<PERSON><PERSON> lòng kiểm tra thông tin đã đư<PERSON><PERSON> gửi đến Email. <PERSON><PERSON>n kết hết hạn trong", "titleChangePassword": "Bạn đã đổi mật khẩu thành công \n Vui lòng đăng nhập lại bằng mật khẩu mới"}, "error": {"titleChangePassword": "<PERSON><PERSON> xảy ra lỗi trong khi thực thi. <PERSON><PERSON> lòng thử lại", "titleExpireSetupPw": "<PERSON><PERSON><PERSON>h<PERSON> hết hạn", "contentExpireSetupPw": "<PERSON><PERSON><PERSON> kết đã hết hạn, vui lòng nhấn xác nhận để thao tác lại.", "notMatch": "<PERSON><PERSON><PERSON> kh<PERSON>u không trùng nhau", "required": {"email": "<PERSON><PERSON> kh<PERSON>ng đư<PERSON><PERSON> để trống", "password": "<PERSON><PERSON><PERSON> kh<PERSON>u không đư<PERSON><PERSON> để trống", "confirmPassword": "<PERSON><PERSON><PERSON> nhận mật khẩu không đư<PERSON><PERSON> để trống"}, "pattern": {"email": "<PERSON><PERSON> kh<PERSON>ng đúng định dạng", "password": "<PERSON><PERSON><PERSON> kh<PERSON>u không đúng định dạng", "confirmPassword": "<PERSON><PERSON><PERSON> nhận mật khẩu không đúng định dạng"}, "minLength": {"password": "<PERSON><PERSON>t khẩu phải có ít nhất {{param}} ký tự", "confirmPassword": "<PERSON><PERSON><PERSON> nh<PERSON>n mật khẩu phải có ít nhất {{param}} ký tự"}, "maxLength": {"email": "<PERSON><PERSON> tối đa {{param}} ký tự", "password": "<PERSON><PERSON><PERSON> kh<PERSON>u tối đa {{param}} ký tự", "confirmPassword": "<PERSON><PERSON><PERSON> nh<PERSON>n mật khẩu tối đa {{param}} ký tự"}}}}, "login": {"loginButton": "<PERSON><PERSON><PERSON>", "userName": "<PERSON><PERSON><PERSON> đ<PERSON>p", "password": "<PERSON><PERSON><PERSON>", "captcha": "<PERSON><PERSON> x<PERSON>n", "error": {"userNameEmpty": "<PERSON><PERSON><PERSON> đăng nhập không được bỏ trống", "userNameMaxLength": "<PERSON><PERSON><PERSON> đ<PERSON>ng nhập không dài quá 75 ký tự", "userNameMinLength": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> nhập tối thiểu 3 ký tự", "passwordEmpty": "<PERSON><PERSON><PERSON> khẩu không được bỏ trống", "passwordInvalid": "<PERSON><PERSON><PERSON> kh<PERSON>u không đúng định dạng", "captchaEmpty": "<PERSON><PERSON> xác nhận không được bỏ trống"}}, "sidebar": {"manageSaving": {"root": "<PERSON><PERSON><PERSON><PERSON> lý tiết kiệm", "detail": "<PERSON> tiết tài khoản tiết kiệm", "tab": {"detailSaving": "<PERSON> tiết tài khoản tiết kiệm", "detailTransaciton": "<PERSON><PERSON><PERSON> sử giao dịch"}}, "configSaving": {"root": "<PERSON><PERSON><PERSON><PERSON> lý cấu hình kỳ hạn tiết kiệm"}, "news": {"root": "<PERSON><PERSON><PERSON><PERSON> lý tin tức"}, "feeSchedule": {"root": "<PERSON><PERSON><PERSON><PERSON> lý biểu phí"}, "manageTransQuantity": {"root": "<PERSON><PERSON><PERSON><PERSON> lý số lượng giao dịch"}, "managePayment": {"root": "<PERSON><PERSON><PERSON><PERSON> lý d<PERSON> v<PERSON> chi hộ"}, "debitAccount": {"root": "<PERSON><PERSON><PERSON><PERSON> lý thu chi hộ <PERSON>", "debit": "<PERSON><PERSON><PERSON><PERSON> lý tài khoản chuyên chi <PERSON>", "historyTransfer": "<PERSON><PERSON><PERSON><PERSON> lý lịch sử giao dịch thu chi hộ <PERSON>", "report": "<PERSON><PERSON><PERSON><PERSON> lý b<PERSON><PERSON> c<PERSON><PERSON>"}, "smsManage": {"root": "<PERSON><PERSON><PERSON><PERSON> lý b<PERSON>o c<PERSON>o <PERSON>"}, "configTransaction": {"root": "<PERSON><PERSON><PERSON><PERSON> lý cấu hình hạn mức giao dịch"}, "insuranceManage": {"root": "<PERSON><PERSON><PERSON><PERSON> lý b<PERSON><PERSON>"}, "structureAccount": {"root": "<PERSON><PERSON><PERSON> trúc số đẹp"}, "specialAccountNumber": {"root": "Số đẹp đặc biệt"}, "premiumAccountNumber": {"root": "<PERSON><PERSON><PERSON><PERSON> lý số đẹp", "title": "<PERSON><PERSON><PERSON>n số đẹp", "sold": "Số đẹp đã bán", "search": "<PERSON><PERSON> c<PERSON>u số đẹp"}, "premiumAccountNumberInterest": {"root": "<PERSON><PERSON><PERSON>n số đẹp", "create": "Mở tài kho<PERSON>n số đẹp có TKTT"}, "premiumAccountNumberHasPaymentAccount": {"root": "Mở TKSĐ khi đã có tài k<PERSON>ản", "create": "Mở tài khoản số đẹp khi đã có tài khoản thanh toán"}, "managementServiceFeeFailed": {"root": "<PERSON><PERSON><PERSON><PERSON> lý thu phí dịch vụ thất bại", "title": "<PERSON><PERSON><PERSON><PERSON> lý thu phí dịch vụ SMS biến động số dư thất bại"}, "versionManage": {"root": "<PERSON><PERSON><PERSON><PERSON> lý phiên bản", "sub-tab": {"android": {"root": "Android"}, "ios": {"root": "IOS"}}}, "dashboard": "Trang chủ", "sysManage": {"root": "<PERSON><PERSON><PERSON><PERSON> lý hệ thống", "setting": "<PERSON><PERSON><PERSON> hình hệ thống", "backgroundLogin": "<PERSON><PERSON><PERSON><PERSON> lý hình nền đăng nhập", "customerSupport": "<PERSON><PERSON><PERSON><PERSON> lý thông tin hỗ trợ khách hàng", "role": {"root": "<PERSON><PERSON><PERSON><PERSON> lý vai trò", "create": "T<PERSON>o mới vai trò", "update": "<PERSON><PERSON><PERSON> nhật vai trò", "detail": "<PERSON> tiết vai trò"}, "errorCode": {"root": "<PERSON><PERSON><PERSON><PERSON> lý mã lỗi", "create": "Tạo mới mã lỗi", "update": "<PERSON><PERSON><PERSON> nhật mã lỗi", "detail": "<PERSON> tiết mã lỗi"}}, "department": {"root": "<PERSON><PERSON><PERSON><PERSON> lý phòng ban", "create": "<PERSON><PERSON>o mới phòng ban", "update": "chỉnh sửa phòng ban", "detail": "<PERSON> tiết phòng ban"}, "informationTemplate": {"root": "<PERSON><PERSON><PERSON><PERSON> lý thông tin hỗ trợ"}, "customer": {"root": "<PERSON><PERSON><PERSON><PERSON> lý kh<PERSON>ch hàng", "registration": {"root": "<PERSON><PERSON><PERSON>n lý đăng ký", "register": "<PERSON><PERSON><PERSON> ký khách hàng", "update": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin khách hàng", "detail": "Thông tin chi tiết khách hàng"}, "approve": {"root": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON>ê <PERSON>", "manage": "<PERSON><PERSON><PERSON><PERSON> lý thông tin khách hàng", "view": "<PERSON><PERSON> thông tin khách hàng"}, "accountNoTransaction": {"root": "<PERSON><PERSON><PERSON><PERSON> lý tài khoản không phát sinh giao dịch", "title": "Quản lý TK không phát sinh GD", "view": "<PERSON> ti<PERSON>"}, "activateAccountManual": {"root": "<PERSON><PERSON><PERSON><PERSON> lý kích hoạt tài khoản thủ công", "title": "<PERSON><PERSON><PERSON><PERSON> lý kích hoạt TK thủ công"}, "dotpManagement": {"root": "Q<PERSON>ản lý tài khoản đăng ký DOTP"}}, "campaign": {"root": "<PERSON><PERSON><PERSON><PERSON> lý chiến dịch", "create": "<PERSON><PERSON><PERSON> mới chiến d<PERSON>ch", "update": "<PERSON><PERSON><PERSON> nhật thông tin chiến dịch", "detail": "Th<PERSON>ng tin chi tiết chiến dịch", "addBanner": "<PERSON><PERSON><PERSON><PERSON> banner"}, "loanOnline": {"root": "<PERSON><PERSON><PERSON><PERSON> lý vay trự<PERSON> tuyến", "detail": "<PERSON> tiết đăng ký vay trự<PERSON> tuyến"}, "notification": {"root": "<PERSON><PERSON><PERSON><PERSON> lý thông báo", "detail": "<PERSON> tiết thông báo"}, "fee": {"root": "<PERSON><PERSON><PERSON> hình phí giao dịch", "fee-rate": "<PERSON><PERSON><PERSON> hình biểu phí giao dịch"}, "bank": {"root": "<PERSON><PERSON><PERSON><PERSON> lý ngân hàng thụ hưởng", "create": "Tạo mới ngân hàng thụ hưởng", "update": "<PERSON><PERSON><PERSON> nhật ngân hàng thụ hưởng", "detail": "<PERSON> tiết ngân hàng thụ hưởng"}, "user": {"root": "<PERSON><PERSON><PERSON><PERSON> lý ng<PERSON><PERSON> dùng"}, "profile": "Thông tin tài k<PERSON>n", "position": "<PERSON><PERSON><PERSON><PERSON> lý ch<PERSON> danh", "merchant": {"root": "<PERSON><PERSON><PERSON><PERSON>", "create": "Tạo mới Merchant", "update": "<PERSON><PERSON><PERSON>", "detail": "<PERSON> ti<PERSON>", "infor": "Thông tin Merchant", "inforMasterMerchant": "Thông tin Master Merchant", "listChilMerchant": "<PERSON><PERSON> con", "transactionHistory": {"root": "<PERSON><PERSON><PERSON><PERSON> lý l<PERSON>ch sử giao dịch", "code": "Mã giao d<PERSON>ch", "name": "<PERSON><PERSON><PERSON> tà<PERSON>", "transactionDate": "<PERSON><PERSON><PERSON> giao d<PERSON>", "transactionOther": "<PERSON><PERSON><PERSON><PERSON> lý giao d<PERSON>", "transactionUmoney": "<PERSON><PERSON><PERSON><PERSON> lý giao d<PERSON><PERSON>", "transactionMMoney": "<PERSON><PERSON><PERSON><PERSON> lý thanh toán điện nước", "internationalTransaction": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON>h toán xuyên biên gi<PERSON>i"}, "masterMerchant": {"root": "<PERSON><PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON> mới Master Merchant", "update": "<PERSON><PERSON><PERSON> Master Merchant", "detail": "<PERSON> ti<PERSON>t Master Merchant"}}, "lapnet": {"root": "<PERSON><PERSON><PERSON><PERSON> lý b<PERSON>o c<PERSON>o lapnet"}, "referral": {"root": "<PERSON><PERSON><PERSON><PERSON> lý mã giới thiệu"}, "rate": {"root": "<PERSON><PERSON><PERSON><PERSON> lý lãi suất"}, "smsBalance": {"root": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> d<PERSON> v<PERSON> on/off sms BĐSD", "list": "<PERSON><PERSON><PERSON><PERSON> lý danh sách dịch vụ SMS biến động số dư"}, "transactionExceedLimit": {"root": "<PERSON><PERSON><PERSON><PERSON> lý giao d<PERSON>ch v<PERSON><PERSON><PERSON> hạn mức"}, "changePassword": "<PERSON>hay đ<PERSON>i mật kh<PERSON>u", "monitorLog": "<PERSON><PERSON> thống <PERSON> giao d<PERSON>ch", "transactionQrPay": "<PERSON><PERSON><PERSON><PERSON> lý giao d<PERSON>ch QRPAY", "numberGroup": "Nhóm số", "currency": "<PERSON><PERSON><PERSON><PERSON> lý tiền tệ"}, "error": {"manageTransQuantity": {"required": {"fromDate": "Từ ngày không đ<PERSON><PERSON><PERSON> để trống", "toDate": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống"}}, "managePayment": {"required": {"accountNumber": "S<PERSON> tài khoản không được bỏ trống", "accountName": "Tên tài khoản không được bỏ trống", "transactionLimit": " <PERSON><PERSON><PERSON> mức không được bỏ trống"}, "pattern": {"accountNumber": "Số tài khoản chỉ được nhập chữ cái, chữ số, tối thiểu 4 ký tự, tối đa 20 ký tự và không bao gồm ký tự đặc biệt", "transactionLimit": " <PERSON><PERSON><PERSON> mức chỉ được nhập chữ số"}, "length": {"accountNumber": "Số tài khoản có độ dài tối đa là 20 kí tự"}}, "sms": {"dateTime": {"fromDateCurrentInvalid": "Từ ngày không đư<PERSON><PERSON> lớn hơn ngày hiện tại", "toDateCurrentInvalid": "<PERSON><PERSON>n ngày không đ<PERSON><PERSON><PERSON> lớn hơn ngày hiện tại", "toDateInvalid": "<PERSON><PERSON>n ngày không được nhỏ hơn từ ngày"}}, "configTransaction": {"required": {"servicePackage": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> dịch vụ không đươc bỏ trống", "transType": "<PERSON><PERSON><PERSON> giao dịch không được bỏ trống", "sectorType": "Loại sector không được bỏ trống", "currency": "Đơn vị tiền tệ không được bỏ trống", "transMoney": "<PERSON><PERSON><PERSON> nhập đ<PERSON>y đủ hạn mức/giao dịch theo tất cả loại giao dịch", "startDate": "<PERSON><PERSON><PERSON> b<PERSON>t đầu không được bỏ trống", "endDate": "<PERSON><PERSON><PERSON> kết thúc không được bỏ trống", "transDay1890": "Hạn mức/ngày đối với sector 1890 không được bỏ trống", "transMonth1891": "Hạn mức/tháng đối với sector 1891 không được bỏ trống", "transDay1891": "Hạn mức/ngày đối với sector 1891 không được bỏ trống", "reason": "Lý do không đư<PERSON><PERSON> để trống"}, "dateTime": {"startDate": "<PERSON><PERSON><PERSON> b<PERSON>t đầu phải lớn hoặc bằng ngày hiện tại", "startDateTime": "<PERSON><PERSON><PERSON> b<PERSON>t đầu phải lớn hơn giờ hiện tại 2 phút", "endDate": "<PERSON><PERSON><PERSON> kết thúc phải lớn hơn ngày bắt đầu", "endDateEqual": "<PERSON><PERSON><PERSON> kết thúc phải lớn hơn ngày hiện tại", "dateEqual": "<PERSON><PERSON><PERSON> b<PERSON>t đầu phải nhỏ hơn ngày kết thúc"}, "invalidMoney": {"transDay": "<PERSON><PERSON><PERSON> mức/ng<PERSON><PERSON> không được bằng 0 hoặc bỏ trống", "transMonth": "<PERSON><PERSON><PERSON> mức/tháng không được bằng 0 hoặc bỏ trống", "transYear": "<PERSON><PERSON><PERSON> mức/năm không được bằng 0 ", "transMoney": "Tất cả hạn mức/giao dịch không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá hạn mức sector theo ng<PERSON>y", "transMoneyNone": "<PERSON><PERSON><PERSON> cả hạn mức/giao dịch không được bằng 0 hoặc bỏ trống"}, "pattern": {"servicePackage": "<PERSON>ên lo<PERSON>i dịch vụ không đư<PERSON><PERSON> bắt đầu bằng các kí tự -, +, =, @ hoặc các chuỗi 0x09 và 0x0D"}}, "insuranceManage": {"dateTime": {"invalidFromDateCurrent": "Từ ngày không đư<PERSON><PERSON> lớn hơn ngày hiện tại", "invalidToDateCurrent": "<PERSON><PERSON>n ngày không đ<PERSON><PERSON><PERSON> lớn hơn ngày hiện tại", "invalidToDate": "<PERSON><PERSON>n ngày không được nhỏ hơn từ ngày"}, "required": {"date": "<PERSON><PERSON><PERSON> tìm kiếm không được bỏ trống", "fromDate": "Từ ngày không được bỏ trống", "toDate": "<PERSON><PERSON><PERSON> ngày không được bỏ trống"}}, "versionManage": {"required": {"versionName": "Tên phiên bản không được bỏ trống", "versionCode": "<PERSON><PERSON> phiên bản không được bỏ trống", "updateType": "<PERSON><PERSON><PERSON> cập nhật không được bỏ trống, h<PERSON><PERSON> chọn ít nhất 1 loại cập nhật", "releaseDate": "<PERSON><PERSON><PERSON> giờ phát hành không được bỏ trống", "contentUpdate": "<PERSON><PERSON><PERSON> dung cập nhật không được bỏ trống"}, "length": {"versionName": "<PERSON>ên phiên bản chỉ tối đa 10 kí tự", "versionCode": "Mã code chỉ tối đa 10 kí tự", "versionContent": "<PERSON><PERSON><PERSON> dung cập nhật chỉ tối đa 500 kí tự"}, "pattern": {"versionName": "<PERSON><PERSON><PERSON> <PERSON><PERSON>ê<PERSON> bản chỉ chứa dấu chấm và chữ số, và có định dạng như: 1.1, 1.2, 2.1.2 ...", "versionCode": "<PERSON><PERSON> phiên bản chỉ chứa số", "updateTime": "<PERSON><PERSON><PERSON> cập nhật phải lớn hơn hoặc bằng giờ hiện tại", "updateDate": "<PERSON><PERSON><PERSON> cập nhật phải lớn hơn hoặc bằng ngày hiện tại"}, "dateTime": {"invalidTime": "Thời gian phát hành phải lớn hơn ít nhất 2 phút so với thời điểm hiện tại", "invalidDate": "<PERSON><PERSON><PERSON> phát hành phải lớn hơn ngày hiện tại", "invalidHours": "G<PERSON><PERSON> phát hành phải lớn hơn hoặc bằng giờ hiện tại", "invalidDateSearch": "<PERSON><PERSON>n ngày không được nhỏ hơn từ ngày", "invalidFromDate": "Từ ngày không đư<PERSON><PERSON> lớn hơn ngày hiện tại", "invalidToDate": "<PERSON><PERSON>n ngày không đ<PERSON><PERSON><PERSON> lớn hơn ngày hiện tại"}}, "common": "Lỗi không x<PERSON><PERSON> định, vui lòng liên hệ với quản trị viên để được hỗ trợ.", "label": "Lỗi", "formInvalid": "<PERSON><PERSON> liệu trên form không hợp lệ", "email": "<PERSON><PERSON> kh<PERSON>ng đúng định dạng", "phoneNumber": "<PERSON><PERSON> điện tho<PERSON>i không đúng định dạng", "cif": "Số CIF không đúng định dạng", "idCardNumber": "Số giấy tờ tùy thân không đúng định dạng", "login": "<PERSON><PERSON><PERSON> thực thất bại, vui lòng thử lại", "maxDateCurrent": "{{param}} kh<PERSON><PERSON> đ<PERSON><PERSON><PERSON> lớn hơn ngày hiện tại", "minDateCurrent": "{{param}} kh<PERSON><PERSON> đư<PERSON> nhỏ hơn ngày hiện tại", "editorMaxLength": "<PERSON><PERSON> dài {{fieldName}} ph<PERSON>i nhỏ hơn {{length}} kí tự", "required": {"fromDate": "Từ ngày không đ<PERSON><PERSON><PERSON> để trống", "toDate": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "fromDateExcel": "<PERSON><PERSON><PERSON> bắt đầu khi xuất excel không đư<PERSON><PERSON> để trống", "toDateExcel": "<PERSON><PERSON><PERSON> kết thúc khi xuất excel không được để trống", "inValidDate": "<PERSON><PERSON><PERSON><PERSON> thời gian tìm kiếm tối đa 90 ngày", "inValidDateExcel": "<PERSON><PERSON><PERSON><PERSON> thời gian tìm kiếm khi xuất excel tối đa 90 ngày"}, "toDateMustGreatherFromDate": "<PERSON><PERSON><PERSON> bắt đầu không đ<PERSON><PERSON><PERSON> lớn hơn ngày kết thúc", "startTimeMustGreatherEndTime": "<PERSON>hời gian bắt đầu hiệu lực không được lớn hơn thời gian kết thúc hiệu lực", "minLength": " độ dài tối thiểu {{param}} ký tự", "maxLength": " độ dài tối đa {{param}} ký tự", "pattern": " kh<PERSON><PERSON> đúng định dạng", "min": " tổi thiểu là {{param}}", "max": " tối đa là {{param}}", "customerCreate": {"required": {"idCardType": "<PERSON>ạ<PERSON> giấy tờ tùy thân không đư<PERSON><PERSON> để trống", "idCardNumber": "Số giấy tờ tùy thân không đư<PERSON><PERSON> để trống", "phoneNumber": "<PERSON><PERSON> điện tho<PERSON><PERSON> không đư<PERSON>c để trống", "fullname": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng không được để trống", "dob": "<PERSON><PERSON><PERSON> sinh không đư<PERSON><PERSON> để trống", "placeOfOrigin": "<PERSON><PERSON><PERSON> sinh không đ<PERSON><PERSON><PERSON> để trống", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h không đư<PERSON><PERSON> để trống", "nationCode": "<PERSON><PERSON><PERSON><PERSON> tị<PERSON> không đ<PERSON><PERSON><PERSON> để trống", "issueDate": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> không đư<PERSON><PERSON> để trống", "issuePlace": "<PERSON><PERSON><PERSON> cấp không đ<PERSON><PERSON><PERSON> để trống", "placeOfResidence": "Địa chỉ thường trú không được để trống", "currentAddress": "Đ<PERSON>a chỉ liên hệ hiện tại không đư<PERSON><PERSON> để trống", "phoneContact": "<PERSON><PERSON> điện thoại liên hệ không đư<PERSON><PERSON> để trống", "customerSectorId": "Tr<PERSON>ng thái hồ sơ định danh khách hàng không được để trống", "staffCode": "<PERSON>ã nhân viên không được để trống", "shopCode": "<PERSON><PERSON> quầy giao dịch không đ<PERSON><PERSON><PERSON> để trống", "idCardPic": "Ảnh giấy tờ tùy thân không được để trống", "picture": "Ảnh giấy tờ tùy thân không được để trống"}, "minLength": {"idCardNumber": "Số GTTT tối thiểu {{param}} kí tự", "phoneNumber": "<PERSON><PERSON> điện thoại tối thiểu {{param}} kí tự", "fullname": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng tối thiểu {{param}} kí tự", "placeOfOrigin": "<PERSON><PERSON><PERSON> sinh tối thiểu {{param}} kí tự", "statusOfResidence": "<PERSON><PERSON><PERSON> trạng cư trú tối thiểu {{param}} kí tự", "nationCode": "<PERSON><PERSON><PERSON><PERSON> tịch tối thiểu {{param}} kí tự", "nationCodeOther": "<PERSON><PERSON><PERSON><PERSON> tịch khác tối thiểu {{param}} kí tự", "issuePlace": "<PERSON><PERSON><PERSON> cấp tối thiểu {{param}} kí tự", "placeOfResidence": "Đ<PERSON><PERSON> chỉ thường trú tối thiểu {{param}} kí tự", "placeOfResidenceOutCountry": "Đ<PERSON>a chỉ thường trú ở nước ngoài tối thiểu {{param}} kí tự", "currentAddress": "<PERSON><PERSON><PERSON> chỉ liên hệ hiện tại tối thiểu {{param}} kí tự", "phoneContact": "<PERSON><PERSON> điện thoại liên hệ tối thiểu {{param}} kí tự", "email": "<PERSON><PERSON> tối thiểu {{param}} kí tự", "maritalStatus": "Tình trạng hôn nhân tối thiểu {{param}} kí tự", "job": "<PERSON><PERSON> nghiệp tối thiểu {{param}} kí tự", "position": "<PERSON><PERSON><PERSON> vụ tối thiểu {{param}} kí tự", "staffCode": "<PERSON>ã nhân viên tối thiểu {{param}} kí tự", "shopCode": "<PERSON><PERSON> qu<PERSON>y giao dịch tối thiểu {{param}} kí tự"}, "maxLength": {"idCardNumber": "Số GTTT tối đa {{param}} kí tự", "phoneNumber": "<PERSON><PERSON> điện thoại tối đa {{param}} kí tự", "fullname": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng tối đa {{param}} kí tự", "placeOfOrigin": "<PERSON><PERSON><PERSON> sinh tối đa {{param}} kí tự", "statusOfResidence": "<PERSON><PERSON><PERSON> trạng cư trú tối đa {{param}} kí tự", "nationCode": "<PERSON><PERSON><PERSON><PERSON> tịch tối đa {{param}} kí tự", "nationCodeOther": "<PERSON><PERSON><PERSON><PERSON> tịch khác tối đa {{param}} kí tự", "issuePlace": "<PERSON><PERSON><PERSON> cấp tối đa {{param}} kí tự", "placeOfResidence": "Địa chỉ thường trú tối đa {{param}} kí tự", "placeOfResidenceOutCountry": "Địa chỉ thường trú ở nước ngoài tối đa {{param}} kí tự", "currentAddress": "Đ<PERSON>a chỉ liên hệ hiện tại tối đa {{param}} kí tự", "phoneContact": "<PERSON><PERSON> điện thoại liên hệ tối đa {{param}} kí tự", "email": "<PERSON><PERSON> tối đa {{param}} kí tự", "maritalStatus": "<PERSON><PERSON>nh trạng hôn nhân tối đa {{param}} kí tự", "job": "<PERSON><PERSON> nghiệp tối đa {{param}} kí tự", "position": "<PERSON><PERSON><PERSON> vụ tối đa {{param}} kí tự", "staffCode": "<PERSON>ã nhân viên tối đa {{param}} kí tự", "shopCode": "<PERSON><PERSON> qu<PERSON>y giao dịch tối đa {{param}} kí tự", "workplace": "<PERSON><PERSON><PERSON> làm vi<PERSON>c tối đa {{param}} ký tự"}, "pattern": {"idCardNumber": "Số GTTT không được chứa kí tự đặc biệt", "phoneNumber": "<PERSON><PERSON> điện thoại chỉ được chứa các kí tự số, bắt đầu 0302, 0304,... hoặc 0202, 0205,...", "fullname": "<PERSON><PERSON><PERSON> kh<PERSON>ch không đúng định dạng", "dob": "<PERSON><PERSON><PERSON> sinh không đúng định dạng", "nationCode": "<PERSON><PERSON><PERSON><PERSON> tị<PERSON> không chứa kí tự đặc biệt", "nationCodeOther": "<PERSON><PERSON><PERSON><PERSON> tị<PERSON> khác không chứa kí tự đặc biệt", "issueDate": "<PERSON><PERSON><PERSON> c<PERSON>p không đúng định dạng", "phoneContact": "<PERSON><PERSON> điện thoại liên hệ chỉ chứa các kí tự số, bắt đầu bằng 0302, 0304,... hoặc 0202, 0205,...", "email": "<PERSON><PERSON> kh<PERSON>ng đúng định dạng", "staffCode": "<PERSON>ã nhân viên không chứa kí tự đặc biệt", "shopCode": "<PERSON><PERSON> quầy giao dịch không chứa kí tự đặc biệt", "dateOfBirth": "<PERSON><PERSON><PERSON> sinh của khách hàng tối thiểu 18 tuổi trở lên", "issueDateMin": "<PERSON><PERSON><PERSON> cấp gi<PERSON>y tờ tùy thân không đư<PERSON><PERSON> quá hạn"}}, "customerDetail": {"required": {"idCardType": "<PERSON>ạ<PERSON> giấy tờ tùy thân không đư<PERSON><PERSON> để trống", "idCardNumber": "Số giấy tờ tùy thân không đư<PERSON><PERSON> để trống", "phoneNumber": "<PERSON><PERSON> điện tho<PERSON><PERSON> không đư<PERSON>c để trống", "fullname": "<PERSON><PERSON> tên không đư<PERSON><PERSON> để trống", "username": "<PERSON><PERSON><PERSON> kh<PERSON>ng đ<PERSON><PERSON><PERSON> để trống", "dateOfBirth": "<PERSON><PERSON><PERSON> sinh không đư<PERSON><PERSON> để trống", "email": "<PERSON><PERSON> kh<PERSON>ng đư<PERSON><PERSON> để trống", "status": "Tr<PERSON>ng thái không được để trống", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h không đư<PERSON><PERSON> để trống", "issueDate": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> không đư<PERSON><PERSON> để trống", "issuePlace": "<PERSON><PERSON><PERSON> cấp gi<PERSON>y tờ tùy thân không đư<PERSON><PERSON> để trống", "placeOfOrigin": "<PERSON><PERSON><PERSON> sinh không đ<PERSON><PERSON><PERSON> để trống", "placeOfResidence": "Địa chỉ thường trú tại không được để trống", "nationCode": "<PERSON><PERSON> quốc gia không được để trống", "customerSectorId": "Customer's sector không được để trống", "cif": "Số CIF không được để trống", "limitPerTransaction": "<PERSON><PERSON><PERSON><PERSON> hạn mỗi lần giao dịch không đượ<PERSON> để trống", "limitPerDay": "<PERSON><PERSON><PERSON><PERSON> hạn giao dịch mỗi ngày không được để trống", "currentAddress": "Đ<PERSON>a chỉ liên hệ hiện tại không đư<PERSON><PERSON> để trống"}, "minLength": {"username": "<PERSON><PERSON><PERSON> đ<PERSON>ng nhập tối thiểu {{param}} ký tự", "fullname": "<PERSON><PERSON> tên tối thiểu {{param}} ký tự", "email": "<PERSON><PERSON> tối thiểu {{param}} ký tự", "phoneNumber": "SĐT tối thiểu {{param}} ký tự", "staffCode": "<PERSON>ã nhân viên tối thiểu {{param}} ký tự", "idCardNumber": "Số GTTT tối thiểu {{param}} ký tự", "issuePlace": "<PERSON><PERSON><PERSON> cấp tối thiểu {{param}} ký tự", "placeOfOrigin": "<PERSON><PERSON><PERSON> sinh tối thiểu {{param}} ký tự", "placeOfResidence": "Đ<PERSON><PERSON> chỉ thường trú tối thiểu {{param}} ký tự", "cif": "Số CIF tối thiểu {{param}} ký tự", "currentAddress": "<PERSON><PERSON><PERSON> chỉ liên hệ hiện tại tối thiểu {{param}} ký tự"}, "maxLength": {"username": "<PERSON>ê<PERSON> đăng nhập chỉ được tối đa {{param}} ký tự", "fullname": "<PERSON>ọ tên chỉ được tối đa {{param}} ký tự", "email": "Email chỉ đư<PERSON>c tối đa {{param}} ký tự", "phoneNumber": "SĐT chỉ được tối đa {{param}} ký tự", "staffCode": "<PERSON>ã nhân viên chỉ được tối đa {{param}} ký tự", "description": "<PERSON><PERSON> tả chỉ được tối đa {{param}} ký tự", "idCardNumber": "Số GTTT chỉ được tối đa {{param}} ký tự", "issuePlace": "<PERSON><PERSON><PERSON> cấp chỉ được tối đa {{param}} ký tự", "placeOfOrigin": "<PERSON><PERSON><PERSON> sinh chỉ được tối đa {{param}} ký tự", "placeOfResidence": "Địa chỉ thường trú chỉ được tối đa {{param}} ký tự", "cif": "Số CIF chỉ được tối đa {{param}} ký tự", "currentAddress": "Đ<PERSON>a chỉ liên hệ hiện tại chỉ được tối đa {{param}} ký tự"}, "pattern": {"username": "<PERSON><PERSON><PERSON> đ<PERSON>ng nhập không đúng định dạng", "fullname": "<PERSON><PERSON> tên không đúng định dạng", "dob": "<PERSON><PERSON><PERSON> sinh không đúng định dạng", "email": "<PERSON><PERSON> kh<PERSON>ng đúng định dạng", "phoneNumber": "SĐT chỉ có thể chứa các ký tự số, bắt đầu bằng 0302, 0304,... hoặc 0202, 0205,...", "staffCode": "<PERSON><PERSON> nhân viên chỉ có thể chứa các ký tự số, bắt đầu bằng 0302, 0304,... hoặc 0202, 0205,...", "idCardNumber": "Số GTTT không đư<PERSON><PERSON> chứa các ký tự đặc biệt"}}, "bank": {"required": {"bankCodeNumber": "Mã số không đư<PERSON>c để trống", "bankCode": "<PERSON>ã ngân hàng không đư<PERSON>c để trống", "order": "<PERSON><PERSON><PERSON> tự sắp xếp không đ<PERSON><PERSON><PERSON> để trống", "files": "Ảnh không đượ<PERSON> để trống", "status": "Tr<PERSON>ng thái <PERSON>ng được để trống", "bankName": "<PERSON>ê<PERSON> ngân hàng không đư<PERSON><PERSON> để trống"}, "maxLength": {"bankCodeNumber": "Mã số tối đa {{param}} ký tự", "bankCode": "<PERSON>ã ngân hàng tối đa {{param}} ký tự", "bankName": "<PERSON><PERSON><PERSON> ngân hàng tối đa {{param}} ký tự", "order": "<PERSON><PERSON><PERSON> tự sắp xếp lớn nhất {{param}}"}, "minLength": {"bankCodeNumber": "Mã số tối thiểu {{param}} ký tự", "bankCode": "<PERSON>ã ngân hàng tối thiểu {{param}} ký tự", "bankName": "<PERSON><PERSON><PERSON> ngân hàng tối thiểu {{param}} ký tự", "order": "<PERSON><PERSON><PERSON> tự sắp xếp nhỏ nhất {{param}}"}, "pattern": {"bankCodeNumber": "Mã số ngân hàng không đúng định dạng", "bankCode": "<PERSON>ã ngân hàng không đúng định dạng"}}, "campaign": {"required": {"campaignName": "<PERSON><PERSON><PERSON> chiến dịch không đư<PERSON><PERSON> để trống", "embedLinkImage": "Hãy upload đầy đủ link đính kèm và banner", "embedLink": "<PERSON> đ<PERSON>h kèm không được bỏ trống khi đã upload banner", "banners": "<PERSON> không đư<PERSON><PERSON> để trống", "startDateFrom": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu không đư<PERSON><PERSON> để trống", "endDateTo": "<PERSON><PERSON><PERSON> kết thúc không được để trống", "screenShow": "<PERSON>í trí hiển thị không được bỏ trống", "bannerScreen": "Hãy upload ít nhất 1 banner cho chiến dịch", "linkBanner": "Hãy upload link đính kèm với hình ảnh đã upload", "description": "<PERSON><PERSON> tả chiến dịch không được bỏ trống", "position": "Bắt bu<PERSON><PERSON> chọn 1 trong 2 vị trí để hiển thị chiến dịch"}, "maxLength": {"campaignName": "<PERSON><PERSON><PERSON> chi<PERSON>n dịch tối đa {{param}} kí tự", "description": "<PERSON><PERSON> tả tối đa {{param}} kí tự", "embedLink": "<PERSON> đ<PERSON>h kèm tối đa {{param}} kí tự"}, "pattern": {"campaignName": "<PERSON><PERSON><PERSON> chiến dịch không được bắt đầu bằng các kí tự -, +, =, @ hoặc các chuỗi 0x09 và 0x0D", "embedLink": "<PERSON> đ<PERSON> kèm ph<PERSON>i có dịnh dạng http://abc., https://abc."}}, "role": {"maxLength": {"description": "<PERSON><PERSON> dài mô tả phải nhỏ hơn {{length}} kí tự", "name": "<PERSON><PERSON> dài tên vai trò phải nhỏ hơn {{length}} kí tự"}, "required": {"name": "Tên vai trò không đư<PERSON><PERSON> để trống"}, "pattern": {"name": "Tên vai trò không được chưa kí tự đặc biệt"}}, "event": {"required": {"title": "Tiêu đề không được để trống", "content": "<PERSON><PERSON><PERSON> dung không đư<PERSON><PERSON> để trống", "announcementTypeId": "<PERSON><PERSON> loại thông báo không đư<PERSON>c để trống", "expectedNotificationAt": "Thời gian gửi không được để trống"}, "maxLength": {"title": "Ti<PERSON>u đề tối đa {{param}} kí tự", "content": "Nội dung tối đa 1500 kí tự"}}, "user": {"required": {"phoneNumber": "<PERSON><PERSON> điện tho<PERSON><PERSON> không đư<PERSON>c để trống", "fullname": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng không được để trống", "dob": "<PERSON><PERSON><PERSON> sinh không đư<PERSON><PERSON> để trống", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h không đư<PERSON><PERSON> để trống", "department": "Phòng ban không được để trống", "position": "<PERSON><PERSON><PERSON> danh không đ<PERSON><PERSON><PERSON> để trống", "role": "<PERSON><PERSON> trò không đư<PERSON><PERSON> để trống", "email": "<PERSON><PERSON> kh<PERSON>ng đư<PERSON><PERSON> để trống", "currentPassword": "<PERSON><PERSON><PERSON> kh<PERSON>u hiện tại không đư<PERSON><PERSON> để trống", "newPassword": "<PERSON><PERSON><PERSON> khẩu mới không được để trống", "confirmPassword": "<PERSON><PERSON><PERSON> nhận mật khẩu mới không đư<PERSON><PERSON> để trống", "reason": "Lý do không đư<PERSON><PERSON> để trống", "phoneOrFullName": "<PERSON><PERSON> điện thoại hoặc họ tên không được để trống"}, "minLength": {"phoneNumber": "<PERSON><PERSON> điện thoại tối thiểu {{param}} kí tự", "fullname": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng tối thiểu {{param}} kí tự", "email": "<PERSON><PERSON> tối thiểu {{param}} kí tự", "newPassword": "<PERSON><PERSON><PERSON> kh<PERSON>u mới tối thiểu {{param}} ký tự", "confirmPassword": "<PERSON><PERSON><PERSON>n mật khẩu mới tối thiểu {{param}} ký tự"}, "maxLength": {"phoneNumber": "<PERSON><PERSON> điện thoại tối đa {{param}} kí tự", "fullname": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng tối đa {{param}} kí tự", "email": "<PERSON><PERSON> tối đa {{param}} kí tự", "newPassword": "<PERSON><PERSON><PERSON> kh<PERSON>u mới tối đa {{param}} ký tự", "confirmPassword": "<PERSON><PERSON><PERSON>h<PERSON>n mật khẩu mới tối đa {{param}} ký tự", "reason": "<PERSON>ý do tối đa {{param}} ký tự"}, "pattern": {"phoneNumber": "<PERSON><PERSON> điện thoại chỉ được chứa các kí tự số, bắt đầu 0302, 0304,... hoặc 0202, 0205,...", "fullname": "<PERSON><PERSON><PERSON> kh<PERSON><PERSON> hàng chỉ chứa kí tự viết thường, viế<PERSON> hoa", "dob": "<PERSON><PERSON><PERSON> sinh không đúng định dạng", "email": "<PERSON><PERSON> kh<PERSON>ng đúng định dạng", "newPassword": "<PERSON><PERSON><PERSON> khẩu mới không đúng định dạng", "confirmPassword": "<PERSON><PERSON><PERSON> nhận mật khẩu mới không đúng định dạng"}}, "position": {"required": {"positionName": "<PERSON><PERSON><PERSON> chức danh không đ<PERSON><PERSON><PERSON> để trống", "shortName": "<PERSON><PERSON><PERSON> viết tắt không được để trống", "positionCode": "<PERSON><PERSON> chức danh không đ<PERSON><PERSON><PERSON> để trống"}, "minLength": {"shortName": "<PERSON><PERSON><PERSON> vi<PERSON>t tắt tối thiểu {{param}} kí tự"}, "maxLength": {"positionName": "<PERSON><PERSON><PERSON> chức danh tối đa {{param}} kí tự", "shortName": "<PERSON><PERSON><PERSON> vi<PERSON>t tắt tối đa {{param}} kí tự", "positionCode": "<PERSON><PERSON> chức danh tối đa {{param}} kí tự", "description": "<PERSON><PERSON> tả tối đa {{param}} kí tự"}, "pattern": {"positionName": "<PERSON><PERSON><PERSON> chức danh chỉ bao gồm chữ cái, chữ số và dấu cách", "shortName": "<PERSON><PERSON><PERSON> viết tắt chức danh chỉ bao gồm chữ cái, chữ số và dấu cách"}}, "referral": {"required": {"userFullName": "<PERSON><PERSON> và tên không đư<PERSON>c để trống", "phoneNumber": "<PERSON><PERSON> điên tho<PERSON>i không được để trống", "type": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "userCode": "<PERSON>ã nhân viên không được để trống", "rmCode": "Mã RM không được để trống"}, "minLength": {"phoneNumber": "<PERSON><PERSON> điện thoại tối thiểu {{param}} kí tự", "userCode": "<PERSON>ã nhân viên tối thiểu {{param}} kí tự", "userFullName": "Họ và tên tối thiểu {{param}} kí tự", "rmCode": "Mã RM tối thiểu {{param}} kí tự"}, "maxLength": {"userFullName": "<PERSON><PERSON> và tên tối đa {{param}} kí tự", "phoneNumber": "<PERSON><PERSON> điện thoại tối đa {{param}} kí tự", "select": "<PERSON><PERSON>i tối đa {{param}} kí tự", "userCode": "<PERSON>ã nhân viên tối đa {{param}} kí tự", "rmCode": "Mã RM tối đa {{param}} kí tự"}, "pattern": {"userFullName": "Họ và tên chỉ bao gồm chữ cái và dấu cách", "phoneNumber": "SĐT chỉ có thể chứa các ký tự số, bắt đầu bằng 0302, 0304,... hoặc 0202, 0205,..."}}, "merchant": {"required": {"merchantName": "<PERSON><PERSON>n <PERSON> không đư<PERSON><PERSON> để trống", "merchantCode": "<PERSON><PERSON> <PERSON> không đư<PERSON><PERSON> để trống", "masterMerchantCode": "Mã Master Merchant kh<PERSON>ng đư<PERSON><PERSON> để trống", "masterMerchantName": "Tên Master Merchant không đư<PERSON><PERSON> để trống", "merchantAccountNumber": "Số tài khoản không được để trống", "serviceType": "<PERSON><PERSON><PERSON> dịch vụ không được bỏ trống"}, "minLength": {"merchantCode": "<PERSON>ã Merchant tối thiểu {{param}} kí tự", "masterMerchantCode": "Mã Master Merchant tối thiểu {{param}} kí tự"}, "maxLength": {"merchantCode": "<PERSON><PERSON> Merchant tối đa {{param}} kí tự", "merchantName": "<PERSON><PERSON><PERSON> tối đa {{param}} kí tự", "description": "<PERSON><PERSON> tả tối đa {{param}} kí tự", "masterMerchantCode": "Mã Master Merchant tối đa {{param}} kí tự", "masterMerchantName": "Tên Master Merchant tối đa {{param}} kí tự", "merchantAccountNumber": "S<PERSON> tài khoản không tối đa {{param}} kí tự"}, "pattern": {"merchantCode": "Mã <PERSON> không đúng định dạng", "masterMerchantCode": "Mã master Merchant k<PERSON><PERSON><PERSON> đ<PERSON>g đ<PERSON>nh dạng", "merchantName": "<PERSON><PERSON><PERSON> chỉ bao gồm chữ cái, chữ số và dấu cách", "masterMerchantName": "Tên Master Merchant chỉ bao gồm chữ cái, chữ số và dấu cách", "merchantAccountNumber": "Số tài khoản chỉ được nhập chữ cái, chữ số, tối thiểu 4 ký tự, tối đa 50 ký tự và không bao gồm ký tự đặc biệt"}}, "feeRate": {"required": {"feeRateName": "Tên biểu phí không được để trống", "transactionAmountMin": "<PERSON><PERSON> tiền giao dịch nhỏ nhất không được để trống", "transactionAmountMax": "Số tiền giao dịch lớn nhất không đư<PERSON><PERSON> để trống", "discountPercent": "% chiết khấu không được để trống và phải lớn hơn 0", "vat": "VAT không đư<PERSON><PERSON> để trống", "effectiveAt": "<PERSON>h<PERSON><PERSON> gian b<PERSON>t đầu hiệu lực không đư<PERSON><PERSON> để trống", "expiredAt": "<PERSON>h<PERSON><PERSON> gian kết thúc hiệu lực không đượ<PERSON> để trống", "feeAmount": "Số tiền phí không được để trống", "transactionAmountMinLength": "Số tiền giao dịch tối thiểu không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 999.999.999.999.999", "transactionAmountMaxLength": "Số tiền giao dịch tối đa không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 999.999.999.999.999", "amountMinMustSmallerAmountMax": "Số tiền giao dịch tối thiểu phải nhỏ hơn số tiền giao dịch tối đa", "amountFeeMustSmallerAmountMax": "Số tiền phí sau VAT phải nhỏ hớn số tiền giao dịch tối đa", "vatMinLength": "VAT tối đa 100%", "vatMin": "VAT tối thiểu 0%", "discountPercentMinLength": "Phần tr<PERSON>m chiết khấu phải nhỏ hơn 100"}, "maxLength": {"feeRateName": "Tên biểu phí tối đa {{param}} ký tự"}, "min": {"transactionAmountMax": "Số tiền giao dịch lớn nhất phải lớn hơn 0"}}, "fee": {"required": {"feeName": "Tên biểu phí không được để trống", "status": "Bạn phải chọn trạng thái", "transactionFeeType": "Bạn ph<PERSON>i chọn loại dịch vụ", "merchantFeeType": "Bạn ph<PERSON>i chọn lo<PERSON>i merchant"}, "maxLength": {"feeName": "<PERSON><PERSON><PERSON> cấu hình phí tối đa {{param}} kí tự"}}}, "customerRegisterManagement": {"createTitle": "<PERSON><PERSON><PERSON> mới kh<PERSON>ch hàng", "detailTitle": "Thông tin chi tiết khách hàng", "updateTitle": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin khách hàng", "no": "STT", "cif": "Số CIF", "cifNumber": "Số CIF", "fullname": "<PERSON><PERSON><PERSON> h<PERSON>ng", "username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "email": "Email", "nationCode": "<PERSON> n<PERSON>h", "issueDate": "<PERSON><PERSON><PERSON> ký", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "phoneNumberSearch": "SĐT Nhận OTP", "identifyNumber": "Số CMTND", "fromDate": "<PERSON><PERSON> ngày", "toDate": "<PERSON><PERSON><PERSON>", "addButton": "<PERSON><PERSON><PERSON><PERSON>", "searchButton": "<PERSON><PERSON><PERSON>", "cancelButton": "<PERSON><PERSON><PERSON><PERSON>", "deleteButton": "Xóa", "lockButton": "Khóa", "idCardNumber": "Số GTTT", "dateOfBirth": "<PERSON><PERSON><PERSON>", "lock": "<PERSON><PERSON><PERSON> nh<PERSON>n khóa tài khoản khách hàng", "unlock": "<PERSON><PERSON><PERSON> nh<PERSON>n mở khóa tài khoản khách hàng", "deleteApproval": "<PERSON><PERSON><PERSON>n x<PERSON>a yêu c<PERSON>u <PERSON>", "cancel": "<PERSON><PERSON><PERSON> nh<PERSON>n đóng tài kho<PERSON>n khách hàng", "unclosed": "<PERSON><PERSON><PERSON> nhận mở đóng tài khoản khách hàng", "update": "<PERSON><PERSON><PERSON> nhận cập nhật tài kho<PERSON>n khách hàng", "create": "<PERSON><PERSON><PERSON> nhận tạo mới tài khoản khách hàng", "lockCustomerRegisterContent": "Bạn có chắc chắn muốn gửi yêu cầu khoá tài khoản khách hàng {{fullname}} không?", "unlockCustomerRegisterContent": "Bạn có chắc chắn muốn gửi yêu cầu mở khoá tài khoản khách hàng {{fullname}} không?", "deleteCustomerApproval": "Bạn có chắc chắn muốn xóa yêu cầu {{approvalType}} kho<PERSON>n khách hàng {{fullname}} không?", "cancelCustomerRegisterContent": "Bạn có chắc chắn muốn gửi yêu cầu đóng tài khoản khách hàng {{fullname}} không?", "unclosedCustomerRegisterContent": "Bạn có chắc chắn muốn gửi yêu cầu mở đóng tài khoản khách hàng {{fullname}} không?", "createCustomerRegisterContent": "Bạn có chắc chắn muốn gửi yêu cầu tạo mới tài khoản khách hàng {{fullname}} không?", "updateCustomerRegisterContent": "<PERSON><PERSON>n có chắc chắn muốn gửi yêu cầu cập nhật tài khoản khách hàng {{fullname}} không?", "resetPassword": "<PERSON><PERSON><PERSON> nhận đổi lại mật khẩu tài khoản khách hàng", "resetPasswordCustomerContent": "Bạn có chắc chắn muốn đổi lại mật khẩu tài khoản khách hàng {{fullname}} không?", "currency": "Đơn vị tiền tệ", "paymentAccount": "<PERSON><PERSON><PERSON>h toán", "overdraftAccount": "<PERSON><PERSON><PERSON> k<PERSON>n thấu chi", "sector": "Sector", "customerCreate": {"title": "<PERSON><PERSON><PERSON> mới kh<PERSON>ch hàng", "info": "Thông tin khách hàng", "search": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "researchConfirmTitle": "<PERSON><PERSON><PERSON>h<PERSON>n tra c<PERSON>u lại", "researchConfirmContent": "Thông tin khách hàng bạn nhập có thể sẽ không đư<PERSON><PERSON> lưu, bạn có chắc chắn muốn tra cứu lại?", "alertDataNotExisted": "Thông tin khách hàng chưa đ<PERSON><PERSON><PERSON> sử dụng", "existedT24Warning": "<PERSON><PERSON><PERSON><PERSON> hàng đã có tài khoản trên hệ thống T24", "warningPremiumAcc": "<span class=\"text-primary-color\">Chú ý: <PERSON><PERSON><PERSON><PERSON> hàng cần nhập OTP SMS khi mở tài khoản số đẹp</span>"}, "customerDetail": {"title": "<PERSON> tiết thông tin khách hàng", "info": "Thông tin khách hàng", "default": "Mặc định", "editButton": "Chỉnh sửa", "backButton": "Quay lại"}, "customerApproval": {"approvalTitle": "<PERSON><PERSON> kh<PERSON><PERSON> hàng", "detailTitle": "<PERSON> tiết kh<PERSON>ch hàng", "approvalStatus": "<PERSON><PERSON><PERSON><PERSON> thái", "approvalType": "<PERSON><PERSON> lo<PERSON>", "lockApproval": "<PERSON><PERSON><PERSON> nhận phê duyệt khóa tài khoản khách hàng", "unlockApproval": "<PERSON><PERSON><PERSON> nhận phê duyệt mở khóa tài khoản khách hàng", "deleteApproval": "<PERSON><PERSON><PERSON> nhận phê duyệt xóa tài khoản khách hàng", "createApproval": "<PERSON><PERSON><PERSON> nhận phê duyệt tạo mới tài khoản khách hàng", "updateApproval": "<PERSON><PERSON><PERSON> nhận phê duyệt cập nhật tài kho<PERSON>n khách hàng", "unclosedApproval": "<PERSON><PERSON><PERSON> nhận phê duyệt mở đóng tài khoản khách hàng", "lockDeny": "<PERSON><PERSON><PERSON> nhận từ chối phê duyệt khóa tài khoản khách hàng", "unlockDeny": "<PERSON><PERSON><PERSON> nhận từ chối phê duyệt mở khóa tài khoản khách hàng", "unclosedDeny": "<PERSON><PERSON><PERSON> nhận từ chối phê duyệt mở đóng tài khoản khách hàng", "deleteDeny": "<PERSON><PERSON><PERSON> nhận từ chối phê duyệt xóa tài kho<PERSON>n khách hàng", "createDeny": "<PERSON><PERSON><PERSON> nhận từ chối phê duyệt tạo mới tài khoản khách hàng", "updateDeny": "<PERSON><PERSON><PERSON> nhận từ chối phê duyệt cập nhật tài kho<PERSON>n khách hàng", "lockApprovalContent": "Bạn có chắc chắn muốn phê duyệt khoá tài khoản khách hàng {{fullname}} không?", "unlockApprovalContent": "Bạn có chắc chắn muốn phê duyệt mở khoá tài khoản khách hàng {{fullname}} không?", "deleteApprovalContent": "Bạn có chắc chắn muốn phê duyệt xóa tài khoản khách hàng {{fullname}} không?", "createApprovalContent": "Bạn có chắc chắn muốn phê duyệt tạo mới khách hàng {{fullname}} không?", "updateApprovalContent": "Bạn có chắc chắn muốn phê duyệt cập nhật khách hàng {{fullname}} không?", "unclosedApprovalContent": "Bạn có chắc chắn muốn phê duyệt mở đóng khách hàng {{fullname}} không?", "lockDenyContent": "Bạn có chắc chắn muốn từ chối phê duyệt khoá tài khoản khách hàng {{fullname}} không?", "unlockDenyContent": "Bạn có chắc chắn muốn từ chối phê duyệt mở khoá tài khoản khách hàng {{fullname}} không?", "unclosedDenyContent": "Bạn có chắc chắn muốn từ chối phê duyệt mở đóng tài khoản khách hàng {{fullname}} không?", "deleteDenyContent": "Bạn có chắc chắn muốn từ chối phê duyệt xóa tài khoản khách hàng {{fullname}} không?", "createDenyContent": "Bạn có chắc chắn muốn từ chối phê duyệt tạo mới khách hàng {{fullname}} không?", "updateDenyContent": "Bạn có chắc chắn muốn từ chối phê duyệt cập nhật khách hàng {{fullname}} không?", "success": {"lockApproval": "<PERSON><PERSON> khóa tài khoản khách hàng thành công", "unlockApproval": "<PERSON><PERSON> du<PERSON> mở khóa tài khoản khách hàng thành công", "deleteApproval": "<PERSON><PERSON> du<PERSON>t xóa tài kho<PERSON>n khách hàng thành công", "createApproval": "<PERSON><PERSON> du<PERSON>t tạo mới tài khoản khách hàng thành công", "updateApproval": "<PERSON><PERSON> cập nhật tài kho<PERSON>n khách hàng thành công", "unclosedApproval": "<PERSON><PERSON> duy<PERSON>t mở đóng tài khoản khách hàng thành công", "deny": {"lockDeny": "Từ chối khóa tài khoản khách hàng thành công", "unlockDeny": "Từ chối mở khóa tài khoản khách hàng thành công", "unclosedDeny": "Từ chối mở đóng tài khoản khách hàng thành công", "deleteDeny": "Từ chối xóa tài kho<PERSON>n khách hàng thành công", "createDeny": "Từ chối tạo mới tài khoản khách hàng thành công", "updateDeny": "Từ chối cập nhật tài kho<PERSON>n khách hàng thành công"}}, "request": {"title": "<PERSON><PERSON><PERSON> c<PERSON>u phê duyệt thông tin khách hàng", "info": "Thông tin khách hàng", "approvalType": "<PERSON><PERSON> lo<PERSON>"}, "detail": {"customerDetail": "<PERSON> tiết thông tin khách hàng", "editInfomation": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin khách hàng", "customerInfomation": "Thông tin khách hàng", "accountType": "<PERSON><PERSON><PERSON> tà<PERSON>", "customerType": "<PERSON><PERSON><PERSON> k<PERSON> h<PERSON>ng", "accountsList": "<PERSON><PERSON> s<PERSON>ch tà<PERSON>", "accountName": "<PERSON><PERSON><PERSON> tà<PERSON>", "accountNumber": "Số tài <PERSON>n", "imei": "IMEI", "deviceId": "Device ID", "operatingSystem": "<PERSON><PERSON> đi<PERSON>u hành", "performer": "<PERSON><PERSON><PERSON><PERSON> th<PERSON> hi<PERSON>n", "approver": "<PERSON><PERSON><PERSON><PERSON> phê <PERSON>", "requestTime": "<PERSON><PERSON><PERSON><PERSON> gian yêu c<PERSON>u", "approvalTime": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON>h<PERSON>", "first": "<PERSON><PERSON><PERSON> tiên", "last": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "handle": "<PERSON><PERSON> lý"}}, "placeholder": {"cifNumber": "<PERSON>hập số cif", "idCardNumber": "<PERSON><PERSON><PERSON><PERSON> số GTTT", "phoneNumberOtp": "Nhập SĐT nhận OTP", "email": "Nhập email"}}, "role": {"title": "<PERSON>hê<PERSON> mới vai trò", "titleUpdate": "<PERSON><PERSON><PERSON> nhật vai trò", "titleDetail": "Thông tin chi tiết vai trò", "deleteRoleContent": "Bạn có chắc chắn muốn xóa vai trò {{roleName}} không?", "lockRoleContent": "Bạn có chắc chắn muốn khoá vai trò {{roleName}} không?", "unlockRoleContent": "Bạn có chắc chắn muốn mở khóa vai trò {{roleName}} không?", "infor": "Thông tin quyền", "createRole": "<PERSON><PERSON><PERSON>", "deleteTitle": "<PERSON><PERSON><PERSON> nh<PERSON>n xóa vai trò", "backRole": "Quay lại", "lock": "<PERSON><PERSON><PERSON> n<PERSON>n kho<PERSON> vai trò", "unLock": "<PERSON><PERSON><PERSON> nhận mở khóa vai trò", "role-name-has-existed": "Tên nhóm quyền đã tồn tại", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "searchButton": "<PERSON><PERSON><PERSON>", "addRole": "<PERSON><PERSON><PERSON><PERSON>"}, "error-code-management": {"errorCode": "<PERSON><PERSON> l<PERSON>", "nameError": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "search": "<PERSON><PERSON><PERSON>", "addError": "<PERSON><PERSON><PERSON><PERSON>", "manipulation": "<PERSON><PERSON>", "contentError": "Nội dung lỗi"}, "loanOnline": {"title": "<PERSON> tiết thông tin khoản vay", "titleInforBrrower": "Thông tin người vay", "titleInforLoanAmount": "Thông tin khoản vay", "loanProduct": "<PERSON><PERSON><PERSON> ph<PERSON>m vay", "search": "<PERSON><PERSON><PERSON>", "unit": {"month": "<PERSON><PERSON><PERSON><PERSON>", "lak": "LAK", "lak-month": "LAK/Tháng"}, "delete": "<PERSON><PERSON><PERSON> n<PERSON>n xóa đăng ký vay", "deleteLoanOnlineContent": "Bạn có chắc chắn muốn xóa đăng ký vay của khách hàng {{fullname}} không?"}, "referral": {"titleDetail": "<PERSON> tiết mã giới thiệu", "titleInfoDetail": "Thông tin mã giới thiệu", "lock": "<PERSON><PERSON><PERSON> nhận khóa mã giới thiệu", "unlock": "<PERSON><PERSON><PERSON> nhận mở khóa mã giới thiệu", "delete": "<PERSON><PERSON><PERSON> nhận xóa mã giới thiệu", "lockReferralContent": "Bạn có chắc chắn muốn khoá mã giới thiệu  {{referralCode}} không?", "unlockReferralContent": "Bạn có chắc chắn muốn mở khoá mã giới thiệu  {{referralCode}} không?", "deleteReferralContent": "Bạn có chắc chắn muốn xóa mã giới thiệu  {{referralCode}} không?"}, "bank": {"root": "<PERSON><PERSON><PERSON><PERSON> lý ngân hàng thụ hưởng", "detailTitle": "<PERSON> tiết ngân hàng hưởng thụ", "createTitle": "Tạo mới ngân hàng thụ hưởng", "updateTitle": "<PERSON><PERSON><PERSON> nhật ngân hàng thụ hưởng", "titleInforBank": "Thông tin ngân hàng", "titleDetailInforBank": "Th<PERSON>ng tin chi tiết ngân hàng", "lock": "<PERSON><PERSON><PERSON> n<PERSON>n khóa ngân hàng", "unlock": "<PERSON><PERSON><PERSON> nhận mở khóa ngân hàng", "delete": "<PERSON><PERSON><PERSON> n<PERSON>n xóa ngân hàng", "create": "<PERSON><PERSON><PERSON> nh<PERSON>n tạo mới ngân hàng", "update": "<PERSON><PERSON><PERSON> nhận cập nhật ngân hàng", "createBankContent": "Bạn có chắc chắn muốn tạo mới ngân hàng {{bankCode}} không??", "updateBankContent": "Bạn có chắc chắn muốn cập nhật ngân hàng {{bankCode}} không??", "lockBankContent": "Bạn có chắc chắn muốn khoá ngân hàng {{bankCode}} không?", "unlockBankContent": "Bạn có chắc chắn muốn mở khoá ngân hàng {{bankCode}} không?", "deleteBankContent": "Bạn có chắc chắn muốn xóa ngân hàng {{bankCode}} không??"}, "event": {"sent": "Đ<PERSON> gửi", "draft": "<PERSON><PERSON><PERSON>", "waiting": "<PERSON>ờ gửi", "cancel": "<PERSON><PERSON> hủy", "deleteEvent": "<PERSON><PERSON><PERSON> nh<PERSON>n xoá thông báo", "deleteContent": "Bạn có chắc chắn muốn xóa thông báo {{title}} không? ", "receiver": "<PERSON><PERSON><PERSON><PERSON>n", "createTitle": "<PERSON><PERSON><PERSON><PERSON> mới thông báo", "detailTitle": "<PERSON> tiết thông báo", "backUpTitle": "<PERSON><PERSON><PERSON> bản sao thông báo", "information": "Thông tin thông báo", "receiverList": "<PERSON><PERSON> s<PERSON>ch ng<PERSON><PERSON>n", "addCustomerTitle": "<PERSON><PERSON><PERSON><PERSON> mới ng<PERSON> nh<PERSON>n", "usernameOrPhoneNumber": "Username hoặc số điện thoại", "delete": "<PERSON><PERSON><PERSON> thông báo", "numberOfReceiver": "<PERSON><PERSON> n<PERSON>n", "numberOfCustomer": "<PERSON><PERSON> kh<PERSON>ch hàng", "downloadFileEvent": "<PERSON><PERSON><PERSON> file", "addNew": "<PERSON><PERSON><PERSON><PERSON> mới", "deleteReceiverContent": "Bạn có chắc chắn muốn xoá người nhận {{username}} không?", "deleteReceiver": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>n", "sender": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>i"}, "user": {"root": "<PERSON><PERSON><PERSON><PERSON> lý ng<PERSON><PERSON> dùng", "createTitle": "<PERSON><PERSON>o mới người dùng", "updateTitle": "<PERSON><PERSON><PERSON> nh<PERSON>t ng<PERSON><PERSON> dùng", "detailTitle": "<PERSON> tiết người dùng", "titleInforUser": "Thông tin người dùng", "lock": "<PERSON><PERSON><PERSON>n khóa người dùng", "unlock": "<PERSON><PERSON><PERSON> nh<PERSON>n mở khóa người dùng", "delete": "<PERSON><PERSON><PERSON> n<PERSON>n xóa người dùng", "lockUserContent": "Bạn có chắc chắn muốn khoá người dùng {{fullname}} không?", "unlockUserContent": "Bạn có chắc chắn muốn mở khoá người dùng {{fullname}} không?", "deleteUserContent": "Bạn có chắc chắn muốn xóa người dùng {{fullname}} không?", "resend": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> lại link kích hoạt người dùng", "resetPassword": "<PERSON><PERSON><PERSON> nhận đổi lại mật khẩu người dùng", "resendUserContent": "Bạn có chắc chắn muốn gửi lại link kích hoạt cho người dùng {{fullname}} không?", "resetPasswordUserContent": "Bạn có chắc chắn muốn đổi lại mật khẩu người dùng {{fullname}} không?"}, "position": {"root": "<PERSON><PERSON><PERSON><PERSON> lý ch<PERSON> danh", "createTitle": "<PERSON><PERSON><PERSON> mới chức danh", "updateTitle": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> chức danh", "titleInforPosition": "Th<PERSON>ng tin chi tiết chức danh", "lock": "<PERSON><PERSON><PERSON>n kh<PERSON><PERSON> chức danh", "unlock": "<PERSON><PERSON><PERSON> nhận mở kh<PERSON>a chức danh", "delete": "<PERSON><PERSON><PERSON>n x<PERSON><PERSON> chức danh", "lockPositionContent": "Bạn có chắc chắn muốn khoá chức danh {{positionName}} không?", "unlockPositionContent": "Bạn có chắc chắn muốn mở khoá chức danh {{positionName}} không?", "deletePositionContent": "Bạn có chắc chắn muốn xóa chức danh {{positionName}} không?"}, "payment": {"lock": "<PERSON><PERSON><PERSON>n kh<PERSON>a dịch vụ chi hộ", "unlock": "<PERSON><PERSON><PERSON>n mở khóa dịch vụ chi hộ", "delete": "<PERSON><PERSON><PERSON>n x<PERSON><PERSON> dịch vụ chi hộ", "lockPaymentContent": "Bạn có chắc chắn muốn khoá dịch vụ chi hộ này?", "unlockPaymentContent": "Bạn có chắc chắn muốn mở khoá dịch vụ chi hộ này?", "deletePaymentContent": "Bạn có chắc chắn muốn xóa dịch vụ chi hộ này?", "auto": "<PERSON><PERSON><PERSON> nhận chuyển chế độ tự động", "manual": "<PERSON><PERSON><PERSON> nhận chuyển chế độ thủ công", "autoContent": "Bạn có chắc chắn muốn chuyển toàn bộ tài kho<PERSON>n sang chế độ tự động không?", "manualContent": "Bạn có chắc chắn muốn chuyển toàn bộ tài kho<PERSON>n sang chế độ thủ công không?", "labelToggle": "<PERSON><PERSON> chế thực hi<PERSON>n"}, "fee": {"title": "<PERSON><PERSON>o mới cấu hình phí", "titleUpdate": "<PERSON><PERSON><PERSON> nhật cấu hình phí", "titleDetail": "<PERSON> tiết cấu hình phí", "keyword": "<PERSON><PERSON> khóa", "configurationFeeType": "<PERSON><PERSON><PERSON> cấu hình phí", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "fee": "Phí", "discount": "<PERSON><PERSON><PERSON>", "active": "<PERSON><PERSON><PERSON> ho<PERSON>t cấu hình phí", "inactive": "<PERSON><PERSON><PERSON><PERSON> cấu hình phí", "activeFeeContent": "Bạn có muốn kích hoạt cấu hình phí {{feeName}} không?", "inactiveFeeContent": "Bạn có muốn khóa cấu hình {{feeName}} không?", "createFee": "<PERSON><PERSON><PERSON> c<PERSON>u hình chi phí", "feeInformationTitle": "Thông tin phí giao dịch", "nameFeeDetail": "<PERSON><PERSON><PERSON> c<PERSON>u hình phí", "detailFeeRate": "<PERSON> tiết biểu phí", "feeTypeDefault": "S<PERSON> tiền cố định", "delete": "<PERSON><PERSON><PERSON> c<PERSON>u hình phí", "deleteContent": "Bạn có chắc chắn muốn xóa cấu hình phí {{feeName}} không?"}, "feeRate": {"feeRateName": "<PERSON><PERSON><PERSON> bi<PERSON>u phí", "transactionAmount": "<PERSON><PERSON> tiền giao d<PERSON>ch", "transactionAmountMin": "<PERSON><PERSON> tiền giao dịch nhỏ nhất", "transactionAmountMax": "<PERSON><PERSON> tiền giao dịch lớn nhất", "amountFee": "Số tiền phí", "feeAmount": "Số tiền phí", "VAT": "VAT (%)", "effectiveDate": "<PERSON><PERSON><PERSON>", "expiredDate": "<PERSON><PERSON><PERSON> h<PERSON> hi<PERSON> l<PERSON>", "titleFeeRate": "<PERSON> tiết biểu phí", "percentCk": "% CK", "ckMin": "<PERSON><PERSON> tiền trừ tối thiểu", "ckMax": "S<PERSON> tiền trừ tối đa", "createTitle": "<PERSON><PERSON><PERSON> biểu phí", "updateTitle": "<PERSON><PERSON><PERSON> nhật biểu phí", "detailTitle": "Thông tin chi tiết biểu phí", "transactionAmountLak": "<PERSON><PERSON> tiền giao d<PERSON>ch", "transactionAmountLakMin": "<PERSON><PERSON> tiền giao dịch tối thiểu", "transactionAmountLakMax": "<PERSON><PERSON> tiền giao dịch tối đa", "from": "Từ", "to": "<PERSON><PERSON><PERSON>", "percentDiscount": "% chiết kh<PERSON>u", "fixedDiscount": "<PERSON><PERSON><PERSON> kh<PERSON>u cố định", "moneyMinusAccount": "<PERSON><PERSON> tiền trừ trên tài khoản ngân <PERSON>ng", "moneyMinusAccountMin": "S<PERSON> tiền trừ trên tài khoản ngân hàng tối thiểu", "moneyMinusAccountMax": "Số tiền trừ trên tài khoản ngân hàng tối đa", "effectTime": "<PERSON><PERSON><PERSON><PERSON> gian hi<PERSON>u l<PERSON>c trong ngày", "startEffectTime": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON>t đ<PERSON>u hiệu lực", "endEffectTime": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc hi<PERSON>u l<PERSON>c", "delete": "Xóa biểu phí", "deleteContent": "Bạn có chắc chắn muốn xóa biểu phí {{feeRateName}} không?", "active": "<PERSON><PERSON><PERSON> ho<PERSON>t biểu phí", "inactive": "<PERSON><PERSON><PERSON> biểu phí"}, "merchant": {"root": "<PERSON><PERSON><PERSON><PERSON>", "createTitle": "Tạo mới Merchant", "updateTitle": "<PERSON><PERSON><PERSON>", "titleInforMerchant": "Th<PERSON>ng tin chi tiết Merchant", "lock": "<PERSON><PERSON><PERSON> kh<PERSON>", "unlock": "<PERSON><PERSON><PERSON> nh<PERSON>n mở khóa Merchant", "delete": "<PERSON><PERSON><PERSON> n<PERSON>n x<PERSON><PERSON>", "merchantCodeSync": "<PERSON><PERSON>ng bộ mã Merchant", "merchantCodeSyncSuccess": "<PERSON><PERSON>ng bộ mã Merchant thành công", "lockMerchantContent": "Bạn có chắc chắn muốn kho<PERSON> {{merchantName}} không?", "unlockMerchantContent": "Bạn có chắc chắn muốn mở khoá Merchant {{merchantName}} không?", "deleteMerchantContent": "Bạn có chắc chắn muốn xóa merchant {{merchantName}} không?", "totalAmount": "<PERSON><PERSON>ng số tiền thanh toán", "totalQuantity": "Tổng số giao dịch", "masterMerchant": {"root": "<PERSON><PERSON><PERSON><PERSON>", "createTitle": "<PERSON><PERSON><PERSON> mới Master Merchant", "updateTitle": "<PERSON><PERSON><PERSON> Master Merchant", "titleInforMerchant": "<PERSON><PERSON><PERSON><PERSON> tin chi tiết Master Merchant", "lock": "<PERSON><PERSON><PERSON> kh<PERSON>a Master Merchant", "unlock": "<PERSON><PERSON><PERSON> mở kh<PERSON>a Master Merchant", "delete": "<PERSON><PERSON><PERSON>n x<PERSON><PERSON> Master Merchant", "lockMerchantContent": "<PERSON>ạn có chắc chắn muốn khoá Master Merchant {{merchantName}} không?", "unlockMerchantContent": "Bạn có chắc chắn muốn mở khoá Master Merchant {{merchantName}} không?", "deleteMerchantContent": "Bạn có chắc chắn muốn xóa Master Merchant {{merchantName}} không?"}}, "matPaginator": {"itemsPerPage": "Số bản ghi", "of": "c<PERSON>a", "firstPage": "<PERSON><PERSON>", "lastPage": "<PERSON><PERSON> cu<PERSON>i", "nextPage": "<PERSON><PERSON><PERSON><PERSON> theo", "previousPage": "<PERSON><PERSON> tr<PERSON>"}, "model": {"manageSaving": {"month": "<PERSON><PERSON><PERSON><PERSON>", "unit": "LAK", "ratePercent": "%/năm", "fixed": "<PERSON><PERSON><PERSON><PERSON> kiệm cố định", "accumulated": "<PERSON><PERSON><PERSON><PERSON> ki<PERSON>m tích l<PERSON>", "no_term": "<PERSON><PERSON><PERSON><PERSON> ki<PERSON>m không kỳ hạn", "tabDetailSaving": "<PERSON> tiết tài khoản tiết kiệm", "tabDetailTrans": "<PERSON><PERSON><PERSON> sử giao dịch", "manageSavingTitle": "<PERSON><PERSON>", "keyword": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "savingType": "<PERSON><PERSON><PERSON> ti<PERSON> ki<PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "fromDate": "<PERSON><PERSON> ngày", "toDate": "<PERSON><PERSON><PERSON>", "no": "STT", "accountNumber": "Số tài <PERSON>n", "accountOwner": "<PERSON><PERSON> tà<PERSON>n", "cif": "Số CIF", "gttt": "Số GTTT", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "startTime": "Th<PERSON>i gian mở", "money": "<PERSON><PERSON> tiền g<PERSON>i", "savingInfo": {"savingDetail": {"startTime": "<PERSON>h<PERSON>i gian mở tài kho<PERSON>n", "settlementDueTime": "<PERSON><PERSON><PERSON><PERSON> gian đến hạn tất toán", "rate": "<PERSON><PERSON><PERSON>", "frequencyPayment": "<PERSON><PERSON><PERSON> su<PERSON>t trả lãi", "rateCurrent": "<PERSON><PERSON><PERSON> suất đến ngày hiện tại", "expectedRate": "<PERSON><PERSON><PERSON> suất dự kiến đến ngày tất toán", "moneyDueDate": "<PERSON><PERSON><PERSON> tiền đến ngày hết hạn", "dueDateType": "<PERSON><PERSON><PERSON> thức đến hạn"}, "transactionHistory": {"no": "STT", "accountSource": "<PERSON><PERSON><PERSON> nguồn", "transactionTime": "<PERSON><PERSON><PERSON><PERSON> gian giao d<PERSON>ch", "transactionCode": "Mã giao d<PERSON>ch", "money": "<PERSON><PERSON> t<PERSON> (LAK)", "type": "<PERSON><PERSON><PERSON>", "content": "<PERSON><PERSON>i dung"}}, "modal": {"startTime": "<PERSON>h<PERSON>i gian mở tài kho<PERSON>n", "fixed": "<PERSON><PERSON><PERSON><PERSON> kiệm cố định", "accumulation": "<PERSON><PERSON><PERSON><PERSON> ki<PERSON>m lũy tiến", "smart": "<PERSON><PERSON><PERSON><PERSON> ki<PERSON>m thông minh"}}, "nationCode": {"VN": "Việt Nam", "KH": "<PERSON><PERSON><PERSON>", "LA": "Lào", "TH": "<PERSON><PERSON><PERSON><PERSON>", "CN": "<PERSON><PERSON>"}, "news": {"no": "STT", "title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "content": "<PERSON><PERSON>i dung", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "categories": "<PERSON><PERSON><PERSON><PERSON>", "newsInternal": "<PERSON> tức n<PERSON>i bộ", "newsInter": "<PERSON> tức", "create": "<PERSON><PERSON><PERSON> mới tin tức", "update": "<PERSON><PERSON><PERSON> nhật tin tức", "detail": "<PERSON> tiết tin tức", "information": "Thông tin tin tức", "lock": "<PERSON><PERSON><PERSON> nh<PERSON>n kho<PERSON> tin tức", "unlock": "<PERSON><PERSON><PERSON> nhận mở khóa tin tức", "lockContent": "Bạn có chắc chắn muốn khóa tin tức này?", "unlockContent": "Bạn có chắc chắn muốn mở khóa tin tức này?", "required": {"categories": "<PERSON><PERSON><PERSON><PERSON> mục không được bỏ trống"}, "modal": {"title": "<PERSON><PERSON><PERSON><PERSON> báo", "content": "Bạn đang có thay đổi chưa đ<PERSON><PERSON><PERSON> lưu lạ<PERSON>. Bạn có muốn tiếp tục chuyển sang ngôn ngữ khác không?", "delete": "<PERSON><PERSON><PERSON> nh<PERSON>n x<PERSON>a tin tức", "deleteNewsContent": "Bạn có chắc chắn muốn xóa tin tức này ?"}, "error": {"content": "<PERSON>ội dung tối đa 5000 kí tự"}}, "manageTransQuantity": {"keyword": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm", "transQuantityGreater": "Số lượng giao dịch lớn nhất", "transQuantityLess": "<PERSON><PERSON> lượng giao dịch nhỏ nhất", "transQuantity": "Số lư<PERSON> giao d<PERSON>ch", "fromDate": "<PERSON><PERSON> ngày", "toDate": "<PERSON><PERSON><PERSON>", "no": "STT", "userName": "<PERSON><PERSON><PERSON> h<PERSON>ng", "cif": "<PERSON><PERSON> h<PERSON> (CIF)", "transCreditSuccess": "Số lượng giao dịch ghi có thành công", "transDebitSuccess": "<PERSON><PERSON> lượng giao dịch ghi nợ thành công", "transactionType": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "credit": "<PERSON><PERSON>", "debit": "<PERSON><PERSON> n<PERSON>"}, "rate": {"title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "no": "STT", "image": "<PERSON><PERSON><PERSON>", "createdDate": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "status": "<PERSON>rang thái", "information": "Thông tin lãi suất", "lock": "<PERSON><PERSON><PERSON>n khóa lãi suất", "lockContent": "Bạn có chắc chắn muốn khoá lãi suất {{title}} không?", "unlock": "<PERSON><PERSON><PERSON> nh<PERSON>n mở khóa lãi suất", "unlockContent": "Bạn có chắc chắn muốn mở khóa lãi suất {{title}} không?", "create": "<PERSON>ạo mới lãi suất", "detail": "<PERSON> tiết lãi suất", "update": "<PERSON><PERSON><PERSON> nh<PERSON>t lãi su<PERSON>t", "modal": {"delete": "<PERSON><PERSON><PERSON> n<PERSON>n xóa lãi suất", "deleteRateContent": "Bạn có chắc chắn muốn xóa lãi suất này ?"}}, "specialAccountNumber": {"list": "<PERSON><PERSON> s<PERSON>ch số đẹp đặc biệt", "createTitle": "<PERSON><PERSON><PERSON><PERSON> mới số đẹp đặc biệt", "updateTitle": "<PERSON><PERSON><PERSON> nhật số đẹp đặc biệt", "detailTitle": "<PERSON> tiết số đẹp đặc biệt", "numberType": "<PERSON><PERSON><PERSON> số", "hiddenNumber": "<PERSON><PERSON> <PERSON><PERSON>", "lock": "<PERSON><PERSON><PERSON>n <PERSON>ố tài k<PERSON>n", "lockContent": "Bạn muốn <PERSON>n <PERSON> tài k<PERSON>n {{title}} ?", "unlock": "<PERSON><PERSON><PERSON>n <PERSON>ố tài k<PERSON>n", "unlockContent": "Bạn muốn mở ẩn <PERSON>ố tài k<PERSON>n {{title}} ?", "delete": "<PERSON><PERSON><PERSON><PERSON><PERSON> tài k<PERSON>n", "deleteConfig": "Bạn muốn <PERSON> k<PERSON> {{title}} khỏi danh sách Số Đặc Biệt?", "error": {"accountNumber": "Số tài khoản không được để trống", "listPrice": "<PERSON><PERSON><PERSON> niêm yết không được để trống", "discount": "<PERSON><PERSON><PERSON> khấu không đư<PERSON><PERSON> để trống", "maxLength": {"listPrice": "<PERSON><PERSON><PERSON> ni<PERSON> yết không hợp lệ"}}, "pattern": {"accountNumber": "<PERSON>ố tài khoản phải chỉ chứa ký tự số, có độ dài 4,5,9,10 và số điện thoại"}}, "premiumAccountNumber": {"list": "<PERSON><PERSON> s<PERSON>ch số đẹp", "openDate": "Ngày mở", "series": "<PERSON><PERSON><PERSON>", "seriesInput": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>y số tìm kiếm", "avoidNumber": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "pending": "<PERSON><PERSON> treo", "info": "Thông tin mở tài khoản số đẹp", "recalled": "<PERSON><PERSON> thu hồi", "unKnown": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "balance": "Số d<PERSON> t<PERSON> (LAK)", "notification": "<PERSON><PERSON><PERSON> n<PERSON>n g<PERSON>i thông báo", "register": "Mở tài k<PERSON>n số đẹp", "charge": "<PERSON>hu phí mua tài k<PERSON>n số đẹp", "otpExpried": "Mã OTP hết hạn sau:", "notificationContent": "<PERSON><PERSON><PERSON> thông báo tới khách hàng {{name}} chủ nhân số tài khoản {{accountNumber}}, để nhắc nhở việc thời hạn thanh toán cho giao dịch Mở Tài Khoản Số Đẹp không?", "accountType": {"phoneNumber": "<PERSON> đi<PERSON>n tho<PERSON>i", "interest": "<PERSON> thích"}}, "structureAccount": {"group4": "Nhóm số 4", "group5": "Nhóm số 5", "group6": "Nhóm số 6", "group8": "Nhóm số 8", "group9": "Nhóm số 9", "name": "<PERSON><PERSON><PERSON> c<PERSON>u trúc", "structureTitle": "<PERSON><PERSON> s<PERSON>ch cấu trúc", "titleStructure": "Thông tin cấu trúc", "numberStructure": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON>", "numberGroup": "Nhóm số", "length": "<PERSON><PERSON> dài", "uploadFile": "<PERSON><PERSON><PERSON> file", "minPrice": "<PERSON><PERSON><PERSON> thấp nh<PERSON>t", "maxPrice": "<PERSON><PERSON><PERSON> cao nh<PERSON>t", "listPrice": "<PERSON><PERSON><PERSON> (LAK)", "originalPrice": "<PERSON><PERSON><PERSON>", "paymentPrice": "<PERSON><PERSON><PERSON> (LAK)", "totalPrice": "<PERSON><PERSON><PERSON> toán", "price": "Giá", "discount": "Chiết khấu %", "btnHidden": "<PERSON><PERSON> s<PERSON>ch s<PERSON>", "btnSpecial": "<PERSON><PERSON> s<PERSON>ch số đặc biệt", "special": "Số đặc biệt", "title": "<PERSON><PERSON><PERSON> nh<PERSON>t cấu trúc số", "info": "Thông tin", "content": "<PERSON><PERSON>", "addPremiumAccNumber": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "formatPattern": "Dạng mẫu cấu trúc", "account": "<PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON> nh<PERSON>n x<PERSON>a cấu trúc số", "lock": "<PERSON><PERSON><PERSON> nh<PERSON>n khoá cấu trúc", "lockContent": "Bạn có muốn khoá cấu trúc {{title}} độ dài {{length}} <b>ký tự</b> không?", "unlock": "<PERSON><PERSON><PERSON> nhận mở khoá cấu trúc", "unlockContent": "Bạn có muốn mở khoá cấu trúc {{title}} độ dài {{length}} <b>ký tự</b> không?", "deleteConfig": "Bạn có muốn xóa cấu trúc {{title}} độ dài {{length}} <b>ký tự</b> này không?", "addAccount": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "titleConfirm": "<PERSON><PERSON><PERSON>n x<PERSON>a", "contentConfirm": "Bạn có muốn xóa số này không ?", "messageNotFound": "<PERSON><PERSON><PERSON><PERSON> có kết quả phù hợp", "createTitle": "<PERSON><PERSON><PERSON> c<PERSON>u trúc", "updateTitle": "<PERSON><PERSON><PERSON> nh<PERSON>t cấu trúc", "detailTitle": "<PERSON> tiết cấu trúc", "hideAccountNumber": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "hideAccountNumberPlaceHolder": "Số cần tr<PERSON>h", "addHideAccNumber": "<PERSON><PERSON><PERSON><PERSON> thêm các số cần tr<PERSON>h", "accountNumber": "Số tài <PERSON>n", "error": {"name": "<PERSON><PERSON><PERSON> cấu trúc số không được bỏ trống", "numberStructure": "<PERSON>ã cấu trúc số không được bỏ trống", "numberGroup": "Nhóm số không được bỏ trống", "price": "<PERSON><PERSON><PERSON> ni<PERSON>m yết không được bỏ trống", "discount": "<PERSON><PERSON><PERSON> khấu không được bỏ trống", "content": "<PERSON><PERSON> tả không được bỏ trống", "account": "<PERSON><PERSON><PERSON> số không được bỏ trống", "structureAccount": "<PERSON><PERSON><PERSON> số nhập không đúng định dạng", "pattern": "<PERSON>tern cấu trúc không được để trống", "length": "<PERSON><PERSON> dài không đ<PERSON><PERSON><PERSON> để trống", "errorPrice": "<PERSON><PERSON><PERSON> niêm yết không hợp lệ với khoảng tiền của nhóm số"}, "pattern": {"discount": "<PERSON><PERSON><PERSON> khấu không đúng định dạng"}}, "managePayment": {"no": "STT", "accountNumber": "Số tài <PERSON>n", "accountName": "<PERSON><PERSON><PERSON> tà<PERSON>", "bankName": "<PERSON><PERSON> h<PERSON>", "currency": "Đơn vị tiền tệ", "balance": "Số dư", "usageBalance": "Số tiền đã chuyển trong ngày", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "action": "<PERSON><PERSON>", "modal": {"create": "<PERSON><PERSON><PERSON> mới", "edit": "<PERSON><PERSON><PERSON>", "detail": "<PERSON> ti<PERSON>", "accountName": "<PERSON><PERSON><PERSON> tà<PERSON>", "bankBenificiary": "<PERSON><PERSON> hàng thụ hưởng", "currency": "Đơn vị tiền tệ", "currencyHolder": "Đơn vị"}, "msg": {"manual": "Chuyển chế độ thủ công thành công", "auto": "Chuyển chế độ tự động thành công"}}, "smsManage": {"title": "DANH SÁCH BÁO CÁO SMS", "no": "STT", "fromDate": "<PERSON><PERSON> ngày", "toDate": "<PERSON><PERSON><PERSON>", "phoneSMS": "SĐT nhận sms", "smsType": "<PERSON><PERSON><PERSON> tin nh<PERSON>n", "amountSendSucceed": "S<PERSON> lư<PERSON> gửi thành công", "amountSendFailed": "Số lư<PERSON> gửi thất bại", "amountSend": "Tổng số lư<PERSON> g<PERSON>i"}, "configTransaction": {"title": "<PERSON><PERSON> s<PERSON>ch hạn mức giao d<PERSON>ch", "denied": "<PERSON><PERSON> chối", "approved": "<PERSON><PERSON> p<PERSON>", "waiting": "<PERSON><PERSON> chờ du<PERSON>", "create": "<PERSON><PERSON><PERSON> mới cấu hình hạn mức giao dịch", "detail": "<PERSON> tiết cấu hình hạn mức giao dịch", "update": "<PERSON><PERSON><PERSON> nh<PERSON>t cấu hình hạn mức giao dịch", "tableCreateTrans": "<PERSON> tiết kho<PERSON>n mục hạn mức của các lo<PERSON>i giao dịch", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "fromDate": "<PERSON><PERSON> ngày", "toDate": "<PERSON><PERSON><PERSON>", "no": "STT", "packageName": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> d<PERSON> v<PERSON>", "serviceType": "<PERSON><PERSON><PERSON> d<PERSON> vụ", "transactionType": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "sectorType": "Loại sector", "maxMoneySend": "<PERSON><PERSON> tiền gửi tối đa", "maxMoneyTrans": "<PERSON><PERSON><PERSON> mức tối đa/ 1 giao dịch", "activeDate": "<PERSON><PERSON><PERSON>", "createdDate": "<PERSON><PERSON><PERSON>", "approvedBy": "<PERSON><PERSON><PERSON><PERSON> phê <PERSON>", "approvedDate": "<PERSON><PERSON><PERSON>", "approvedStatus": "<PERSON>r<PERSON><PERSON> thái phê <PERSON>", "action": "<PERSON><PERSON>", "reason": "Lý do", "reasonApproval": "<PERSON><PERSON> <PERSON> phê <PERSON>", "reasonDeined": "<PERSON>í do từ chối", "createConfig": "<PERSON><PERSON><PERSON> mới hạn mức thành công", "denySuccess": "Từ chối hạn mức thành công", "approvalSuccess": "<PERSON><PERSON>t hạn mức thành công", "statusCofig": {"waiting": "<PERSON><PERSON> chờ du<PERSON>", "approved": "<PERSON><PERSON> p<PERSON>", "cancel": "<PERSON><PERSON><PERSON> <PERSON><PERSON>"}, "placeholder": {"packageName": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> d<PERSON> v<PERSON>", "transactionDay": "<PERSON><PERSON><PERSON><PERSON> hạn mức theo ng<PERSON>y", "transactionMonthNo": "<PERSON><PERSON><PERSON>ng gi<PERSON>i hạn", "transactionMonth": "<PERSON><PERSON><PERSON><PERSON> hạn mức theo tháng", "transactionYear": "<PERSON><PERSON><PERSON>ng gi<PERSON>i hạn"}, "modal": {"confirmDeny": "<PERSON><PERSON><PERSON>n từ chối", "confirmApproval": "<PERSON><PERSON><PERSON>n phê <PERSON>", "confirmReasonDeny": "Bạn có chắc chắn từ chối phê duyệt cấu hình hạn mức này ?", "confirmReasonApproval": "Bạn có chắc chắn phê duyệt cấu hình hạn mức này ?", "delete": "<PERSON><PERSON><PERSON> nhận x<PERSON>a cấu hình hạn mức", "deleteConfigTransContent": "Bạn có chắc chắn muốn xóa cấu hình hạn mức này ?"}, "form": {"servicePackage": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> d<PERSON> v<PERSON>", "transType": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "sectorType": "Hạn mức theo sector", "currencyType": "<PERSON><PERSON><PERSON> mức theo đơn vị tiền tệ", "sector": "Loại sector", "maxMoney": "<PERSON><PERSON><PERSON>", "trans": "LAK/ Giao d<PERSON>", "transDay": "LAK/ Ngày", "transMonth": "LAK/ Tháng", "transYear": "LAK/ Năm", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "basicService": "<PERSON><PERSON><PERSON> bản", "advanceService": "<PERSON><PERSON><PERSON> n<PERSON>g cao", "internalTransfer": "<PERSON><PERSON><PERSON><PERSON> tiền trong hệ thống", "interbankTransfer": "<PERSON>y<PERSON>n tiền liên ngân hàng", "internationalTransfer": "<PERSON><PERSON> to<PERSON> xuyên biên g<PERSON>i", "topUp": "<PERSON><PERSON><PERSON> tiền điện thoại", "billing": "<PERSON><PERSON> <PERSON><PERSON> hóa đơn", "cashIn": "Nạp v<PERSON>", "insurance": "<PERSON><PERSON> <PERSON><PERSON> b<PERSON>o <PERSON> L<PERSON>"}}, "configSaving": {"title": "<PERSON><PERSON> s<PERSON>ch kỳ hạn tiết kiệm", "type": "<PERSON><PERSON><PERSON> hình tiết ki<PERSON>m", "tenor": "<PERSON><PERSON> hạn", "tenorPeriod": "<PERSON><PERSON><PERSON> kỳ hạn", "m": "<PERSON><PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON> mới kỳ hạn tiết kiệm", "update": "<PERSON><PERSON><PERSON> nh<PERSON>t kỳ hạn tiết kiệm", "view": "<PERSON> tiết kỳ hạn tiết kiệm", "modal": {"delete": "<PERSON><PERSON><PERSON> nhận x<PERSON>a cấu hình kỳ hạn tiết kiệm", "deleteConfigSavingContent": "Bạn có chắc chắn muốn xóa cấu hình kỳ hạn tiết kiệm này ?"}, "error": {"type": "<PERSON><PERSON><PERSON> hình tiết kiệm không được bỏ trống", "tenor": "<PERSON><PERSON> hạn không được bỏ trống"}, "pattern": {"tenor": "<PERSON><PERSON> hạn chỉ được nhập chữ số"}}, "insuranceManage": {"title": "<PERSON><PERSON> s<PERSON>ch báo cáo mua bảo hiểm LVI", "fromDate": "<PERSON><PERSON> ngày", "toDate": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "no": "STT", "customerName": "<PERSON><PERSON><PERSON> h<PERSON>ng", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "costAfterDiscount": "<PERSON><PERSON> t<PERSON><PERSON><PERSON> (LAK)", "vehicleType": "Loại xe", "cifNumber": "<PERSON><PERSON> h<PERSON> (CIF)", "insurancePolicy": "<PERSON><PERSON> hợp đồng b<PERSON><PERSON> hi<PERSON>", "type": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "productPackage": "<PERSON><PERSON><PERSON> p<PERSON>m", "costInsurance": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> (LAK)", "discount": "<PERSON><PERSON><PERSON> kh<PERSON>u từ LVI (%)", "moneyFromLvi": "Số tiền LVI trả về (LAK)", "discountMB": "<PERSON><PERSON><PERSON> kh<PERSON>u từ <PERSON> (%)", "moneyFromMb": "<PERSON>í giao <PERSON> (LAK)", "totalAmount": "<PERSON><PERSON> (LAK)", "transactionCode": "Mã giao d<PERSON>ch", "transactionTime": "<PERSON><PERSON><PERSON><PERSON> gian giao d<PERSON>ch", "vehicle": "<PERSON><PERSON><PERSON> xe cơ giới", "health": "<PERSON><PERSON><PERSON> sức khỏe", "placeholder": {"type": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "productPackage": "<PERSON><PERSON><PERSON> p<PERSON>m", "vehicleType": "Loại xe"}, "productSelect": {"motor": "<PERSON><PERSON><PERSON> xe cơ giới", "health": "<PERSON><PERSON><PERSON> sức khỏe"}}, "customer": {"customerType": {"customer": "<PERSON><PERSON><PERSON> k<PERSON> h<PERSON>ng", "customerMB": "<PERSON><PERSON><PERSON><PERSON> hàng mở tài khoản từ hệ thống MB", "customerUmoney": "<PERSON><PERSON><PERSON><PERSON> hàng mở tài khoản từ Umoney"}, "ekycImages": "Ảnh nhận diện khuôn mặt", "idCardType": "<PERSON>ạ<PERSON> giấy tờ tùy thân", "idCardNumber": "Số giấy tờ tùy thân", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "fullname": "<PERSON><PERSON><PERSON> h<PERSON>ng", "dateOfBirth": "<PERSON><PERSON><PERSON>", "manualActivationTime": "<PERSON>h<PERSON><PERSON> gian kích hoạt thủ công", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "statusOfResidence": "<PERSON><PERSON>nh trạng cư trú", "nationCode": "<PERSON><PERSON><PERSON><PERSON>", "nationCodeOther": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "issueDate": "<PERSON><PERSON><PERSON> c<PERSON>p gi<PERSON>y tờ tùy thân", "issuePlace": "<PERSON><PERSON><PERSON> cấp gi<PERSON>y tờ tùy thân", "placeOfResidence": "Địa chỉ thường trú", "placeOfResidenceOutCountry": "Địa chỉ thường trú ở nước ngoài", "currentAddress": "<PERSON><PERSON><PERSON> chỉ liên hệ hiện tại", "phoneContact": "<PERSON><PERSON> điện thoại liên hệ", "email": "Email", "job": "<PERSON><PERSON><PERSON>", "position": "<PERSON><PERSON><PERSON> v<PERSON>", "identityCardImages": "Ảnh gi<PERSON>y tờ tùy thân", "signatureImage": "Ảnh chữ ký khách hàng", "customerSectorId": "<PERSON><PERSON><PERSON><PERSON> thá<PERSON> hồ sơ định danh khách hàng", "cif": "Mã CIF", "accountCreatedTime": "<PERSON><PERSON><PERSON> tạo tài k<PERSON>n trên T24", "staffCode": "Mã nhân viên", "oldSector": "Sector cũ", "accountsList": "<PERSON><PERSON> s<PERSON>ch tà<PERSON>", "updateHistory": "<PERSON><PERSON><PERSON> sử cập nh<PERSON>t", "usageInformation": "Thông tin sử dụng", "approvedBy": "<PERSON><PERSON><PERSON><PERSON> phê <PERSON>", "status": "<PERSON>rang thái", "approvedDate": "<PERSON><PERSON><PERSON>", "activatedHistory": "<PERSON><PERSON><PERSON> sử kích hoạt lần đầu trên app", "updateServiceHistory": "<PERSON><PERSON><PERSON> sử cập nh<PERSON>t dịch vụ", "username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "accountNumber": "Số tài <PERSON>n", "accountType": "<PERSON><PERSON><PERSON> tà<PERSON>", "activatedDate": "<PERSON><PERSON><PERSON>", "imei": "IMEI", "deviceId": "Device ID", "operatingSystem": "<PERSON><PERSON> đi<PERSON>u hành", "deviceName": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> bị", "action": "<PERSON><PERSON>", "detail": "<PERSON> ti<PERSON>", "editedBy": "<PERSON><PERSON><PERSON><PERSON> th<PERSON> hi<PERSON>n", "requestedTime": "<PERSON><PERSON><PERSON><PERSON> gian yêu c<PERSON>u", "maritalStatus": "Tình trạng hôn nhân", "residentStatus": "<PERSON><PERSON>nh trạng cư trú", "createdDateAccountT24": "Thời gian đăng ký tài khoản T24", "createdDateAccount": "<PERSON>h<PERSON><PERSON> gian đ<PERSON>ng ký", "statusResidence": "<PERSON><PERSON>nh trạng cư trú", "placeOfOrigin": "<PERSON><PERSON><PERSON>", "shopCode": "<PERSON><PERSON> qu<PERSON>y giao d<PERSON>ch", "default": "Mặc định", "companyID": "Mã công ty", "lastModifiedDate": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON><PERSON> nh<PERSON>t", "title": "<PERSON> tiết số tài k<PERSON>n kh<PERSON>ch hàng", "workplace": "<PERSON><PERSON><PERSON> làm vi<PERSON>c", "staff": "Nhân viên", "customer": "<PERSON><PERSON><PERSON><PERSON>", "collaborators": "<PERSON><PERSON><PERSON> tác viên", "emptyListUpdateSector": "<PERSON><PERSON> lòng chọn kh<PERSON>ch hàng", "premiumAccountNumber": "<PERSON><PERSON><PERSON>n số đẹp", "paymentPrice": "<PERSON><PERSON><PERSON> (LAK)", "buttonSms": "Gửi sms", "otp": "<PERSON>hậ<PERSON> mã OTP", "phoneOrFullName": "<PERSON><PERSON> điện thoại hoặc họ tên", "maritalStatusOption": {"single": "<PERSON><PERSON><PERSON> thân", "married": "<PERSON><PERSON> kết hôn", "other": "K<PERSON><PERSON><PERSON>"}, "residentStatusOption": {"temporary": "Tạm trú", "permanent": "Thư<PERSON>ng trú"}, "customerSector": {"customerId1740": "Nhóm KH chỉ mở tài kho<PERSON>n thanh toán tại quầy giao dịch", "customerId1700": "Nhóm KH chỉ mở tài kho<PERSON>n thanh toán tại quầy giao dịch", "customerId1890": "Nhóm KH mới là KHCN đã đến quầy mở tài khoản và mở app", "customerId1891": " Nhóm KH mới mở TKTT và app EKYC online 100%, chưa đến quầy để bổ sung hồ sơ", "customerId1742": "Nhóm KH private có hưởng lãi", "customerSectorId1890": "<PERSON><PERSON><PERSON><PERSON> hàng mới đã đến quầy mở tài khoản và mở app", "customerSectorId1891": "<PERSON><PERSON><PERSON><PERSON> hàng mới mở tài khoản thanh toán và app EKYC online 100%, chưa đến quầy để bổ sung hồ sơ"}, "accountNoTransaction": {"accountClose": "<PERSON><PERSON><PERSON> t<PERSON>", "requestTitle": "<PERSON><PERSON><PERSON>n gửi yêu cầu đóng tài k<PERSON>n", "requestContent": "Bạn có chắc chắn muốn đóng tài khoản này không?", "alertNotification": "Bạn chưa chọn tài khoản nào"}, "activateAccountManual": {"activateAccountTitle": "<PERSON><PERSON><PERSON> kích ho<PERSON>t tài k<PERSON>n", "activateAccountContent": "Bạn có chắc chắn muốn kích hoạt tài khoản này không?", "alertNotification": "Bạn chưa chọn tài khoản nào"}}, "role": {"name": "<PERSON><PERSON>n quyền", "groupName": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> quyền", "description": "<PERSON><PERSON>", "creator": "<PERSON><PERSON><PERSON><PERSON> tạo", "fromDate": "<PERSON><PERSON><PERSON>", "editRole": "Chỉnh sửa", "lastModifiedDate": "<PERSON><PERSON><PERSON> c<PERSON>", "updater": "<PERSON><PERSON><PERSON><PERSON> cậ<PERSON>"}, "backgroundLogin": {"title": "<PERSON><PERSON><PERSON> nh<PERSON>t hình nền đăng nhập", "info": "Thông tin", "bgImage": "<PERSON><PERSON><PERSON>", "colorCode": "<PERSON><PERSON>n mã màu chữ", "status": "<PERSON>r<PERSON><PERSON> thái ho<PERSON>t động", "error": {"status": "Tr<PERSON>ng thái không được để trống", "bgImage": "<PERSON><PERSON><PERSON> n<PERSON>n không được để trống"}}, "customerSupport": {"title": "<PERSON><PERSON><PERSON> nhật thông tin hỗ trợ khách hàng", "titleConfirm": "<PERSON><PERSON><PERSON>n x<PERSON>a", "contentConfirm": "Bạn có muốn xóa số điện thoại này không ?", "info": "Thông tin", "address": "Đ<PERSON>a chỉ ngân hàng", "mail": "Địa chỉ Mail", "phone": "<PERSON><PERSON> điện tho<PERSON>i chăm sóc kh<PERSON>ch hàng", "add": "<PERSON><PERSON><PERSON><PERSON> mới", "error": {"address": "Địa chỉ ngân hàng không được để trống", "mail": "Địa chỉ Mail không được để trống", "phone": "Số điện tho<PERSON>i chăm sóc khách hàng không được để trống"}, "pattern": {"mail": "Địa chỉ Mail không đúng định dạng", "phone": "Số điện tho<PERSON>i chăm sóc khách hàng tối thiểu 8 số"}}, "loanOnline": {"name": "<PERSON><PERSON><PERSON>", "fromDate": "<PERSON><PERSON> ngày", "toDate": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "customerName": "<PERSON><PERSON><PERSON> h<PERSON>ng", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "moneyLoanOnline": "<PERSON><PERSON> tiền vay trực tuyên", "purposeLoan": "<PERSON><PERSON><PERSON> vay", "loanAmount": "Số tiền đăng ký vay (LAK)", "loanTime": "<PERSON><PERSON><PERSON><PERSON> gian cho vay (tháng)", "collateral": "<PERSON><PERSON><PERSON> sản đảm bảo", "fullName": "Họ và tên", "dateOfBirth": "<PERSON><PERSON><PERSON>", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "maritalStatus": "Tình trạng hôn nhân", "email": "Email", "address": "Địa chỉ", "job": "<PERSON><PERSON><PERSON>", "position": "<PERSON><PERSON>, chứ vụ", "workplace": "<PERSON><PERSON><PERSON> làm vi<PERSON>c", "income": "<PERSON><PERSON> <PERSON><PERSON>"}, "event": {"title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "eventType": "<PERSON><PERSON> lo<PERSON>", "content": "<PERSON><PERSON>i dung", "contentNotification": "<PERSON><PERSON><PERSON> dung thông báo", "numberSent": "<PERSON><PERSON> thông báo đã gửi", "expectedNotificationAt": "<PERSON><PERSON><PERSON><PERSON> gian g<PERSON>i", "allCustomer": "<PERSON><PERSON><PERSON> c<PERSON> kh<PERSON>ch hàng", "eachCustomer": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng", "announcementTypeId": "<PERSON><PERSON> lo<PERSON>"}, "fee": {"configurationFeeTypeName": "<PERSON><PERSON><PERSON> c<PERSON>u hình phí", "configurationFeeType": "<PERSON><PERSON><PERSON> cấu hình phí", "createDate": "<PERSON><PERSON><PERSON>", "createBy": "<PERSON><PERSON><PERSON><PERSON> tạo", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "feeType": "Loại phí", "transactionFeeType": "<PERSON><PERSON><PERSON> d<PERSON> vụ", "merchantFeeType": "Loại merchant"}, "bank": {"name": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "code": "<PERSON><PERSON> ngân hàng", "codeNumber": "Mã số", "logo": "Logo", "order": "<PERSON><PERSON><PERSON> tự sắp xếp"}, "campaign": {"name": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> d<PERSON>ch", "position": "<PERSON><PERSON> trí hiển thị", "description": "<PERSON><PERSON> t<PERSON> chiến dịch", "embedLink": "<PERSON>", "startDateFrom": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDateTo": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createdDate": "<PERSON><PERSON><PERSON>", "createdBy": "<PERSON><PERSON><PERSON><PERSON> tạo", "lastModifiedDate": "<PERSON><PERSON><PERSON> c<PERSON>", "lastModifiedBy": "<PERSON><PERSON><PERSON><PERSON> cậ<PERSON>", "banners": "<PERSON><PERSON> s<PERSON> banner c<PERSON><PERSON> chi<PERSON>n dịch", "lockTitle": "<PERSON><PERSON><PERSON>n kh<PERSON>a chiến dịch", "unlockTitle": "<PERSON><PERSON><PERSON> nhận mở khóa chiến dịch", "deleteTitle": "<PERSON><PERSON><PERSON> n<PERSON>n x<PERSON>a chiến dịch", "lockContent": "Bạn có chắc chắn muốn khóa chiến dịch {{campaignName}}?", "unlockContent": "Bạn có chắc chắn muốn mở khóa chiến dịch {{campaignName}}?", "deleteContent": "Bạn có chắc chắn muốn xóa chiến dịch {{campaignName}}?", "deleteBannerContent": "Bạn có chắc chắn muốn xóa banner?", "campaignInfoTitle": "Thông tin chiến dịch", "loginScreen": "<PERSON><PERSON><PERSON> hình đ<PERSON>ng nh<PERSON>p", "homepageScreen": "<PERSON><PERSON><PERSON> hình trang chủ", "showScreen": "<PERSON><PERSON> trí hiển thị", "linkBanner": "<PERSON>", "textBannersLogin": "<PERSON><PERSON><PERSON> upload theo tỉ lệ 1:1", "textBannersHomepage": "<PERSON><PERSON><PERSON> upload theo tỉ lệ 5:3, tả<PERSON> lên tối đa 5 ảnh", "textBannersOther": "<PERSON><PERSON><PERSON> upload theo tỉ lệ 9:16, tả<PERSON> lên tối đa 5 ảnh", "createSuccess": "<PERSON><PERSON><PERSON> mới chiến dịch thành công", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t chiến dịch thành công", "deleteBannerSuccess": "<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>nh chiến dịch thành công", "addImage": "<PERSON><PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON> mới chiến d<PERSON>ch", "detail": "<PERSON> tiết chiến dịch", "update": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> chi<PERSON>n d<PERSON>ch", "titleImage": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON>"}, "user": {"username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "password": "<PERSON><PERSON><PERSON>", "fullname": "Họ và tên", "dateOfBirth": "<PERSON><PERSON><PERSON>", "email": "Email", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "description": "<PERSON><PERSON>", "department": "Phòng ban", "position": "<PERSON><PERSON><PERSON> danh", "role": "<PERSON>ai trò", "currentPassword": "<PERSON><PERSON><PERSON><PERSON> hiện tại", "newPassword": "<PERSON><PERSON><PERSON> mới", "confirmPassword": "<PERSON><PERSON><PERSON> nh<PERSON>n mật khẩu mới"}, "position": {"positionName": "<PERSON><PERSON><PERSON> ch<PERSON> danh", "positionCode": "<PERSON><PERSON> chức danh", "shortName": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> t<PERSON>", "description": "<PERSON><PERSON>", "createdDate": "<PERSON><PERSON><PERSON>", "createdBy": "<PERSON><PERSON><PERSON><PERSON> tạo", "lastModifiedDate": "<PERSON><PERSON><PERSON> c<PERSON>", "lastModifiedBy": "Người chỉnh sửa"}, "department": {"name": "<PERSON>ên phòng ban", "shortName": "<PERSON><PERSON><PERSON> r<PERSON>n", "code": "Mã phòng ban", "description": "<PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "time": "<PERSON><PERSON><PERSON><PERSON> gian", "createdDate": "<PERSON><PERSON><PERSON>", "createdBy": "<PERSON><PERSON><PERSON><PERSON> tạo", "lastModifiedDate": "<PERSON><PERSON><PERSON> c<PERSON>", "lastModifiedBy": "Người chỉnh sửa"}, "informationTemplate": {"informationTemplateName": "Tên thông tin hỗ trợ", "informationTemplateCode": "<PERSON>ã thông tin hỗ trợ", "displayName": "<PERSON><PERSON><PERSON> hiển thị", "template": "<PERSON><PERSON>i dung", "createdDate": "<PERSON><PERSON><PERSON>", "createdBy": "<PERSON><PERSON><PERSON><PERSON> tạo", "lastModifiedBy": "Người chỉnh sửa cuối cùng", "lastModifiedDate": "<PERSON><PERSON>y chỉnh sửa cuối cùng", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "time": "<PERSON><PERSON><PERSON><PERSON> gian"}, "merchant": {"merchantName": "<PERSON><PERSON><PERSON>", "merchantCode": "Mã Merchant", "merchantAccountNumber": "Số tài <PERSON>n", "merchantBalanceAccount": "Số dư tài k<PERSON> topup", "merchantBankCode": "<PERSON><PERSON> ngân hàng", "description": "<PERSON><PERSON>", "origin": "Loại Merchant", "masterMerchant": {"merchantName": "<PERSON><PERSON><PERSON> Master Merchant", "merchantCode": "Mã Master Merchant", "merchantAccountNumber": "Số tài <PERSON>n", "masterMerchantBilling": "<PERSON><PERSON> <PERSON><PERSON> hóa đơn", "masterMerchantTopup": "<PERSON><PERSON><PERSON> tiền điện thoại", "masterMerchantOther": "<PERSON><PERSON><PERSON>", "serviceType": "<PERSON><PERSON><PERSON> d<PERSON> vụ", "origin": "Loại Master Merchant"}, "transactionHistory": {"name": "<PERSON><PERSON><PERSON> h<PERSON>ng", "cif": "<PERSON><PERSON> h<PERSON> (CIF)", "code": "Mã giao d<PERSON>ch", "totalAmount": "<PERSON><PERSON><PERSON> tiền giao <PERSON> (Lak)", "date": "<PERSON><PERSON><PERSON><PERSON> gian giao d<PERSON>ch", "fromDate": "<PERSON><PERSON><PERSON> giao d<PERSON>ch từ ngày", "discount": "<PERSON><PERSON><PERSON> (%)"}, "monney": "<PERSON>ố tiền giao <PERSON> (Lak)", "fee": "<PERSON><PERSON> g<PERSON> (Lak)", "transferType": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "transactionAmount": "<PERSON><PERSON> tiền giao d<PERSON>ch", "feeAmount": "<PERSON><PERSON> giao d<PERSON>ch", "totalAmount": "<PERSON><PERSON><PERSON> tiền giao d<PERSON>ch"}, "report": {"lapnet": {"id": "Mã giao d<PERSON>ch", "time": "<PERSON><PERSON><PERSON><PERSON> gian", "referenceNumber": "Reference", "fromMember": "Từ thành viên", "fromUser": "Từ người dùng", "fromAccount": "<PERSON>ừ tài k<PERSON>n", "toType": "<PERSON><PERSON><PERSON>", "toAccount": "<PERSON><PERSON><PERSON> tà<PERSON>", "toMember": "<PERSON><PERSON><PERSON> thành viên", "amount": "Số lượng", "currency": "Đơn vị tiền tệ", "purpose": "<PERSON><PERSON><PERSON>", "fee": "Phí"}, "transaction": {"foreignCurrency": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "foreignMoney": "<PERSON><PERSON> tiền giao dịch ngoại tệ", "foreignFee": "<PERSON><PERSON> giao dịch ngo<PERSON>i tệ", "totalForeignAmount": "<PERSON><PERSON><PERSON> tiền giao dịch ngoại tệ", "exchangeRate": "Tỷ giá", "transferMoney": "<PERSON><PERSON><PERSON><PERSON> tiền", "qrcode": "QR Code", "saveTransaction": "<PERSON><PERSON><PERSON> giao <PERSON>", "topup": "<PERSON><PERSON><PERSON> đi<PERSON> tho<PERSON>i", "cashIn": "Nạp v<PERSON>", "billing": "<PERSON><PERSON> <PERSON><PERSON> hóa đơn", "insurance": "<PERSON><PERSON> <PERSON><PERSON> b<PERSON>o <PERSON> L<PERSON>", "internalBank": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> bộ", "interBank": "<PERSON><PERSON><PERSON><PERSON> liên ngân hàng", "internationalBank": "<PERSON><PERSON> to<PERSON> xuyên biên g<PERSON>i", "customerAccNumber": "<PERSON><PERSON><PERSON> nguồn", "transID": "TransID", "clientMessageId": "Client message id", "customerAccountName": "<PERSON><PERSON><PERSON> đ<PERSON>p", "target": "<PERSON><PERSON><PERSON>n", "beneficiaryCustomerName": "<PERSON><PERSON><PERSON>n", "transactionCurrency": "<PERSON><PERSON><PERSON> ti<PERSON>n", "tradingResults": "<PERSON><PERSON><PERSON> qu<PERSON> giao d<PERSON>ch", "transactionStatus": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch", "electric": "<PERSON><PERSON> <PERSON><PERSON> đi<PERSON>n", "water": "<PERSON><PERSON> n<PERSON>", "invoiceNumber": "<PERSON><PERSON> h<PERSON> đơn (AccNo)", "proCode": "Mã tỉnh (ProCode)", "remark": "<PERSON>ội dung thanh toán (Remark)", "titleLa": "<PERSON><PERSON><PERSON> t<PERSON> (Title_LA)", "accName": "<PERSON><PERSON><PERSON><PERSON> d<PERSON> (AccName)", "fixedDiscount": "<PERSON><PERSON><PERSON> kh<PERSON>u cố định (LAK)", "limitPerDay": "<PERSON><PERSON><PERSON> m<PERSON>", "limitPerMonth": "<PERSON><PERSON><PERSON> m<PERSON> tháng", "limitPerYear": "<PERSON><PERSON><PERSON> m<PERSON> n<PERSON>m", "limitPerTransaction": "<PERSON><PERSON><PERSON> mức trong một giao dịch", "contactStatus": "<PERSON><PERSON><PERSON><PERSON> thái liên hệ", "configAutoButton": "<PERSON><PERSON><PERSON> hình gửi báo cáo tự động", "crossBorder": "<PERSON><PERSON><PERSON><PERSON> biên g<PERSON>i", "configAutoReport": {"titleReport": "<PERSON><PERSON><PERSON> hình gửi báo cáo tự động <PERSON>", "entrucstedHistoryTitle": "<PERSON><PERSON><PERSON> hình gửi báo cáo tự động lịch sử giao dịch thu chi hộ <PERSON>oney", "title": "<PERSON>i<PERSON><PERSON> đ<PERSON>ail", "content": "<PERSON>ội dung Email", "time": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> (<PERSON><PERSON>ng <PERSON>)", "status": "<PERSON>r<PERSON><PERSON> thái ho<PERSON>t động", "titleMail": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "addMail": "<PERSON><PERSON><PERSON><PERSON> mới <PERSON>h<PERSON>n", "placeholder": {"time": "<PERSON><PERSON><PERSON> thời gian"}, "error": {"required": {"title": "Tiêu đề không được để trống", "content": "<PERSON><PERSON><PERSON> dung không đư<PERSON><PERSON> để trống", "time": "Thời gian gửi không được để trống", "mail": "<PERSON><PERSON> kh<PERSON>ng đư<PERSON><PERSON> để trống", "syncDate": "<PERSON><PERSON><PERSON> đồng bộ không đư<PERSON>c để trống", "transactionStatuses": "Tr<PERSON>ng thái giao dịch không đ<PERSON><PERSON><PERSON> để trống", "status": "Trạng thái hoạt động không được để trống"}, "pattern": {"emailDuplicate": "<PERSON><PERSON> kh<PERSON>ng đ<PERSON><PERSON><PERSON> trùng nhau"}}}, "configAutoReportLapNet": {"titleReport": "<PERSON><PERSON><PERSON> hình gửi báo cáo tự động LAPNET"}, "configAutoReportLapNetUmoney": {"titleReport": "<PERSON><PERSON><PERSON> hình gửi báo cáo tự động LAPNET UMONEY"}, "configAutoNotificationHistory": {"titleReport": "<PERSON><PERSON>u hình gửi mail thông báo thu phí lỗi", "configAutoButton": "<PERSON><PERSON>u hình gửi mail"}}}, "referral": {"userFullName": "Họ và tên", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "userCode": "Mã nhân viên", "rmCode": "Mã RM", "referralId": "<PERSON>ã giới thiệu", "rmLink": "<PERSON> gi<PERSON>i thiệu", "titleDetail": "<PERSON> tiết mã giới thiệu", "type": "<PERSON><PERSON><PERSON>", "import-export": "Xuất/tải file", "updateTitle": "<PERSON><PERSON><PERSON> nhật mã giới thiệu", "createTitle": "Tạo mới mã giới thiệu", "qrCode": "Mã QR", "quantity": "Số lượng khách đã giới thiệu", "referralCode": "<PERSON>ã giới thiệu", "referralCodeUser": "<PERSON>ã người giới thiệu", "referrerPhoneNumber": "SĐT người giới thiệu", "referrerName": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i giới thiệu", "referrerInformation": "Thông tin người giới thiệu"}, "versionManage": {"no": "STT", "versionName": "<PERSON><PERSON><PERSON> p<PERSON>ê<PERSON> bản", "versionCode": "<PERSON><PERSON> p<PERSON><PERSON>n bản", "forceUpdate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> cậ<PERSON> nh<PERSON>t", "noti": "<PERSON><PERSON><PERSON><PERSON> báo", "handUpdate": "<PERSON><PERSON><PERSON> nh<PERSON>t thủ công", "installed": "Đã cài đặt", "urlStore": "URL store", "createdDate": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "action": "<PERSON><PERSON>", "fromDate": "<PERSON><PERSON> ngày", "toDate": "<PERSON><PERSON><PERSON>", "detailIos": "<PERSON> tiết phiên bản hệ điều hành IOS", "createIos": "<PERSON><PERSON><PERSON> phiên bản mới cho hệ điều hành IOS", "detailAndroid": "<PERSON> tiết phiên bản hệ điều hành <PERSON>", "createAndroid": "<PERSON><PERSON><PERSON> phiên bản mới cho hệ điều hành <PERSON>", "updateIos": "<PERSON><PERSON><PERSON> nh<PERSON>t phiên bản cho hệ điều hành IOS", "updateAndroid": "<PERSON><PERSON><PERSON> nh<PERSON>t phiên bản cho hệ điều hành <PERSON>", "versionStatus": {"published": "<PERSON><PERSON> ph<PERSON>t hành", "unreleased": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> h<PERSON>nh"}, "form": {"versionCode": "<PERSON><PERSON> p<PERSON><PERSON>n bản", "versionName": "<PERSON><PERSON><PERSON> p<PERSON>ê<PERSON> bản", "versionContent": "<PERSON><PERSON><PERSON> dung cập nh<PERSON>t", "releaseDate": "<PERSON><PERSON><PERSON> ph<PERSON>t hành phiên bản", "forceUpdate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> cậ<PERSON> nh<PERSON>t", "manualUpdate": "<PERSON><PERSON><PERSON> nh<PERSON>t thủ thông", "forceNotification": "<PERSON><PERSON><PERSON><PERSON> báo", "urlStore": "Url store"}, "placeholder": {"versionName": "<PERSON><PERSON><PERSON><PERSON> tên phiên bản", "versionCode": "<PERSON><PERSON><PERSON><PERSON> mã phiên bản", "versionContent": "<PERSON><PERSON><PERSON> dung cập nh<PERSON>t", "urlStore": "Đường link cập nhật"}}, "feeSchedule": {"title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "no": "STT", "image": "<PERSON><PERSON><PERSON>", "createdDate": "<PERSON><PERSON><PERSON><PERSON> gian tạo", "status": "<PERSON>rang thái", "information": "Thông tin biểu phí", "lock": "<PERSON><PERSON><PERSON> nh<PERSON>n khóa biểu phí", "lockContent": "Bạn có chắc chắn muốn khoá biểu phí {{title}} không?", "unlock": "<PERSON><PERSON><PERSON> nhận mở khóa biểu phí", "unlockContent": "Bạn có chắc chắn muốn mở khóa biểu phí {{title}} không?", "create": "Tạo mới biểu phí", "detail": "<PERSON> tiết biểu phí", "update": "<PERSON><PERSON><PERSON> nhật biểu phí", "modal": {"delete": "<PERSON><PERSON><PERSON> nh<PERSON>n xóa biểu phí", "deleteRateContent": "Bạn có chắc chắn muốn xóa biểu phí này ?"}}, "notificationLimit": {"contacted": "<PERSON><PERSON><PERSON> nhận chuyển trạng thái đã liên hệ", "notContacted": "<PERSON><PERSON><PERSON> nhận chuyển trạng thái chưa liên hệ", "contactedContent": "Bạn có chắc chắn muốn chuyển trạng thái đã liên hệ tên đăng nhập {{fullname}} không?", "notContactedContent": "<PERSON><PERSON>n có chắc chắn muốn chuyển trạng thái chưa liên hệ tên đăng nhập {{fullname}} không?", "title": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>o thanh to<PERSON> v<PERSON><PERSON><PERSON> hạn mức", "notiTabTitle": "<PERSON><PERSON><PERSON> m<PERSON>c giao d<PERSON>ch"}, "monitorLog": {"type": "Type", "clientMessageId": "ClientMessageId", "method": "Method", "requestUri": "RequestUri", "serviceName": "ServiceName", "duration": "Duration", "httpStatus": "HttpStatus", "requestTime": "RequestTime", "responseTime": "ResponseTime"}, "transactionQrPay": {"bankTransId": "Mã giao d<PERSON>ch", "partnerId": "<PERSON><PERSON> đối tác", "bankAccount": "<PERSON><PERSON><PERSON> nguồn", "accountNumber": "<PERSON><PERSON><PERSON> k<PERSON>n thụ hưởng", "amount": "<PERSON><PERSON> tiền", "content": "<PERSON><PERSON>i dung", "transDate": "<PERSON><PERSON><PERSON><PERSON> gian giao d<PERSON>ch"}}, "template": {"loanOnline": "<PERSON><PERSON>_<PERSON><PERSON>_Dan<PERSON>_ky_<PERSON><PERSON>_T<PERSON><PERSON>_Tuyen", "merchantHistory": "Lich_<PERSON>_Giao_Dich_Merchant_{{param}}", "masterMerchantHistory": "Lich_<PERSON>_<PERSON><PERSON><PERSON>_Dich_Master_Merchant_{{param}}", "customerEvent": "<PERSON><PERSON>_sach_khach_hang", "eventReceiver": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>o", "transactionReport": "Lich_Su_Giao_Dich_Khac", "transactionNotifiLimitReport": "<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>", "transactionReportUmoney": "<PERSON>ch_<PERSON>_G<PERSON><PERSON>_<PERSON><PERSON>_Umoney", "transactionReportDebitDeposit": "Lich_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>ch_<PERSON>hu_<PERSON>_Ho", "lapnetReport": "<PERSON><PERSON>_<PERSON>_<PERSON>", "customerRegistration": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_Dang_<PERSON>y", "merchant": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>", "masterMerchant": "<PERSON><PERSON>_<PERSON>ch_Master_Merchant", "referralTemplate": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>", "referral": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>", "android": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_Ban_<PERSON>", "ios": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_Ban_IOS", "insurance": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>em", "configTransLimit": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_G<PERSON><PERSON>_Dich", "campaign": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>", "sms": "<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_SMS", "customerTemplate": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>", "errorImportCustomer": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>ch_Hang", "transReport": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>ch", "savingAccount": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>em", "smsBalance": "<PERSON><PERSON>_<PERSON>ch_On_Off_SMS", "transactionMMoneyReport": "<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_to<PERSON>_<PERSON><PERSON>_<PERSON>c", "activateAccountManual": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>hu_Cong", "accountNoTransaction": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_G<PERSON>o_Dich", "accountStructure": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_Dep", "errorImportPremiumAccount": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_Import_Cau_Truc_TKSĐ", "transactionQrpay": "<PERSON><PERSON>_<PERSON>_Giao_Dich_QRPAY", "premiumAccountNumberSoldReport": "<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_Ban", "specialAccountNumber": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_Biet", "premiumAccountNumberReport": "<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>uu_<PERSON>_Dep", "numberGroupReport": "<PERSON><PERSON>_<PERSON>_<PERSON>hom_<PERSON>", "luckyNumberAccountStructure": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_Truc_So", "errorImportSpecialAccount": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_I<PERSON>rt_So_Dep_Dac_Biet", "premiumAccountRevertReport": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>hu_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>i", "smsBalanceChargeFail": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>hu_Phi_SMS_BĐSD_Loi", "internationalPaymentTemplate": "<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_Bien_Gioi", "dotpManagement": "<PERSON><PERSON>_<PERSON>_<PERSON>uan_Ly_DOTP"}, "department": {"lockTitle": "<PERSON><PERSON><PERSON> nh<PERSON>n khóa phòng ban", "unlockTitle": "<PERSON><PERSON><PERSON> nhận mở khóa phòng ban", "deleteTitle": "<PERSON><PERSON><PERSON> nhận xóa phòng ban", "lockContent": "Bạn có chắc chắn muốn khoá phòng ban {{departmentName}} không?", "unlockContent": "Bạn có chắc chắn muốn mở khoá phòng ban {{departmentName}} không?", "deleteContent": "Bạn có chắc chắn muốn xóa phòng ban {{departmentName}} không?", "create": "<PERSON><PERSON>o mới phòng ban", "update": "<PERSON><PERSON><PERSON> nhật phòng ban", "detail": "<PERSON> tiết phòng ban", "error": {"required": {"departmentCode": "<PERSON><PERSON> phòng ban không được để trống", "departmentName": "Tên phòng ban không được để trống", "status": "Tr<PERSON>ng thái không được để trống", "departmentShortName": "<PERSON><PERSON><PERSON> viết tắt phòng ban không đư<PERSON><PERSON> để trống"}, "maxLength": {"shortName": "<PERSON><PERSON>n rút gọn có tối đa {{param}} kí tự", "description": "<PERSON><PERSON> tả có tối đa {{param}} kí tự", "departmentCode": "<PERSON>ã phòng ban có tối đa {{param}} kí tự", "departmentName": "Tên phòng ban có tối đa {{param}} kí tự"}, "minLength": {"departmentCode": "<PERSON><PERSON> phòng ban tối thiểu {{param}} kí tự", "departmentName": "Tên phòng ban tối thiêu {{param}} kí tự"}, "pattern": {"departmentName": "<PERSON>ên phòng ban chỉ bao gồm chữ cái, chữ số và dấu cách", "departmentShortName": "<PERSON><PERSON>n rút gọn phòng ban chỉ bao gồm chữ cái, chữ số và dấu cách"}}}, "informationTemplate": {"lockTitle": "<PERSON><PERSON><PERSON> nhận khóa thông tin hỗ trợ", "unlockTitle": "<PERSON><PERSON><PERSON> nhận mở khóa thông tin hỗ trợ", "deleteTitle": "<PERSON><PERSON><PERSON> nhận xóa thông tin hỗ trợ", "lockContent": "Bạn có chắc chắn muốn khoá thông tin hỗ trợ {{informationTemplateName}} không?", "unlockContent": "Bạn có chắc chắn muốn mở khoá thông tin hỗ trợ {{informationTemplateName}} không?", "deleteContent": "Bạn có chắc chắn muốn xóa thông tin hỗ trợ {{informationTemplateName}} không?", "create": "Tạo mới thông tin hỗ trợ", "update": "<PERSON><PERSON><PERSON> nhật thông tin hỗ trợ", "detail": "<PERSON> tiết thông tin hỗ trợ", "infor": "Thông tin hỗ trợ", "error": {"required": {"informationTemplateCode": "<PERSON>ã thông tin hỗ trợ không được để trống", "informationTemplateName": "Tên thông tin hỗ trợ không được để trống", "status": "Tr<PERSON>ng thái không được để trống", "informationTemplateDisplayName": "Tên hiển thị không đư<PERSON><PERSON> để trống", "template": "<PERSON><PERSON>i dung thông tin hỗ trợ không được để trống"}, "maxLength": {"displayName": "Tên hiển thị có tối đa {{param}} kí tự", "description": "<PERSON><PERSON> tả có tối đa {{param}} kí tự", "informationTemplateCode": "<PERSON>ã thông tin hỗ trợ có tối đa {{param}} kí tự", "informationTemplateName": "<PERSON>ên thông tin hỗ trợ có tối đa {{param}} kí tự", "informationTemplateDisplayName": "Tên hiển thị thông tin hỗ trợ có tối đa {{param}} kí tự", "template": "<PERSON><PERSON><PERSON> dung có tối đa {{param}} kí tự"}, "pattern": {"informationTemplateName": "<PERSON>ên thông tin hỗ trợ chỉ bao gồm chữ cái, chữ số và dấu cách", "informationTemplateDisplayName": "Tên hiển thị thông tin hỗ trợ chỉ bao gồm chữ cái, chữ số và dấu cách", "informationTemplateCode": "<PERSON>ã thông tin hỗ trợ không đúng định dạng"}}}, "smsBalance": {"root": "<PERSON><PERSON><PERSON><PERSON> lý dịch vụ on/off sms biến động số dư", "customerName": "<PERSON><PERSON><PERSON> h<PERSON>ng", "cif": "Số CIF", "customerAccountNumber": "<PERSON><PERSON><PERSON> k<PERSON>n đăng ký", "issueDate": "<PERSON><PERSON><PERSON> ký", "cancellationDate": "Ngày huỷ đăng ký", "manageSmsBalanceTitle": "<PERSON><PERSON> s<PERSON>ch dịch vụ on/off SMS biến động số dư", "notificationFail": "<PERSON><PERSON> ph<PERSON> <PERSON> biến động số dư", "chargeFee": "<PERSON>hu phí tài k<PERSON>n", "expectedTime": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> (Phút/lần)", "error": {"required": {"expectedTime": "Thời gian gửi không được để trống"}}}, "servicePack": {"root": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> vụ", "createTitle": "Tạo mới dịch vụ", "updateTitle": "<PERSON><PERSON><PERSON> n<PERSON><PERSON><PERSON> d<PERSON> v<PERSON>", "detailTitle": "<PERSON> ti<PERSON><PERSON> d<PERSON>ch vụ", "titleInforUser": "Thông tin dịch vụ", "lock": "<PERSON><PERSON><PERSON>n kh<PERSON>a dịch vụ", "unlock": "<PERSON><PERSON><PERSON>n mở khóa dịch vụ", "delete": "<PERSON><PERSON><PERSON>n x<PERSON><PERSON> d<PERSON>ch vụ", "lockContent": "Bạn có chắc chắn muốn khoá dịch vụ {{name}} không?", "unlockContent": "Bạn có chắc chắn muốn mở khoá dịch vụ {{name}} không?", "deleteContent": "Bạn có chắc chắn muốn xóa dịch vụ {{name}} không?", "deleteContentUrl": "Bạn có chắc chắn muốn xóa thông tin API {{name}} này không?", "name": "<PERSON><PERSON><PERSON> v<PERSON>", "type": "<PERSON><PERSON><PERSON> d<PERSON> vụ", "code": "Mã d<PERSON>ch vụ", "clientId": "ClientID", "url": "Địa chỉ url", "password": "<PERSON><PERSON><PERSON>", "listAPI": "<PERSON><PERSON>", "nameAPI": "Tên API", "carrieType": "<PERSON><PERSON><PERSON> mạng", "list": "<PERSON><PERSON> s<PERSON>ch d<PERSON>ch vụ", "error": {"required": {"clientId": "ClientID không đư<PERSON><PERSON> để trống", "password": "<PERSON><PERSON><PERSON> kh<PERSON>u không đư<PERSON><PERSON> để trống", "status": "Tr<PERSON>ng thái không được để trống", "type": "<PERSON><PERSON><PERSON> dịch vụ không đ<PERSON><PERSON><PERSON> để trống", "name": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> vụ không đ<PERSON><PERSON><PERSON> để trống", "url": "Địa chỉ url không được để trống", "nameFunction": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "nameAPI": "Tên api không đư<PERSON><PERSON> để trống"}, "minLength": {"clientId": "ClientID tối thiểu {{param}} kí tự", "password": "<PERSON><PERSON><PERSON> kh<PERSON>u tối thiêu {{param}} kí tự"}, "pattern": {"clientId": "ClientID không đúng định dạng", "password": "<PERSON><PERSON><PERSON> kh<PERSON>u không đúng định dạng", "name": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> v<PERSON> k<PERSON>ng h<PERSON>p lệ", "nameAPI": "<PERSON><PERSON><PERSON> a<PERSON> không hợp lệ", "url": "Đ<PERSON>a chỉ url không hợp lệ"}}}, "premiumAccNumber": {"pattern": {"premiumAccNumber": "<PERSON><PERSON><PERSON> k<PERSON>n số đẹp không đúng định dạng", "firstDigitZero": "<PERSON><PERSON><PERSON> k<PERSON>n không tồn tại", "expireOtp": "Mã OTP hết hiệu lực. <PERSON><PERSON> lòng nhấn gửi lại OTP để nhận mã mới"}, "success": {"requestOtp": "<PERSON><PERSON><PERSON> yêu cầu <PERSON><PERSON> thành công", "confirmOtp": "Mở tài khoản số đẹp thành công", "sendOtp": "Gửi OTP thành công"}, "error": {"maxLength": {"premiumAccNumber": "<PERSON><PERSON><PERSON> kho<PERSON>n số đẹp phải từ 4 đến 10 ký tự"}, "minLength": {"otp": "Mã OTP gồm 8 ký tự số"}, "required": {"premiumAccNumber": "<PERSON><PERSON><PERSON> k<PERSON>n số đẹp không được để trống", "accountType": "<PERSON><PERSON>i tài khoản số đẹp không được để trống", "otpValue": "Mã OTP không đư<PERSON>c để trống"}}}, "client": {"root": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "createTitle": "Tạo mới client", "updateTitle": "<PERSON><PERSON><PERSON> nh<PERSON> client", "detailTitle": "<PERSON> tiết client", "titleInforUser": "Thông tin client", "lock": "<PERSON><PERSON><PERSON> kh<PERSON> client", "unlock": "<PERSON><PERSON><PERSON> nh<PERSON>n mở khóa client", "delete": "<PERSON><PERSON><PERSON>n x<PERSON>a client", "lockContent": "Bạn có chắc chắn muốn khoá client {{name}} không?", "unlockContent": "Bạn có chắc chắn muốn mở khoá client {{name}} không?", "deleteContent": "Bạn có chắc chắn muốn xóa client {{name}} không?", "name": "Tên client", "type": "<PERSON><PERSON> lo<PERSON>", "clientId": "ClientID", "password": "<PERSON><PERSON><PERSON>", "inter": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> thống", "internal": "<PERSON><PERSON><PERSON> bộ", "error": {"required": {"clientId": "ClientID không đư<PERSON><PERSON> để trống", "password": "<PERSON><PERSON><PERSON> kh<PERSON>u không đư<PERSON><PERSON> để trống", "status": "Tr<PERSON>ng thái không được để trống", "type": "<PERSON>ân loại không đư<PERSON>c để trống", "name": "Tên client kh<PERSON>ng đ<PERSON><PERSON><PERSON> để trống"}, "minLength": {"clientId": "ClientID tối thiểu {{param}} kí tự", "password": "<PERSON><PERSON><PERSON> kh<PERSON>u tối thiêu {{param}} kí tự"}, "pattern": {"clientId": "ClientID không đúng định dạng", "password": "<PERSON><PERSON><PERSON> kh<PERSON>u không đúng định dạng", "name": "Tên client kh<PERSON><PERSON> hợp lệ"}}}, "numberGroup": {"root": "Nhóm số", "list": "<PERSON><PERSON> s<PERSON>", "createTitle": "Tạo mới Nhó<PERSON> số", "updateTitle": "<PERSON><PERSON><PERSON>", "detailTitle": "<PERSON> ti<PERSON>t <PERSON> số", "titleInforUser": "<PERSON>h<PERSON><PERSON> tin Nhóm số", "lock": "<PERSON><PERSON><PERSON> khó<PERSON> Nhóm số", "unlock": "<PERSON><PERSON><PERSON>h<PERSON> mở khóa Nhóm số", "delete": "<PERSON><PERSON><PERSON>n x<PERSON><PERSON> số", "lockContent": "Bạn có chắc chắn muốn khoá <PERSON>ó<PERSON> số {{name}} không?", "unlockContent": "Bạn có chắc chắn muốn mở khoá <PERSON> số {{name}} không?", "deleteContent": "Bạn muốn x<PERSON>a <PERSON> số {{name}} khỏi danh sách?", "deleteModalContent": "<b>{{name}}</b> đang đ<PERSON> áp dụng cho các <b><PERSON><PERSON><PERSON> trúc số</b>:", "performModalContent": "Để xóa thành công nhóm số, bạn hãy ngắt sự phụ thuộc của các Cấu trúc số?", "name": "<PERSON><PERSON><PERSON> s<PERSON>", "code": "Mã", "minPrice": "<PERSON><PERSON><PERSON> thấp nh<PERSON>t", "maxPrice": "<PERSON><PERSON><PERSON> cao nh<PERSON>t", "message": {"understand": "Tôi đã hiểu", "warning": "Chú ý: ", "text": "Chú ý: <span class=\"text-primary-color\" style=\"color: red\">Bạn vừa thay đổi khoảng giá của <b>{{name}}</b>, sự thay đổi này có thể ảnh hưởng tới thông tin giá của những <b>Cấu trúc số</b> đang phụ thuộc!</span>"}, "error": {"required": {"password": "<PERSON><PERSON><PERSON> kh<PERSON>u không đư<PERSON><PERSON> để trống", "status": "Tr<PERSON>ng thái không được để trống", "type": "<PERSON>ân loại không đư<PERSON>c để trống", "name": "<PERSON><PERSON><PERSON> nh<PERSON>m số không được để trống", "minPrice": "<PERSON><PERSON><PERSON> thấp nhất không được để trống", "maxPrice": "<PERSON><PERSON><PERSON> cao nhất không được để trống", "invalidNumber": "<PERSON><PERSON><PERSON> thấp nhất không đư<PERSON>c lớn hơn giá cao nhất"}, "minLength": {"password": "<PERSON><PERSON><PERSON> kh<PERSON>u tối thiêu {{param}} kí tự"}, "maxLength": {"minPrice": "<PERSON><PERSON><PERSON> thấp nhất không hợp lệ", "maxPrice": "<PERSON><PERSON><PERSON> cao nhất không hợp lệ"}, "pattern": {"password": "<PERSON><PERSON><PERSON> kh<PERSON>u không đúng định dạng", "name": "<PERSON><PERSON><PERSON> nh<PERSON>m số không hợp lệ", "minPrice": "<PERSON><PERSON><PERSON> thấp nhất không đư<PERSON>c lớn hơn giá cao nhất"}}}, "currency": {"root": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "list": "<PERSON><PERSON> s<PERSON>ch tiền tệ", "createTitle": "<PERSON><PERSON><PERSON> mới tiền tệ", "updateTitle": "<PERSON><PERSON><PERSON> nhật tiền tệ", "detailTitle": "<PERSON> tiết tiền tệ", "titleInforUser": "Th<PERSON>ng tin tiền tệ", "lock": "<PERSON><PERSON><PERSON> nh<PERSON>n khóa tiền tệ", "unlock": "<PERSON><PERSON><PERSON> nhận mở khóa tiền tệ", "delete": "<PERSON><PERSON><PERSON> nhận xóa tiền tệ", "lockContent": "Bạn có chắc chắn muốn khoá tiền tệ {{name}} không?", "unlockContent": "Bạn có chắc chắn muốn mở khoá tiền tệ {{name}} không?", "deleteContent": "Bạn muốn xóa tiền tệ {{name}} khỏi danh sách?", "name": "<PERSON><PERSON><PERSON> ti<PERSON>n tệ", "code": "<PERSON><PERSON> tiền tệ", "value": "<PERSON>ã tiền tệ khi tạo QR", "error": {"required": {"password": "<PERSON><PERSON><PERSON> kh<PERSON>u không đư<PERSON><PERSON> để trống", "status": "Tr<PERSON>ng thái không được để trống", "type": "<PERSON>ân loại không đư<PERSON>c để trống", "name": "Tên tiền tệ không được để trống", "invalidNumber": "<PERSON><PERSON><PERSON> thấp nhất không đư<PERSON>c lớn hơn giá cao nhất"}, "minLength": {"name": "Tên tiền tệ tối thiêu {{param}} kí tự", "code": "<PERSON>ã tiền tệ tối thiêu {{param}} kí tự"}}}, "premiumAccountRevert": {"root": "<PERSON><PERSON><PERSON><PERSON> lý thu phí TKSĐ thất bại", "list": "<PERSON><PERSON><PERSON><PERSON> lý thu phí tài khoản số đẹp thất bại", "date": "<PERSON><PERSON><PERSON> tháng năm thu phí lỗi", "limit": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> v<PERSON><PERSON><PERSON> hạn mức", "premium": "<PERSON><PERSON><PERSON><PERSON> báo tài kho<PERSON>n số đẹp thu hồi thất bại", "title": "Thông báo thu phí tài khoản số đẹp thất bại", "note1": "<PERSON>hu phí tài k<PERSON>n số đẹp ", "note2": " c<PERSON><PERSON> k<PERSON><PERSON>ch h<PERSON>ng ", "note3": " thất b<PERSON>i", "contentContracted": "Bạn có chắc chắn muốn chuyển trạng thái đã liên hệ khách hàng {{name}} này?", "contentNotContracted": "Bạn có chắc chắn muốn chuyển trạng thái chưa liên hệ khách hàng {{name}} này?"}, "managementSMSBalanceFeeFailed": {"root": "<PERSON><PERSON><PERSON><PERSON> lý thu phí SMS BĐSD thất bại", "list": "<PERSON><PERSON><PERSON><PERSON> lý thu phí <PERSON> biến động số dư thất bại"}, "managePayment": {"no": "STT", "accountNumber": "Số tài <PERSON>n", "accountName": "<PERSON><PERSON><PERSON> tà<PERSON>", "bankName": "<PERSON><PERSON> h<PERSON>", "currency": "Đơn vị tiền tệ", "balance": "Số dư", "usageBalance": "Số tiền đã chuyển trong ngày", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "action": "<PERSON><PERSON>", "cif": "Số cif", "addedTransfer": "Số tiền đã cộng vào trong ngày", "modal": {"create": "<PERSON><PERSON><PERSON> mới", "edit": "<PERSON><PERSON><PERSON>", "detail": "<PERSON> ti<PERSON>", "accountName": "<PERSON><PERSON><PERSON> tà<PERSON>", "bankBenificiary": "<PERSON><PERSON> hàng thụ hưởng", "currency": "Đơn vị tiền tệ", "currencyHolder": "Đơn vị"}, "msg": {"lock": "<PERSON><PERSON><PERSON> kh<PERSON>a tài k<PERSON>n", "unlock": "<PERSON><PERSON><PERSON> n<PERSON>n mở khóa tài kho<PERSON>n", "delete": "<PERSON><PERSON><PERSON>n x<PERSON>a tài <PERSON>n", "lockContent": "Bạn có chắc chắn muốn khoá tài khoản {{name}} không?", "unlockContent": "Bạn có chắc chắn muốn mở khoá tài khoản {{name}} không?", "deleteContent": "Bạn muốn xóa tài k<PERSON>n {{name}} khỏi danh sách?"}, "bank": "<PERSON><PERSON> h<PERSON>n", "bankCode": "<PERSON><PERSON> ngân hàng nh<PERSON>n", "remark": "<PERSON><PERSON><PERSON> dung chuyển k<PERSON>n", "nameTransfer": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng ch<PERSON>n", "account": "Số TK <PERSON>h toán của <PERSON> chuyển", "amount": "<PERSON><PERSON> tiền", "fee": "Số phí", "remarkPayment": "<PERSON><PERSON><PERSON> dung thu phí", "fromUserFullName": "<PERSON><PERSON><PERSON> h<PERSON>ng", "fromMember": "<PERSON><PERSON> ngân hàng gọi ho<PERSON>ch toán", "fromaccount": "<PERSON><PERSON> điện thoại tài kho<PERSON>n gửi", "fromUser": "STK tài khoản ví/bank", "debit": "<PERSON>", "deposit": "<PERSON><PERSON>", "accTransactionCode": "<PERSON><PERSON> giao d<PERSON>ch ho<PERSON>ch toán", "limit": "<PERSON><PERSON><PERSON> m<PERSON>/giao d<PERSON>ch"}, "dotp": {"title": "Q<PERSON>ản lý tài khoản đăng ký DOTP", "search": {"keyword": {"placeholder": "<PERSON><PERSON><PERSON><PERSON> từ khóa tìm kiếm (CIF, tên kh<PERSON><PERSON> hàng, số điện thoại)"}}, "table": {"cifNumber": "Số CIF", "customerName": "<PERSON><PERSON><PERSON> h<PERSON>ng", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "deviceId": "<PERSON><PERSON> thiết bị", "registrationDate": "<PERSON><PERSON><PERSON> ký", "cancelDate": "Ngày huỷ", "deviceName": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> bị"}, "modal": {"cancel": {"title": "<PERSON><PERSON><PERSON> nh<PERSON>n huỷ DOTP", "content": "Bạn có chắc chắn muốn huỷ DOTP của khách hàng {{customerName}} không?", "success": "Huỷ DOTP thành công"}}}}