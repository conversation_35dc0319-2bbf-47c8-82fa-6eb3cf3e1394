import { Component, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  Validators,
} from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  CALCULATE_MILLISECONDS_IN_A_YEAR,
  DATE_CONSTANT,
  DATE_RANGE_STATISTICAL_CUSTOMER,
  ENTITY_STATUS_CONST,
  FILE_EXTENSION,
  GENDER,
  GENDER_MAP,
  MODAL_ACTION,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import {
  APPROVAL_STATUS_CONST,
  CUSTOMER_ACTIVE_STATUS,
  CUSTOMER_ACTIVE_STATUS_MAP,
  CUSTOMER_SECTOR,
  CUSTOMER_SECTOR_CONST,
  CUSTOMER_SECTOR_MAP,
  CUSTOMER_STATUS,
  CUSTOMER_STATUS_CONST,
  CUSTOMER_STATUS_MAP,
  CUSTOMER_TYPE,
  CUSTOMER_TYPE_CONST,
  CUSTOMER_TYPE_MAP,
} from '@shared/constants/customer.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ICustomer } from '@shared/models/customer.model';
import { ICustomerSearch } from '@shared/models/request/customer.search';
import { CustomerService } from '@shared/services/customer.service';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';
import { AccountNumberModalComponent } from './account-number-modal/account-number-modal.component';

@Component({
  selector: 'app-customer-register-management',
  templateUrl: './customer-register-management.component.html',
  styleUrls: ['./customer-register-management.component.scss'],
})
export class CustomerRegisterManagementComponent implements OnInit {
  customer: ICustomer[] = [];
  data: any[] = [];
  recordSelected: any = [];
  storage: any;
  action: any = '';
  ngSelect = '';
  maxDate = new Date();
  maxToDate = new Date();
  indeterminate = false;
  checked = false;
  onShowButtonAndCol = false;
  setOfCheckedId = new Set();

  ROUTER_UTILS = ROUTER_UTILS;
  pageSizeOptions = PAGINATION.OPTIONS;
  VALIDATORS = VALIDATORS;
  CUSTOMER_STATUS = CUSTOMER_STATUS;
  CUSTOMER_STATUS_CONST = CUSTOMER_STATUS_CONST;
  GENDER_LIST = GENDER;
  GENDER_MAP = GENDER_MAP;
  CUSTOMER_STATUS_MAP = CUSTOMER_STATUS_MAP;
  SYSTEM_RULES = SYSTEM_RULES;
  APPROVAL_STATUS_CONST = APPROVAL_STATUS_CONST;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  CUSTOMER_ACTIVE_STATUS_MAP = CUSTOMER_ACTIVE_STATUS_MAP;
  CUSTOMER_ACTIVE_STATUS = CUSTOMER_ACTIVE_STATUS;
  CUSTOMER_SECTOR_CONST = CUSTOMER_SECTOR_CONST;
  CUSTOMER_SECTOR = CUSTOMER_SECTOR;
  CUSTOMER_SECTOR_MAP = CUSTOMER_SECTOR_MAP;
  CUSTOMER_TYPE_CONST = CUSTOMER_TYPE_CONST;
  CUSTOMER_TYPE = CUSTOMER_TYPE;
  CUSTOMER_TYPE_MAP = CUSTOMER_TYPE_MAP;

  isValidMaxDate = (control: FormControl) => {
    const dateOfBirth = new Date(control.value).getTime();
    const maxDateOfBirth = new Date().getTime();
    return dateOfBirth > maxDateOfBirth ? { invalidMaxDate: true } : null;
  };

  // default data form
  form = this.fb.group({
    cif: ['', [Validators.pattern(VALIDATORS.PATTERN.CIF_SEARCH)]],
    idCardNumber: [
      '',
      [Validators.pattern(VALIDATORS.PATTERN.ID_CARD_NUMBER_SEARCH)],
    ],
    username: '',
    fullname: '',
    status: null,
    activeStatus: null,
    phoneNumber: ['', [Validators.pattern(VALIDATORS.PATTERN.PHONE_SEARCH)]],
    email: '',
    createdDateStartAt: [null],
    createdDateEndAt: [null],
    customerSectorId: null,
    accountType: null,
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    // previousPageIndex: 0,
  });

  constructor(
    private customerService: CustomerService,
    private modalService: ModalService,
    private fb: FormBuilder,
    private router: Router,
    private toastService: ToastrCustomService,
    private translateService: TranslateService,
    private downloadService: DownloadService,
    private ngb: NgbModal
  ) {
    this.storage = sessionStorage.getItem(STORAGE_APP.CUSTOMER_REGISTER);
  }

  ngOnInit(): void {
    if (this.storage) {
      // get session storage and parse json
      const filter = JSON.parse(this.storage) as ICustomerSearch;
      // set value form control
      if (filter.status) {
        const status = +filter.status;
        this.form.controls.status.setValue(status);
      }
      if (filter.activeStatus) {
        const activeStatus = +filter.activeStatus;
        this.form.controls.activeStatus.setValue(activeStatus);
      }
      this.form.controls.cif.setValue(filter.cif);
      this.form.controls.idCardNumber.setValue(filter.idCardNumber);
      this.form.controls.username.setValue(filter.username);
      this.form.controls.fullname.setValue(filter.fullname);
      this.form.controls.phoneNumber.setValue(filter.phoneNumber);
      this.form.controls.email.setValue(filter.email);
      this.form.controls.customerSectorId.setValue(filter.customerSectorId);
      if (filter.createdDateStartAt) {
        this.form.controls.createdDateStartAt.setValue(
          CommonUtils.formatDate(
            CommonUtils.newDate(filter.createdDateStartAt),
            DATE_CONSTANT.YYYYMMDD_HYPHEN_BIG
          )
        );
      }
      if (filter.createdDateEndAt) {
        this.form.controls.createdDateEndAt.setValue(
          CommonUtils.formatDate(
            CommonUtils.newDate(filter.createdDateEndAt),
            DATE_CONSTANT.YYYYMMDD_HYPHEN_BIG
          )
        );
      }
      const search = { ...this.form.value };
      delete search.length;
      delete search.pageSize;
      // delete body.previousPageIndex;
      delete search.pageIndex;
      if (!CommonUtils.objectIsEmpty(search)) {
        this.onSearch();
      }
    }
    sessionStorage.removeItem(STORAGE_APP.CUSTOMER_REGISTER);
  }

  /**
   * next page and preview page
   *
   * @param page PageEvent
   */
  onChangePage(page: PageEvent) {
    this.form.controls.pageIndex.setValue(page.pageIndex);
    this.form.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  onSearchSubmit() {
    this.form.controls.pageIndex.setValue(PAGINATION.PAGE_NUMBER_DEFAULT);
    this.form.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    if (
      !(
        this.form.controls.createdDateStartAt.value &&
        this.form.controls.createdDateEndAt.value
      )
    ) {
      this.form.controls.createdDateStartAt.clearValidators();
      this.form.controls.createdDateStartAt.updateValueAndValidity();
      this.form.controls.createdDateEndAt.clearValidators();
      this.form.controls.createdDateEndAt.updateValueAndValidity();
    }
    this.onSearch();
  }

  onSearch() {
    const body = { ...this.form.value };
    delete body.length;
    delete body.pageSize;
    // delete body.previousPageIndex;
    this.changeValidDate(true);
    delete body.pageIndex;
    if (CommonUtils.objectIsEmpty(body)) {
      this.toastService.warning('common.searchEmpty');
      return;
    }
    if (this.form.valid) {
      this.customerService
        .search(this.form.value)
        .subscribe((res: any): void => {
          this.data = res.body.content;
          this.form.controls.length.setValue(res.body.totalElements);
          this.refreshCheckedStatus();
          if (this.data && this.data.length > 0) {
            this.onShowButtonAndCol = res?.body?.content?.some(
              (item: ICustomer) => {
                return item;
              }
            );
          } else {
            this.onShowButtonAndCol = false;
          }
        });
    }
  }

  validateDate() {
    if (this.form.controls['createdDateEndAt'].value) {
      this.maxDate = this.form.controls['createdDateEndAt'].value;
    }
  }

  dateValidator(value: number): any {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return value > DATE_RANGE_STATISTICAL_CUSTOMER
        ? { invalidDate: true }
        : null;
    };
  }

  changeValidDate(isSearch: boolean) {
    if (!isSearch) {
      this.validForm();
    } else {
      if (
        this.form.controls.createdDateStartAt.value &&
        this.form.controls.createdDateEndAt.value
      ) {
        this.validForm();
      } else {
        this.form.controls.createdDateStartAt.clearValidators();
        this.form.controls.createdDateStartAt.updateValueAndValidity();
        this.form.controls.createdDateEndAt.clearValidators();
        this.form.controls.createdDateEndAt.updateValueAndValidity();
      }
    }
  }

  validForm(): void {
    if (
      this.form.controls.createdDateStartAt.value &&
      this.form.controls.createdDateEndAt.value
    ) {
      if (this.form.controls['createdDateEndAt'].value) {
        this.maxDate = this.form.controls['createdDateEndAt'].value;
      }
      const startDate = CommonUtils.setStartTime(
        CommonUtils.getStartOfDay(
          moment(this.form.controls.createdDateStartAt.value).valueOf()
        )
      );
      const endDate = CommonUtils.setStartTime(
        CommonUtils.getStartOfDay(
          moment(this.form.controls.createdDateEndAt.value).valueOf()
        )
      );

      const dayMax =
        (endDate - startDate) / CALCULATE_MILLISECONDS_IN_A_YEAR.DAY;
      if (dayMax > DATE_RANGE_STATISTICAL_CUSTOMER) {
        this.form.controls.createdDateStartAt.setValidators([
          Validators.required,
          this.dateValidator(dayMax),
          this.isValidMaxDate,
        ]);
        this.form.controls.createdDateStartAt.updateValueAndValidity();
      } else {
        this.form.controls.createdDateStartAt.clearValidators();
        this.form.controls.createdDateStartAt.setValidators([
          Validators.required,
          this.isValidMaxDate,
        ]);
        this.form.controls.createdDateStartAt.updateValueAndValidity();
      }
    }
  }

  /**
   * reset password
   *
   * @param customer: ICustomer
   */
  resetPassword(customer: ICustomer) {
    const modalData = {
      title: 'customerRegisterManagement.resetPassword',
      content: 'customerRegisterManagement.resetPasswordCustomerContent',
      interpolateParams: { fullname: `<b>${customer?.fullname || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { customerId: customer?.customerId };
        this.customerService.resetPassword(params).subscribe((res) => {
          this.toastService.success('common.action.changePasswordSuccess');
        });
      }
    });
  }

  /**
   * check lock and unlock and call api
   *
   * @param customer ICustomer
   */
  lockAndUnlock(customer: ICustomer): void {
    if (customer.status === ENTITY_STATUS_CONST.INACTIVE.code) {
      this.unLockCustomer(customer);
    } else {
      this.lockCustomer(customer);
    }
  }

  /**
   * Lock customer register
   *
   * @param customer ICustomer
   */
  private lockCustomer(customer: ICustomer) {
    const modalData = {
      title: 'customerRegisterManagement.lock',
      content: 'customerRegisterManagement.lockCustomerRegisterContent',
      interpolateParams: { fullname: `<b>${customer?.fullname || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { customerId: customer?.customerId };
        this.customerService.lock(params).subscribe((res) => {
          this.toastService.success('common.action.sendApprovalSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * UnLock customer register
   *
   * @param customer: ICustomer
   */
  private unLockCustomer(customer: ICustomer) {
    const modalData = {
      title: 'customerRegisterManagement.unlock',
      content: 'customerRegisterManagement.unlockCustomerRegisterContent',
      interpolateParams: { fullname: `<b>${customer?.fullname || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { customerId: customer?.customerId };
        this.customerService.unlock(params).subscribe((res) => {
          this.toastService.success('common.action.sendApprovalSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * Delete customer register
   *
   * @param customer ICustomer
   */
  // public deleteCustomer(customer: ICustomer) {
  //   const modalData = {
  //     title: 'customerRegisterManagement.delete',
  //     content: 'customerRegisterManagement.deleteCustomerRegisterContent',
  //     interpolateParams: { fullname: `<b>${customer?.fullname || ''}</b>` },
  //   };

  //   this.modalService.confirm(modalData).then((result) => {
  //     if (result === MODAL_ACTION.CONFIRM.code) {
  //       const params = { customerId: customer?.customerId };
  //       this.customerService.approvalDeleteCustomer(params).subscribe((res) => {
  //         this.toastService.success('common.action.deleteSuccess');
  //         this.onSearch();
  //       });
  //     }
  //   });
  // }

  /**
   * Cancel customer register
   *
   * @param customer ICustomer
   */
  public cancelCustomer(customer: ICustomer) {
    const modalData = {
      title: 'customerRegisterManagement.cancel',
      content: 'customerRegisterManagement.cancelCustomerRegisterContent',
      interpolateParams: { fullname: `<b>${customer?.fullname || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { customerId: customer?.customerId };
        this.customerService.cancelCustomer(params).subscribe((res) => {
          this.toastService.success('common.action.sendApprovalSuccess');
          this.onSearch();
        });
      }
    });
  }

  public unclosed(customer: ICustomer) {
    const modalData = {
      title: 'customerRegisterManagement.unclosed',
      content: 'customerRegisterManagement.unclosedCustomerRegisterContent',
      interpolateParams: { fullname: `<b>${customer?.fullname || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      const params = { customerId: customer?.customerId };
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.customerService.unclosed(params).subscribe((res) => {
          this.toastService.success('common.action.sendApprovalSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * go to page view detail
   *
   * @param customerId number
   */
  viewDetail(customerId: number) {
    sessionStorage.setItem(
      STORAGE_APP.CUSTOMER_REGISTER,
      JSON.stringify(this.form.value)
    );
    this.router.navigate([
      ROUTER_UTILS.customer.root,
      ROUTER_UTILS.customer.registration.root,
      customerId,
      ROUTER_ACTIONS.detail,
    ]);
  }

  /**
   * go to page create
   *
   */
  onCreate() {
    sessionStorage.setItem(
      STORAGE_APP.CUSTOMER_REGISTER,
      JSON.stringify(this.form.value)
    );
  }

  /**
   * reset form
   */
  onReset(): void {
    this.form.controls.cif.reset();
    this.form.controls.idCardNumber.reset();
    this.form.controls.username.reset();
    this.form.controls.fullname.reset();
    this.form.controls.status.reset();
    this.form.controls.activeStatus.reset();
    this.form.controls.phoneNumber.reset();
    this.form.controls.email.reset();
    this.form.controls.createdDateStartAt.reset();
    this.form.controls.createdDateEndAt.reset();
    this.form.controls.customerSectorId.reset();
    this.form.controls.accountType.reset();
    // this.form.controls.status.setValue('');
  }

  /**
   * export file
   */
  exportFile(): void {
    this.form.controls.createdDateStartAt.setValidators([Validators.required]);
    this.form.controls.createdDateEndAt.setValidators([Validators.required]);
    this.form.controls.createdDateStartAt.updateValueAndValidity();
    this.form.controls.createdDateEndAt.updateValueAndValidity();
    // this.isSearch = false;
    this.changeValidDate(false);
    // get value form control
    const bodySearch = this.form.getRawValue();
    if (this.form.invalid) {
      CommonUtils.markFormGroupTouched(this.form);
    } else {
      // get file name
      const fileName = this.translateService.instant(
        'template.customerRegistration'
      );
      const obFile = this.customerService.export({
        createdDateStartAt: bodySearch.createdDateStartAt,
        createdDateEndAt: bodySearch.createdDateEndAt,
        keyword: bodySearch.keyword,
        status: bodySearch.status,
        activeStatus: bodySearch.activeStatus,
        customerSectorId: bodySearch.customerSectorId,
        accountType: bodySearch.accountType,
      });
      if (this.data.length > 0) {
        this.downloadService.downloadFileWithObservableAndName(
          obFile,
          fileName,
          FILE_EXTENSION.XLSX
        );
      }
    }
  }

  /**
   *
   * @param departmentId
   * @returns
   */
  async openGetAllAccountNumberModal(customerId?: number) {
    let customer = null;

    if (customerId) {
      await this.customerService
        .getAccountNumberCustomer({ customerId })
        .toPromise()
        .then((result: any) => {
          customer = result.body;
        });
    }

    const modalRef = this.ngb.open(AccountNumberModalComponent, {
      backdrop: 'static',
      size: 'xl',
      centered: true,
      keyboard: false,
    });

    if (customerId) {
      modalRef.componentInstance.customer = customer;
    }
  }

  /**
   * index on list
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.form.value.pageIndex,
      this.form.value.pageSize
    );
  }

  onAllChecked(checked?: boolean): void {
    this.indeterminate = false;
    this.data.forEach((item: ICustomer) => {
      if ((item?.approvalStatus !== APPROVAL_STATUS_CONST.PENDING.value && item?.status === CUSTOMER_STATUS_CONST.ACTIVE.value) ||
        (item?.approvalStatus !== APPROVAL_STATUS_CONST.PENDING.value && item?.status === CUSTOMER_STATUS_CONST.INACTIVE.value)) {
        this.updateCheckedSet(item.cif || '', checked);
      }
    });
    this.refreshCheckedStatus();
  }

  onItemChecked(cif?: string, checked?: boolean): void {
    if (cif) {
      this.updateCheckedSet(cif, checked);
      this.refreshCheckedStatus();
    }
  }

  updateCheckedSet(cif: string, checked?: boolean): void {
    if (checked) {
      this.setOfCheckedId.add(cif);
    } else {
      this.setOfCheckedId.delete(cif);
    }
  }

  refreshCheckedStatus(): void {
    const listOfEnabledData = this.data.filter(
      (item) =>
      ((item?.approvalStatus !== APPROVAL_STATUS_CONST.PENDING.value &&
        item?.status === CUSTOMER_STATUS_CONST.ACTIVE.value) ||
        (item?.approvalStatus !== APPROVAL_STATUS_CONST.PENDING.value &&
          item?.status === CUSTOMER_STATUS_CONST.INACTIVE.value))
    );

    this.checked = listOfEnabledData?.every(({ cif }) =>
      this.setOfCheckedId.has(cif || 0)
    );

    this.indeterminate =
      listOfEnabledData.some(({ cif }) =>
        this.setOfCheckedId.has(cif || 0)
      ) && !this.checked;
  }

  syncCustomerInfo() {
    const customerCifs: string[] = [];
    const body = { ...this.form.value };

    if (CommonUtils.objectIsEmpty(body)) {
      this.toastService.warning('common.searchEmpty');
      return;
    } else {
      if (this.form.valid) {
        if (this.setOfCheckedId.size !== 0) {
          this.setOfCheckedId.forEach((item) => {
            customerCifs.push(item + '');
          });
          const data = {
            cifs: customerCifs,
          };
          this.customerService.syncCustomerInfo(data).subscribe((res: any) => {
            if (res.body) {
              const customerSuccess = res.body.customerSuccess;
              const customerFails = res.body.customerFails;

              if (customerSuccess.length > 0) {
                const cifs = customerSuccess.map((item: ICustomer) => item.cif);
                let fullname;
                for (const value of this.setOfCheckedId) {
                  if (cifs.includes(value + '')) {
                    this.setOfCheckedId.delete(value);
                  }
                }

                if (customerSuccess.length > 5) {
                  const fullnameTop5 = customerSuccess.slice(0, 5);
                  fullname = fullnameTop5.map((customer: ICustomer) => customer.fullname).join(', ');
                  fullname += '...';
                } else if (customerSuccess.length >= 1 && customerSuccess.length <= 5) {
                  fullname = customerSuccess.map((customer: ICustomer) => customer.fullname).join(', ');
                }

                this.toastService.successCustom('common.action.syncSuccess', { name: fullname });
              }

              if (customerFails.length > 0) {
                let fullNames;
                fullNames = customerFails.map((item: ICustomer) => item.fullname);
                if (customerFails.length > 5) {
                  fullNames = customerFails.map((customer: ICustomer) => customer?.fullname).splice(0, 5).join(', ');
                  fullNames += '...';
                } else if (customerFails.length > 1 && customerFails.length <= 5) {
                  fullNames = customerFails.map((customer: ICustomer) => customer.fullname).join(', ');
                }

                this.toastService.errorCustom('common.action.syncFails', { name: fullNames });
              }

              if (this.setOfCheckedId.size === 0) {
                this.setOfCheckedId = new Set();
                this.refreshCheckedStatus();
              }
            }

          });
          this.onSearch();
        } else {
          this.toastService.warning('model.customer.emptyListUpdateSector');
        }
      }
    }
  }

  updateSectors() {
    if (this.setOfCheckedId.size === 0) {
      this.toastService.warning('model.customer.emptyListUpdateSector');
    } else {
      const modalData = {
        title: 'common.action.updateSector',
        content: 'event.usernameOrPhoneNumber',
        interpolateParams: { setOfCheckedId: this.setOfCheckedId },
      };

      this.modalService.updateSectorModal(modalData).then((result) => {
        if (result === MODAL_ACTION.CONFIRM.code) {
          this.setOfCheckedId = new Set();
          this.refreshCheckedStatus();
          this.onSearch();
        }
      });
    }
  }

  validateUnclosed(customer: ICustomer) {
    if (customer.revertPremiumAccNumber === true &&
      customer.status === CUSTOMER_STATUS_CONST.CANCEL.value && customer.approvalStatus !==
      APPROVAL_STATUS_CONST.PENDING.value) {
      return true;
    } else {
      return false;
    }
  }
}
