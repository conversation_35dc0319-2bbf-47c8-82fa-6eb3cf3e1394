.fa-trash,
.fa-lock {
  font-size: 14px;
}

.table-customer {
  min-width: 2200px;
}

.freezer {
  position: sticky;
  right: 0;
}
.freezer-td {
  position: sticky;
  right: 0;
  background-color: white;
}

// input[type="date"]::-webkit-datetime-edit,
// input[type="date"]::-webkit-inner-spin-button,
// input[type="date"]::-webkit-clear-button {
//   color: #fff;
//   position: relative;
// }

// input[type="date"]::-webkit-datetime-edit-year-field {
//   position: absolute !important;
//   color: #000;
//   width: 60px;
//   text-align: center;
//   left: 60px;
// }

// input[type="date"]::-webkit-datetime-edit-month-field {
//   position: absolute !important;
//   width: 35px;
//   text-align: center;
//   border-right: 1px solid;
//   color: #000;
//   left: 30px;
// }

// input[type="date"]::-webkit-datetime-edit-day-field {
//   position: absolute !important;
//   color: #000;
//   width: 30px;
//   border-right: 1px solid;
//   text-align: center;
//   left: 0;
// }

.href-color {
  color: #0d6efd;
  cursor: pointer;
}

.hide {
  display: none;
}
