import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ROUTER_UTILS } from '@shared/utils/router.utils';

@Component({
  selector: 'app-saving-info',
  templateUrl: './saving-info.component.html',
  styleUrls: ['./saving-info.component.scss'],
})
export class SavingInfoComponent implements OnInit {
  isDetailSavingTrans = false;
  constructor(private router: Router, private location: Location) {
    this.location.subscribe((event) => {
      if (event.pop) {
        this.router.navigate([ROUTER_UTILS.saving.root]);
      }
    });
  }

  back(): void {
    this.router.navigate([ROUTER_UTILS.saving.root]);
    this.isDetailSavingTrans = false;
  }

  ngOnInit(): void {}

  selectedIndexChange(event: any) {
    if (event === 0) {
      this.isDetailSavingTrans = false;
    } else {
      this.isDetailSavingTrans = true;
    }
  }
}
