import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ENTITY_STATUS } from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IGroupPrivileges } from '@shared/models/group-privileges.model';
import { IPrivilege } from '@shared/models/privilege.model';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { RoleService } from '@shared/services/role.service';
import { ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-create-role',
  templateUrl: './create-role.component.html',
  styleUrls: ['./create-role.component.scss'],
})
export class CreateRoleComponent implements OnInit {
  isShowEdit = false;
  groupPrivileges: IGroupPrivileges[] = [];
  checked = true;
  createRole!: FormGroup;

  VALIDATORS = VALIDATORS;

  ENTITY_STATUS = ENTITY_STATUS;

  role: any;

  roleId = 0;

  SYSTEM_RULES = SYSTEM_RULES;

  constructor(
    public fb: FormBuilder,
    public roleService: RoleService,
    protected router: Router,
    protected activatedRoute: ActivatedRoute,
    protected toast: ToastrCustomService
  ) {}

  ngOnInit(): void {
    this.activatedRoute.paramMap.subscribe((params) => {
      this.roleId = Number(params.get('roleId'));
    });
    this.getListPrivileges();
    this.initForm();
  }

  initForm(): void {
    this.createRole = this.fb.group({
      name: [
        null,
        [
          Validators.required,
          Validators.maxLength(VALIDATORS.LENGTH.NAME_ROLE_MAX_LENGTH),
        ],
      ],
      description: [
        null,
        [Validators.maxLength(VALIDATORS.LENGTH.DESCRIPTION_ROLE_MAX_LENGTH)],
      ],
      createdBy: [null],
      createdDate: [null],
      lastModifiedBy: [null],
      lastModifiedDate: [null],
    });

    if (this.roleId) {
      this.isShowEdit = true;
    }
  }

  patchValueToForm() {
    this.roleService.detail({ roleId: this.roleId }).subscribe((res) => {
      this.role = res.body;
      this.createRole.patchValue({
        name: this.role?.name,
        description: this.role?.description,
        createdBy: this.role?.createdBy,
        lastModifiedBy: this.role?.lastModifiedBy,
        createdDate: this.role?.createDateTime
          ? CommonUtils.reverseDate(this.role?.createDateTime)
          : null,
        lastModifiedDate: this.role?.lastModifiedDatetime
          ? CommonUtils.reverseDate(this.role?.lastModifiedDatetime)
          : null,
      });

      // map privilege
      for (const groupPrivilege of this.groupPrivileges) {
        if (groupPrivilege?.privileges) {
          groupPrivilege.checked = true;
          const privileges = groupPrivilege?.privileges;
          for (const privilege of privileges) {
            if (this.role?.privilegeIds.includes(privilege.privilegeId)) {
              privilege.checked = true;
            }

            // check case don't have full privileges => checked = false
            if (!privilege.checked) {
              groupPrivilege.checked = false;
            }
          }
        }
      }
    });
  }

  getListPrivileges(): void {
    this.roleService.listPrivilege().subscribe((res: any): void => {
      this.groupPrivileges = res.body;
      if (this.roleId) {
        this.patchValueToForm();
      }
    });
  }

  onSave(): void {
    if (this.createRole.invalid) {
      CommonUtils.markFormGroupTouched(this.createRole);
      return;
    }

    if (this.createRole.valid) {
      const role = { ...this.createRole.value };

      // map privileges
      const privilegeList = [];
      for (const groupPrivilege of this.groupPrivileges) {
        if (groupPrivilege?.privileges) {
          const privileges = groupPrivilege?.privileges;
          for (const privilege of privileges) {
            if (privilege.checked) {
              privilegeList.push(privilege.privilegeId as number);
            }
          }
        }
      }

      role.level = 1;
      role.privilegeIds = privilegeList;
      if (this.roleId) {
        role.roleId = this.roleId;
        this.roleService.update(role).subscribe((res) => {
          this.router.navigate([ROUTER_UTILS.sysManage.role.root]);
          this.toast.success('common.success');
        });
      } else {
        this.roleService.create(role).subscribe((res): void => {
          if (res.ok) {
            this.router.navigate([ROUTER_UTILS.sysManage.role.root]);
            this.toast.success('common.success');
          }
        });
      }
    }
  }

  selectAllPrivileges(event: any, groupPrivilege: IGroupPrivileges) {
    event?.stopPropagation();
    const privileges = groupPrivilege?.privileges;
    if (groupPrivilege.checked) {
      privileges?.map((item) => (item.checked = false));
    } else {
      privileges?.map((item) => (item.checked = true));
    }
  }

  selectOnePrivileges(
    event: any,
    privilege: IPrivilege,
    groupPrivilege: IGroupPrivileges
  ) {
    event?.stopPropagation();
    const checkAll = true;
    const privilegesNotChecks = [];
    if (groupPrivilege?.privileges) {
      const privileges = groupPrivilege?.privileges;
      for (const privilegeEle of privileges) {
        if (!privilegeEle.checked) {
          privilegesNotChecks.push(privilegeEle);
        }
      }
    }

    if (privilege.checked) {
      groupPrivilege.checked = false;
    } else if (
      privilegesNotChecks?.length === 1 &&
      privilege.privilegeId === privilegesNotChecks[0].privilegeId
    ) {
      groupPrivilege.checked = true;
    }
  }

  backToDetail(): void {
    // this.router.navigate([ROUTER_UTILS.sysManage.role.root, this.roleId, ROUTER_ACTIONS.detail]);
    history.back();
  }
}
