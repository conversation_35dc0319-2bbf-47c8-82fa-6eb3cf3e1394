<div class="modal-content">
  <div class="modal-header" [ngClass]="isHiddenBtnClose ? 'border-0' : ''">
    <h5 class="modal-title" *ngIf="isCreate">
      {{ "department.create" | translate }}
    </h5>
    <h5 class="modal-title" *ngIf="isUpdate">
      {{ "department.update" | translate }}
    </h5>
    <h5 class="modal-title" *ngIf="isDetail">
      {{ "department.detail" | translate }}
    </h5>
    <button
      type="button"
      class="close"
      data-dismiss="modal"
      aria-label="Close"
      (click)="activeModal.close(MODAL_ACTION.CLOSE.code)"
    >
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="formDepartment">
      <div class="row">
        <div class="form-group col-md-6">
          <label
            >{{ "model.department.code" | translate }}
            <span class="text-danger" *ngIf="!isDetail">*</span></label
          >
          <input
            trim
            placeholder="{{ 'model.department.code' | placeholder }}"
            type="text"
            formControlName="departmentCode"
            class="w-100"
            class="form-control"
            [maxLength]="VALIDATORS.LENGTH.DEPARTMENT_CODE_MAX_LENGTH"
          />
          <small
            class="form-text text-danger noti-small"
            *ngIf="
              formDepartment.get('departmentCode')?.errors?.required &&
              formDepartment.get('departmentCode')?.touched
            "
          >
            {{ "department.error.required.departmentCode" | translate }}
          </small>
          <small
            class="form-text text-danger noti-small"
            *ngIf="
              formDepartment.get('departmentCode')?.errors?.minlength &&
              formDepartment.get('departmentCode')?.touched
            "
          >
            {{
              "department.error.minLength.departmentCode"
                | translate
                  : {
                      param: VALIDATORS.LENGTH.DEPARTMENT_CODE_MIN_LENGTH
                    }
            }}
          </small>
          <small
            class="form-text text-danger noti-small"
            *ngIf="
              formDepartment.get('departmentCode')?.errors?.maxlength &&
              formDepartment.get('departmentCode')?.touched
            "
          >
            {{
              "department.error.maxLength.departmentCode"
                | translate
                  : {
                      param: VALIDATORS.LENGTH.DEPARTMENT_CODE_MAX_LENGTH
                    }
            }}
          </small>
        </div>
        <div class="form-group col-md-6">
          <label
            >{{ "model.department.name" | translate
            }}<span class="text-danger" *ngIf="!isDetail">*</span></label
          >
          <input
            trim
            placeholder="{{ 'model.department.name' | placeholder }}"
            type="text"
            formControlName="departmentName"
            class="w-100"
            class="form-control"
            [maxLength]="VALIDATORS.LENGTH.DEPARTMENT_NAME_MAX_LENGTH"
          />
          <small
            class="form-text text-danger noti-small"
            *ngIf="
              formDepartment.get('departmentName')?.errors?.required &&
              formDepartment.get('departmentName')?.touched
            "
          >
            {{ "department.error.required.departmentName" | translate }}
          </small>
          <small
            class="form-text text-danger noti-small"
            *ngIf="
              formDepartment.get('departmentName')?.errors?.pattern &&
              formDepartment.get('departmentName')?.touched
            "
          >
            {{ "department.error.pattern.departmentName" | translate }}
          </small>
          <small
            class="form-text text-danger noti-small"
            *ngIf="
              formDepartment.get('departmentName')?.errors?.minlength &&
              formDepartment.get('departmentName')?.touched
            "
          >
            {{
              "department.error.minLength.departmentName"
                | translate
                  : {
                      param: VALIDATORS.LENGTH.DEPARTMENT_NAME_MIN_LENGTH
                    }
            }}
          </small>
          <small
            class="form-text text-danger noti-small"
            *ngIf="
              formDepartment.get('departmentName')?.errors?.maxlength &&
              formDepartment.get('departmentName')?.touched
            "
          >
            {{
              "department.error.maxLength.departmentName"
                | translate
                  : {
                      param: VALIDATORS.LENGTH.DEPARTMENT_NAME_MAX_LENGTH
                    }
            }}
          </small>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-md-6">
          <label
            >{{ "model.department.shortName" | translate }}
            <span class="text-danger" *ngIf="!isDetail">*</span>
          </label>
          <input
            trim
            placeholder="{{ 'model.department.shortName' | placeholder }}"
            type="text"
            formControlName="shortName"
            class="w-100"
            class="form-control"
            [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
          />
          <small
            class="form-text text-danger noti-small"
            *ngIf="
              formDepartment.get('shortName')?.errors?.required &&
              formDepartment.get('shortName')?.touched
            "
          >
            {{ "department.error.required.departmentShortName" | translate }}
          </small>
          <small
            class="form-text text-danger noti-small"
            *ngIf="
              formDepartment.get('shortName')?.errors?.maxlength &&
              formDepartment.get('shortName')?.touched
            "
          >
            {{
              "department.error.maxLength.shortName"
                | translate
                  : {
                      param: VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH
                    }
            }}
          </small>
          <small
            class="form-text text-danger noti-small"
            *ngIf="
              formDepartment.get('shortName')?.errors?.pattern &&
              formDepartment.get('shortName')?.touched
            "
          >
            {{ "department.error.pattern.departmentShortName" | translate }}
          </small>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-md-12">
          <label>{{ "model.department.description" | translate }}</label>
          <textarea
            trim
            placeholder="{{ 'model.department.description' | placeholder }}"
            type="text"
            formControlName="description"
            class="w-100"
            class="form-control"
            [maxLength]="VALIDATORS.LENGTH.DEPARTMENT_DESCRIPTION_MAX_LENGTH"
          >
          </textarea>
          <small
            class="form-text text-danger noti-small"
            *ngIf="
              formDepartment.get('description')?.errors?.maxlength &&
              formDepartment.get('description')?.touched
            "
          >
            {{
              "department.error.maxLength.description"
                | translate
                  : {
                      param: VALIDATORS.LENGTH.DEPARTMENT_DESCRIPTION_MAX_LENGTH
                    }
            }}
          </small>
        </div>
      </div>
      <div class="row" *ngIf="isDetail">
        <div class="col-md-6">
          <div class="form-group">
            <label>{{ "model.department.createdDate" | translate }} </label>
            <input
              trim
              [value]="department.createdDate"
              type="text"
              class="w-100"
              class="form-control"
              disabled
            />
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label>{{ "model.department.createdBy" | translate }} </label>
            <input
              trim
              formControlName="userCreated"
              type="text"
              class="w-100"
              class="form-control"
            />
          </div>
        </div>
      </div>
      <div class="row" *ngIf="isDetail">
        <div class="col-md-6">
          <div class="form-group">
            <label
              >{{ "model.department.lastModifiedDate" | translate }}
            </label>
            <input
              trim
              [value]="department.lastModifiedDate"
              type="text"
              class="w-100"
              class="form-control"
              disabled
            />
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label>{{ "model.department.lastModifiedBy" | translate }} </label>
            <input
              trim
              formControlName="userLastModified"
              type="text"
              class="w-100"
              class="form-control"
            />
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="text-center pb-4">
    <button
      type="button"
      class="btn mb-btn-outline-color mr-3"
      (click)="activeModal.close(MODAL_ACTION.CANCEL.code)"
    >
      {{ "common.action.close" | translate }}
    </button>
    <ng-container
      *hasPrivileges="
        isUpdate
          ? SYSTEM_RULES.DEPARTMENT_WRITE
          : SYSTEM_RULES.DEPARTMENT_CREATE
      "
    >
      <button
        type="button"
        *ngIf="!isDetail"
        class="btn mb-btn-color"
        (click)="onSave()"
      >
        {{
          (isUpdate ? "common.action.update" : "common.action.create")
            | translate
        }}
      </button>
    </ng-container>
  </div>
</div>
