<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="backToList()" />
      <h5>{{ "merchant.titleInforMerchant" | translate }}</h5>
    </div>
    <!-- <form [formGroup]="form" *ngIf="customer?.customerId"></form> -->
    <form
      [formGroup]="formMerchant"
      *ngIf="isUpdate ? merchant?.merchantId : !isUpdate"
    >
      <div class="col-md-12">
        <div class="row">
          <h2 class="title-create">
            {{ "merchant.titleInforMerchant" | translate }}
          </h2>
        </div>
        <div class="row border-create">
          <h3>{{ "sidebar.merchant.infor" | translate }}</h3>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label>{{ "model.merchant.merchantCode" | translate }}</label>
                  <input
                    trim
                    formControlName="merchantCode"
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{
                      'model.merchant.merchantCode' | placeholder
                    }}"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('merchantCode')?.errors?.required &&
                      formMerchant.get('merchantCode')?.touched
                    "
                  >
                    {{ "error.merchant.required.merchantCode" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('merchantCode')?.errors?.minlength &&
                      formMerchant.get('merchantCode')?.touched
                    "
                  >
                    {{
                      "error.merchant.minLength.merchantCode"
                        | translate
                          : {
                              param: VALIDATORS.LENGTH.CODE_MIN_LENGTH
                            }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('merchantCode')?.errors?.maxlength &&
                      formMerchant.get('merchantCode')?.touched
                    "
                  >
                    {{
                      "error.merchant.maxLength.merchantCode"
                        | translate
                          : {
                              param: VALIDATORS.LENGTH.MERCHANT_CODE_MAX_LENGTH
                            }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('merchantCode')?.errors?.pattern &&
                      formMerchant.get('merchantCode')?.touched
                    "
                  >
                    {{ "error.merchant.pattern.merchantCode" | translate }}
                  </small>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label
                    >{{ "model.merchant.merchantName" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    trim
                    formControlName="merchantName"
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{
                      'model.merchant.merchantName' | placeholder
                    }}"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('merchantName')?.errors?.required &&
                      formMerchant.get('merchantName')?.touched
                    "
                  >
                    {{ "error.merchant.required.merchantName" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('merchantName')?.errors?.maxlength &&
                      formMerchant.get('merchantName')?.touched
                    "
                  >
                    {{
                      "error.merchant.maxLength.merchantName"
                        | translate
                          : {
                              param: VALIDATORS.LENGTH.MERCHANT_NAME_MAX_LENGTH
                            }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('merchantName')?.errors?.pattern &&
                      formMerchant.get('merchantName')?.touched
                    "
                  >
                    {{ "error.merchant.pattern.merchantName" | translate }}
                  </small>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label
                    >{{
                      "model.merchant.masterMerchant.merchantName" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <ng-select
                    appearance="outline"
                    [searchable]="false"
                    [clearable]="false"
                    formControlName="parentId"
                    placeholder="{{
                      'model.merchant.masterMerchant.merchantName'
                        | placeholder : 'select'
                    }}"
                    (change)="onChangeGetMerchantAccountNumber($event)"
                  >
                    <ng-option
                      *ngFor="let item of masterMerchant; let i = index"
                      [value]="item.merchantId"
                    >
                      {{ item.merchantName }}
                    </ng-option>
                  </ng-select>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('parentId')?.errors?.required &&
                      formMerchant.get('parentId')?.touched
                    "
                  >
                    {{
                      "error.merchant.required.masterMerchantName" | translate
                    }}
                  </small>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label>{{
                    "model.merchant.merchantAccountNumber" | translate
                  }}</label>
                  <input
                    trim
                    formControlName="merchantAccountNumber"
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{
                      'model.merchant.merchantAccountNumber' | placeholder
                    }}"
                  />
                </div>
              </div>
              <!-- <div class="col-md-3">
                <div class="form-group">
                  <label>{{
                    "currency.root" | translate
                    }}</label>
                  <input trim formControlName="currency" type="text" class="w-100" class="form-control" placeholder="{{
                      'currency.root' | placeholder
                    }}" />
                </div>
              </div> -->
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-12">
                <div class="form-group">
                  <label>{{ "model.merchant.description" | translate }}</label>
                  <textarea
                    formControlName="description"
                    type="text"
                    class="w-100"
                    class="form-control"
                    rows="4"
                    cols="50"
                    placeholder="{{
                      'model.merchant.description' | placeholder
                    }}"
                  >
                  </textarea>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('description')?.errors?.maxlength &&
                      formMerchant.get('description')?.touched
                    "
                  >
                    {{
                      "error.merchant.maxLength.description"
                        | translate
                          : {
                              param: VALIDATORS.LENGTH.MEDIUM_TEXT_MAX_LENGTH
                            }
                    }}
                  </small>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12" *ngIf="qrCode">
            <div class="row">
              <div class="col-md-12">
                <div class="form-group" #qrElement>
                  <div class="qrcode-left">
                    <qrcode
                      #qrCodeData
                      [qrdata]="qrCode"
                      [width]="256"
                      [errorCorrectionLevel]="'H'"
                      (imageSrc)="getImage($event)"
                      (qrCodeURL)="onChangeURL($event)"
                      id="printQrcode"
                      class="printQrcodeClass"
                    ></qrcode>
                    <div class="logo-qr-wrap">
                      <img
                        src="assets/dist/img/logoQR.png"
                        height="42"
                        class="logo-qr"
                      />
                    </div>
                  </div>

                  <button
                    class="bi bi-printer btn-action bi-bi-printer btn btn-white col-lg-2 mr-2"
                    nz-button
                    nzType="default"
                    (click)="this.generatePdf(BUTTON_ACTION_CONST.PRINT)"
                    *hasPrivileges="SYSTEM_RULES.MERCHANT_PRINT_QR_CODE"
                  >
                    <span>
                      {{ "common.action.print" | translate }}</span
                    ></button
                  ><button
                    class="bi bi-download btn-action btn btn-white col-lg-2"
                    nz-button
                    nzType="default"
                    (click)="this.generatePdf(BUTTON_ACTION_CONST.DOWNLOAD)"
                    *hasPrivileges="SYSTEM_RULES.MERCHANT_PRINT_QR_CODE"
                  >
                    <span> {{ "common.action.download" | translate }}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row border-create mt-4">
          <h3>
            {{ "sidebar.merchant.transactionHistory.root" | translate }}
          </h3>
          <div
            class="col-md-12"
            *hasPrivileges="SYSTEM_RULES.MERCHANT_QUERY_TRANSACTION_HISTORY"
          >
            <div class="col-12">
              <form
                [formGroup]="formMerchantHistorySearch"
                (submit)="onSearchMerchantHistorySubmit()"
              >
                <div class="row">
                  <div class="col-md-3">
                    <div class="form-group">
                      <label>{{
                        "common.action.searchKeyword" | translate
                      }}</label>
                      <input
                        trim
                        type="text"
                        formControlName="keywordMerchant"
                        placeholder="{{
                          'common.action.searchKeyword' | placeholder
                        }}"
                        class="w-100"
                        class="form-control"
                      />
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label
                        >{{
                          "model.merchant.transactionHistory.fromDate"
                            | translate
                        }}<span class="text-danger">*</span></label
                      >
                      <mat-form-field appearance="fill" class="date-picker">
                        <input
                          matInput
                          [matDatepicker]="transactionDateStart"
                          formControlName="transactionDateStart"
                          placeholder="DD/MM/YYYY"
                          [max]="maxDate"
                          (change)="changeValidDate()"
                          (dateInput)="changeValidDate()"
                          dateTransform
                        />
                        <mat-datepicker-toggle
                          matSuffix
                          [for]="transactionDateStart"
                        ></mat-datepicker-toggle>
                        <mat-datepicker #transactionDateStart></mat-datepicker>
                      </mat-form-field>
                      <small
                        class="form-text text-danger noti-small"
                        *ngIf="
                          formMerchantHistorySearch.get('transactionDateStart')
                            ?.errors?.required &&
                          formMerchantHistorySearch.get('transactionDateStart')
                            ?.touched
                        "
                      >
                        {{ "error.required.fromDate" | translate }}
                      </small>
                      <small
                        class="form-text text-danger noti-small"
                        *ngIf="
                          formMerchantHistorySearch.get('transactionDateStart')
                            ?.errors?.invalidDate &&
                          formMerchantHistorySearch.get('transactionDateStart')
                            ?.touched
                        "
                      >
                        {{ "error.required.inValidDate" | translate }}
                      </small>
                      <small
                        class="form-text text-danger noti-small"
                        *ngIf="
                          formMerchantHistorySearch.get('transactionDateStart')
                            ?.errors?.invalidMaxDate &&
                          formMerchantHistorySearch.get('transactionDateStart')
                            ?.touched
                        "
                      >
                        {{
                          "error.maxDateCurrent"
                            | translate
                              : {
                                  param: "common.action.fromDate" | translate
                                }
                        }}
                      </small>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label
                        >{{ "common.action.toDate" | translate
                        }}<span class="text-danger">*</span></label
                      >
                      <!-- <input
                        trim
                        type="date"
                        formControlName="transactionDateEnd"
                        placeholder="{{ 'common.action.toDate' | placeholder }}"
                        class="w-100"
                        class="form-control"
                        max="{{ maxDate | date: 'yyyy-MM-dd' }}"
                      /> -->
                      <mat-form-field appearance="fill" class="date-picker">
                        <input
                          matInput
                          [matDatepicker]="transactionDateEnd"
                          formControlName="transactionDateEnd"
                          placeholder="DD/MM/YYYY"
                          [min]="
                            formMerchantHistorySearch.controls[
                              'transactionDateStart'
                            ].value | date : 'yyyy-MM-dd'
                          "
                          [max]="maxToDate"
                          (change)="changeValidDate()"
                          (dateInput)="changeValidDate()"
                          dateTransform
                        />
                        <mat-datepicker-toggle
                          matSuffix
                          [for]="transactionDateEnd"
                        ></mat-datepicker-toggle>
                        <mat-datepicker #transactionDateEnd></mat-datepicker>
                      </mat-form-field>
                      <small
                        class="form-text text-danger noti-small"
                        *ngIf="
                          formMerchantHistorySearch.get('transactionDateEnd')
                            ?.errors?.required &&
                          formMerchantHistorySearch.get('transactionDateEnd')
                            ?.touched
                        "
                      >
                        {{ "error.required.toDate" | translate }}
                      </small>
                      <small
                        class="form-text text-danger noti-small"
                        *ngIf="
                          formMerchantHistorySearch.get('transactionDateEnd')
                            ?.errors?.invalidMaxDate &&
                          formMerchantHistorySearch.get('transactionDateEnd')
                            ?.touched
                        "
                      >
                        {{
                          "error.maxDateCurrent"
                            | translate
                              : {
                                  param: "common.action.toDate" | translate
                                }
                        }}
                      </small>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <!-- <button class="btn btn-red mr-2" type="button" (click)="onReset()">
                      {{ "common.action.reset" | translate }}
                    </button> -->
                    <button
                      class="btn btn-search btn-search-child"
                      type="submit"
                      *hasPrivileges="
                        SYSTEM_RULES.MERCHANT_QUERY_TRANSACTION_HISTORY
                      "
                    >
                      {{ "common.action.search" | translate }}
                    </button>
                  </div>
                </div>
                <div class="border-bottom-search mt-3"></div>
                <div class="d-block text-right mb-3 mt-2">
                  <button
                    class="btn btn-red mr-2"
                    type="button"
                    (click)="exportFile()"
                    [disabled]="merchantHistory?.length === 0"
                    *hasPrivileges="
                      SYSTEM_RULES.MERCHANT_EXPORT_TRANSACTION_HISTORY
                    "
                  >
                    {{ "common.action.export" | translate }}
                  </button>
                </div>
              </form>
            </div>
            <div class="col-12">
              <div class="table-responsive">
                <table class="table">
                  <thead>
                    <tr>
                      <th scope="col">{{ "common.no" | translate }}</th>
                      <th scope="col">
                        {{
                          "model.merchant.transactionHistory.code" | translate
                        }}
                      </th>
                      <th scope="col">
                        {{ "model.merchant.transferType" | translate }}
                      </th>
                      <th scope="col">
                        {{
                          "model.merchant.transactionHistory.cif" | translate
                        }}
                      </th>
                      <th scope="col">
                        {{
                          "model.merchant.transactionHistory.name" | translate
                        }}
                      </th>
                      <th scope="col">
                        {{ "model.merchant.merchantAccountNumber" | translate }}
                      </th>
                      <th scope="col">
                        {{ "model.merchant.monney" | translate }}
                      </th>
                      <th scope="col">
                        {{ "model.merchant.fee" | translate }}
                      </th>
                      <th scope="col">
                        {{
                          "model.merchant.transactionHistory.totalAmount"
                            | translate
                        }}
                      </th>
                      <th scope="col">
                        {{
                          "model.merchant.transactionHistory.date" | translate
                        }}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let dataItem of merchantHistory; let i = index">
                      <td class="text-center">{{ fillIndexItem(i) }}</td>
                      <td>{{ dataItem.transactionId }}</td>
                      <td>{{ dataItem.transferTypeStr }}</td>
                      <td>{{ dataItem.customerCif }}</td>
                      <td title="{{ dataItem.customerFullName }}">
                        {{ dataItem.customerFullName | limitWord }}
                      </td>
                      <td>{{ dataItem.customerAccountNumber }}</td>
                      <td>{{ dataItem.transactionAmount | currencyLak }}</td>
                      <td>{{ dataItem.transactionFee | currencyLak }}</td>
                      <td>
                        {{
                          totalAmount(
                            dataItem.transactionAmount,
                            dataItem.transactionFee
                          ) | currencyLak
                        }}
                      </td>
                      <td>{{ dataItem.transactionDateStr }}</td>
                    </tr>
                  </tbody>
                </table>
                <div
                  class="row d-block text-center m-0"
                  *ngIf="merchantHistory?.length === 0"
                >
                  <img
                    src="/assets/dist/img/icon/empty.svg"
                    height="120"
                    alt="no_search_result"
                  />
                  <p class="text-center mb-5">
                    {{ "common.no_search_result" | translate }}
                  </p>
                </div>
                <div *ngIf="merchantHistory.length">
                  <mat-paginator
                    [length]="formMerchantHistorySearch.value.length"
                    [pageSize]="formMerchantHistorySearch.value.pageSize"
                    [pageIndex]="formMerchantHistorySearch.value.pageIndex"
                    [pageSizeOptions]="pageSizeOptions"
                    (page)="onChangePage($event)"
                    aria-label="Select page"
                  >
                  </mat-paginator>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="d-block text-center mb-5 mt-4">
          <button
            class="btn btn-red mr-2"
            data-toggle="modal"
            (click)="backToList()"
          >
            {{ "common.action.back" | translate }}
          </button>
        </div>
      </div>
    </form>
  </div>
</section>
