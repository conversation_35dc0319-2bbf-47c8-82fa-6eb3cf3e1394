export interface ILoanOnline {
  loanOnlineId?: number;
  loanProductId?: number;
  loanAmount?: string;
  loanTime?: number;
  collateral?: string;
  fullName?: string;
  dateOfBirth?: string;
  gender?: number;
  maritalStatus?: string;
  email?: string;
  phoneNumber?: string;
  address?: string;
  job?: string;
  position?: string;
  workplace?: string;
  income?: string;
  createdDate?: string;
  status?: number;
  loanProductName?: string;
}

export class LoanOnline implements ILoanOnline {
  constructor(
    public loanOnlineId?: number,
    public loanProductId?: number,
    public loanAmount?: string,
    public loanTime?: number,
    public collateral?: string,
    public fullName?: string,
    public dateOfBirth?: string,
    public gender?: number,
    public maritalStatus?: string,
    public email?: string,
    public phoneNumber?: string,
    public address?: string,
    public job?: string,
    public position?: string,
    public workplace?: string,
    public income?: string,
    public createdDate?: string,
    public status?: number,
    public loanProductName?: string
  ) {
    this.loanOnlineId = loanOnlineId;
    this.loanProductId = loanProductId;
    this.loanAmount = loanAmount;
    this.loanTime = loanTime;
    this.collateral = collateral;
    this.fullName = fullName;
    this.dateOfBirth = dateOfBirth;
    this.gender = gender;
    this.maritalStatus = maritalStatus;
    this.email = email;
    this.phoneNumber = phoneNumber;
    this.address = address;
    this.job = job;
    this.position = position;
    this.workplace = workplace;
    this.income = income;
    this.createdDate = createdDate;
    this.status = status;
    this.loanProductName = loanProductName;
  }
}
