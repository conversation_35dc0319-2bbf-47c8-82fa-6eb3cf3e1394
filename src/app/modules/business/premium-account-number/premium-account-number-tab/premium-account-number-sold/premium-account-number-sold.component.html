<div class="">
  <div class="col-md-12">
    <form [formGroup]="formSearch" (submit)="onSearchSubmit()">
      <div class="row">
        <div class="col-md-2 col-lg-2">
          <div class="form-group">
            <label>{{ "common.action.searchKeyword" | translate }}</label>
            <input
              trim
              type="text"
              placeholder="{{ 'common.action.searchKeyword' | placeholder }}"
              formControlName="keyword"
              class="w-100"
              class="form-control"
              [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
            />
          </div>
        </div>
        <div class="col-md-2 col-lg-2">
          <div class="form-group">
            <label>{{ "model.referral.referralCode" | translate }}</label>
            <ng-select
              placeholder="{{
                'model.referral.referralCode' | placeholder : 'select'
              }}"
              [searchFn]="customReferralSearchFn"
              [items]="referrals"
              bindLabel="rmCode"
              bindValue="referralId"
              [multiple]="true"
              appearance="outline"
              [searchable]="true"
              [clearable]="true"
              placeholder="{{ 'model.referral.referralCode' | translate }}"
              formControlName="referralIds"
            >
              <ng-option
                [value]="item.referralId"
                *ngFor="let item of referrals"
              >
                <span [title]="item.rmCode">{{
                  item.rmCode | limitWord : 5
                }}</span>
              </ng-option>
              <ng-template ng-header-tmp>
                <div>
                  <button
                    class="btn btn-link"
                    type="button"
                    (click)="onSelectAll(SELECTED_CONST.REFERRAL_ID)"
                  >
                    {{ "common.action.selectAll" | translate }}
                  </button>
                  <button
                    class="btn btn-link"
                    type="button"
                    (click)="onClearAll(SELECTED_CONST.REFERRAL_ID)"
                  >
                    {{ "common.action.clearAll" | translate }}
                  </button>
                </div>
              </ng-template>
            </ng-select>
          </div>
        </div>
        <div class="col-md-2 col-lg-2">
          <div class="form-group">
            <label>{{ "common.status" | translate }}</label>
            <ng-select
              placeholder="{{ 'common.status' | placeholder : 'select' }}"
              [searchable]="false"
              formControlName="status"
              [clearable]="true"
            >
              <ng-option
                [value]="item.code"
                *ngFor="let item of PREMIUM_ACC_NUMBER_STATUS"
              >
                {{ item.label | translate }}
              </ng-option>
            </ng-select>
          </div>
        </div>
        <div class="col-md-4 col-lg-4">
          <div class="date-picker-container form-group row">
            <div class="col-md-6">
              <label
                >{{ "common.action.fromDate" | translate }}
                <span class="text-danger">*</span></label
              >
              <mat-form-field appearance="fill" class="date-picker">
                <input
                  matInput
                  [matDatepicker]="startDate"
                  formControlName="startDate"
                  placeholder="DD/MM/YYYY"
                  [max]="maxDate | date : 'yyyy-MM-dd'"
                  (change)="changeValidDate()"
                  (dateInput)="changeValidDate()"
                  dateTransform
                />
                <mat-datepicker-toggle
                  matSuffix
                  [for]="startDate"
                ></mat-datepicker-toggle>
                <mat-datepicker #startDate></mat-datepicker>
              </mat-form-field>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formSearch.get('startDate')?.errors?.required &&
                  formSearch.get('startDate')?.touched
                "
              >
                {{ "error.required.fromDate" | translate }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formSearch.get('startDate')?.errors?.invalidDate &&
                  formSearch.get('startDate')?.touched
                "
              >
                {{ "error.required.inValidDate" | translate }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formSearch.get('startDate')?.errors?.invalidMaxDate &&
                  formSearch.get('startDate')?.touched
                "
              >
                {{
                  "error.maxDateCurrent"
                    | translate
                      : { param: "common.action.fromDate" | translate }
                }}
              </small>
            </div>
            <div class="col-md-6">
              <label
                >{{ "common.action.toDate" | translate }}
                <span class="text-danger">*</span></label
              >
              <mat-form-field appearance="fill" class="date-picker">
                <input
                  matInput
                  [matDatepicker]="endDate"
                  formControlName="endDate"
                  placeholder="DD/MM/YYYY"
                  min="{{
                    formSearch.controls['startDate'].value | date : 'yyyy-MM-dd'
                  }}"
                  [max]="maxToDate"
                  (change)="changeValidDate()"
                  (dateInput)="changeValidDate()"
                  dateTransform
                />
                <mat-datepicker-toggle
                  matSuffix
                  [for]="endDate"
                ></mat-datepicker-toggle>
                <mat-datepicker #endDate></mat-datepicker>
              </mat-form-field>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formSearch.get('endDate')?.errors?.required &&
                  formSearch.get('endDate')?.touched
                "
              >
                {{ "error.required.toDate" | translate }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formSearch.get('endDate')?.errors?.invalidMaxDate &&
                  formSearch.get('endDate')?.touched
                "
              >
                {{
                  "error.maxDateCurrent"
                    | translate : { param: "common.action.toDate" | translate }
                }}
              </small>
            </div>
          </div>
        </div>
        <div class="col-lg-2 col-md-2 mt-4 d-flex">
          <div
            class="col-btn-reset"
            ngbTooltip="{{ 'common.action.reset' | translate }}"
          >
            <div class="btn-reset">
              <i
                class="bi bi-arrow-clockwise"
                type="button"
                (click)="onReset()"
              >
              </i>
            </div>
          </div>
          <div>
            <button class="btn btn-search mr-2" type="submit">
              {{ "common.action.search" | translate }}
            </button>
          </div>
        </div>
      </div>
      <div class="border-bottom-search"></div>
      <div
        class="d-flex justify-content-between align-items-end text-right mb-3 mt-4"
      >
        <h5 class="title-table text-uppercase mb-4">
          {{ "model.premiumAccountNumber.list" | translate }}
        </h5>
        <div class="d-block text-right mb-3 mt-4">
          <button
            type="button"
            class="btn btn-red"
            (click)="onExport()"
            [disabled]="this.data.length === 0"
            *hasPrivileges="SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_EXPORT"
          >
            {{ "common.action.export" | translate }}
          </button>
        </div>
      </div>
    </form>
    <div class="table-responsive">
      <table class="table table-customer">
        <thead>
          <tr>
            <th class="text-center">{{ "model.rate.no" | translate }}</th>
            <th class="text-left">
              {{ "common.accountNumber" | translate }}
            </th>
            <th class="text-right">
              {{ "customerRegisterManagement.cif" | translate }}
            </th>
            <th class="text-left">
              {{ "common.customer" | translate }}
            </th>
            <th class="text-right">
              {{ "common.phoneNumber" | translate }}
            </th>
            <th class="text-left" width="110px">
              {{ "model.referral.referralCode" | translate }}
            </th>
            <th class="text-right" width="140px">
              {{ "model.structureAccount.listPrice" | translate }}
            </th>
            <th class="text-right" width="110px">
              {{ "model.structureAccount.discount" | translate }}
            </th>
            <th class="text-right" width="155px">
              {{ "model.structureAccount.paymentPrice" | translate }}
            </th>
            <th class="text-left" scope="col" width="100px">
              {{ "model.premiumAccountNumber.openDate" | translate }}
            </th>
            <th class="text-left" scope="col">
              {{ "model.structureAccount.numberStructure" | translate }}
            </th>
            <th class="text-center status-freezer" scope="col">
              {{ "common.status" | translate }}
            </th>
            <th class="text-center freezer" scope="col">
              {{ "common.action.label" | translate }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of data; let i = index">
            <td class="text-center">{{ fillIndexItem(i) }}</td>
            <td class="text-left">
              {{ item?.premiumAccNumber }}
            </td>
            <td class="text-right">
              {{ item?.cif }}
            </td>
            <td class="text-left">
              {{ item?.fullName }}
            </td>
            <td class="text-right">
              {{ item?.phoneNumber }}
            </td>
            <td class="text-left">
              {{ item?.rmCode || item?.referralCode }}
            </td>
            <td class="text-right">{{ item?.originalPrice | currencyLak }}</td>
            <td class="text-right">
              {{ item?.discount }}
            </td>
            <td class="text-right">{{ item?.totalPrice | currencyLak }}</td>
            <td class="text-left">{{ item?.createdDateStr }}</td>
            <td class="text-left" [title]="item?.numberStructure">
              {{ item?.numberStructure | limitWord : 20 }}
            </td>
            <td class="text-center status-freezer-td">
              <span
                class="badge"
                [ngClass]="
                  PREMIUM_ACC_NUMBER_STATUS_MAP[item.status || 0].style
                "
                >{{
                  PREMIUM_ACC_NUMBER_STATUS_MAP[item.status || 0].label
                    | translate
                }}</span
              >
            </td>
            <td class="text-center freezer-td">
              <ng-container *ngIf="item.status === 2">
                <button
                  ngbTooltip="{{ 'common.action.notification' | translate }}"
                  class="btn px-1 py-0"
                  *hasPrivileges="
                    SYSTEM_RULES.PREMIUM_ACCOUNT_NUMBER_NOTIFICATION
                  "
                  (click)="onNotification(item)"
                >
                  <i class="bi bi-megaphone mb-color" aria-hidden="true"></i>
                </button>
              </ng-container>
            </td>
          </tr>
        </tbody>
      </table>
      <div
        class="row d-block text-center m-0 no-search-result-wrapper"
        *ngIf="data?.length === 0"
      >
        <img
          src="/assets/dist/img/icon/empty.svg"
          height="120"
          alt="no_search_result"
        />
        <p class="text-center mb-5">
          {{ "common.no_search_result" | translate }}
        </p>
      </div>
    </div>
    <div *ngIf="data?.length" class="paginator col-md-12">
      <mat-paginator
        [length]="formSearch.value.length"
        [pageSize]="formSearch.value.pageSize"
        [pageIndex]="formSearch.value.pageIndex"
        [pageSizeOptions]="pageSizeOptions"
        (page)="onChangePage($event)"
        aria-label="Select page"
      >
      </mat-paginator>
    </div>
  </div>
</div>
