import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ICustomerActivationHistory } from '@shared/models/customer-activation-history.mode';
import { ICustomerApprovalHistory } from '@shared/models/customer-approval-history.mode';
import { ICustomer } from '@shared/models/customer.model';
import { IMoneyAccount } from '@shared/models/money-account.model';
import { IOtpPremiumAccNumber } from '@shared/models/otpPremiumAccNumber.model';
import { SearchWithPagination } from '@shared/models/request/base.request.model';
import { ICustomerSearch } from '@shared/models/request/customer.search';
import { IFileEntrySearch } from '@shared/models/request/file-entry.search';
import { IRequestOtp } from '@shared/models/requestOtp.model';
import CommonUtils from '@shared/utils/common-utils';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({ providedIn: 'root' })
export class CustomerService extends AbstractService {
  public resourceUrl = '/customer';

  constructor(protected http: HttpClient) {
    super(http);
  }

  /**
   * API list
   *
   * @param request SearchWithPagination
   * @returns Observable<EntityResponseType<ICustomer[]>>
   */
  list(
    request: SearchWithPagination
  ): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer[]>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  /**
   * API search
   *
   * @param request ICustomerSearch
   * @returns Observable<EntityResponseType<ICustomer[]>>
   */
  search(request: ICustomerSearch): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer[]>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  /**
   * API search approval
   *
   * @param request ICustomerSearch
   * @returns Observable<EntityResponseType<ICustomer[]>>
   */
  searchApproval(
    request: ICustomerSearch
  ): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer[]>>(
      `${this.resourceUrl}/search-approval`,
      request
    );
  }

  /**
   * API verify customer: check customer existed
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  identityVerifyCreate(
    request: ICustomer
  ): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/verify-create`,
      request
    );
  }

  /**
   * API verify customer: check customer existed
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  identityVerifyUpdate(
    request: ICustomer
  ): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/verify-update`,
      request
    );
  }

  /**
   * API create
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  create(request: ICustomer): Observable<EntityResponseType<ICustomer>> {
    const body = CommonUtils.appendObjectToFormData(request, new FormData());
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/create`,
      body
    );
  }

  /**
   * API create
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  confirmCreateAccount(
    request: ICustomer
  ): Observable<EntityResponseType<ICustomer>> {
    const body = CommonUtils.appendObjectToFormData(request, new FormData());
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/confirm-create`,
      body
    );
  }

  /**
   * API create
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  confirmUpdateAccount(
    request: ICustomer,
    imageDelete: []
  ): Observable<EntityResponseType<ICustomer>> {
    const body = CommonUtils.appendObjectToFormData(request, new FormData());
    if (imageDelete.length > 0) {
      body.append('idCardFileDelete', imageDelete.join(','));
    }
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/confirm-update`,
      body
    );
  }

  /**
   * API update
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  update(
    request: ICustomer,
    imageDelete: []
  ): Observable<EntityResponseType<ICustomer>> {
    const body = CommonUtils.appendObjectToFormData(request, new FormData());
    if (imageDelete.length > 0) {
      body.append('idCardFileDelete', imageDelete.join(','));
    }
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/update`,
      body
    );
  }

  /**
   * API get detail information customer
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  info(request: ICustomer): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/info`,
      request
    );
  }

  /**
   * API lock
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  lock(request: ICustomer): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/lock`,
      request
    );
  }

  /**
   * API reject lock customer
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  lockDeny(request: ICustomer): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/approval/lock/reject`,
      request
    );
  }

  /**
   * API approve lock customer
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  lockApproval(request: ICustomer): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/approval/lock/approve`,
      request
    );
  }

  /**
   * API unlock customer
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  unlock(request: ICustomer): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/unlock`,
      request
    );
  }

  /**
   * API unclosed customer
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  unclosed(request: ICustomer): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/unclosed`,
      request
    );
  }

  /**
   * API reject unlock customer
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  unlockDeny(request: ICustomer): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/approval/unlock/reject`,
      request
    );
  }

  /**
   * API reject unlock customer
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  unclosedDeny(request: ICustomer): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/approval/unclosed/reject`,
      request
    );
  }

  /**
   * API approve unlock customer
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  unlockApproval(
    request: ICustomer
  ): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/approval/unlock/approve`,
      request
    );
  }

  unclosedApproval(
    request: ICustomer
  ): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/approval/unclosed/approve`,
      request
    );
  }

  /**
   * API delete customer
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  approvalDeleteCustomer(
    request: ICustomer
  ): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/approval-delete`,
      request
    );
  }

  /**
   * API cancel customer
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  cancelCustomer(
    request: ICustomer
  ): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/closed`,
      request
    );
  }

  /**
   * API reject delete customer
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  deleteDeny(request: ICustomer): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/approval/cancel/reject`,
      request
    );
  }

  /**
   * API approve delete customer
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  deleteApproval(
    request: ICustomer
  ): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/approval/closed/approve`,
      request
    );
  }

  /**
   * API reject create customer
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  createDeny(request: ICustomer): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/approval/create/reject`,
      request
    );
  }

  /**
   * API approve create customer
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  createApproval(
    request: ICustomer
  ): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/approval/create/approve`,
      request
    );
  }

  /**
   * API reject update customer
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  updateDeny(request: ICustomer): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/approval/update/reject`,
      request
    );
  }

  /**
   * API approve update customer
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  updateApproval(
    request: ICustomer
  ): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/approval/update/approve`,
      request
    );
  }

  /**
   * API getCustomerAccountBalance
   *
   * @returns Observable<EntityResponseType<IMoneyAccount>>
   */
  getCustomerAccountBalance(
    request: ICustomer,
    ignoreError = true
  ): Observable<EntityResponseType<IMoneyAccount>> {
    return super.post<EntityResponseType<IMoneyAccount>>(
      `${this.resourceUrl}/account/balance`,
      request,
      { ignoreError }
    );
  }

  /**
   * API search auto-complete
   *
   * @param request
   * @returns
   */
  searchAutoComplete(
    request: ICustomerSearch,
    loading = true,
  ): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer[]>>(
      `${this.resourceUrl}/auto-complete`,
      request,
      {
        loading
      }
    );
  }

  /**
   * getImage
   *
   * @param search IFileEntrySearch
   * @param loading boolean
   * @param ignoreError boolean
   * @returns Observable<any>
   */
  getImage(search: IFileEntrySearch, loading = false, ignoreError = true): any {
    return super.postFile(`${this.resourceUrl}/get-image`, search, {
      loading,
      ignoreError,
    });
  }

  getImageSignature(
    search: IFileEntrySearch,
    loading = false,
    ignoreError = true
  ): any {
    return super.postFile(`${this.resourceUrl}/image-signature`, search, {
      loading,
      ignoreError,
    });
  }

  getEkycImage(
    search: IFileEntrySearch,
    loading = false,
    ignoreError = true
  ): any {
    return super.postFile(`${this.resourceUrl}/get-ekyc-image`, search, {
      loading,
      ignoreError,
    });
  }
  /**
   * Activation History
   *
   * @param request ICustomerSearch
   * @returns Observable<EntityResponseType<ICustomer[]>>
   */
  getActivationHistory(
    request: ICustomer
  ): Observable<EntityResponseType<ICustomerActivationHistory>> {
    return super.post<EntityResponseType<ICustomerActivationHistory>>(
      `${this.resourceUrl}/activation-history`,
      request
    );
  }

  /**
   * Activation History
   *
   * @param request ICustomerSearch
   * @returns Observable<EntityResponseType<ICustomer[]>>
   */
  getUpdateHistory(
    request: ICustomer
  ): Observable<EntityResponseType<ICustomerApprovalHistory>> {
    return super.post<EntityResponseType<ICustomerApprovalHistory[]>>(
      `${this.resourceUrl}/activity-history`,
      request
    );
  }

  /**
   * API reset password
   *
   * @param request IUser
   * @returns Observable<EntityResponseType<IUser>>
   */
  resetPassword(request: ICustomer): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/resetpw`,
      request
    );
  }

  /**
   * API export
   *
   * @param request ICustomerSearch
   * @returns Observable<any>
   */
  export(request: ICustomerSearch): Observable<any> {
    return super.postFile(`${this.resourceUrl}/export`, request);
  }

  /**
   * API get account number customer
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */
  getAccountNumberCustomer(
    request: ICustomer
  ): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer>>(
      `${this.resourceUrl}/account-numbers`,
      request
    );
  }

  downloadTemplate(): Observable<any> {
    return super.postFile(`${this.resourceUrl}/download-template`, null);
  }

  importDataTemplate(file: File): Observable<EntityResponseType<ICustomer>> {
    const formData = new FormData();
    formData.append('file', file);
    return super.post(`${this.resourceUrl}/import-customers`, formData);
  }
  /**
   * API search no transaction
   *
   * @param request ICustomerSearch
   * @returns Observable<EntityResponseType<ICustomer[]>>
   */

  searchNoTransaction(
    request: ICustomerSearch
  ): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer[]>>(
      `${this.resourceUrl}/search-no-transaction`,
      request
    );
  }

  cancelNoTransaction(
    request: Array<number>
  ): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer[]>>(
      `${this.resourceUrl}/closed-no-transaction`,
      request
    );
  }

  exportNoTransaction(request: ICustomerSearch): Observable<any> {
    return super.postFile(`${this.resourceUrl}/export-no-transaction`, request);
  }

  /**
   * API get activate account manual
   *
   * @param request ICustomer
   * @returns Observable<EntityResponseType<ICustomer>>
   */

  searchActivateAccount(
    request: ICustomerSearch
  ): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer[]>>(
      `${this.resourceUrl}/search/manual-activation`,
      request
    );
  }

  activateAccount(
    request: Array<number>
  ): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer[]>>(
      `${this.resourceUrl}/manual-activation`,
      request
    );
  }

  syncCustomerInfo(
    request: ICustomer
  ): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer[]>>(
      `${this.resourceUrl}/sync-customer-info`,
      request
    );
  }

  updateSectors(request: ICustomer): Observable<EntityResponseType<ICustomer>> {
    return super.post<EntityResponseType<ICustomer[]>>(
      `${this.resourceUrl}/update-sectors`,
      request
    );
  }

  exportActivateAccount(request: ICustomerSearch): Observable<any> {
    return super.postFile(
      `${this.resourceUrl}/export/manual-activation`,
      request
    );
  }

  sendSms(
    request: IRequestOtp
  ): Observable<EntityResponseType<IOtpPremiumAccNumber>> {
    return super.post<EntityResponseType<IOtpPremiumAccNumber>>(
      `${this.resourceUrl}/premium-request-otp`,
      request
    );
  }
}
