<div class="modal-content">
  <div class="modal-header" [ngClass]="isHiddenBtnClose ? 'border-0' : ''">
    <h5 class="modal-title" *ngIf="!isUpdate && !isDetail">
      {{ "informationTemplate.create" | translate }}
    </h5>
    <h5 class="modal-title" *ngIf="isUpdate">
      {{ "informationTemplate.update" | translate }}
    </h5>
    <h5 class="modal-title" *ngIf="isDetail">
      {{ "informationTemplate.detail" | translate }}
    </h5>
    <button
      type="button"
      class="close"
      data-dismiss="modal"
      aria-label="Close"
      (click)="activeModal.close(MODAL_ACTION.CLOSE.code)"
    >
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="formInformationTemplate">
      <div class="row">
        <div class="form-group col-md-6">
          <label
            >{{
              "model.informationTemplate.informationTemplateCode" | translate
            }}
            <span class="text-danger" *ngIf="!isDetail">*</span></label
          >
          <input
            trim
            type="text"
            formControlName="informationTemplateCode"
            class="w-100"
            class="form-control"
            [maxLength]="VALIDATORS.LENGTH.INFORMATION_TEMPLATE_CODE_MAX_LENGTH"
          />
          <small
            class="form-text text-danger noti-small"
            *ngIf="
              formInformationTemplate.get('informationTemplateCode')?.errors
                ?.required &&
              formInformationTemplate.get('informationTemplateCode')?.touched
            "
          >
            {{
              "informationTemplate.error.required.informationTemplateCode"
                | translate
            }}
          </small>
          <small
            class="form-text text-danger noti-small"
            *ngIf="
              formInformationTemplate.get('informationTemplateCode')?.errors
                ?.maxlength &&
              formInformationTemplate.get('informationTemplateCode')?.touched
            "
          >
            {{
              "informationTemplate.error.maxLength.informationTemplateCode"
                | translate
                  : {
                      param:
                        VALIDATORS.LENGTH.INFORMATION_TEMPLATE_CODE_MAX_LENGTH
                    }
            }}
          </small>
        </div>
        <div class="form-group col-md-6">
          <label
            >{{ "model.informationTemplate.informationTemplateName" | translate
            }}<span class="text-danger" *ngIf="!isDetail">*</span></label
          >
          <input
            trim
            type="text"
            formControlName="informationTemplateName"
            class="w-100"
            class="form-control"
            [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
          />
          <small
            class="form-text text-danger noti-small"
            *ngIf="
              formInformationTemplate.get('informationTemplateName')?.errors
                ?.required &&
              formInformationTemplate.get('informationTemplateName')?.touched
            "
          >
            {{
              "informationTemplate.error.required.informationTemplateName"
                | translate
            }}
          </small>
          <small
            class="form-text text-danger noti-small"
            *ngIf="
              formInformationTemplate.get('informationTemplateName')?.errors
                ?.pattern &&
              formInformationTemplate.get('informationTemplateName')?.touched
            "
          >
            {{
              "informationTemplate.error.pattern.informationTemplateName"
                | translate
            }}
          </small>
          <small
            class="form-text text-danger noti-small"
            *ngIf="
              formInformationTemplate.get('informationTemplateName')?.errors
                ?.maxlength &&
              formInformationTemplate.get('informationTemplateName')?.touched
            "
          >
            {{
              "informationTemplate.error.maxLength.informationTemplateName"
                | translate
                  : {
                      param: VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH
                    }
            }}
          </small>
        </div>
        <div class="form-group col-md-6">
          <label
            >{{ "model.informationTemplate.displayName" | translate
            }}<span class="text-danger" *ngIf="!isDetail">*</span></label
          >
          <input
            trim
            type="text"
            formControlName="displayName"
            class="w-100"
            class="form-control"
            [maxLength]="
              VALIDATORS.LENGTH.INFORMATION_TEMPLATE_DISPLAY_NAME_MAX_LENGTH
            "
          />
          <small
            class="form-text text-danger noti-small"
            *ngIf="
              formInformationTemplate.get('displayName')?.errors?.required &&
              formInformationTemplate.get('displayName')?.touched
            "
          >
            {{
              "informationTemplate.error.required.informationTemplateDisplayName"
                | translate
            }}
          </small>
          <small
            class="form-text text-danger noti-small"
            *ngIf="
              formInformationTemplate.get('displayName')?.errors?.maxlength &&
              formInformationTemplate.get('displayName')?.touched
            "
          >
            {{
              "informationTemplate.error.maxLength.informationTemplateDisplayName"
                | translate
                  : {
                      param:
                        VALIDATORS.LENGTH
                          .INFORMATION_TEMPLATE_DISPLAY_NAME_MAX_LENGTH
                    }
            }}
          </small>
          <small
            class="form-text text-danger noti-small"
            *ngIf="
              formInformationTemplate.get('displayName')?.errors?.pattern &&
              formInformationTemplate.get('displayName')?.touched
            "
          >
            {{
              "informationTemplate.error.pattern.informationTemplateDisplayName"
                | translate
            }}
          </small>
        </div>

        <div class="row">
          <div class="form-group col-md-12">
            <label class="col-12"
              >{{ "model.informationTemplate.template" | translate
              }}<span class="text-danger" *ngIf="!isDetail">*</span></label
            >
            <div class="col-12">
              <mb-editor
                [placeholder]="'content' | translate"
                [value]="formInformationTemplate?.get('template')?.value || ''"
                (HTMLdata)="getTemplate($event)"
                [isReadOnly]="isDetail"
                [maxLength]="VALIDATORS.LENGTH.INFORMATION_TEMPLATE_MAX_LENGTH"
              >
              </mb-editor>
            </div>
            <small
              class="form-text text-danger noti-small"
              *ngIf="
                formInformationTemplate.get('template')?.errors?.required &&
                formInformationTemplate.get('template')?.touched
              "
            >
              {{ "informationTemplate.error.required.template" | translate }}
            </small>
          </div>
        </div>
      </div>
    </form>
    <div class="row" *ngIf="isDetail">
      <div class="col-md-12">
        <p>
          {{ ("model.informationTemplate.createdBy" | translate) + ": " }}
          <strong>{{ template.createdByFullname }}</strong>
          {{ " - " }}
          {{ ("model.informationTemplate.time" | translate) + ": " }}
          <strong>{{ template.createdDate }}</strong>
        </p>
      </div>
      <div class="col-md-12">
        <p>
          {{ ("model.informationTemplate.lastModifiedBy" | translate) + ": " }}
          <strong>{{ template.lastModifiedByFullname }}</strong>
          {{ " - " }}
          {{ ("model.informationTemplate.time" | translate) + ": " }}
          <strong>{{ template.lastModifiedDate }}</strong>
        </p>
      </div>
    </div>
  </div>
  <div class="text-center pb-4">
    <button
      type="button"
      class="btn mb-btn-outline-color mr-3"
      (click)="activeModal.close(MODAL_ACTION.CANCEL.code)"
    >
      {{ "common.action.close" | translate }}
    </button>
    <ng-container
      *hasPrivileges="
        isUpdate
          ? SYSTEM_RULES.INFORMATION_TEMPLATE_WRITE
          : SYSTEM_RULES.INFORMATION_TEMPLATE_CREATE
      "
    >
      <button
        type="button"
        *ngIf="!isDetail"
        class="btn mb-btn-color"
        (click)="onSave()"
      >
        {{
          (isUpdate ? "common.action.update" : "common.action.create")
            | translate
        }}
      </button>
    </ng-container>
  </div>
</div>
