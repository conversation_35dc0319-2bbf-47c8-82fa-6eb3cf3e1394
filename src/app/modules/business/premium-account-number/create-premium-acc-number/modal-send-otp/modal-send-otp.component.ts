import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { MODEL_MAP_ITEM_COMMON } from '@shared/models/model.type';
import { IConfirmOtp } from '@shared/models/premium-acc-number.modal';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { PremiumAccNumberService } from '@shared/services/premium-acc-number.service';

@Component({
  selector: 'app-import-structure',
  templateUrl: './modal-send-otp.component.html',
  styleUrls: ['./modal-send-otp.component.scss'],
})
export class SendOtpComponent implements OnInit, OnDestroy {
  formConfirm: FormGroup = new FormGroup({});
  actionConfirm!: MODEL_MAP_ITEM_COMMON;
  SYSTEM_RULES = SYSTEM_RULES;
  VALIDATORS = VALIDATORS;
  @Input() data?: IConfirmOtp;
  targetTime!: Date;
  intervalId: any;
  timeLeft?: string;
  submitConfirm = 0;
  isLoading = false;
  isHidden = false;
  @Input() onResendOtp!: () => Promise<boolean>;
  constructor(
    private fb: FormBuilder,
    public activeModal: NgbActiveModal,
    private premiumAccNumberService: PremiumAccNumberService,
    private toastService: ToastrCustomService
  ) {}

  ngOnDestroy() {
    clearInterval(this.intervalId);
  }

  ngOnInit(): void {
    this.startCountdown();
    this.initForm();
  }

  startCountdown() {
    this.targetTime = new Date(new Date().getTime() + 2 * 60 * 1000);
    this.intervalId = setInterval(() => {
      const now = new Date().getTime();
      const distance = new Date(this.targetTime).getTime() - now;

      if (distance <= 0) {
        clearInterval(this.intervalId);
        this.isHidden = true;
      } else {
        const hours = Math.floor((distance / (1000 * 60 * 60)) % 24);
        const minutes = Math.floor((distance / (1000 * 60)) % 60);
        const seconds = Math.floor((distance / 1000) % 60);
        this.timeLeft = `${this.pad(hours)}:${this.pad(minutes)}:${this.pad(
          seconds
        )}`;
      }
    }, 100);
  }

  pad(value: number): string {
    return value < 10 ? `0${value}` : `${value}`;
  }

  initForm() {
    this.formConfirm = this.fb.group({
      otpValue: [
        { value: '', disabled: false },
        [
          Validators.required,
          Validators.minLength(VALIDATORS.LENGTH.OTP_MAX_LENGTH),
        ],
      ],
    });
  }

  onConfirmOtp() {
    const param = {
      customerId: this.data?.customerId,
      otpValue: this.formConfirm.get('otpValue')?.value,
      transactionId: this.data?.transactionId,
    };

    this.premiumAccNumberService.confirmOtp(param).subscribe((res: any) => {
      if (res) {
        this.toastService.success('premiumAccNumber.success.confirmOtp');
        this.activeModal.close(this.actionConfirm.code);
      }
    });

    if (this.submitConfirm++ >= 2) {
      this.activeModal.close();
    }
  }

  resendOTP(event: Event) {
    event.preventDefault();
    if (this.isLoading) return;
    this.startCountdown();
    this.isLoading = true;
    this.isHidden = false;

    this.onResendOtp().then((success) => {
      if (!success) {
        this.activeModal.close();
      }
    }).finally(() => {
      setTimeout(() => {
        this.isLoading = false;
      }, 1000);
    });
  }
}
