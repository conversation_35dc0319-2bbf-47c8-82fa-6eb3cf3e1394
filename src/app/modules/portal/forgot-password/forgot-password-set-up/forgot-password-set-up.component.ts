import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { MODAL_ACTION } from '@shared/constants/app.constants';
import { ACTION_TOKEN_CONST } from '@shared/constants/user.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ActiveToken, IActiveToken } from '@shared/models/active-token.model';
import { AccountService } from '@shared/services/account.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { encrypt, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import Validation from '@shared/validators/confirmed-password.validator';

@Component({
  selector: 'app-forgot-password-set-up',
  templateUrl: './forgot-password-set-up.component.html',
  styleUrls: ['./forgot-password-set-up.component.scss'],
})
export class ForgotPasswordSetUpComponent implements OnInit {
  passwordVisible = false;
  confirmPasswordVisible = false;

  userForgotPasswordForm = this.fb.group(
    {
      email: [
        '',
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.EMAIL_MB),
          Validators.minLength(VALIDATORS.LENGTH.EMAIL_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.EMAIL_MAX_LENGTH),
        ],
      ],
      password: [
        '',
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.PASSWORD),
          Validators.minLength(VALIDATORS.LENGTH.PASSWORD_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.PASSWORD_MAX_LENGTH),
        ],
      ],
      confirmPassword: [
        '',
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.PASSWORD),
          Validators.minLength(VALIDATORS.LENGTH.PASSWORD_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.PASSWORD_MAX_LENGTH),
        ],
      ],
      token: [''],
    },
    {
      validators: [Validation.match('password', 'confirmPassword')],
    }
  );

  // Kiểm tra mật khẩu khác nhau hay không
  get f(): { [key: string]: AbstractControl } {
    return this.userForgotPasswordForm.controls;
  }

  VALIDATORS = VALIDATORS;

  activeTokenDTO: IActiveToken = {};

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private translate: TranslateService,
    private toastService: ToastrCustomService,
    private accountService: AccountService,
    private activatedRoute: ActivatedRoute,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    // get token from paramater
    this.activatedRoute.paramMap.subscribe((res) => {
      const token = res.get('token');
      if (token) {
        this.activeTokenDTO.token = token;
      }
    });
  }

  /**
   * onSetUpPassword
   */
  onSetUpPassword(): void {
    const { email, password, confirmPassword } =
      this.userForgotPasswordForm.value;
    if (this.userForgotPasswordForm.invalid) {
      CommonUtils.markFormGroupTouched(this.userForgotPasswordForm);
    }
    const params = this.userForgotPasswordForm.getRawValue();
    if (this.userForgotPasswordForm.valid) {
      // get token from param
      params.token = this.activeTokenDTO.token;
      // encrypt password
      params.password = encrypt(password) + '';
      params.confirmPassword = encrypt(confirmPassword) + '';
      this.accountService.changePassword(params).subscribe((res: any) => {
        // this.isResendSucces = true;
        const data = res.body as ActiveToken;
        // check type if CHANGE_PW_SUCCESS go to link login
        if (data.tokenAction === ACTION_TOKEN_CONST.CHANGE_PW_SUCCESS) {
          this.toastService.success('common.success');
          this.router.navigate([ROUTER_UTILS.auth.login]);
          // check type if EXPIRE_TOKEN_CHANGE_PW open popup
        } else if (
          data.tokenAction === ACTION_TOKEN_CONST.EXPIRE_TOKEN_CHANGE_PW
        ) {
          const modalData = {
            title: 'public.forgotPassword.error.titleExpireSetupPw',
            content: 'public.forgotPassword.error.contentExpireSetupPw',
          };
          this.modalService.confirm(modalData).then((result) => {
            if (result === MODAL_ACTION.CONFIRM.code) {
              this.router.navigate([ROUTER_UTILS.portal.forgotPassword.root]);
            }
          });
        }
      });
    }
  }

  /**
   * displayContent: display content in file i18n
   *
   * @param content string
   * @returns string
   */
  displayContent(content: string): string {
    return this.translate.instant(content);
  }

  /**
   * Change type view password
   */
  onChangPasswordVisible(): void {
    this.passwordVisible = !this.passwordVisible;
  }

  /**
   * Change type view password
   */
  onChangConfirmPasswordVisible(): void {
    this.confirmPasswordVisible = !this.confirmPasswordVisible;
  }
}
