import { Pipe, PipeTransform } from '@angular/core';

/**
 * Slice date from 0 to 10
 *
 * <AUTHOR>
 * @date 2022-05-15
 * @export
 * @class AppDatePipe
 * @implements {PipeTransform}
 * @howToUse
 * ```
 *     <some-element>{{ user?.date | appDate }}</some-element>
 * ```
 */
@Pipe({ name: 'appDate' })
export class AppDatePipe implements PipeTransform {
  transform(date: string): string {
    if (date) {
      return date.slice(0, 10);
    }
    return '';
  }
}
