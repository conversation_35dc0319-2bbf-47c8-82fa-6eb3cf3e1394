<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form
        [formGroup]="formTransactionReportSearch"
        (submit)="onSearchSubmit()"
      >
        <div class="row">
          <div class="col-md-4 col-lg-2">
            <div class="form-group">
              <label>{{ "common.action.searchKeyword" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="keyword"
                class="w-100"
                class="form-control"
                placeholder="{{ 'common.action.searchKeyword' | placeholder }}"
              />
            </div>
          </div>
          <div class="form-group col-md-4 col-lg-2">
            <label>{{ "model.merchant.transferType" | translate }}</label>
            <ng-select
              [multiple]="true"
              appearance="outline"
              [searchable]="true"
              [clearable]="true"
              formControlName="billingTypes"
              placeholder="{{ 'model.merchant.transferType' | translate }}"
            >
              <ng-option
                [value]="item.code"
                *ngFor="let item of TRANSACTION_MMONEY_ENTITY_TYPE"
              >
                {{ item.label | translate }}
              </ng-option>
              <ng-template ng-header-tmp>
                <div>
                  <button
                    class="btn btn-link"
                    type="button"
                    (click)="onSelectAll(SELECTED_CONST.TRANSACTION_TYPE)"
                  >
                    {{ "common.action.selectAll" | translate }}
                  </button>
                  <button
                    class="btn btn-link"
                    type="button"
                    (click)="onClearAll(SELECTED_CONST.TRANSACTION_TYPE)"
                  >
                    {{ "common.action.clearAll" | translate }}
                  </button>
                </div>
              </ng-template>
            </ng-select>
          </div>
          <div class="form-group col-md-4 col-lg-2">
            <label>{{
              "model.report.transaction.transactionStatus" | translate
            }}</label>
            <ng-select
              placeholder="{{
                'model.report.transaction.transactionStatus' | translate
              }}"
              [searchable]="true"
              formControlName="transactionStatus"
              [clearable]="true"
              [multiple]="true"
              appearance="outline"
            >
              <ng-option
                [value]="item.code"
                *ngFor="let item of TRANSACTION_ENTITY_STATUS"
              >
                {{ item.label | translate }}
              </ng-option>
              <ng-template ng-header-tmp>
                <div>
                  <button
                    class="btn btn-link"
                    type="button"
                    (click)="onSelectAll(SELECTED_CONST.TRANSACTION_STATUS)"
                  >
                    {{ "common.action.selectAll" | translate }}
                  </button>
                  <button
                    class="btn btn-link"
                    type="button"
                    (click)="onClearAll(SELECTED_CONST.TRANSACTION_STATUS)"
                  >
                    {{ "common.action.clearAll" | translate }}
                  </button>
                </div>
              </ng-template>
            </ng-select>
          </div>
          <div class="col-lg-4 col-md-8">
            <div class="date-picker-container form-group row">
              <div class="col-md-6">
                <label
                  >{{ "common.action.fromDate" | translate
                  }}<span class="text-danger">*</span></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="fromDate"
                    formControlName="fromDate"
                    placeholder="DD/MM/YYYY"
                    [max]="maxDate"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="fromDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #fromDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formTransactionReportSearch.get('fromDate')?.errors
                      ?.required &&
                    formTransactionReportSearch.get('fromDate')?.touched
                  "
                >
                  {{ "error.required.fromDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formTransactionReportSearch.get('fromDate')?.errors
                      ?.invalidDate &&
                    formTransactionReportSearch.get('fromDate')?.touched
                  "
                >
                  {{ "error.required.inValidDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formTransactionReportSearch.get('fromDate')?.errors
                      ?.invalidMaxDate &&
                    formTransactionReportSearch.get('fromDate')?.touched
                  "
                >
                  {{
                    "error.maxDateCurrent"
                      | translate
                        : {
                            param: "common.action.fromDate" | translate
                          }
                  }}
                </small>
              </div>
              <div class="col-md-6">
                <label
                  >{{ "common.action.toDate" | translate
                  }}<span class="text-danger">*</span></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="toDate"
                    formControlName="toDate"
                    placeholder="DD/MM/YYYY"
                    min="{{
                      formTransactionReportSearch.controls['fromDate'].value
                        | date : 'yyyy-MM-dd'
                    }}"
                    [max]="maxToDate"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="toDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #toDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formTransactionReportSearch.get('toDate')?.errors
                      ?.required &&
                    formTransactionReportSearch.get('toDate')?.touched
                  "
                >
                  {{ "error.required.toDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formTransactionReportSearch.get('toDate')?.errors
                      ?.invalidMaxDate &&
                    formTransactionReportSearch.get('toDate')?.touched
                  "
                >
                  {{
                    "error.maxDateCurrent"
                      | translate
                        : {
                            param: "common.action.toDate" | translate
                          }
                  }}
                </small>
              </div>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
            >
              <div class="btn-reset">
                <i
                  class="bi bi-arrow-clockwise"
                  type="button"
                  (click)="onReset()"
                >
                </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" type="submit">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-4 mt-4">
          <!-- <button class="btn btn-red mr-2" (click)="print()">{{ "common.action.print" | translate }}
          </button> -->
          <button
            class="btn btn-red mr-2"
            type="button"
            (click)="exportFile()"
            [disabled]="this.transactionReport.length === 0"
            *hasPrivileges="SYSTEM_RULES.UTILITY_TRANSACTION_EXPORT"
          >
            {{ "common.action.export" | translate }}
          </button>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table table-transaction">
          <thead>
            <tr>
              <th scope="col" class="text-center">
                {{ "common.no" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{ "model.report.transaction.customerAccNumber" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.report.transaction.customerAccountName" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "model.merchant.transactionHistory.date" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{ "model.report.transaction.target" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{
                  "model.report.transaction.beneficiaryCustomerName" | translate
                }}
              </th>

              <th scope="col" class="text-right">
                {{ "model.merchant.transactionHistory.cif" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.report.transaction.transactionCurrency" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.merchant.transferType" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{ "model.merchant.monney" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{ "model.merchant.fee" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{ "model.merchant.transactionHistory.discount" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{ "model.report.transaction.fixedDiscount" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{
                  "model.merchant.transactionHistory.totalAmount" | translate
                }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.merchant.transactionHistory.code" | translate }}
              </th>
              <!-- <th scope="col" class="text-center">
                {{ "common.status" | translate }}
              </th> -->
              <th scope="col" class="text-left">
                {{ "model.report.transaction.transID" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{ "model.report.transaction.invoiceNumber" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.report.transaction.proCode" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.report.transaction.remark" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.report.transaction.titleLa" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.report.transaction.accName" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "model.report.transaction.tradingResults" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of transactionReport; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>

              <td class="text-right">
                {{ dataItem.customerAccNumber }}
              </td>
              <td class="text-left" title="{{ dataItem.customerAccountName }}">
                {{ dataItem.customerAccountName | limitWord }}
              </td>
              <td class="text-center">
                {{ dataItem.transactionStartTime }}
              </td>
              <td class="text-right" [title]="dataItem.target">
                {{ dataItem?.beneficiaryAccountNumber }}
              </td>
              <td class="text-left">
                {{ dataItem?.beneficiaryCustomerName }}
              </td>

              <td class="text-right">{{ dataItem.customerCif }}</td>
              <td class="text-left">{{ dataItem.transactionCurrency }}</td>
              <td class="text-left">{{ dataItem?.billingTypeStr || "" }}</td>
              <td
                class="text-left"
                *ngIf="
                  dataItem.transferType ===
                  TRANSACTION_TYPE_TRANSFER_MONEY_CONST
                "
              >
                {{
                  (dataItem.bankCode === TYPE_BANK_CODE_MB_CONST
                    ? "model.report.transaction.internalBank"
                    : "model.report.transaction.interBank"
                  ) | translate
                }}
              </td>

              <td class="text-right">
                {{ dataItem.transactionAmount | currencyLak }}
              </td>
              <td class="text-right">
                {{ dataItem.transactionFee | currencyLak }}
              </td>
              <td scope="col" class="text-right">
                {{ dataItem.discount }}
              </td>
              <td scope="col" class="text-right">
                {{ dataItem.discountFixedStr }}
              </td>
              <td class="text-right">
                {{ dataItem.totalAmount || "" | currencyLak }}
              </td>
              <td>{{ dataItem.transactionCode }}</td>
              <td class="text-left">
                {{ dataItem.transactionId }}
              </td>
              <td class="text-right">
                {{ dataItem?.target }}
              </td>
              <td class="text-left">
                {{ dataItem?.provinceCode }}
              </td>
              <td class="text-left">
                {{ dataItem?.message }}
              </td>
              <td class="text-left">
                {{ dataItem?.titleLa }}
              </td>
              <td class="text-left">
                {{ dataItem?.accountName }}
              </td>
              <td class="text-center">
                <span
                  class="badge"
                  [ngClass]="
                    TRANSACTION_ENTITY_STATUS_MAP[
                      dataItem.transactionStatus ||
                        ENTITY_TRANSACTION_STATUS_CONST.PROCESSING.code
                    ].style
                  "
                  >{{
                    TRANSACTION_ENTITY_STATUS_MAP[
                      dataItem.transactionStatus ||
                        ENTITY_TRANSACTION_STATUS_CONST.PROCESSING.code
                    ].label | translate
                  }}</span
                >
              </td>
            </tr>
          </tbody>
        </table>
        <div
          class="row d-block text-center m-0 no-search-result-wrapper"
          *ngIf="transactionReport?.length === 0"
        >
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
      </div>
      <div *ngIf="transactionReport.length">
        <mat-paginator
          [length]="formTransactionReportSearch.value.length"
          [pageSize]="formTransactionReportSearch.value.pageSize"
          [pageIndex]="formTransactionReportSearch.value.pageIndex"
          [pageSizeOptions]="pageSizeOptions"
          (page)="onChangePage($event)"
          aria-label="Select page"
        >
        </mat-paginator>
      </div>
    </div>
  </div>
</section>
