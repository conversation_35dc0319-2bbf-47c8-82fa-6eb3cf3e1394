import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { TransactionFeeTypeSearch } from '@shared/models/request/transaction-fee-type.search';
import {
  IServicePack
} from '@shared/models/service-pack.model';
import { ISms } from '@shared/models/sms.model';
import { ITransactionFeeType } from '@shared/models/transaction-fee-type.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { Observable } from 'rxjs';
import { AbstractDomainService } from './common/abstract.domain.service';
import { EntityResponseType } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class ServicePackService extends AbstractDomainService<IServicePack> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.SERVICE_PACK;

  export(request: ISms): Observable<EntityResponseType<ISms>> {
    return super.postFile(RESOURCE_CONST.SAVING.API.EXPORT, request);
  }

  getAllServicePackType(request: TransactionFeeTypeSearch): Observable<EntityResponseType<ITransactionFeeType>> {
    return super.post(RESOURCE_CONST.SERVICE_PACK.API.SERVICE_PACK_TYPE, request);
  }
}
