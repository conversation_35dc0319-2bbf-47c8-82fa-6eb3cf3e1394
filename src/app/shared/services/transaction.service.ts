import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IMerchantTransactionHistory } from '@shared/models/merchant-transaction-history.model';
import { ITransactionSearch } from '@shared/models/request/transaction.search';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({ providedIn: 'root' })
export class TransactionService extends AbstractService {
  public resourceUrl = '/transaction';

  constructor(protected http: HttpClient) {
    super(http);
  }

  /**
   * API search
   *
   * @param request ITransactionSearch
   * @returns Observable<EntityResponseType<IMerchantTransactionHistory>>
   */
  search(
    request: ITransactionSearch
  ): Observable<EntityResponseType<IMerchantTransactionHistory>> {
    return super.post<EntityResponseType<IMerchantTransactionHistory>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  /**
   * API detail
   *
   * @param request IMerchantTransactionHistory
   * @returns Observable<EntityResponseType<IMerchantTransactionHistory>>
   */
  detail(
    request: IMerchantTransactionHistory
  ): Observable<EntityResponseType<IMerchantTransactionHistory>> {
    return super.post<EntityResponseType<IMerchantTransactionHistory>>(
      `${this.resourceUrl}/detail`,
      request
    );
  }

  /**
   * API export
   *
   * @param request ITransactionSearch
   * @returns Observable<any>
   */
  export(request: ITransactionSearch): Observable<any> {
    return super.postFile(`${this.resourceUrl}/export`, request);
  }

  searchUmoney(
    request: ITransactionSearch
  ): Observable<EntityResponseType<IMerchantTransactionHistory>> {
    return super.post<EntityResponseType<IMerchantTransactionHistory>>(
      `${this.resourceUrl}/umoney/search`,
      request
    );
  }

  exportUmoney(request: ITransactionSearch): Observable<any> {
    return super.postFile(`${this.resourceUrl}/umoney/export`, request);
  }

  exportDebitDeposit(request: ITransactionSearch): Observable<any> {
    return super.postFile(`${this.resourceUrl}/debit-deposit/export`, request);
  }

  searchMMoney(
    request: ITransactionSearch
  ): Observable<EntityResponseType<IMerchantTransactionHistory>> {
    return super.post<EntityResponseType<IMerchantTransactionHistory>>(
      `${this.resourceUrl}/utility/search`,
      request
    );
  }

  exportMMoney(request: ITransactionSearch): Observable<any> {
    return super.postFile(`${this.resourceUrl}/utility/export`, request);
  }

  searchInternationalTransaction(
    request: ITransactionSearch
  ): Observable<EntityResponseType<IMerchantTransactionHistory>> {
    return super.post<EntityResponseType<IMerchantTransactionHistory>>(
      `${this.resourceUrl}/international-payment/search`,
      request
    );
  }

  exportInternationalTransaction(request: ITransactionSearch): Observable<any> {
    return super.postFile(`${this.resourceUrl}/international-payment/export`, request);
  }



  searchDebitUmoney(
    request: ITransactionSearch
  ): Observable<EntityResponseType<IMerchantTransactionHistory>> {
    return super.post<EntityResponseType<IMerchantTransactionHistory>>(
      `${this.resourceUrl}/debit-deposit/search`,
      request
    );
  }

  exportDebitUmoney(request: ITransactionSearch): Observable<any> {
    return super.postFile(`${this.resourceUrl}/debit-deposit/export`, request);
  }
}
