<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="onBack()" />
      <h5>
        {{
          "customerRegisterManagement.customerApproval.approvalTitle"
            | translate
        }}
      </h5>
    </div>
    <div class="col-md-12">
      <div class="row">
        <h2 class="title-create">
          {{
            "customerRegisterManagement.customerApproval.request.title"
              | translate
          }}
        </h2>
      </div>
      <app-customer-info [customerInfo]="customerInfo"> </app-customer-info>

      <app-customer-history-info [customerInfo]="customerInfo">
      </app-customer-history-info>
      <app-customer-update-history
        [customerInfo]="customerInfo"
      ></app-customer-update-history>

      <div class="d-block text-center mb-5 mt-4">
        <!-- <button class="btn btn-white mr-2" *ngIf="isUpdate">
              {{ "common.action.update" | translate }}
            </button> -->
        <button class="btn btn-red mr-2" (click)="onBack()">
          {{ "common.action.back" | translate }}
        </button>
        <ng-container *ngFor="let item of approvalButton">
          <ng-container *hasPrivileges="item.privilege">
            <ng-container
              *ngIf="
                (customerInfo.approvalStatus ===
                  APPROVAL_STATUS_CONST.PENDING.value ||
                  customerInfo.approvalStatus ===
                    APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
                customerInfo.approvalType === item.type
              "
              ><button class="btn btn-white mr-2" (click)="deny(item.type)">
                {{ "common.action.deny" | translate }}
              </button>
              <button class="btn btn-red" (click)="approval(item.type)">
                {{ "common.action.approval" | translate }}
              </button></ng-container
            >
          </ng-container>
        </ng-container>
        <!-- <ng-container
          *hasPrivileges="[
            SYSTEM_RULES.CUSTOMER_UPDATE_APPROVAL,
            SYSTEM_RULES.CUSTOMER_LOCK_APPROVAL,
            SYSTEM_RULES.CUSTOMER_UNLOCK_APPROVAL
          ]"
        >
          <button
            *ngIf="
              customerInfo.approvalStatus ===
                APPROVAL_STATUS_CONST.PENDING.value ||
              customerInfo.approvalStatus ===
                APPROVAL_STATUS_CONST.APPROVAL_ERROR.value
            "
            class="btn btn-white mr-2"
            (click)="deny()"
          >
            {{ "common.action.deny" | translate }}
          </button>
          <button
            *ngIf="
              customerInfo.approvalStatus ===
                APPROVAL_STATUS_CONST.PENDING.value ||
              customerInfo.approvalStatus ===
                APPROVAL_STATUS_CONST.APPROVAL_ERROR.value
            "
            class="btn btn-red"
            (click)="approval()"
          >
            {{ "common.action.approval" | translate }}
          </button>
        </ng-container> -->
      </div>
    </div>
  </div>
</section>
