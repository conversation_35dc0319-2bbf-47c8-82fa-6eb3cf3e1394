import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ICaptcha } from '@shared/models/captcha.model';

import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({ providedIn: 'root' })
export class CaptchaService extends AbstractService {
  public resourceUrl = '/public/captcha';

  constructor(protected http: HttpClient) {
    super(http);
  }

  /**
   * Get captcha
   *
   * @returns ICaptcha
   */
  getCaptcha() {
    return super.post<EntityResponseType<ICaptcha>>(this.resourceUrl);
  }
}
