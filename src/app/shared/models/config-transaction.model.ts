export interface IConfigTransaction {
  transactionLimitId: number;
  serviceName?: string;
  startDate?: string;
  endDate?: string;
  maxTransferAmtOfDay?: number;
  maxTransferAmtOfMonth?: number;
  maxTransferAmtOfYear?: number;
  status: number;
  sectorId: number;
  createdDate?: string;
  lastModifiedDate?: string;
  lastModifiedBy?: string;
  transactionLimitDetailDTOS: ITransLimitDetails[];
  approvalBy?: string;
  approvalDate?: string;
  reason?: string;
  currency?: string;
}

export interface ITransLimitDetails {
  transferType?: string;
  maxTransferAmount?: number;
}
