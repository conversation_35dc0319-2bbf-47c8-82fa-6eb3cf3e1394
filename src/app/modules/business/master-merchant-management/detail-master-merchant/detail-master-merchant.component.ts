import { Component, HostListener, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { SafeUrl } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  CALCULATE_MILLISECONDS_IN_A_YEAR,
  CONFIGURATION_FEE_TYPE_CONST,
  DATE_RANGE_STATISTICAL_LOAN_ONLINE,
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  FILE_EXTENSION,
  GENDER,
  MASTER_MERCHANT_SERVICE,
  MASTER_MERCHANT_SERVICE_CONST,
  MASTER_MERCHANT_SERVICE_ENUM,
  MASTER_MERCHANT_SERVICE_MAP,
  MASTER_MERCHANT_TYPE_BILLING,
  MASTER_MERCHANT_TYPE_BILLING_CONST,
  MASTER_MERCHANT_TYPE_ENUM,
  MASTER_MERCHANT_TYPE_TOPUP,
  MASTER_MERCHANT_TYPE_TOPUP_CONST,
  MOMENT_CONST,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IMerchantTransactionHistory } from '@shared/models/merchant-transaction-history.model';
import { IMerchant } from '@shared/models/merchant.model';
import { IMerchantTransactionHistorySearch } from '@shared/models/request/merchant-transaction-history.search';
import { AuthenticationService } from '@shared/services/auth/authentication.service';
import { DownloadService } from '@shared/services/helpers/download.service';
import { MerchantService } from '@shared/services/merchant.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';

@Component({
  selector: 'app-detail-master-merchant',
  templateUrl: './detail-master-merchant.component.html',
  styleUrls: ['./detail-master-merchant.component.scss'],
})
export class DetailMasterMerchantComponent implements OnInit, OnDestroy {
  formMerchant: FormGroup = new FormGroup({});

  merchantHistorySearch: IMerchantTransactionHistorySearch = {};
  // input call api
  merchantDto: IMerchant = {};
  // input data form control
  merchant: IMerchant = {};
  // master Merchant
  masterMerchant: IMerchant[] = [];
  // master Merchant
  merchantHistory: IMerchantTransactionHistory[] = [];
  // master Merchant
  merchantChild: IMerchant[] = [];
  action = '';
  currentValue = '';
  merchantAccountNumber = '';
  qrCode = '';
  qrCodeDownloadLink: SafeUrl = '';
  hasFilter = false;
  isUpdate = false;
  maxDateOfBirth = new Date();
  maxDate = new Date();
  maxToDate = new Date();
  isOther = false;
  money = '';
  feeId: number | undefined;
  configurationFeeType = '';

  pageSizeOptions = PAGINATION.OPTIONS;
  pageSizeOptionsChild = PAGINATION.OPTIONS;
  ROUTER_UTILS = ROUTER_UTILS;
  ENTITY_STATUS = ENTITY_STATUS;
  VALIDATORS = VALIDATORS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  GENDER_LIST = GENDER;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  SYSTEM_RULES = SYSTEM_RULES;
  MASTER_MERCHANT_SERVICE = MASTER_MERCHANT_SERVICE;
  MASTER_MERCHANT_SERVICE_MAP = MASTER_MERCHANT_SERVICE_MAP;
  MASTER_MERCHANT_SERVICE_CONST = MASTER_MERCHANT_SERVICE_CONST;
  MASTER_MERCHANT_SERVICE_ENUM = MASTER_MERCHANT_SERVICE_ENUM;
  MASTER_MERCHANT_TYPE_TOPUP = MASTER_MERCHANT_TYPE_TOPUP;
  MASTER_MERCHANT_TYPE_TOPUP_CONST = MASTER_MERCHANT_TYPE_TOPUP_CONST;
  MASTER_MERCHANT_TYPE_BILLING_CONST = MASTER_MERCHANT_TYPE_BILLING_CONST;
  MASTER_MERCHANT_TYPE_ENUM = MASTER_MERCHANT_TYPE_ENUM;
  MASTER_MERCHANT_TYPE_BILLING = MASTER_MERCHANT_TYPE_BILLING;
  isValidMaxDate = (control: FormControl) => {
    const dateOfBirth = new Date(control.value).getTime();
    const maxDateOfBirth = new Date().getTime();
    return dateOfBirth > maxDateOfBirth ? { invalidMaxDate: true } : null;
  };

  // default form search
  formListChildSearch = this.fb.group({
    keyword: '',
    parentId: null,
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    timeZoneStr: '',
    // previousPageIndex: 0,
  });

  // default form search
  formMerchantHistorySearch = this.fb.group({
    keywordMerchant: '',
    transactionDateStart: [null, [Validators.required]],
    transactionDateEnd: [null, [Validators.required, this.isValidMaxDate]],
    merchantId: null,
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    sourceZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
    // previousPageIndex: 0,
  });

  constructor(
    private fb: FormBuilder,
    private formBuilder: FormBuilder,
    private merchantService: MerchantService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private downloadService: DownloadService,
    private translateService: TranslateService,
    private authenticationService: AuthenticationService
  ) {
    this.activatedRoute.data.subscribe((res) => {
      this.action = res.action;
    });
    // get id form paramater
    this.activatedRoute.paramMap.subscribe((res) => {
      const idParam = res.get('merchantId');
      if (idParam) {
        this.merchantDto.merchantId = +idParam;
        this.isUpdate = true;
      }
    });
  }

  /**
   * remove storage
   */
  ngOnDestroy(): void {
    if (!this.hasFilter) {
      sessionStorage.removeItem(STORAGE_APP.MASTER_MERCHANT);
    }
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.hasFilter = true;
  }

  ngOnInit(): void {
    this.getMasterMerchant();
    if (this.isUpdate) {
      this.getDetail();
      // this.mappingRoles(this.roles);
    } else {
      this.initForm();
    }
  }

  /**
   * init form
   *
   * @param merchant User
   */
  initForm(merchant?: IMerchant): void {
    this.formMerchant = this.formBuilder.group({
      serviceType: [merchant?.serviceType || null, [Validators.required]],
      merchantCode: [merchant?.merchantCode || ''],
      merchantName: [merchant?.merchantName || ''],
      merchantAccountNumber: [merchant?.merchantAccountNumber || ''],
      parentId: [merchant?.parentId || null, [Validators.required]],
      status: [merchant?.status || ENTITY_STATUS_CONST.ACTIVE.code],
      description: [merchant?.description || ''],
      qrCode: [merchant?.qrCode || ''],
      cif: [merchant?.cif || ''],
      phoneNumber: [merchant?.phoneNumber || ''],
    });
    if (
      merchant?.serviceType === MASTER_MERCHANT_SERVICE_ENUM.BILLING ||
      merchant?.serviceType === MASTER_MERCHANT_SERVICE_ENUM.TOPUP
    ) {
      this.isOther = false;
    }
    if (merchant?.serviceType === MASTER_MERCHANT_SERVICE_ENUM.OTHER) {
      this.isOther = true;
    }
    this.formMerchant.disable();
  }

  /**
   * back to monitor list
   */
  backToList(): void {
    this.hasFilter = true;
    this.router.navigate([ROUTER_UTILS.merchant.masterMerchant.root]);
  }

  /**
   * Change page: next page and preview page
   *
   * @param page PageEvent
   */
  onChangePageListChild(page: PageEvent) {
    this.formListChildSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formListChildSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearchListChildMerChant();
  }

  /**
   * Change page: next page and preview page
   *
   * @param page PageEvent
   */
  onChangePageHistory(page: PageEvent) {
    this.formMerchantHistorySearch.controls.pageIndex.setValue(page.pageIndex);
    this.formMerchantHistorySearch.controls.pageSize.setValue(page.pageSize);
    this.onSearchHistory();
  }

  /**
   * get detail merchant
   */
  getDetail(): void {
    if (this.merchantDto.merchantId) {
      this.merchantService.detail(this.merchantDto).subscribe((res: any) => {
        this.merchant = res.body;
        this.feeId = this.merchant.feeId;
        this.configurationFeeType = CONFIGURATION_FEE_TYPE_CONST.DISCOUNT.code + '';

        const data = res.body as IMerchant;
        if (data?.balance) {
          this.money = CommonUtils.moneyFormat(data?.balance.toString());
        }
        if (data.qrCode?.qrCodeValue) {
          this.qrCode = data.qrCode?.qrCodeValue;
        }
        if (this.merchant.merchantId) {
          const startDate = moment()
            .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
            .format(MOMENT_CONST.FORMAT_DEFAULT);
          const endDate = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
          this.formMerchantHistorySearch.controls.transactionDateStart.setValue(
            CommonUtils.reverseDate(startDate)
          );
          this.formMerchantHistorySearch.controls.transactionDateEnd.setValue(
            CommonUtils.reverseDate(endDate)
          );
          this.formMerchantHistorySearch.controls.merchantId.setValue(
            this.merchant.merchantId
          );
          this.onSearchHistory();

          this.formListChildSearch.controls.parentId.setValue(
            this.merchant.merchantId
          );
          this.onSearchListChildMerChant();
        }

        this.initForm(data);
      });
    }
  }

  totalAmount(value = 0, fee = 0): number {
    return +value + +fee;
  }

  /**
   * index on list
   *
   * @param index number
   * @returns number
   */
  fillIndexItemChild(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formListChildSearch.value.pageIndex,
      this.formListChildSearch.value.pageSize
    );
  }

  /**
   * index on list
   *
   * @param index number
   * @returns number
   */
  fillIndexItemHistory(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formMerchantHistorySearch.value.pageIndex,
      this.formMerchantHistorySearch.value.pageSize
    );
  }

  onSearchListChildMerChantSubmit() {
    this.formListChildSearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.formListChildSearch.controls.length.setValue(
      PAGINATION.LENGTH_DEFAULT
    );
    this.onSearchListChildMerChant();
  }

  /**
   * Search List Child MerChant
   */
  onSearchListChildMerChant(): void {
    const body = this.formListChildSearch.value;
    body.timeZoneStr = Intl.DateTimeFormat().resolvedOptions().timeZone;
    this.merchantService.search(body).subscribe((res: any): void => {
      this.merchantChild = res.body.content;
      this.formListChildSearch.controls.length.setValue(res.body.totalElements);
    });
  }

  onSearchHistorySubmit() {
    this.formMerchantHistorySearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.formMerchantHistorySearch.controls.length.setValue(
      PAGINATION.LENGTH_DEFAULT
    );
    this.onSearchHistory();
  }

  /**
   * Search History
   */
  onSearchHistory(): void {
    if (
      this.authenticationService.hasAnyPrivileges(
        SYSTEM_RULES.MASTER_MERCHANT_QUERY_TRANSACTION_HISTORY
      )
    ) {
      const body = this.formMerchantHistorySearch.value;
      const prams = { ...body, keyword: body.keywordMerchant };
      if (this.formMerchantHistorySearch.valid) {
        this.merchantService
          .transactionHistory(prams)
          .subscribe((res: any): void => {
            this.merchantHistory = res.body.content;
            this.merchantHistory.forEach((element) => {
              element.transactionDateStr = moment(
                CommonUtils.reverseDateTimeZone(element.transactionDateStr)
              ).format(MOMENT_CONST.LOCAL_DATE_TIME_FORMAT);
            });
            this.formMerchantHistorySearch.controls.length.setValue(
              res.body.totalElements
            );
          });
      }
    }
  }

  /**
   * load list data master Merchant
   *
   */
  getMasterMerchant(): void {
    this.merchantService
      .searchMaster({
        timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
      })
      .subscribe((res: any): void => {
        this.masterMerchant = res.body.content;
      });
  }

  /**
   * onChange Get Merchant Account Number
   *
   * @param value any
   */
  onChangeGetMerchantAccountNumber(value: any): void {
    this.masterMerchant.forEach((element) => {
      if (element.merchantId === value) {
        this.formMerchant.controls.merchantAccountNumber.setValue(
          element.merchantAccountNumber
        );
      }
    });
  }

  /**
   * export file
   */
  exportFile(): void {
    // get value form control
    const bodySearch = this.formMerchantHistorySearch.getRawValue();
    if (this.formMerchantHistorySearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formMerchantHistorySearch);
    } else {
      // get file name
      const fileName = this.translateService.instant(
        'template.masterMerchantHistory',
        { param: this.merchant.merchantCode }
      );
      const obFile = this.merchantService.export({
        transactionDateStart: bodySearch.transactionDateStart,
        transactionDateEnd: bodySearch.transactionDateEnd,
        keyword: bodySearch.keywordMerchant,
        merchantId: this.merchant.merchantId,
        sourceZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        timeZoneStr: Intl.DateTimeFormat().resolvedOptions().timeZone,
      });

      this.downloadService.downloadFileWithObservableAndName(
        obFile,
        fileName,
        FILE_EXTENSION.XLSX
      );
    }
  }

  exportMerchant(): void {
    const bodySearch = this.formListChildSearch.value;
    const fileName = this.translateService.instant('template.merchant');
    const obFile = this.merchantService.exportMerchant({
      keyword: bodySearch.keyword,
      parentId: this.merchant.merchantId,
    });
    this.downloadService.downloadFileWithObservableAndName(
      obFile,
      fileName,
      FILE_EXTENSION.XLSX
    );
  }

  dateValidator(value: number): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return value > DATE_RANGE_STATISTICAL_LOAN_ONLINE
        ? { invalidDate: true }
        : null;
    };
  }

  changeValidDate() {
    if (
      this.formMerchantHistorySearch.controls.transactionDateStart.value &&
      this.formMerchantHistorySearch.controls.transactionDateEnd.value
    ) {
      if (this.formMerchantHistorySearch.controls['transactionDateEnd'].value) {
        this.maxDate =
          this.formMerchantHistorySearch.controls['transactionDateEnd'].value;
      }
      const startDate = CommonUtils.setStartTime(
        new Date(
          this.formMerchantHistorySearch.controls.transactionDateStart.value
        ).getTime()
      );
      const endDate = CommonUtils.setStartTime(
        new Date(
          this.formMerchantHistorySearch.controls.transactionDateEnd.value
        ).getTime()
      );
      const dayMax =
        (endDate - startDate) / CALCULATE_MILLISECONDS_IN_A_YEAR.DAY;

      if (dayMax > DATE_RANGE_STATISTICAL_LOAN_ONLINE) {
        this.formMerchantHistorySearch.controls.transactionDateStart.setValidators(
          [Validators.required, this.dateValidator(dayMax), this.isValidMaxDate]
        );
        this.formMerchantHistorySearch.controls.transactionDateStart.updateValueAndValidity();
      } else {
        this.formMerchantHistorySearch.controls.transactionDateStart.clearValidators();
        this.formMerchantHistorySearch.controls.transactionDateStart.setValidators(
          [Validators.required, this.isValidMaxDate]
        );
        this.formMerchantHistorySearch.controls.transactionDateStart.updateValueAndValidity();
      }
    }
  }
}
