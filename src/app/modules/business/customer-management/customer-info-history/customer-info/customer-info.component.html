<form [formGroup]="formDetail" *ngIf="customerInfo?.fullname">
  <div class="row border-create">
    <h3>
      {{
        "customerRegisterManagement.customerApproval.request.info" | translate
      }}
    </h3>
    <div class="col-md-3">
      <div class="form-group">
        <label
          >{{ "model.customer.fullname" | translate
          }}<span class="text-danger">*</span></label
        >
        <input
          formControlName="fullname"
          type="text"
          class="w-100"
          class="form-control border-input"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{ "model.customer.cif" | translate }}</label>
        <input
          trim
          formControlName="cif"
          type="text"
          class="w-100"
          class="form-control border-input"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label
          >{{ "model.customer.phoneNumber" | translate
          }}<span class="text-danger">*</span></label
        >
        <input
          formControlName="phoneNumber"
          type="text"
          class="w-100"
          class="form-control border-input"
          placeholder="{{ 'model.customer.phoneNumber' | placeholder }}"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label
          >{{ "model.customer.idCardNumber" | translate
          }}<span class="text-danger">*</span></label
        >
        <input
          formControlName="idCardNumber"
          type="text"
          class="w-100"
          class="form-control border-input"
          placeholder="{{ 'model.customer.idCardNumber' | placeholder }}"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label
          >{{ "model.customer.idCardType" | translate
          }}<span class="text-danger">*</span></label
        >
        <ng-select
          appearance="outline"
          [searchable]="false"
          placeholder="{{ 'common.appSelectOption.idCardType' | translate }}"
          [clearable]="true"
          formControlName="idCardTypeId"
        >
          <ng-option
            [value]="item.idCardTypeId"
            *ngFor="let item of idCardTypes"
          >
            {{ item.name }}
          </ng-option>
        </ng-select>
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label
          >{{ "model.customer.issueDate" | translate
          }}<span class="text-danger">*</span></label
        >
        <!-- <input
          trim
          formControlName="issueDate"
          type="date"
          class="w-100"
          class="form-control border-input"
        /> -->
        <mat-form-field appearance="fill" class="date-picker">
          <input
            matInput
            [matDatepicker]="issueDate"
            formControlName="issueDate"
            placeholder="DD/MM/YYYY"
            dateTransform
          />
          <mat-datepicker-toggle
            matSuffix
            [for]="issueDate"
          ></mat-datepicker-toggle>
          <mat-datepicker #issueDate></mat-datepicker>
        </mat-form-field>
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label
          >{{ "model.customer.issuePlace" | translate
          }}<span class="text-danger">*</span></label
        >
        <input
          formControlName="issuePlace"
          type="text"
          class="w-100"
          class="form-control border-input"
          placeholder="{{ 'model.customer.issuePlace' | placeholder }}"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label
          >{{ "model.customer.dateOfBirth" | translate
          }}<span class="text-danger">*</span></label
        >
        <!-- <input
          formControlName="dateOfBirth"
          type="date"
          class="w-100"
          class="form-control border-input"
        /> -->
        <mat-form-field appearance="fill" class="date-picker">
          <input
            matInput
            [matDatepicker]="dateOfBirth"
            formControlName="dateOfBirth"
            placeholder="DD/MM/YYYY"
            dateTransform
          />
          <mat-datepicker-toggle
            matSuffix
            [for]="dateOfBirth"
          ></mat-datepicker-toggle>
          <mat-datepicker #dateOfBirth></mat-datepicker>
        </mat-form-field>
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label
          >{{ "model.customer.placeOfOrigin" | translate
          }}<span class="text-danger">*</span></label
        >
        <input
          formControlName="placeOfOrigin"
          type="text"
          class="w-100"
          class="form-control border-input"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label
          >{{ "model.customer.gender" | translate
          }}<span class="text-danger">*</span></label
        >
        <ng-select
          [searchable]="false"
          [clearable]="false"
          appearance="outline"
          formControlName="gender"
        >
          <ng-option [value]="item.code" *ngFor="let item of GENDER_LIST">
            {{ item.label | translate }}
          </ng-option>
        </ng-select>
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label
          >{{ "model.customer.nationCode" | translate
          }}<span class="text-danger">*</span></label
        >
        <input
          trim
          formControlName="nationName"
          type="text"
          class="w-100"
          class="form-control border-input"
          placeholder="{{ 'model.customer.nationCode' | placeholder }}"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{ "model.customer.nationCodeOther" | translate }}</label>
        <input
          trim
          formControlName="nationOtherName"
          type="text"
          class="w-100"
          class="form-control border-input"
          placeholder="{{ 'model.customer.nationCodeOther' | placeholder }}"
        />
      </div>
    </div>
    <div class="col-md-6">
      <div class="form-group">
        <label
          >{{ "model.customer.placeOfResidence" | translate
          }}<span class="text-danger">*</span></label
        >
        <input
          formControlName="placeOfResidence"
          type="text"
          class="w-100"
          class="form-control border-input"
          placeholder="{{ 'model.customer.placeOfResidence' | placeholder }}"
        />
      </div>
    </div>
    <div class="col-md-6">
      <div class="form-group">
        <label>{{
          "model.customer.placeOfResidenceOutCountry" | translate
        }}</label>
        <input
          formControlName="placeOfResidenceOutCountry"
          type="text"
          class="w-100"
          class="form-control border-input"
          placeholder="{{
            'model.customer.placeOfResidenceOutCountry' | placeholder
          }}"
        />
      </div>
    </div>
    <div class="col-md-6">
      <div class="form-group">
        <label>{{ "model.customer.currentAddress" | translate }}</label>
        <input
          formControlName="currentAddress"
          type="text"
          class="w-100"
          class="form-control border-input"
          placeholder="{{ 'model.customer.currentAddress' | placeholder }}"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{ "model.customer.phoneContact" | translate }}</label>
        <input
          formControlName="phoneContact"
          type="text"
          class="w-100"
          class="form-control border-input"
          placeholder="{{ 'model.customer.phoneContact' | placeholder }}"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{ "model.customer.email" | translate }}</label>
        <input
          formControlName="email"
          type="text"
          class="w-100"
          class="form-control border-input"
          placeholder="{{ 'model.customer.email' | placeholder }}"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{ "model.customer.residentStatus" | translate }}</label>
        <ng-select
          appearance="outline"
          [searchable]="false"
          [clearable]="false"
          placeholder="{{ 'common.placeholder.residentStatus' | translate }}"
          formControlName="residentStatus"
        >
          <ng-option [value]="item.value" *ngFor="let item of RESIDENT_STATUS">
            {{ item.label | translate }}
          </ng-option>
        </ng-select>
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{ "model.customer.maritalStatus" | translate }}</label>
        <ng-select
          appearance="outline"
          [searchable]="false"
          [clearable]="false"
          placeholder="{{ 'common.placeholder.maritalStatus' | translate }}"
          formControlName="maritalStatus"
        >
          <ng-option [value]="item.value" *ngFor="let item of MARITAL_STATUS">
            {{ item.label | translate }}
          </ng-option>
        </ng-select>
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{ "model.customer.job" | translate }}</label>
        <input
          formControlName="job"
          type="text"
          class="w-100"
          class="form-control border-input"
          placeholder="{{ 'model.customer.job' | placeholder }}"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{ "model.customer.position" | translate }}</label>
        <input
          formControlName="position"
          type="text"
          class="w-100"
          class="form-control border-input"
          placeholder="{{ 'model.customer.position' | placeholder }}"
        />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>{{ "model.customer.workplace" | translate }}</label>
        <input
          formControlName="workplace"
          type="text"
          class="w-100"
          class="form-control border-input"
          placeholder="{{ 'model.customer.workplace' | placeholder }}"
        />
      </div>
    </div>
    <div class="col-md-3" *ngIf="action !== ROUTER_ACTIONS.update">
      <div class="form-group">
        <label>{{ "model.customer.premiumAccountNumber" | translate }}</label>
        <input
          formControlName="premiumAccountNumber"
          type="text"
          class="w-100"
          class="form-control border-input"
          placeholder="{{
            'model.customer.premiumAccountNumber' | placeholder
          }}"
        />
      </div>
    </div>
    <div class="col-md-3" *ngIf="action !== ROUTER_ACTIONS.update">
      <div class="form-group">
        <label>{{ "model.customer.paymentPrice" | translate }}</label>
        <input
          formControlName="paymentPrice"
          type="text"
          class="w-100"
          class="form-control border-input bold-text"
          placeholder="{{ 'model.customer.paymentPrice' | placeholder }}"
        />
      </div>
    </div>
    <!-- <div class="col-md-3">
          <div class="form-group">
            <label>{{ "model.customer.companyID" | translate }}</label>
            <input trim
              formControlName="companyID"
              type="text"
              class="w-100"
              class="form-control"
            />
          </div>
        </div> -->
    <div class="col-md-12 image">
      <div class="row">
        <div class="col-md-6">
          <label>{{ "model.customer.identityCardImages" | translate }}</label>
          <app-upload-single-images
            [fileRequired]="fileRequired"
            [checkDisabled]="true"
            [imageUrls]="imageUrls"
            [type]="ROUTER_ACTIONS.detail"
          ></app-upload-single-images>
        </div>
        <div class="col-md-6">
          <label>{{ "model.customer.signatureImage" | translate }}</label>
          <app-upload-single-images
            [fileRequired]="fileRequired"
            [checkDisabled]="true"
            [imageUrls]="signatureImageUrls"
            [type]="ROUTER_ACTIONS.detail"
          ></app-upload-single-images>
        </div>
      </div>
    </div>
    <div class="col-md-12">
      <label>{{ "model.customer.ekycImages" | translate }}</label>
      <app-upload-images
        [fileRequired]="fileRequired"
        [checkDisabled]="true"
        [imageUrls]="ekycUrls"
        [type]="ROUTER_ACTIONS.detail"
      ></app-upload-images>
    </div>
    <div class="col-md-12">
      <div class="row">
        <div class="col-md-3">
          <div class="form-group">
            <label>{{ "model.customer.customerSectorId" | translate }}</label>
            <span [ngbTooltip]="formDetail.value.customerSectorIdStr"
              ><input
                trim
                formControlName="customerSectorId"
                type="text"
                class="w-100 form-control border-input"
            /></span>
            <!-- <ng-select appearance="outline" [searchable]="false" [clearable]="false" placeholder="{{
                'model.customer.customerSectorId' | translate
              }}" formControlName="customerSectorId">
              <ng-option [value]="item.value" *ngFor="let item of CUSTOMER_SECTOR_ID">
                {{ item.label | translate }}
              </ng-option>
            </ng-select> -->
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label>{{ "customerRegisterManagement.sector" | translate }}</label>
            <input
              trim
              formControlName="customerSectorIdCode"
              type="text"
              class="w-100"
              class="form-control border-input"
            />
          </div>
        </div>
        <div class="col-md-3" *ngIf="ekycUrls.length">
          <div class="form-group">
            <label>{{
              "model.customer.createdDateAccountT24" | translate
            }}</label>
            <input
              trim
              formControlName="createdDateAccountT24"
              type="text"
              class="w-100"
              class="form-control border-input"
            />
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label>{{ "model.customer.oldSector" | translate }}</label>
            <input
              trim
              formControlName="customerOldSectorId"
              type="text"
              class="w-100"
              class="form-control border-input"
            />
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label>{{ "model.customer.staffCode" | translate }}</label>
            <input
              trim
              formControlName="staffCode"
              type="text"
              class="w-100"
              class="form-control border-input"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="row border-create" *ngIf="action === ROUTER_ACTIONS.detail">
    <h3>
      {{ "model.referral.referrerInformation" | translate }}
    </h3>
    <div class="col-md-12">
      <div class="row">
        <div class="col-md-3">
          <div class="form-group">
            <label>{{ "model.referral.referralCode" | translate }}</label>
            <span
              ><input
                trim
                formControlName="referralCode"
                type="text"
                class="w-100 form-control border-input"
            /></span>
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label>{{ "model.referral.referrerName" | translate }}</label>
            <input
              trim
              formControlName="userFullNameReferral"
              type="text"
              class="w-100"
              class="form-control border-input"
            />
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label>{{
              "model.referral.referrerPhoneNumber" | translate
            }}</label>
            <input
              trim
              formControlName="phoneNumberReferral"
              type="text"
              class="w-100"
              class="form-control border-input"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
