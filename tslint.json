{
  "rulesDirectory": ["node_modules/codelyzer"],
  "extends": ["tslint:latest", "tslint-config-prettier", "tslint:recommended"],
  "rules": {
    "align": {
      "options": ["parameters", "statements"]
    },
    "prefer-conditional-expression": [false, "check-else-if"],
    "no-submodule-imports": false,
    "no-implicit-dependencies": false,
    "class-name": true,
    "comment-format": [true, "check-space"],
    "curly": true,
    "eofline": true,
    "forin": true,
    "indent": [true, "spaces"],
    "label-position": true,
    "member-access": false,
    // "member-ordering": [true, "static-before-instance", "variables-before-functions"],
    "no-arg": true,
    "no-bitwise": true,
    "no-console": [true, "debug", "info", "time", "timeEnd", "trace"],
    "no-construct": true,
    "no-debugger": true,
    "no-duplicate-variable": true,
    "no-empty": false,
    "no-eval": true,
    "no-inferrable-types": [true],
    "no-shadowed-variable": true,
    "no-string-literal": false,
    "no-switch-case-fall-through": true,
    "no-trailing-whitespace": true,
    "no-unused-expression": true,
    "no-var-keyword": true,
    "object-literal-sort-keys": false,
    "one-line": [
      true,
      "check-open-brace",
      "check-catch",
      "check-else",
      "check-whitespace"
    ],
    "quotemark": [true, "single", "avoid-escape"],
    "radix": true,
    "semicolon": [true, "always", "ignore-bound-class-methods"],
    "triple-equals": [true, "allow-null-check"],
    "typedef-whitespace": [
      true,
      {
        "call-signature": "nospace",
        "index-signature": "nospace",
        "parameter": "nospace",
        "property-declaration": "nospace",
        "variable-declaration": "nospace"
      }
    ],
    "variable-name": false,
    "whitespace": [
      true,
      "check-branch",
      "check-decl",
      "check-operator",
      "check-separator",
      "check-type"
    ],
    "prefer-const": true,
    "arrow-parens": [false, "ban-single-arg-parens"],
    "arrow-return-shorthand": [true],
    "import-spacing": true,
    "no-consecutive-blank-lines": [true],
    "object-literal-shorthand": true,
    "space-before-function-paren": [
      true,
      {
        "asyncArrow": "always",
        "anonymous": "never",
        "constructor": "never",
        "method": "never",
        "named": "never"
      }
    ],
    "directive-selector": [true, "attribute", "camelCase"],
    "component-selector": [true, "element", "kebab-case"],
    "no-inputs-metadata-property": true,
    "no-outputs-metadata-property": true,
    "no-host-metadata-property": true,
    "no-input-rename": true,
    "no-output-rename": true,
    "use-lifecycle-interface": true,
    "use-pipe-transform-interface": false,
    "component-class-suffix": true,
    "directive-class-suffix": true,
    "max-classes-per-file": false
  }
}
