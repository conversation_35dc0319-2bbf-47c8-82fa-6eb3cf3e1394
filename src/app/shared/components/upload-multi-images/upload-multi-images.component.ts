import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { ModalDeleteBannerComponent } from '@business/campaign-management/modal-delete-banner/modal-delete-banner.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import {
  MAX_FILE_SIZE_CONST,
  MODAL_ACTION,
  SCREEN_SHOW_BANNER,
} from '@shared/constants/app.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IImageUrls } from '@shared/models/request/image-type.search';
import { LoadingService } from '@shared/services/helpers/loading.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ImageViewerService } from '@shared/services/image-viewer.service';
import { ROUTER_ACTIONS } from '@shared/utils';
import { NzImagePreviewRef } from 'ng-zorro-antd/image';
import { NzUploadFile } from 'ng-zorro-antd/upload';
import { of } from 'rxjs';

export type Image = {
  src: string | null;
  name: string;
  file?: File;
};

@Component({
  selector: 'app-upload-multi-images',
  templateUrl: './upload-multi-images.component.html',
  styleUrls: ['./upload-multi-images.component.scss'],
})
export class UploadMultiImagesComponent implements OnInit, OnChanges {
  formImageGroup = new FormGroup({});

  isEnrtyId = false;

  @Input() fileExtension?: string[] = [];
  @Input() bannerList: IImageUrls[] = [];
  @Input() action?: string;
  @Input() position?: string;

  @Output() imageUploaded: EventEmitter<any> = new EventEmitter();
  @Output() imageRemoved: EventEmitter<any> = new EventEmitter();
  @Output() imageRemain: EventEmitter<any> = new EventEmitter();
  @Output() addImage: EventEmitter<any> = new EventEmitter();

  @ViewChild(NzImagePreviewRef) imagePreviewRef!: NzImagePreviewRef;

  SCREEN_SHOW_BANNER = SCREEN_SHOW_BANNER;
  ROUTER_ACTIONS = ROUTER_ACTIONS;

  constructor(
    private imagePreviewService: ImageViewerService,
    private toast: ToastrCustomService,
    private fb: FormBuilder,
    private ngbModal: NgbModal,
    public loadingService: LoadingService
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (this.bannerList.length > 1) {
      this.formImageGroup = this.fb.group({
        linkImage: this.fb.array([...this.createLinkFormGroups()]),
      });
    } else {
      this.formImageGroup = this.fb.group({
        linkImage: this.fb.array([this.createLinkFormGroup()]),
      });
    }
  }

  ngOnInit(): void {
    if (this.bannerList.length > 0) {
      for (let index = 0; index < this.bannerList.length; index++) {
        this.getLinkGroupControl()
          .controls[index].get('src')
          ?.setValue(`${this.bannerList[index].src}`);
        this.getLinkGroupControl()
          .controls[index].get('file')
          ?.setValue(`${this.bannerList[index].entryId}`);
        this.getLinkGroupControl()
          .controls[index].get('embedLink')
          ?.setValue(`${this.bannerList[index].embedLink}`);
        this.getLinkGroupControl()
          .controls[index].get('classPk')
          ?.setValue(`${this.bannerList[index].classPk}`);
        this.getLinkGroupControl()
          .controls[index].get('entryId')
          ?.setValue(`${this.bannerList[index].entryId}`);
        this.getLinkGroupControl()
          .controls[index].get('id')
          ?.setValue(`${this.bannerList[index].entryId}`);
      }
      for (const item of this.getLinkGroupControl().controls) {
        item.get('embedLink')?.enable();
      }
    }
  }

  getItem(i: number) {
    if (this.getLinkGroupControl().controls[i].value.entryId) {
      this.isEnrtyId = true;
    } else {
      this.isEnrtyId = false;
    }
  }

  private createLinkFormGroup(): FormGroup {
    return new FormGroup({
      embedLink: new FormControl('', [
        Validators.pattern(VALIDATORS.PATTERN.HTTP_PATTERN),
      ]),
      src: new FormControl(''),
      name: new FormControl(''),
      file: new FormControl(''),
      classPk: new FormControl(''),
      entryId: new FormControl(''),
      index: new FormControl(''),
      id: new FormControl(''),
    });
  }

  private createLinkFormGroups(): FormGroup[] {
    const arr: any = [];
    // tslint:disable-next-line:prefer-for-of
    for (let i = 0; i < this.bannerList?.length; i++) {
      arr.push(this.createLinkFormGroup());
    }
    return arr;
  }
  public getLinkGroupControl(): FormArray {
    return this.formImageGroup.get('linkImage') as FormArray;
  }

  // for test
  beforeUpload = (file: NzUploadFile, _fileList: NzUploadFile[]): boolean => {
    const isLessThan = file.size! < MAX_FILE_SIZE_CONST['5MB'].BYTE;
    if (!isLessThan) {
      this.toast.error({
        message: 'common.maxFileSize',
        params: { size: MAX_FILE_SIZE_CONST['5MB'].MB },
      });
      return false;
    }
    if (!this.fileExtension?.includes(file?.type || '')) {
      this.toast.error({
        message: 'common.fileUploadInvalid',
      });
      return false;
    }
    return true;
  };

  handlePreview = (event: any, index: number) => {
    event.stopPropagation();
    const rs: string[] = [];

    rs.push(this.getLinkGroupControl()?.controls[index]?.value.src);

    this.imagePreviewService.previewImages(rs);
  };

  onRemovePic(itemControl: AbstractControl, index: number) {
    const formArray = [...this.getLinkGroupControl().controls];
    if (itemControl.value.entryId) {
      const bannersId: number[] = [];

      const modalRef = this.ngbModal.open(ModalDeleteBannerComponent, {
        size: 'sm',
        keyboard: true,
        centered: true,
        backdrop: 'static',
      });
      modalRef.componentInstance.item = { ...itemControl.value };
      modalRef.result.then((res) => {
        if (res === MODAL_ACTION.CONFIRM.code) {
          this.imageRemain.emit(itemControl.value);
          this.getLinkGroupControl().controls[index].get('src')?.reset();
          this.getLinkGroupControl().controls[index].get('entryId')?.reset();
          this.getLinkGroupControl().controls[index].get('file')?.reset();
          this.getLinkGroupControl()
            .controls[index].get('embedLink')
            ?.clearValidators();
          this.getLinkGroupControl()
            .controls[index].get('embedLink')
            ?.updateValueAndValidity();
          this.isEnrtyId = false;
          for (const item of this.getLinkGroupControl().controls) {
            bannersId.push(item.value);
          }
          if (this.getLinkGroupControl().controls.length === 0) {
            this.getLinkGroupControl().controls.push(
              this.createLinkFormGroup()
            );
          }
          this.imageRemoved.emit(bannersId);
        }
      });
      return;
    } else {
      if (this.getLinkGroupControl().controls[index].get('file')?.value) {
        this.getLinkGroupControl().controls[index].get('file')?.reset();
        this.getLinkGroupControl().controls[index].get('src')?.reset();
        this.getLinkGroupControl()
          .controls[index].get('embedLink')
          ?.clearValidators();
        this.getLinkGroupControl()
          .controls[index].get('embedLink')
          ?.updateValueAndValidity();
        const bannerHasFile = [];
        for (const item of formArray) {
          bannerHasFile.push(item.value);
        }
        if (this.getLinkGroupControl().controls.length === 0) {
          this.getLinkGroupControl().controls.push(this.createLinkFormGroup());
        }

        this.imageRemoved.emit(bannerHasFile);
        return;
      }
      if (!this.getLinkGroupControl().controls[index].get('file')?.value) {
        this.getLinkGroupControl()
          .controls[index].get('embedLink')
          ?.clearValidators();
        this.getLinkGroupControl()
          .controls[index].get('embedLink')
          ?.updateValueAndValidity();
        this.getLinkGroupControl().controls.splice(index, 1);
        const bannerHasFile = [];
        for (const item of this.getLinkGroupControl().controls) {
          bannerHasFile.push(item.value);
        }
        if (this.getLinkGroupControl().controls.length === 0) {
          this.getLinkGroupControl().controls.push(this.createLinkFormGroup());
        }
        this.imageRemoved.emit(bannerHasFile);
        return;
      }
    }
  }

  // for test
  handleChange(info: { file: NzUploadFile }, index: number): void {
    const fileReader: FileReader = new FileReader();
    fileReader.onload = (ev: ProgressEvent<FileReader>) => {
      const rs = ev.target?.result ? (ev.target.result as string) : '';
      // tslint:disable-next-line:prefer-for-of
      for (let i = 0; i < this.getLinkGroupControl().controls.length; i++) {
        if (
          i === index &&
          !this.getLinkGroupControl().controls[i].get('src')?.value
        ) {
          this.getLinkGroupControl().controls[i].get('src')?.setValue(rs);
          this.getLinkGroupControl()
            .controls[i].get('file')
            ?.setValue(info.file.originFileObj);
          this.getLinkGroupControl().controls[i].get('index')?.setValue(index);
          this.getLinkGroupControl().controls[i].get('embedLink')?.enable();
          this.getLinkGroupControl()
            .controls[i].get('embedLink')
            ?.setValidators([
              Validators.required,
              Validators.pattern(VALIDATORS.PATTERN.HTTP_PATTERN),
            ]);
          this.getLinkGroupControl()
            .controls[i].get('embedLink')
            ?.updateValueAndValidity();
          if (
            this.position === this.SCREEN_SHOW_BANNER.LOGIN.label &&
            this.position !== this.SCREEN_SHOW_BANNER.HOMEPAGE.label &&
            this.position !== this.SCREEN_SHOW_BANNER.OTHER.label
          ) {
            this.imageUploaded.emit(this.getLinkGroupControl().controls);
            return;
          }
          if (
            this.position !== this.SCREEN_SHOW_BANNER.LOGIN.label &&
            (this.position === this.SCREEN_SHOW_BANNER.HOMEPAGE.label ||
              this.position === this.SCREEN_SHOW_BANNER.OTHER.label)
          ) {
            if (this.getLinkGroupControl().controls.length <= 5) {
              this.imageUploaded.emit(this.getLinkGroupControl().controls);
              // this.getLinkGroupControl().push(this.createLinkFormGroup());
            } else {
              for (const item of this.getLinkGroupControl().controls) {
                this.imageUploaded.emit(item.value);
              }
            }
            return;
          }
        }

        if (
          i === index &&
          this.getLinkGroupControl().controls[i].get('src')?.value
        ) {
          this.getLinkGroupControl().controls[i].get('src')?.setValue(rs);
          this.getLinkGroupControl()
            .controls[i].get('embedLink')
            ?.setValidators([
              Validators.required,
              Validators.pattern(VALIDATORS.PATTERN.HTTP_PATTERN),
            ]);
          this.getLinkGroupControl()
            .controls[i].get('embedLink')
            ?.updateValueAndValidity();
          this.getLinkGroupControl()
            .controls[i].get('file')
            ?.setValue(info.file.originFileObj);
          this.imageUploaded.emit(this.getLinkGroupControl().controls);
        }
      }
    };
    if (info.file.originFileObj) {
      fileReader.readAsDataURL(info.file.originFileObj);
    } else {
      fileReader.readAsDataURL(info.file as any);
    }
  }

  getEmbedLink(i: number) {
    for (
      let index = 0;
      index < this.getLinkGroupControl().controls.length;
      index++
    ) {
      if (
        index === i &&
        this.getLinkGroupControl().controls[i]?.get('src')?.value
      ) {
        this.getLinkGroupControl()
          .controls[i].get('embedLink')
          ?.setValidators([
            Validators.required,
            Validators.pattern(VALIDATORS.PATTERN.HTTP_PATTERN),
          ]);
        this.getLinkGroupControl()
          .controls[i].get('embedLink')
          ?.updateValueAndValidity();
      }
      this.imageUploaded.emit(this.getLinkGroupControl().controls);
    }
  }

  addFormImage() {
    if (this.getLinkGroupControl().controls.length < 5) {
      this.getLinkGroupControl().push(this.createLinkFormGroup());

      this.addImage.emit(this.getLinkGroupControl().controls);
    }
    if (this.getLinkGroupControl().controls.length === 5) {
      return;
    }
  }

  handleCustomUploadReq(item: any) {
    return of(item).subscribe(
      (next) => {},
      (err) => {
        console.log(err);
      }
    );
  }
}
