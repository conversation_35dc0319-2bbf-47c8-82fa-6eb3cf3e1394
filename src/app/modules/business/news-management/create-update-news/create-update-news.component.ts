import { Component, OnInit, ViewChild } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { EditorComponent } from '@shared/components/editor/editor.component';
import {
  FILE_UPLOAD_EXTENSIONS,
  MODAL_ACTION,
  NEWSFILE_TYPE,
} from '@shared/constants/app.constants';
import { LANGUAGES } from '@shared/constants/language.constants';
import { STORAGE_LANGUAGES } from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ICategory } from '@shared/models/category.model';
import { INews } from '@shared/models/news.model';
import { IFileEntrySearch } from '@shared/models/request/file-entry.search';
import { CategoryService } from '@shared/services/category.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { IconService } from '@shared/services/icon.service';
import { NewsService } from '@shared/services/news.service';
import CommonUtils from '@shared/utils/common-utils';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils/router.utils';
import { LocalStorageService } from 'ngx-webstorage';
@Component({
  selector: 'app-create-update-news',
  templateUrl: './create-update-news.component.html',
  styleUrls: ['./create-update-news.component.scss'],
})
export class CreateNewsComponent implements OnInit {
  maxLengthContent = VALIDATORS.LENGTH.CONTENT_NEWS;
  formCreate: FormGroup = new FormGroup({});
  fileUploads: any[] = [];
  fileRequired: string[] = ['Image'];
  fileDelete: any[] = [];
  imageUrls: any[] = [];
  ROUTER_UTILS = ROUTER_UTILS;
  VALIDATORS = VALIDATORS;
  FILE_UPLOAD_EXTENSIONS = FILE_UPLOAD_EXTENSIONS;
  NEWSFILE_TYPE = NEWSFILE_TYPE;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  STORAGE_LANGUAGES = STORAGE_LANGUAGES;
  LANGUAGES = Object.values(LANGUAGES);
  language = '';
  newsId?: number;
  action?: string;
  data: INews = [];
  categoryList?: ICategory[] = [];
  @ViewChild('content') content: EditorComponent | any;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private newsService: NewsService,
    private localStorage: LocalStorageService,
    private toastService: ToastrCustomService,
    private iconService: IconService,
    private categoryService: CategoryService,
    private modalService: ModalService
  ) {
    this.activatedRoute.paramMap.subscribe((res) => {
      const idParam = res.get('newsId');
      if (idParam) {
        this.newsId = +idParam;
      }
    });
    this.activatedRoute.data.subscribe((res) => {
      this.action = res.action;
    });
    this.language =
      this.router?.getCurrentNavigation()?.extras.state?.language ||
      this.localStorage.retrieve(STORAGE_LANGUAGES);
  }

  ngOnInit(): void {
    if (this.newsId) {
      this.newsService.detail({ newsId: this.newsId }).subscribe((res: any) => {
        this.data = res?.body;
        this.getImages(res?.body?.newsContents);
      });
    } else {
      this.initForm();
    }
    this.getCategory();
  }

  getCategory(): void {
    this.categoryService.category().subscribe((res: any): void => {
      this.categoryList = res?.body;
    });
  }

  getData(data?: any) {
    return data?.find((item: INews) => item.language === this.language);
  }

  getImages(data?: any) {
    if (this.getData(data)) {
      const searchFile: IFileEntrySearch = {};
      searchFile.classPk = this.getData(data).icon?.classPk;
      searchFile.fileEntryId = this.getData(data).icon?.id;
      searchFile.normalizeName = this.getData(data).icon?.normalizeName;
      searchFile.className = NEWSFILE_TYPE.CLASSNAME;
      searchFile.resource = NEWSFILE_TYPE.RESOURCE;
      this.iconService.getIcon(searchFile).subscribe((responsive: any) => {
        CommonUtils.blobToBase64(responsive.body).then((base64) => {
          this.imageUrls = [
            { src: base64, name: null, id: this.getData(data)?.icon?.id },
          ];
        });
      });
    }
    this.initForm(this.getData(data));
  }

  initForm(data?: INews): void {
    this.formCreate = this.formBuilder.group({
      title: [
        {
          value: data?.title || '',
          disabled: this.action === ROUTER_ACTIONS.detail ? true : false,
        },
        [
          Validators.required,
          Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
        ],
      ],
      content: [
        data?.content || '',
        [
          Validators.required,
          Validators.maxLength(VALIDATORS.LENGTH.CONTENT_NEWS),
        ],
      ],
      categoryId: [
        {
          value: this.data?.categoryId || null,
          disabled: this.action === ROUTER_ACTIONS.detail ? true : false,
        },
        [Validators.required],
      ],
    });
    this.content?.setValue(data?.content);
  }

  onChangeData(type: string, content: any): void {
    this.formCreate.get(type)?.setValue(content);
  }

  onChangeLang(language: string) {
    if (
      this.action === ROUTER_ACTIONS.create &&
      (this.formCreate.value.title ||
        this.formCreate.value.categoryId ||
        this.formCreate.value.content ||
        this.fileUploads.length > 0) &&
      language !== this.language
    ) {
      const modalData = {
        title: 'model.news.modal.title',
        content: 'model.news.modal.content',
      };
      this.modalService.confirm(modalData).then((result) => {
        if (result === MODAL_ACTION.CONFIRM.code) {
          this.getDataLang(language);
        }
      });
    } else if (language !== this.language) {
      this.getDataLang(language);
    }
  }

  getDataLang(language: string) {
    this.language = language;
    this.formCreate.reset();
    this.fileUploads = [];
    this.imageUrls = [];
    this.getImages(this.data?.newsContents);
  }

  backToList(): void {
    this.router.navigate([ROUTER_UTILS.news.root]);
  }

  onUploadPics(fileSelected: any): void {
    this.fileUploads = fileSelected;
  }

  onRemoveImage(image: any): void {
    this.fileDelete.push(image.id);
  }

  onSubmit() {
    if (this.formCreate.invalid) {
      CommonUtils.markFormGroupTouched(this.formCreate);
      return;
    }
    const body = this.formCreate.value;
    body.files = this.fileUploads.map((fileX) => fileX.file);
    body.fileId =
      (this.fileUploads[0]?.src === null && this.fileDelete.length > 0) ||
      (this.fileUploads.length === 0 && this.fileDelete.length > 0)
        ? this.getData(this.data?.newsContents)?.icon?.id
        : '';
    body.language = this.language;
    body.newsId = this.newsId ? this.newsId : '';
    body.newsContentId =
      this.getData(this.data?.newsContents)?.newsContentId || '';
    const serviceEvent =
      this.action === ROUTER_ACTIONS.update
        ? this.newsService.update(body)
        : this.newsService.create(body);
    serviceEvent.subscribe((res: any) => {
      if (res) {
        this.action === ROUTER_ACTIONS.create
          ? this.router.navigate(
              [
                ROUTER_UTILS.news.root,
                res?.body?.newsId,
                ROUTER_ACTIONS.update,
              ],
              {
                state: {
                  language: this.language,
                },
              }
            )
          : this.router.navigate([ROUTER_UTILS.news.root]);
      }
      this.toastService.success(
        this.action === ROUTER_ACTIONS.update
          ? 'common.action.updateSuccess'
          : 'common.action.createSuccess'
      );
    });
  }

  getTemplate(data: string) {
    this.formCreate.patchValue({
      content: data.split(VALIDATORS.PATTERN.SPACE_HTML).join(' '),
    });
  }
}
