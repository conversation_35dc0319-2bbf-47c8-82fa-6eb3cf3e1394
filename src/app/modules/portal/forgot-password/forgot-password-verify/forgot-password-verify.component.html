<!-- <div *ngIf="this.activeToken.token">
  <p *ngIf="!activeToken.user?.active && !expire24Hour">
    active-user works! check mail send user and password
  </p>
  <p *ngIf="!active && !expire24Hour">
    active-user works! check mail send user and password
  </p>
  <p *ngIf="activeToken.user?.active && !expire24Hour">
    user already is active
  </p>
  <p *ngIf="active && !expire24Hour">user already is active</p>
  <div class="d-block text-center mb-3 mt-4" *ngIf="expire24Hour && !active">
    <button class="btn btn-white" (click)="onReSend()">
      {{ "public.resend" | translate }}
    </button>
  </div>
</div> -->

<main class="bg-login d-flex align-items-center min-vh-100 py-3 py-md-0">
  <app-change-language></app-change-language>
  <div class="wrap-login">
    <div class="login-card">
      <div class="row no-gutters">
        <div class="col-lg-12 col-md-12">
          <div class="card-body">
            <div class="brand-wrapper text-center">
              <img src="../../assets/dist/img/mblogo.png" alt="logo login" />
            </div>
            <div
              *ngIf="
                (action === ACTION_TOKEN_CONST.ACTIVATED ||
                  action === ACTION_TOKEN_CONST.SEND_LINK_ACTIVATE) &&
                !hidevalue
              "
            >
              <div class="text-center">
                <div
                  class="title"
                  [innerHTML]="
                    displayContent('public.forgotPassword.success.title')
                  "
                ></div>
              </div>
            </div>
            <div class="text-center mt-5" *ngIf="maxtime">
              <h3 *ngIf="!hidevalue">{{ timer | formatTime }}</h3>
            </div>
            <div
              *ngIf="
                action === ACTION_TOKEN_CONST.RESEND_LINK_ACTIVATE || hidevalue
              "
            >
              <div class="text-center">
                <div
                  class="content-size"
                  [innerHTML]="displayContent('public.titleResendPw')"
                ></div>
              </div>
            </div>
            <div
              class="text-center mt-5"
              *ngIf="
                action === ACTION_TOKEN_CONST.RESEND_LINK_ACTIVATE || hidevalue
              "
            >
              <div>
                <a class="title-link" (click)="onResend()">{{
                  "public.resendLink" | translate
                }}</a>
              </div>
            </div>
            <div
              class="text-center mt-5"
              *ngIf="
                (action === ACTION_TOKEN_CONST.ACTIVATED ||
                  action === ACTION_TOKEN_CONST.SEND_LINK_ACTIVATE) &&
                !hidevalue
              "
            >
              <div>
                <a class="title-link" disabled>{{
                  "public.resendLink" | translate
                }}</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>
