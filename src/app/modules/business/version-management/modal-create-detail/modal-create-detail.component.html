<div class="modal-content">
  <div class="modal-header">
    <h5 class="modal-title" *ngIf="action !== ROUTER_ACTION.update">
      {{
        action === ROUTER_ACTION.detail
          ? operate === TYPE_OPERATE_SYSTEM.ANDROID.value
            ? ("model.versionManage.detailAndroid" | translate)
            : ("model.versionManage.detailIos" | translate)
          : operate === TYPE_OPERATE_SYSTEM.IOS.value
          ? ("model.versionManage.createIos" | translate)
          : ("model.versionManage.createAndroid" | translate)
      }}
    </h5>
    <h5 class="modal-title" *ngIf="action === ROUTER_ACTION.update">
      {{
        operate === TYPE_OPERATE_SYSTEM.ANDROID.value
          ? ("model.versionManage.updateAndroid" | translate)
          : ("model.versionManage.updateIos" | translate)
      }}
    </h5>
    <button
      type="button"
      class="close"
      data-dismiss="modal"
      aria-label="Close"
      (click)="activeModal.close()"
    >
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="formVersion">
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label
                >{{ "model.versionManage.form.versionCode" | translate
                }}<span class="text-danger small-text"> *</span></label
              >
              <input
                placeholder="{{
                  'model.versionManage.placeholder.versionCode' | translate
                }}"
                formControlName="code"
                type="text"
                class="w-100 form-control"
                [maxLength]="VALIDATORS.LENGTH.VERSION_CODE_LENGTH"
              />
              <span
                *ngIf="
                  formVersion.get('code')?.errors?.required &&
                  formVersion.get('code')?.touched
                "
                class="text-danger small-text"
                >{{
                  "error.versionManage.required.versionCode" | translate
                }}</span
              >
              <span
                *ngIf="
                  formVersion.get('code')?.errors?.pattern &&
                  formVersion.get('code')?.touched
                "
                class="text-danger small-text"
                >{{
                  "error.versionManage.pattern.versionCode" | translate
                }}</span
              >
              <span
                *ngIf="
                  formVersion.get('code')?.errors?.maxLength &&
                  formVersion.get('code')?.touched
                "
                class="text-danger small-text"
                >{{
                  "error.versionManage.length.versionCode" | translate
                }}</span
              >
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label
                >{{ "model.versionManage.form.versionName" | translate
                }}<span class="text-danger small-text"> *</span></label
              >
              <input
                placeholder="{{
                  'model.versionManage.placeholder.versionName' | translate
                }}"
                formControlName="name"
                type="text"
                class="w-100 form-control"
                [maxlength]="VALIDATORS.LENGTH.VERSION_NAME_LENGTH"
              />
              <span
                *ngIf="
                  formVersion.get('name')?.errors?.required &&
                  formVersion.get('name')?.touched
                "
                class="text-danger small-text"
                >{{
                  "error.versionManage.required.versionName" | translate
                }}</span
              >
              <span
                *ngIf="
                  formVersion.get('name')?.errors?.pattern &&
                  formVersion.get('name')?.touched
                "
                class="text-danger small-text"
                >{{
                  "error.versionManage.pattern.versionName" | translate
                }}</span
              >
              <span
                *ngIf="
                  formVersion.get('name')?.errors?.maxLength &&
                  formVersion.get('name')?.touched
                "
                class="text-danger small-text"
                >{{
                  "error.versionManage.length.versionName" | translate
                }}</span
              >
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-12">
            <div class="form-group">
              <label
                >{{ "model.versionManage.form.versionContent" | translate
                }}<span class="text-danger"> *</span></label
              >
              <textarea
                class="w-100 form-control"
                rows="4"
                cols="50"
                type="text"
                formControlName="description"
                [maxLength]="VALIDATORS.LENGTH.VERSION_CONTENT"
                [placeholder]="
                  'model.versionManage.placeholder.versionContent' | translate
                "
              ></textarea>
              <span
                *ngIf="
                  formVersion.get('description')?.errors?.required &&
                  formVersion.get('description')?.touched
                "
                class="text-danger"
                >{{
                  "error.versionManage.required.contentUpdate" | translate
                }}</span
              >
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-4 checkbox-version">
                <div class="form-check">
                  <input
                    class="form-check-input"
                    type="checkbox"
                    id="forceUpdate"
                    formControlName="forceUpdate"
                    (change)="checkCheckForceUpdate($event)"
                  />
                  <label class="form-check-label" for="forceUpdate">
                    {{ "model.versionManage.form.forceUpdate" | translate }}
                  </label>
                </div>
              </div>
              <div class="col-md-4 checkbox-version">
                <div class="form-check">
                  <input
                    class="form-check-input"
                    type="checkbox"
                    id="manualUpdate"
                    formControlName="manualUpdate"
                    (change)="checkCheckManualUpdate($event)"
                  />
                  <label class="form-check-label" for="manualUpdate">
                    {{ "model.versionManage.form.manualUpdate" | translate }}
                  </label>
                </div>
              </div>
              <div class="col-md-4 checkbox-version">
                <div class="form-check">
                  <input
                    class="form-check-input"
                    type="checkbox"
                    id="forceNotification"
                    formControlName="forceNotification"
                    (change)="checkCheckforceNotification($event)"
                  />
                  <label class="form-check-label" for="forceNotification">
                    {{
                      "model.versionManage.form.forceNotification" | translate
                    }}
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-12 mt-2">
        <div class="row">
          <div class="col-md-12">
            <div class="form-group">
              <label
                >{{ "model.versionManage.form.urlStore" | translate
                }}<span class="text-danger small-text"> *</span></label
              >
              <input
                type="text"
                class="w-100 form-control"
                formControlName="urlStore"
                placeholder="{{
                  'model.versionManage.placeholder.urlStore' | translate
                }}"
              />
              <span
                *ngIf="
                  formVersion?.get('urlStore')?.errors?.required &&
                  formVersion?.get('urlStore')?.touched
                "
                class="text-danger"
                >{{ "error.versionManage.required.urlStore" | translate }}</span
              >
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label class=""
                >{{ "model.versionManage.form.releaseDate" | translate
                }}<span class="text-danger small-text"> *</span></label
              >
              <mat-form-field appearance="fill" class="date-picker">
                <input
                  matInput
                  formControlName="releaseDate"
                  [ngxMatDatetimePicker]="picker"
                  [placeholder]="MOMENT_CONST.FORMAT_DATE_TIME_PLACEHOLDER"
                  [min]="minDate | date : MOMENT_CONST.FORMAT_DATE_MAT_PICKER"
                  readonly
                />
                <mat-datepicker-toggle
                  matSuffix
                  [for]="$any(picker)"
                ></mat-datepicker-toggle>
                <ngx-mat-datetime-picker #picker> </ngx-mat-datetime-picker>
              </mat-form-field>
              <small
                class="text-danger"
                *ngIf="
                  formVersion?.get('releaseDate')?.errors?.required &&
                  formVersion?.get('releaseDate')?.touched
                "
                >{{
                  "error.versionManage.required.releaseDate" | translate
                }}</small
              >
              <small
                class="text-danger"
                *ngIf="
                  formVersion?.get('releaseDate')?.errors?.invalidCreateTime &&
                  formVersion?.get('releaseDate')?.touched
                "
                >{{
                  "error.versionManage.dateTime.invalidHours" | translate
                }}</small
              >
              <small
                class="text-danger"
                *ngIf="
                  formVersion?.get('releaseDate')?.errors
                    ?.invalidCreateMinutesTime &&
                  formVersion?.get('releaseDate')?.touched
                "
                >{{
                  "error.versionManage.dateTime.invalidTime" | translate
                }}</small
              >
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="text-center pb-4">
    <button
      type="button"
      class="btn mb-btn-outline-color mr-3"
      (click)="activeModal.close()"
    >
      {{ "common.action.close" | translate }}
    </button>
    <ng-container
      *hasPrivileges="
        action === ROUTER_ACTION.update
          ? SYSTEM_RULES.VERSION_WRITE
          : SYSTEM_RULES.VERSION_CREATE
      "
    >
      <button
        *ngIf="
          action === ROUTER_ACTION.update || action === ROUTER_ACTION.create
        "
        type="button"
        class="btn mb-btn-color"
        (click)="action === ROUTER_ACTION.create ? onCreate() : onUpdate()"
      >
        {{
          (action === ROUTER_ACTION.update
            ? "common.action.update"
            : "common.action.create"
          ) | translate
        }}
      </button>
    </ng-container>
  </div>
</div>
