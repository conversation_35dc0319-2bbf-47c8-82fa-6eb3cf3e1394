import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SERVER_API_URL } from 'src/app/shared/constants/app.constants';
import { AuthServerProvider } from 'src/app/shared/services/auth-jwt.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(private authServerProvider: AuthServerProvider) { }

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (
      !request ||
      !request.url ||
      (/^http/.test(request.url) && !(SERVER_API_URL && request.url.startsWith(SERVER_API_URL)))
    ) {
      return next.handle(request);
    }

    request = request.clone({
      //headers: request.headers.set('Authorization', 'Bearer ' + token),
      withCredentials: true
    });

    // const token = this.getAccessToken(request.url);

    // if (!!token) {
    //   request = request.clone({
    //     headers: request.headers.set('Authorization', 'Bearer ' + token),
    //   });
    // }

    return next.handle(request);
  }

  // getAccessToken(url: string): string {
  //   const regex = new RegExp('/api/(.*)/organization');
  //   return regex.test(url) ? this.authServerProvider.getOrgToken() : this.authServerProvider.getToken();
  // }
}
