import { IQrCode } from './qr-code.model';
import { ReferralType } from './types/model.type';

export interface IReferral {
  referralId?: number;
  referralCode?: string;
  userFullName?: string;
  userCode?: string;
  rmCode?: string;
  rmLink?: string;
  type?: ReferralType;
  phoneNumber?: string;
  status?: number;
  qrCode?: IQrCode;
  quantity?: number;
}

export class Referral implements IReferral {
  constructor(
    public referralId: number,
    public referralCode: string,
    public userFullName?: string,
    public userCode?: string,
    public rmCode?: string,
    public rmLink?: string,
    public type?: ReferralType,
    public phoneNumber?: string,
    public status?: number,
    public qrCode?: IQrCode,
    public quantity?: number
  ) {
    this.referralId = referralId;
    this.userFullName = userFullName;
    this.referralCode = referralCode;
    this.phoneNumber = phoneNumber;
    this.rmCode = rmCode;
    this.rmLink = rmLink;
    this.type = type;
    this.userCode = userCode;
    this.status = status;
    this.qrCode = qrCode;
    this.quantity = quantity;
  }
}
