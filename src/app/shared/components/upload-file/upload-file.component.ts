import {
  Component,
  EventEmitter,
  Input,
  isDevMode,
  OnInit,
  Output,
} from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { MODAL_ACTION, TYPE_FILES } from '@shared/constants/app.constants';
import { FILE_SIZE, MAX_FILE_SIZE } from '@shared/constants/file.constant';
import { MODEL_MAP_ITEM_COMMON } from '@shared/models/model.type';
import { CustomerService } from '@shared/services/customer.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { NzUploadChangeParam, NzUploadFile } from 'ng-zorro-antd/upload';
import { Observer, of } from 'rxjs';
import { Observable } from 'rxjs/internal/Observable';

@Component({
  selector: 'app-upload-file',
  templateUrl: './upload-file.component.html',
  styleUrls: ['./upload-file.component.scss'],
})
export class UploadFileComponent implements OnInit {
  @Input() fileRequired: string[] = [];

  @Input() multiple = false;

  @Input() acceptTypeFiles: string[] = [
    'default' || 'docx' || 'excel' || 'pdf' || 'image',
  ];

  @Input() hasBtnDownloadTemplate = false;

  @Output() emitter: EventEmitter<any> = new EventEmitter();

  readonly typeFiles = TYPE_FILES;

  files: NzUploadFile[] = [];

  acceptFiles: string[] = [];

  public title = 'common.action.confirm';
  public content = '';

  isSingle = false;

  hasFile = false;

  public action!: MODEL_MAP_ITEM_COMMON;

  MODAL_ACTION = MODAL_ACTION;

  constructor(
    public activeModal: NgbActiveModal,
    public translateService: TranslateService,
    public customerService: CustomerService,
    private toast: ToastrCustomService
  ) {}

  ngOnInit(): void {
    this.filesAccept();
  }

  getFiles(files: any): void {
    this.files = files;
  }

  handleChange({ file, fileList, event }: NzUploadChangeParam): void {
    if (this.multiple) {
      this.files = this.valid(fileList);
    } else {
      this.files = this.valid([file]);
    }

    if (this.files.length > 0) {
      this.hasFile = true;
    }
    // this.emitter.emit(this.files);
  }

  valid(files: any) {
    return files.filter(
      (file: any) =>
        this.acceptFiles.includes(file?.type) && file?.size <= MAX_FILE_SIZE
    );
  }

  handleCustomUploadReq = (item: any) => {
    return of(item).subscribe(
      (next) => {
        if (isDevMode()) {
        }
      },
      (err) => {
        if (isDevMode()) {
        }
      }
    );
  };

  filesAccept(): void {
    if (this.acceptTypeFiles.includes('default')) {
      this.acceptFiles = this.typeFiles.map((file) => file.value);
    } else {
      this.acceptFiles = this.typeFiles
        .filter((file) => this.acceptTypeFiles.includes(file.type))
        .map((val) => val.value);
    }
  }

  beforeUpload = (
    file: NzUploadFile,
    fileList: NzUploadFile[]
  ): Observable<boolean> =>
    new Observable((observer: Observer<boolean>) => {
      if (!file) {
        return;
      }
      const isFileAccept = this.acceptFiles.includes(file.type || '');
      if (!isFileAccept) {
        let type = '';
        if (this.acceptTypeFiles.includes('default')) {
          type = ['docx', 'excel', 'pdf', 'image'].toString();
        } else {
          type = this.acceptTypeFiles.toString();
        }
        this.toast.warning('common.fileAccept');
        observer.complete();
        return;
      }
      const isLessThan = (file.size || 0 / 1024 / 1024 < FILE_SIZE) as boolean;
      if (!isLessThan) {
        this.toast.warning('common.maxSizeFile');
        observer.complete();
        return;
      }
      this.files = [file];
      observer.next(isFileAccept && isLessThan);
      observer.complete();
    });

  downloadTemplate() {
    this.emitter.emit(MODAL_ACTION.DOWNLOAD_TEMPLATE.code);
  }
}
