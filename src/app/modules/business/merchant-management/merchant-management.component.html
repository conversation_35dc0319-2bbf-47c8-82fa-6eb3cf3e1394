<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formMerchantSearch" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-md-2">
            <div class="form-group">
              <label>{{ "common.action.searchKeyword" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="keyword"
                placeholder="{{ 'common.action.searchKeyword' | placeholder }}"
                class="w-100"
                class="form-control"
              />
            </div>
          </div>
          <!-- placeholder="{{ 'common.appSelectOption.status' | translate }}" -->
          <div class="col-md-2">
            <div class="form-group">
              <label>{{ "common.status" | translate }}</label>
              <ng-select
                appearance="outline"
                [searchable]="false"
                placeholder="{{ 'common.status' | placeholder : 'select' }}"
                [clearable]="true"
                formControlName="status"
              >
                <ng-option
                  [value]="item.code"
                  *ngFor="let item of ENTITY_STATUS"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-lg-4 col-md-8">
            <div class="date-picker-container form-group row">
              <div class="col-md-6">
                <label>{{ "common.action.fromDate" | translate }}</label>
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="fromDate"
                    formControlName="fromDate"
                    placeholder="DD/MM/YYYY"
                    [max]="maxDate"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="fromDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #fromDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formMerchantSearch.get('fromDate')?.errors?.invalidDate &&
                    formMerchantSearch.get('fromDate')?.touched
                  "
                >
                  {{ "error.required.inValidDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formMerchantSearch.get('fromDate')?.errors
                      ?.invalidMaxDate &&
                    formMerchantSearch.get('fromDate')?.touched
                  "
                >
                  {{
                    "error.maxDateCurrent"
                      | translate
                        : {
                            param: "common.action.fromDate" | translate
                          }
                  }}
                </small>
              </div>
              <div class="col-md-6">
                <label>{{ "common.action.toDate" | translate }}</label>
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="toDate"
                    formControlName="toDate"
                    placeholder="DD/MM/YYYY"
                    min="{{
                      formMerchantSearch.controls['fromDate'].value
                        | date : 'yyyy-MM-dd'
                    }}"
                    [max]="maxToDate"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="toDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #toDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formMerchantSearch.get('toDate')?.errors?.invalidMaxDate &&
                    formMerchantSearch.get('toDate')?.touched
                  "
                >
                  {{
                    "error.maxDateCurrent"
                      | translate
                        : {
                            param: "common.action.toDate" | translate
                          }
                  }}
                </small>
              </div>
            </div>
          </div>
          <div class="col-md-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
            >
              <div class="btn-reset">
                <i
                  class="bi bi-arrow-clockwise"
                  type="button"
                  (click)="onReset()"
                >
                </i>
              </div>
            </div>
            <div class="">
              <button
                class="btn btn-search mr-2"
                (click)="onSearch()"
                *hasPrivileges="SYSTEM_RULES.MERCHANT_READ"
              >
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-2 mt-4">
          <button
            class="btn btn-red mb-2"
            type="button"
            (click)="merchantCodeSync()"
            [disabled]="!isSync"
            *hasPrivileges="SYSTEM_RULES.MERCHANT_CODE_SYNC"
          >
            {{ "merchant.merchantCodeSync" | translate }}
          </button>
          <button
            class="btn btn-white mb-2"
            type="button"
            [disabled]="merchants?.length === 0"
            (click)="exportFile()"
            *hasPrivileges="SYSTEM_RULES.EXPORT_MERCHANT"
          >
            {{ "common.action.export" | translate }}
          </button>
          <button
            class="btn btn-red mb-2"
            type="button"
            (click)="create()"
            *hasPrivileges="SYSTEM_RULES.MERCHANT_CREATE"
          >
            {{ "common.action.create" | translate }}
          </button>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th class="text-center" scope="col">
                {{ "common.no" | translate }}
              </th>
              <th scope="col">
                {{ "model.merchant.merchantCode" | translate }}
              </th>
              <th scope="col">
                {{ "model.merchant.merchantName" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "model.merchant.origin" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.merchant.merchantAccountNumber" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.merchant.description" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "model.configTransaction.createdDate" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.status" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of merchants; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td>{{ dataItem.merchantCode }}</td>
              <td>
                <span title="{{ dataItem.merchantName }}">{{
                  dataItem.merchantName | limitWord
                }}</span>
              </td>
              <td class="text-center">{{ dataItem.origin }}</td>
              <td class="text-left">{{ dataItem.merchantAccountNumber }}</td>
              <td class="text-left">
                <span title="{{ dataItem.description }}">{{
                  dataItem.description | limitWord
                }}</span>
              </td>
              <td class="text-center">{{ dataItem.createdDate }}</td>
              <td class="text-center">
                <!-- {{ ENTITY_STATUS_MAP[dataItem.status || 0] | translate }} -->
                <span
                  class="badge"
                  [ngClass]="ENTITY_STATUS_MAP[dataItem.status || 0].style"
                  >{{
                    ENTITY_STATUS_MAP[dataItem.status || 0].label | translate
                  }}</span
                >
              </td>
              <td class="text-center">
                <!-- <button
                  *ngIf="
                    dataItem.actionToken ===
                    ACTION_TOKEN_CONST.RESEND_LINK_ACTIVATE
                  "
                  class="btn px-1 py-0"
                  (click)="resend(dataItem)"
                > -->
                <button
                  ngbTooltip="{{ 'common.action.detail' | translate }}"
                  class="btn px-1 py-0"
                  (click)="detail(dataItem.merchantId)"
                  *hasPrivileges="SYSTEM_RULES.MERCHANT_READ"
                >
                  <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                </button>
                <button
                  ngbTooltip="{{ 'common.action.update' | translate }}"
                  class="btn px-1 py-0"
                  (click)="edit(dataItem.merchantId)"
                  data-toggle="modal"
                  *hasPrivileges="SYSTEM_RULES.MERCHANT_WRITE"
                >
                  <i class="fa fa-edit mb-color" aria-hidden="true"></i>
                </button>
                <button
                  [ngbTooltip]="
                    (dataItem.status === ENTITY_STATUS_CONST.ACTIVE.code
                      ? 'common.action.lock'
                      : 'common.action.unlock'
                    ) | translate
                  "
                  class="btn px-1 py-0"
                  (click)="lockAndUnlock(dataItem)"
                  *hasPrivileges="
                    dataItem.status === 1
                      ? SYSTEM_RULES.MERCHANT_LOCK
                      : SYSTEM_RULES.MERCHANT_UNLOCK
                  "
                >
                  <i
                    [className]="
                      dataItem.status === 1
                        ? 'fa fa-lock mb-color'
                        : 'fa fa-unlock mb-color'
                    "
                    aria-hidden="true"
                  ></i>
                </button>
                <!--*ngIf="dataItem.approvalStatus === 1 && (dataItem.approvalType === 1 || dataItem.approvalType === 2)"-->
                <button
                  ngbTooltip="{{ 'common.action.delete' | translate }}"
                  class="btn px-1 py-0"
                  (click)="delete(dataItem)"
                  *hasPrivileges="SYSTEM_RULES.MERCHANT_DELETE"
                >
                  <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        <div
          class="row d-block text-center m-0"
          *ngIf="merchants?.length === 0"
        >
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
        <div *ngIf="merchants.length">
          <mat-paginator
            [length]="formMerchantSearch.value.length"
            [pageSize]="formMerchantSearch.value.pageSize"
            [pageIndex]="formMerchantSearch.value.pageIndex"
            [pageSizeOptions]="pageSizeOptions"
            (page)="onChangePage($event)"
            aria-label="Select page"
          >
          </mat-paginator>
        </div>
      </div>
    </div>
  </div>
</section>
