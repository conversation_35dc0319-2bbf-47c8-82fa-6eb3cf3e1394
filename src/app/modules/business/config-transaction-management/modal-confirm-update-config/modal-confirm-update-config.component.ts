import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import {
  CONFIG_TRANS_STATUS_CONST,
  MODAL_ACTION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ConfigTransactionService } from '@shared/services/config-transaction.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-modal-confirm-update-config',
  templateUrl: './modal-confirm-update-config.component.html',
  styleUrls: ['./modal-confirm-update-config.component.scss'],
})
export class ModalConfirmUpdateConfigComponent implements OnInit {
  formReason: FormGroup = new FormGroup({});
  action = '';
  transactionLimitId?: number;

  SYSTEM_RULES = SYSTEM_RULES;
  MODAL_ACTION = MODAL_ACTION;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  CONFIG_TRANS_STATUS_CONST = CONFIG_TRANS_STATUS_CONST;
  VALIDATORS = VALIDATORS;
  constructor(
    public activeModal: NgbActiveModal,
    private fb: FormBuilder,
    private configTransService: ConfigTransactionService,
    private toastrCustom: ToastrCustomService
  ) {}

  ngOnInit(): void {
    this.formReason = this.fb.group({
      reason: [null],
      status: [
        this.action === ROUTER_ACTIONS.deny
          ? CONFIG_TRANS_STATUS_CONST.DENY.value
          : CONFIG_TRANS_STATUS_CONST.APPROVAL.value,
      ],
      transactionLimitId: [
        this.transactionLimitId ? this.transactionLimitId : '',
      ],
    });

    if (this.action === ROUTER_ACTIONS.deny) {
      this.formReason.get('reason')?.setValidators(Validators.required);
      this.formReason.get('reason')?.updateValueAndValidity();
    } else {
      this.formReason.get('reason')?.clearValidators();
      this.formReason.get('reason')?.updateValueAndValidity();
    }
  }

  onConfirm(): void {
    if (this.formReason.invalid) {
      CommonUtils.markFormGroupTouched(this.formReason);
      return;
    }
    this.configTransService.approve(this.formReason.value).subscribe((res) => {
      if (this.action === ROUTER_ACTIONS.deny) {
        this.activeModal.close(MODAL_ACTION.CONFIRM.code);
        this.toastrCustom.success('model.configTransaction.denySuccess');
      }
      if (this.action === ROUTER_ACTIONS.approval) {
        this.activeModal.close(MODAL_ACTION.CONFIRM.code);
        this.toastrCustom.success('model.configTransaction.approvalSuccess');
      }
    });
  }
}
