import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { IBank } from '@shared/models/bank.model';
import { IBankSearch } from '@shared/models/request/bank.search';
import { IFileEntrySearch } from '@shared/models/request/file-entry.search';
import CommonUtils from '@shared/utils/common-utils';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({ providedIn: 'root' })
export class BankService extends AbstractService {
  public resourceUrl = '/bank';

  constructor(protected http: HttpClient, private sanitizer: DomSanitizer) {
    super(http);
  }

  /**
   * API search
   *
   * @param request IBankSearch
   * @returns Observable<EntityResponseType<IBank>>
   */
  search(request: IBankSearch): Observable<EntityResponseType<IBank>> {
    return super.post<EntityResponseType<IBank>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  /**
   * API detail
   *
   * @param request IBank
   * @returns Observable<EntityResponseType<IBank>>
   */
  detail(request: IBank): Observable<EntityResponseType<IBank>> {
    return super.post<EntityResponseType<IBank>>(
      `${this.resourceUrl}/info`,
      request
    );
  }

  /**
   * API create
   *
   * @param request IBank
   * @returns Observable<EntityResponseType<IBank>>
   */
  create(request: IBank): Observable<EntityResponseType<IBank>> {
    const body = CommonUtils.appendObjectToFormData(request, new FormData());
    return super.post<EntityResponseType<IBank>>(
      `${this.resourceUrl}/create`,
      body
    );
  }

  /**
   * API update
   *
   * @param request IBank
   * @returns Observable<EntityResponseType<IBank>>
   */
  update(request: IBank): Observable<EntityResponseType<IBank>> {
    const body = CommonUtils.appendObjectToFormData(request, new FormData());
    return super.post<EntityResponseType<IBank>>(
      `${this.resourceUrl}/update`,
      body
    );
  }

  /**
   * API delete
   *
   * @param request IBank
   * @returns Observable<EntityResponseType<IBank>>
   */
  deleteBank(request: IBank): Observable<EntityResponseType<IBank>> {
    return super.post<EntityResponseType<IBank>>(
      `${this.resourceUrl}/delete`,
      request
    );
  }

  /**
   * API lock
   *
   * @param request IBank
   * @returns Observable<EntityResponseType<IBank>>
   */
  lock(request: IBank): Observable<EntityResponseType<IBank>> {
    return super.post<EntityResponseType<IBank>>(
      `${this.resourceUrl}/lock`,
      request
    );
  }

  /**
   * API unlock
   *
   * @param request IBank
   * @returns Observable<EntityResponseType<IBank>>
   */
  unlock(request: IBank): Observable<EntityResponseType<IBank>> {
    return super.post<EntityResponseType<IBank>>(
      `${this.resourceUrl}/unlock`,
      request
    );
  }

  /**
   * getIconUri
   *
   * @param search IFileEntrySearch
   * @param loading boolean
   * @returns Observable<any>
   */
  getIconUri(
    search: IFileEntrySearch,
    loading = false,
    ignoreError = true
  ): any {
    return super
      .postFile(`${this.resourceUrl}/icon`, search, {
        loading,
        ignoreError,
      })
      .pipe(
        map((res) => {
          const objectURL = URL.createObjectURL(res.body);
          return this.sanitizer.bypassSecurityTrustUrl(objectURL);
        })
      );
  }

  /**
   * getIcon
   *
   * @param search IFileEntrySearch
   * @param loading boolean
   * @returns Observable<any>
   */
  getIcon(search: IFileEntrySearch, loading = false, ignoreError = true): any {
    return super.postFile(`${this.resourceUrl}/icon`, search, {
      loading,
      ignoreError,
    });
  }
}
