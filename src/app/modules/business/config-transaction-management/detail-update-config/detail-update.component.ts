import { Component, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import {
  CONFIG_TRANS_STATUS_CONST,
  CURRENCY,
  CURRENCY_CONST,
  DATE_CONSTANT,
  ENTITY_STATUS_CONST,
  INTER_TRANSACTION_TYPE,
  INTERNAL_TRANSACTION_TYPE,
  INTERNATIONAL_TRANSACTION_TYPE,
  MODAL_ACTION,
  MOMENT_CONST,
  OTHER_TRANSACTION_TYPE,
  PAGINATION,
  SERVICE_TYPE,
  SERVICE_TYPE_CONST,
  SERVICE_TYPE_MAP,
  TRANSACTION_SECTOR,
  TRANSACTION_SECTOR_CONST,
  TRANSACTION_SECTOR_MAP,
  TRA<PERSON><PERSON>TION_TYPE,
  TRANSACTION_TYPE_CONST,
  TRANSACTION_TYPE_MAP,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import {
  IConfigTransaction,
  ITransLimitDetails,
} from '@shared/models/config-transaction.model';
import { ConfigTransactionService } from '@shared/services/config-transaction.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';
import { ModalConfirmUpdateConfigComponent } from '../modal-confirm-update-config/modal-confirm-update-config.component';
import { ICurrency } from '@shared/models/currency.model';
import { CurrencyService } from '@shared/services/currency.service';
@Component({
  selector: 'app-detail-update-config',
  templateUrl: './detail-update-config.component.html',
  styleUrls: ['./detail-update-config.component.scss'],
})
export class DetailUpdateConfigComponent implements OnInit {
  formArray: FormGroup = new FormGroup({});
  formCreate: FormGroup = new FormGroup({});
  action?: string;
  itemId?: number;
  currency?: string;
  data?: IConfigTransaction;
  transactionLimitDetailDTOS: ITransLimitDetails[] = [];
  currencies: ICurrency[] = [];

  minDateStart = new Date();
  currentDate = new Date();
  minDateEnd = new Date();

  moneyDay = 0;
  transactionLimitId = 0;
  itemStatus = 0;
  maxDateStart = '';

  isSector1890 = false;
  isSector1891 = false;
  isSector1740 = false;
  isDisabled?: boolean;
  isSectorSelected = true;

  SECTOR_1890 = TRANSACTION_SECTOR_CONST.SECTOR_1890.value.toString();
  SECTOR_1891 = TRANSACTION_SECTOR_CONST.SECTOR_1891.value.toString();
  SECTOR_1740 = TRANSACTION_SECTOR_CONST.SECTOR_1740.value.toString();
  pageIndex = PAGINATION.PAGE_NUMBER_DEFAULT;
  pageSize = PAGINATION.PAGE_SIZE_DEFAULT;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  ROUTER_UTILS = ROUTER_UTILS;
  VALIDATORS = VALIDATORS;
  SYSTEM_RULES = SYSTEM_RULES;
  SERVICE_TYPE_CONST = SERVICE_TYPE_CONST;
  SERVICE_TYPE = SERVICE_TYPE;
  SERVICE_TYPE_MAP = SERVICE_TYPE_MAP;
  TRANSACTION_TYPE_CONST = TRANSACTION_TYPE_CONST;
  TRANSACTION_TYPE = TRANSACTION_TYPE;
  INTERNATIONAL_TRANSACTION_TYPE = INTERNATIONAL_TRANSACTION_TYPE;
  INTER_TRANSACTION_TYPE = INTER_TRANSACTION_TYPE;
  INTERNAL_TRANSACTION_TYPE = INTERNAL_TRANSACTION_TYPE;
  OTHER_TRANSACTION_TYPE = OTHER_TRANSACTION_TYPE;
  CURRENCY_CONST = CURRENCY_CONST;
  INTERNAL_CURRENCY = [CURRENCY_CONST.THB.value, CURRENCY_CONST.USD.value];
  TRANSACTION_TYPE_MAP = TRANSACTION_TYPE_MAP;
  TRANSACTION_SECTOR_CONST = TRANSACTION_SECTOR_CONST;
  TRANSACTION_SECTOR = TRANSACTION_SECTOR;
  TRANSACTION_SECTOR_MAP = TRANSACTION_SECTOR_MAP;
  MODAL_ACTION = MODAL_ACTION;
  CONFIG_TRANS_STATUS_CONST = CONFIG_TRANS_STATUS_CONST;
  DATE_CONSTANT = DATE_CONSTANT;
  CURRENCY = CURRENCY;

  constructor(
    private fb: FormBuilder,
    private toastrService: ToastrCustomService,
    private modalService: NgbModal,
    private router: Router,
    private activeRoute: ActivatedRoute,
    private configTransService: ConfigTransactionService,
    private currencyService: CurrencyService
  ) {
    this.activeRoute.data.subscribe((res) => {
      this.action = res.action;
    });

    this.activeRoute.paramMap.subscribe((param) => {
      const transactionLimitId = Number(param.get('transactionLimitId'));
      if (transactionLimitId) {
        this.transactionLimitId = transactionLimitId;
        this.getDetail();
      }
    });
  }

  ngOnInit(): void {
    this.formArray = this.fb.group({
      arrayForm: this.fb.array([...this.createLinkFormGroups()]),
    });

    this.validateTimeStart = this.validateTimeStart.bind(this);
    this.validateTimeEnd = this.validateTimeEnd.bind(this);
  }

  private createLinkFormGroups(currency?: string): FormGroup[] {
    const arr = [];
    let transactionTypes;

    if (!currency) {
      transactionTypes = this.TRANSACTION_TYPE;
    } else if (currency === CURRENCY_CONST.LAK.value) {
      transactionTypes = this.INTERNAL_TRANSACTION_TYPE;
    } else if (
      currency === CURRENCY_CONST.THB.value ||
      currency === CURRENCY_CONST.USD.value
    ) {
      transactionTypes = this.INTER_TRANSACTION_TYPE;
    } else if (
      currency === CURRENCY_CONST.VND.value ||
      currency === CURRENCY_CONST.KHR.value
    ) {
      transactionTypes = this.INTERNATIONAL_TRANSACTION_TYPE;
    } else {
      transactionTypes = this.OTHER_TRANSACTION_TYPE;
    }

    for (const item of transactionTypes) {
      const form = this.fb.group({
        label: [item.label],
        transferType: [item.formName],
        maxTransferAmount: ['', this.moneyTransInvalidNone],
      });
      arr.push(form);
    }

    if (currency) {
      (this.formArray.get('arrayForm') as FormArray).clear();
      this.formCreate.get('transactionLimitDetails')?.setValue([]);

      arr.forEach((element) => {
        (this.formArray.get('arrayForm') as FormArray).push(element);
      });
    }
    return arr;
  }

  public getFormGroupArray(): FormArray {
    return this.formArray.get('arrayForm') as FormArray;
  }

  getCurrencies(): void {
    this.currencyService
      .search({
        hasPageable: false,
        status: Number(ENTITY_STATUS_CONST.ACTIVE.code),
      })
      .subscribe((res: any) => {
        if (res.body.content.length) {
          this.currencies = res.body.content;
        }
      });
  }

  initForm(data: IConfigTransaction): void {
    this.getCurrencies();
    if (data) {
      this.formCreate = this.fb.group({
        serviceName: [
          this.data?.serviceName || '',
          [
            Validators.required,
            Validators.pattern(VALIDATORS.PATTERN.NAME_NO_SPECIAL),
            Validators.pattern(VALIDATORS.PATTERN.NAME_NO_ASCII),
          ],
        ],
        sectorId: [this.data?.sectorId.toString() || '', [Validators.required]],
        currency: [
          this.data?.currency?.toString() || '',
          [Validators.required],
        ],
        maxTransferAmtOfDay: [
          data.maxTransferAmtOfDay
            ? CommonUtils.moneyFormat(data.maxTransferAmtOfDay?.toString())
            : '',
          [this.moneyDayInvalid],
        ],
        maxTransferAmtOfMonth: [
          data.maxTransferAmtOfMonth
            ? CommonUtils.moneyFormat(data.maxTransferAmtOfMonth?.toString())
            : '',
          [this.moneyMonthInvalid],
        ],
        maxTransferAmtOfYear: [
          data.maxTransferAmtOfYear
            ? CommonUtils.moneyFormat(data.maxTransferAmtOfYear?.toString())
            : null,
          [this.moneyYearInvalid],
        ],
        startDate: [
          CommonUtils.getDateStringISO(data.startDate + ''),
          [Validators.required],
        ],
        endDate: [
          CommonUtils.getDateStringISO(data.endDate + ''),
          [Validators.required],
        ],
        transactionLimitDetails: [[]],
        status: [0],
      });

      if (this.action === this.ROUTER_ACTIONS.update) {
        this.formCreate.enable();
        this.isDisabled = false;
      }
      if (
        Number(this.formCreate.controls.sectorId.value) ===
        this.TRANSACTION_SECTOR_CONST.SECTOR_1890.value
      ) {
        this.isSector1890 = true;
      }
      if (
        Number(this.formCreate.controls.sectorId.value) ===
        this.TRANSACTION_SECTOR_CONST.SECTOR_1891.value
      ) {
        this.isSector1891 = true;
        this.formCreate.controls.maxTransferAmtOfMonth.clearValidators();

        this.formCreate.controls.maxTransferAmtOfMonth.setValidators([
          this.moneyMonthSectorInvalid,
        ]);
        this.formCreate.controls.maxTransferAmtOfMonth.updateValueAndValidity();
      }

      this.currency = this.data?.currency;
    }

    this.formArray = this.fb.group({
      arrayForm: this.fb.array([...this.createLinkFormGroups(data?.currency)]),
    });

    if (this.action === this.ROUTER_ACTIONS.detail) {
      this.formCreate.disable();
      this.getFormGroupArray().disable();
      this.isDisabled = true;
    }
  }

  onUpdate(): void {
    const data = { ...this.formCreate.getRawValue() };
    data.transactionLimitDetails = [];

    for (const item of this.getFormGroupArray().controls) {
      if (item.invalid && this.formCreate.invalid) {
        CommonUtils.markFormGroupTouched(this.formCreate);
        CommonUtils.markFormGroupTouchedArray(this.getFormGroupArray());
        return;
      }
      if (item.invalid) {
        CommonUtils.markFormGroupTouchedArray(this.getFormGroupArray());
        return;
      }
      if (this.formCreate.invalid) {
        CommonUtils.markFormGroupTouched(this.formCreate);
        return;
      }
    }
    if (
      !CommonUtils.checkFormGroupInvalid(
        this.getFormGroupArray(),
        'maxTransferAmount'
      ) &&
      !CommonUtils.checkFormGroupRequired(
        this.getFormGroupArray(),
        'maxTransferAmount'
      )
    ) {
      for (const item of this.getFormGroupArray().controls) {
        const dataTransaction = { ...item.value };
        delete dataTransaction.label;
        dataTransaction.maxTransferAmount = Number(
          dataTransaction?.maxTransferAmount?.split('.').join('')
        );
        data.transactionLimitDetails.push(dataTransaction);
      }
    }
    data.maxTransferAmtOfDay = Number(
      data.maxTransferAmtOfDay?.split('.').join('')
    );
    if (data.maxTransferAmtOfMonth) {
      data.maxTransferAmtOfMonth = Number(
        data.maxTransferAmtOfMonth?.split('.').join('')
      );
    }
    if (data.maxTransferAmtOfYear) {
      data.maxTransferAmtOfYear = Number(
        data.maxTransferAmtOfYear?.split('.').join('')
      );
    }

    const endDate = new Date(this.formCreate?.get('endDate')?.value);
    const startDate = new Date(this.formCreate?.get('startDate')?.value);
    data.transactionLimitId = this.transactionLimitId;
    data.sectorId = Number(data.sectorId);
    data.startDate = startDate.toISOString();
    data.endDate = endDate.toISOString();
    this.configTransService.update(data).subscribe((res) => {
      this.toastrService.success('common.action.updateSuccess');
      this.router.navigate([ROUTER_UTILS.sysManage.configTransaction.root]);
    });
  }

  onDeny(): void {
    const modalRef = this.modalService.open(ModalConfirmUpdateConfigComponent, {
      size: 'lg',
      keyboard: true,
      centered: true,
      backdrop: 'static',
    });
    modalRef.componentInstance.action = ROUTER_ACTIONS.deny;
    modalRef.componentInstance.transactionLimitId = this.transactionLimitId;
    modalRef.result.then((res) => {
      if (res === MODAL_ACTION.CONFIRM.code) {
        this.router.navigate([ROUTER_UTILS.sysManage.configTransaction.root]);
      }
    });
  }

  onConfirm(): void {
    const modalRef = this.modalService.open(ModalConfirmUpdateConfigComponent, {
      size: 'lg',
      keyboard: true,
      centered: true,
      backdrop: 'static',
    });
    modalRef.componentInstance.action = ROUTER_ACTIONS.approval;
    modalRef.componentInstance.transactionLimitId = this.transactionLimitId;
    modalRef.result.then((res) => {
      if (res === MODAL_ACTION.CONFIRM.code) {
        this.router.navigate([ROUTER_UTILS.sysManage.configTransaction.root]);
      }
    });
  }

  back(): void {
    this.router.navigate([ROUTER_UTILS.sysManage.configTransaction.root]);
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(index, this.pageIndex, this.pageSize);
  }

  getDetail(): void {
    this.configTransService
      .detail({
        transactionLimitId: this.transactionLimitId,
      })
      .subscribe((res: any) => {
        this.data = res.body as IConfigTransaction;
        this.initForm(this.data);
        this.transactionLimitDetailDTOS = res.body.transactionLimitDetailDTOS;
        this.getTransLimit(this.transactionLimitDetailDTOS);
      });
  }

  getTransLimit(data: ITransLimitDetails[]): void {
    for (const item of data) {
      for (const formArray of this.getFormGroupArray().controls) {
        if (
          formArray?.get('transferType')?.value === item.transferType &&
          item.maxTransferAmount
        ) {
          formArray
            .get('maxTransferAmount')
            ?.setValue(
              CommonUtils.moneyFormat(item.maxTransferAmount.toString())
            );
          formArray.get('maxTransferAmount')?.updateValueAndValidity();
        }
      }
    }
  }

  getCurrency(currency: string): void {
    this.currency = currency;
    this.createLinkFormGroups(currency);
  }

  getSector(event: any): void {
    const sector = event.target.value;
    if (Number(sector) === this.TRANSACTION_SECTOR_CONST.SECTOR_1890.value) {
      this.isSector1890 = true;
      this.isSector1740 = false;
      this.isSector1891 = false;
      this.isSectorSelected = false;
      this.formCreate
        .get('sectorId')
        ?.setValue(this.TRANSACTION_SECTOR_CONST.SECTOR_1890.value);

      this.formCreate.controls.maxTransferAmtOfMonth.clearValidators();
      this.formCreate.controls.maxTransferAmtOfMonth.setValidators([
        this.moneyMonthInvalid,
      ]);
      this.formCreate.controls.maxTransferAmtOfMonth.updateValueAndValidity();
    }

    if (Number(sector) === this.TRANSACTION_SECTOR_CONST.SECTOR_1740.value) {
      this.isSector1740 = true;
      this.isSector1890 = false;
      this.isSector1891 = false;
      this.isSectorSelected = false;
      this.formCreate
        .get('sectorId')
        ?.setValue(this.TRANSACTION_SECTOR_CONST.SECTOR_1740.value);

      this.formCreate.controls.maxTransferAmtOfMonth.clearValidators();
      this.formCreate.controls.maxTransferAmtOfMonth.setValidators([
        this.moneyMonthInvalid,
      ]);
      this.formCreate.controls.maxTransferAmtOfMonth.updateValueAndValidity();
    }

    if (Number(sector) === this.TRANSACTION_SECTOR_CONST.SECTOR_1891.value) {
      this.formCreate
        .get('sectorId')
        ?.setValue(this.TRANSACTION_SECTOR_CONST.SECTOR_1891.value);
      this.isSector1891 = true;
      this.isSector1740 = false;
      this.isSector1890 = false;
      this.formCreate.controls.maxTransferAmtOfDay.updateValueAndValidity();
      this.formCreate.controls.maxTransferAmtOfMonth.clearValidators();

      this.formCreate.controls.maxTransferAmtOfMonth.setValidators([
        this.moneyMonthSectorInvalid,
      ]);
      this.formCreate.controls.maxTransferAmtOfMonth.updateValueAndValidity();
    }
  }

  //// -----------custom validate form--------
  moneyDayInvalid = (control: AbstractControl): ValidationErrors | null => {
    if (control?.value) {
      const money = Number(control?.value?.split('.').join(''));
      this.moneyDay = money;
      if (money > 0) {
        for (const item of this.getFormGroupArray().controls) {
          item
            .get('maxTransferAmount')
            ?.setValidators([
              this.moneyTransInvalid,
              this.moneyTransInvalidNone,
            ]);
          item.get('maxTransferAmount')?.updateValueAndValidity();
        }
        return null; // passes the test, return null to clear any errors
      }
      if (money === 0) {
        return { invalidMoneyDay: true };
      }
    }
    if (!control?.value) {
      return { invalidMoneyDay: true };
    }
    return null;
  };

  moneyYearInvalid = (control: AbstractControl): ValidationErrors | null => {
    if (control?.value) {
      const money = Number(control?.value?.split('.').join(''));
      if (money > 0) {
        return null; // passes the test, return null to clear any errors
      }
      if (money === 0) {
        return { invalidMoneyYear: true };
      }
    }
    if (!control?.value) {
      return null;
    }
    return null;
  };

  moneyMonthInvalid = (control: AbstractControl): ValidationErrors | null => {
    if (control?.value) {
      const money = Number(control?.value?.split('.').join(''));
      if (money > 0) {
        return null; // passes the test, return null to clear any errors
      }
      if (money === 0) {
        return { invalidMoneyMonth: true };
      }
    }
    if (!control?.value) {
      return null;
    }
    return null;
  };

  moneyMonthSectorInvalid = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (control?.value) {
      const money = Number(control?.value?.split('.').join(''));

      if (money > 0) {
        return null; // passes the test, return null to clear any errors
      }
      if (money === 0) {
        return { invalidMoneyMonth: true };
      }
    }
    if (!control?.value) {
      return { invalidMoneyMonth: true };
    }
    return null;
  };

  moneyTransInvalid = (control: AbstractControl): ValidationErrors | null => {
    if (control?.value) {
      const money = Number(control?.value?.split('.').join(''));
      if (money <= this.moneyDay) {
        return null;
      }
    }
    if (!control?.value) {
      return null;
    }
    return { invalidMoneyTrans: true };
  };

  moneyTransInvalidNone = (
    control: AbstractControl
  ): ValidationErrors | null => {
    if (control?.value) {
      const money = Number(control?.value?.split('.').join(''));
      if (money > 0) {
        return null;
      }
      if (money === 0) {
        return { invalidMoneyTransNone: true };
      }
    }
    if (!control?.value) {
      return { invalidMoneyTransNone: true };
    }
    return null;
  };

  setValidateTimeStart(): void {
    this.formCreate.get('startDate')?.addValidators(this.validateTimeStart);
    this.formCreate.get('startDate')?.updateValueAndValidity();
  }
  setValidateTimeEnd(): void {
    this.formCreate.get('endDate')?.addValidators(this.validateTimeEnd);
    this.formCreate.get('endDate')?.updateValueAndValidity();
  }

  validateTimeStart(control: AbstractControl): ValidationErrors | null {
    if (control.value) {
      const startDate = new Date(control.value);
      const date = new Date();
      const startDateCheck = new Date(control.value);
      this.minDateEnd = new Date(startDate.setDate(startDate.getDate() + 1));
      const endDate = new Date(this.formCreate.get('endDate')?.value);
      const startDateMoment = moment(control.value).format(
        MOMENT_CONST.FORMAT_YEAR
      );
      const dateCurrentMoment = moment(new Date()).format(
        MOMENT_CONST.FORMAT_YEAR
      );
      if (
        endDate.getMonth() === startDateCheck.getMonth() &&
        endDate.getDate() === startDateCheck.getDate()
      ) {
        return { dateEqual: true };
      }
      if (startDateMoment === dateCurrentMoment) {
        if (startDateCheck.getHours() < date.getHours()) {
          return { isValidStartHour: true };
        }
        if (startDateCheck.getHours() === date.getHours()) {
          if (startDateCheck.getMinutes() < date.getMinutes() + 2) {
            return { isValidStartTime: true };
          } else {
            return null;
          }
        }
      }
    }
    return null;
  }

  validateTimeEnd(control: AbstractControl): ValidationErrors | null {
    if (control.value) {
      this.maxDateStart = control.value;
      const startDate = this.formCreate.get('startDate')?.value;
      const date = moment(new Date()).format(MOMENT_CONST.FORMAT_YEAR);
      const endDateCheck = moment(control.value).format(
        MOMENT_CONST.FORMAT_YEAR
      );
      const startDateCheck = moment(startDate).format(MOMENT_CONST.FORMAT_YEAR);
      if (!startDate) {
        if (date === endDateCheck) {
          return { inValidEndDateEqual: true };
        } else {
          return null;
        }
      } else {
        if (endDateCheck < startDateCheck || endDateCheck === startDateCheck) {
          return { isValidaEndDate: true };
        } else {
          return null;
        }
      }
    }
    return null;
  }
}
