export interface INotificationLimitTransaction {
  merchantId?: number;
  customerAccountNumber?: string;
  customerCif?: string;
  transactionId?: string;
  transactionDateStart?: string;
  transactionDateEnd?: string;
  merchantIds?: string;
  customerFullName?: string;
  transactionAmount?: number;
  transactionFee?: number;
  transactionDate?: string;
  customerId?: number;
  merchantCode?: string;
  merchantName?: string;
  customerAccNumber?: string;
  customerAccountName?: string;
  transactionStartTime?: string;
  transactionCode?: string;
  transactionStatus?: string;
  transferType?: string;
  bankCode?: string;
  target?: string;
  beneficiaryCustomerName?: string;
  transactionCurrency?: string;
  totalAmountTransaction?: number;
  transID?: string;
  transactionStartTimeStr?: string;
  extraTransactionCode?: string;
  transactionDateStr?: string;
  discount?: string;
  discountFixed?: number;
  clientMessageId?: string;
  billingTypeStr?: string;
  billingType?: string;
  provinceCode?: string;
  message?: string;
  titleLa?: string;
  accountName?: string;
  beneficiaryAccountNumber?: string;
  discountFixedStr?: string;
  totalAmount?: number;
  actualTransactionAmount?: number;
  limitPerTransaction?: number;
  limitPerDay?: number;
  limitPerMonth?: number;
  limitPerYear?: number;
  typeLimit?: string;
  actualTransactionAmountStr?: string;
  limitPerTransactionStr?: string;
  limitPerDayStr?: string;
  limitPerMonthStr?: string;
  limitPerYearStr?: string;
  statusStr?: string;
  status?: number;
  notificationLimitId?: number;
  transferTypeStr?: string;
  typeLimitStr?: string;
  phoneNumber?: string;
  referralCode?: string;
  createdDate?: string;
  createdDateStr?: string;
  fullname?: string;
}

export interface INotificationTransaction {
  numberNotificationLimit?: number;
  numberPremiumAccountRevert?: number;
}
