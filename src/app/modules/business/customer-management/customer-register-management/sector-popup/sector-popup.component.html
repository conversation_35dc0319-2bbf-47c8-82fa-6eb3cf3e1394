<div class="modal-content">
  <div class="modal-header">
    <h5 class="modal-title">
      {{ "common.action.updateSector" | translate }}
    </h5>
    <h5 class="modal-title"></h5>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="activeModal.close()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="form">
      <div class="form-group">
        <label>{{ "customerRegisterManagement.sector" | translate }}
          <span class="text-danger">*</span></label>
        <ng-select appAutoValidate placeholder="{{ 'customerRegisterManagement.sector' | translate }}"
          appearance="outline" [searchable]="false" [clearable]="false" formControlName="customerSectorId"
          [items]="options" bindLabel="value" bindValue="value">
          <ng-template ng-option-tmp let-item="item">
            <div>{{ item.value }}</div>
          </ng-template>
        </ng-select>
      </div>
    </form>
  </div>
  <div class="text-center pb-4">
    <button type="button" class="btn mb-btn-outline-color mr-3" (click)="activeModal.close()">
      {{ "common.action.close" | translate }}
    </button>
    <ng-container>
      <button type="button" class="btn mb-btn-color" (click)="updateSectors()">
        {{ "common.action.update" | translate }}
      </button>
    </ng-container>
  </div>
</div>