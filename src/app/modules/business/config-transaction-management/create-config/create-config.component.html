<div class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="back()" />
      <h5>{{ "model.configTransaction.create" | translate }}</h5>
    </div>
    <form [formGroup]="formCreate" class="mt-4">
      <div class="col-md-12">
        <div class="row border-create">
          <h3>{{ "model.configTransaction.create" | translate }}</h3>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-4">
                <label>{{ "model.configTransaction.form.servicePackage" | translate
                  }}<small class="text-danger">*</small></label>
                <input class="form-control w-1000" formControlName="serviceName" placeholder="{{
                    'model.configTransaction.placeholder.packageName'
                      | translate
                  }}" type="text" trim [maxLength]="VALIDATORS.LENGTH.PACKAGE_NAME_LENGTH" />
                <small class="text-danger" *ngIf="
                    formCreate.get('serviceName')?.errors?.required &&
                    formCreate.get('serviceName')?.touched
                  ">{{
                  "error.configTransaction.required.servicePackage"
                  | translate
                  }}</small>
                <small class="text-danger" *ngIf="
                    formCreate.get('serviceName')?.errors?.pattern &&
                    formCreate.get('serviceName')?.touched
                  ">{{
                  "error.configTransaction.pattern.servicePackage" | translate
                  }}</small>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>{{ "model.configTransaction.form.startDate" | translate
                    }}<span class="text-danger">*</span></label>
                  <mat-form-field appearance="fill" class="date-picker">
                    <input matInput formControlName="startDate" [ngxMatDatetimePicker]="picker"
                      (dateChange)="onChangeStartTime($event)" [placeholder]="DATE_CONSTANT.HHMMSSDDMMYYYY_SLASH_BIG"
                      [min]="
                        minDateStart
                          | date : DATE_CONSTANT.YYYYMMDD_HYPHEN_SMALL
                      " max="{{
                        maxDateStart
                          | date : DATE_CONSTANT.YYYYMMDD_HYPHEN_SMALL
                      }}" readonly />
                    <mat-datepicker-toggle matSuffix [for]="$any(picker)"></mat-datepicker-toggle>
                    <ngx-mat-datetime-picker #picker> </ngx-mat-datetime-picker>
                  </mat-form-field>
                  <small class="text-danger" *ngIf="
                      formCreate.get('startDate')?.errors?.required &&
                      formCreate.get('startDate')?.touched
                    ">{{
                    "error.configTransaction.required.startDate" | translate
                    }}</small>
                  <small class="text-danger" *ngIf="
                      formCreate.get('startDate')?.errors?.isValidStartTime &&
                      formCreate.get('startDate')?.touched
                    ">{{
                    "error.configTransaction.dateTime.startDateTime"
                    | translate
                    }}</small>
                  <small class="text-danger" *ngIf="
                      formCreate.get('startDate')?.errors?.isValidStartHour &&
                      formCreate.get('startDate')?.touched
                    ">{{
                    "error.configTransaction.dateTime.startDateTime"
                    | translate
                    }}</small>
                  <small class="text-danger" *ngIf="
                      formCreate.get('startDate')?.errors?.dateEqual &&
                      formCreate.get('startDate')?.touched
                    ">{{
                    "error.configTransaction.dateTime.dateEqual" | translate
                    }}</small>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>{{ "model.configTransaction.form.endDate" | translate
                    }}<span class="text-danger">*</span></label>
                  <mat-form-field appearance="fill" class="date-picker">
                    <input matInput formControlName="endDate" [ngxMatDatetimePicker]="picker1"
                      (dateChange)="onChangeEndTime($event)" [placeholder]="DATE_CONSTANT.HHMMSSDDMMYYYY_SLASH_BIG"
                      [min]="
                        minDateEnd | date : DATE_CONSTANT.YYYYMMDD_HYPHEN_SMALL
                      " readonly />
                    <mat-datepicker-toggle matSuffix [for]="$any(picker1)"></mat-datepicker-toggle>
                    <ngx-mat-datetime-picker #picker1>
                    </ngx-mat-datetime-picker>
                  </mat-form-field>
                  <small class="text-danger" *ngIf="
                      formCreate.get('endDate')?.errors?.required &&
                      formCreate.get('endDate')?.touched
                    ">{{
                    "error.configTransaction.required.endDate" | translate
                    }}</small>
                  <small class="text-danger" *ngIf="
                      formCreate.get('endDate')?.errors?.isValidaEndDate &&
                      formCreate.get('endDate')?.touched
                    ">{{
                    "error.configTransaction.dateTime.endDate" | translate
                    }}</small>
                  <small class="text-danger" *ngIf="
                      formCreate.get('endDate')?.errors?.inValidEndDateEqual &&
                      formCreate.get('endDate')?.touched
                    ">{{
                    "error.configTransaction.dateTime.endDateEqual"
                    | translate
                    }}</small>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label>{{ "model.managePayment.currency" | translate }}<span class="text-danger">*</span></label>
                  <ng-select placeholder="{{ 'model.managePayment.currency' | placeholder : 'select' }}" appAutoValidate
                    [searchable]="false" formControlName="currency" [clearable]="false" (change)="getCurrency($event)">
                    <ng-option [value]="item.code" *ngFor="let item of currencies">
                      {{ item.code + '' | translate }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
              <div class="col-md-8">
                <span class="d-block mb-1 sector-header">{{
                  "model.configTransaction.form.sectorType" | translate
                  }}:</span>
                <span class="d-block mb-1 sector-error">{{ "model.configTransaction.form.sector" | translate
                  }}<small class="text-danger">*</small></span>
                <small class="text-danger d-block" *ngIf="
                    formCreate.get('sectorId')?.errors?.required &&
                    formCreate.get('sectorId')?.touched
                  ">{{
                  "error.configTransaction.required.sectorType" | translate
                  }}</small>
                <div class="row col-md-6">
                  <div class="col-md-3">
                    <div class="form-check">
                      <input (change)="getSector($event)" class="form-check-input" type="radio" name="sectorId"
                        id="sector" [value]="SECTOR_1890" />
                      <label class="form-check-label" for="sector">
                        {{ this.TRANSACTION_SECTOR_CONST.SECTOR_1890.value }}
                      </label>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-check">
                      <input (change)="getSector($event)" class="form-check-input" type="radio" name="sectorId"
                        id="sector" [value]="SECTOR_1891" />
                      <label class="form-check-label" for="sector">
                        {{ this.TRANSACTION_SECTOR_CONST.SECTOR_1891.value }}
                      </label>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-check">
                      <input (change)="getSector($event)" class="form-check-input" type="radio" name="sectorId"
                        id="sector" [value]="SECTOR_1740" />
                      <label class="form-check-label" for="sector">
                        {{ this.TRANSACTION_SECTOR_CONST.SECTOR_1740.value }}
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <label class="mt-2 label-maxmoney">{{ "model.configTransaction.form.maxMoney" | translate
                }}<span class="text-danger">*</span></label>
              <div class="col-md-12">
                <div class="row mb-4">
                  <div class="col-lg-4 col-md-6">
                    <div class="form-group form-container-money">
                      <input trim type="text" class="form-control-money" formControlName="maxTransferAmtOfDay"
                        placeholder="{{
                          'model.configTransaction.placeholder.transactionDay'
                            | translate
                        }}" [maxLength]="VALIDATORS.LENGTH.MONEY_MAX_LENGTH" separator />
                      <div class="currentcy">
                        <span *ngIf="currency">{{currency}}/</span><span>{{"common.day" | translate}}</span><small
                          class="text-danger" *ngIf="isSector1891 || isSector1890 || isSector1740">*</small>
                      </div>
                    </div>
                    <small class="text-danger" *ngIf="
                        formCreate.get('maxTransferAmtOfDay')?.errors
                          ?.invalidMoneyDay &&
                        formCreate.get('maxTransferAmtOfDay')?.touched
                      ">{{
                      "error.configTransaction.invalidMoney.transDay"
                      | translate
                      }}</small>
                  </div>
                  <div class="col-lg-4 col-md-6">
                    <div class="form-group form-container-money">
                      <input trim separator type="text" class="form-control-money"
                        formControlName="maxTransferAmtOfMonth" placeholder="{{
                          isSectorSelected || isSector1890 || isSector1740
                            ? ('model.configTransaction.placeholder.transactionMonthNo'
                              | translate)
                            : ('model.configTransaction.placeholder.transactionMonth'
                              | translate)
                        }}" [maxLength]="VALIDATORS.LENGTH.MONEY_MAX_LENGTH" />
                      <div class="currentcy">
                        <span *ngIf="currency">{{currency}}/</span><span>{{"common.month" | translate}}</span> <small
                          class="text-danger" *ngIf="isSector1891">*</small>
                      </div>
                    </div>
                    <small class="text-danger" *ngIf="
                        formCreate.get('maxTransferAmtOfMonth')?.errors
                          ?.invalidMoneyMonth &&
                        formCreate.get('maxTransferAmtOfMonth')?.touched
                      ">{{
                      "error.configTransaction.invalidMoney.transMonth"
                      | translate
                      }}</small>
                  </div>
                  <div class="col-lg-4 col-md-6">
                    <div class="form-group form-container-money">
                      <input trim type="text" class="form-control-money" formControlName="maxTransferAmtOfYear"
                        placeholder="{{
                          'model.configTransaction.placeholder.transactionYear'
                            | translate
                        }}" separator [maxLength]="VALIDATORS.LENGTH.MONEY_MAX_LENGTH" />
                      <div class="currentcy">
                        <span *ngIf="currency">{{currency}}/</span><span>{{"common.year" | translate}}</span>
                      </div>
                    </div>
                    <small class="text-danger" *ngIf="
                        formCreate.get('maxTransferAmtOfYear')?.errors
                          ?.invalidMoneyYear &&
                        formCreate.get('maxTransferAmtOfYear')?.touched
                      ">{{
                      "error.configTransaction.invalidMoney.transYear"
                      | translate
                      }}</small>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-9 table-create">
              <h4>
                {{ "model.configTransaction.tableCreateTrans" | translate }}
              </h4>

              <form [formGroup]="formArray" class="table-responsive mt-3">
                <table class="table" formArrayName="arrayForm">
                  <thead>
                    <tr>
                      <th [width]="'50px'" class="tex-center">
                        {{ "model.configTransaction.no" | translate }}
                      </th>
                      <th [width]="'250px'">
                        <span>
                          {{
                          "model.configTransaction.transactionType"
                          | translate
                          }}</span>
                      </th>
                      <th class="text-center">
                        {{
                        "model.configTransaction.maxMoneyTrans" | translate
                        }} <span *ngIf="currency">({{currency}})</span> <small class="text-danger">*</small>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="
                        let item of getFormGroupArray().controls;
                        let i = index
                      " [formGroupName]="i">
                      <td class="text-center">{{ fillIndexItem(i) }}</td>
                      <td [width]="'350px'">
                        {{ item.get("label")?.value | translate }}
                      </td>
                      <td>
                        <input type="text" trim class="form-control w-100 max-transfer"
                          formControlName="maxTransferAmount" separator
                          [maxLength]="VALIDATORS.LENGTH.MONEY_MAX_LENGTH" />
                        <small class="text-danger" *ngIf="
                            item.get('maxTransferAmount')?.errors?.required &&
                            item.get('maxTransferAmount')?.touched
                          ">{{
                          "error.configTransaction.required.transMoney"
                          | translate
                          }}</small>
                        <small class="text-danger" *ngIf="
                            item.get('maxTransferAmount')?.errors
                              ?.invalidMoneyTrans &&
                            item.get('maxTransferAmount')?.touched
                          ">{{
                          "error.configTransaction.invalidMoney.transMoney"
                          | translate
                          }}</small>
                        <small class="text-danger" *ngIf="
                            item.get('maxTransferAmount')?.errors
                              ?.invalidMoneyTransNone &&
                            item.get('maxTransferAmount')?.touched
                          ">{{
                          "error.configTransaction.invalidMoney.transMoneyNone"
                          | translate
                          }}</small>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </form>
            </div>
          </div>
        </div>
        <div class="d-block text-center pb-4 mt-4">
          <button type="button" class="btn btn-white mr-3" (click)="back()">
            {{ "common.action.back" | translate }}
          </button>
          <ng-container *hasPrivileges="SYSTEM_RULES.TRANSACTION_LIMIT_CREATE">
            <button type="button" class="btn btn-red" (click)="onCreate()">
              {{ "common.action.create" | translate }}
            </button>
          </ng-container>
        </div>
      </div>
    </form>
  </div>
</div>