<div class="modal-content">
  <!-- <div class="modal-header" [ngClass]="isHiddenBtnClose ? 'border-0' : ''">
    <h3 class="modal-title">{{title | translate}}</h3>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"
      (click)="activeModal.close(MODAL_ACTION.CLOSE.code)">
      <span aria-hidden="true">&times;</span>
    </button>
  </div> -->
  <div class="modal-header" [ngClass]="isHiddenBtnClose ? 'border-0' : ''">
    <h5 class="modal-title">{{ title | translate }}</h5>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"
      (click)="activeModal.close(MODAL_ACTION.CLOSE.code)">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <span class="preventLongTextFix" [innerHTML]="showContent() | translate"></span>
  </div>
  <div class="modal-body">
    <form [formGroup]="userReasonForm">
      <ng-container *ngIf="showSendSmsAndOtp()">
        <div class="row" *ngIf="timeLeft">
          <div class="col-md-12">
            <label class="mb-3 mr-2">{{
              "model.premiumAccountNumber.otpExpried" | translate
              }}</label>
            <span class="countdown-timer">{{ timeLeft }}</span>
          </div>
        </div>
      </ng-container>

      <div class="row" *ngIf="showSendSmsAndOtp()">
        <div class="col-12 mb-3">
          <div [innerHTML]="
              displayContent(
                'customerRegisterManagement.customerCreate.warningPremiumAcc'
              )
            "></div>
        </div>
      </div>
      <div class="input mb-3">
        <div class="row" *ngIf="showSendSmsAndOtp()">
          <div class="col-4">
            <div class="form-group">
              <input formControlName="otp" numbersOnly [maxLength]="VALIDATORS.LENGTH.OTP_MAX_LENGTH" type="text"
                class="form-control" placeholder="{{ 'model.customer.otp' | translate }}" />
              <small class="form-text text-danger noti-small" *ngIf="
                  userReasonForm.get('otp')?.errors?.required &&
                  userReasonForm.get('otp')?.touched
                ">
                {{ "premiumAccNumber.error.required.otpValue" | translate }}
              </small>
              <small class="form-text text-danger noti-small" *ngIf="
                  userReasonForm.get('otp')?.errors?.minlength &&
                  userReasonForm.get('otp')?.touched
                ">
                {{ "premiumAccNumber.error.minLength.otp" | translate }}
              </small>
            </div>
          </div>
          <div class="col-2">
            <button [disabled]="timeLeft" type="button" id="buttonSend" class="btn btn-danger sms"
              (click)="apiSendSms()">
              {{ "model.customer.buttonSms" | translate }}
            </button>
          </div>
        </div>

        <div class="form-group position-relative">
          <label>{{ "common.reason" | translate
            }}<span *ngIf="isDeny" class="text-danger">*</span></label>
          <input trim #currentPassword [type]="'text'" class="w-100" formControlName="reason" class="form-control"
            placeholder="{{ 'common.reason' | translate }}" />
        </div>
        <small class="form-text text-danger noti-small" *ngIf="
            userReasonForm.get('reason')?.errors?.required &&
            userReasonForm.get('reason')?.touched
          ">
          {{ "error.user.required.reason" | translate }}
        </small>
        <small class="form-text text-danger noti-small" *ngIf="
            userReasonForm.get('reason')?.errors?.maxlength &&
            userReasonForm.get('reason')?.touched
          ">
          {{ "error.user.maxLength.reason" | translate }}
        </small>
      </div>
    </form>
  </div>
  <div class="text-center pb-4">
    <button type="button" class="btn btn-secondary mr-3" (click)="activeModal.close()">
      {{ "common.action.close" | translate }}
    </button>
    <button type="button" class="btn btn-danger approval" id="buttonApproval" (click)="isDeny ? deny() : approval()">
      {{ "common.action.confirm" | translate }}
    </button>
  </div>
</div>