import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IFeeRate } from '@shared/models/IFeeRate.model';
import { IFeeRateSearch } from '@shared/models/request/fee-rate.search';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class FeeRateService extends AbstractService {
  public resourceUrl = '/fee-rate';

  constructor(protected http: HttpClient) {
    super(http);
  }

  search(request: IFeeRateSearch): Observable<EntityResponseType<IFeeRate>> {
    return super.post<EntityResponseType<IFeeRate>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  /**
   * API delete
   *
   * @param request IEvent
   * @returns Observable<EntityResponseType<IFeeRate>>
   */
  deleteFeeRate(request: any): Observable<EntityResponseType<IFeeRate>> {
    return super.post<EntityResponseType<IFeeRate>>(
      `${this.resourceUrl}/delete`,
      request
    );
  }

  /**
   * API create
   *
   * @param request IEvent
   * @returns Observable<EntityResponseType<IFeeRate>>
   */
  create(request: IFeeRate): Observable<EntityResponseType<IFeeRate>> {
    return super.post<EntityResponseType<IFeeRate>>(
      `${this.resourceUrl}/create`,
      request
    );
  }

  /**
   * API create
   *
   * @param request IFeeRate
   * @returns Observable<EntityResponseType<IFeeRate>>
   */
  update(request: IFeeRate): Observable<EntityResponseType<IFeeRate>> {
    return super.post<EntityResponseType<IFeeRate>>(
      `${this.resourceUrl}/update`,
      request
    );
  }

  /**
   * API get detail
   *
   * @param request
   */
  detail(feeRateId: any): Observable<EntityResponseType<IFeeRate>> {
    return super.post<EntityResponseType<IFeeRate>>(
      `${this.resourceUrl + '/' + feeRateId + '/detail'}`
    );
  }

  /**
   * API lock
   *
   * @param request IFeeRate
   * @returns Observable<EntityResponseType<IFeeRate>>
   */
  lock(request: any): Observable<EntityResponseType<IFeeRate>> {
    return super.post<EntityResponseType<IFeeRate>>(
      `${this.resourceUrl}/inactive`,
      request
    );
  }

  /**
   * API unlock
   *
   * @param request IFeeRate
   * @returns Observable<EntityResponseType<IFeeRate>>
   */
  unlock(request: any): Observable<EntityResponseType<IFeeRate>> {
    return super.post<EntityResponseType<IFeeRate>>(
      `${this.resourceUrl}/active`,
      request
    );
  }
}
