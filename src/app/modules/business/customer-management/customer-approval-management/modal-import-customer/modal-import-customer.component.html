<div class="modal-content">
  <div class="modal-header">
    <h5 class="modal-title">
      {{ "common.action.uploadFiles" | translate }}
    </h5>
    <h5 class="modal-title"></h5>
    <button
      type="button"
      class="close"
      data-dismiss="modal"
      aria-label="Close"
      (click)="activeModal.close()"
    >
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="formImport">
      <div class="col-md-12 mb-2">
        <label class="mb-3 mr-2">{{
          "common.action.download-template" | translate
        }}</label>
        <button
          class="btn btn-white download"
          type="button"
          (click)="downloadTemplateImport()"
        >
          <i class="fa fa-download"> </i>
          {{ "common.action.download" | translate }}
        </button>
        <br />
      </div>
      <label>{{ "common.action.uploadFiles" | translate }}</label>
      <div class="col-md-12 mb-2">
        <div class="row">
          <div class="file-upload d-flex align-items-center">
            <label class="label-upload" for="file-input">
              <i class="fas fa-cloud-upload-alt fa-style"></i
              >{{ "common.chooseFile" | translate }}
            </label>
            <div class="file-input-container">
              <input
                id="file-input"
                type="file"
                (change)="onFileSelected($event)"
                formControlName="uploadFile"
                accept=".xlsx, .xls"
                #fileInput
              />
              <div id="file-name" [class.active]="selectedFileName">
                {{ selectedFileName }}
              </div>
            </div>
            <button
              *ngIf="selectedFileName"
              ngbTooltip="{{ 'common.action.delete' | translate }}"
              class="btn px-1 py-0"
              (click)="deleteFile()"
            >
              <i class="fa fa-trash mb-color" aria-hidden="true"></i>
            </button>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="text-center pb-4">
    <button
      type="button"
      class="btn mb-btn-outline-color mr-3"
      (click)="activeModal.close()"
    >
      {{ "common.action.close" | translate }}
    </button>
    <ng-container>
      <button
        type="button"
        class="btn mb-btn-color"
        [disabled]="formImport.invalid"
        (click)="importFile()"
        *hasPrivileges="SYSTEM_RULES.CUSTOMER_IMPORT"
      >
        {{ "common.action.uploadFile" | translate }}
      </button>
    </ng-container>
  </div>
</div>
