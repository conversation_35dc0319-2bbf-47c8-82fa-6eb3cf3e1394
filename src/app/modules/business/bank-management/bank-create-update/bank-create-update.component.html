<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="backToList()" />

      <h5>
        {{
          isUpdate
            ? ("sidebar.bank.update" | translate)
            : ("sidebar.bank.create" | translate)
        }}
      </h5>
    </div>
    <!-- <form [formGroup]="form" *ngIf="customer?.customerId"></form> -->
    <form [formGroup]="formBank" *ngIf="isUpdate ? bank?.bankId : !isUpdate">
      <div class="col-md-12">
        <div class="row">
          <h2 class="title-create">
            {{
              (isUpdate ? "bank.updateTitle" : "bank.createTitle") | translate
            }}
          </h2>
        </div>
        <div class="row border-create">
          <h3>{{ "bank.titleInforBank" | translate }}</h3>
          <div class="col-3">
            <div class="col-12">
              <label
                >{{ "common.image" | translate
                }}<span class="text-danger">*</span></label
              >
              <div class="bank-upload">
                <app-upload-images
                  (imageUploaded)="onUploadPics($event)"
                  [fileRequired]="fileRequired"
                  [fileExtension]="FILE_UPLOAD_EXTENSIONS.ICON"
                  (imageRemoved)="onRemoveImage($event)"
                  [imageUrls]="imageUrls"
                  [type]="
                    bankDTO.bankId
                      ? ROUTER_ACTIONS.update
                      : ROUTER_ACTIONS.create
                  "
                ></app-upload-images>
              </div>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formBank.get('files')?.errors?.required &&
                  formBank.get('files')?.touched
                "
              >
                {{ "error.bank.required.files" | translate }}
              </small>
            </div>
          </div>
          <div class="col-9">
            <div class="col-md-12">
              <div class="row">
                <div class="col-md-4">
                  <div class="form-group">
                    <label
                      >{{ "model.bank.codeNumber" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      appAutoValidate
                      [customiseErrorMessage]="
                        validateCustomMessages.bankCodeNumber
                      "
                      numbersOnly
                      formControlName="bankCodeNumber"
                      type="text"
                      class="w-100"
                      class="form-control"
                      placeholder="{{ 'model.bank.codeNumber' | placeholder }}"
                      [maxLength]="
                        VALIDATORS.LENGTH.BANK_CODE_NUMBER_MAX_LENGTH
                      "
                    />
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <label
                      >{{ "model.bank.code" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      appAutoValidate
                      [customiseErrorMessage]="validateCustomMessages.bankCode"
                      trim
                      formControlName="bankCode"
                      type="text"
                      class="w-100"
                      class="form-control"
                      placeholder="{{ 'model.bank.code' | placeholder }}"
                      [maxLength]="VALIDATORS.LENGTH.BANK_CODE_MAX_LENGTH"
                    />
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <label
                      >{{ "model.bank.order" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      numbersOnly
                      appAutoValidate
                      formControlName="order"
                      type="text"
                      class="w-100"
                      class="form-control"
                      placeholder="{{ 'model.bank.order' | placeholder }}"
                      [maxLength]="VALIDATORS.LENGTH.BANK_ORDER_MAX_LENGTH"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-12">
              <div class="row">
                <div class="form-group col-md-4">
                  <label
                    >{{ "common.nation" | translate }}
                    <span class="text-danger">*</span></label
                  >
                  <ng-select
                    appAutoValidate
                    appearance="outline"
                    [searchable]="false"
                    [clearable]="true"
                    formControlName="nation"
                    placeholder="{{ 'common.nation' | translate }}"
                  >
                    <ng-option
                      [value]="item.code"
                      *ngFor="let item of NATION_CODES"
                    >
                      {{ item.label | translate }}
                    </ng-option>
                  </ng-select>
                </div>
                <div class="col-md-8">
                  <div class="form-group">
                    <label
                      >{{ "model.bank.name" | translate }}
                      <span class="text-danger">*</span></label
                    >
                    <input
                      trim
                      appAutoValidate
                      formControlName="bankName"
                      type="text"
                      class="w-100"
                      class="form-control"
                      placeholder="{{ 'model.bank.name' | placeholder }}"
                      [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="d-block text-center mb-5 mt-4">
          <button
            class="btn btn-white mr-2"
            data-toggle="modal"
            (click)="backToList()"
            type="button"
          >
            {{ "common.action.back" | translate }}
          </button>
          <button
            class="btn btn-red"
            (click)="isUpdate ? onUpdate() : onCreate()"
            *hasPrivileges="
              isUpdate ? SYSTEM_RULES.BANK_WRITE : SYSTEM_RULES.BANK_CREATE
            "
          >
            {{
              (isUpdate ? "common.action.update" : "common.action.create")
                | translate
            }}
          </button>
        </div>
      </div>
    </form>
  </div>
</section>
