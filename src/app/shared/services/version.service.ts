import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IVersionSearch } from '@shared/models/request/version.search';
import { IVersion } from '@shared/models/version.model';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class VersionService extends AbstractService {
  public resourceUrl = '/version';

  constructor(protected http: HttpClient) {
    super(http);
  }
  search(request: any): Observable<EntityResponseType<IVersion[]>> {
    return super.post<EntityResponseType<IVersion[]>>(
      `${this.resourceUrl}/search`,
      request
    );
  }
  create(request: IVersion): Observable<EntityResponseType<IVersion>> {
    return super.post<EntityResponseType<IVersion>>(
      `${this.resourceUrl}/create`,
      request
    );
  }
  update(request: IVersion): Observable<EntityResponseType<IVersion>> {
    return super.post<EntityResponseType<IVersion>>(
      `${this.resourceUrl}/update`,
      request
    );
  }
  getValue(
    request: IVersionSearch
  ): Observable<EntityResponseType<IVersion[]>> {
    return super.get<EntityResponseType<IVersion[]>>(
      `${this.resourceUrl}/search`,
      request
    );
  }
  exportVersion(request: IVersion): Observable<EntityResponseType<IVersion>> {
    return super.postFile(`${this.resourceUrl}/export`, request);
  }
}
