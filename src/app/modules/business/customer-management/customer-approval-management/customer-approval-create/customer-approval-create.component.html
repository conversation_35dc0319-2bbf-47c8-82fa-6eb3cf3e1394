<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="onBack()" />
      <h5>
        {{
          customerInfo.approvalStatus === APPROVAL_STATUS_CONST.PENDING.value ||
          customerInfo.approvalStatus ===
            APPROVAL_STATUS_CONST.APPROVAL_ERROR.value
            ? ("customerRegisterManagement.customerApproval.approvalTitle"
              | translate)
            : ("customerRegisterManagement.customerApproval.detailTitle"
              | translate)
        }}
      </h5>
    </div>
    <div class="col-md-12">
      <div class="row">
        <h2 class="title-create">
          {{
            "customerRegisterManagement.customerApproval.request.title"
              | translate
          }}
        </h2>
      </div>
      <app-customer-info [customerInfo]="customerInfo"> </app-customer-info>
      <div class="d-block text-center mb-5 mt-4">
        <!-- <button class="btn btn-white mr-2" *ngIf="isUpdate">
              {{ "common.action.update" | translate }}
            </button> -->
        <button class="btn btn-red mr-2" (click)="onBack()">
          {{ "common.action.back" | translate }}
        </button>
        <ng-container *hasPrivileges="SYSTEM_RULES.CUSTOMER_CREATE_APPROVAL">
          <ng-container
            *ngIf="
              customerInfo.approvalStatus ===
                APPROVAL_STATUS_CONST.PENDING.value ||
              customerInfo.approvalStatus ===
                APPROVAL_STATUS_CONST.APPROVAL_ERROR.value
            "
            ><button class="btn btn-white mr-2" (click)="deny()">
              {{ "common.action.deny" | translate }}
            </button>
            <button class="btn btn-red" (click)="approval()">
              {{ "common.action.approval" | translate }}
            </button></ng-container
          >
        </ng-container>
      </div>
    </div>
  </div>
</section>
