import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  ENTITY_STATUS,
  ENTITY_STATUS_MAP,
  MODAL_ACTION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { MODEL_MAP_ITEM_COMMON } from '@shared/models/model.type';
import { IPosition, Position } from '@shared/models/position.model';
import { AccountService } from '@shared/services/account.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { PositionService } from '@shared/services/position.service';
import { ROUTER_ACTIONS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-position-create-update',
  templateUrl: './position-create-update.component.html',
  styleUrls: ['./position-create-update.component.scss'],
})
export class PositionCreateUpdateComponent implements OnInit {
  formPosition: FormGroup = new FormGroup({});

  position: IPosition = {};
  positionDTO: IPosition = {};
  interpolateParams: object = {};
  title = 'common.action.confirm';
  content = '';
  action = '';
  positionId: any;
  isHiddenBtnClose = false;
  // public action!: MODEL_MAP_ITEM_COMMON;

  actionConfirm!: MODEL_MAP_ITEM_COMMON;
  VALIDATORS = VALIDATORS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  ENTITY_STATUS = ENTITY_STATUS;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  MODAL_ACTION = MODAL_ACTION;
  SYSTEM_RULES = SYSTEM_RULES;

  initForm(position?: Position): void {
    this.formPosition = this.fb.group({
      positionName: [
        position?.positionName || '',
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.PATTERN_NAME),
          Validators.minLength(VALIDATORS.LENGTH.POSITION_NAME_MIN_LENGTHG),
          Validators.maxLength(VALIDATORS.LENGTH.POSITION_NAME_MAX_LENGTH),
        ],
      ],
      shortName: [
        position?.shortName || '',
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.PATTERN_NAME),
          Validators.minLength(
            VALIDATORS.LENGTH.POSITION_SHORT_NAME_MIN_LENGTH
          ),
          Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
        ],
      ],
      positionCode: [
        position?.positionCode || '',
        [
          Validators.required,
          Validators.maxLength(VALIDATORS.LENGTH.POSITION_CODE_MAX_LENGTH),
        ],
      ],
      description: [
        position?.description || '',
        [Validators.maxLength(VALIDATORS.LENGTH.MEDIUM_TEXT_MAX_LENGTH)],
      ],
      status: position?.status || 0,
      createdDate: position?.createdDate || '',
      userCreated: position?.userCreated || '',
      lastModifiedDate: position?.lastModifiedDate || '',
      userLastModified: position?.userLastModified || '',
      // status: [position?.status || ENTITY_STATUS_CONST.ACTIVE.code],
    });
    if (this.action === ROUTER_ACTIONS.detail) {
      this.formPosition.disable();
    }
  }

  constructor(
    public activeModal: NgbActiveModal,
    public translateService: TranslateService,
    private fb: FormBuilder,
    private translate: TranslateService,
    private accountService: AccountService,
    private toastService: ToastrCustomService,
    private positionService: PositionService
  ) {}

  ngOnInit(): void {
    if (this.action === ROUTER_ACTIONS.create) {
      this.initForm();
    } else {
      this.getDetail();
    }
  }

  // ngOnDestroy(): void {
  //   this.activeModal.close();
  // }

  // /**
  //  * catch event back
  //  */
  // @HostListener('window:popstate', ['$event'])
  // backToListPage() {
  //   this.activeModal.close();
  // }

  /**
   * displayContent: display content in file i18n
   *
   * @param content string
   * @returns string
   */
  displayContent(content: string): string {
    return this.translate.instant(content);
  }

  getDetail(): void {
    if (this.positionId) {
      this.positionDTO.positionId = this.positionId;
      this.positionService.detail(this.positionDTO).subscribe((res: any) => {
        this.position = res.body;
        const data = res.body || undefined;
        this.initForm(data);
      });
    }
  }

  onCreate(): void {
    if (this.formPosition.invalid) {
      CommonUtils.markFormGroupTouched(this.formPosition);
    }
    const params = this.formPosition.getRawValue();
    if (this.formPosition.valid) {
      this.positionService.create(params).subscribe((res: any) => {
        this.toastService.success('common.action.createSuccess');
        this.activeModal.close(this.actionConfirm.code);
      });
    }
  }

  onUpdate(): void {
    if (this.formPosition.invalid) {
      CommonUtils.markFormGroupTouched(this.formPosition);
    }
    const params = this.formPosition.getRawValue();
    if (this.formPosition.valid) {
      params.positionId = this.position.positionId;
      this.positionService.update(params).subscribe((res: any) => {
        this.toastService.success('common.action.updateSuccess');
        this.activeModal.close(this.actionConfirm.code);
      });
    }
  }
}
