import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IActiveToken } from '@shared/models/active-token.model';
import { AccountService } from '@shared/services/account.service';
import { AuthProviderService } from '@shared/services/auth/auth-provider.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { encrypt, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import Validation from '@shared/validators/confirmed-password.validator';

@Component({
  selector: 'app-change-password-expire',
  templateUrl: './change-password-expire.component.html',
  styleUrls: ['./change-password-expire.component.scss'],
})
export class ChangePasswordExpireComponent implements OnInit {
  passwordVisible = false;

  confirmPasswordVisible = false;

  currentPasswordVisible = false;

  userChangePasswordForm = this.fb.group(
    {
      currentPassword: ['', [Validators.required]],
      password: [
        '',
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.PASSWORD),
          Validators.minLength(VALIDATORS.LENGTH.PASSWORD_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.PASSWORD_MAX_LENGTH),
        ],
      ],
      confirmPassword: [
        '',
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.PASSWORD),
          Validators.minLength(VALIDATORS.LENGTH.PASSWORD_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.PASSWORD_MAX_LENGTH),
        ],
      ],
      token: [''],
    },
    {
      validators: [Validation.match('password', 'confirmPassword')],
    }
  );

  // Kiểm tra mật khẩu khác nhau hay không
  get f(): { [key: string]: AbstractControl } {
    return this.userChangePasswordForm.controls;
  }

  VALIDATORS = VALIDATORS;

  activeTokenDTO: IActiveToken = {};

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private translate: TranslateService,
    private toastService: ToastrCustomService,
    private accountService: AccountService,
    private activatedRoute: ActivatedRoute,
    private authProviderService: AuthProviderService
  ) {}

  ngOnInit(): void {
    // get token from paramater
    this.activatedRoute.paramMap.subscribe((res) => {
      const token = res.get('token');
      if (token) {
        this.activeTokenDTO.token = token;
      }
    });
  }

  /**
   * onSetUpPassword
   */
  onSetUpPassword(): void {
    const { password, confirmPassword, currentPassword } =
      this.userChangePasswordForm.value;
    if (this.userChangePasswordForm.invalid) {
      CommonUtils.markFormGroupTouched(this.userChangePasswordForm);
    }
    const params = this.userChangePasswordForm.getRawValue();
    if (this.userChangePasswordForm.valid) {
      // get token from param
      params.token = this.activeTokenDTO.token;
      // encrypt password
      params.currentPassword = encrypt(currentPassword) + '';
      params.password = encrypt(password) + '';
      params.confirmPassword = encrypt(confirmPassword) + '';
      this.accountService.changePasswordUser(params).subscribe((res: any) => {
        this.toastService.success('common.action.changePasswordSuccess');
        this.authProviderService.logout();
        this.router.navigate([ROUTER_UTILS.auth.login]);
      });
    }
  }

  /**
   * displayContent: display content in file i18n
   *
   * @param content string
   * @returns string
   */
  displayContent(content: string): string {
    return this.translate.instant(content);
  }

  /**
   * Change type view password
   */
  onChangePasswordVisible(): void {
    this.passwordVisible = !this.passwordVisible;
  }

  /**
   * Change type view password
   */
  onChangeConfirmPasswordVisible(): void {
    this.confirmPasswordVisible = !this.confirmPasswordVisible;
  }

  /**
   * Change type view password
   */
  onChangeCurrentPasswordVisible(): void {
    this.currentPasswordVisible = !this.currentPasswordVisible;
  }
}
