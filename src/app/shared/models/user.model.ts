import { IRole } from './role.model';

export interface IUser {
  userId?: number;
  username?: string;
  fullname?: string;
  dateOfBirth?: any;
  gender?: number;
  email?: string;
  phoneNumber?: number;
  transactionId?: number;
  status?: number;
  description?: string;
  password?: string;
  active?: boolean;
  departmentId?: number;
  positionId?: number;
  roleIds?: [];
  roles?: IRole[];
  actionToken?: string;
  departmentName?: string;
  positionName?: string;
  roleName?: string;
}

export interface IUserStorage {
  email?: string;
  fullname?: string;
  maxRoleLevel?: number;
  username?: string;
}

export class UserStorage {
  constructor(
    public maxRoleLevel?: number,
    public email?: string,
    public username?: string,
    public fullname?: string
  ) {
    this.maxRoleLevel = maxRoleLevel;
    this.email = email;
    this.username = username;
    this.fullname = fullname;
  }
}

export class User implements IUser {
  constructor(
    public userId?: number,
    public username?: string,
    public fullname?: string,
    public dateOfBirth?: any,
    public gender?: number,
    public email?: string,
    public phoneNumber?: number,
    public transactionId?: number,
    public status?: number,
    public description?: string,
    public password?: string,
    public active?: boolean,
    public departmentId?: number,
    public positionId?: number,
    public roleIds?: [],
    public roles?: IRole[],
    public actionToken?: string,
    public departmentName?: string,
    public positionName?: string
  ) {
    this.userId = userId;
    this.username = username;
    this.fullname = fullname;
    this.dateOfBirth = dateOfBirth;
    this.gender = gender;
    this.email = email;
    this.status = status;
    this.description = description;
    this.phoneNumber = phoneNumber;
    this.transactionId = transactionId;
    this.password = password;
    this.active = active;
    this.departmentId = departmentId;
    this.positionId = positionId;
    this.roleIds = roleIds;
    this.roles = roles;
    this.actionToken = actionToken;
    this.departmentName = departmentName;
    this.positionName = positionName;
  }
}
