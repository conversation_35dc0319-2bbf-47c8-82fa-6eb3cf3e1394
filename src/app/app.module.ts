import {
  HTTP_INTERCEPTORS,
  HttpClient,
  HttpClientModule,
} from '@angular/common/http';
import { NgModule } from '@angular/core';
import { MatPaginatorIntl } from '@angular/material/paginator';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { IconDefinition } from '@ant-design/icons-angular';
import {
  CaretLeftOutline,
  DeleteOutline,
  SettingOutline,
  StepBackwardOutline,
} from '@ant-design/icons-angular/icons';
import { AuthModule } from '@auth/auth.module';
import { AuthInterceptor } from '@core/interceptors/auth.interceptor';
import { HandlerInterceptor } from '@core/interceptors/handler.interceptor';
import { LoadingInterceptor } from '@core/interceptors/loading.interceptor';
import { PageLoaderComponent } from '@core/layouts/page-loader/page-loader.component';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { BreadcrumbService } from '@shared/services/helpers/breadcrumb.service';
import { MatPaginationIntlService } from '@shared/services/helpers/mat-pagination-intl.service';
import { SharedModule } from '@shared/shared.module';
import { NZ_I18N, en_US } from 'ng-zorro-antd/i18n';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { ToastrModule } from 'ngx-toastr';
import { NgxWebstorageModule } from 'ngx-webstorage';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { CoreModule } from './core/core.module';

// initializeApp(environment.firebase);

const icons: IconDefinition[] = [
  StepBackwardOutline,
  CaretLeftOutline,
  SettingOutline,
  DeleteOutline,
];

@NgModule({
  declarations: [AppComponent, PageLoaderComponent],
  imports: [
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: translatePartialLoader,
        deps: [HttpClient],
      },
      useDefaultLang: true,
    }),
    ToastrModule.forRoot({ timeOut: 5000, positionClass: 'toast-top-right' }),
    NgxWebstorageModule.forRoot({ prefix: '', separator: '' }),
    BrowserModule,
    BrowserAnimationsModule,
    AppRoutingModule,
    NzIconModule.forRoot(icons),
    SharedModule,
    AuthModule,
    CoreModule,
    HttpClientModule,
  ],
  providers: [
    BreadcrumbService,
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: HandlerInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: LoadingInterceptor, multi: true },
    { provide: MatPaginatorIntl, useClass: MatPaginationIntlService },
    { provide: NZ_I18N, useValue: en_US },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}

export function translatePartialLoader(http: HttpClient): TranslateLoader {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}
