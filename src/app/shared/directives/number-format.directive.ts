import {
  Directive,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  Output,
} from '@angular/core';

@Directive({ selector: '[appNumberFormatter]' })
export class NumberFormatterDirective {
  private el: any;
  @Input() maxValue = 100;
  @Output() onMaxValue: EventEmitter<any> = new EventEmitter();

  constructor(private elementRef: ElementRef) {
    this.el = this.elementRef.nativeElement;
  }

  @HostListener('keyup', ['$event.target.value'])
  onKeyUp(value: string): void {
    // tslint:disable-next-line:prefer-conditional-expression
    if (value.toString().length === 0) {
      this.el.value = null;
    } else if (+value === 0) {
      this.el.value = 0;
    } else {
      // this.el.value = this.transform(value);
    }
  }

  @HostListener('keypress', ['$event'])
  validateNumericKeyPress(event: any): void {
    const pattern = /[0-9]/;
    const inputChar = event.key;

    if (!pattern.test(inputChar)) {
      // invalid character, prevent input
      event.preventDefault();
    }
  }

  transform(value: string): string {
    return Number(value.replace(/\D/g, ''))
      .toString()
      .replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  }

  parse(value: string): string {
    return value.toString().replace(/\D/g, '');
  }

  // private pushEvent(): void {
  //   this.onMaxValue.emit({
  //     value: this.maxValue,
  //   });
  // }
}
