import { <PERSON>mpo<PERSON>, HostListener, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import {
  ENTITY_STATUS,
  FILE_UPLOAD_EXTENSIONS,
  NATION_CODES,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { Bank } from '@shared/models/bank.model';
import { IFileEntrySearch } from '@shared/models/request/file-entry.search';
import { BankService } from '@shared/services/bank.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-bank-detail',
  templateUrl: './bank-detail.component.html',
  styleUrls: ['./bank-detail.component.scss'],
})
export class BankDetailComponent implements OnInit, OnDestroy {
  // form control data
  formBank: FormGroup = new FormGroup({});
  // input call api
  bankDTO: Bank = new Bank();
  // input data form control
  bank: Bank = new Bank();
  reader: FileReader = new FileReader();
  eKycPicUrls: string[] = [];
  // input: file upload to create
  fileUploads: any[] = [];
  getFile: any[] = [];
  imageUrls: any[] = [];
  fileDelete: any[] = [];
  // default two file upload
  fileRequired: string[] = ['Logo'];

  hasFilter = false;
  isUpdate = false;

  ROUTER_UTILS = ROUTER_UTILS;
  ENTITY_STATUS = ENTITY_STATUS;
  VALIDATORS = VALIDATORS;
  FILE_UPLOAD_EXTENSIONS = FILE_UPLOAD_EXTENSIONS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  NATION_CODES = NATION_CODES;

  constructor(
    private formBuilder: FormBuilder,
    private bankService: BankService,
    private routerActive: ActivatedRoute,
    private router: Router
  ) {
    // get id form paramater
    this.routerActive.paramMap.subscribe((res) => {
      const idParam = res.get('bankId');
      if (idParam) {
        this.bankDTO.bankId = +idParam;
        this.isUpdate = true;
      }
    });
  }

  /**
   * remove storage
   */
  ngOnDestroy(): void {
    if (!this.hasFilter) {
      sessionStorage.removeItem(STORAGE_APP.BANK);
    }
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.hasFilter = true;
  }

  ngOnInit(): void {
    if (this.isUpdate) {
      this.getDetail();
    } else {
      this.initForm();
    }
  }

  /**
   * init form
   *
   * @param bank Bank
   */
  initForm(bank?: Bank): void {
    this.formBank = this.formBuilder.group({
      bankName: [bank?.bankName || ''],
      bankCode: [bank?.bankCode || ''],
      bankCodeNumber: [bank?.bankCodeNumber || null],
      status: [bank?.status || 0],
      order: [bank?.order || null],
      icon: [bank?.icon || ''],
      files: [null],
      nation: [bank?.nation || null],
    });
    this.formBank.disable();
  }

  /**
   * back to monitor list
   */
  backToList(): void {
    this.hasFilter = true;
    this.router.navigate([ROUTER_UTILS.bank.root]);
  }

  /**
   * goToUpdate
   *
   */
  goToUpdate(): void {
    if (this.bankDTO.bankId) {
      this.router.navigate([
        ROUTER_UTILS.bank.root,
        this.bankDTO.bankId,
        ROUTER_ACTIONS.update,
      ]);
    }
  }

  /**
   * get detail bank
   */
  getDetail(): void {
    if (this.bankDTO.bankId) {
      this.bankService.detail(this.bankDTO).subscribe((res: any) => {
        const data = res.body || undefined;
        if (data.icon) {
          const searchFile: IFileEntrySearch = {};
          searchFile.classPk = data.bankId;
          searchFile.fileEntryId = data.icon.id;
          searchFile.normalizeName = data.icon.normalizeName;

          this.bankService.getIcon(searchFile).subscribe((responsive: any) => {
            CommonUtils.blobToBase64(responsive.body).then((base64) => {
              this.imageUrls = [{ src: base64, name: null, id: data.icon.id }];
            });
          });
          // this.getFile = [{ scr: data.iconUrL, name: null }];
        }
        this.bank = data;
        this.initForm(data);
      });
    }
  }
}
