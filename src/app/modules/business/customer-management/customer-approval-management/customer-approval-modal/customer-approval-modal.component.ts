import { Component, HostListener, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { MODAL_ACTION } from '@shared/constants/app.constants';
import {
  APPROVAL_STATUS_CONST,
  APPROVAL_TYPE_CONST,
} from '@shared/constants/customer.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { Customer } from '@shared/models/customer.model';
import { MODEL_MAP_ITEM_COMMON } from '@shared/models/model.type';
import { IOtpPremiumAccNumber } from '@shared/models/otpPremiumAccNumber.model';
import { CustomerService } from '@shared/services/customer.service';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-customer-approval-modal',
  templateUrl: './customer-approval-modal.component.html',
  styleUrls: ['./customer-approval-modal.component.scss'],
})
export class CustomerApprovalModalComponent implements OnInit {
  public isDeny = false;
  intervalId: any;
  timeLeft?: string;
  submitConfirm = 0;

  VALIDATORS = VALIDATORS;

  customer: Customer = {};
  otpResponse: IOtpPremiumAccNumber = {};

  userReasonForm = this.fb.group({
    reason: [
      '',
      [Validators.maxLength(VALIDATORS.LENGTH.MEDIUM_TEXT_MAX_LENGTH)],
    ],
    otp: ['', [Validators.minLength(VALIDATORS.LENGTH.OTP_MAX_LENGTH)]],
  });

  public title = 'common.action.confirm';
  public content = '';
  public interpolateParams: object = {};
  public isHiddenBtnClose = false;
  public action!: MODEL_MAP_ITEM_COMMON;

  MODAL_ACTION = MODAL_ACTION;

  constructor(
    public activeModal: NgbActiveModal,
    public translateService: TranslateService,
    private fb: FormBuilder,
    private translate: TranslateService,
    private customerService: CustomerService
  ) {}

  ngOnInit(): void {
    const value = this.customer?.premiumAccountNumberCache?.premiumAccNumber;
    if (
      (this.customer.approvalStatus === APPROVAL_STATUS_CONST.PENDING.value ||
        this.customer.approvalStatus ===
          APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
      this.customer.approvalType === APPROVAL_TYPE_CONST.CREATE.value &&
      value && !this.isDeny
    ) {
      this.userReasonForm.controls.otp.setValidators([
        Validators.required,
        Validators.minLength(VALIDATORS.LENGTH.OTP_MAX_LENGTH),
      ]);
      this.userReasonForm.controls.otp.updateValueAndValidity();
    }
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.activeModal.close();
  }

  /**
   * displayContent: display content in file i18n
   *
   * @param content string
   * @returns string
   */
  displayContent(content: string): string {
    return this.translate.instant(content);
  }

  deny() {
    if (this.isDeny) {
      this.userReasonForm.controls.reason.setValidators(Validators.required);
      this.userReasonForm.controls.reason.updateValueAndValidity();
    }
    if (this.userReasonForm.invalid) {
      CommonUtils.markFormGroupTouched(this.userReasonForm);
    }
    const params = this.userReasonForm.getRawValue();
    if (this.userReasonForm.valid) {
      params.customerId = this.customer.customerId;
      if (
        (this.customer.approvalStatus === APPROVAL_STATUS_CONST.PENDING.value ||
          this.customer.approvalStatus ===
            APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
        this.customer.approvalType === APPROVAL_TYPE_CONST.LOCK.value
      ) {
        this.customerService.lockDeny(params).subscribe((res: any) => {
          this.activeModal.close(this.action.code);
          this.activeModal.close();
        });
      } else if (
        (this.customer.approvalStatus === APPROVAL_STATUS_CONST.PENDING.value ||
          this.customer.approvalStatus ===
            APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
        this.customer.approvalType === APPROVAL_TYPE_CONST.UNLOCK.value
      ) {
        this.customerService.unlockDeny(params).subscribe((res: any) => {
          this.activeModal.close(this.action.code);
          this.activeModal.close();
        });
      } else if (
        (this.customer.approvalStatus === APPROVAL_STATUS_CONST.PENDING.value ||
          this.customer.approvalStatus ===
            APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
        this.customer.approvalType === APPROVAL_TYPE_CONST.UNCLOSED.value
      ) {
        this.customerService.unclosedDeny(params).subscribe((res: any) => {
          this.activeModal.close(this.action.code);
          this.activeModal.close();
        });
      } else if (
        (this.customer.approvalStatus === APPROVAL_STATUS_CONST.PENDING.value ||
          this.customer.approvalStatus ===
            APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
        this.customer.approvalType === APPROVAL_TYPE_CONST.DELETE.value
      ) {
        this.customerService.deleteDeny(params).subscribe((res: any) => {
          this.activeModal.close(this.action.code);
          this.activeModal.close();
        });
      } else if (
        (this.customer.approvalStatus === APPROVAL_STATUS_CONST.PENDING.value ||
          this.customer.approvalStatus ===
            APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
        this.customer.approvalType === APPROVAL_TYPE_CONST.UPDATE.value
      ) {
        this.customerService.updateDeny(params).subscribe((res: any) => {
          this.activeModal.close(this.action.code);
          this.activeModal.close();
        });
      } else if (
        (this.customer.approvalStatus === APPROVAL_STATUS_CONST.PENDING.value ||
          this.customer.approvalStatus ===
            APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
        this.customer.approvalType === APPROVAL_TYPE_CONST.CREATE.value
      ) {
        this.customerService.createDeny(params).subscribe((res: any) => {
          this.activeModal.close(this.action.code);
          this.activeModal.close();
        });
      }
    }
  }

  approval() {
    if (this.isDeny) {
      this.userReasonForm.controls.reason.setValidators(Validators.required);
      this.userReasonForm.controls.reason.updateValueAndValidity();
    }
    if (this.userReasonForm.invalid) {
      CommonUtils.markFormGroupTouched(this.userReasonForm);
    }
    const params = this.userReasonForm.getRawValue();
    if (this.userReasonForm.valid) {
      params.customerId = this.customer.customerId;
      if (
        (this.customer.approvalStatus === APPROVAL_STATUS_CONST.PENDING.value ||
          this.customer.approvalStatus ===
            APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
        this.customer.approvalType === APPROVAL_TYPE_CONST.LOCK.value
      ) {
        this.customerService.lockApproval(params).subscribe((res: any) => {
          this.activeModal.close(this.action.code);
          this.activeModal.close();
        });
      } else if (
        (this.customer.approvalStatus === APPROVAL_STATUS_CONST.PENDING.value ||
          this.customer.approvalStatus ===
            APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
        this.customer.approvalType === APPROVAL_TYPE_CONST.UNLOCK.value
      ) {
        this.customerService.unlockApproval(params).subscribe((res: any) => {
          this.activeModal.close(this.action.code);
          this.activeModal.close();
        });
      } else if (
        (this.customer.approvalStatus === APPROVAL_STATUS_CONST.PENDING.value ||
          this.customer.approvalStatus ===
            APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
        this.customer.approvalType === APPROVAL_TYPE_CONST.DELETE.value
      ) {
        this.customerService.deleteApproval(params).subscribe((res: any) => {
          this.activeModal.close(this.action.code);
          this.activeModal.close();
        });
      } else if (
        (this.customer.approvalStatus === APPROVAL_STATUS_CONST.PENDING.value ||
          this.customer.approvalStatus ===
            APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
        this.customer.approvalType === APPROVAL_TYPE_CONST.UPDATE.value
      ) {
        this.customerService.updateApproval(params).subscribe((res: any) => {
          this.activeModal.close(this.action.code);
          this.activeModal.close();
        });
      } else if (
        (this.customer.approvalStatus === APPROVAL_STATUS_CONST.PENDING.value ||
          this.customer.approvalStatus ===
            APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
        this.customer.approvalType === APPROVAL_TYPE_CONST.CREATE.value
      ) {
        if (this.otpResponse) {
          params.transactionId = this.otpResponse.transactionId;
          this.customerService.createApproval(params).subscribe((res: any) => {
            this.activeModal.close(this.action.code);
            this.activeModal.close();
          });
          if (this.submitConfirm++ >= 2) {
            this.activeModal.close();
          }
        } else {
          this.customerService.createApproval(params).subscribe((res: any) => {
            this.activeModal.close(this.action.code);
            this.activeModal.close();
          });
        }
      } else if (
        (this.customer.approvalStatus === APPROVAL_STATUS_CONST.PENDING.value ||
          this.customer.approvalStatus ===
            APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
        this.customer.approvalType === APPROVAL_TYPE_CONST.UNCLOSED.value
      ) {
        this.customerService.unclosedApproval(params).subscribe((res: any) => {
          this.activeModal.close(this.action.code);
          this.activeModal.close();
        });
      }
    }
  }

  showSendSmsAndOtp(): boolean {
    const value = this.customer?.premiumAccountNumberCache?.premiumAccNumber;
    if (value) {
      return (
        (this.customer.approvalStatus === APPROVAL_STATUS_CONST.PENDING.value ||
          this.customer.approvalStatus ===
            APPROVAL_STATUS_CONST.APPROVAL_ERROR.value) &&
        this.customer.approvalType === APPROVAL_TYPE_CONST.CREATE.value && !this.isDeny
      );
    }
    return false;
  }

  showContent(): string {
    return this.translateService.instant(this.content, {
      fullname: this.customer.fullname,
    });
  }

  apiSendSms(): void {
    this.customerService
      .sendSms({ customerId: this.customer.customerId })
      .subscribe((res: any) => {
        this.otpResponse = res?.body;
        this.startCountdown();
      });
  }

  startCountdown() {
    const targetTime = new Date(new Date().getTime() + 2 * 60 * 1000);
    this.intervalId = setInterval(() => {
      const now = new Date().getTime();
      const distance = new Date(targetTime).getTime() - now;

      if (distance <= 0) {
        clearInterval(this.intervalId);
        this.timeLeft = undefined;
        // this.activeModal.close();
      } else {
        const hours = Math.floor((distance / (1000 * 60 * 60)) % 24);
        const minutes = Math.floor((distance / (1000 * 60)) % 60);
        const seconds = Math.floor((distance / 1000) % 60);
        this.timeLeft = `${this.pad(hours)}:${this.pad(minutes)}:${this.pad(
          seconds
        )}`;
      }
    }, 100);
  }

  pad(value: number): string {
    return value < 10 ? `0${value}` : `${value}`;
  }
}
