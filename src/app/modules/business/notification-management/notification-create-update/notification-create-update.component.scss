.upload-file-customer {
  border: none !important;
  background: none !important;
  text-decoration: var(--mb-color);
}

.create-customer {
  border: none !important;
  background: none !important;
}

.text-decoration {
  text-decoration:var(--mb-color);
  color: var(--mb-color);
}

.btn-danger {
  background: var(--mb-color);
  border-radius: 8px;
  color: white;
  font-size: 16px;
  padding: 8px 25px;
}

:host ::ng-deep .bank-upload .ant-upload.ant-upload-select-picture-card {
  height: 110px !important;
  width: 350px !important;
}

@media (min-width: 600px) and (max-width: 1199px) {
  :host ::ng-deep .bank-upload .ant-upload.ant-upload-select-picture-card {
    height: 110px !important;
    width: 150px !important;
  }
}

@media (min-width: 1200px) and (max-width: 1370px) {
  :host ::ng-deep .bank-upload .ant-upload.ant-upload-select-picture-card {
    height: 110px !important;
    width: 200px !important;
  }
}

@media (min-width: 1380px) and (max-width: 1690px) {
  :host ::ng-deep .bank-upload .ant-upload.ant-upload-select-picture-card {
    height: 110px !important;
    width: 240px !important;
  }
}

@media (min-width: 1691px) and (max-width: 1900px) {
  :host ::ng-deep .bank-upload .ant-upload.ant-upload-select-picture-card {
    height: 110px !important;
    width: 300px !important;
  }
}

::ng-deep .mat-radio-button.mat-accent .mat-radio-inner-circle {
  background-color:  var(--mb-color);
}

::ng-deep .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
  border-color:  var(--mb-color);
}