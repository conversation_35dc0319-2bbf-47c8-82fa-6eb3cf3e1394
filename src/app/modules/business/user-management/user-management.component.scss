// ::ng-deep .ng-select {
//   &.ng-select-multiple {
//     .ng-select-container {
//       .ng-value-container {
//         flex-wrap: wrap;
//         min-width: 0;

//         .ng-value {
//           white-space: nowrap;
//           min-width: 0;
//           display: flex;
//           max-width: 95%;

//           .ng-value-label {
//             // @include wrapt-text;
//             white-space: nowrap;
//             overflow: hidden;
//             text-overflow: ellipsis;
//           }
//         }
//       }
//     }
//   }
// }

// .example {
//   white-space: nowrap;
//   width: 150px;
//   overflow: hidden;
//   text-overflow: ellipsis;
// }
