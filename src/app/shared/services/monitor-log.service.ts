import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { IMonitorLog } from '@shared/models/monitor-log.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { AbstractDomainService } from './common/abstract.domain.service';

@Injectable({
  providedIn: 'root',
})
export class MonitorLogService extends AbstractDomainService<IMonitorLog> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.MONITOR_LOG;
}
