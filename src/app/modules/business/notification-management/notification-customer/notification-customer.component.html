<div class="table-responsive">
  <table class="table">
    <thead>
      <tr>
        <th class="text-center" scope="col">
          {{ "common.no" | translate }}
        </th>
        <th scope="col">{{ "customerRegisterManagement.cif" | translate }}</th>
        <th scope="col">{{ "model.customer.fullname" | translate }}</th>
        <th scope="col">
          {{ "model.customer.username" | translate }}
        </th>
        <th scope="col">
          {{ "model.customer.phoneNumber" | translate }}
        </th>
        <th scope="col">
          {{ "model.customer.email" | translate }}
        </th>
        <th class="text-center" scope="col">
          {{ "common.status" | translate }}
        </th>
        <th class="text-center" scope="col">
          {{ "common.action.label" | translate }}
        </th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let dataItem of customers; let i = index">
        <td class="text-center">{{ i + 1 }}</td>
        <td>{{ dataItem.cif }}</td>
        <td>{{ dataItem.fullname }}</td>
        <td>{{ dataItem.username }}</td>
        <td>{{ dataItem.phoneNumber }}</td>
        <td>{{ dataItem.email }}</td>
        <td class="text-center">
          <span [ngClass]="getEventBadge(dataItem?.notificationStatus) + ''">
            {{ getEventStatus(dataItem.notificationStatus) | translate }}
          </span>
        </td>
        <!-- *hasPrivileges="SYSTEM_RULES.NOTIFICATION_DELETE" -->
        <td class="text-center">
          <button
            [disabled]="isWaitingOrSend"
            class="btn px-1 py-0"
            (click)="delete(dataItem)"
          >
            <i class="fa fa-trash mb-color" aria-hidden="true"></i>
          </button>
        </td>
      </tr>
    </tbody>
  </table>
  <div class="row d-block text-center m-0" *ngIf="customers?.length === 0">
    <img
      src="/assets/dist/img/icon/empty.svg"
      height="120"
      alt="no_search_result"
    />
    <p class="text-center mb-5">
      {{ "common.no_search_result" | translate }}
    </p>
  </div>
</div>
