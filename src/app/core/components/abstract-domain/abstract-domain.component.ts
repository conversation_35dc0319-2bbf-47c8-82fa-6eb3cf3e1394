import { Component, HostListener, OnD<PERSON>roy } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';

@Component({
  selector: 'app-abstract-domain',
  templateUrl: './abstract-domain.component.html',
  styleUrls: ['./abstract-domain.component.scss']
})
export abstract class AbstractDomainComponent implements OnDestroy {
  private hasSearch = false;
  abstract resource: RESOURCE_TYPE;
  SYSTEM_RULES = SYSTEM_RULES;

  constructor(protected router: Router) { }

  ngOnDestroy(): void {
    let previousPath = '';
    this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        previousPath = event.urlAfterRedirects;
      }
    });
    if (!this.hasSearch) {
      sessionStorage.removeItem(previousPath);
    }
  }

  /**
   * backToListPage
   *
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage(): void {
    this.hasSearch = true;
  }

}
