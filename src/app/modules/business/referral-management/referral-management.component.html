<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formEventSearch" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-lg-3 col-md-6">
            <div class="form-group">
              <label>{{ "common.action.searchKeyword" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="keyword"
                placeholder="{{ 'common.action.searchKeyword' | placeholder }}"
                class="w-100"
                class="form-control"
              />
            </div>
          </div>
          <div class="col-lg-3 col-md-6">
            <div class="form-group">
              <label>{{ "common.status" | translate }}</label>
              <ng-select
                appearance="outline"
                [searchable]="false"
                placeholder="{{ 'common.status' | placeholder : 'select' }}"
                [clearable]="true"
                formControlName="status"
              >
                <ng-option
                  [value]="item.code"
                  *ngFor="let item of ENTITY_STATUS"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-lg-3 col-md-6">
            <div class="form-group">
              <label>{{ "model.referral.type" | translate }}</label>
              <ng-select
                appearance="outline"
                [multiple]="true"
                [searchable]="false"
                placeholder="{{
                  'model.referral.type' | placeholder : 'select'
                }}"
                [clearable]="true"
                formControlName="types"
              >
                <ng-option [value]="item.code" *ngFor="let item of RM_TYPE">
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
            >
              <div class="btn-reset">
                <i
                  class="bi bi-arrow-clockwise"
                  type="button"
                  (click)="onReset()"
                >
                </i>
              </div>
            </div>
            <div class="">
              <button
                class="btn btn-search mr-2"
                type="submit"
                *hasPrivileges="SYSTEM_RULES.REFERRAL_READ"
              >
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-2 mt-4">
          <div class="row import-excel justify-content-end">
            <div class="col-md-2 import-excel"></div>
            <button
              class="btn btn-red format-button mb-2"
              type="button"
              (click)="exportFile()"
              *hasPrivileges="SYSTEM_RULES.REFERRAL_EXPORT"
            >
              {{ "common.action.export" | translate }}
            </button>
            <button
              class="btn btn-white format-button mb-2"
              type="button"
              (click)="onImportExcel()"
              *hasPrivileges="SYSTEM_RULES.REFERRAL_IMPORT"
            >
              <i class="fas fa-cloud-upload-alt"> </i>
              {{ "common.action.uploadFiles" | translate }}
            </button>
            <button
              class="btn btn-red format-button mb-2"
              type="button"
              (click)="onCreate()"
              *hasPrivileges="SYSTEM_RULES.REFERRAL_IMPORT"
            >
              {{ "common.action.create" | translate }}
            </button>
          </div>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th class="text-center" scope="col">
                {{ "common.no" | translate }}
              </th>
              <th scope="col">
                {{ "model.referral.userFullName" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{ "model.referral.phoneNumber" | translate }}
              </th>
              <th scope="col">
                {{ "model.referral.type" | translate }}
              </th>
              <th scope="col">
                {{ "model.referral.userCode" | translate }}
              </th>
              <th scope="col">
                {{ "model.referral.rmCode" | translate }}
              </th>
              <th scope="col">
                {{ "model.referral.referralCode" | translate }}
              </th>
              <th scope="col">
                {{ "model.referral.rmLink" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{ "model.referral.quantity" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.status" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of referrals; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td>
                <span title="{{ dataItem.userFullName }}">{{
                  dataItem.userFullName | limitWord
                }}</span>
              </td>
              <td class="text-right">
                <span title="{{ dataItem.phoneNumber }}">{{
                  dataItem.phoneNumber | limitWord
                }}</span>
              </td>
              <td>
                {{ ENTITY_TYPE_MAP[dataItem.type || ""].label | translate }}
              </td>
              <td>
                <span title="{{ dataItem.userCode }}">{{
                  dataItem.userCode | limitWord
                }}</span>
              </td>
              <td>
                <span title="{{ dataItem.rmCode }}">{{
                  dataItem.rmCode | limitWord
                }}</span>
              </td>
              <td>
                {{ dataItem.referralCode }}
              </td>
              <td class="dynamic-link">
                <a
                  href="{{ dataItem.rmLink }}"
                  target="_blank"
                  title="{{ dataItem.rmLink }}"
                  >{{ dataItem.rmLink | limitWord }}
                </a>
                <i
                  *ngIf="dataItem.rmLink"
                  class="fa fa-clone copy-text"
                  (click)="copyText(dataItem.rmLink)"
                  aria-hidden="true"
                ></i>
              </td>
              <td class="text-right">
                {{ dataItem.quantity }}
              </td>
              <td class="text-center">
                <span
                  class="badge"
                  [ngClass]="ENTITY_STATUS_MAP[dataItem.status || 0].style"
                  >{{
                    ENTITY_STATUS_MAP[dataItem.status || 0].label | translate
                  }}</span
                >
              </td>
              <td class="text-center">
                <button
                  ngbTooltip="{{ 'common.action.detail' | translate }}"
                  class="btn px-1 py-0"
                  (click)="detail(dataItem.referralId)"
                  *hasPrivileges="SYSTEM_RULES.REFERRAL_READ"
                >
                  <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                </button>
                <button
                  [ngbTooltip]="
                    (dataItem.status === ENTITY_STATUS_CONST.ACTIVE.code
                      ? 'common.action.lock'
                      : 'common.action.unlock'
                    ) | translate
                  "
                  class="btn px-1 py-0"
                  (click)="lockAndUnlock(dataItem)"
                  *hasPrivileges="
                    dataItem.status === 1
                      ? SYSTEM_RULES.REFERRAL_LOCK
                      : SYSTEM_RULES.REFERRAL_UNLOCK
                  "
                >
                  <i
                    [className]="
                      dataItem.status === 1
                        ? 'fa fa-lock mb-color'
                        : 'fa fa-unlock mb-color'
                    "
                    aria-hidden="true"
                  ></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="row d-block text-center m-0" *ngIf="referrals.length === 0">
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
        <div *ngIf="referrals.length">
          <mat-paginator
            [length]="formEventSearch.value.length"
            [pageSize]="formEventSearch.value.pageSize"
            [pageIndex]="formEventSearch.value.pageIndex"
            [pageSizeOptions]="pageSizeOptions"
            (page)="onChangePage($event)"
            aria-label="Select page"
          >
          </mat-paginator>
        </div>
      </div>
    </div>
  </div>
</section>
