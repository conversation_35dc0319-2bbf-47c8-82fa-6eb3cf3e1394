import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { AutoValidateDirective } from './auto-validate.directive';
import { ERROR_MESSAGE_TOKEN } from './error-message-map-token';
import { DefaultErrorMessageMapEnUs } from './locale/default-error-message-map-en-us';
import { DefaultRenderDivNodeStrategy, RENDER_DIV_NODE_STRATEGY } from './render-div-node-strategy';

@NgModule({
  imports: [
    CommonModule
  ],
  declarations: [AutoValidateDirective
  ],
  exports: [AutoValidateDirective],
  providers: [
    {
      provide: ERROR_MESSAGE_TOKEN,
      useValue: DefaultErrorMessageMapEnUs,
    },
    {
      provide: RENDER_DIV_NODE_STRATEGY,
      useClass: DefaultRenderDivNodeStrategy,
    }
  ]
})
export class AutoValidateModule { }
