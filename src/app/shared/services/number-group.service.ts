import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { INumberGroup } from '@shared/models/number-group.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { Observable } from 'rxjs';
import { AbstractDomainService } from './common/abstract.domain.service';
import { EntityResponseType } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class NumberGroupService extends AbstractDomainService<INumberGroup> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.NUMBER_GROUP;

  export(request: INumberGroup): Observable<EntityResponseType<INumberGroup>> {
    return super.postFile(RESOURCE_CONST.NUMBER_GROUP.API.EXPORT, request);
  }

  getNumberGroup(): Observable<INumberGroup[]> {
    return super.post<EntityResponseType<INumberGroup[]>>(RESOURCE_CONST.NUMBER_GROUP.API.INFO);
  }
}
