import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import {
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  LIMIT_LENGTH_WORD_CONST,
  MODAL_ACTION,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { IInformationTemplate } from '@shared/models/information-template.model';
import { IInformationTemplateSearch } from '@shared/models/request/information-template.search';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { InformationTemplateService } from '@shared/services/information-template.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-information-template',
  templateUrl: './information-template.component.html',
  styleUrls: ['./information-template.component.scss'],
})
export class InformationTemplateComponent implements OnInit {
  informationTemplateFormSearch: FormGroup = this.fb.group({
    keyword: [null],
    status: [null],
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
  });

  informationTemplateSearch: IInformationTemplateSearch = {};
  informationTemplates: IInformationTemplate[] = [];

  pageSizeOptions = PAGINATION.OPTIONS;
  ENTITY_STATUS = ENTITY_STATUS;
  ROUTER_UTILS = ROUTER_UTILS;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  SYSTEM_RULES = SYSTEM_RULES;
  LIMIT_LENGTH_WORD_CONST = LIMIT_LENGTH_WORD_CONST;
  ROUTER_ACTIONS = ROUTER_ACTIONS;

  constructor(
    private fb: FormBuilder,
    private informationTemplateService: InformationTemplateService,
    private modalService: ModalService,
    private toastService: ToastrCustomService,
    private router: Router,
    private ngb: NgbModal
  ) {}

  ngOnInit(): void {
    this.onSearch();
  }

  onNavigateCRUD(action?: string, informationTemplateId?: number) {
    if (action === ROUTER_ACTIONS.create) {
      this.router.navigate([
        ROUTER_UTILS.sysManage.informationTemplate.root,
        action,
      ]);
    } else {
      this.router.navigate([
        ROUTER_UTILS.sysManage.informationTemplate.root,
        informationTemplateId,
        action,
      ]);
    }
  }

  onReset() {
    this.informationTemplateFormSearch.controls.keyword.reset();
    this.informationTemplateFormSearch.controls.status.reset();
  }

  onSearchSubmit() {
    this.informationTemplateFormSearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.informationTemplateFormSearch.controls.length.setValue(
      PAGINATION.LENGTH_DEFAULT
    );
    this.onSearch();
  }

  onSearch() {
    this.informationTemplateService
      .search(this.informationTemplateFormSearch.value)
      .subscribe((res: any): void => {
        this.informationTemplates = res.body.content;
        this.informationTemplateFormSearch.controls.length.setValue(
          res.body.totalElements
        );
      });
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.informationTemplateFormSearch.value.pageIndex,
      this.informationTemplateFormSearch.value.pageSize
    );
  }

  onChangePage(page: PageEvent) {
    this.informationTemplateFormSearch.controls.pageIndex.setValue(
      page.pageIndex
    );
    this.informationTemplateFormSearch.controls.pageSize.setValue(
      page.pageSize
    );
    this.onSearch();
  }

  delete(template: IInformationTemplate) {
    const modalData = {
      title: 'informationTemplate.deleteTitle',
      content: 'informationTemplate.deleteContent',
      interpolateParams: {
        informationTemplateName: `<b>${
          template?.informationTemplateContent?.informationTemplateName || ''
        }</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = {
          informationTemplateId: template.informationTemplateId,
        };
        this.informationTemplateService
          .deleteDepartment(params)
          .subscribe((res) => {
            this.toastService.success('common.action.deleteSuccess');
            this.onSearch();
          });
      }
    });
  }

  lockAndUnlock(template: IInformationTemplate) {
    if (template.status === ENTITY_STATUS_CONST.INACTIVE.code) {
      this.unlock(template);
    } else {
      this.lock(template);
    }
  }

  lock(template: IInformationTemplate): boolean {
    let rs = false;

    const modalData = {
      title: 'informationTemplate.lockTitle',
      content: 'informationTemplate.lockContent',
      interpolateParams: {
        informationTemplateName: `<b>${
          template?.informationTemplateContent?.informationTemplateName || ''
        }</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = {
          informationTemplateId: template.informationTemplateId,
        };
        this.informationTemplateService.lock(params).subscribe((res) => {
          this.toastService.success('common.action.lockSuccess');
          this.onSearch();
          rs = true;
        });
      }
    });

    return rs;
  }

  unlock(template: IInformationTemplate): boolean {
    let rs = false;

    const modalData = {
      title: 'informationTemplate.unlockTitle',
      content: 'informationTemplate.unlockContent',
      interpolateParams: {
        informationTemplateName: `<b>${
          template?.informationTemplateContent?.informationTemplateName || ''
        }</b>`,
      },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = {
          informationTemplateId: template.informationTemplateId,
        };
        this.informationTemplateService.unlock(params).subscribe((res) => {
          this.toastService.success('common.action.unlockSuccess');
          this.onSearch();
          rs = true;
        });
      }
    });

    return rs;
  }

  getContentHTML(content?: string): string {
    if (content) {
      return CommonUtils.stripHTML(content);
    }
    return '';
  }
}
