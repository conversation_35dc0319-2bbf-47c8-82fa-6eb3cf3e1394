::ng-deep .cuh .card {
  box-shadow: 0 0 1px rgb(0 0 0 / 13%), 0 1px 3px rgb(0 0 0 / 20%) !important;
  margin-bottom: 1rem;
}

::ng-deep .cuh .card-header {
  padding: 2px;
  background-color: rgb(243 235 221);
  border-top-left-radius: 0.25rem !important;
  border-top-right-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
}

::ng-deep .cuh .card-body {
  font-size: 16px;
}

::ng-deep .cuh .accordion > .card:not(:last-of-type) {
  border-bottom-left-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
}
