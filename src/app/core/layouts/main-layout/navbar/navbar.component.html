<!-- Navbar -->
<nav class="main-header navbar navbar-expand navbar-white navbar-light">
  <!-- Left navbar links -->
  <ul class="navbar-nav">
    <li class="nav-item d-flex">
      <a
        class="nav-link ml-2"
        data-widget="pushmenu"
        href="#"
        role="button"
        (click)="collapseMenu(!isCollapsed)"
      >
        <img src="assets/dist/img/bar.png" alt="MB" />
      </a>
      <h1 class="title-mb mb-color ml-1 text-uppercase">
        {{ pageTitle | translate }}
      </h1>
    </li>
  </ul>
  <div class="d-flex navbar-nav ml-auto mr-cus">
    <ng-select
      appearance="outline"
      [searchable]="false"
      class="user-name"
      style="line-height: 0"
      [clearable]="false"
      [ngModel]="currentLanguage"
      (change)="onChangeLanguage($event)"
    >
      <ng-option [value]="lang.code" *ngFor="let lang of LANGUAGES">
        <img [src]="lang.icon" [alt]="lang.code" width="20px" height="20px" />
        {{ lang.label | translate }}
      </ng-option>
    </ng-select>

    <div
      class="ml-3 noti-wrapper"
      *hasPrivileges="SYSTEM_RULES.NOTIFICATION_LIMIT_READ"
      (clickOutside)="closeDropdown()"
    >
      <div class="noti-bell" (click)="toggleNotification()">
        <i class="fa fa-bell bell-icon"></i>
        <span class="noti-badge">{{ getNotificationCount() }}</span>
      </div>

      <div class="notification-dropdown" *ngIf="showDropdown">
        <div class="notification-header">
          <span>{{ "common.notification" | translate }}</span>
        </div>

        <div class="notification-tabs">
          <button
            class="tab"
            ngbTooltip="{{ 'common.all' | translate }}"
            [class.active]="activeTab === TAB_CONST.ALL"
            (click)="changeTab(TAB_CONST.ALL)"
          >
            {{ "common.all" | translate }}
          </button>
          <button
            class="tab"
            ngbTooltip="{{ 'common.notContacted' | translate }}"
            [class.active]="activeTab === TAB_CONST.UNREAD"
            (click)="changeTab(TAB_CONST.UNREAD)"
          >
            <i class="bi bi-envelope"></i>
            {{ "common.notContacted" | translate | limitWord : 12 }}
          </button>
          <button
            class="tab"
            ngbTooltip="{{ 'common.contacted' | translate }}"
            [class.active]="activeTab === TAB_CONST.READ"
            (click)="changeTab(TAB_CONST.READ)"
          >
            <i class="bi bi-envelope-open"></i>
            {{ "common.contacted" | translate }}
          </button>
        </div>

        <div class="notification-list">
          <div
            *ngFor="let noti of filterNotifications()"
            class="notification-item"
          >
            <div class="notification-icon-small">
              <i
                *ngIf="noti.status === ENTITY_STATUS_CONST.INACTIVE.code"
                class="bi bi-envelope"
              ></i>
              <i
                *ngIf="noti.status === ENTITY_STATUS_CONST.ACTIVE.code"
                class="bi bi-envelope-open"
              ></i>
            </div>
            <div class="notification-content">
              <div
                [ngClass]="
                  noti.status === ENTITY_STATUS_CONST.INACTIVE.code
                    ? 'notification-title'
                    : ''
                "
              >
                <p
                  *ngIf="noti.notificationType === 'NOTIFICATION_LIMIT'"
                  (click)="onConfigSavingManagement()"
                >
                  {{ noti?.fullname + " " }}
                  {{ noti?.transferTypeStr + " " | lowercase }}
                  {{ noti?.typeLimitStr | lowercase }}
                </p>
                <p
                  *ngIf="
                    noti.notificationType === 'PREMIUM_ACCOUNT_NUMBER_REVERT'
                  "
                  (click)="onRouterPremiumAccountRevert()"
                >
                  {{ "premiumAccountRevert.note1" | translate }}
                  {{ noti?.target || "" }}
                  {{ "premiumAccountRevert.note2" | translate }}
                  {{ noti?.fullname || "" }}
                  {{ "premiumAccountRevert.note3" | translate }}
                </p>
                <p
                  *ngIf="noti.notificationType === 'SMS_BALANCE_CHARGE_FAIL'"
                  (click)="onRouterSmsBalanceChangeFailed()"
                >
                  {{ "smsBalance.chargeFee" | translate }}
                  {{ noti?.target || "" }}
                  {{ "premiumAccountRevert.note2" | translate }}
                  {{ noti?.fullname || "" }}
                  {{ "premiumAccountRevert.note3" | translate }}
                </p>
              </div>
              <div class="notification-time">
                {{ noti.transactionFinishTime }}
              </div>
            </div>
            <span
              *ngIf="noti.status === ENTITY_STATUS_CONST.INACTIVE.code"
              class="unread-dot"
            ></span>
          </div>
        </div>
        <div
          *ngIf="activeTab === TAB_CONST.ALL && isShow"
          class="noti-footer"
          (click)="loadAllData()"
        >
          {{ "common.addMore" | translate }}
        </div>
        <div
          *ngIf="activeTab === TAB_CONST.UNREAD && isShowUnread"
          class="noti-footer"
          (click)="loadUnreadData()"
        >
          {{ "common.addMore" | translate }}
        </div>
        <div
          *ngIf="activeTab === TAB_CONST.READ && isShowRead"
          class="noti-footer"
          (click)="loadReadData()"
        >
          {{ "common.addMore" | translate }}
        </div>
      </div>
    </div>
    <div class="welcom" [matMenuTriggerFor]="appMenu">
      {{ "common.hello" | translate }} {{ userInfo?.fullname }}
    </div>
    <mat-menu #appMenu="matMenu" class="mat-menu-content">
      <div
        mat-menu-item
        class="user-card"
        style="
          background-image: url('../../../../assets/dist/img/bg-user-card.jpg');
        "
      >
        <div>
          <img
            src="../../assets/dist/img/avatar-white.svg"
            alt="logo login"
            class="icon"
          />
        </div>
        <div class="fullname">
          {{ userInfo?.fullname }}
        </div>
      </div>
      <div class="cart-container">
        <div class="avatar d-flex card-item" (click)="onProfile()">
          <div class="col-2 pl-0">
            <i
              class="fa fa-address-card"
              style="font-size: 45px; color: #0054a5"
            ></i>
          </div>
          <div class="col">
            <div class="font-weight-bold">
              <b> {{ "common.myProfile" | translate }}</b>
            </div>
            <p>{{ "sidebar.profile" | translate }}</p>
          </div>
        </div>
        <hr />
        <button class="btn text-center btn-red" (click)="logout()">
          {{ "common.logout" | translate }}
        </button>
      </div>
    </mat-menu>

    <!-- <button class="btn-user ant-btn" (click)="onProfile()">
      <div class="user-name text-center">
        <span class="me-1">{{ "common.hello" | translate }}</span>
        <strong
          ><span>{{ userInfo?.fullname | limitWord }}</span></strong
        >
      </div>
    </button> -->

    <!-- <button class="btn text-center btn-logout" (click)="logout()">
      {{ "common.logout" | translate }}
    </button> -->
  </div>

  <!-- /.navbar -->
</nav>
