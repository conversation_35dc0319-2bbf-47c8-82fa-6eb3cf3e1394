import { Injectable } from '@angular/core';
import { IMerchantTopup } from '@shared/models/merchant-topup.model';
import { IMerchantTransactionHistory } from '@shared/models/merchant-transaction-history.model';
import { IMerchant, IMerchantCodeRequest } from '@shared/models/merchant.model';
import { IMerchantTransactionHistorySearch } from '@shared/models/request/merchant-transaction-history.search';
import { IMerchantSearch } from '@shared/models/request/merchant.search';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';
@Injectable({
  providedIn: 'root',
})
export class MerchantService extends AbstractService {
  public resourceUrl = '/merchant';

  /**
   * search
   *
   * @param request IMerchant
   * @returns Observable<EntityResponseType<IMerchant[]>>
   */
  search(
    request: IMerchantSearch
  ): Observable<EntityResponseType<IMerchant[]>> {
    return super.post<EntityResponseType<IMerchant[]>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  /**
   * search master
   *
   * @param request IMerchant
   * @returns Observable<EntityResponseType<IMerchant[]>>
   */
  searchMaster(
    request: IMerchantSearch
  ): Observable<EntityResponseType<IMerchant[]>> {
    return super.post<EntityResponseType<IMerchant[]>>(
      `${this.resourceUrl}/search-master`,
      request
    );
  }

  /**
   * search transaction history
   *
   * @param request IMerchantTransactionHistory
   * @returns Observable<EntityResponseType<IMerchantTransactionHistory[]>>
   */
  transactionHistory(
    request: IMerchantTransactionHistorySearch
  ): Observable<EntityResponseType<IMerchantTransactionHistory[]>> {
    return super.post<EntityResponseType<IMerchantTransactionHistory[]>>(
      `${this.resourceUrl}/transaction-history`,
      request
    );
  }

  /**
   * API create
   *
   * @param request IMerchant
   * @returns Observable<EntityResponseType<IMerchant>>
   */
  create(request: IMerchant): Observable<EntityResponseType<IMerchant>> {
    return super.post<EntityResponseType<IMerchant>>(
      `${this.resourceUrl}/create`,
      request
    );
  }

  /**
   * API create master
   *
   * @param request IMerchant
   * @returns Observable<EntityResponseType<IMerchant>>
   */
  createMaster(request: IMerchant): Observable<EntityResponseType<IMerchant>> {
    return super.post<EntityResponseType<IMerchant>>(
      `${this.resourceUrl}/create-master`,
      request
    );
  }

  /**
   * API update
   *
   * @param request IMerchant
   * @returns Observable<EntityResponseType<IMerchant>>
   */
  update(request: IMerchant): Observable<EntityResponseType<IMerchant>> {
    return super.post<EntityResponseType<IMerchant>>(
      `${this.resourceUrl}/update`,
      request
    );
  }

  /**
   * API update master
   *
   * @param request IMerchant
   * @returns Observable<EntityResponseType<IMerchant>>
   */
  updateMaster(request: IMerchant): Observable<EntityResponseType<IMerchant>> {
    return super.post<EntityResponseType<IMerchant>>(
      `${this.resourceUrl}/update-master`,
      request
    );
  }

  /**
   * API detail
   *
   * @param request IMerchant
   * @returns Observable<EntityResponseType<ILoanOnline>>
   */
  detail(request: IMerchant): Observable<EntityResponseType<IMerchant>> {
    return super.post<EntityResponseType<IMerchant>>(
      `${this.resourceUrl}/info`,
      request
    );
  }

  /**
   * API lock
   *
   * @param request IMerchant
   * @returns Observable<EntityResponseType<IMerchant>>
   */
  lock(request: IMerchant): Observable<EntityResponseType<IMerchant>> {
    return super.post<EntityResponseType<IMerchant>>(
      `${this.resourceUrl}/lock`,
      request
    );
  }

  /**
   * API unlock
   *
   * @param request IMerchant
   * @returns Observable<EntityResponseType<IMerchant>>
   */
  unlock(request: IMerchant): Observable<EntityResponseType<IMerchant>> {
    return super.post<EntityResponseType<IMerchant>>(
      `${this.resourceUrl}/unlock`,
      request
    );
  }

  /**
   * API delete
   *
   * @param request IMerchant
   * @returns Observable<EntityResponseType<ILoanOnline>>
   */
  deleteMerchant(
    request: IMerchant
  ): Observable<EntityResponseType<IMerchant>> {
    return super.post<EntityResponseType<IMerchant>>(
      `${this.resourceUrl}/delete`,
      request
    );
  }

  /**
   * API export
   *
   * @param request IMerchantTransactionHistorySearch
   * @returns Observable<any>
   */
  export(request: IMerchantTransactionHistorySearch): Observable<any> {
    return super.postFile(
      `${this.resourceUrl}/transaction-history/export`,
      request
    );
  }

  /**
   * API export
   *
   * @param request IMerchantSearch
   * @returns Observable<any>
   */
  exportMerchant(request: IMerchantSearch): Observable<any> {
    return super.postFile(`${this.resourceUrl}/export`, request);
  }

  /**
   * API export
   *
   * @param request IMerchantTransactionHistorySearch
   * @returns Observable<any>
   */
  exportMasterMerchant(
    request: IMerchantTransactionHistorySearch
  ): Observable<any> {
    return super.postFile(
      `${this.resourceUrl}/export-master-merchant`,
      request
    );
  }

  getMerchantList(): Observable<EntityResponseType<IMerchantTopup[]>> {
    return super.post<EntityResponseType<IMerchantTopup[]>>(
      `${this.resourceUrl}/mobile-topup`
    );
  }

  getMerchantCode(
    request: IMerchantCodeRequest
  ): Observable<EntityResponseType<any>> {
    return super.post<EntityResponseType<any>>(
      `${this.resourceUrl}/search-service-type-master`,
      request
    );
  }

  merchantCodeSync(request?: IMerchantSearch): Observable<any> {
    return super.postFile(`${this.resourceUrl}/merchant-code-sync`, request);
  }
}
