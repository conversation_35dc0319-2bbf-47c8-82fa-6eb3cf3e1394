import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import {
  CROSS_BORDER_NATION_CODES,
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  LIMIT_LENGTH_WORD_CONST,
  MODAL_ACTION,
  NATION_CODES,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { IBank } from '@shared/models/bank.model';
import { IBankSearch } from '@shared/models/request/bank.search';
import { IFileEntrySearch } from '@shared/models/request/file-entry.search';
import { BankService } from '@shared/services/bank.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-bank-management',
  templateUrl: './bank.component.html',
  styleUrls: ['./bank.component.scss'],
})
export class BankComponent implements OnInit {
  banks: IBank[] = [];
  recordSelected: any = [];
  action: any = '';
  storage: any;

  ROUTER_UTILS = ROUTER_UTILS;
  pageSizeOptions = PAGINATION.OPTIONS;
  ENTITY_STATUS = ENTITY_STATUS;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  SYSTEM_RULES = SYSTEM_RULES;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  LIMIT_LENGTH_WORD_CONST = LIMIT_LENGTH_WORD_CONST;
  NATION_CODES = NATION_CODES;

  // default form search
  formBankSearch = this.fb.group({
    code: '',
    bankCodeNumber: null,
    name: '',
    status: null,
    nation: null,
    length: 0,
    pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
    pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    // previousPageIndex: 0,
  });

  constructor(
    private bankService: BankService,
    private fb: FormBuilder,
    private router: Router,
    private modalService: ModalService,
    private toastService: ToastrCustomService
  ) {
    // get session storage
    this.storage = sessionStorage.getItem(STORAGE_APP.BANK);
  }

  /**
   * Init data
   */
  ngOnInit(): void {
    // check storage
    // (when click back button or back website)
    if (this.storage) {
      // get session storage and parse json
      const filter = JSON.parse(this.storage) as IBankSearch;
      // set value form control
      if (filter.status) {
        const status = +filter.status;
        this.formBankSearch.controls.status.setValue(status);
      }
      this.formBankSearch.controls.code.setValue(filter.code);
      this.formBankSearch.controls.name.setValue(filter.name);
      this.formBankSearch.controls.bankCodeNumber.setValue(
        filter.bankCodeNumber
      );
      this.onSearch();
    } else {
      this.onSearch();
    }
    // click form clear storage
    sessionStorage.removeItem(STORAGE_APP.BANK);
  }

  /**
   * Change page: next page and preview page
   *
   * @param page PageEvent
   */
  onChangePage(page: PageEvent) {
    this.formBankSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formBankSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  onSearchSubmit() {
    this.formBankSearch.controls.pageIndex.setValue(
      PAGINATION.PAGE_NUMBER_DEFAULT
    );
    this.formBankSearch.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    this.onSearch();
  }

  /**
   * search form
   */
  onSearch(): void {
    const body = this.formBankSearch.value;
    // this.bankSearch.code = body.code;
    // this.bankSearch.bankCodeNumber = body.bankCodeNumber;
    // this.bankSearch.name = body.name;
    // this.bankSearch.status = body.status;
    this.bankService.search(body).subscribe((res: any): void => {
      this.banks = res.body.content;
      res.body.content?.forEach((bank: IBank) => {
        if (bank.icon) {
          const searchFile: IFileEntrySearch = {};
          searchFile.classPk = bank.bankId;
          searchFile.fileEntryId = bank.icon.id;
          searchFile.normalizeName = bank.icon.normalizeName;
          bank.iconUrL = this.bankService.getIconUri(searchFile);
        }
      });
      this.formBankSearch.controls.length.setValue(res.body.totalElements);
    });
  }

  /**
   * index on list bank
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formBankSearch.value.pageIndex,
      this.formBankSearch.value.pageSize
    );
  }

  /**
   * button click detail
   *
   * @param bankId number
   */
  detail(bankId?: number): void {
    sessionStorage.setItem(
      STORAGE_APP.BANK,
      JSON.stringify(this.formBankSearch.value)
    );
    this.router.navigate([
      ROUTER_UTILS.bank.root,
      bankId,
      ROUTER_ACTIONS.detail,
    ]);
  }

  /**
   * reset form
   */
  onReset(): void {
    // reset data
    this.formBankSearch.controls.code.reset();
    this.formBankSearch.controls.bankCodeNumber.reset();
    this.formBankSearch.controls.name.reset();
    this.formBankSearch.controls.status.reset();
    this.formBankSearch.controls.nation.reset();
    // this.formBankSearch.controls.status.setValue('');
  }

  /**
   * update
   *
   * @param bankId number
   */
  update(bankId?: number): void {
    sessionStorage.setItem(
      STORAGE_APP.BANK,
      JSON.stringify(this.formBankSearch.value)
    );
    this.router.navigate([
      ROUTER_UTILS.bank.root,
      bankId,
      ROUTER_ACTIONS.update,
    ]);
  }

  /**
   * delete
   *
   * @param bank IBank
   */
  delete(bank: IBank): void {
    // open modal
    const modalData = {
      title: 'bank.delete',
      content: 'bank.deleteBankContent',
      interpolateParams: { bankCode: `<b>${bank?.bankCode || ''}</b>` },
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { bankId: bank?.bankId };
        this.bankService.deleteBank(params).subscribe((res: any) => {
          this.toastService.success('common.action.deleteSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * check lock and unlock and call api
   *
   * @param bank IBank
   */
  lockAndUnlock(bank: IBank): void {
    if (bank.status === ENTITY_STATUS_CONST.INACTIVE.code) {
      this.unLockBank(bank);
    } else {
      this.lockBank(bank);
    }
  }

  /**
   * Lock bank
   *
   * @param bank IBank
   */
  private lockBank(bank: IBank) {
    const modalData = {
      title: 'bank.lock',
      content: 'bank.lockBankContent',
      interpolateParams: { bankCode: `<b>${bank?.bankCode || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { bankId: bank?.bankId };
        this.bankService.lock(params).subscribe((res) => {
          this.toastService.success('common.action.lockSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * UnLock Bank
   *
   * @param bank: IBank
   */
  private unLockBank(bank: IBank) {
    const modalData = {
      title: 'bank.unlock',
      content: 'bank.unlockBankContent',
      interpolateParams: { bankCode: `<b>${bank?.bankCode || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { bankId: bank?.bankId };
        this.bankService.unlock(params).subscribe((res) => {
          this.toastService.success('common.action.unlockSuccess');
          this.onSearch();
        });
      }
    });
  }
}
