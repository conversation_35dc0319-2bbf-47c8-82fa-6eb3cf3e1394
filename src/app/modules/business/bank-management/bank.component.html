<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formBankSearch" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-md-4 col-lg-2">
            <div class="form-group">
              <label>{{ "model.bank.codeNumber" | translate }}</label>
              <input
                numbersOnly
                type="text"
                formControlName="bankCodeNumber"
                class="w-100"
                class="form-control"
                placeholder="{{ 'model.bank.codeNumber' | placeholder }}"
              />
            </div>
          </div>
          <div class="col-md-4 col-lg-2">
            <div class="form-group">
              <label>{{ "common.status" | translate }}</label>
              <ng-select
                placeholder="{{ 'common.appSelectOption.status' | translate }}"
                [searchable]="false"
                formControlName="status"
                [clearable]="true"
              >
                <ng-option
                  [value]="item.code"
                  *ngFor="let item of ENTITY_STATUS"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-4 col-lg-2">
            <div class="form-group">
              <label>{{ "model.bank.code" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="code"
                class="w-100"
                class="form-control"
                placeholder="{{ 'model.bank.code' | placeholder }}"
              />
            </div>
          </div>
          <div class="col-md-4 col-lg-2">
            <div class="form-group">
              <label>{{ "model.bank.name" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="name"
                class="w-100"
                class="form-control"
                placeholder="{{ 'model.bank.name' | placeholder }}"
              />
            </div>
          </div>
          <div class="col-md-4 col-lg-2">
            <div class="form-group">
              <label>{{ "common.nation" | translate }}</label>
              <ng-select
                appearance="outline"
                [searchable]="false"
                [clearable]="true"
                formControlName="nation"
                placeholder="{{ 'common.nation' | translate }}"
              >
                <ng-option
                  [value]="item.code"
                  *ngFor="let item of NATION_CODES"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-md-2 mt-4 d-flex col-lg-2">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
            >
              <div class="btn-reset">
                <i
                  class="bi bi-arrow-clockwise"
                  type="button"
                  (click)="onReset()"
                >
                </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" type="submit">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-3 mt-4">
          <button
            type="button"
            class="btn btn-red"
            [routerLink]="[ROUTER_UTILS.bank.create]"
            [routerLinkActive]="['active']"
            *hasPrivileges="SYSTEM_RULES.BANK_CREATE"
          >
            {{ "common.action.create" | translate }}
          </button>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th class="text-center" scope="col">
                {{ "common.no" | translate }}
              </th>
              <th scope="col">{{ "model.bank.codeNumber" | translate }}</th>
              <th scope="col">{{ "model.bank.code" | translate }}</th>
              <th scope="col">{{ "model.bank.name" | translate }}</th>
              <th scope="col">{{ "model.bank.logo" | translate }}</th>
              <th class="text-center" scope="col">
                {{ "common.status" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of banks; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td>{{ dataItem.bankCodeNumber }}</td>
              <td>{{ dataItem.bankCode }}</td>
              <td>
                <span title="{{ dataItem.bankName }}">{{
                  dataItem.bankName | limitWord : LIMIT_LENGTH_WORD_CONST.MEDIUM
                }}</span>
              </td>
              <td>
                <img
                  height="60px"
                  [src]="dataItem.iconUrL | async"
                  class="rounded"
                  alt=""
                />
              </td>
              <td class="text-center">
                <!-- {{ ENTITY_STATUS_MAP[dataItem.status || 0] | translate }} -->
                <span
                  class="badge"
                  [ngClass]="ENTITY_STATUS_MAP[dataItem.status || 0].style"
                  >{{
                    ENTITY_STATUS_MAP[dataItem.status || 0].label | translate
                  }}</span
                >
              </td>
              <td class="text-center">
                <button
                  ngbTooltip="{{ 'common.action.detail' | translate }}"
                  class="btn px-1 py-0"
                  (click)="detail(dataItem.bankId)"
                >
                  <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                </button>
                <button
                  ngbTooltip="{{ 'common.action.update' | translate }}"
                  class="btn px-1 py-0"
                  (click)="update(dataItem?.bankId)"
                  *hasPrivileges="SYSTEM_RULES.BANK_WRITE"
                >
                  <i class="fa fa-edit mb-color" aria-hidden="true"></i>
                </button>
                <button
                  [ngbTooltip]="
                    (dataItem.status === ENTITY_STATUS_CONST.ACTIVE.code
                      ? 'common.action.lock'
                      : 'common.action.unlock'
                    ) | translate
                  "
                  class="btn px-1 py-0"
                  (click)="lockAndUnlock(dataItem)"
                  *hasPrivileges="
                    dataItem.status === 1
                      ? SYSTEM_RULES.BANK_LOCK
                      : SYSTEM_RULES.BANK_UNLOCK
                  "
                >
                  <i
                    [className]="
                      dataItem.status === 1
                        ? 'fa fa-lock mb-color'
                        : 'fa fa-unlock mb-color'
                    "
                    aria-hidden="true"
                  ></i>
                </button>
                <!--*ngIf="dataItem.approvalStatus === 1 && (dataItem.approvalType === 1 || dataItem.approvalType === 2)"-->
                <button
                  class="btn px-1 py-0"
                  ngbTooltip="{{ 'common.action.delete' | translate }}"
                  (click)="delete(dataItem)"
                  *hasPrivileges="SYSTEM_RULES.BANK_DELETE"
                >
                  <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="row d-block text-center m-0" *ngIf="banks?.length === 0">
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>

        <div *ngIf="banks.length">
          <mat-paginator
            [length]="formBankSearch.value.length"
            [pageSize]="formBankSearch.value.pageSize"
            [pageIndex]="formBankSearch.value.pageIndex"
            [pageSizeOptions]="pageSizeOptions"
            (page)="onChangePage($event)"
            aria-label="Select page"
          >
          </mat-paginator>
        </div>
      </div>
    </div>
  </div>
</section>
