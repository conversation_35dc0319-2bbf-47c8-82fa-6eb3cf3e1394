import { Component, OnInit } from '@angular/core';
import { Form<PERSON><PERSON>er, FormGroup } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { AbstractDomainManageComponent } from '@core/components/abstract-domain-manage/abstract-domain-manage.component';
import { TranslateService } from '@ngx-translate/core';
import {
  ENTITY_STATUS_CONST,
  FILE_EXTENSION,
  MODAL_ACTION,
  PAGINATION,
} from '@shared/constants/app.constants';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { IBaseResponseModel } from '@shared/models/base/base.model';
import { IDotp, ISearchDotp } from '@shared/models/dotp.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { DotpService } from '@shared/services/dotp.service';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'dotp-management',
  templateUrl: './dotp-management.component.html',
  styleUrls: ['./dotp-management.component.scss'],
})
export class DotpManagementComponent
  extends AbstractDomainManageComponent<IDotp>
  implements OnInit
{
  resource: RESOURCE_TYPE = RESOURCE_CONST.DOTP;
  searchForm: FormGroup = new FormGroup({});
  searchResults: IBaseResponseModel<IDotp[]> = {};
  data: IDotp[] = [];

  // Constants
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  MODAL_ACTION = MODAL_ACTION;

  // Pagination options
  pageSizeOptions = PAGINATION.OPTIONS;

  // Status options
  statusOptions = [
    { value: null, label: 'common.all' },
    { value: ENTITY_STATUS_CONST.ACTIVE.code, label: 'common.status.active' },
    {
      value: ENTITY_STATUS_CONST.INACTIVE.code,
      label: 'common.status.inactive',
    },
  ];

  constructor(
    private fb: FormBuilder,
    private dotpService: DotpService,
    private translateService: TranslateService,
    private downloadService: DownloadService,
    private toastrService: ToastrCustomService
  ) {
    super(dotpService as any);
    this.initForm();
  }

  ngOnInit(): void {
    this.onSearch();
  }

  private initForm(): void {
    this.searchForm = this.fb.group({
      keyword: [''],
      status: [null],
      pageIndex: [PAGINATION.PAGE_NUMBER_DEFAULT],
      pageSize: [PAGINATION.PAGE_SIZE_DEFAULT],
      length: [0],
      hasPageable: [true],
    });
  }

  onSearch(): void {
    if (this.searchForm.valid) {
      const searchData: ISearchDotp = {
        ...this.searchForm.value,
      };

      this.dotpService.search(searchData).subscribe((res: any) => {
        if (res?.body) {
          this.data = res.body.content || [];
          this.searchForm.controls.length.setValue(res.body.totalElements || 0);
          this.searchResults = res.body;
        }
      });
    }
  }

  onReset(): void {
    this.searchForm.controls.keyword.reset();
    this.searchForm.controls.status.reset();
    this.onSearch();
  }

  onChangePage(event: PageEvent): void {
    this.searchForm.controls.pageIndex.setValue(event.pageIndex);
    this.searchForm.controls.pageSize.setValue(event.pageSize);
    this.onSearch();
  }

  onExport(): void {
    const searchData: ISearchDotp = {
      ...this.searchForm.value,
      hasPageable: false,
    };

    const fileName = this.translateService.instant('template.dotpManagement');
    const obFile = this.dotpService.export(searchData);

    this.downloadService.downloadFileWithObservableAndName(
      obFile,
      fileName,
      FILE_EXTENSION.XLSX
    );
  }

  onDelete(item: IDotp): void {
    if (item.status === ENTITY_STATUS_CONST.ACTIVE.code) {
      const modalData = {
        title: 'dotp.confirm.delete',
        content: 'dotp.confirm.delete',
        interpolateParams: {
          customerName: `<b>${item.customerName || ''}</b>`,
        },
      };

      this.modalService.confirm(modalData).then((result) => {
        if (result === MODAL_ACTION.CONFIRM.code) {
          this.dotpService.delete({ dotpId: item.dotpId }).subscribe(() => {
            this.toastrService.success('dotp.delete.success');
            this.onSearch();
          });
        }
      });
    }
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.searchForm.value.pageIndex,
      this.searchForm.value.pageSize
    );
  }

  getStatusLabel(status: number): string {
    return status === ENTITY_STATUS_CONST.ACTIVE.code
      ? 'common.status.active'
      : 'common.status.inactive';
  }

  getStatusClass(status: number): string {
    return status === ENTITY_STATUS_CONST.ACTIVE.code
      ? 'badge-success'
      : 'badge-danger';
  }

  canDelete(item: IDotp): boolean {
    return item.status === ENTITY_STATUS_CONST.ACTIVE.code;
  }
}
