import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { IDotp, ISearchDotp } from '@shared/models/dotp.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';
import { AbstractDomainService } from './common/abstract.domain.service';

@Injectable({ providedIn: 'root' })
export class DotpService extends AbstractDomainService<IDotp> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.DOTP;

  cancel(request: any): Observable<EntityResponseType<any>> {
    return super.post<EntityResponseType<any>>(
      RESOURCE_CONST.DOTP.API.DELETE,
      request
    );
  }

  export(request: ISearchDotp): Observable<EntityResponseType<IDotp>> {
    return super.postFile(RESOURCE_CONST.DOTP.API.EXPORT, request);
  }
}
