import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, FormGroup } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { AbstractDomainManageComponent } from '@core/components/abstract-domain-manage/abstract-domain-manage.component';
import {
  MOMENT_CONST,
  PAGINATION,
  TRANSFER_TYPE,
  TRANSFER_TYPE_CONST,
  TRANSFER_TYPE_MAP,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { IBaseResponseModel } from '@shared/models/base/base.model';
import { ISavingTransaction } from '@shared/models/saving.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { SavingTransactionService } from '@shared/services/saving-transaction.service';
@Component({
  selector: 'app-saving-transaction-history',
  templateUrl: './saving-transaction-history.component.html',
  styleUrls: ['./saving-transaction-history.component.scss'],
})
export class SavingTransactionHistoryComponent
  extends AbstractDomainManageComponent<any>
  implements OnInit, OnDestroy
{
  resource: RESOURCE_TYPE = RESOURCE_CONST.SAVING;
  searchForm: FormGroup;
  searchResults: IBaseResponseModel<ISavingTransaction[]> = {};
  maxDate = new Date();
  data: ISavingTransaction[] = [];
  SYSTEM_RULES = SYSTEM_RULES;
  id = 0;

  PAGINATION = PAGINATION;
  MOMENT_CONST = MOMENT_CONST;
  pageSizeOptions = PAGINATION.OPTIONS;
  TRANSFER_TYPE_MAP = TRANSFER_TYPE_MAP;
  TRANSFER_TYPE_CONST = TRANSFER_TYPE_CONST;
  TRANSFER_TYPE = TRANSFER_TYPE;

  constructor(
    private savingTransactionService: SavingTransactionService,
    private fb: FormBuilder,
    protected route: ActivatedRoute
  ) {
    super(savingTransactionService);
    this.searchForm = this.fb.group({
      keyword: '',
      id: null,
      fromDate: null,
      toDate: null,
      transferType: null,
      length: PAGINATION.PAGE_NUMBER_DEFAULT,
      pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
      pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
    });
    this.route.params.subscribe((res) => {
      this.id = res.savingId;
    });
  }

  ngOnInit(): void {
    super.ngOnInit();
  }

  onParseValueSearchForm(): void {
    super.onParseValueSearchForm();
  }

  onSearch(): void {
    this.searchForm.controls.id?.setValue(this.id);
    this.savingTransactionService
      .savingTransaction(this.searchForm.value)
      .subscribe((res: any) => {
        this.data = res.body.content;
        this.searchForm.controls.length.setValue(res.body.totalElements, {
          emitEvent: false,
        });
      });
  }

  onReset(): void {
    this.searchForm.reset();
  }
}
