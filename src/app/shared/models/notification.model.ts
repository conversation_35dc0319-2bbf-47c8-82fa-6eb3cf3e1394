import {Moment} from 'moment';

export interface INotification {
  notificationId?: number;
  classPk?: number;
  title?: string;
  description?: string;
  content?: string;
  notificationStatus?: string;
  publishTime?: Moment;
  notificationType?: string;
  targetType?: string;
  topicName?: number;
  read?: boolean;
  endUserType?: string;
}

export class Notification implements INotification {
  constructor(
    public classPk?: number,
    public notificationId?: number,
    public title?: string,
    public description?: string,
    public content?: string,
    public notificationStatus?: string,
    public publishTime?: Moment,
    public notificationType?: string,
    public targetType?: string,
    public topicName?: number,
    public read?: boolean,
    public endUserType?: string
  ) {
    this.classPk = classPk;
    this.notificationId = notificationId;
    this.title = title;
    this.description = description;
    this.content = content;
    this.notificationStatus = notificationStatus;
    this.publishTime = publishTime;
    this.notificationType = notificationType;
    this.targetType = targetType;
    this.topicName = topicName;
    this.read = read;
    this.endUserType = endUserType;
  }
}
