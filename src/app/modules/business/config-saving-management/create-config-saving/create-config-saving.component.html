<div class="modal-content">
  <div class="modal-header">
    <h5 class="modal-title">
      {{
        type === ROUTER_ACTIONS.create
          ? ("model.configSaving.create" | translate)
          : type === ROUTER_ACTIONS.update
          ? ("model.configSaving.update" | translate)
          : ("model.configSaving.view" | translate)
      }}
    </h5>
    <button
      type="button"
      class="close"
      data-dismiss="modal"
      aria-label="Close"
      (click)="activeModal.close()"
    >
      <span aria-hidden="true"><i class="bi bi-x-lg"></i></span>
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="formCreate">
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-12">
            <div class="form-group">
              <label
                >{{ "model.configSaving.type" | translate }}
                <span class="text-danger small-text"> *</span></label
              >
              <ng-select
                appearance="outline"
                [searchable]="false"
                [clearable]="true"
                formControlName="savingType"
                placeholder="{{ 'model.configSaving.type' | translate }}"
              >
                <ng-option
                  *ngFor="let item of SAVING_TYPE"
                  [value]="item.value"
                >
                  <span>{{ item.label | translate }}</span>
                </ng-option></ng-select
              >
              <small
                class="text-danger"
                *ngIf="
                  formCreate.get('savingType')?.errors?.required &&
                  formCreate.get('savingType')?.touched
                "
              >
                {{ "model.configSaving.error.type" | translate }}
              </small>
            </div>
          </div>
          <div class="col-md-12">
            <div class="form-group">
              <label>
                {{ "model.configSaving.tenor" | translate
                }}<span class="text-danger small-text"> *</span></label
              >
              <input
                placeholder="{{
                  'model.configSaving.tenor' | placeholder
                }}"
                type="text"
                class="form-control w-100"
                formControlName="tenor"
                [disabled]="true"
              />
              <small
                class="text-danger"
                *ngIf="
                  formCreate.get('tenor')?.errors?.required &&
                  formCreate.get('tenor')?.touched
                "
              >
                {{ "model.configSaving.error.tenor" | translate }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formCreate.get('tenor')?.errors?.pattern &&
                  formCreate.get('tenor')?.touched
                "
              >
                {{ "model.configSaving.pattern.tenor" | translate }}
              </small>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="text-center pb-4">
    <button
      type="button"
      class="btn mb-btn-outline-color mr-3"
      (click)="activeModal.close()"
    >
      {{ "common.action.close" | translate }}
    </button>
    <ng-container *ngIf="type !== ROUTER_ACTIONS.view">
      <button type="button" class="btn mb-btn-color" (click)="onSubmit()">
        {{
          (type === ROUTER_ACTIONS.create
            ? "common.action.create"
            : "common.action.update"
          ) | translate
        }}
      </button>
    </ng-container>
  </div>
</div>
