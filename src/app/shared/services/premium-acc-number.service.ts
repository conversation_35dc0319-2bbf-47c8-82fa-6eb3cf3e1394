import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import {
  IPremiumAccNumber,
  IPremiumAccNumberSearch,
  IQueryPremiumAccNumber,
} from '@shared/models/premium-acc-number.modal';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { Observable } from 'rxjs';
import { AbstractDomainService } from './common/abstract.domain.service';
import { EntityResponseType } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class PremiumAccNumberService extends AbstractDomainService<IPremiumAccNumber> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.PREMIUM_ACCOUNT_NUMBER;
  public resourceUrl = '/premium-account-number';

  /**
   * API search
   *
   * @param request IPagination
   * @returns Observable<EntityResponseType<IReferral[]>>
   */
  searchSold(
    request: IPremiumAccNumberSearch
  ): Observable<EntityResponseType<IPremiumAccNumber[]>> {
    return super.post<EntityResponseType<IPremiumAccNumber[]>>(
      RESOURCE_CONST.PREMIUM_ACCOUNT_NUMBER.API.SEARCH_SOLD,
      request
    );
  }

  /**
   * API search
   *
   * @param request IPagination
   * @returns Observable<EntityResponseType<IReferral[]>>
   */
  searchPremiumAccNumber(
    request: IPremiumAccNumberSearch
  ): Observable<EntityResponseType<IQueryPremiumAccNumber[]>> {
    return super.post<EntityResponseType<IQueryPremiumAccNumber[]>>(
      RESOURCE_CONST.PREMIUM_ACCOUNT_NUMBER.API.SEARCH_ACC_NUMBER,
      request
    );
  }

  searchPremiumAccNumberCustomer(
    request: IPremiumAccNumberSearch
  ): Observable<EntityResponseType<IQueryPremiumAccNumber>> {
    return super.post<EntityResponseType<IQueryPremiumAccNumber>>(
      RESOURCE_CONST.PREMIUM_ACCOUNT_NUMBER.API.SEARCH_ACC_NUMBER_CUSTOMER,
      request
    );
  }

  /**
   * API export
   *
   * @param request IMerchantSearch
   * @returns Observable<any>
   */
  exportSold(request: IPremiumAccNumberSearch): Observable<any> {
    return super.postFile(
      RESOURCE_CONST.PREMIUM_ACCOUNT_NUMBER.API.EXPORT_SOLD,
      request
    );
  }

  /**
   * API unlock
   *
   * @param request IPosition
   * @returns Observable<EntityResponseType<IPosition>>
   */
  notificationSold(
    request: IPremiumAccNumberSearch
  ): Observable<EntityResponseType<IPremiumAccNumber>> {
    return super.post<EntityResponseType<IPremiumAccNumber>>(
      RESOURCE_CONST.PREMIUM_ACCOUNT_NUMBER.API.NOTIFICATION,
      request
    );
  }

  /**
   * API export
   *
   * @param request IMerchantSearch
   * @returns Observable<any>
   */
  exportPremiumNumber(request: IPremiumAccNumberSearch): Observable<any> {
    return super.postFile(
      RESOURCE_CONST.PREMIUM_ACCOUNT_NUMBER.API.EXPORT_PREMIUM_ACC_NUMBER,
      request
    );
  }

  /**
   * API check avaiable
   *
   * @param request IPremiumAccNumber
   * @returns Observable<EntityResponseType<IPremiumAccNumber>>
   */
  checkAvaiable(request: any): Observable<EntityResponseType<IPremiumAccNumber>> {
    return super.post<EntityResponseType<IPremiumAccNumber>> (
      RESOURCE_CONST.PREMIUM_ACCOUNT_NUMBER.API.CHECK_AVAIABLE,
      request
    );
  }

  /**
   * API request otp
   *
   * @param request IPremiumAccNumber
   * @returns Observable<EntityResponseType<IPremiumAccNumber>>
   */
  requestOtp(request: any): Observable<EntityResponseType<IPremiumAccNumber>> {
    return super.post<EntityResponseType<IPremiumAccNumber>> (
      RESOURCE_CONST.PREMIUM_ACCOUNT_NUMBER.API.REQUEST_OTP,
      request
    );
  }

  /**
   * API request otp
   *
   * @param request IPremiumAccNumber
   * @returns Observable<EntityResponseType<IPremiumAccNumber>>
   */
  confirmOtp(request: any): Observable<EntityResponseType<IPremiumAccNumber>> {
    return super.post<EntityResponseType<IPremiumAccNumber>> (
      RESOURCE_CONST.PREMIUM_ACCOUNT_NUMBER.API.CONFIRM_OTP,
      request
    );
  }
}
