<!-- <div *ngIf="this.activeToken.token">
  <p *ngIf="!activeToken.user?.active && !expire24Hour">
    active-user works! check mail send user and password
  </p>
  <p *ngIf="!active && !expire24Hour">
    active-user works! check mail send user and password
  </p>
  <p *ngIf="activeToken.user?.active && !expire24Hour">
    user already is active
  </p>
  <p *ngIf="active && !expire24Hour">user already is active</p>
  <div class="d-block text-center mb-3 mt-4" *ngIf="expire24Hour && !active">
    <button class="btn btn-white" (click)="onReSend()">
      {{ "public.resend" | translate }}
    </button>
  </div>
</div> -->

<main class="bg-login d-flex align-items-center min-vh-100 py-3 py-md-0">
  <app-change-language></app-change-language>
  <div class="wrap-login">
    <div class="login-card">
      <div class="row no-gutters">
        <div class="col-lg-12 col-md-12">
          <div class="card-body">
            <div class="brand-wrapper text-center">
              <img src="../../assets/dist/img/mblogo.png" alt="logo login" />
            </div>
            <div
              *ngIf="
                action === ACTION_TOKEN_CONST.RESEND_LINK_ACTIVATE &&
                !isResendSucces
              "
            >
              <div class="row text-center">
                <div
                  class="title"
                  [innerHTML]="displayContent('public.titleResend')"
                ></div>
              </div>
              <div class="text-center">
                <div>
                  <span class="title-link" (click)="onReSend()">{{
                    "public.resendLink" | translate
                  }}</span>
                </div>
              </div>
            </div>
            <div
              *ngIf="
                action === ACTION_TOKEN_CONST.RESEND_LINK_ACTIVATE &&
                isResendSucces
              "
            >
              <div class="row text-center">
                <div
                  class="title"
                  [innerHTML]="displayContent('public.titleResendSuccess')"
                ></div>
              </div>
            </div>
            <div
              *ngIf="
                action === ACTION_TOKEN_CONST.ACTIVATED ||
                action === ACTION_TOKEN_CONST.SEND_LINK_ACTIVATE
              "
            >
              <div class="row text-center">
                <div
                  class="title"
                  [innerHTML]="displayContent('public.titleActive')"
                ></div>
              </div>
            </div>
            <button class="btn btn-block login-btn mt-5" (click)="goToLogin()">
              {{ "login.loginButton" | translate }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>
