import { IQrCode } from './qr-code.model';

export interface IMerchant {
  merchantId?: number;
  parentId?: number;
  merchantName?: string;
  merchantCode?: string;
  merchantAccountNumber?: string;
  merchantBankCode?: string;
  merchantType?: string;
  channel?: string;
  description?: string;
  status?: number;
  child?: IMerchant[];
  qrCode?: IQrCode;
  serviceType?: string;
  balance?: number;
  origin?: string;
  currency?: string;
  createdDateStr?: string;
  createdDate?: string;
  cif?: string;
  phoneNumber?: string;
  totalQuantity?: number;
  totalAmount?: number;
  feeId?: number;
}

export interface IMerchantCodeRequest {
  serviceType: string;
}

export interface IMerchantCode {
  commonId?: number;
  code?: string;
  value?: string;
  status?: number;
  category?: string;
}
