import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { MOMENT_CONST } from '@shared/constants/app.constants';
import { HTTP_HEADERS } from '@shared/constants/http.constants';
import * as fileSaver from 'file-saver';
import * as moment from 'moment';
import { Observable } from 'rxjs';
import { ToastrCustomService } from './toastr-custom.service';

@Injectable({ providedIn: 'root' })
export class DownloadService {
  constructor(private http: HttpClient, private toast: ToastrCustomService) {}

  downloadFile(url: string): void {
    this.downloadFileWithObservable(
      this.http.get(url, { observe: 'response', responseType: 'blob' })
    );
  }

  // downloadFileWithName(url: string, fileName: string): void {
  //   this.downloadFileWithObservableAndName(
  //     this.http.get(url, { observe: 'response', responseType: 'blob' }),
  //     fileName
  //   );
  // }

  downloadFileWithObservable(ob: Observable<any>): void {
    ob.subscribe(
      (res) => {
        const blob: any = new Blob([res.body], { type: res.body.type });
        const fileName =
          res.headers.get('X-Action-Mesage') ??
          'file_download_' +
            moment(Date.now()).format('YYYYMMDDHHmmss') +
            '.xlsx';

        fileSaver.saveAs(blob, fileName);
      },
      () => {
        this.toast.error('error.file.not-found');
      }
    );
  }

  /**
   * Download file with name
   *
   * @param ob Observable<any>
   * @param fileName string
   */
  downloadFileWithObservableAndName(
    ob: Observable<any>,
    fileName: string,
    fileExtension: string
  ): void {
    ob.subscribe((res) => {
      const blob: any = new Blob([res.body], { type: res.body.type });
      const _fileName =
        fileName ??
        res.headers[HTTP_HEADERS.CONTENT_DISPOSITION]?.split(
          HTTP_HEADERS.CONTENT_FILENAME
        )[1];

      fileSaver.saveAs(
        blob,
        _fileName +
          '_' +
          moment(Date.now()).format(MOMENT_CONST.TIMESTAMP_FORMAT) +
          fileExtension
      );
    });
  }
}
