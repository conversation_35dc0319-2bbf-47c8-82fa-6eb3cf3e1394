import { Injectable } from '@angular/core';
import { IDotp, ISearchDotp } from '@shared/models/dotp.model';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({ providedIn: 'root' })
export class DotpService extends AbstractService {
  public resourceUrl = '/dotp';

  search(
    request: ISearchDotp
  ): Observable<EntityResponseType<IDotp>> {
    return super.post<EntityResponseType<IDotp>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  detail(request: any): Observable<EntityResponseType<IDotp>> {
    return super.post<EntityResponseType<IDotp>>(
      `${this.resourceUrl}/detail`,
      request
    );
  }

  delete(request: any): Observable<EntityResponseType<any>> {
    return super.post<EntityResponseType<any>>(
      `${this.resourceUrl}/delete`,
      request
    );
  }

  export(
    request: ISearchDotp
  ): Observable<EntityResponseType<IDotp>> {
    return super.postFile(`${this.resourceUrl}/export`, request);
  }
}
