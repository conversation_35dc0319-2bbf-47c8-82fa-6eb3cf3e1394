import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import {
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  MODAL_ACTION,
  MOMENT_CONST,
  PAGINATION
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { LANGUAGES } from '@shared/constants/language.constants';
import { IServicePackFunction } from './../../../shared/models/service-pack.model';

import { Router } from '@angular/router';
import { AbstractDomainManageComponent } from '@core/components/abstract-domain-manage/abstract-domain-manage.component';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { SELECTED_CONST } from '@shared/constants/user.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ISearchWithPagination } from '@shared/models/base/base-request.model';
import { IBaseResponseModel } from '@shared/models/base/base.model';
import { TransactionFeeTypeSearch } from '@shared/models/request/transaction-fee-type.search';
import { IClient, IServicePack, IServicePackType } from '@shared/models/service-pack.model';
import { ITransactionFeeType } from '@shared/models/transaction-fee-type.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { ClientService } from '@shared/services/client.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ServicePackService } from '@shared/services/service-pack.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-service-pack-management',
  templateUrl: './service-pack-management.component.html',
  styleUrls: ['./service-pack-management.component.scss'],
})
export class ServicePackManagementComponent
  extends AbstractDomainManageComponent<any>
  implements OnInit
{
  resource: RESOURCE_TYPE = RESOURCE_CONST.SERVICE_PACK;
  searchForm: FormGroup = new FormGroup({});
  searchResults: IBaseResponseModel<IClient[]> = {};
  transactionFeeSearch: TransactionFeeTypeSearch = {};
  maxDate = new Date();
  data: IServicePack[] = [];
  clients: IClient[] = [];
  servicePackTypes: ITransactionFeeType[] = [];

  pageSizeOptions = PAGINATION.OPTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  LANGUAGES = LANGUAGES;
  MOMENT_CONST = MOMENT_CONST;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  SELECTED_CONST = SELECTED_CONST;
  VALIDATORS = VALIDATORS;

  constructor(
    private fb: FormBuilder,
    protected router: Router,
    private servicePackService: ServicePackService,
    private translateService: TranslateService,
    private clientService: ClientService,
    private toastService: ToastrCustomService
  ) {
    super(servicePackService);
    this.searchForm = this.fb.group({
      keyword: '',
      types: [],
      clientIds: [],
      status: null,
      fromDate: null,
      toDate: null,
      pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
      pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
      length: 0,
      hasPageable: true,
    });
  }

  ngOnInit(): void {
    this.getServicePackTypes();
    this.getClient();
    super.ngOnInit();
  }

  onParseValueSearchForm(): void {
    super.onParseValueSearchForm();
  }

  onSearch() {
    if (this.searchForm.invalid) {
      CommonUtils.markFormGroupTouched(this.searchForm);
      return;
    }
    if (this.searchForm.value.types?.length > 0) {
      this.searchForm.value.types = this.searchForm.value.types.map((item: IServicePackType) => item.transferType ? item.transferType : item);
    }
    this.servicePackService
      .search(this.searchForm.value)
      .subscribe((res: any) => {
        this.data = res.body.content;
        this.searchForm.controls.length.setValue(res.body.totalElements, {
          emitEvent: false,
        });
      });
  }

  onReset() {
    this.searchForm.controls.keyword.reset();
    this.searchForm.controls.fromDate.reset();
    this.searchForm.controls.toDate.reset();
    this.searchForm.controls.types.setValue(null);
    this.searchForm.controls.clientIds.setValue(null);
    this.searchForm.controls.status.reset();
  }

  getClient(): void {
    const params = {
      hasPageable: false,
    };
    this.clientService.search(params as ISearchWithPagination).subscribe((response: any) => {
      this.clients = response.body.content;
    });
  }

  getServicePackTypes(): void {
    this.servicePackService
      .getAllServicePackType(this.transactionFeeSearch)
      .subscribe((response: any) => {
        this.servicePackTypes = response.body?.content;
      });
  }

  detail(id?: number) {
    this.router.navigate([
      ROUTER_UTILS.servicePack.root,
      id,
      ROUTER_ACTIONS.detail,
    ]);
  }

  onUpdate(id?: number) {
    this.router.navigate([
      ROUTER_UTILS.servicePack.root,
      id,
      ROUTER_ACTIONS.update,
    ]);
  }

  onCreate() {
    this.router.navigate([
      ROUTER_UTILS.servicePack.root,
      ROUTER_ACTIONS.create,
    ]);
  }

  onSelectAll(value?: string) {
    const selectedClientId = this.clients.map(
      (item) => item.clientId
    );
    const selectedType = this.servicePackTypes.map((item) => item.transactionFeeTypeCode);
    switch (value) {
      case SELECTED_CONST.CLIENT_ID:
        this.searchForm
          .get('clientIds')
          ?.patchValue(selectedClientId);
        break;
      case SELECTED_CONST.SERVICE_PACK_TYPE:
        this.searchForm.get('types')?.patchValue(selectedType);
        break;
    }
  }

  onClearAll(value?: string) {
    switch (value) {
      case SELECTED_CONST.CLIENT_ID:
        this.searchForm.get('clientIds')?.patchValue([]);
        break;
      case SELECTED_CONST.SERVICE_PACK_TYPE:
        this.searchForm.get('types')?.patchValue([]);
        break;
    }
  }

  /**
   * check lock and unlock and call api
   *
   * @param customer ICustomer
   */
  lockAndUnlock(servicePack?: IServicePack): void {
    if (servicePack) {
      if (servicePack.status === ENTITY_STATUS_CONST.INACTIVE.code) {
        this.unLock(servicePack);
      } else {
        this.lock(servicePack);
      }
    }
  }

  /**
   * button click edit
   *
   * @param userId number
   */
  edit(id?: number): void {
    this.router.navigate([
      ROUTER_UTILS.servicePack.root,
      id,
      ROUTER_ACTIONS.update,
    ]);
  }

  /**
   * Lock user register
   *
   * @param user IUser
   */
  private lock(servicePack?: IServicePack) {
    const modalData = {
      title: 'servicePack.lock',
      content: 'servicePack.lockContent',
      interpolateParams: { name: `<b>${servicePack?.name || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { servicePackId: servicePack?.servicePackId };
        this.servicePackService.lock(params).subscribe((res) => {
          this.toastService.success('common.action.lockSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * UnLock user register
   *
   * @param user: IUser
   */
  private unLock(servicePack?: IServicePack) {
    const modalData = {
      title: 'servicePack.unlock',
      content: 'servicePack.unlockContent',
      interpolateParams: { name: `<b>${servicePack?.name || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { servicePackId: servicePack?.servicePackId };
        this.servicePackService.unlock(params).subscribe((res) => {
          this.toastService.success('common.action.unlockSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * delete
   *
   * @param user IUser
   */
  delete(servicePack?: IServicePack): void {
    // open modal
    const modalData = {
      title: 'servicePack.delete',
      content: 'servicePack.deleteContent',
      interpolateParams: { name: `<b>${servicePack?.name || ''}</b>` },
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { servicePackId: servicePack?.servicePackId };
        this.servicePackService.delete(params).subscribe((res: any) => {
          this.toastService.success('common.action.deleteSuccess');
          this.onSearch();
        });
      }
    });
  }

  customClientSearchFn(term: string, item: string) {
    if (item) {
      term = term.replace(/ + /g, ' ');
      term = term.trim();
      term = term.toLocaleLowerCase();
      return item.toLocaleLowerCase().indexOf(term) > -1;
    }
    return '';
  }

  customServicePackSearchFn(term: string, item: IServicePackFunction) {
    if (item?.name) {
      term = term.replace(/ + /g, ' ');
      term = term.trim();
      term = term.toLocaleLowerCase();
      return item?.name.toLocaleLowerCase().indexOf(term) > -1;
    }
    return '';
  }
}
