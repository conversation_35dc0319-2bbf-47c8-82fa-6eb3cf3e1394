export interface IMonitorLog {
  type?: string;
  clientMessageId?: string;
  method?: string;
  requestUri?: string;
  serviceName?: string;
  duration?: number;
  requestData?: string;
  responseData?: string;
  httpStatus?: string;
  errorCode?: string;
  errorDescription?: string;
  errorDetail?: string;
  createdDate?: string;
  lastModifiedDate?: string;
}

export class MonitorLog implements IMonitorLog {
  constructor(data: IMonitorLog) {
    Object.assign(this, data);
  }
}
