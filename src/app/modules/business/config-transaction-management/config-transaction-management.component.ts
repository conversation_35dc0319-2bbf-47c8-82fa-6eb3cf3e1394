import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  CONFIG_TRANS_STATUS,
  CONFIG_TRANS_STATUS_CONST,
  CONFIG_TRANS_STATUS_MAP,
  DATE_CONSTANT,
  FILE_EXTENSION,
  MODAL_ACTION,
  MOMENT_CONST,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IConfigTransaction } from '@shared/models/config-transaction.model';
import { ConfigTransactionService } from '@shared/services/config-transaction.service';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-config-transaction-management',
  templateUrl: './config-transaction-management.component.html',
  styleUrls: ['./config-transaction-management.component.scss'],
})
export class ConfigTransactionManagementComponent implements OnInit {
  formSearch: FormGroup = new FormGroup({});
  maxDate = new Date();
  data: IConfigTransaction[] = [];
  sourceZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  SYSTEM_RULES = SYSTEM_RULES;
  MOMENT_CONST = MOMENT_CONST;
  CONFIG_TRANS_STATUS_CONST = CONFIG_TRANS_STATUS_CONST;
  CONFIG_TRANS_STATUS = CONFIG_TRANS_STATUS;
  CONFIG_TRANS_STATUS_MAP = CONFIG_TRANS_STATUS_MAP;
  PAGINATION = PAGINATION;
  VALIDATOR = VALIDATORS;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  pageSizeOptions = PAGINATION.OPTIONS;
  ROUTER_UTILS = ROUTER_UTILS;
  DATE_CONSTANT = DATE_CONSTANT;
  constructor(
    private fb: FormBuilder,
    private router: Router,
    private configTransService: ConfigTransactionService,
    private downloadService: DownloadService,
    private translateService: TranslateService,
    private modalService: ModalService,
    private toastrService: ToastrCustomService
  ) {
    this.formSearch = this.fb.group({
      keyword: '',
      status: null,
      fromDate: null,
      toDate: null,
      pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
      pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
      length: 0,
      sourceZone: this.sourceZone,
    });
  }

  ngOnInit(): void {
    this.onSearch();
  }

  onSearchSubmit(): void {
    this.formSearch.controls.pageIndex.setValue(PAGINATION.PAGE_NUMBER_DEFAULT);
    this.formSearch.controls.length.setValue(PAGINATION.LENGTH_DEFAULT);
    this.onSearch();
  }

  onSearch(): void {
    const params = this.formSearch.value;
    params.sourceZone = this.sourceZone;
    this.configTransService.search(params).subscribe((res: any) => {
      this.data = res.body.content;
      this.formSearch.controls.length.setValue(res.body.totalElements);
    });
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      this.formSearch.value.pageIndex,
      this.formSearch.value.pageSize
    );
  }

  onReset(): void {
    this.formSearch.controls.keyword.reset();
    this.formSearch.controls.status.reset();
    this.formSearch.controls.fromDate.reset();
    this.formSearch.controls.toDate.reset();
  }

  onDetail(transactionLimitId: number): void {
    this.router.navigate([
      ROUTER_UTILS.sysManage.configTransaction.root,
      transactionLimitId,
      ROUTER_ACTIONS.detail,
    ]);
  }

  onUpdate(transactionLimitId: number): void {
    this.router.navigate([
      ROUTER_UTILS.sysManage.configTransaction.root,
      transactionLimitId,
      ROUTER_ACTIONS.update,
    ]);
  }

  onChangePage(page: PageEvent): void {
    this.formSearch.controls.pageIndex.setValue(page.pageIndex);
    this.formSearch.controls.pageSize.setValue(page.pageSize);
    this.onSearch();
  }

  onExport(): void {
    const formSearch = this.formSearch.value;
    const fileName = this.translateService.instant('template.configTransLimit');
    const obFile = this.configTransService.export(formSearch);
    this.downloadService.downloadFileWithObservableAndName(
      obFile,
      fileName,
      FILE_EXTENSION.XLSX
    );
    return;
  }

  onDelete(id: number): void {
    const modalData = {
      title: 'model.configTransaction.modal.delete',
      content: 'model.configTransaction.modal.deleteConfigTransContent',
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { transactionLimitId: id };
        this.configTransService.delete(params).subscribe((res: any) => {
          this.toastrService.success('common.action.deleteSuccess');
          this.onSearch();
        });
      }
    });
  }
}
