::ng-deep
  .check-box-group.mat-checkbox
  .mat-checkbox-layout
  .mat-checkbox-inner-container {
  margin-top: 1px !important;
}

.check-box-group {
  margin-top: 40px;
  margin-left: 30px;
}

:host ::ng-deep .mat-checkbox-frame {
  border: 2px solid var(--mb-color);
}

:host ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background {
  background-color: var(--mb-color);
}

.freezer {
  position: none;
  right: 0;
  width: 130px;
}

:host ::ng-deep .status-freezer {
  position: sticky;
  right: 130px;
  width: 125px;
}

:host ::ng-deep .hidden-freezer {
  position: sticky;
  right: 255px;
}

:host ::ng-deep .hidden-freezer-td {
  position: sticky;
  right: 255px;
  width: 90px;
  background-color: white;
}

:host ::ng-deep .status-freezer-td {
  position: sticky;
  right: 130px;
  width: 90px;
  background-color: white;
}

:host ::ng-deep .freezer-td {
  position: sticky;
  right: 0;
  background-color: white;
  width: 130px;
}

.table-customer {
  min-width: 1440px;
}

.modal-max .modal-lg {
  max-width: 1000px;
}

.special-account.badge-primary {
  background-color: #1588f5;
}

.special-account.badge-warning {
  background-color: #ff9900;
}

.item-hidden {
  color: #cecece;
}
