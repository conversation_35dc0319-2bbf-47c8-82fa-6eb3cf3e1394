import { IUser } from './user.model';

export interface IActiveToken {
  activeTokenId?: number;
  userId?: number;
  token?: string;
  expireTime?: any;
  user?: IUser;
  timeNow?: any;
  isExpire?: boolean;
  tokenAction?: string;
  timeCountdown?: number;
}

export class ActiveToken implements IActiveToken {
  constructor(
    public activeTokenId?: number,
    public userId?: number,
    public token?: string,
    public expireTime?: any,
    public user?: IUser,
    public timeNow?: any,
    public isExpire?: boolean,
    public tokenAction?: string,
    public timeCountdown?: number
  ) {
    this.activeTokenId = activeTokenId;
    this.userId = userId;
    this.token = token;
    this.expireTime = expireTime;
    this.user = user;
    this.timeNow = timeNow;
    this.isExpire = isExpire;
    this.tokenAction = tokenAction;
    this.timeCountdown = timeCountdown;
  }
}
