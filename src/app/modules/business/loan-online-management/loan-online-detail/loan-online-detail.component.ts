import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { ENTITY_STATUS, GENDER } from '@shared/constants/app.constants';
import { MARITAL_STATUS } from '@shared/constants/customer.constants';
import { IIdCardType } from '@shared/models/id-card-type.model';
import { LoanOnline } from '@shared/models/loan-online.models';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { LoanOnlineService } from '@shared/services/loan-online.service';
import { ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-loan-online-detail',
  templateUrl: './loan-online-detail.component.html',
  styleUrls: ['./loan-online-detail.component.scss'],
})
export class LoanOnlineDetailComponent implements OnInit {
  // form control data
  formLoanOnlineDetail: FormGroup = new FormGroup({});
  // input call api
  loanOnlineDTO: LoanOnline = new LoanOnline();
  // input data form control
  loanOnline: LoanOnline = new LoanOnline();
  reader: FileReader = new FileReader();
  eKycPicUrls: string[] = [];
  idCardTypes: IIdCardType[] = [];

  GENDER_LIST = GENDER;
  ROUTER_UTILS = ROUTER_UTILS;
  ENTITY_STATUS = ENTITY_STATUS;
  MARITAL_STATUS = MARITAL_STATUS;

  constructor(
    private formBuilder: FormBuilder,
    protected toast: ToastrCustomService,
    protected translate: TranslateService,
    private loanOnlineService: LoanOnlineService,
    private routerActive: ActivatedRoute,
    private router: Router
  ) {
    // get id form paramater
    this.routerActive.paramMap.subscribe((res) => {
      const idParam = res.get('loanOnlineId');
      if (idParam) {
        this.loanOnlineDTO.loanOnlineId = +idParam;
      }
    });
  }

  ngOnInit(): void {
    this.getDetail();
  }

  /**
   * init form
   *
   * @param loanOnline LoanOnline
   */
  initForm(loanOnline?: LoanOnline): void {
    this.formLoanOnlineDetail = this.formBuilder.group({
      fullName: [loanOnline?.fullName || ''],
      dateOfBirth: [loanOnline?.dateOfBirth || ''],
      gender: [loanOnline?.gender],
      phoneNumber: [loanOnline?.phoneNumber || ''],
      maritalStatus: [loanOnline?.maritalStatus],
      email: [loanOnline?.email || ''],
      address: [loanOnline?.address || ''],
      job: [loanOnline?.job || ''],
      position: [loanOnline?.position || ''],
      workplace: [loanOnline?.workplace || ''],
      income: [
        loanOnline?.income ? CommonUtils.moneyFormat(loanOnline?.income) : '',
      ],
      loanProductId: [loanOnline?.loanProductId || ''],
      loanAmount: [
        loanOnline?.loanAmount
          ? CommonUtils.moneyFormat(loanOnline?.loanAmount)
          : '',
      ],
      loanTime: [loanOnline?.loanTime || ''],
      collateral: [loanOnline?.collateral || ''],
      status: [loanOnline?.status],
      loanProductName: [loanOnline?.loanProductName || ''],
    });
    this.formLoanOnlineDetail.disable();
  }

  /**
   * back to monitor list
   */
  backToList(): void {
    // this.router.navigate([ROUTER_UTILS.loanOnline.root]);
    window.history.back();
  }

  /**
   * get detail loan online
   */
  getDetail(): void {
    if (this.loanOnlineDTO.loanOnlineId) {
      this.loanOnlineService
        .detail({ loanOnlineId: this.loanOnlineDTO.loanOnlineId })
        .subscribe((res: any) => {
          this.loanOnline = res.body;
          const data = res.body || undefined;
          this.initForm(data);
        });
    }
  }
}
