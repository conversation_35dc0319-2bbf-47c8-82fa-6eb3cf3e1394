import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import {
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  MODAL_ACTION,
  MOMENT_CONST,
  PAGINATION
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { LANGUAGES } from '@shared/constants/language.constants';

import { Router } from '@angular/router';
import { AbstractDomainManageComponent } from '@core/components/abstract-domain-manage/abstract-domain-manage.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { SELECTED_CONST } from '@shared/constants/user.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IBaseResponseModel } from '@shared/models/base/base.model';
import { ICurrency } from '@shared/models/currency.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { CurrencyService } from '@shared/services/currency.service';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import { CurrencyCrudComponent } from './currency-crud/currency-crud.component';

@Component({
  selector: 'app-currency-management',
  templateUrl: './currency-management.component.html',
  styleUrls: ['./currency-management.component.scss'],
})
export class CurrencyManagementComponent
  extends AbstractDomainManageComponent<any>
  implements OnInit
{
  resource: RESOURCE_TYPE = RESOURCE_CONST.NUMBER_GROUP;
  searchForm: FormGroup = new FormGroup({});
  searchResults: IBaseResponseModel<ICurrency[]> = {};
  data: ICurrency[] = [];

  pageSizeOptions = PAGINATION.OPTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  LANGUAGES = LANGUAGES;
  MOMENT_CONST = MOMENT_CONST;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  SELECTED_CONST = SELECTED_CONST;
  VALIDATORS = VALIDATORS;

  constructor(
    private fb: FormBuilder,
    protected router: Router,
    private currencyService: CurrencyService,
    private translateService: TranslateService,
    private toastService: ToastrCustomService,
    private modal: NgbModal,
    private downloadService: DownloadService,
  ) {
    super(currencyService);
    this.searchForm = this.fb.group({
      keyword: '',
      status: null,
      pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
      pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
      length: 0,
      hasPageable: true,
    });
  }

  ngOnInit(): void {
    super.ngOnInit();
  }

  onParseValueSearchForm(): void {
    super.onParseValueSearchForm();
  }

  onSearch() {
    if (this.searchForm.invalid) {
      CommonUtils.markFormGroupTouched(this.searchForm);
      return;
    }
    this.currencyService
      .search(this.searchForm.value)
      .subscribe((res: any) => {
        this.data = res.body.content;
        this.searchForm.controls.length.setValue(res.body.totalElements, {
          emitEvent: false,
        });
      });
  }

  onReset() {
    this.searchForm.controls.keyword.reset();
    this.searchForm.controls.status.reset();
  }

  /**
   * delete
   *
   * @param user IUser
   */
  delete(currency?: ICurrency): void {
    // open modal
    const modalData = {
      title: 'currency.delete',
      content: 'currency.deleteContent',
      interpolateParams: { name: `<b>${currency?.name || ''}</b>` },
      confirmButtonText: 'common.action.delete', // Đổi text nút xác nhận thành "Xóa"
      cancelButtonText: 'common.action.cancel', // Đổi text nút hủy nếu cần
      // buttonOrder: 'cancel-first', // Hoặc 'confirm-first' tùy theo thứ tự bạn muốn
      // confirmBtnClass: 'btn-danger',
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { currencyId: currency?.currencyId };
        this.currencyService.delete(params).subscribe((res: any) => {
          this.toastService.success('common.action.deleteSuccess');
          if (this.data?.length === 1) {
            this.searchForm.controls.pageIndex.setValue(
              this.searchForm.controls.pageIndex.value === 0 ? '' : Number(this.searchForm.controls.pageIndex.value) - 1
            );
          }
          this.onSearch();
        });
      }
    });
  }

  create(id?: number): void {
    const modalRef = this.modal.open(CurrencyCrudComponent, {
      backdrop: 'static',
      size: 'lg',
      centered: true,
      keyboard: false,
    });
    modalRef.componentInstance.action = ROUTER_ACTIONS.create;
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;

    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }

  detail(id?: number): void {
    const modalRef = this.modal.open(CurrencyCrudComponent, {
      backdrop: 'static',
      size: 'lg',
      centered: true,
      keyboard: false,
    });
    modalRef.componentInstance.currencyId = id;
    modalRef.componentInstance.action = ROUTER_ACTIONS.detail;
  }

  update(id?: number): void {
    const modalRef = this.modal.open(CurrencyCrudComponent, {
      backdrop: 'static',
      size: 'lg',
      centered: true,
      keyboard: false,
    });
    modalRef.componentInstance.currencyId = id;
    modalRef.componentInstance.action = ROUTER_ACTIONS.update;
    modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;

    modalRef.result.then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        this.onSearch();
      }
    });
  }

  activeAndInactive(currency: ICurrency) {
    if (currency.status === ENTITY_STATUS_CONST.INACTIVE.code) {
      this.active(currency);
    } else {
      this.inactive(currency);
    }
  }

  /**
   * Lock
   *
   * @param currency
   */
  private inactive(currency: ICurrency) {
    const modalData = {
      title: 'currency.lock',
      content: 'currency.lockContent',
      interpolateParams: { name: `<b>${currency?.name || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { currencyId: currency?.currencyId };
        this.currencyService.inactive(params).subscribe((res) => {
          this.toastService.success('common.action.lockSuccess');
          this.onSearch();
        });
      }
    });
  }

  /**
   * UnLock
   *
   * @param currency
   */
  private active(currency: ICurrency) {
    const modalData = {
      title: 'currency.unlock',
      content: 'currency.unlockContent',
      interpolateParams: { name: `<b>${currency?.name || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { currencyId: currency?.currencyId };
        this.currencyService.active(params).subscribe((res) => {
          this.toastService.success('common.action.unlockSuccess');
          this.onSearch();
        });
      }
    });
  }
}
