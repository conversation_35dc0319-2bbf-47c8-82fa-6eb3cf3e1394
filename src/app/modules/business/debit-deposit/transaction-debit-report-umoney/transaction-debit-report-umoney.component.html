<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form
        [formGroup]="formTransactionReportSearch"
        (submit)="onSearchSubmit()"
      >
        <div class="row">
          <div class="col-md-4 col-lg-2">
            <div class="form-group">
              <label>{{ "common.action.searchKeyword" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="keyword"
                class="w-100"
                [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
                class="form-control"
                placeholder="{{ 'common.action.searchKeyword' | placeholder }}"
              />
            </div>
          </div>
          <div class="form-group col-md-4 col-lg-2">
            <label>{{ "model.merchant.transferType" | translate }}</label>
            <ng-select
              [multiple]="true"
              appearance="outline"
              [searchable]="true"
              [clearable]="true"
              formControlName="transferTypes"
              placeholder="{{ 'model.merchant.transferType' | translate }}"
            >
              <ng-option
                [value]="item.code"
                *ngFor="let item of TRANSACTION_DEBIT_DEPOSIT_ENTITY_TYPE"
              >
                {{ item.label | translate }}</ng-option
              >
              <ng-template ng-header-tmp>
                <div>
                  <button
                    class="btn btn-link"
                    type="button"
                    (click)="onSelectAll(SELECTED_CONST.TRANSACTION_TYPE)"
                  >
                    {{ "common.action.selectAll" | translate }}
                  </button>
                  <button
                    class="btn btn-link"
                    type="button"
                    (click)="onClearAll(SELECTED_CONST.TRANSACTION_TYPE)"
                  >
                    {{ "common.action.clearAll" | translate }}
                  </button>
                </div>
              </ng-template>
            </ng-select>
          </div>
          <div class="form-group col-md-4 col-lg-2">
            <label>{{
              "model.report.transaction.transactionStatus" | translate
            }}</label>
            <ng-select
              placeholder="{{
                'model.report.transaction.transactionStatus' | translate
              }}"
              [searchable]="false"
              formControlName="transactionStatus"
              [clearable]="true"
              [multiple]="true"
            >
              <ng-option
                [value]="item.code"
                *ngFor="let item of TRANSACTION_ENTITY_STATUS"
              >
                {{ item.label | translate }}
              </ng-option>
              <ng-template ng-header-tmp>
                <div>
                  <button
                    class="btn btn-link"
                    type="button"
                    (click)="onSelectAll(SELECTED_CONST.TRANSACTION_STATUS)"
                  >
                    {{ "common.action.selectAll" | translate }}
                  </button>
                  <button
                    class="btn btn-link"
                    type="button"
                    (click)="onClearAll(SELECTED_CONST.TRANSACTION_STATUS)"
                  >
                    {{ "common.action.clearAll" | translate }}
                  </button>
                </div>
              </ng-template>
            </ng-select>
          </div>
          <div class="col-lg-4 col-md-8">
            <div class="date-picker-container form-group row">
              <div class="col-md-6">
                <label
                  >{{ "common.action.fromDate" | translate
                  }}<span class="text-danger">*</span></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="fromDate"
                    formControlName="fromDate"
                    placeholder="DD/MM/YYYY"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="fromDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #fromDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formTransactionReportSearch.get('fromDate')?.errors
                      ?.required &&
                    formTransactionReportSearch.get('fromDate')?.touched
                  "
                >
                  {{ "error.required.fromDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formTransactionReportSearch.get('fromDate')?.errors
                      ?.invalidDate &&
                    formTransactionReportSearch.get('fromDate')?.touched
                  "
                >
                  {{ "error.required.inValidDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formTransactionReportSearch.get('fromDate')?.errors
                      ?.invalidMaxDate &&
                    formTransactionReportSearch.get('fromDate')?.touched
                  "
                >
                  {{
                    "error.maxDateCurrent"
                      | translate
                        : {
                            param: "common.action.fromDate" | translate
                          }
                  }}
                </small>
              </div>
              <div class="col-md-6">
                <label
                  >{{ "common.action.toDate" | translate
                  }}<span class="text-danger">*</span></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="toDate"
                    formControlName="toDate"
                    placeholder="DD/MM/YYYY"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    [max]="maxToDate"
                    min="{{
                      formTransactionReportSearch.controls['fromDate'].value
                        | date : MOMENT_CONST.FORMAT_DATE_MAT_PICKER
                    }}"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="toDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #toDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formTransactionReportSearch.get('toDate')?.errors
                      ?.required &&
                    formTransactionReportSearch.get('toDate')?.touched
                  "
                >
                  {{ "error.required.toDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formTransactionReportSearch.get('toDate')?.errors
                      ?.invalidMaxDate &&
                    formTransactionReportSearch.get('toDate')?.touched
                  "
                >
                  {{
                    "error.maxDateCurrent"
                      | translate
                        : {
                            param: "common.action.toDate" | translate
                          }
                  }}
                </small>
              </div>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
            >
              <div class="btn-reset">
                <i
                  class="bi bi-arrow-clockwise"
                  type="button"
                  (click)="onReset()"
                >
                </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" type="submit">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-4 mt-4">
          <button
            class="btn btn-red mr-2 w-auto"
            (click)="onConfigAutoReport()"
            *hasPrivileges="SYSTEM_RULES.DEBIT_DEPOSIT_CONFIG_MAIL_INFO"
          >
            {{ "model.report.transaction.configAutoButton" | translate }}
          </button>
          <button
            class="btn btn-red mr-2"
            type="button"
            (click)="exportFile()"
            *hasPrivileges="SYSTEM_RULES.DEBIT_DEPOSIT_TRANSACTION_EXPORT"
          >
            {{ "common.action.export" | translate }}
          </button>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table table-transaction">
          <thead>
            <tr>
              <th scope="col" class="text-center">
                {{ "common.no" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "managePayment.bank" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "managePayment.bankCode" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "managePayment.remark" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "managePayment.nameTransfer" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "managePayment.account" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{ "managePayment.amount" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{ "managePayment.fee" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.merchant.transferType" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "managePayment.fromMember" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{ "managePayment.fromaccount" | translate }}
              </th>
              <th scope="col" class="text-right">
                {{ "managePayment.fromUser" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "model.merchant.transactionHistory.date" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.report.transaction.clientMessageId" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.report.transaction.transID" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "managePayment.accTransactionCode" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "model.report.transaction.tradingResults" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of transactionReport; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>

              <td class="text-left" [title]="dataItem.bankName || ''">
                {{ dataItem.bankName | limitWord }}
              </td>
              <td class="text-left" [title]="dataItem.bankCode || ''">
                {{ dataItem.bankCode | limitWord }}
              </td>
              <td class="text-left" [title]="dataItem.message || ''">
                {{ dataItem.message | limitWord : 30 }}
              </td>
              <td class="text-left" [title]="dataItem.clientAccountName || ''">
                {{ dataItem.clientAccountName | limitWord : 20 }}
              </td>
              <td class="text-left">
                {{ dataItem.clientAccountNumber }}
              </td>

              <td class="text-right">
                {{ dataItem.transactionAmount | currencyLak }}
              </td>
              <td class="text-right">
                {{ dataItem.transactionFee | currencyLak }}
              </td>
              <td>{{ dataItem.transferTypeStr }}</td>
              <td class="text-left" [title]="dataItem.fromMember || ''">
                {{ dataItem.fromMember | limitWord : 30 }}
              </td>
              <td class="text-right" [title]="dataItem.fromAccount || ''">
                {{ dataItem.fromAccount | limitWord : 30 }}
              </td>
              <td class="text-right" [title]="dataItem.fromUser || ''">
                {{ dataItem.fromUser | limitWord : 30 }}
              </td>
              <td class="text-center">{{ dataItem.transactionStartTime }}</td>
              <td class="text-left">
                {{ dataItem.clientMessageId }}
              </td>
              <td class="text-left">
                {{ dataItem.transactionId }}
              </td>
              <td class="text-left">
                {{ dataItem.description }}
              </td>
              <td class="text-center">
                <span
                  class="badge"
                  [ngClass]="
                    TRANSACTION_ENTITY_STATUS_MAP[
                      dataItem.transactionStatus ||
                        ENTITY_TRANSACTION_STATUS_CONST.PROCESSING.code
                    ].style
                  "
                  >{{
                    TRANSACTION_ENTITY_STATUS_MAP[
                      dataItem.transactionStatus ||
                        ENTITY_TRANSACTION_STATUS_CONST.PROCESSING.code
                    ].label | translate
                  }}</span
                >
              </td>
            </tr>
          </tbody>
        </table>
        <div
          class="row d-block text-center m-0 no-search-result-wrapper"
          *ngIf="transactionReport?.length === 0"
        >
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
      </div>
      <div *ngIf="transactionReport.length">
        <mat-paginator
          [length]="formTransactionReportSearch.value.length"
          [pageSize]="formTransactionReportSearch.value.pageSize"
          [pageIndex]="formTransactionReportSearch.value.pageIndex"
          [pageSizeOptions]="pageSizeOptions"
          (page)="onChangePage($event)"
          aria-label="Select page"
        >
        </mat-paginator>
      </div>
    </div>
  </div>
</section>
