import { Component, OnInit } from '@angular/core';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  FormGroup,
  Validators,
} from '@angular/forms';
import {
  CLIENT_TYPE,
  CLIENT_TYPE_CONST,
  CLIENT_TYPE_MAP,
  DATE_RANGE_STATISTICAL_LOAN_ONLINE,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  FILE_EXTENSION,
  MOMENT_CONST,
  PAGINATION,
  SAVING_ACCOUNT_STATUS_MAP,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { LANGUAGES } from '@shared/constants/language.constants';

import { Router } from '@angular/router';
import { AbstractDomainManageComponent } from '@core/components/abstract-domain-manage/abstract-domain-manage.component';
import { TranslateService } from '@ngx-translate/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IBaseResponseModel } from '@shared/models/base/base.model';
import { IDotp } from '@shared/models/dotp.model';
import { IEventSearch } from '@shared/models/request/event.search';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { DotpService } from '@shared/services/dotp.service';
import { DownloadService } from '@shared/services/helpers/download.service';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';

@Component({
  selector: 'dotp-management',
  templateUrl: './dotp-management.component.html',
  styleUrls: ['./dotp-management.component.scss'],
})
export class DotpManagementComponent
  extends AbstractDomainManageComponent<any>
  implements OnInit
{
  resource: RESOURCE_TYPE = RESOURCE_CONST.DOTP;
  searchForm: FormGroup = new FormGroup({});
  searchResults: IBaseResponseModel<IDotp[]> = {};
  maxDate = new Date();
  data: IDotp[] = [];
  isErrorStartDateGreaterEndDate = false;

  pageSizeOptions = PAGINATION.OPTIONS;
  eventSearch: IEventSearch = {};
  SYSTEM_RULES = SYSTEM_RULES;
  LANGUAGES = LANGUAGES;
  MOMENT_CONST = MOMENT_CONST;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  SAVING_ACCOUNT_STATUS_MAP = SAVING_ACCOUNT_STATUS_MAP;
  CLIENT_TYPE_CONST = CLIENT_TYPE_CONST;
  CLIENT_TYPE_MAP = CLIENT_TYPE_MAP;
  CLIENT_TYPE = CLIENT_TYPE;

  constructor(
    private fb: FormBuilder,
    protected router: Router,
    private dotpService: DotpService,
    private translateService: TranslateService,
    private downloadService: DownloadService
  ) {
    super(dotpService as any);
    this.searchForm = this.fb.group({
      keyword: '',
      status: null,
      pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
      pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
      length: 0,
      hasPageable: true,
    });
  }

  ngOnInit(): void {
    const startDate = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDate = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.eventSearch.fromTime = startDate;
    this.eventSearch.toTime = endDate;
    this.searchForm.controls.fromDate.setValue(
      CommonUtils.reverseDate(startDate)
    );
    this.searchForm.controls.toDate.setValue(CommonUtils.reverseDate(endDate));
    super.ngOnInit();
  }

  onParseValueSearchForm(): void {
    super.onParseValueSearchForm();
  }

  onSearch() {
    if (this.searchForm.invalid) {
      CommonUtils.markFormGroupTouched(this.searchForm);
      return;
    }
    this.dotpService.search(this.searchForm.value).subscribe((res: any) => {
      this.data = res.body.content;
      this.searchForm.controls.length.setValue(res.body.totalElements, {
        emitEvent: false,
      });
    });
  }

  onReset() {
    const startDateReset = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDateReset = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.searchForm.controls.fromDate.setValue(
      CommonUtils.reverseDate(startDateReset)
    );
    this.searchForm.controls.toDate.setValue(
      CommonUtils.reverseDate(endDateReset)
    );
    this.searchForm.controls.fromDate.clearValidators();
    this.searchForm.controls.fromDate.setValidators([Validators.required]);
    this.searchForm.controls.fromDate.updateValueAndValidity();
    this.searchForm.controls.keyword.reset();
    this.searchForm.controls.status.reset();
    this.searchForm.controls.customerName.reset();
  }

  exportFile(): void {
    const fileName = this.translateService.instant('template.dotp');
    const obFile = this.dotpService.export(this.searchForm.value);
    this.downloadService.downloadFileWithObservableAndName(
      obFile,
      fileName,
      FILE_EXTENSION.XLSX
    );
    return;
  }
}
