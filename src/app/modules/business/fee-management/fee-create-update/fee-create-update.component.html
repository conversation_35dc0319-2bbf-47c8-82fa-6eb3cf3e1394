<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="backToList()" />
      <h5>
        {{
          isDetail
            ? ("fee.titleDetail" | translate)
            : isUpdate
            ? ("fee.titleUpdate" | translate)
            : ("fee.title" | translate)
        }}
      </h5>
    </div>
    <!-- <form [formGroup]="form" *ngIf="customer?.customerId"></form> -->
    <form [formGroup]="formFeeCreateUpdate">
      <div class="row">
        <div class="col-md-12">
          <div class="row">
            <h2 class="title-create">
              {{ "fee.feeInformationTitle" | translate }}
            </h2>
          </div>
          <div class="row border-create">
            <h3>{{ "fee.feeInformationTitle" | translate }}</h3>
            <div class="col-md-12">
              <div class="row">
                <div class="col-md-3">
                  <div class="form-group">
                    <label
                      >{{ "fee.nameFeeDetail" | translate }}
                      <span class="text-danger">*</span></label
                    >
                    <input
                      trim
                      formControlName="feeName"
                      [readOnly]="isDetail"
                      placeholder="{{ 'fee.nameFeeDetail' | translate }}"
                      type="text"
                      class="w-100"
                      class="form-control"
                      [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
                    />
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="
                        formFeeCreateUpdate.get('feeName')?.errors?.required &&
                        formFeeCreateUpdate.get('feeName')?.touched
                      "
                    >
                      {{ "error.fee.required.feeName" | translate }}
                    </small>
                    <small
                      class="form-text text-danger noti-small"
                      *ngIf="
                        formFeeCreateUpdate.get('feeName')?.errors?.maxlength &&
                        formFeeCreateUpdate.get('feeName')?.touched
                      "
                    >
                      {{
                        "error.fee.maxLength.feeName"
                          | translate
                            : {
                                param: VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH
                              }
                      }}
                    </small>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label
                      >{{ "fee.configurationFeeType" | translate }}
                      <span class="text-danger">*</span></label
                    >
                    <input
                      readonly
                      type="text"
                      value="{{
                        getConfigurationFeeType(configurationFeeType)
                          | translate
                      }}"
                      class="w-100"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="col-md-3" *ngIf="isUpdate || isDetail">
                  <div class="form-group">
                    <label>{{ "model.fee.createBy" | translate }}</label>
                    <input
                      formControlName="createdBy"
                      [readOnly]="true"
                      placeholder="{{ 'model.fee.createBy' | translate }}"
                      type="text"
                      class="w-100"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label>{{ "model.fee.feeType" | translate }}</label>
                    <input
                      formControlName="feeType"
                      readonly
                      placeholder="{{ 'model.fee.feeType' | translate }}"
                      type="text"
                      class="w-100"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="col-md-3" *ngIf="isUpdate || isDetail">
                  <div class="form-group">
                    <label>{{ "model.fee.createDate" | translate }}</label>
                    <input
                      [value]="fee.createdDate"
                      [readOnly]="true"
                      placeholder="{{ 'model.fee.createDate' | translate }}"
                      type="text"
                      class="w-100"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label
                      >{{ "common.status" | translate }}
                      <span class="text-danger">*</span></label
                    >
                    <ng-select
                      appearance="outline"
                      [readonly]="isDetail"
                      [searchable]="false"
                      placeholder="{{ 'common.status' | translate }}"
                      [clearable]="false"
                      formControlName="status"
                    >
                      <ng-option
                        [value]="item.code"
                        *ngFor="let item of ENTITY_STATUS"
                      >
                        {{ item.label | translate }}
                      </ng-option>
                    </ng-select>
                    <small
                      class="form-option text-danger noti-small"
                      *ngIf="
                        formFeeCreateUpdate.get('status')?.errors?.required &&
                        formFeeCreateUpdate.get('status')?.touched
                      "
                    >
                      {{ "error.fee.required.status" | translate }}
                    </small>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label
                      >{{ "model.fee.transactionFeeType" | translate }}
                      <span class="text-danger">*</span></label
                    >
                    <ng-select
                      (change)="getTransactionFee($event)"
                      appearance="outline"
                      [readonly]="isDetail"
                      [searchable]="false"
                      placeholder="{{
                        'model.fee.transactionFeeType' | translate
                      }}"
                      [clearable]="false"
                      formControlName="transactionFeeTypeId"
                    >
                      <ng-option
                        [value]="transactionFeeType?.transactionFeeTypeId"
                        *ngFor="let transactionFeeType of transactionFeeTypes"
                      >
                        {{ transactionFeeType?.transactionFeeTypeName }}
                      </ng-option>
                    </ng-select>
                    <small
                      class="form-option text-danger noti-small"
                      *ngIf="
                        formFeeCreateUpdate.get('transactionFeeTypeId')?.errors
                          ?.required &&
                        formFeeCreateUpdate.get('transactionFeeTypeId')?.touched
                      "
                    >
                      {{ "error.fee.required.transactionFeeType" | translate }}
                    </small>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label
                      >{{ "model.fee.merchantFeeType" | translate }}
                      <span class="text-danger" *ngIf="isTopup">*</span></label
                    >
                    <ng-select
                      appearance="outline"
                      [searchable]="false"
                      [readonly]="isDetail"
                      formControlName="merchantId"
                      placeholder="{{
                        'model.fee.merchantFeeType' | placeholder
                      }}"
                    >
                      <ng-option
                        *ngFor="let item of merchantTopup"
                        [value]="item.merchantId"
                      >
                        {{ item.merchantName }}
                      </ng-option>
                    </ng-select>
                    <small
                      *ngIf="
                        formFeeCreateUpdate?.get('merchantId')?.errors
                          ?.required &&
                        formFeeCreateUpdate.get('merchantId')?.touched
                      "
                      class="text-danger"
                    >
                      {{
                        "error.fee.required.merchantFeeType" | translate
                      }}</small
                    >
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-12">
              <div class="row"></div>
            </div>
          </div>
          <ng-container *hasPrivileges="SYSTEM_RULES.FEE_RATE_READ">
            <app-fee-rate
              *ngIf="isUpdate"
              [feeId]="feeId"
              [configurationFeeType]="configurationFeeType"
              [transactionFeeTypeId]="transactionFeeTypeId"
            ></app-fee-rate>
          </ng-container>
          <div class="d-block text-center mb-5 mt-4">
            <button
              class="btn btn-white mr-2"
              data-toggle="modal"
              (click)="backToList()"
            >
              {{ "common.action.back" | translate }}
            </button>

            <ng-container
              *hasPrivileges="
                isUpdate ? SYSTEM_RULES.FEE_WRITE : SYSTEM_RULES.FEE_CREATE
              "
            >
              <button
                *ngIf="!(action === ROUTER_ACTIONS.detail)"
                class="btn btn-red mr-2"
                data-toggle="modal"
                (click)="isUpdate ? onUpdate() : onCreate()"
              >
                {{ "common.action.save" | translate }}
              </button>
            </ng-container>
          </div>
        </div>
      </div>
    </form>
  </div>
</section>
