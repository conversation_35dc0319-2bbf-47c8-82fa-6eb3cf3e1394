<div class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formSearch" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-lg-3">
            <div class="form-group">
              <label>{{ "model.configSaving.type" | translate }}</label>
              <ng-select
                appearance="outline"
                [searchable]="false"
                [clearable]="true"
                formControlName="type"
                placeholder="{{ 'model.configSaving.type' | translate }}"
              >
                <ng-option
                  *ngFor="let item of SAVING_TYPE"
                  [value]="item.value"
                >
                  <span>{{ item.label | translate }}</span>
                </ng-option></ng-select
              >
            </div>
          </div>
          <div class="col-md-2 col-lg-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
              (click)="onReset()"
            >
              <div class="btn-reset">
                <i class="bi bi-arrow-clockwise" type="button"> </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" type="submit">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div
          class="d-flex justify-content-between align-items-center text-right mb-2 mt-4"
        >
          <h5>{{ "model.configSaving.title" | translate }}</h5>
          <button
            (click)="openCreate(ROUTER_ACTIONS.create)"
            class="btn btn-red mb-2"
            type="button"
            *hasPrivileges="SYSTEM_RULES.SAVING_CONFIGURATION_WRITE"
          >
            {{ "common.action.create" | translate }}
          </button>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table table-config-transaction">
          <thead>
            <tr>
              <th class="text-center" rowspan="2" [width]="'50px'">
                {{ "model.configTransaction.no" | translate }}
              </th>
              <th class="text-center" rowspan="2" [width]="'200px'">
                {{ "model.configSaving.type" | translate }}
              </th>
              <th rowspan="2" class="text-center" [width]="'90px'">
                {{ "model.configSaving.tenor" | translate }}
              </th>
              <th rowspan="2" class="text-center" [width]="'90px'">
                {{ "model.configSaving.tenorPeriod" | translate }}
              </th>
              <th [width]="'180px'" class="text-center">
                {{ "model.configTransaction.action" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of data; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td class="text-center">
                {{ SAVING_TYPE_MAP[item.savingType ?? ""].label | translate }}
              </td>
              <td class="text-center">{{ item?.tenor }}</td>
              <td class="text-center">
                {{ PERIOD_TYPE_MAP[item.tenorPeriod ?? ""].label | translate }}
              </td>
              <td class="text-center">
                <ng-container>
                  <button
                    ngbTooltip="{{ 'common.action.detail' | translate }}"
                    class="btn px-1 py-0"
                    (click)="openModal(item, ROUTER_ACTIONS.view)"
                    *hasPrivileges="SYSTEM_RULES.SAVING_CONFIGURATION_READ"
                  >
                    <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                  </button>
                  <button
                    ngbTooltip="{{ 'common.action.update' | translate }}"
                    class="btn px-1 py-0"
                    (click)="openModal(item, ROUTER_ACTIONS.update)"
                    *hasPrivileges="SYSTEM_RULES.SAVING_CONFIGURATION_WRITE"
                  >
                    <i class="fa fa-edit mb-color" aria-hidden="true"></i>
                  </button>
                  <button
                    ngbTooltip="{{ 'common.action.delete' | translate }}"
                    class="btn px-1 py-0"
                    (click)="onDelete(item.id)"
                    *hasPrivileges="SYSTEM_RULES.SAVING_CONFIGURATION_DELETE"
                  >
                    <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                  </button>
                </ng-container>
              </td>
            </tr>
          </tbody>
        </table>
        <div
          class="row d-block text-center m-0 no-search-result-container"
          *ngIf="data?.length === 0"
        >
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
      </div>
      <div *ngIf="data?.length" class="paginator col-md-12">
        <mat-paginator
          [length]="formSearch.value.length"
          [pageSize]="formSearch.value.size"
          [pageIndex]="formSearch.value.page"
          [pageSizeOptions]="pageSizeOptions"
          (page)="onChangePage($event)"
          aria-label="Select page"
        >
        </mat-paginator>
      </div>
    </div>
  </div>
</div>
