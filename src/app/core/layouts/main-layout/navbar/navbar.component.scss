.h-cus {
  height: 2.55em !important;
}

.mr-cus {
  margin-right: 1.15em !important;
}

.navbar-expand .navbar-nav .nav-link {
  padding-left: 0 !important;
  padding-right: 0.5em !important;
}

.navbar-nav > .user-menu > .dropdown-menu {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  padding: 0;
  width: 250px !important;
}

h1 {
  font-weight: 600;
  font-size: 20px;
  line-height: 35px;
  // color: #282828;
  line-height: 39px;
  margin-bottom: 0px;
}

.btn-logout {
  border: 1px solid #eb2d4b;
  border-radius: 8px;
  font-size: 14px;
  color: #eb2d4b;
  margin-left: 20px;
}

.notification {
  position: relative;
  padding-right: 20px;
  margin-top: 9px;
  cursor: pointer;
}

.notification span {
  background: #eb2d4b;
  font-size: 10px;
  color: white;
  padding: 1px 6px;
  border-radius: 50%;
  position: absolute;
  top: -5px;
  left: 7px;
}

::ng-deep .mat-menu-panel {
  // max-height: 500px !important;
  // overflow-y: scroll !important;
  max-height: unset !important;
  max-width: 800px !important;
  width: 800px !important;
  background: #f3ebdd;
}

::ng-deep .box-notification {
  width: 400px;
}
::ng-deep .box-notification .ant-popover-inner-content {
  padding: 0 !important;
}

.list-notifi {
  padding: 0px;
  height: 480px;
  margin-bottom: 0px;
}

.noti-scroll {
  overflow-y: scroll;
}

.list-notifi li {
  list-style: none;
  padding: 12px 20px;
  background: #fafdff;
  border-bottom: 1px solid #ccc;
}

.list-notifi li.ac-notification {
  background-color: #3994f124;
}

.noti-list .noti-item .ac-notification {
  background-color: #3994f124;
}

.list-notifi li p {
  margin-bottom: 0px;
}

.content-notifi {
  // width: 80%;
}

.content-notifi h6 {
  cursor: pointer;
}

.right-notifi {
  width: 20%;
  text-align: right;
  position: relative;
}

::ng-deep .mat-menu-panel::-webkit-scrollbar {
  width: 6px;
  background-color: #f5f5f5;
}

::ng-deep .mat-menu-panel::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 7px;
}

::ng-deep .mat-menu-panel::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  background-color: #f5f5f5;
}

.status-notifi {
  background: #eb2d4b;
  height: 16px;
  width: 16px;
  position: absolute;
  right: 0px;
  border-radius: 50%;
}

.time-notifi {
  margin-top: 35px;
}

.user-name {
  line-height: 37px;
  font-weight: 600;
  font-size: 14px;
  position: relative;
  margin-right: 20px;
  width: 128px;
}

.user-name::after {
  background: #d3d3d3;
  height: 27px;
  width: 1px;
  position: absolute;
  content: "";
  right: -20px;
  top: 6px;
}

.welcom {
  cursor: pointer;
  line-height: 37px;
  font-weight: 600;
  font-size: 14px;
  position: relative;
  margin-right: 20px;

  border: 1px solid #0054a5;
  border-radius: 8px;
  border: 1px solid rgba(0, 84, 165, 0);
  transition: border 0.5s ease-out;
  padding: 1px 10px;
  color: #0054a5;
}

.welcom:hover {
  border: 1px solid #0054a5;
  color: #0054a5;
}

.welcom::after {
  background: #d3d3d3;
  height: 27px;
  width: 1px;
  position: absolute;
  content: "";
  right: -20px;
  top: 6px;
}

.btn-user {
  border: 1px solid rgba(0, 84, 165, 0);
  transition: border 0.5s ease-out;
  margin-top: 4px;
  border-radius: 25%;
}

.btn-user:hover {
  border: 1px solid #0054a5;
  border-radius: 25%;
}

.user-card {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-size: cover;
  background-repeat: no-repeat;
  padding: 2rem 1rem;
  background-color: #f9fafe;
}

.icon {
  width: 90px;
  height: 95px;
}

.fullname {
  color: white;
  margin-left: 40px;
  font-size: large;
  font-weight: bold;
}

.mat-menu-content {
  background-color: "white" !important;
}

::ng-deep.mat-menu-panel {
  width: 400px !important;
  background-color: white;
}

.btn-red {
  margin-left: 130px;
  width: 150px;
}

.cart-container {
  background-color: #ffffff;
}

.avatar {
  cursor: pointer;
  padding: 30px 35px 0px 35px;
}

.avatar:hover {
  border: 1px solid white;
}

::ng-deep .navbar-nav .ng-dropdown-panel.ng-select-bottom {
  margin-top: -6px !important;
}

// .card-item:hover {
//   background-color: rgb(190, 190, 190, 0.1);
//   color: #0054A5;
// }

.title-color {
  color: rgb(20, 30, 210);
}

.load-more {
  color: var(--primary-color);
  background-color: var(--white);
  padding: 10px;
  text-align: center;
  cursor: pointer;
}

.noti-title {
  // display: flex;
  justify-content: center;
  // align-items: center;
}

.noti-bell {
  position: relative;
  cursor: pointer;
  padding: 10px;
}

.noti-bell:hover {
  transform: scale(1.1);
}

.bell-icon {
  font-size: 20px;
  color: rgb(20, 30, 210);
  animation: ring 2s infinite ease-in-out;
}

@keyframes ring {
  0%,
  100% {
    transform: rotate(0deg);
  }
  20% {
    transform: rotate(15deg);
  }
  40% {
    transform: rotate(-10deg);
  }
  60% {
    transform: rotate(5deg);
  }
  80% {
    transform: rotate(-5deg);
  }
}

.noti-badge {
  position: absolute;
  top: 0px;
  right: 1px;
  background: #f44336;
  color: white;
  font-size: 11px;
  font-weight: bold;
  border-radius: 10px;
  padding: 1px 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.noti-footer {
  text-align: center;
  padding: 10px;
  font-weight: bold;
  cursor: pointer;
  background: #f9f9f9;
  transition: background 0.2s;
  border-top: 1px solid #f0f0f0;
}

.notification-dropdown {
  position: absolute;
  right: 6%;
  width: 420px;
  background: white;
  border-radius: 8px;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  z-index: 100;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  font-weight: bold;
  background: #fff;
  border-bottom: 1px solid #eee;
}

.mark-all-read {
  font-size: 12px;
  background: none;
  border: none;
  color: blue;
  cursor: pointer;
}

.notification-tabs {
  display: flex;
  padding: 8px;
  background: #f5f5f5;
}

.tab {
  flex: 1;
  border: none;
  background: none;
  outline: none;
  padding: 8px;
  cursor: pointer;
  text-align: center;
  font-size: 14px;
}

.tab.active {
  color: white;
  outline: none;
  background: blue;
  border-radius: 4px;
}

.notification-list {
  max-height: 350px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #eee;
  position: relative;
  cursor: pointer;
  transition: background 0.3s;
}

.notification-item:hover {
  background: #e9e9e9;
}

.notification-icon-small {
  width: 34px;
  height: 34px;
  background: blue;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  margin-right: 10px;
}

.notification-content {
  width: 85%;
}

.notification-title {
  font-weight: bold;
}

.notification-time {
  font-size: 12px;
  margin-top: -12px;
}

.unread-dot {
  width: 8px;
  height: 8px;
  background: rgb(20, 30, 210);
  border-radius: 50%;
  position: absolute;
  right: 10px;
}
