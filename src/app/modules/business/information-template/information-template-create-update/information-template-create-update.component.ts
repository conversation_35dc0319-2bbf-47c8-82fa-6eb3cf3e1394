import { Component, HostListener, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import {
  ENTITY_STATUS,
  ENTITY_STATUS_CONST,
  MODAL_ACTION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IInformationTemplate } from '@shared/models/information-template.model';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { InformationTemplateService } from '@shared/services/information-template.service';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-information-template-create-update',
  templateUrl: './information-template-create-update.component.html',
  styleUrls: ['./information-template-create-update.component.scss'],
})
export class InformationTemplateCreateUpdateComponent implements OnInit {
  template!: IInformationTemplate;

  isUpdate = false;

  isDetail = false;

  informationtemplateId!: number;

  formInformationTemplate!: FormGroup;

  MODAL_ACTION = MODAL_ACTION;

  ENTITY_STATUS = ENTITY_STATUS;

  VALIDATORS = VALIDATORS;
  SYSTEM_RULES = SYSTEM_RULES;

  isHiddenBtnClose = false;

  constructor(
    private formBuilder: FormBuilder,
    private toastService: ToastrCustomService,
    public activeModal: NgbActiveModal,
    private informationTemplateService: InformationTemplateService
  ) {}

  ngOnInit(): void {
    this.initForm(this.template);
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.activeModal.close();
  }

  initForm(template?: IInformationTemplate) {
    this.formInformationTemplate = this.formBuilder.group({
      informationTemplateCode: [
        null,
        [
          Validators.required,
          Validators.maxLength(
            VALIDATORS.LENGTH.INFORMATION_TEMPLATE_CODE_MAX_LENGTH
          ),
        ],
      ],
      informationTemplateName: [
        null,
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.PATTERN_NAME),
          Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
        ],
      ],
      displayName: [
        null,
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.PATTERN_NAME),
          Validators.maxLength(
            VALIDATORS.LENGTH.INFORMATION_TEMPLATE_DISPLAY_NAME_MAX_LENGTH
          ),
        ],
      ],
      status: [
        { value: ENTITY_STATUS_CONST.ACTIVE.code, disabled: true },
        [Validators.required],
      ],
      informationTemplateId: [null],
      template: [
        { value: null, disabled: this.isDetail ? true : false },
        [
          Validators.required,
          Validators.maxLength(
            VALIDATORS.LENGTH.INFORMATION_TEMPLATE_MAX_LENGTH
          ),
        ],
      ],
    });

    if (this.template) {
      this.formInformationTemplate.patchValue({
        informationTemplateId: this.template.informationTemplateId,
        informationTemplateCode: this.template.informationTemplateCode,
        informationTemplateName: this.template.informationTemplateName,
        displayName: this.template.displayName,
        status: this.template.status,
        template: this.template.template,
      });
    }

    if (this.isUpdate || !this.informationtemplateId) {
      this.enableAll();
    }
    if (this.isDetail) {
      this.disableAll();
    }
  }

  enableAll() {
    this.formInformationTemplate.get('informationTemplateCode')?.enable();
    this.formInformationTemplate.get('informationTemplateName')?.enable();
    this.formInformationTemplate.get('displayName')?.enable();
    this.formInformationTemplate.get('template')?.enable();
  }

  disableAll() {
    this.formInformationTemplate.get('informationTemplateCode')?.disable();
    this.formInformationTemplate.get('informationTemplateName')?.disable();
    this.formInformationTemplate.get('displayName')?.disable();
    this.formInformationTemplate.get('template')?.disable();
  }

  onSave() {
    if (!this.isUpdate && this.formInformationTemplate.valid) {
      this.informationTemplateService
        .create(this.formInformationTemplate.value)
        .subscribe((result) => {
          if (result.ok && result.body) {
            this.activeModal.close(MODAL_ACTION.RESET.code);
            this.toastService.success('common.action.createSuccess');
          }
        });
    } else if (this.isUpdate) {
      this.informationTemplateService
        .update(this.formInformationTemplate.value)
        .subscribe((result) => {
          if (result.ok && result.body) {
            this.activeModal.close(MODAL_ACTION.RESET.code);
            this.toastService.success('common.action.updateSuccess');
          }
        });
    } else if (this.formInformationTemplate.invalid) {
      CommonUtils.markFormGroupTouched(this.formInformationTemplate);
    }
  }

  getTemplate(data: string) {
    this.formInformationTemplate.patchValue({
      template: data.split(VALIDATORS.PATTERN.SPACE_HTML).join(' '),
    });
  }
}
