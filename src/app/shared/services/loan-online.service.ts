import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { ILoanOnline } from '@shared/models/loan-online.models';
import { ILoanOnlineSearch } from '@shared/models/request/loan-online.search';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { Observable } from 'rxjs';
import { AbstractDomainService } from './common/abstract.domain.service';

@Injectable({ providedIn: 'root' })
export class LoanOnlineService extends AbstractDomainService<ILoanOnline> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.LOAN_ONLINE;

  constructor(protected http: HttpClient) {
    super(http);
  }
  /**
   * API export
   *
   * @param request ILoanOnlineSearch
   * @returns Observable<any>
   */
  export(request: ILoanOnlineSearch): Observable<any> {
    return super.postFile(RESOURCE_CONST.LOAN_ONLINE.API.EXPORT, request);
  }
}
