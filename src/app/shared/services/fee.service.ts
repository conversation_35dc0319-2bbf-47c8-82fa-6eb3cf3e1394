import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IFee } from '@shared/models/IFee.model';
import { IFeeSearch } from '@shared/models/request/fee.search';
import { Observable } from 'rxjs';
import { AbstractService, EntityResponseType } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class FeeService extends AbstractService {
  public resourceUrl = '/fee';

  constructor(protected http: HttpClient) {
    super(http);
  }

  /**
   * tim kiem cau hinh phi
   *
   * @param request
   */
  search(request: IFeeSearch): Observable<EntityResponseType<IFee>> {
    return super.post<EntityResponseType<IFee>>(
      `${this.resourceUrl}/search`,
      request
    );
  }

  /**
   * API delete
   *
   * @param request IEvent
   * @returns Observable<EntityResponseType<IFee>>
   */
  deleteFee(request: any): Observable<EntityResponseType<IFee>> {
    return super.post<EntityResponseType<IFee>>(
      `${this.resourceUrl}/delete`,
      request
    );
  }

  /**
   * API create
   *
   * @param request IEvent
   * @returns Observable<EntityResponseType<IEvent>>
   */
  create(request: IFee): Observable<EntityResponseType<IFee>> {
    return super.post<EntityResponseType<IFee>>(
      `${this.resourceUrl}/create`,
      request
    );
  }

  /**
   * API create
   *
   * @param request IEvent
   * @returns Observable<EntityResponseType<IFee>>
   */
  update(request: IFee): Observable<EntityResponseType<IFee>> {
    return super.post<EntityResponseType<IFee>>(
      `${this.resourceUrl}/update`,
      request
    );
  }

  /**
   * API get detail
   *
   * @param request
   */
  detail(request: any): Observable<EntityResponseType<IFee>> {
    return super.post<EntityResponseType<IFee>>(
      `${this.resourceUrl + '/detail'}`,
      request
    );
  }

  /**
   * API lock
   *
   * @param request IFee
   * @returns Observable<EntityResponseType<IFee>>
   */
  lock(request: any): Observable<EntityResponseType<IFee>> {
    return super.post<EntityResponseType<IFee>>(
      `${this.resourceUrl}/inactive`,
      request
    );
  }

  /**
   * API unlock
   *
   * @param request IFee
   * @returns Observable<EntityResponseType<IFee>>
   */
  unlock(request: any): Observable<EntityResponseType<IFee>> {
    return super.post<EntityResponseType<IFee>>(
      `${this.resourceUrl}/active`,
      request
    );
  }
}
