import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import {
  CURRENCY_CONST,
  MODAL_ACTION,
  PREMIUM_ACC_TYPE,
  PREMIUM_ACC_TYPE_CONST,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { PREMIUM_ACCOUNT_NUMBER_OBJECT } from '@shared/constants/customer.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { ICustomer } from '@shared/models/customer.model';
import { IMoneyAccount } from '@shared/models/money-account.model';
import {
  IConfirmOtp,
  IPremiumAccNumber,
} from '@shared/models/premium-acc-number.modal';
import { CustomerService } from '@shared/services/customer.service';
import { PremiumAccNumberService } from '@shared/services/premium-acc-number.service';
import { ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import { SendOtpComponent } from './modal-send-otp/modal-send-otp.component';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
@Component({
  selector: 'create-premium-acc-number',
  templateUrl: './create-premium-acc-number.component.html',
  styleUrls: ['./create-premium-acc-number.component.scss'],
})
export class CreatePremiumAccNumberComponent implements OnInit {
  formCreate: FormGroup = new FormGroup({});
  customers: ICustomer[] = [];
  paymentAccounts: IMoneyAccount[] = [];
  premiumAccNumberInfo: IPremiumAccNumber = {};
  isBack?: boolean;
  confirmOtp: IConfirmOtp = {};
  VALIDATORS = VALIDATORS;
  SYSTEM_RULES = SYSTEM_RULES;
  PREMIUM_ACC_TYPE = PREMIUM_ACC_TYPE;

  constructor(
    private formBuilder: FormBuilder,
    private modal: NgbModal,
    private premiumAccNumberService: PremiumAccNumberService,
    private customerService: CustomerService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private toastService: ToastrCustomService,
  ) {
    const premiumAccNumber = this.activatedRoute.snapshot.queryParamMap.get(
      PREMIUM_ACCOUNT_NUMBER_OBJECT.ACCOUNT_NUMBER
    );
    const listPrice = this.activatedRoute.snapshot.queryParamMap.get(
      PREMIUM_ACCOUNT_NUMBER_OBJECT.PRICE
    );

    const discount = this.activatedRoute.snapshot.queryParamMap.get(
      PREMIUM_ACCOUNT_NUMBER_OBJECT.DISCOUNT
    );

    const totalPrice = this.activatedRoute.snapshot.queryParamMap.get(
      PREMIUM_ACCOUNT_NUMBER_OBJECT.TOTAL_PRICE
    );

    if (premiumAccNumber || listPrice || totalPrice) {
      this.isBack = true;
      this.premiumAccNumberInfo.premiumAccNumber = premiumAccNumber || '';
      this.premiumAccNumberInfo.price = Number(listPrice);
      this.premiumAccNumberInfo.discount = Number(discount);
      this.premiumAccNumberInfo.totalPrice = Number(totalPrice);
    }
  }

  ngOnInit(): void {
    this.initForm();
    this.loadListCustomer();
    this.formCreate.get('premiumAccNumber')?.valueChanges.subscribe(value => {
      this.validateFirstDigit(value);
    });
  }

  validateFirstDigit(value: string) {
    if (value && value.startsWith('0')
      && this.formCreate.controls.type.value === PREMIUM_ACC_TYPE_CONST.INTEREST.code ) {
      this.formCreate.patchValue({ premiumAccNumber: value.replace(/^0+/, '') }, { emitEvent: false });
    } 
  }

  initForm(): void {
    this.formCreate = this.formBuilder.group({
      phoneNumber: [
        {
          value: null,
          disabled: false,
        },
        [Validators.required],
      ],
      cif: [
        {
          value: '',
          disabled: true,
        },
      ],
      fullname: [
        {
          value: '',
          disabled: true,
        },
      ],
      premiumAccNumber: [
        {
          value: this.premiumAccNumberInfo?.premiumAccNumber || '',
          disabled: this.premiumAccNumberInfo.premiumAccNumber ? false : true,
        },
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.PHONE_MB),
          Validators.maxLength(VALIDATORS.LENGTH.PREMIUM_ACC_NUMBER_MAX_LENGTH),
        ],
      ],
      paymentAccNumber: [
        {
          value: null,
          disabled: true,
        },
      ],
      listPrice: [
        {
          value:
            CommonUtils.moneyFormatNumber(
              Number(this.premiumAccNumberInfo?.price)
            )?.toString() || '0',
          disabled: true,
        },
      ],
      totalPrice: [
        {
          value:
            CommonUtils.moneyFormatNumber(
              Number(this.premiumAccNumberInfo?.totalPrice)
            )?.toString() || '0',
          disabled: true,
        },
      ],
      discount: [
        {
          value: this.premiumAccNumberInfo?.discount || '0',
          disabled: true,
        },
      ],
      length: [
        {
          value: this.premiumAccNumberInfo?.premiumAccNumber
            ? this.premiumAccNumberInfo.premiumAccNumber.length
            : '',
          disabled: true,
        },
      ],
      type: [
        {
          value: null,
          disabled: false,
        },
        [Validators.required],
      ],
      balance: {
        value: '',
        disabled: true,
      },
    });

    this.valiadatePremiumAccNumberType();
  }

  valiadatePremiumAccNumberType() {
    if (this.premiumAccNumberInfo.premiumAccNumber) {
      if (
        this.premiumAccNumberInfo.premiumAccNumber?.match(
          VALIDATORS.PATTERN.PHONE_MB
        )
      ) {
        this.formCreate.controls.type.setValue(
          PREMIUM_ACC_TYPE_CONST.PHONE_NUMBER.code
        );
      } else {
        this.formCreate.controls.type.setValue(
          PREMIUM_ACC_TYPE_CONST.INTEREST.code
        );
        this.formCreate.controls.premiumAccNumber.setValidators([
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.EXPECT_STRING),
        ]);
        this.formCreate.updateValueAndValidity();
      }
      this.formCreate.controls.premiumAccNumber.enable();
    }
  }

  onResearch() {
    if (this.formCreate.invalid) {
      CommonUtils.markFormGroupTouched(this.formCreate);
    } else {
      const params = {
        expectString: this.formCreate.controls.premiumAccNumber.value,
        type: this.formCreate.controls.type.value,
      };

      this.premiumAccNumberService
        .checkAvaiable(params)
        .subscribe((res: any) => {
          this.formCreate.controls.listPrice.setValue(
            CommonUtils.moneyFormatNumber(
              Number(res.body.originalPrice)
            )?.toString()
          );

          this.formCreate.controls.totalPrice.setValue(
            CommonUtils.moneyFormatNumber(
              Number(res.body.totalPrice)
            )?.toString()
          );
          this.formCreate.controls.discount.setValue(res.body.discount || '0');
          this.formCreate.controls.length.setValue(params.expectString?.length);
          this.formCreate.controls.phoneNumber.enable();
        });
    }
  }

  onChangePaymentAccount(item: IMoneyAccount) {
    if (item.availableAmount) {
      this.formCreate.controls.balance.setValue(
        CommonUtils.moneyFormatNumber(+item.availableAmount)?.toString()
      );
    }
  }

  onChangePremiumAccType(item: string) {
    this.formCreate.controls.length.reset();
    this.formCreate.controls.listPrice.setValue('0');
    this.formCreate.controls.discount.setValue('0');
    this.formCreate.controls.totalPrice.setValue('0');

    if (item === PREMIUM_ACC_TYPE_CONST.PHONE_NUMBER.code) {
      this.formCreate.controls.premiumAccNumber.disable();
      this.formCreate.controls.premiumAccNumber.setValidators([
        Validators.required,
        Validators.pattern(VALIDATORS.PATTERN.PHONE_MB),
      ]);

      const customer = this.formCreate.controls.phoneNumber?.value || null;

      if (customer !== null) {
        this.formCreate.controls.premiumAccNumber.setValue(customer.username);
        this.onResearch();
      }
    } else {
      this.formCreate.controls.premiumAccNumber.reset();
      this.formCreate.controls.premiumAccNumber.enable();
      this.formCreate.controls.premiumAccNumber.setValidators([
        Validators.required,
        Validators.pattern(VALIDATORS.PATTERN.EXPECT_STRING),
      ]);
    }

    this.formCreate.controls.premiumAccNumber.updateValueAndValidity();
  }

  loadListCustomer(event?: any) {
    let keywordSearch = '';
    if (event?.target?.value) {
      keywordSearch = event?.target?.value;
    }
    this.customerService
      .searchAutoComplete({ keyword: keywordSearch })
      .subscribe((res: any) => {
        this.customers = res.body.content;
      });
  }

  onChangePhoneNumber(item: ICustomer) {
    this.formCreate.controls.paymentAccNumber.reset();
    this.formCreate.controls.balance.reset();
    this.formCreate.controls.length.reset();
    this.formCreate.controls.listPrice.setValue('0');
    this.formCreate.controls.discount.setValue('0');
    this.formCreate.controls.totalPrice.setValue('0');

    const cusPurchase = this.customers.find(
      (customer) => customer?.customerId === item?.customerId
    );

    if (cusPurchase) {
      this.formCreate.controls.cif.setValue(cusPurchase.cif);
      this.formCreate.controls.fullname.setValue(cusPurchase.fullname);
      this.formCreate.controls.paymentAccNumber.enable();

      if (cusPurchase.customerId) {
        this.loadPaymentAccountNumber(cusPurchase.customerId);
      }

      if (
        this.formCreate.controls.type.value ===
        PREMIUM_ACC_TYPE_CONST.PHONE_NUMBER.code
      ) {
        this.formCreate.controls.premiumAccNumber.setValue(
          cusPurchase.phoneNumber
        );
        this.onResearch();
      } else {
        if (this.formCreate.controls.premiumAccNumber.value) {
          this.onResearch();
        }
      }
    }
  }

  loadPaymentAccountNumber(customerId: number) {
    const params: ICustomer = {
      customerId,
      currency: CURRENCY_CONST.LAK.value,
    };
    this.customerService
      .getAccountNumberCustomer(params)
      .subscribe((res: any) => {
        this.paymentAccounts = res.body.moneyAccounts;
      });
  }

  backToList(): void {
    this.router.navigate([
      ROUTER_UTILS.premiumAccountNumber.root,
      ROUTER_UTILS.premiumAccountNumber.interest.root,
    ]);
  }

  onSendOtp() {
    if (this.formCreate.invalid) {
      CommonUtils.markFormGroupTouched(this.formCreate);
    } else {
      const cif = this.formCreate.controls.cif.value;
      const moneyAccount = this.formCreate.controls.paymentAccNumber.value;

      const params = {
        cif,
        premiumAccNumber: this.formCreate.controls.premiumAccNumber.value,
        paymentAccNumber: moneyAccount.accountNumber,
      };

      this.premiumAccNumberService.requestOtp(params).subscribe((res: any) => {
        if (res) {
          this.toastService.success('premiumAccNumber.success.requestOtp');
          this.confirmOtp.transactionId = res.body.transactionId;
          this.confirmOtp.customerId = res.body.customerId;

          const modalRef = this.modal.open(SendOtpComponent, {
            backdrop: 'static',
            centered: true,
            size: '50%',
            keyboard: true,
          });
          modalRef.componentInstance.actionConfirm = MODAL_ACTION.CONFIRM;
          modalRef.componentInstance.data = this.confirmOtp;
          modalRef.componentInstance.onResendOtp = () => this.onReSendOtp(modalRef);

          modalRef.result.then((result) => {
            if (result === MODAL_ACTION.CONFIRM.code) {
              this.onReset();
            }
          });
        }
      });
    }
  }

  onReSendOtp(modalRef: any): Promise<boolean> {
    return new Promise((resolve) => {
      const params = {
        cif: this.formCreate.controls.cif.value,
        premiumAccNumber: this.formCreate.controls.premiumAccNumber.value,
        paymentAccNumber: this.formCreate.controls.paymentAccNumber.value.accountNumber,
      };

      this.premiumAccNumberService.requestOtp(params).subscribe({
        next: (res: any) => {
          if (res) {
            this.confirmOtp = {
              transactionId: res.body.transactionId,
              customerId: res.body.customerId
            };
            modalRef.componentInstance.data = this.confirmOtp;

            this.toastService.success('premiumAccNumber.success.requestOtp');
            resolve(true);
          } else {
            resolve(false);
          }
        },
        error: () => {
          resolve(false);
        }
      });
    });
  }

  onReset() {
    this.formCreate.reset();
    this.formCreate.controls.phoneNumber.setValue(null);
    this.formCreate.controls.listPrice.setValue('0');
    this.formCreate.controls.discount.setValue('0');
    this.formCreate.controls.totalPrice.setValue('0');
    this.formCreate.controls.premiumAccNumber.disable();
    this.formCreate.controls.paymentAccNumber.disable();
  }

  isSendOtp(): boolean {
    if (
      this.formCreate.controls.length.value === null ||
      this.formCreate.controls.paymentAccNumber.value == null ||
      this.formCreate.controls.balance.value === '0'
    ) {
      return true;
    }
    return false;
  }
}
