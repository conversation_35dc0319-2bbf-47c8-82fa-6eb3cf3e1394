import { Component, OnInit } from '@angular/core';
import {
  <PERSON>bstractControl,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import {
  CALCULATE_MILLISECONDS_IN_A_YEAR,
  CLIENT_TYPE,
  CLIENT_TYPE_CONST,
  CLIENT_TYPE_MAP,
  DATE_RANGE_STATISTICAL_LOAN_ONLINE,
  ENTITY_STATUS_CONST,
  ENTITY_STATUS_MAP,
  FILE_EXTENSION,
  MODAL_ACTION,
  MOMENT_CONST,
  PAGINATION,
  SAVING_ACCOUNT_STATUS_MAP,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { LANGUAGES } from '@shared/constants/language.constants';

import { Router } from '@angular/router';
import { AbstractDomainManageComponent } from '@core/components/abstract-domain-manage/abstract-domain-manage.component';
import { TranslateService } from '@ngx-translate/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IBaseResponseModel } from '@shared/models/base/base.model';
import { IDotp } from '@shared/models/dotp.model';
import { IEventSearch } from '@shared/models/request/event.search';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { DotpService } from '@shared/services/dotp.service';
import { DownloadService } from '@shared/services/helpers/download.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';

@Component({
  selector: 'dotp-management',
  templateUrl: './dotp-management.component.html',
  styleUrls: ['./dotp-management.component.scss'],
})
export class DotpManagementComponent
  extends AbstractDomainManageComponent<any>
  implements OnInit
{
  resource: RESOURCE_TYPE = RESOURCE_CONST.DOTP;
  searchForm: FormGroup = new FormGroup({});
  searchResults: IBaseResponseModel<IDotp[]> = {};
  maxDate = new Date();
  data: IDotp[] = [];
  isErrorStartDateGreaterEndDate = false;

  pageSizeOptions = PAGINATION.OPTIONS;
  eventSearch: IEventSearch = {};
  SYSTEM_RULES = SYSTEM_RULES;
  LANGUAGES = LANGUAGES;
  MOMENT_CONST = MOMENT_CONST;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;
  SAVING_ACCOUNT_STATUS_MAP = SAVING_ACCOUNT_STATUS_MAP;
  CLIENT_TYPE_CONST = CLIENT_TYPE_CONST;
  CLIENT_TYPE_MAP = CLIENT_TYPE_MAP;
  CLIENT_TYPE = CLIENT_TYPE;
  VALIDATORS = VALIDATORS;
  DATE_RANGE_STATISTICAL_LOAN_ONLINE = DATE_RANGE_STATISTICAL_LOAN_ONLINE;

  constructor(
    private fb: FormBuilder,
    protected router: Router,
    private dotpService: DotpService,
    private toastService: ToastrCustomService,
    private translateService: TranslateService,
    private downloadService: DownloadService
  ) {
    super(dotpService as any);
    this.searchForm = this.fb.group({
      keyword: '',
      status: null,
      fromDate: [null, [Validators.required]],
      toDate: [null, [Validators.required]],
      pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
      pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
      length: 0,
      hasPageable: true,
    });
  }

  ngOnInit(): void {
    const startDate = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDate = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.eventSearch.fromTime = startDate;
    this.eventSearch.toTime = endDate;
    this.searchForm.controls.fromDate.setValue(
      CommonUtils.reverseDate(startDate)
    );
    this.searchForm.controls.toDate.setValue(CommonUtils.reverseDate(endDate));
    super.ngOnInit();
  }

  onParseValueSearchForm(): void {
    super.onParseValueSearchForm();
  }

  onSearch() {
    if (this.searchForm.invalid) {
      CommonUtils.markFormGroupTouched(this.searchForm);
      return;
    }
    this.dotpService.search(this.searchForm.value).subscribe((res: any) => {
      this.data = res.body.content;
      this.searchForm.controls.length.setValue(res.body.totalElements, {
        emitEvent: false,
      });
    });
  }

  onCancel(dotp: IDotp) {
    const modalData = {
      title: 'dotpManagement.cancel',
      content: 'dotpManagement.cancelDotpContent',
      interpolateParams: { fullname: `<b>${dotp?.fullname || ''}</b>` },
    };

    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        const params = { dotpId: dotp?.dotpId };
        this.dotpService.cancel(params).subscribe(() => {
          this.toastService.success('common.action.deleteSuccess');
          this.onSearch();
        });
      }
    });
  }

  onReset() {
    const startDateReset = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDateReset = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.searchForm.controls.fromDate.setValue(
      CommonUtils.reverseDate(startDateReset)
    );
    this.searchForm.controls.toDate.setValue(
      CommonUtils.reverseDate(endDateReset)
    );
    this.searchForm.controls.fromDate.clearValidators();
    this.searchForm.controls.fromDate.setValidators([Validators.required]);
    this.searchForm.controls.fromDate.updateValueAndValidity();
    this.searchForm.controls.keyword.reset();
    this.searchForm.controls.status.reset();
  }

  exportFile(): void {
    const fileName = this.translateService.instant('template.dotp');
    const obFile = this.dotpService.export(this.searchForm.value);
    this.downloadService.downloadFileWithObservableAndName(
      obFile,
      fileName,
      FILE_EXTENSION.XLSX
    );
    return;
  }

  changeValidDate() {
    if (
      this.searchForm.controls.fromDate.value &&
      this.searchForm.controls.toDate.value
    ) {
      if (this.searchForm.controls['toDate'].value) {
        this.maxDate = this.searchForm.controls['toDate'].value;
      }
      const fromDate = CommonUtils.setStartTime(
        new Date(this.searchForm.controls.fromDate.value).getTime()
      );
      const toDate = CommonUtils.setStartTime(
        new Date(this.searchForm.controls.toDate.value).getTime()
      );
      const dayMax = (toDate - fromDate) / CALCULATE_MILLISECONDS_IN_A_YEAR.DAY;
      if (dayMax > DATE_RANGE_STATISTICAL_LOAN_ONLINE) {
        this.searchForm.controls.fromDate.setValidators([
          Validators.required,
          this.dateValidator(dayMax),
        ]);
        this.searchForm.controls.fromDate.updateValueAndValidity();
      } else {
        this.searchForm.controls.fromDate.clearValidators();
        this.searchForm.controls.fromDate.setValidators([Validators.required]);
        this.searchForm.controls.fromDate.updateValueAndValidity();
      }
    }
    this.changeEndDate();
  }

  dateValidator(value: number): any {
    return (_control: AbstractControl): { [key: string]: any } | null => {
      return value > DATE_RANGE_STATISTICAL_LOAN_ONLINE
        ? { invalidDate: true }
        : null;
    };
  }

  changeEndDate() {
    if (this.searchForm.controls['toDate'].value) {
      this.maxDate = this.searchForm.controls['toDate'].value;
    }
    const dataSearch = this.searchForm.getRawValue();
    if (dataSearch.fromDate && dataSearch.toDate) {
      const startDateSearch = new Date(dataSearch.fromDate);
      const endDateSearch = new Date(dataSearch.toDate);
      this.isErrorStartDateGreaterEndDate =
        startDateSearch.getTime() > endDateSearch.getTime();
    }
  }
}
