import { SearchWithPagination } from '@shared/models/request/base.request.model';
import { NotificationLimitEnum } from './notification-limit.search';

export interface INotificationHistorySearch extends SearchWithPagination {
  fromDate?: string;
  toDate?: string;
  transactionCode?: string;
  hasPageable?: boolean;
  transferTypes?: [];
  timeZoneStr?: string;
  transactionStatus?: [];
  billingTypes?: [];
  statuses?: number[];
  notificationTypes?: NotificationLimitEnum[];
}

export enum NotificationHistoryEnum {
  SMS_BALANCE_CHANGE_FAILED = 'SMS_BALANCE_CHARGE_FAIL',
  PREMIUM_ACCOUNT_NUMBER_REVERT = 'PREMIUM_ACCOUNT_NUMBER_REVERT',
}
