import {
  Component,
  HostListener,
  Input,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  On<PERSON>nit,
  SimpleChang<PERSON>,
} from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { GENDER } from '@shared/constants/app.constants';
import {
  APPROVAL_STATUS,
  APPROVAL_TYPE,
  APPROVAL_TYPE_CONST,
  APPROVAL_TYPE_MAP,
  CUSTOMER_SECTOR_ID_CONST,
  CUSTOMER_SECTOR_ID_MAP,
  MARITAL_STATUS,
  RESIDENT_STATUS,
} from '@shared/constants/customer.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { Customer } from '@shared/models/customer.model';
import { IIdCardType } from '@shared/models/id-card-type.model';
import { IFileEntrySearch } from '@shared/models/request/file-entry.search';
import { CustomerService } from '@shared/services/customer.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { IdCardTypeService } from '@shared/services/id-card-type.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-customer-info',
  templateUrl: './customer-info.component.html',
  styleUrls: ['./customer-info.component.scss'],
})
export class CustomerInfoComponent implements OnInit, OnDestroy, OnChanges {
  @Input()
  customerInfo: Customer = new Customer();

  formDetail: FormGroup = new FormGroup({});
  eKycPicUrls: string[] = [];
  reader: FileReader = new FileReader();
  idCardTypes: IIdCardType[] = [];
  imageUrls: any[] = [];
  signatureImageUrls: any[] = [];
  ekycUrls: any[] = [];
  fileRequired: string[] = ['idCardFront', 'idCardBack'];
  hasFilter = false;
  action = '';

  GENDER_LIST = GENDER;
  ROUTER_UTILS = ROUTER_UTILS;
  APPROVAL_TYPE = APPROVAL_TYPE;
  APPROVAL_TYPE_MAP = APPROVAL_TYPE_MAP;
  APPROVAL_STATUS = APPROVAL_STATUS;
  APPROVAL_TYPE_CONST = APPROVAL_TYPE_CONST;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  VALIDATORS = VALIDATORS;
  RESIDENT_STATUS = RESIDENT_STATUS;
  MARITAL_STATUS = MARITAL_STATUS;
  CUSTOMER_SECTOR_ID_MAP = CUSTOMER_SECTOR_ID_MAP;

  constructor(
    private formBuilder: FormBuilder,
    private idCardTypeService: IdCardTypeService,
    private toastService: ToastrCustomService,
    private translate: TranslateService,
    private customerService: CustomerService,
    private activatedRoute: ActivatedRoute
  ) {
    this.activatedRoute.data.subscribe((res) => {
      this.action = res.action;
    });
  }

  /**
   * init data
   */
  ngOnInit(): void {
    this.getIdCardType();
    this.initFileReader();
  }

  /**
   * ngOnChanges
   *
   * @param changes SimpleChanges
   */
  ngOnChanges(changes: SimpleChanges): void {
    this.initForm(this.customerInfo);
    const searchFile: IFileEntrySearch = {};
    searchFile.classPk = this.customerInfo.customerId;
    if (this.customerInfo.idCardFiles) {
      this.customerInfo.idCardFiles.forEach((item: any) => {
        searchFile.fileEntryId = item.id;
        searchFile.normalizeName = item.normalizeName;

        this.customerService
          .getImage(searchFile)
          .subscribe((responsive: any) => {
            CommonUtils.blobToBase64(responsive.body).then((base64) => {
              this.imageUrls.push({
                src: base64,
                name: null,
                id: item.id,
              });
            });
          });
      });
    }

    if (this.customerInfo.signatureFiles) {
      this.customerService
        .getImageSignature(searchFile)
        .subscribe((responsive: any) => {
          CommonUtils.blobToBase64(responsive.body).then((base64) => {
            this.signatureImageUrls.push({
              src: base64,
              name: null,
            });
          });
        });
    }

    if (this.customerInfo.ekycFiles) {
      this.customerInfo.ekycFiles.forEach((item: any) => {
        searchFile.classPk = this.customerInfo.customerId;
        searchFile.fileEntryId = item.id;
        searchFile.normalizeName = item.normalizeName;

        this.customerService
          .getEkycImage(searchFile)
          .subscribe((responsive: any) => {
            CommonUtils.blobToBase64(responsive.body).then((base64) => {
              this.ekycUrls.push({
                src: base64,
                name: null,
                id: item.id,
              });
            });
          });
      });
    }
  }

  /**
   * remove session when load done monitor
   */
  ngOnDestroy(): void {
    if (!this.hasFilter) {
      sessionStorage.removeItem(STORAGE_APP.CUSTOMER_APPROVAL);
    }
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.hasFilter = true;
  }

  /**
   * init form and set valueidCard
   *
   * @param customer Customer
   */
  initForm(customer?: Customer): void {
    this.formDetail = this.formBuilder.group({
      fullname: [customer?.fullname || ''],
      dateOfBirth: [CommonUtils.reverseDate(customer?.dateOfBirth) || ''],
      email: [customer?.email || ''],
      phoneNumber: [customer?.phoneNumber || ''],
      approvalType: [customer?.approvalType || null],
      approvalStatus: [customer?.approvalStatus || null],
      status: [customer?.status || null],
      approvedBy: [customer?.approvedBy || ''],
      idCardTypeId: [customer?.idCardTypeId || null],
      idCardTypeName: [customer?.idCardTypeId || ''],
      idCardNumber: [customer?.idCardNumber || ''],
      gender: [customer?.gender],
      customerOldSectorId: [customer?.customerOldSectorId],
      customerSectorId: [
        CommonUtils.limitWord(
          this.translate.instant(
            CUSTOMER_SECTOR_ID_MAP[
              customer?.customerSectorId ||
                CUSTOMER_SECTOR_ID_CONST.SECTOR_1891.value
            ]
          ),
          40
        ),
      ],
      customerSectorIdStr: [
        this.translate.instant(
          CUSTOMER_SECTOR_ID_MAP[
            customer?.customerSectorId ||
              CUSTOMER_SECTOR_ID_CONST.SECTOR_1891.value
          ]
        ),
      ],
      address: [customer?.address || ''],
      description: [customer?.description || ''],
      issueDate: [CommonUtils.reverseDate(customer?.issueDate) || ''],
      issuePlace: [customer?.issuePlace || ''],
      createAppAccount: [true],
      cif: [customer?.cif || ''],
      accountType: [null],
      customerType: [null],
      nationCode: [customer?.nationCode || null],
      nationName: [customer?.nationName || ''],
      nationCodeOther: [customer?.nationCodeOther || null],
      nationOtherName: [customer?.nationOtherName || ''],
      placeOfResidence: [customer?.placeOfResidence || ''],
      placeOfResidenceOutCountry: [customer?.placeOfResidenceOutCountry || ''],
      currentAddress: [customer?.currentAddress || ''],
      phoneContact: [customer?.phoneContact || ''],
      maritalStatus: [customer?.maritalStatus],
      job: [customer?.job || ''],
      position: [customer?.position || ''],
      staffCode: [customer?.staffCode || ''],
      // shopCode: [customer?.job || ''],
      username: [customer?.username || ''],
      createdDate: [customer?.createdDate || ''],
      residentStatus: [customer?.residentStatus],
      createdDateAccountT24: [customer?.createdDate || ''],
      placeOfOrigin: [customer?.placeOfOrigin || ''],
      imageUrls: [customer?.imageUrls || []],
      signatureImageUrl: [customer?.signatureImageUrls || []],
      workplace: [customer?.workplace || ''],
      referralCode: [customer?.referralCode || ''],
      userFullNameReferral: [customer?.userFullNameReferral || ''],
      phoneNumberReferral: [customer?.phoneNumberReferral || ''],
      customerSectorIdCode: [customer?.customerSectorId || ''],
      premiumAccountNumber: [
        customer?.premiumAccountNumberCache?.premiumAccNumber || '',
      ],
      paymentPrice: [
        customer?.premiumAccountNumberCache?.price
          ? CommonUtils.moneyFormatNumber(
              customer?.premiumAccountNumberCache?.price
            )
          : '0',
      ],
    });
    // if not update then disable form, if is update then disable some column
    this.formDetail.disable();
  }

  /**
   * get all IdCardType
   */
  getIdCardType(): void {
    this.idCardTypeService.getAll().subscribe((res: any) => {
      this.idCardTypes = res.body;
    });
  }

  /**
   * back
   */
  onBack(): void {
    window.history.back();
  }

  /**
   * gen picture
   */
  initFileReader() {
    this.reader.onload = (ev: ProgressEvent<FileReader>) => {
      const result: any | null = ev.target?.result;

      if (result) {
        this.eKycPicUrls.push(result as string);
      } else {
        this.toastService.error('');
      }
    };
  }
}
