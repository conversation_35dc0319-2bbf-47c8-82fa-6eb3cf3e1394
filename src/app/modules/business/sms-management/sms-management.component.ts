import { Component, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
} from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import {
  FILE_EXTENSION,
  MOMENT_CONST,
  PAGINATION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { ISms } from '@shared/models/sms.model';
import { DownloadService } from '@shared/services/helpers/download.service';
import { SmsManageService } from '@shared/services/sms.service';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-sms-management',
  templateUrl: './sms-management.component.html',
  styleUrls: ['./sms-management.component.scss'],
})
export class SmsManagementComponent implements OnInit {
  formSearch: FormGroup = new FormGroup({});
  maxDate = new Date();
  data: ISms[] = [];
  MOMENT_CONST = MOMENT_CONST;
  PAGINATION = PAGINATION;
  SYSTEM_RULES = SYSTEM_RULES;
  FILE_EXTENSION = FILE_EXTENSION;
  constructor(
    private fb: FormBuilder,
    private translateService: TranslateService,
    private smsManageService: SmsManageService,
    private downloadService: DownloadService
  ) {}

  ngOnInit(): void {
    this.formSearch = this.fb.group({
      fromDate: null,
      toDate: [null],
      sourceZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    });
    this.onSearch();
  }

  onSearchSubmit() {
    this.onSearch();
  }

  onSearch() {
    const params = this.formSearch.value;

    if (this.formSearch.invalid) {
      CommonUtils.markFormGroupTouched(this.formSearch);
      return;
    }
    this.smsManageService.search(params).subscribe((res: any) => {
      this.data = res.body;
    });
  }

  onReset() {
    this.formSearch.controls.toDate.clearValidators();
    this.formSearch.controls.fromDate.clearValidators();
    this.formSearch.reset();
  }

  exportFile() {
    const formSearch = this.formSearch.value;
    const fileName = this.translateService.instant('template.sms');
    const obFile = this.smsManageService.export(formSearch);
    this.downloadService.downloadFileWithObservableAndName(
      obFile,
      fileName,
      FILE_EXTENSION.XLSX
    );
  }

  toDateInput() {
    this.formSearch.controls.toDate.setValidators([this.dateToInvalid]);
    this.formSearch.controls.toDate.updateValueAndValidity();
  }

  fromDateInput() {
    this.formSearch.controls.fromDate.setValidators([this.dateFromInvalid]);
    this.formSearch.controls.fromDate.updateValueAndValidity();
  }

  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(
      index,
      PAGINATION.PAGE_NUMBER_DEFAULT,
      PAGINATION.PAGE_SIZE_DEFAULT
    );
  }

  dateToInvalid = (control: AbstractControl): ValidationErrors | null => {
    const fromDate = this.formSearch.get('fromDate')?.value;
    if (fromDate && control.value) {
      if (new Date(fromDate) > new Date(control.value)) {
        return { invalidToDate: true };
      }
      if (new Date(control.value) > new Date()) {
        return { invalidToDateCurrent: true };
      }
    }
    if (!fromDate && control.value) {
      if (new Date(control.value) > new Date()) {
        return { invalidToDateCurrent: true };
      }
    }
    return null;
  };

  dateFromInvalid = (control: AbstractControl): ValidationErrors | null => {
    const fromDate = this.formSearch.get('fromDate')?.value;
    if (fromDate) {
      if (new Date(fromDate) > new Date()) {
        return { invalidFromDate: true };
      }
    }
    return null;
  };
}
