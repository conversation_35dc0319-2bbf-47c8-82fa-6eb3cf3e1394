import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ENTITY_STATUS } from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { IGroupPrivileges } from '@shared/models/group-privileges.model';
import { RoleService } from '@shared/services/role.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';

@Component({
  selector: 'app-detail-role',
  templateUrl: './detail-role.component.html',
  styleUrls: ['./detail-role.component.scss'],
})
export class DetailRoleComponent implements OnInit {
  roleId = 0;

  groupPrivileges: IGroupPrivileges[] = [];

  ENTITY_STATUS = ENTITY_STATUS;

  roleStatus = 0;

  role: any;

  SYSTEM_RULES = SYSTEM_RULES;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private roleService: RoleService
  ) {}

  ngOnInit(): void {
    this.loadRoleDetail();
  }

  loadRoleDetail() {
    this.activatedRoute.paramMap.subscribe((params) => {
      this.roleId = Number(params.get('roleId'));
      this.roleService.detail({ roleId: this.roleId }).subscribe((res) => {
        this.role = res.body;

        this.roleStatus = this.role.status;

        this.roleService.listPrivilege().subscribe((response: any): void => {
          this.groupPrivileges = response.body;

          for (const groupPrivilege of this.groupPrivileges) {
            if (groupPrivilege?.privileges) {
              const privileges = groupPrivilege?.privileges;
              for (const privilege of privileges) {
                if (this.role?.privilegeIds.includes(privilege.privilegeId)) {
                  privilege.checked = true;
                }
              }
            }
          }
        });
      });
    });
  }

  editRole() {
    this.router.navigate([
      ROUTER_UTILS.sysManage.role.root,
      this.roleId,
      ROUTER_ACTIONS.update,
    ]);
  }

  backToList(): void {
    this.router.navigateByUrl(ROUTER_UTILS.sysManage.role.root);
  }
}
