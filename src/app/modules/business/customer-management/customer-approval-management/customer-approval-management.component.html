<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="form" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-lg-3 col-md-4">
            <div class="form-group">
              <label>{{ "model.customer.cif" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="cif"
                class="w-100"
                class="form-control"
                placeholder="{{ 'model.customer.cif' | placeholder }}"
              />
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  form.get('cif')?.errors?.pattern && form.get('cif')?.touched
                "
              >
                {{ "error.cif" | translate }}
              </small>
            </div>
          </div>
          <div class="col-lg-3 col-md-4">
            <div class="form-group">
              <label>{{ "model.customer.phoneNumber" | translate }}</label>
              <input
                numbersOnly
                type="text"
                [maxLength]="VALIDATORS.LENGTH.SMALL_TEXT_MAX_LENGTH"
                formControlName="phoneNumber"
                class="w-100"
                class="form-control"
                placeholder="{{ 'model.customer.phoneNumber' | placeholder }}"
              />
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  form.get('phoneNumber')?.errors?.pattern &&
                  form.get('phoneNumber')?.touched
                "
              >
                {{ "error.phoneNumber" | translate }}
              </small>
            </div>
          </div>
          <div class="col-lg-3 col-md-4">
            <div class="form-group">
              <label>{{ "model.customer.idCardNumber" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="idCardNumber"
                class="w-100"
                class="form-control"
                placeholder="{{ 'model.customer.idCardNumber' | placeholder }}"
              />
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  form.get('idCardNumber')?.errors?.pattern &&
                  form.get('idCardNumber')?.touched
                "
              >
                {{ "error.idCardNumber" | translate }}
              </small>
            </div>
          </div>
          <div class="col-lg-3 col-md-4">
            <div class="form-group">
              <label>{{ "model.customer.email" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="email"
                class="w-100"
                class="form-control"
                placeholder="{{ 'model.customer.email' | placeholder }}"
              />
            </div>
          </div>
          <div class="col-lg-3 col-md-4">
            <div class="form-group">
              <label>{{
                "customerRegisterManagement.customerApproval.approvalType"
                  | translate
              }}</label>
              <ng-select
                placeholder="{{
                  'common.appSelectOption.approvalType' | translate
                }}"
                [searchable]="false"
                formControlName="approvalType"
                [clearable]="true"
              >
                <ng-option
                  [value]="item.value"
                  *ngFor="let item of APPROVAL_TYPE"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-lg-3 col-md-4">
            <div class="form-group">
              <label>{{
                "customerRegisterManagement.customerApproval.approvalStatus"
                  | translate
              }}</label>
              <ng-select
                placeholder="{{
                  'common.appSelectOption.approvalStatus' | translate
                }}"
                [searchable]="false"
                formControlName="approvalStatus"
                [clearable]="true"
              >
                <ng-option
                  [value]="item.value"
                  *ngFor="let item of APPROVAL_STATUS_VALUE"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-lg-3 col-md-8">
            <div class="date-picker-container form-group row">
              <div class="col-md-6">
                <label>{{ "common.action.fromDate" | translate }}</label>
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="createdDateStartAt"
                    formControlName="createdDateStartAt"
                    placeholder="DD/MM/YYYY"
                    [max]="currentDate"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="createdDateStartAt"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #createdDateStartAt></mat-datepicker>
                </mat-form-field>
              </div>
              <div class="col-md-6">
                <label>{{ "common.action.toDate" | translate }}</label>
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="createdDateEndAt"
                    formControlName="createdDateEndAt"
                    placeholder="DD/MM/YYYY"
                    [max]="currentDate"
                    min="{{
                      form.controls['createdDateStartAt'].value
                        | date : 'yyyy-MM-dd'
                    }}"
                    (change)="validateDate()"
                    (dateInput)="validateDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="createdDateEndAt"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #createdDateEndAt></mat-datepicker>
                </mat-form-field>
              </div>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
            >
              <div class="btn-reset">
                <i
                  class="bi bi-arrow-clockwise"
                  type="button"
                  (click)="onReset()"
                >
                </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" type="submit">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-2 mt-4">
          <button
            class="btn btn-red mb-2"
            type="button"
            (click)="importFile()"
            *hasPrivileges="SYSTEM_RULES.CUSTOMER_IMPORT"
          >
            {{ "common.action.uploadFiles" | translate }}
          </button>
        </div>
      </form>
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th class="text-center" scope="col">
                {{ "common.no" | translate }}
              </th>
              <th scope="col">{{ "model.customer.cif" | translate }}</th>
              <th scope="col">{{ "model.customer.fullname" | translate }}</th>
              <th scope="col">
                {{ "model.customer.idCardNumber" | translate }}
              </th>
              <th scope="col">
                {{ "model.customer.dateOfBirth" | translate }}
              </th>
              <th scope="col">
                {{ "model.customer.phoneNumber" | translate }}
              </th>
              <th scope="col">{{ "model.customer.email" | translate }}</th>
              <th class="text-center" scope="col">
                {{
                  "customerRegisterManagement.customerApproval.approvalStatus"
                    | translate
                }}
              </th>
              <th class="text-center" scope="col">
                {{
                  "customerRegisterManagement.customerApproval.approvalType"
                    | translate
                }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of data; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td>{{ dataItem.cif }}</td>
              <td>
                <span title="{{ dataItem.fullname }}">{{
                  dataItem.fullname | limitWord
                }}</span>
              </td>
              <td>{{ dataItem.idCardNumber }}</td>
              <td>{{ dataItem.dateOfBirth }}</td>
              <td>{{ dataItem.phoneNumber }}</td>
              <td>
                <span title="{{ dataItem.email }}">{{
                  dataItem.email | limitWord
                }}</span>
              </td>
              <td class="text-center">
                {{ APPROVAL_STATUS_MAP[dataItem.approvalStatus] | translate }}
              </td>
              <td class="text-center">
                {{ APPROVAL_TYPE_MAP[dataItem.approvalType] | translate }}
              </td>
              <td class="text-center">
                <button
                  ngbTooltip="{{ 'common.action.detail' | translate }}"
                  class="btn px-1 py-0"
                  (click)="detail(dataItem)"
                >
                  <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                </button>
                <!--*ngIf="dataItem.approvalStatus === 1 && (dataItem.approvalType === 1 || dataItem.approvalType === 2)"-->
                <!-- <button
                class="btn px-1 py-0"
                (click)="edit(dataItem.customerId)"
                data-toggle="modal"
                data-target="#popupModal"
              >
                <i class="fa fa-edit color-primary" aria-hidden="true"></i>
              </button> -->
                <ng-container
                  *ngIf="checkEnableDeleteBtn(dataItem.approvalStatus)"
                >
                  <button
                    ngbTooltip="{{ 'common.action.delete' | translate }}"
                    *hasPrivileges="SYSTEM_RULES.CUSTOMER_DELETE"
                    (click)="deleteCustomerApproval(dataItem)"
                    class="btn px-1 py-0"
                  >
                    <i class="fa fa-trash mb-color" aria-hidden="true"></i>
                  </button>
                </ng-container>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="row d-block text-center m-0" *ngIf="data?.length === 0">
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
        <div *ngIf="data.length">
          <mat-paginator
            [length]="form.value.length"
            [pageSize]="form.value.pageSize"
            [pageIndex]="form.value.pageIndex"
            [pageSizeOptions]="pageSizeOptions"
            (page)="onChangePage($event)"
            aria-label="Select page"
          >
          </mat-paginator>
        </div>
      </div>
    </div>
  </div>
</section>
