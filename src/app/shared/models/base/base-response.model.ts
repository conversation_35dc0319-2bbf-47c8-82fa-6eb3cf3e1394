export interface IBaseResponse<T> {
  msg_code?: string;
  msg_content?: string;
  payload: any;
  data: any;
}

// export class BaseResponse implements IBaseResponse {
//   constructor(
//     public success?: boolean,
//     public code?: number | string,
//     public data?: [] | {},
//     public message?: string,
//     public page?: IPageable,
//     public timestamp?: string | number | any,
//   ) {
//     this.success = success;
//     this.code = code;
//     this.data = data;
//     this.message = message;
//     this.page = page;
//     this.timestamp = timestamp;
//   }
// }
