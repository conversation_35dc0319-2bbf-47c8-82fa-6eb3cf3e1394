/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  <PERSON>tt<PERSON><PERSON><PERSON>,
  <PERSON>tt<PERSON><PERSON><PERSON><PERSON>,
  HttpInterceptor,
  HttpRequest,
  HttpResponse,
  HttpStatusCode,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { BOOLEAN_STRING, MOMENT_CONST } from '@shared/constants/app.constants';
import { HTTP_HEADERS } from '@shared/constants/http.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { AuthProviderService } from '@shared/services/auth/auth-provider.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';
import * as momentTZ from 'moment-timezone';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { catchError, filter, map, switchMap, take } from 'rxjs/operators';

@Injectable()
export class HandlerInterceptor implements HttpInterceptor {
  private isTokenRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<string> =
    new BehaviorSubject<string>('');

  constructor(
    private authProviderService: AuthProviderService,
    private toastService: ToastrService,
    private toastCustomService: ToastrCustomService,
    private activeModal: NgbModal
  ) {}

  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(
      map((event: HttpEvent<any>) => {
        if (request && request.url && event instanceof HttpResponse) {
          event = event.clone({ body: this.resolveBody(event.body) });
        }
        return event;
      }),
      catchError((err) => {
        if (err.status === HttpStatusCode.Unauthorized) {
          this.authProviderService.logout();
          this.activeModal.dismissAll();
        } else if (err.status === HttpStatusCode.Forbidden) {
          return this.handle403Error(request, next);
        } else if (
          err?.error &&
          request.headers.get(HTTP_HEADERS.X_IGNORE_ERROR) !==
            BOOLEAN_STRING.TRUE
        ) {
          if (err?.error instanceof Blob) {
            this.getErrorFile(err);
          } else if (err.error?.fieldErrors) {
            err.error.fieldErrors?.forEach(
              (error: { message: string | undefined }) =>
                this.toastService.error(error.message)
            );
          } else {
            if (err.error.message) {
              this.toastService.error(err.error.message);
            } else {
              this.toastCustomService.handlerError(err.error.message);
            }
          }
        }

        return throwError(err);
      })
    );
  }

  private resolveBody(body: any): any {
    if (body?.content) {
      this.resolveBody(body?.content);
    } else if (body instanceof Array) {
      body.forEach((element) => {
        this.resolveBody(element);
      });
    } else if (body instanceof Object) {
      Object.keys(body).forEach((key) => {
        if (body[key] instanceof Object) {
          this.resolveBody(body[key]);
        }

        const value = body[key] + '';
        if (
          value.match(VALIDATORS.PATTERN.MATCH_DATE_TIME_REVERSE) ||
          value.match(VALIDATORS.PATTERN.MATCH_DATE_TIME)
        ) {
          body[key] = momentTZ
            .tz(
              CommonUtils.reverseDateTimeZone(body[key]),
              Intl.DateTimeFormat().resolvedOptions().timeZone
            )
            .format(MOMENT_CONST.TIMEZONE_FORMAT);
        }

        if (value.match(VALIDATORS.PATTERN.MATCH_DATE_TIME_ZONE)) {
          body[key] = moment(body[key]).format(
            MOMENT_CONST.LOCAL_DATE_TIME_FORMAT
          );
        }
      });
    }

    return body;
  }

  private handle403Error(request: HttpRequest<any>, next: HttpHandler) {
    if (!this.isTokenRefreshing) {
      this.isTokenRefreshing = true;
      this.refreshTokenSubject.next('');

      return this.authProviderService.refreshToken().pipe(
        switchMap((res: any) => {
          this.isTokenRefreshing = false;
          this.refreshTokenSubject.next(res.token);
          return next.handle(this.addTokenToHeader(request, res.token));
        }),
        catchError((err) => {
          return throwError(err);
        })
      );
    } else {
      return this.refreshTokenSubject.pipe(
        filter((token) => token !== ''),
        take(1),
        switchMap((jwt) => {
          return next.handle(this.addTokenToHeader(request, jwt));
        })
      );
    }
  }

  private addTokenToHeader(request: HttpRequest<any>, token: string) {
    return (request = request.clone({
      headers: request.headers.set(
        HTTP_HEADERS.AUTHORIZATION,
        `${HTTP_HEADERS.AUTHORIZATION_TYPE}${token}`
      ),
      withCredentials: true,
    }));
  }

  private getErrorFile(err: any): any {
    const blob = new Blob([err.error]);
    return blob.text().then((data: any) => {
      const _error = JSON.parse(data);
      if (_error?.fieldErrors) {
        _error?.fieldErrors?.forEach((error: { message: string | undefined }) =>
          this.toastService.error(error.message)
        );
      } else {
        this.toastService.error(_error.message);
      }
    });
  }
}
