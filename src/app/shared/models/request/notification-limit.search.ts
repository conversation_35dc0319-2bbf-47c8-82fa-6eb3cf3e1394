import { SearchWithPagination } from '@shared/models/request/base.request.model';

export interface INotificationLimitSearch extends SearchWithPagination {
  fromDate?: string;
  toDate?: string;
  transactionCode?: string;
  hasPageable?: boolean;
  transferTypes?: [];
  timeZoneStr?: string;
  transactionStatus?: [];
  billingTypes?: [];
  statuses?: number[];
  notificationTypes?: NotificationLimitEnum[];
}

export enum NotificationLimitEnum {
  NOTIFICATION_LIMIT = 'NOTIFICATION_LIMIT',
  PREMIUM_ACCOUNT_NUMBER_REVERT = 'PREMIUM_ACCOUNT_NUMBER_REVERT',
  SMS_BALANCE_CHANGE_FAILED = 'SMS_BALANCE_CHARGE_FAIL',
}
