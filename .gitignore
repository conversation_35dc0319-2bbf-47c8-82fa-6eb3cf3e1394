# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
/dist
/tmp
/out-tsc
# Only exists if <PERSON><PERSON> was run
/bazel-out

# dependencies
/node_modules

# profiling files
chrome-profiler-events*.json

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.scannerwork

# IDE - VSCode
.vscode/*
.history/*
.angular/*

# husky
.husky/*

# misc
/.angular/cache
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db
/package-lock.json
/src/environments/environment.ts
/src/environments/environment.qa.ts
/src/environments/environment.prod.ts

# Docker
deploy.key
.nginx.conf
docker-compose.yml
Dockerfile
Jenkinsfile
