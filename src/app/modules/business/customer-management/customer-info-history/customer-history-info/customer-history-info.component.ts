import {
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges
} from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ENTITY_STATUS, PAGINATION } from '@shared/constants/app.constants';
import { APPROVAL_TYPE_MAP } from '@shared/constants/customer.constants';
import { ICustomerActivationHistory } from '@shared/models/customer-activation-history.mode';
import { ICustomerApprovalHistory } from '@shared/models/customer-approval-history.mode';
import { Customer, ICustomer } from '@shared/models/customer.model';
import { IMoneyAccount } from '@shared/models/money-account.model';
import { CustomerService } from '@shared/services/customer.service';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-customer-history-info',
  templateUrl: './customer-history-info.component.html',
  styleUrls: ['./customer-history-info.component.scss'],
})
export class CustomerHistoryInfoComponent implements OnInit, OnChanges {
  @Input()
  customerInfo: ICustomer = new Customer();

  formInfo: FormGroup = new FormGroup({});
  moneyAccounts: IMoneyAccount[] = [];
  updateHistory: ICustomerApprovalHistory[] = [];
  activationHistory: ICustomerActivationHistory = {};

  ENTITY_STATUS = ENTITY_STATUS;
  APPROVAL_TYPE_MAP = APPROVAL_TYPE_MAP;
  pageSize = PAGINATION.PAGE_SIZE_DEFAULT;
  pageIndex = PAGINATION.PAGE_NUMBER_DEFAULT;

  constructor(
    private formBuilder: FormBuilder,
    private customerService: CustomerService
  ) {}

  ngOnInit(): void {
    this.getCustomerAccountBalance();
    this.getActivationHistory();
    this.getUpdateHistory();
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.initForm(this.customerInfo);
  }

  /**
   * init form and set value
   *
   * @param customer Customer
   */
  initForm(customer?: Customer): void {
    this.formInfo = this.formBuilder.group({
      fullname: [customer?.fullname || ''],
      email: [customer?.email || ''],
      phoneNumber: [customer?.phoneNumber || ''],
      approvedBy: [customer?.approvedBy || ''],
      createdDateAccount: [customer?.createdDate || ''],
      phoneContact: [customer?.phoneContact || ''],
      username: [customer?.username || customer?.phoneNumber || ''],
      status: [customer?.status || null],
    });
    // if not update then disable form, if is update then disable some column
    this.formInfo.disable();
  }

  /**
   * get detail customer
   */
  getCustomerAccountBalance(): void {
    if (this.customerInfo.customerId) {
      const params = { customerId: this.customerInfo.customerId };
      this.customerService
        .getCustomerAccountBalance(params)
        .subscribe((res: any) => {
          this.customerInfo = res.body;
          const data = res.body;
          if (data?.length > 0) {
            data[0].isDefault = true;
          }
          this.moneyAccounts = data;
        });
    }
  }

  /**
   * get Activation History
   */
  getActivationHistory(): void {
    if (this.customerInfo.customerId) {
      const params = { customerId: this.customerInfo.referenceId ?? this.customerInfo.customerId };
      this.customerService
        .getActivationHistory(params)
        .subscribe((res: any) => {
          this.activationHistory = res.body;
        });
    }
  }

  /**
   * get Update History
   */
  getUpdateHistory(): void {
    if (this.customerInfo.customerId) {
      const params = { customerId: this.customerInfo.customerId };
      this.customerService.getUpdateHistory(params).subscribe((res: any) => {
        this.updateHistory = res.body;
      });
    }
  }

  /**
   * index on list
   *
   * @param index number
   * @returns number
   */
  fillIndexItem(index: number): number {
    return CommonUtils.fillIndexItem(index, this.pageIndex, this.pageSize);
  }
}
