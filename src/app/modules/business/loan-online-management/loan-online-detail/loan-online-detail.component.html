<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="backToList()" />
      <h5>
        {{ "loanOnline.title" | translate }}
      </h5>
    </div>
    <!-- <form [formGroup]="form" *ngIf="customer?.customerId"></form> -->
    <form [formGroup]="formLoanOnlineDetail" *ngIf="loanOnline?.loanOnlineId">
      <div class="row">
        <div class="col-md-12">
          <div class="row">
            <h2 class="title-create">{{ "loanOnline.title" | translate }}</h2>
          </div>
          <div class="row border-create">
            <h3>{{ "loanOnline.titleInforBrrower" | translate }}</h3>
            <div class="col-md-12">
              <div class="row">
                <div class="col-md-3">
                  <div class="form-group">
                    <label
                      >{{ "model.loanOnline.fullName" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      trim
                      formControlName="fullName"
                      type="text"
                      class="w-100"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label
                      >{{ "model.loanOnline.phoneNumber" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      trim
                      formControlName="phoneNumber"
                      type="text"
                      class="w-100"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label
                      >{{ "model.loanOnline.dateOfBirth" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      trim
                      formControlName="dateOfBirth"
                      type="text"
                      class="w-100"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label>{{ "model.loanOnline.gender" | translate }}</label>
                    <select
                      formControlName="gender"
                      class="w-100"
                      class="form-control"
                    >
                      <option
                        *ngFor="let item of GENDER_LIST"
                        [value]="item.code"
                      >
                        {{ item.label | translate }}
                      </option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-12">
              <div class="row">
                <div class="col-md-3">
                  <div class="form-group">
                    <label>{{ "model.loanOnline.email" | translate }}</label>
                    <input
                      trim
                      formControlName="email"
                      type="text"
                      class="w-100"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label>{{
                      "model.loanOnline.maritalStatus" | translate
                    }}</label>
                    <ng-select
                      appearance="outline"
                      [searchable]="false"
                      [clearable]="false"
                      placeholder="{{
                        'common.placeholder.maritalStatus' | translate
                      }}"
                      formControlName="maritalStatus"
                    >
                      <ng-option
                        [value]="item.value"
                        *ngFor="let item of MARITAL_STATUS"
                      >
                        {{ item.label | translate }}
                      </ng-option>
                    </ng-select>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group">
                    <label
                      >{{ "model.loanOnline.address" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      trim
                      formControlName="address"
                      type="text"
                      class="w-100"
                      class="form-control"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-12">
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label>{{ "model.loanOnline.job" | translate }}</label>
                    <input
                      trim
                      formControlName="job"
                      type="text"
                      class="w-100"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label>{{ "model.loanOnline.position" | translate }}</label>
                    <input
                      trim
                      formControlName="position"
                      type="text"
                      class="w-100"
                      class="form-control"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-12">
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label>{{
                      "model.loanOnline.workplace" | translate
                    }}</label>
                    <input
                      trim
                      formControlName="workplace"
                      type="text"
                      class="w-100"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label
                      >{{ "model.loanOnline.income" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <div id="text-unit">
                      <input
                        trim
                        formControlName="income"
                        type="text"
                        class="w-100"
                        class="form-control"
                      />
                      <div id="text-unit-right">
                        <span>{{
                          "loanOnline.unit.lak-month" | translate
                        }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="row border-create">
            <h3>{{ "loanOnline.titleInforLoanAmount" | translate }}</h3>
            <div class="col-md-12">
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label
                      >{{ "loanOnline.loanProduct" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      trim
                      formControlName="loanProductName"
                      type="text"
                      class="w-100"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label
                      >{{ "model.loanOnline.loanAmount" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <div id="text-unit">
                      <input
                        trim
                        formControlName="loanAmount"
                        type="text"
                        class="w-100"
                        class="form-control"
                      />
                      <div id="text-unit-right">
                        <span>{{ "loanOnline.unit.lak" | translate }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label
                      >{{ "model.loanOnline.loanTime" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <div id="text-unit">
                      <input
                        trim
                        formControlName="loanTime"
                        type="text"
                        class="w-100"
                        class="form-control"
                      />
                      <div id="text-unit-right">
                        <span>{{ "loanOnline.unit.month" | translate }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-12">
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label>{{
                      "model.loanOnline.collateral" | translate
                    }}</label>
                    <input
                      trim
                      formControlName="collateral"
                      type="text"
                      class="w-100"
                      class="form-control"
                    />
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label>{{ "model.loanOnline.status" | translate }}</label>
                    <select
                      formControlName="status"
                      class="w-100"
                      class="form-control"
                      appSelectOption
                    >
                      <option
                        *ngFor="let item of ENTITY_STATUS"
                        [value]="item.code"
                      >
                        {{ item.label | translate }}
                      </option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="d-block text-center mb-5 mt-4">
            <button
              class="btn btn-red"
              data-toggle="modal"
              (click)="backToList()"
            >
              {{ "common.action.back" | translate }}
            </button>
          </div>
        </div>
      </div>
    </form>
  </div>
</section>
