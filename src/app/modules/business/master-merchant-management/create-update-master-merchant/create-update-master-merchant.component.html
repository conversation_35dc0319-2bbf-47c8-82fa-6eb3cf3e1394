<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="backToList()" />
      <h5>
        {{
          isUpdate
            ? ("sidebar.merchant.masterMerchant.update" | translate)
            : ("sidebar.merchant.masterMerchant.create" | translate)
        }}
      </h5>
    </div>
    <!-- <form [formGroup]="form" *ngIf="customer?.customerId"></form> -->
    <form
      [formGroup]="formMerchant"
      *ngIf="isUpdate ? merchant?.merchantId : !isUpdate"
    >
      <div class="col-md-12">
        <div class="row">
          <h2 class="title-create">
            {{
              (isUpdate
                ? "sidebar.merchant.masterMerchant.update"
                : "sidebar.merchant.masterMerchant.create"
              ) | translate
            }}
          </h2>
        </div>
        <div class="row border-create">
          <h3>{{ "sidebar.merchant.inforMasterMerchant" | translate }}</h3>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label
                    >{{ "model.merchant.masterMerchant.serviceType" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <ng-select
                    (change)="getMerchantCode($event)"
                    appearance="outline"
                    [clearable]="false"
                    formControlName="serviceType"
                    placeholder="{{
                      'model.merchant.masterMerchant.serviceType'
                        | placeholder : 'select'
                    }}"
                  >
                    <ng-option
                      *ngFor="let item of MASTER_MERCHANT_SERVICE"
                      [value]="item.value"
                    >
                      {{ item.label | translate }}
                    </ng-option>
                  </ng-select>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('serviceType')?.errors?.required &&
                      formMerchant.get('serviceType')?.touched
                    "
                  >
                    {{ "error.merchant.required.serviceType" | translate }}
                  </small>
                </div>
              </div>
              <div
                class="col-md-6"
                *ngIf="
                  formMerchant.get('serviceType')?.value !==
                    MASTER_MERCHANT_SERVICE_ENUM.OTHER || isUpdate
                "
              >
                <label
                  >{{ "model.merchant.masterMerchant.merchantCode" | translate
                  }}<span class="text-danger">*</span></label
                >
                <div class="form-group">
                  <ng-container *ngIf="!isOther"
                    ><ng-select
                      (change)="getMerchantCode($event)"
                      appearance="outline"
                      [clearable]="false"
                      [searchable]="false"
                      formControlName="merchantCode"
                      placeholder="{{
                        'model.merchant.masterMerchant.merchantCode'
                          | placeholder : 'select'
                      }}"
                    >
                      <ng-option
                        *ngFor="let item of merchantCode"
                        [value]="item.code"
                      >
                        {{ item.code }}
                      </ng-option>
                    </ng-select>
                    <small
                      *ngIf="
                        formMerchant.get('merchantCode')?.errors?.required &&
                        formMerchant.get('merchantCode')?.touched
                      "
                      class="text-danger"
                      >{{
                        "error.merchant.required.merchantCode" | translate
                      }}</small
                    ></ng-container
                  >

                  <ng-container *ngIf="isOther">
                    <input
                      readonly="true"
                      formControlName="merchantCode"
                      class="w-100 form-control"
                      type="text"
                      trim
                      placeholder="{{
                        'model.merchant.masterMerchant.merchantCode'
                          | placeholder
                      }}"
                      [maxLength]="
                        VALIDATORS.LENGTH.MASTER_MERCHANT_CODE_MAX_LENGTH
                      "
                    />
                    <small
                      *ngIf="
                        formMerchant.get('merchantCode')?.errors?.required &&
                        formMerchant.get('merchantCode')?.touched
                      "
                      class="text-danger"
                      >{{
                        "error.merchant.required.merchantCode" | translate
                      }}</small
                    >
                    <small
                      *ngIf="
                        formMerchant.get('merchantCode')?.errors?.pattern &&
                        formMerchant.get('merchantCode')?.touched
                      "
                      class="text-danger"
                      >{{
                        "error.merchant.pattern.merchantCode" | translate
                      }}</small
                    >
                  </ng-container>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label
                    >{{
                      "model.merchant.masterMerchant.merchantName" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    trim
                    formControlName="merchantName"
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{
                      'model.merchant.masterMerchant.merchantName' | placeholder
                    }}"
                    [maxLength]="VALIDATORS.LENGTH.MERCHANT_NAME_MAX_LENGTH"
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('merchantName')?.errors?.required &&
                      formMerchant.get('merchantName')?.touched
                    "
                  >
                    {{ "error.merchant.required.merchantName" | translate }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('merchantName')?.errors?.maxlength &&
                      formMerchant.get('merchantName')?.touched
                    "
                  >
                    {{
                      "error.merchant.maxLength.merchantName"
                        | translate
                          : {
                              param: VALIDATORS.LENGTH.MERCHANT_NAME_MAX_LENGTH
                            }
                    }}
                  </small>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label
                    >{{ "model.merchant.merchantAccountNumber" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    trim
                    formControlName="merchantAccountNumber"
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{
                      'model.merchant.merchantAccountNumber' | placeholder
                    }}"
                    [maxLength]="
                      VALIDATORS.LENGTH.ACCOUNT_NUMBER_MERCHANT_MAX_LENGTH
                    "
                  />
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('merchantAccountNumber')?.errors
                        ?.required &&
                      formMerchant.get('merchantAccountNumber')?.touched
                    "
                  >
                    {{
                      "error.merchant.required.merchantAccountNumber"
                        | translate
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('merchantAccountNumber')?.errors
                        ?.maxlength &&
                      formMerchant.get('merchantAccountNumber')?.touched
                    "
                  >
                    {{
                      "error.merchant.maxLength.merchantAccountNumber"
                        | translate
                          : {
                              param: VALIDATORS.LENGTH.ACCOUNT_NUMBER_MAX_LENGTH
                            }
                    }}
                  </small>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('merchantAccountNumber')?.errors
                        ?.pattern &&
                      formMerchant.get('merchantAccountNumber')?.touched
                    "
                  >
                    {{
                      "error.merchant.pattern.merchantAccountNumber" | translate
                    }}
                  </small>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="row">
              <div class="col-6">
                <div class="form-group">
                  <label
                    >{{ "customerRegisterManagement.cif" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <input
                    trim
                    formControlName="cif"
                    appAutoValidate
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{
                      'customerRegisterManagement.cif' | placeholder
                    }}"
                    [maxLength]="
                      VALIDATORS.LENGTH.CASHOUT_ACCOUNT_NUMBER_MAX_LENGTH
                    "
                  />
                </div>
              </div>
              <div class="col-6">
                <div class="form-group">
                  <label>{{
                    "customerRegisterManagement.phoneNumber" | translate
                  }}</label>
                  <input
                    trim
                    formControlName="phoneNumber"
                    type="text"
                    class="w-100"
                    class="form-control"
                    placeholder="{{
                      'customerRegisterManagement.phoneNumber' | placeholder
                    }}"
                  />
                </div>
              </div>
            </div>
          </div>
          <!-- <div class="col-md-12">
            <div class="row">
              <div class="col-6">
                <div class="form-group">
                  <label
                    >{{ "currency.root" | translate
                    }}<span class="text-danger">*</span></label
                  >
                  <ng-select
                    placeholder="{{ 'currency.root' | placeholder : 'select' }}"
                    [searchable]="false"
                    formControlName="currency"
                    [clearable]="true"
                    appAutoValidate
                  >
                    <ng-option
                      [value]="item?.code"
                      *ngFor="let item of currencies"
                    >
                      {{ item.name + "" || "" | translate }}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
            </div>
          </div> -->
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-12">
                <div class="form-group">
                  <label>{{ "model.merchant.description" | translate }}</label>
                  <textarea
                    formControlName="description"
                    [maxLength]="VALIDATORS.LENGTH.MEDIUM_TEXT_MAX_LENGTH"
                    type="text"
                    class="w-100"
                    class="form-control"
                    rows="4"
                    cols="50"
                    placeholder="{{
                      'model.merchant.description' | placeholder
                    }}"
                  >
                  </textarea>
                  <small
                    class="form-text text-danger noti-small"
                    *ngIf="
                      formMerchant.get('description')?.errors?.maxlength &&
                      formMerchant.get('description')?.touched
                    "
                  >
                    {{
                      "error.merchant.maxLength.description"
                        | translate
                          : {
                              param: VALIDATORS.LENGTH.MEDIUM_TEXT_MAX_LENGTH
                            }
                    }}
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <br />
      <ng-container *hasPrivileges="SYSTEM_RULES.FEE_RATE_READ">
        <app-fee-rate
          *ngIf="isOther && isUpdate"
          [feeId]="feeId"
          [configurationFeeType]="DISCOUNT_CONFIGURATION_TYPE"
        ></app-fee-rate>
      </ng-container>
    </form>
    <div class="d-block text-center mb-5 mt-4">
      <button
        class="btn btn-white mr-2"
        (click)="backToList()"
        data-toggle="modal"
      >
        {{ "common.action.back" | translate }}
      </button>
      <ng-container
        *hasPrivileges="
          isUpdate
            ? SYSTEM_RULES.MASTER_MERCHANT_WRITE
            : SYSTEM_RULES.MASTER_MERCHANT_CREATE
        "
      >
        <button
          *ngIf="!(action === ROUTER_ACTIONS.detail)"
          class="btn btn-red"
          (click)="isUpdate ? onUpdate() : onCreate()"
        >
          {{
            (isUpdate ? "common.action.update" : "common.action.create")
              | translate
          }}
        </button>
      </ng-container>
    </div>
  </div>
</section>
