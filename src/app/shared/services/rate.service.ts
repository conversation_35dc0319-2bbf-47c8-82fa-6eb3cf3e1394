import { Injectable } from '@angular/core';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { IRate } from '@shared/models/rate.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import CommonUtils from '@shared/utils/common-utils';
import { Observable } from 'rxjs/internal/Observable';
import { AbstractDomainService } from './common/abstract.domain.service';
import { EntityResponseType } from './common/abstract.service';

@Injectable({
  providedIn: 'root',
})
export class RateService extends AbstractDomainService<IRate> {
  resource: RESOURCE_TYPE = RESOURCE_CONST.RATE;

  create(request: IRate): Observable<EntityResponseType<IRate>> {
    const body = CommonUtils.appendObjectToFormData(request, new FormData());
    return super.post<EntityResponseType<IRate>>(`/create`, body);
  }

  update(request: IRate): Observable<EntityResponseType<IRate>> {
    const body = CommonUtils.appendObjectToFormData(request, new FormData());
    return super.post<EntityResponseType<IRate>>(`/update`, body);
  }

  lock(request: IRate): Observable<EntityResponseType<IRate>> {
    return super.post<EntityResponseType<IRate>>(`/lock`, request);
  }

  unlock(request: IRate): Observable<EntityResponseType<IRate>> {
    return super.post<EntityResponseType<IRate>>(`/unlock`, request);
  }
}
