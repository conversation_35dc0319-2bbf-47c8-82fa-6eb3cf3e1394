import { Component, OnInit } from '@angular/core';
import {
  A<PERSON>tractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import {
  CALCULATE_MILLISECONDS_IN_A_YEAR,
  DATE_RANGE_STATISTICAL_LOAN_ONLINE,
  ENTITY_ORDER_CONST,
  ENTITY_STATUS_MAP,
  MOMENT_CONST,
  PAGINATION,
  SAVING_ACCOUNT_STATUS,
  SAVING_ACCOUNT_STATUS_MAP,
  SAVING_TYPE,
  SAVING_TYPE_CONST,
  SAVING_TYPE_MAP,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { LANGUAGES } from '@shared/constants/language.constants';

import { Router } from '@angular/router';
import { AbstractDomainManageComponent } from '@core/components/abstract-domain-manage/abstract-domain-manage.component';
import { RESOURCE_CONST } from '@shared/constants/resource.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IBaseResponseModel } from '@shared/models/base/base.model';
import { IMonitorLog } from '@shared/models/monitor-log.model';
import { RESOURCE_TYPE } from '@shared/models/types/resource.type';
import { DownloadService } from '@shared/services/helpers/download.service';
import { MonitorLogService } from '@shared/services/monitor-log.service';
import CommonUtils from '@shared/utils/common-utils';
import moment from 'moment';
import { NzTableQueryParams } from 'ng-zorro-antd/table';

@Component({
  selector: 'app-monitor-log-management',
  templateUrl: './monitor-log-management.component.html',
  styleUrls: ['./monitor-log-management.component.scss'],
})
export class MonitorLogManagementComponent
  extends AbstractDomainManageComponent<any>
  implements OnInit
{
  resource: RESOURCE_TYPE = RESOURCE_CONST.SAVING;
  searchForm: FormGroup = new FormGroup({});
  searchResults: IBaseResponseModel<IMonitorLog[]> = {};
  maxDate = new Date();
  data: IMonitorLog[] = [];
  maxToDate = new Date();

  pageSizeOptions = PAGINATION.OPTIONS;
  SYSTEM_RULES = SYSTEM_RULES;
  LANGUAGES = LANGUAGES;
  MOMENT_CONST = MOMENT_CONST;
  SAVING_TYPE = SAVING_TYPE;
  SAVING_TYPE_MAP = SAVING_TYPE_MAP;
  SAVING_TYPE_CONST = SAVING_TYPE_CONST;
  ENTITY_STATUS_MAP = ENTITY_STATUS_MAP;
  SAVING_ACCOUNT_STATUS_MAP = SAVING_ACCOUNT_STATUS_MAP;
  SAVING_ACCOUNT_STATUS = SAVING_ACCOUNT_STATUS;
  VALIDATORS = VALIDATORS;

  isValidMaxDate = (control: FormControl) => {
    const dateOfBirth = new Date(control.value).getTime();
    const maxDateOfBirth = new Date().getTime();
    return dateOfBirth > maxDateOfBirth ? { invalidMaxDate: true } : null;
  };

  constructor(
    private fb: FormBuilder,
    protected router: Router,
    private downloadService: DownloadService,
    private monitorLogService: MonitorLogService,
    private translateService: TranslateService
  ) {
    super(monitorLogService);
    const startDate = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDate = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.searchForm = this.fb.group({
      clientMessageId: '',
      requestUri: '',
      serviceName: '',
      types: [],
      fromDate: [CommonUtils.reverseDate(startDate), [Validators.required]],
      toDate: [
        CommonUtils.reverseDate(endDate),
        [Validators.required, this.isValidMaxDate],
      ],
      pageIndex: PAGINATION.PAGE_NUMBER_DEFAULT,
      pageSize: PAGINATION.PAGE_SIZE_DEFAULT,
      orderByType: '',
      length: 0,
      hasPageable: true,
    });
  }

  ngOnInit(): void {
    super.ngOnInit();
  }

  onParseValueSearchForm(): void {
    super.onParseValueSearchForm();
  }

  onSearch() {
    if (this.searchForm.invalid) {
      CommonUtils.markFormGroupTouched(this.searchForm);
      return;
    }
    this.monitorLogService
      .search(this.searchForm.value)
      .subscribe((res: any) => {
        this.data = res.body.content;
        this.searchForm.controls.length.setValue(res.body.totalElements, {
          emitEvent: false,
        });
      });
  }

  onReset() {
    this.searchForm.controls.clientMessageId.reset();
    this.searchForm.controls.serviceName.reset();
    this.searchForm.controls.requestUri.reset();
    const startDateReset = moment()
      .subtract(DATE_RANGE_STATISTICAL_LOAN_ONLINE, 'days')
      .format(MOMENT_CONST.FORMAT_DEFAULT);
    const endDateReset = moment().format(MOMENT_CONST.FORMAT_DEFAULT);
    this.searchForm.controls.fromDate.setValue(
      CommonUtils.reverseDate(startDateReset)
    );
    this.searchForm.controls.toDate.setValue(
      CommonUtils.reverseDate(endDateReset)
    );
    this.searchForm.controls.fromDate.clearValidators();
    this.searchForm.controls.fromDate.setValidators([Validators.required]);
    this.searchForm.controls.fromDate.updateValueAndValidity();
  }

  onChangePageSort(params: NzTableQueryParams): void {
    const { pageSize, pageIndex, sort, filter } = params;
    if (params.sort[0].value) {
      this.searchForm.controls.orderByType.setValue(
        ENTITY_ORDER_CONST.ASC.code === params.sort[0].value
          ? ENTITY_ORDER_CONST.ASC.label
          : ENTITY_ORDER_CONST.DESC.label
      );
    } else {
      this.searchForm.controls.orderByType.setValue(null);
    }
    this.monitorLogService
      .search(this.searchForm.value)
      .subscribe((res: any) => {
        this.data = res.body.content;
        this.searchForm.controls.length.setValue(res.body.totalElements, {
          emitEvent: false,
        });
      });
  }

  dateValidator(value: number): any {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return value > DATE_RANGE_STATISTICAL_LOAN_ONLINE
        ? { invalidDate: true }
        : null;
    };
  }

  changeValidDate() {
    if (
      this.searchForm.controls.fromDate.value &&
      this.searchForm.controls.toDate.value
    ) {
      if (this.searchForm.controls['toDate'].value) {
        this.maxDate = this.searchForm.controls['toDate'].value;
      }
      const startDate = CommonUtils.setStartTime(
        new Date(this.searchForm.controls.fromDate.value).getTime()
      );
      const endDate = CommonUtils.setStartTime(
        new Date(this.searchForm.controls.toDate.value).getTime()
      );
      const dayMax =
        (endDate - startDate) / CALCULATE_MILLISECONDS_IN_A_YEAR.DAY;
      if (dayMax > DATE_RANGE_STATISTICAL_LOAN_ONLINE) {
        this.searchForm.controls.fromDate.setValidators([
          Validators.required,
          this.dateValidator(dayMax),
          this.isValidMaxDate,
        ]);
        this.searchForm.controls.fromDate.updateValueAndValidity();
      } else {
        this.searchForm.controls.fromDate.clearValidators();
        this.searchForm.controls.fromDate.setValidators([
          Validators.required,
          this.isValidMaxDate,
        ]);
        this.searchForm.controls.fromDate.updateValueAndValidity();
      }
    }
  }
}
