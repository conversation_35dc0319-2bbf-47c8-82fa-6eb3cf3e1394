<div class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="searchForm" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-lg-2">
            <div class="form-group">
              <label>{{ "common.action.searchKeyword" | translate }}</label>
              <input
                trim
                class="form-control w-100"
                formControlName="keyword"
                type="text"
                placeholder="{{ 'common.action.searchKeyword' | placeholder }}"
              />
            </div>
          </div>
          <div class="col-lg-2">
            <div class="form-group">
              <label>{{ "model.manageSaving.savingType" | translate }}</label>
              <ng-select
                placeholder="{{ 'model.manageSaving.savingType' | translate }}"
                [multiple]="true"
                appearance="outline"
                [searchable]="true"
                [clearable]="true"
                class="example"
                formControlName="types"
              >
                <ng-option
                  *ngFor="let item of SAVING_TYPE"
                  [value]="item.value"
                >
                  {{ item.label | translate }}
                </ng-option>
                <ng-template ng-header-tmp>
                  <div>
                    <button
                      class="btn btn-link"
                      type="button"
                      (click)="onSelectAll()"
                    >
                      {{ "common.action.selectAll" | translate }}
                    </button>
                    <button
                      class="btn btn-link"
                      type="button"
                      (click)="onClearAll()"
                    >
                      {{ "common.action.clearAll" | translate }}
                    </button>
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="col-lg-2">
            <div class="form-group">
              <label>{{ "model.manageSaving.status" | translate }}</label>
              <ng-select
                placeholder="{{ 'model.manageSaving.status' | translate }}"
                [searchable]="false"
                formControlName="status"
                [clearable]="true"
              >
                <ng-option
                  [value]="item.code"
                  *ngFor="let item of SAVING_ACCOUNT_STATUS"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-lg-4">
            <div class="form-group row">
              <div class="col-md-6">
                <label class=""
                  >{{ "model.insuranceManage.fromDate" | translate
                  }}<small class="text-danger">*</small></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="fromDate"
                    formControlName="fromDate"
                    [placeholder]="MOMENT_CONST.FORMAT_DATE_PLACEHOLDER"
                    max="{{
                      searchForm.controls['toDate'].value
                        ? (searchForm.controls['toDate'].value
                          | date : MOMENT_CONST.FORMAT_DATE_MAT_PICKER)
                        : (maxDate | date : MOMENT_CONST.FORMAT_DATE_MAT_PICKER)
                    }}"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="fromDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #fromDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="text-danger"
                  *ngIf="
                    searchForm.get('fromDate')?.errors?.required &&
                    searchForm.get('fromDate')?.touched
                  "
                >
                  {{
                    "error.insuranceManage.required.fromDate" | translate
                  }}</small
                >
                <small
                  class="text-danger"
                  *ngIf="
                    searchForm.get('fromDate')?.errors
                      ?.invalidFromDateCurrent &&
                    searchForm.get('fromDate')?.touched
                  "
                >
                  {{
                    "error.insuranceManage.dateTime.invalidFromDateCurrent"
                      | translate
                  }}</small
                >
              </div>
              <div class="col-md-6">
                <label class=""
                  >{{ "model.insuranceManage.toDate" | translate
                  }}<small class="text-danger">*</small></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="toDate"
                    formControlName="toDate"
                    [placeholder]="MOMENT_CONST.FORMAT_DATE_PLACEHOLDER"
                    dateTransform
                    [max]="maxDate"
                    min="{{
                      searchForm.controls['fromDate'].value
                        | date : MOMENT_CONST.FORMAT_DATE_MAT_PICKER
                    }}"
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="toDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #toDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="text-danger"
                  *ngIf="
                    searchForm.get('toDate')?.errors?.required &&
                    searchForm.get('toDate')?.touched
                  "
                >
                  {{
                    "error.insuranceManage.required.toDate" | translate
                  }}</small
                >
                <small
                  class="text-danger"
                  *ngIf="
                    searchForm.get('toDate')?.errors?.invalidToDate &&
                    searchForm.get('toDate')?.touched
                  "
                >
                  {{
                    "error.insuranceManage.dateTime.invalidToDate" | translate
                  }}</small
                >
                <small
                  class="text-danger"
                  *ngIf="
                    searchForm.get('toDate')?.errors?.invalidToDateCurrent &&
                    searchForm.get('toDate')?.touched
                  "
                >
                  {{
                    "error.insuranceManage.dateTime.invalidToDateCurrent"
                      | translate
                  }}</small
                >
              </div>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
              (click)="onReset()"
            >
              <div class="btn-reset">
                <i class="bi bi-arrow-clockwise" type="button"> </i>
              </div>
            </div>
            <div class="">
              <button
                class="btn btn-search mr-2"
                type="submit"
                *hasPrivileges="SYSTEM_RULES.SAVING_ACCOUNT_READ"
              >
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div
          class="d-flex justify-content-between align-items-end text-right mb-3 mt-4"
        >
          <h5 class="title-table">
            {{ "model.manageSaving.manageSavingTitle" | translate }}
          </h5>
          <button
            class="btn btn-red"
            type="button"
            *hasPrivileges="SYSTEM_RULES.SAVING_ACCOUNT_EXPORT"
            (click)="exportFile()"
          >
            {{ "common.action.export" | translate }}
          </button>
        </div>
      </form>

      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th [width]="'50px'" class="text-center">
                {{ "model.manageSaving.no" | translate }}
              </th>
              <th [width]="'180px'" class="text-right">
                {{ "model.manageSaving.accountNumber" | translate }}
              </th>
              <th class="text-left" [width]="'250px'">
                {{ "model.manageSaving.accountOwner" | translate }}
              </th>
              <th [width]="'100px'" class="text-right">
                {{ "model.manageSaving.cif" | translate }}
              </th>
              <th [width]="'220px'" class="text-right">
                {{ "model.manageSaving.gttt" | translate }}
              </th>
              <th [width]="'220px'" class="text-right">
                {{ "model.manageSaving.phoneNumber" | translate }}
              </th>
              <th [width]="'250px'" class="text-left">
                {{ "model.manageSaving.savingType" | translate }}
              </th>
              <th [width]="'250px'" class="text-left">
                {{ "model.manageSaving.money" | translate }}
              </th>
              <th [width]="'300px'" class="text-right">
                {{ "model.manageSaving.startTime" | translate }}
              </th>
              <th [width]="'200px'" class="text-right">
                {{ "model.referral.rmCode" | translate }}
              </th>
              <th [width]="'200px'" class="text-center">
                {{ "model.manageSaving.status" | translate }}
              </th>
              <th [width]="'200px'" class="text-center">
                {{ "common.action.label" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of data; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td class="text-right">{{ item.savingAccountNumber }}</td>
              <td class="text-left">{{ item.fullname }}</td>
              <td class="text-right">{{ item.cif }}</td>
              <td class="text-right">{{ item.idCardNumber }}</td>
              <td class="text-right">{{ item.phoneNumber }}</td>
              <td class="text-left">
                {{ SAVING_TYPE_MAP[item.type ?? ""].label | translate }}
              </td>
              <td class="text-left">{{ item.savingAmount | currencyLak }}</td>
              <td class="text-right">{{ item.startTime }}</td>
              <td class="text-right">{{ item.rmCode }}</td>
              <td class="text-center">
                <span
                  class="badge"
                  [ngClass]="SAVING_ACCOUNT_STATUS_MAP[item.status || 0].style"
                >
                  {{
                    SAVING_ACCOUNT_STATUS_MAP[item.status || 0].label
                      | translate
                  }}
                </span>
              </td>
              <td class="text-center">
                <button
                  ngbTooltip="{{ 'common.action.detail' | translate }}"
                  class="btn px-1 py-0"
                  (click)="detail(item.savingAccountId)"
                >
                  <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        <div
          class="row d-block text-center m-0 no-search-result-wrapper"
          *ngIf="data?.length === 0"
        >
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
      </div>
      <div *ngIf="data?.length">
        <mat-paginator
          [length]="searchForm.value.length"
          [pageSize]="searchForm.value.pageSize"
          [pageIndex]="searchForm.value.pageIndex"
          [pageSizeOptions]="pageSizeOptions"
          (page)="onChangePage($event)"
          aria-label="Select page"
        >
        </mat-paginator>
      </div>
    </div>
  </div>
</div>
