import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class LanguageService {
  renderer: Renderer2;
  private _language: BehaviorSubject<string>;

  constructor(
    private translateService: TranslateService,
    private titleService: Title,
    private router: Router,
    rootRenderer: RendererFactory2
  ) {
    this._language = new BehaviorSubject<string>(
      this.translateService.currentLang
    );
    this.renderer = rootRenderer.createRenderer(
      document.querySelector('html'),
      null
    );
    this.init();
  }

  get language(): Observable<string> {
    return this._language.asObservable();
  }

  updateTitle(titleKey?: string) {
    if (!titleKey) {
      titleKey = this.getPageTitle(this.router.routerState.snapshot.root);
    }

    this.translateService.get(titleKey).subscribe((title) => {
      this.titleService.setTitle(title);
    });
  }

  private init() {
    this.translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this._language.next(this.translateService.currentLang);
      this.renderer.setAttribute(
        document.querySelector('html'),
        'lang',
        this.translateService.currentLang
      );
      this.updateTitle();
    });
  }

  public getPageTitle(routeSnapshot: ActivatedRouteSnapshot) {
    let title: string =
      routeSnapshot.data && routeSnapshot.data['title']
        ? routeSnapshot.data['title']
        : 'MB LAOS';
    if (routeSnapshot.firstChild) {
      title = this.getPageTitle(routeSnapshot.firstChild) || title;
    }
    return title;
  }
}
