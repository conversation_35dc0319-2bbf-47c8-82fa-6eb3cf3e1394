<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12">
      <form [formGroup]="formSearch" (submit)="onSearchSubmit()">
        <div class="row">
          <div class="col-lg-3 col-md-4">
            <div class="form-group">
              <label>{{ "model.customer.cif" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="cif"
                class="w-100"
                class="form-control"
                [maxLength]="VALIDATORS.LENGTH.IDENTIFICATION_MIN_LENGTH"
                placeholder="{{ 'model.customer.cif' | placeholder }}"
              />
            </div>
          </div>
          <div class="col-lg-3 col-md-4">
            <div class="form-group">
              <label>{{ "model.customer.phoneNumber" | translate }}</label>
              <input
                numbersOnly
                type="text"
                [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
                formControlName="phoneNumber"
                class="w-100"
                class="form-control"
                placeholder="{{ 'model.customer.phoneNumber' | placeholder }}"
              />
            </div>
          </div>
          <div class="col-lg-3 col-md-4">
            <div class="form-group">
              <label>{{ "model.customer.idCardNumber" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="idCardNumber"
                class="w-100"
                class="form-control"
                placeholder="{{ 'model.customer.idCardNumber' | placeholder }}"
                [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
              />
            </div>
          </div>
          <div class="col-lg-3 col-md-4">
            <div class="form-group">
              <label>{{ "model.customer.email" | translate }}</label>
              <input
                trim
                type="text"
                formControlName="email"
                class="w-100"
                class="form-control"
                placeholder="{{ 'model.customer.email' | placeholder }}"
                [maxLength]="VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH"
              />
            </div>
          </div>
          <div class="col-lg-3 col-md-4">
            <div class="form-group">
              <label
                ngbTooltip="{{
                  'model.customer.customerType.customer' | translate
                }}"
                >{{
                  "model.customer.customerType.customer"
                    | translate
                    | limitWord : LIMIT_LENGTH_WORD_CONST.LIMIT_WORD_26
                }}</label
              >
              <ng-select
                placeholder="{{
                  'model.customer.customerType.customer'
                    | placeholder : 'select'
                }}"
                [searchable]="false"
                formControlName="accountType"
                [clearable]="true"
              >
                <ng-option
                  *ngFor="let item of CUSTOMER_TYPE"
                  [value]="item.value"
                >
                  <div [title]="item.label | translate">
                    {{
                      item.label
                        | translate
                        | limitWord : LIMIT_LENGTH_WORD_CONST.LIMIT_WORD_40
                    }}
                  </div>
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-lg-3 col-md-4">
            <div class="form-group">
              <label>{{
                "customerRegisterManagement.customerApproval.approvalStatus"
                  | translate
              }}</label>
              <ng-select
                placeholder="{{ 'common.status' | placeholder : 'select' }}"
                [searchable]="false"
                formControlName="activeStatus"
                [clearable]="true"
              >
                <ng-option
                  [value]="item.value"
                  *ngFor="let item of CUSTOMER_ACTIVE_STATUS"
                >
                  {{ item.label | translate }}
                </ng-option>
              </ng-select>
            </div>
          </div>
          <div class="col-lg-4 col-lg-2">
            <div class="date-picker-container form-group row">
              <div class="col-md-6">
                <label
                  >{{ "common.action.fromDate" | translate
                  }}<span class="text-danger">*</span></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="fromDate"
                    formControlName="fromDate"
                    placeholder="DD/MM/YYYY"
                    [max]="maxDate | date : 'yyyy-MM-dd'"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="fromDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #fromDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formSearch.get('fromDate')?.errors?.required &&
                    formSearch.get('fromDate')?.touched
                  "
                >
                  {{ "error.required.fromDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="isErrorStartDateGreaterEndDate"
                >
                  {{ "error.toDateMustGreatherFromDate" | translate }}
                </small>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="formSearch.get('fromDate')?.errors?.invalidDate"
                >
                  {{ "error.required.inValidDate" | translate }}
                </small>
              </div>
              <div class="col-md-6">
                <label
                  >{{ "common.action.toDate" | translate
                  }}<span class="text-danger">*</span></label
                >
                <mat-form-field appearance="fill" class="date-picker">
                  <input
                    matInput
                    [matDatepicker]="toDate"
                    formControlName="toDate"
                    placeholder="DD/MM/YYYY"
                    min="{{
                      formSearch.controls['fromDate'].value
                        | date : 'yyyy-MM-dd'
                    }}"
                    [max]="MAX_DATE_CONST"
                    (change)="changeValidDate()"
                    (dateInput)="changeValidDate()"
                    dateTransform
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="toDate"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #toDate></mat-datepicker>
                </mat-form-field>
                <small
                  class="form-text text-danger noti-small"
                  *ngIf="
                    formSearch.get('toDate')?.errors?.required &&
                    formSearch.get('toDate')?.touched
                  "
                >
                  {{ "error.required.toDate" | translate }}
                </small>
              </div>
            </div>
          </div>
          <div class="col-md-2 col-lg-2 mt-4 d-flex">
            <div
              class="col-btn-reset"
              ngbTooltip="{{ 'common.action.reset' | translate }}"
            >
              <div class="btn-reset">
                <i
                  class="bi bi-arrow-clockwise"
                  type="button"
                  (click)="onReset()"
                >
                </i>
              </div>
            </div>
            <div class="">
              <button class="btn btn-search mr-2" type="submit">
                {{ "common.action.search" | translate }}
              </button>
            </div>
          </div>
        </div>
        <div class="border-bottom-search"></div>
        <div class="d-block text-right mb-3 mt-4">
          <button
            type="button"
            class="btn btn-red"
            (click)="exportFile()"
            *hasPrivileges="SYSTEM_RULES.MANUAL_ACTIVATION_EXPORT"
            [disabled]="data.length === 0"
          >
            {{ "common.action.export" | translate }}
          </button>
          <button
            class="btn btn-red ml-2"
            type="button"
            [disabled]="!onShowButtonAndCol"
            (click)="onActiveAccount(setOfCheckedId)"
            *hasPrivileges="SYSTEM_RULES.MANUAL_ACTIVATION"
          >
            {{ "common.activate" | translate }}
          </button>
        </div>
      </form>
      <div></div>
      <div class="table-responsive">
        <nz-table
          [nzData]="data"
          (nzCurrentPageDataChange)="onCurrentPageDataChange($event)"
          class="table-account-transaction"
        >
          <thead>
            <tr>
              <th class="text-center" scope="col">
                {{ "common.no" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.customer.cif" | translate }}
              </th>
              <th scope="col" class="text-left">
                {{ "model.customer.fullname" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "model.customer.idCardNumber" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "model.customer.dateOfBirth" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "model.customer.phoneNumber" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "model.customer.email" | translate }}
              </th>
              <th scope="col" class="text-center">
                {{ "model.customer.manualActivationTime" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "model.customer.customerType.customer" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.status" | translate }}
              </th>
              <th class="text-center" scope="col">
                {{ "common.action.approval" | translate }}
              </th>
              <th
                [nzChecked]="checked"
                [className]="onShowButtonAndCol ? '' : 'hide'"
                [nzIndeterminate]="indeterminate"
                (nzCheckedChange)="onAllChecked($event)"
                *hasPrivileges="SYSTEM_RULES.MANUAL_ACTIVATION"
              ></th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of data; let i = index">
              <td class="text-center">{{ fillIndexItem(i) }}</td>
              <td class="text-left">
                {{ item?.cif }}
              </td>
              <td class="text-left">
                {{ item?.fullname | limitWord }}
              </td>
              <td class="text-center">
                {{ item?.idCardNumber }}
              </td>
              <td class="text-center">
                {{ item?.dateOfBirth }}
              </td>
              <td class="text-center">
                {{ item?.phoneNumber }}
              </td>
              <td>
                <span title="{{ item.email }}">{{
                  item?.email | limitWord
                }}</span>
              </td>
              <td class="text-center">
                {{ item?.manualActivationTime }}
              </td>
              <td class="text-center">
                <span>{{
                  CUSTOMER_TYPE_MAP[item?.accountType || 0] | translate
                }}</span>
              </td>
              <td class="text-center">
                <span
                  class="badge"
                  [ngClass]="
                    CUSTOMER_ACTIVE_STATUS_MAP[item?.activeStatus || 0].style
                  "
                  >{{
                    CUSTOMER_ACTIVE_STATUS_MAP[item?.activeStatus || 0].label
                      | translate
                  }}</span
                >
              </td>
              <td class="text-center">
                <button
                  ngbTooltip="{{ 'common.action.detail' | translate }}"
                  class="btn px-1 py-0 mr-1"
                  (click)="onDetail(item)"
                >
                  <i class="bi bi-eye mb-color" aria-hidden="true"></i>
                </button>
                <ng-container
                  *ngIf="
                    item?.activeStatus ===
                    CUSTOMER_ACTIVE_STATUS_CONST.INACTIVATED.value
                  "
                >
                  <nz-switch
                    [nzControl]="true"
                    nzSize="small"
                    (click)="onActiveAccount(item)"
                    *hasPrivileges="SYSTEM_RULES.MANUAL_ACTIVATION"
                    ngbTooltip="{{ 'common.activate' | translate }}"
                  ></nz-switch>
                </ng-container>
              </td>
              <td
                [nzChecked]="setOfCheckedId.has(item?.customerId)"
                [className]="
                  item?.activeStatus ===
                  CUSTOMER_ACTIVE_STATUS_CONST.INACTIVATED.value
                    ? ''
                    : 'hide'
                "
                (nzCheckedChange)="onItemChecked(item?.customerId, $event)"
                *hasPrivileges="SYSTEM_RULES.MANUAL_ACTIVATION"
              ></td>
            </tr>
          </tbody>
        </nz-table>
        <div class="row d-block text-center m-0" *ngIf="data?.length === 0">
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
        <div *ngIf="data?.length" class="paginator col-md-12">
          <mat-paginator
            [length]="formSearch.value.length"
            [pageSize]="formSearch.value.pageSize"
            [pageIndex]="formSearch.value.pageIndex"
            [pageSizeOptions]="pageSizeOptions"
            (page)="onChangePage($event)"
            aria-label="Select page"
          >
          </mat-paginator>
        </div>
      </div>
    </div>
  </div>
</section>
