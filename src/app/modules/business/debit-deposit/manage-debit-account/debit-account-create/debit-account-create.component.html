<div class="modal-content">
  <div class="modal-header">
    <h5 class="modal-title">
      {{ "model.managePayment.modal.create" | translate }}
    </h5>
    <button
      type="button"
      class="close"
      data-dismiss="modal"
      aria-label="Close"
      (click)="activeModal.close()"
    >
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <form [formGroup]="formCreate">
      <div class="col-md-12">
        <div class="row">
          <div class="col-md-12">
            <div class="form-group">
              <label
                >{{ "model.managePayment.accountNumber" | translate
                }}<span class="text-danger small-text"> *</span></label
              >
              <input
                trim
                placeholder="{{
                  'model.managePayment.accountNumber' | placeholder
                }}"
                type="text"
                class="form-control w-100"
                [maxLength]="
                  VALIDATORS.LENGTH.CASHOUT_ACCOUNT_NUMBER_MAX_LENGTH
                "
                formControlName="accountNumber"
              />
              <small
                class="text-danger"
                *ngIf="
                  formCreate.get('accountNumber')?.errors?.required &&
                  formCreate.get('accountNumber')?.touched
                "
              >
                {{ "error.managePayment.required.accountNumber" | translate }}
              </small>
              <small
                class="text-danger"
                *ngIf="
                  formCreate.get('accountNumber')?.errors?.maxLength &&
                  formCreate.get('accountNumber')?.touched
                "
              >
                {{ "error.managePayment.length.accountNumber" | translate }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formCreate.get('accountNumber')?.errors?.pattern &&
                  formCreate.get('accountNumber')?.touched
                "
              >
                {{ "error.managePayment.pattern.accountNumber" | translate }}
              </small>
            </div>
          </div>
          <div class="col-md-12">
            <div class="form-group">
              <label>
                {{ "model.manageSaving.cif" | translate
                }}<span class="text-danger small-text"> *</span></label
              >
              <input
                placeholder="{{ 'model.manageSaving.cif' | placeholder }}"
                type="text"
                class="form-control w-100"
                formControlName="cif"
              />
              <small
                class="text-danger"
                *ngIf="
                  formCreate.get('cif')?.errors?.required &&
                  formCreate.get('cif')?.touched
                "
              >
                {{ "error.customerDetail.required.cif" | translate }}
              </small>
              <small
                class="form-text text-danger noti-small"
                *ngIf="
                  formCreate.get('cif')?.errors?.pattern &&
                  formCreate.get('cif')?.touched
                "
              >
                {{ "error.cif" | translate }}
              </small>
            </div>
          </div>
          <div class="col-md-12">
            <div class="form-group">
              <label
                >{{ "model.managePayment.modal.accountName" | translate
                }}<span class="text-danger small-text"> *</span></label
              >
              <input
                placeholder="{{
                  'model.managePayment.modal.accountName' | placeholder
                }}"
                type="text"
                class="form-control w-100"
                [maxLength]="VALIDATORS.LENGTH.ACCOUNT_NAME_LENGTH"
                formControlName="accountName"
              />
              <small
                class="text-danger"
                *ngIf="
                  formCreate.get('accountName')?.errors?.required &&
                  formCreate.get('accountName')?.touched
                "
              >
                {{ "error.managePayment.required.accountName" | translate }}
              </small>
            </div>
          </div>
          <div class="col-md-12">
            <div class="form-group">
              <label
                >{{ "managePayment.limit" | translate
                }}<span class="text-danger small-text"> *</span></label
              >
              <input
                placeholder="{{ 'managePayment.limit' | placeholder }}"
                type="text"
                class="form-control w-100"
                [maxLength]="VALIDATORS.LENGTH.TRANSACTION_LIMIT_MAX_LENGTH"
                formControlName="transactionLimit"
                appMoneyInput
                trim
              />
              <small
                class="text-danger"
                *ngIf="
                  formCreate.get('transactionLimit')?.errors?.required &&
                  formCreate.get('transactionLimit')?.touched
                "
              >
                {{
                  "error.managePayment.required.transactionLimit" | translate
                }}
              </small>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="text-center pb-4">
    <button
      type="button"
      class="btn mb-btn-outline-color mr-3"
      (click)="activeModal.close()"
    >
      {{ "common.action.close" | translate }}
    </button>
    <ng-container *hasPrivileges="SYSTEM_RULES.CASHOUT_ACCOUNT_WRITE">
      <button type="button" class="btn mb-btn-color" (click)="onCreate()">
        {{ "common.action.create" | translate }}
      </button>
    </ng-container>
  </div>
</div>
