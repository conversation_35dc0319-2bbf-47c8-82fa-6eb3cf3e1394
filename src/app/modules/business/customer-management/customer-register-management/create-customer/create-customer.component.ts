import { Component, HostListener, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  FILE_UPLOAD_EXTENSIONS,
  GENDER,
  MODAL_ACTION,
} from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import {
  CUSTOMER_SECTOR,
  CUSTOMER_SECTOR_ID_CONST,
  CUSTOMER_SECTOR_MAP,
  ID_CARD_TYPE,
  MARITAL_STATUS,
  MINUS_YEAR,
  PREMIUM_ACCOUNT_NUMBER_OBJECT,
  QUANTITY_FILE_ID_CARD_CUSTOMER_UPLOAD,
  RESIDENT_STATUS,
} from '@shared/constants/customer.constants';
import { STORAGE_APP } from '@shared/constants/storage.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { I<PERSON>ustomer } from '@shared/models/customer.model';
import { IIdCardType } from '@shared/models/id-card-type.model';
import { INation } from '@shared/models/nation.model';
import { IFileEntrySearch } from '@shared/models/request/file-entry.search';
import { AreaService } from '@shared/services/area.service';
import { CustomerService } from '@shared/services/customer.service';
import { ModalService } from '@shared/services/helpers/modal.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { IdCardTypeService } from '@shared/services/id-card-type.service';
import { PremiumAccNumberService } from '@shared/services/premium-acc-number.service';
import { ROUTER_ACTIONS, ROUTER_UTILS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-create-customer',
  templateUrl: './create-customer.component.html',
  styleUrls: ['./create-customer.component.scss'],
})
export class CreateCustomerComponent implements OnInit, OnDestroy {
  protected _unsubscribeAll$ = new Subject();
  customerCreateForm!: FormGroup;
  customerInfo: ICustomer = {};
  eKycPics: File[] = [];
  eKycPicUrls: string[] = [];
  reader: FileReader = new FileReader();
  idCardTypes: IIdCardType[] = [];
  nations: INation[] = [];
  fileRequired: string[] = ['idCardFront'];
  fileUploads: any[] = [];
  maxDateOfBirth: any;
  maxIssueDate: any;
  minIssueDate: any;
  imageUrls: any[] = [];
  fileDelete: number[] = [];

  ROUTER_ACTIONS = ROUTER_ACTIONS;
  VALIDATORS = VALIDATORS;
  FILE_UPLOAD_EXTENSIONS = FILE_UPLOAD_EXTENSIONS;
  routerAction = ROUTER_ACTIONS.create;
  MARITAL_STATUS = MARITAL_STATUS;
  RESIDENT_STATUS = RESIDENT_STATUS;
  GENDER_LIST = GENDER;
  SYSTEM_RULES = SYSTEM_RULES;
  CUSTOMER_SECTOR = CUSTOMER_SECTOR;
  CUSTOMER_SECTOR_MAP = CUSTOMER_SECTOR_MAP;

  isUpdateScreen = false;
  accountExistedT24 = false;
  checkConfirmSearch = false;
  hasAccountNumber = false;
  disabledSearch = false;
  disabledFile = true;
  hasFilter = false;
  ID_CARD_TYPE = [1, 2];

  constructor(
    private formBuilder: FormBuilder,
    private customerService: CustomerService,
    private idCardTypeService: IdCardTypeService,
    private modalService: ModalService,
    private router: Router,
    private toastService: ToastrCustomService,
    private translate: TranslateService,
    private areaService: AreaService,
    private activatedRoute: ActivatedRoute,
    private premiumAccNumberService: PremiumAccNumberService
  ) {
    this.activatedRoute.data.subscribe((res) => {
      this.routerAction = res.action;
      if (res.action === ROUTER_ACTIONS.update) {
        this.isUpdateScreen = true;
        this.activatedRoute.paramMap.subscribe((reS) => {
          const idParam = reS.get('customerId');
          if (idParam) {
            this.customerInfo.customerId = +idParam;
          }
        });
      }
    });
    const externalPremium = this.activatedRoute.snapshot.queryParamMap.get(
      PREMIUM_ACCOUNT_NUMBER_OBJECT.ACCOUNT_NUMBER
    );
    const externalPrice = this.activatedRoute.snapshot.queryParamMap.get(
      PREMIUM_ACCOUNT_NUMBER_OBJECT.PRICE
    );
    if (externalPremium && externalPrice) {
      this.customerInfo.premiumAccountNumber = externalPremium;
      this.customerInfo.totalPrice = +externalPrice;
    }
  }

  /**
   * remove storage
   */
  ngOnDestroy(): void {
    if (!this.hasFilter) {
      sessionStorage.removeItem(STORAGE_APP.CUSTOMER_REGISTER);
    }
    this._unsubscribeAll$.next();
    this._unsubscribeAll$.complete();
  }

  /**
   * catch event back
   */
  @HostListener('window:popstate', ['$event'])
  backToListPage() {
    this.hasFilter = true;
  }

  ngOnInit(): void {
    this.getNation();
    this.getIdCardType();
    this.initFileReader();
    if (this.customerInfo.customerId) {
      this.getCustomerDetail();
      this.checkConfirmSearch = true;
      this.disabledFile = false;
      // this.fileRequired = [];
    } else {
      this.initForm();
    }
  }

  // @HostListener('window:beforeunload', ['$event'])
  // beforeunload(event: Event): void {
  //   debugger;
  //   event.preventDefault();
  //   event.stopPropagation();
  //   // event.returnValue = 'A message.';
  // }

  // @HostListener('document:keydown.f5', ['$event'])
  // keydownF5(event: any): void {
  //   debugger;
  //   // event.preventDefault();
  //   // event.returnValue = 'A message.';
  // }

  // @HostListener('document:keydown.control.f5', ['$event'])
  // controlF5(event: any): void {
  //   debugger;
  //   // event.preventDefault();
  //   // event.returnValue = 'A message.';
  // }

  // @HostListener('document:keydown.shift.f5', ['$event'])
  // shiftF5(event: any): void {
  //   debugger;
  //   // event.preventDefault();
  //   // event.returnValue = 'A message.';
  // }

  /**
   * Init form and set form value case update
   *
   * @param data any
   */
  initForm(data?: ICustomer): void {
    if (
      data?.customerSectorId === CUSTOMER_SECTOR_ID_CONST.SECTOR_1890.value ||
      data?.customerSectorId === CUSTOMER_SECTOR_ID_CONST.SECTOR_1891.value
    ) {
      CUSTOMER_SECTOR.forEach((item, index) => {
        if (item.value === CUSTOMER_SECTOR_ID_CONST.SECTOR_1740.value) {
          CUSTOMER_SECTOR.splice(index);
        }
      });
    }

    this.customerCreateForm = this.formBuilder.group({
      customerId: [data?.customerId || null],
      customerDraftId: [data?.customerDraftId || null],
      phoneNumber: [
        data?.phoneNumber || null,
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.PHONE),
          Validators.minLength(VALIDATORS.LENGTH.PHONE_NUMBER_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.PHONE_NUMBER_MAX_LENGTH),
        ],
      ],
      idCardTypeId: [data?.idCardTypeId || null, [Validators.required]],
      idCardNumber: [
        data?.idCardNumber || null,
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.ID),
          Validators.maxLength(VALIDATORS.LENGTH.IDENTIFICATION_MAX_LENGTH),
          Validators.minLength(VALIDATORS.LENGTH.IDENTIFICATION_MIN_LENGTH),
        ],
      ],
      fullname: [
        data?.fullname || null,
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.FULLNAME),
          Validators.minLength(VALIDATORS.LENGTH.FULL_NAME_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
        ],
      ],
      placeOfOrigin: [
        data?.placeOfOrigin || null,
        [
          Validators.required,
          Validators.minLength(VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.ADDRESS_MAX_LENGTH),
        ],
      ],
      gender: [data?.gender, [Validators.required]],
      dateOfBirth: [
        CommonUtils.reverseDate(data?.dateOfBirth) || null,
        [Validators.required],
      ],
      email: [
        data?.email || null,
        [
          Validators.pattern(VALIDATORS.PATTERN.EMAIL),
          Validators.minLength(VALIDATORS.LENGTH.EMAIL_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.EMAIL_MAX_LENGTH),
        ],
      ],
      // description: [
      //   this.customerDTO.description || null,
      //   [
      //     Validators.required,
      //     Validators.maxLength(VALIDATORS.LENGTH.DESCRIPTION_MAX_LENGTH),
      //   ],
      // ],
      issueDate: [
        CommonUtils.reverseDate(data?.issueDate) || null,
        [Validators.required, this.isValidMaxDate],
      ],
      issuePlace: [
        data?.issuePlace || null,
        [
          Validators.required,
          Validators.minLength(VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
        ],
      ],
      cif: [data?.cif || null],
      nationCode: [
        data?.nationCode || null,
        [
          Validators.required,
          Validators.minLength(VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.SMALL_TEXT_MAX_LENGTH),
        ],
      ],
      nationCodeOther: [data?.nationCodeOther || null],
      placeOfResidence: [
        data?.placeOfResidence || null,
        [
          Validators.required,
          Validators.minLength(VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
        ],
      ],
      placeOfResidenceOutCountry: [
        data?.placeOfResidenceOutCountry || null,
        [
          Validators.minLength(VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
        ],
      ],
      currentAddress: [
        data?.currentAddress || null,
        [
          Validators.minLength(VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
        ],
      ],
      phoneContact: [
        data?.phoneContact || null,
        [
          Validators.pattern(VALIDATORS.PATTERN.PHONE),
          Validators.minLength(VALIDATORS.LENGTH.PHONE_NUMBER_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.PHONE_NUMBER_MAX_LENGTH),
        ],
      ],
      maritalStatus: [data?.maritalStatus],
      job: [
        data?.job || null,
        [Validators.maxLength(VALIDATORS.LENGTH.JOB_MAX_LENGTH)],
      ],
      position: [
        data?.position || null,
        [Validators.maxLength(VALIDATORS.LENGTH.POSITION_MAX_LENGTH)],
      ],
      workplace: [
        data?.workplace || null,
        [Validators.maxLength(VALIDATORS.LENGTH.SMALL_TEXT_MAX_LENGTH)],
      ],
      customerSectorId: [
        // this.customerDTO.customerSectorId || null,
        data?.customerSectorId,
        // data?.customerSectorId,
        // data?.customerSectorId || CUSTOMER_SECTOR_ID_CONST.SECTOR_1891.value,
      ],
      customerOldSectorId: [data?.customerOldSectorId],
      staffCode: [
        data?.staffCode || null,
        [
          Validators.pattern(VALIDATORS.PATTERN.PHONE),
          Validators.minLength(VALIDATORS.LENGTH.PHONE_NUMBER_MIN_LENGTH),
          Validators.maxLength(VALIDATORS.LENGTH.PHONE_NUMBER_MAX_LENGTH),
        ],
      ],
      // shopCode: [
      //   this.customerDTO.shopCode || null,
      //   Validators.required,
      // ],
      // companyID: [
      //   this.customerDTO.companyID || null,
      //   [Validators.maxLength(VALIDATORS.LENGTH.COMPANY_ID_MAX_LENGTH)],
      // ],
      residentStatus: [data?.residentStatus],
      files: [null],
      premiumAccountNumber: [
        data?.premiumAccountNumber || null,
        [Validators.pattern(VALIDATORS.PATTERN.SPECIAL_ACCOUNT_NUMBER)],
      ],
      price: [
        data?.totalPrice
          ? CommonUtils.moneyFormatNumber(data?.totalPrice)
          : 0 || 0,
      ],
    });

    if (!data) {
      this.disableForm();
    }
    if (data) {
      setTimeout(() => {
        this.getInnitFormDisable();
      }, 500);
    }

    this.validDate();
    if (
      !this.accountExistedT24 ||
      (this.accountExistedT24 && !this.hasAccountNumber)
    ) {
      this.enrichValueForm();
      this.valueChangeForm();
    }
  }

  valueChangeForm(): void {
    this.customerCreateForm?.controls.premiumAccountNumber.valueChanges
      ?.pipe(
        debounceTime(500),
        distinctUntilChanged(),
        takeUntil(this._unsubscribeAll$)
      )
      ?.subscribe((value) => {
        if (
          value &&
          this.customerCreateForm?.controls.premiumAccountNumber.valid
        ) {
          this.premiumAccNumberService
            .searchPremiumAccNumberCustomer({ expectedString: value })
            .subscribe(
              (res: any) => {
                this.customerCreateForm?.controls.price.setValue(
                  CommonUtils.moneyFormatNumber(res.body.totalPrice)
                );
              },
              (error) => {
                if (error) {
                  this.customerCreateForm
                    .get('premiumAccountNumber')
                    ?.setValue(null);
                  this.customerCreateForm.get('price')?.setValue(0);
                }
              }
            );
        } else {
          if (
            this.customerInfo.premiumAccountNumber &&
            value === this.customerInfo.premiumAccountNumber &&
            this.customerInfo.premiumAccountNumber.match(
              VALIDATORS.PATTERN.PHONE
            )
          ) {
            this.customerCreateForm
              .get('price')
              ?.setValue(
                CommonUtils.moneyFormatNumber(this.customerInfo.totalPrice || 0)
              );
          } else {
            this.customerCreateForm?.controls.price.setValue(0);
          }
        }
      });
  }

  enrichValueForm(): void {
    if (
      (this.customerInfo.totalPrice || this.customerInfo.totalPrice === 0) &&
      this.customerInfo.premiumAccountNumber
    ) {
      this.customerCreateForm
        .get('premiumAccountNumber')
        ?.setValue(this.customerInfo.premiumAccountNumber);
      this.customerCreateForm
        .get('price')
        ?.setValue(CommonUtils.moneyFormatNumber(this.customerInfo.totalPrice));

      if (
        this.customerInfo.premiumAccountNumber.match(VALIDATORS.PATTERN.PHONE)
      ) {
        this.customerCreateForm
          .get('phoneNumber')
          ?.setValue(this.customerInfo.premiumAccountNumber);
      }
    }
  }

  // getCustomerSectorId(): void {
  //   if (
  //     this.customerCreateForm.get('customerSectorId')?.value ===
  //     CUSTOMER_SECTOR_ID_CONST.SECTOR_1740.value
  //   ) {
  //     this.customerCreateForm.controls.customerSectorId.disable();
  //   }
  // }

  /**
   * disable input when input have data
   */
  getInnitFormDisable(): void {
    if (!this.isUpdateScreen) {
      const keyForm = Object.keys(this.customerCreateForm.value);
      this.customerCreateForm.enable();
      // this.getCustomerSectorId();
      for (const key of keyForm) {
        if (this.customerCreateForm.controls[key].value) {
          this.customerCreateForm.get([key])?.disable();
        }
      }
      this.customerCreateForm.controls.premiumAccountNumber.enable();
    } else {
      this.customerCreateForm.enable();
      // this.getCustomerSectorId();
    }

    this.customerCreateForm.controls.idCardTypeId.disable();
    this.customerCreateForm.controls.idCardNumber.disable();
    this.customerCreateForm.controls.phoneNumber.disable();
    this.customerCreateForm.controls.customerOldSectorId.disable();
    this.customerCreateForm.controls.cif.disable();
    // this.customerCreateForm.controls.premiumAccountNumber.disable();
    this.customerCreateForm.controls.price.disable();
  }

  /**
   * disable form and enable idCardTypeId, idCardNumber, phoneNumber
   */
  disableForm(): void {
    this.customerCreateForm.disable();
    this.customerCreateForm.controls.idCardTypeId.enable();
    this.customerCreateForm.controls.idCardNumber.enable();
    this.customerCreateForm.controls.phoneNumber.enable();
  }

  getCustomerDetail(): void {
    if (this.customerInfo.customerId) {
      this.customerService.info(this.customerInfo).subscribe((res: any) => {
        this.customerInfo = res.body;
        const data = res.body || undefined;
        if (data.idCardFiles) {
          data.idCardFiles.forEach((item: any) => {
            const searchFile: IFileEntrySearch = {};
            searchFile.classPk = data.customerId;
            searchFile.fileEntryId = item.id;
            searchFile.normalizeName = item.normalizeName;

            this.customerService
              .getImage(searchFile)
              .subscribe((responsive: any) => {
                CommonUtils.blobToBase64(responsive.body).then((base64) => {
                  this.imageUrls.push({
                    src: base64,
                    name: null,
                    id: item.id,
                    file: null,
                  });
                });
              });
          });

          if (data.idCardFiles.length === 1) {
            this.fileRequired = [];
          }
        }
        this.initForm(data);
      });
    }
  }

  validDate(): void {
    // max date in IssueDate
    const maxDateIssue = new Date();
    this.maxIssueDate = maxDateIssue;
    // min date in IssueDate
    const minDateIssue = new Date();

    if (this.customerCreateForm.get('idCardTypeId')?.value) {
      const idCardType = this.idCardTypes.find(
        (item) =>
          item.idCardTypeId ===
          this.customerCreateForm.get('idCardTypeId')?.value
      );
      if (idCardType?.code === ID_CARD_TYPE.CMND) {
        // set value date minus 5 year
        minDateIssue.setFullYear(
          minDateIssue.getFullYear() - MINUS_YEAR.ISSUE_DATE
        );
        this.minIssueDate = minDateIssue;
      } else {
        minDateIssue.setFullYear(
          minDateIssue.getFullYear() - MINUS_YEAR.ISSUE_DATE_PASSPORT
        );
        this.minIssueDate = minDateIssue;
      }
    } else {
      minDateIssue.setFullYear(
        minDateIssue.getFullYear() - MINUS_YEAR.ISSUE_DATE_PASSPORT
      );
      this.minIssueDate = minDateIssue;
    }
    // set max date of birth
    const date = new Date();
    // set value date minus 15 year
    date.setFullYear(date.getFullYear() - MINUS_YEAR.DAY_OF_BIRTH);
    this.maxDateOfBirth = date;
  }

  dateValidatorMin(value: number): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return CommonUtils.getStartOfDay(value) >=
        new Date(control.value).getTime()
        ? { invalidMinDate: true }
        : null;
    };
  }

  changeValidDateMin() {
    if (this.customerCreateForm.controls.issueDate.value) {
      const minDateIssue = new Date();
      if (this.customerCreateForm.get('idCardTypeId')?.value) {
        const idCardType = this.idCardTypes.find(
          (item) =>
            item.idCardTypeId ===
            this.customerCreateForm.get('idCardTypeId')?.value
        );
        if (idCardType?.code === ID_CARD_TYPE.CMND) {
          // set value date minus 5 year
          minDateIssue.setFullYear(
            minDateIssue.getFullYear() - MINUS_YEAR.ISSUE_DATE
          );
        } else {
          minDateIssue.setFullYear(
            minDateIssue.getFullYear() - MINUS_YEAR.ISSUE_DATE_PASSPORT
          );
        }
      }
      const issueDate = new Date(
        this.customerCreateForm.controls.issueDate.value
      ).getTime();
      if (issueDate <= minDateIssue.getTime()) {
        this.customerCreateForm.controls.issueDate.setValidators([
          Validators.required,
          this.dateValidatorMin(minDateIssue.getTime()),
          this.isValidMaxDate,
        ]);
        this.customerCreateForm.controls.issueDate.updateValueAndValidity();
      } else {
        this.customerCreateForm.controls.issueDate.clearValidators();
        this.customerCreateForm.controls.issueDate.setValidators([
          Validators.required,
          this.isValidMaxDate,
        ]);
        this.customerCreateForm.controls.issueDate.updateValueAndValidity();
      }
    } else {
      this.customerCreateForm.controls.issueDate.clearValidators();
      this.customerCreateForm.controls.issueDate.setValidators([
        Validators.required,
        this.isValidMaxDate,
      ]);
      this.customerCreateForm.controls.issueDate.updateValueAndValidity();
    }
  }

  dateValidatorMaxDateOfBirth(value: number): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      // return null if there's no errors
      return CommonUtils.getStartOfDay(value) <
        new Date(control.value).getTime()
        ? { invalidMaxDateOfBirth: true }
        : null;
    };
  }

  changeValidDateMaxDateOfBirth() {
    if (this.customerCreateForm.controls.dateOfBirth.value) {
      const date = new Date();
      // set value date minus 15 year
      const maxDdate = date.setFullYear(
        date.getFullYear() - MINUS_YEAR.DAY_OF_BIRTH
      );
      const dateOfBirth = new Date(
        this.customerCreateForm.controls.dateOfBirth.value
      ).getTime();
      if (dateOfBirth > maxDdate) {
        this.customerCreateForm.controls.dateOfBirth.setValidators([
          Validators.required,
          this.dateValidatorMaxDateOfBirth(maxDdate),
        ]);
        this.customerCreateForm.controls.dateOfBirth.updateValueAndValidity();
      } else {
        this.customerCreateForm.controls.dateOfBirth.clearValidators();
        this.customerCreateForm.controls.dateOfBirth.setValidators([
          Validators.required,
        ]);
        this.customerCreateForm.controls.dateOfBirth.updateValueAndValidity();
      }
    } else {
      this.customerCreateForm.controls.dateOfBirth.clearValidators();
      this.customerCreateForm.controls.dateOfBirth.setValidators([
        Validators.required,
      ]);
      this.customerCreateForm.controls.dateOfBirth.updateValueAndValidity();
    }
  }

  /**
   * research profile customer
   */
  identityVerify(): void {
    if (
      this.customerCreateForm.controls.idCardTypeId.invalid ||
      this.customerCreateForm.controls.idCardNumber.invalid ||
      this.customerCreateForm.controls.phoneNumber.invalid
    ) {
      CommonUtils.markFormGroupTouched(this.customerCreateForm);
    } else {
      this.validDate();
      if (this.isUpdateScreen) {
        this.identityVerifyUpdate();
      } else {
        this.identityVerifyCreate();
      }
    }
  }

  private identityVerifyCreate() {
    // get value from form control
    const body = this.customerCreateForm.getRawValue();
    const params = {
      idCardTypeId: +body.idCardTypeId,
      idCardNumber: body.idCardNumber,
      phoneNumber: body.phoneNumber,
    };
    this.customerService.identityVerifyCreate(params).subscribe((res) => {
      const data = res.body as ICustomer;
      if (data.customerDraftId) {
        if (res.body) {
          this.initForm(res.body as ICustomer);
        }
        this.customerCreateForm.controls.idCardTypeId.setValue(
          +body.idCardTypeId
        );
        this.customerCreateForm.controls.idCardNumber.setValue(
          body.idCardNumber
        );
        this.customerCreateForm.controls.phoneNumber.setValue(body.phoneNumber);
        this.customerCreateForm.controls.idCardTypeId.updateValueAndValidity();
        this.customerCreateForm.controls.idCardNumber.updateValueAndValidity();
        this.customerCreateForm.controls.phoneNumber.updateValueAndValidity();

        // this.customerCreateForm.updateValueAndValidity();

        this.disabledFile = false;

        // 4.2 Account customer does not exist => return null

        // 4.3 Account customer exist => throw exception

        // 4.4 PhoneNumber exited, GTTT false -> throw exception

        // 4.5 GTTT exited, PhoneNumber false -> throw exception
        this.checkConfirmSearch = true;
        this.accountExistedT24 = true;

        if (data.hasAccountNumber) {
          this.hasAccountNumber = true;
        }
        this.clearValidateT24Existed();
      } else {
        // enable form and disabled 3 colum idCardTypeId, idCardNumber and phoneNumber
        this.customerCreateForm.enable();
        // this.getCustomerSectorId();
        this.customerCreateForm.controls.idCardTypeId.disable();
        this.customerCreateForm.controls.idCardNumber.disable();
        this.customerCreateForm.controls.phoneNumber.disable();
        this.customerCreateForm.controls.cif.disable();
        // this.customerCreateForm.controls.premiumAccountNumber.disable();
        this.customerCreateForm.controls.price.disable();
        // this.customerCreateForm.controls.customerSectorId.setValue(
        //   data.customerSectorId
        // );
        // alert message
        this.toastService.success(
          'customerRegisterManagement.customerCreate.alertDataNotExisted'
        );
        // open upload file
        this.disabledFile = false;
        // close button search and open research again
        this.checkConfirmSearch = true;
        this.accountExistedT24 = false;
        this.hasAccountNumber = false;
        this.resetValidate();
      }
    });
  }

  private identityVerifyUpdate() {
    // get value from form control
    const body = this.customerCreateForm.getRawValue();
    const params = {
      idCardTypeId: +body.idCardTypeId,
      idCardNumber: body.idCardNumber,
      phoneNumber: body.phoneNumber,
      customerId: body.customerId,
    };
    this.customerService.identityVerifyUpdate(params).subscribe((res) => {
      const data = res.body as ICustomer;
      data.customerId = body.customerId;
      if (data.customerDraftId) {
        if (data) {
          this.initForm(data as ICustomer);
        }
        this.customerCreateForm.controls.idCardTypeId.setValue(
          +body.idCardTypeId
        );
        this.customerCreateForm.controls.idCardNumber.setValue(
          body.idCardNumber
        );
        this.customerCreateForm.controls.phoneNumber.setValue(body.phoneNumber);
        this.customerCreateForm.controls.idCardTypeId.updateValueAndValidity();
        this.customerCreateForm.controls.idCardNumber.updateValueAndValidity();
        this.customerCreateForm.controls.phoneNumber.updateValueAndValidity();

        // this.customerCreateForm.updateValueAndValidity();

        this.disabledFile = false;

        // 4.2 Account customer does not exist => return null

        // 4.3 Account customer exist => throw exception

        // 4.4 PhoneNumber exited, GTTT false -> throw exception

        // 4.5 GTTT exited, PhoneNumber false -> throw exception
        this.checkConfirmSearch = true;
        this.accountExistedT24 = true;
      } else {
        // enable form and disabled 3 colum idCardTypeId, idCardNumber and phoneNumber
        this.customerCreateForm.enable();
        this.customerCreateForm.controls.idCardTypeId.disable();
        this.customerCreateForm.controls.idCardNumber.disable();
        this.customerCreateForm.controls.phoneNumber.disable();
        // this.getCustomerSectorId();
        this.customerCreateForm.controls.cif.disable();
        // this.customerCreateForm.controls.customerSectorId.setValue(
        //   data.customerSectorId
        // );
        // alert message
        this.toastService.success(
          'customerRegisterManagement.customerCreate.alertDataNotExisted'
        );
        // open upload file
        this.disabledFile = false;
        // close button search and open research again
        this.checkConfirmSearch = true;
        this.accountExistedT24 = false;
      }
    });
  }

  getInnitDetailForm() {}

  /**
   * button search again: open modal confirm and enable 3 colum idCardTypeId, idCardNumber, phoneNumber
   */
  confirmSearch(): void {
    const modalData = {
      title: 'customerRegisterManagement.customerCreate.researchConfirmTitle',
      content:
        'customerRegisterManagement.customerCreate.researchConfirmContent',
    };
    this.modalService.confirm(modalData).then((result) => {
      if (result === MODAL_ACTION.CONFIRM.code) {
        // close button search again and open button search
        this.checkConfirmSearch = false;
        // this.customerCreateForm.reset();
        // enable form and disabled 3 colum idCardTypeId, idCardNumber and phoneNumber
        this.customerCreateForm.disable();
        this.customerCreateForm.controls.idCardTypeId.enable();
        this.customerCreateForm.controls.idCardNumber.enable();
        this.customerCreateForm.controls.phoneNumber.enable();
        if (!this.isUpdateScreen) {
          this.customerCreateForm.controls.phoneNumber.enable();
        }
        this.disabledFile = true;
      }
    });
  }

  /**
   * get all IdCardType
   */
  getIdCardType(): void {
    this.idCardTypeService.getAll().subscribe((res: any) => {
      this.idCardTypes = res.body;
    });
  }

  /**
   * get all IdCardType
   */
  getNation(): void {
    this.areaService.getAllNation().subscribe((res: any) => {
      this.nations = res.body;
    });
  }

  /**
   * gen picture
   */
  initFileReader() {
    this.reader.onload = (ev: ProgressEvent<FileReader>) => {
      const result: any | null = ev.target?.result;

      if (result) {
        this.eKycPicUrls.push(result as string);
      } else {
        this.toastService.error('');
      }
    };
  }

  /**
   * Create: check validate file and data input if valid then call api create
   */
  onSenApproval() {
    if (this.routerAction === ROUTER_ACTIONS.update) {
      this.onUpdateCustomer();
    } else {
      this.onCreateCustomer();
    }
  }

  /**
   * onUpdateCustomer
   */
  private onUpdateCustomer() {
    this.validateFileLength(this.imageUrls);

    if (this.customerCreateForm.invalid) {
      CommonUtils.markFormGroupTouched(this.customerCreateForm);
    }

    const data = this.customerCreateForm.getRawValue();
    data.customerId = this.customerInfo?.customerId;
    data.idCardFileDelete = this.fileDelete
      .filter((idFile) => !!idFile)
      .map((id) => id);
    // data.customerSectorId = null;

    data.files = this.fileUploads
      .filter((fileX) => !!fileX?.file)
      .map((fileImage) => fileImage.file);

    if (this.customerCreateForm.valid) {
      this.customerService
        .update(data, data.idCardFileDelete)
        .subscribe((res): void => {
          this.router.navigate([
            ROUTER_UTILS.customer.root,
            ROUTER_UTILS.customer.registration.root,
          ]);
          this.toastService.success('common.action.sendApprovalSuccess');
        });
      // if (this.accountExistedT24) {
      //   this.customerService
      //     .confirmUpdateAccount(data, data.idCardFileDelete)
      //     .subscribe((res): void => {
      //       this.router.navigate([
      //         ROUTER_UTILS.customer.root,
      //         ROUTER_UTILS.customer.registration.root,
      //       ]);
      //       this.toastService.success('common.action.sendApprovalSuccess');
      //     });
      // } else {
      //   this.customerService
      //     .update(data, data.idCardFileDelete)
      //     .subscribe((res): void => {
      //       this.router.navigate([
      //         ROUTER_UTILS.customer.root,
      //         ROUTER_UTILS.customer.registration.root,
      //       ]);
      //       this.toastService.success('common.action.sendApprovalSuccess');
      //     });
      // }
    }
  }

  /**
   * onCreateCustomer
   */
  private onCreateCustomer() {
    // check file, if is empty then set required file
    if (this.fileUploads.length === 0) {
      this.customerCreateForm.controls.files.setValidators(Validators.required);
      this.customerCreateForm.controls.files.updateValueAndValidity();
    }
    this.validateFileLength(this.fileUploads);

    if (this.accountExistedT24) {
      this.clearValidateT24Existed();
    }

    // touched form
    if (this.customerCreateForm.invalid) {
      CommonUtils.markFormGroupTouched(this.customerCreateForm);
    }
    const data = this.customerCreateForm.getRawValue();
    //  set data files = map fileUploads
    data.files = this.fileUploads.map((fileX) => fileX.file);

    if (this.customerCreateForm.valid) {
      if (this.accountExistedT24) {
        data.customerId = data.customerDraftId;
        // truong hop khach hang co thong Tin tren T24 nhung chua co TKSD
        if (!this.hasAccountNumber) {
          data.hasAccountNumber = false;
        }
        this.customerService
          .confirmCreateAccount(data)
          .subscribe((res): void => {
            this.router.navigate([
              ROUTER_UTILS.customer.root,
              ROUTER_UTILS.customer.registration.root,
            ]);
            this.toastService.success('common.action.sendApprovalSuccess');
          });
      } else {
        this.customerService.create(data).subscribe((res): void => {
          this.router.navigate([
            ROUTER_UTILS.customer.root,
            ROUTER_UTILS.customer.registration.root,
          ]);
          this.toastService.success('common.action.sendApprovalSuccess');
        });
      }
    }
  }

  clearValidateT24Existed(): void {
    this.customerCreateForm
      .get('fullname')
      ?.setValidators([
        Validators.pattern(VALIDATORS.PATTERN.FULLNAME),
        Validators.minLength(VALIDATORS.LENGTH.FULL_NAME_MIN_LENGTH),
        Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
      ]);
    this.customerCreateForm.get('gender')?.setValidators(null);
    this.customerCreateForm.get('issueDate')?.setValidators(null);
    this.customerCreateForm
      .get('issuePlace')
      ?.setValidators([
        Validators.minLength(VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH),
        Validators.maxLength(VALIDATORS.LENGTH.SMALL_TEXT_MAX_LENGTH),
      ]);
    this.customerCreateForm.get('nationCode')?.setValidators(null);
    this.customerCreateForm
      .get('placeOfResidence')
      ?.setValidators([
        Validators.minLength(VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH),
        Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
      ]);
    this.customerCreateForm
      .get('currentAddress')
      ?.setValidators([
        Validators.minLength(VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH),
        Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
      ]);
    this.customerCreateForm
      .get('phoneContact')
      ?.setValidators([
        Validators.pattern(VALIDATORS.PATTERN.PHONE),
        Validators.minLength(VALIDATORS.LENGTH.PHONE_NUMBER_MIN_LENGTH),
        Validators.maxLength(VALIDATORS.LENGTH.PHONE_NUMBER_MAX_LENGTH),
      ]);
    this.customerCreateForm.get('dateOfBirth')?.setValidators(null);
    this.customerCreateForm
      .get('placeOfOrigin')
      ?.setValidators([
        Validators.minLength(VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH),
        Validators.maxLength(VALIDATORS.LENGTH.PLACE_OF_ORIGIN_MAX_LENGTH),
      ]);
    this.customerCreateForm.get('files')?.setValidators(null);

    this.customerCreateForm.get('fullname')?.updateValueAndValidity();
    this.customerCreateForm.get('gender')?.updateValueAndValidity();
    this.customerCreateForm.get('issueDate')?.updateValueAndValidity();
    this.customerCreateForm.get('issuePlace')?.updateValueAndValidity();
    this.customerCreateForm.get('nationCode')?.updateValueAndValidity();
    this.customerCreateForm.get('placeOfResidence')?.updateValueAndValidity();
    this.customerCreateForm.get('currentAddress')?.updateValueAndValidity();
    this.customerCreateForm.get('phoneContact')?.updateValueAndValidity();
    this.customerCreateForm.get('dateOfBirth')?.updateValueAndValidity();
    this.customerCreateForm.get('placeOfOrigin')?.updateValueAndValidity();
    this.customerCreateForm.get('files')?.updateValueAndValidity();
  }

  resetValidate(): void {
    this.customerCreateForm
      .get('fullname')
      ?.setValidators([
        Validators.required,
        Validators.pattern(VALIDATORS.PATTERN.FULLNAME),
        Validators.minLength(VALIDATORS.LENGTH.FULL_NAME_MIN_LENGTH),
        Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
      ]);
    this.customerCreateForm.get('gender')?.setValidators([Validators.required]);
    this.customerCreateForm
      .get('issueDate')
      ?.setValidators([Validators.required, this.isValidMaxDate]);
    this.customerCreateForm
      .get('issuePlace')
      ?.setValidators([
        Validators.required,
        Validators.minLength(VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH),
        Validators.maxLength(VALIDATORS.LENGTH.SMALL_TEXT_MAX_LENGTH),
      ]);
    this.customerCreateForm
      .get('nationCode')
      ?.setValidators([
        Validators.required,
        Validators.minLength(VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH),
        Validators.maxLength(VALIDATORS.LENGTH.SMALL_TEXT_MAX_LENGTH),
      ]);
    this.customerCreateForm
      .get('placeOfResidence')
      ?.setValidators([
        Validators.required,
        Validators.minLength(VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH),
        Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
      ]);
    this.customerCreateForm
      .get('currentAddress')
      ?.setValidators([
        Validators.minLength(VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH),
        Validators.maxLength(VALIDATORS.LENGTH.MINI_TEXT_MAX_LENGTH),
      ]);
    this.customerCreateForm
      .get('phoneContact')
      ?.setValidators([
        Validators.pattern(VALIDATORS.PATTERN.PHONE),
        Validators.minLength(VALIDATORS.LENGTH.PHONE_NUMBER_MIN_LENGTH),
        Validators.maxLength(VALIDATORS.LENGTH.PHONE_NUMBER_MAX_LENGTH),
      ]);
    this.customerCreateForm
      .get('dateOfBirth')
      ?.setValidators([Validators.required]);
    this.customerCreateForm
      .get('placeOfOrigin')
      ?.setValidators([
        Validators.required,
        Validators.minLength(VALIDATORS.LENGTH.ADDRESS_MIN_LENGTH),
        Validators.maxLength(VALIDATORS.LENGTH.ADDRESS_MAX_LENGTH),
      ]);
    this.customerCreateForm.get('files')?.setValidators([Validators.required]);

    this.customerCreateForm.get('fullname')?.updateValueAndValidity();
    this.customerCreateForm.get('gender')?.updateValueAndValidity();
    this.customerCreateForm.get('issueDate')?.updateValueAndValidity();
    this.customerCreateForm.get('issuePlace')?.updateValueAndValidity();
    this.customerCreateForm.get('nationCode')?.updateValueAndValidity();
    this.customerCreateForm.get('placeOfResidence')?.updateValueAndValidity();
    this.customerCreateForm.get('currentAddress')?.updateValueAndValidity();
    this.customerCreateForm.get('phoneContact')?.updateValueAndValidity();
    this.customerCreateForm.get('dateOfBirth')?.updateValueAndValidity();
    this.customerCreateForm.get('placeOfOrigin')?.updateValueAndValidity();
    this.customerCreateForm.get('files')?.updateValueAndValidity();
  }

  /**
   * onRemoveImage
   *
   * @param image any
   */
  onRemoveImage(image: any): void {
    this.fileDelete.push(image.id);
  }

  /**
   * Reset value form
   */
  onReset() {
    this.customerCreateForm.reset();
  }

  /**
   * back to list form registration
   */
  onBackToList(): void {
    this.router.navigate([
      ROUTER_UTILS.customer.root,
      ROUTER_UTILS.customer.registration.root,
    ]);
    this.hasFilter = true;
  }

  /**
   * Upload picture
   * set file upload and check validate file
   *
   * @param fileSelected any
   */
  onUploadPics(fileSelected: any[]): void {
    this.fileUploads = fileSelected;

    if (this.isUpdateScreen) {
      fileSelected?.forEach((fileEntry) => {
        if (fileEntry.file) {
          this.onRemoveImage(fileEntry);
        }
      });
    }
    // check file, if file have 1 then required file and check have 2 file
    this.validateFileLength(this.fileUploads);
  }

  /**
   * Remove picture
   *
   * @param index number
   */
  onRemovePic(index: number) {
    if (
      index < this.eKycPics.length &&
      this.eKycPicUrls.length === this.eKycPics.length
    ) {
      this.eKycPics.splice(index, 1);
      this.eKycPicUrls.splice(index, 1);
    }
  }

  isValidMaxDate = (control: FormControl) => {
    const dateOfBirth = new Date(control.value).getTime();
    const maxDateOfBirth = new Date().getTime();
    return dateOfBirth > maxDateOfBirth ? { invalidMaxDate: true } : null;
  };

  /**
   * check validate file
   *
   * @param files any[]
   */
  private validateFileLength(files: any[] = []): void {
    // check file, if file have 1 then required file and check have 2 file clear required
    // const srcFile =
    //   files.map((file: any) => file.src).filter((src: any) => src != null) ||
    //   [];
    const srcFile = files
      .filter((file) => !!file?.file || !!file?.id)
      .map((fileMap: any) => fileMap.src);
    if (srcFile?.length < QUANTITY_FILE_ID_CARD_CUSTOMER_UPLOAD) {
      this.customerCreateForm.controls.files.setValidators(Validators.required);
      this.customerCreateForm.controls.files.updateValueAndValidity();
    } else {
      this.customerCreateForm.controls.files.setErrors(null);
      this.customerCreateForm.controls.files.clearValidators();
      this.customerCreateForm.controls.files.updateValueAndValidity();
    }
  }

  displayContent(content: string): string {
    return this.translate.instant(content);
  }

  displayDanger(): boolean {
    if (
      !this.accountExistedT24 ||
      this.routerAction === ROUTER_ACTIONS.update ||
      this.routerAction === ROUTER_ACTIONS.detail
    ) {
      return true;
    }
    return false;
  }

  displayPremiumAccount(): boolean {
    if (
      (this.routerAction !== ROUTER_ACTIONS.update &&
        !this.accountExistedT24) ||
      (this.accountExistedT24 && !this.hasAccountNumber)
    ) {
      return true;
    }
    return false;
  }
}
