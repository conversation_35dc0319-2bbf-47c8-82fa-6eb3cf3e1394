<section class="box-content">
  <div class="container-fluid">
    <div class="col-md-12 back-container">
      <img src="assets/dist/img/icon/back.png" (click)="backToList()" />
      <h5>
        {{
          action === ROUTER_ACTIONS.detail
            ? ("model.campaign.detail" | translate)
            : ("model.campaign.update" | translate)
        }}
      </h5>
    </div>
    <!-- <form [formGroup]="form" *ngIf="customer?.customerId"></form> -->
    <form [formGroup]="formCampaign">
      <div class="col-md-12">
        <div class="row">
          <h2 class="title-create">
            {{ "sidebar.campaign.detail" | translate }}
          </h2>
        </div>
        <div class="row border-create">
          <h3>{{ "sidebar.campaign.detail" | translate }}</h3>
          <div class="col-12">
            <div class="col-md-12">
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label
                      >{{ "model.campaign.name" | translate
                      }}<span class="text-danger">*</span></label
                    >
                    <input
                      formControlName="campaignName"
                      type="text"
                      class="w-100"
                      class="form-control"
                      [maxLength]="VALIDATORS.LENGTH.CAMPAIGN_NAME_MAX_LENGTH"
                      placeholder="{{ 'model.campaign.name' | placeholder }}"
                      trim
                    />
                    <small
                      class="text-danger"
                      *ngIf="
                        formCampaign.get('campaignName')?.errors?.required &&
                        formCampaign.get('campaignName')?.touched
                      "
                    >
                      {{
                        "error.campaign.required.campaignName" | translate
                      }}</small
                    >
                    <small
                      class="text-danger"
                      *ngIf="
                        formCampaign.get('campaignName')?.errors?.pattern &&
                        formCampaign.get('campaignName')?.touched
                      "
                    >
                      {{
                        "error.campaign.pattern.campaignName" | translate
                      }}</small
                    >
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label
                      >{{ "model.campaign.startDateFrom" | translate }}
                      <span class="text-danger">*</span></label
                    >
                    <mat-form-field appearance="fill" class="date-picker">
                      <input
                        matInput
                        [matDatepicker]="picker"
                        formControlName="startDate"
                        [placeholder]="DATE_CONSTANT.DDMMYYYY_SLASH_BIG"
                        dateTransform
                        [min]="maxDate"
                        max="{{
                          formCampaign.controls['endDate'].value
                            | date : DATE_CONSTANT.YYYYMMDD_HYPHEN_SMALL
                        }}"
                      />
                      <mat-datepicker-toggle
                        matSuffix
                        [for]="picker"
                      ></mat-datepicker-toggle>
                      <mat-datepicker #picker></mat-datepicker>
                    </mat-form-field>
                    <small
                      class="text-danger"
                      *ngIf="
                        formCampaign.get('startDate')?.errors?.required &&
                        formCampaign.get('startDate')?.touched
                      "
                    >
                      {{
                        "error.campaign.required.startDateFrom" | translate
                      }}</small
                    >
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="form-group">
                    <label
                      >{{ "model.campaign.endDateTo" | translate }}
                      <span class="text-danger">*</span></label
                    >
                    <mat-form-field appearance="fill" class="date-picker">
                      <input
                        matInput
                        [matDatepicker]="picker1"
                        formControlName="endDate"
                        [placeholder]="DATE_CONSTANT.DDMMYYYY_SLASH_BIG"
                        dateTransform
                        [min]="maxDate"
                        min="{{
                          startDateMin
                            | date : DATE_CONSTANT.YYYYMMDD_HYPHEN_SMALL
                        }}"
                      />
                      <mat-datepicker-toggle
                        matSuffix
                        [for]="picker1"
                      ></mat-datepicker-toggle>
                      <mat-datepicker #picker1></mat-datepicker>
                    </mat-form-field>
                    <small
                      class="text-danger"
                      *ngIf="
                        formCampaign.get('endDate')?.errors?.required &&
                        formCampaign.get('endDate')?.touched
                      "
                      >{{
                        "error.campaign.required.endDateTo" | translate
                      }}</small
                    >
                  </div>
                </div>
              </div>

              <label
                >{{ "model.campaign.showScreen" | translate }}
                <span class="text-danger">*</span></label
              >
              <div class="row">
                <div class="col-md-6 row">
                  <div class="col-md-4">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="position"
                        [id]="SCREEN_SHOW_BANNER.LOGIN.label"
                        [value]="SCREEN_SHOW_BANNER.LOGIN.label"
                        [checked]="
                          formCampaign.get('position')?.value ===
                          SCREEN_SHOW_BANNER.LOGIN.label
                        "
                        [disabled]="isDisabled"
                      />
                      <label class="form-check-label" for="position">
                        {{ "model.campaign.loginScreen" | translate }}
                      </label>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="position"
                        [id]="SCREEN_SHOW_BANNER.HOMEPAGE.label"
                        [value]="SCREEN_SHOW_BANNER.HOMEPAGE.label"
                        [checked]="
                          formCampaign.get('position')?.value ===
                          SCREEN_SHOW_BANNER.HOMEPAGE.label
                        "
                        [disabled]="isDisabled"
                      />
                      <label
                        class="form-check-label"
                        [for]="SCREEN_SHOW_BANNER.HOMEPAGE.label"
                      >
                        {{ "model.campaign.homepageScreen" | translate }}
                      </label>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="position"
                        [id]="SCREEN_SHOW_BANNER.OTHER.label"
                        [value]="SCREEN_SHOW_BANNER.OTHER.label"
                        [checked]="
                          formCampaign.get('position')?.value ===
                          SCREEN_SHOW_BANNER.OTHER.label
                        "
                        [disabled]="isDisabled"
                      />
                      <label
                        class="form-check-label"
                        [for]="SCREEN_SHOW_BANNER.OTHER.label"
                      >
                        {{ "model.campaign.other" | translate }}
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row mt-3">
                <div class="col-md-12">
                  <label>{{ "model.campaign.description" | translate }}</label>
                  <textarea
                    class="w-100 form-control"
                    rows="6"
                    formControlName="description"
                    placeholder="{{
                      'model.campaign.description' | placeholder
                    }}"
                    [maxLength]="
                      VALIDATORS.LENGTH.CAMPAIGN_DESCRIPTION_MAX_LENGTH
                    "
                  ></textarea>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-12 mt-3">
            <span class="title-banner">{{
              "model.campaign.banners" | translate
            }}</span
            ><br />
            <ng-container
              *ngIf="campaign.position === SCREEN_SHOW_BANNER.LOGIN.label"
            >
              <span>{{
                "model.campaign.textBannersLogin" | translate
              }}</span></ng-container
            >
            <ng-container
              *ngIf="campaign.position === SCREEN_SHOW_BANNER.HOMEPAGE.label"
            >
              <span>{{
                "model.campaign.textBannersHomepage" | translate
              }}</span></ng-container
            >
            <ng-container
              *ngIf="campaign.position === SCREEN_SHOW_BANNER.OTHER.label"
            >
              <span>{{
                "model.campaign.textBannersOther" | translate
              }}</span></ng-container
            >
            <div class="row mt-3">
              <!-- <div class="col-md-12 mb-5" *ngIf="isHasCampaign">
                <app-page-loader-image></app-page-loader-image>
              </div> -->
              <div class="col-md-12" *ngIf="isLoadBannerList">
                <app-upload-multi-images
                  (imageUploaded)="onUploadPics($event)"
                  (imageRemoved)="onRemovePics($event)"
                  (imageRemain)="onPicsRemain($event)"
                  (addImage)="onAddImage($event)"
                  [fileExtension]="FILE_UPLOAD_EXTENSIONS.BANNER"
                  [bannerList]="bannerList"
                  [action]="action"
                  [position]="position"
                ></app-upload-multi-images>
              </div>
            </div>
          </div>
        </div>

        <div class="d-block text-center mb-5 mt-4">
          <button
            class="btn btn-white mr-2"
            data-toggle="modal"
            (click)="backToList()"
            type="button"
          >
            {{ "common.action.back" | translate }}
          </button>
          <ng-container *ngIf="action === ROUTER_ACTIONS.update">
            <button
              [disabled]="!isLoadBannerList"
              class="btn btn-red mr-2"
              data-toggle="modal"
              (click)="onUpdate()"
              type="button"
              *hasPrivileges="SYSTEM_RULES.CAMPAIGN_WRITE"
            >
              {{ "common.action.update" | translate }}
            </button>
          </ng-container>
        </div>
      </div>
    </form>
  </div>
</section>
