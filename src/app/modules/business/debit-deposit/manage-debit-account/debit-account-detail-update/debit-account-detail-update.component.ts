import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ENTITY_STATUS_CONST } from '@shared/constants/app.constants';
import { SYSTEM_RULES } from '@shared/constants/authority.constants';
import { VALIDATORS } from '@shared/constants/validators.constant';
import { IDebitAccount } from '@shared/models/debit-account.model';
import { MODEL_MAP_ITEM_COMMON } from '@shared/models/model.type';
import { DebitAccountService } from '@shared/services/debit-account.service';
import { ToastrCustomService } from '@shared/services/helpers/toastr-custom.service';
import { ROUTER_ACTIONS } from '@shared/utils';
import CommonUtils from '@shared/utils/common-utils';

@Component({
  selector: 'app-debit-account-detail-update',
  templateUrl: './debit-account-detail-update.component.html',
  styleUrls: ['./debit-account-detail-update.component.scss'],
})
export class DebitAccountDetailUpdateComponent implements OnInit {
  formCreate: FormGroup = new FormGroup({});
  data?: IDebitAccount;
  id = 0;
  action = '';

  SYSTEM_RULES = SYSTEM_RULES;
  ROUTER_ACTIONS = ROUTER_ACTIONS;
  VALIDATORS = VALIDATORS;
  actionConfirm!: MODEL_MAP_ITEM_COMMON;
  ENTITY_STATUS_CONST = ENTITY_STATUS_CONST;

  constructor(
    public activeModal: NgbActiveModal,
    private fb: FormBuilder,
    private paymentService: DebitAccountService,
    private toastService: ToastrCustomService
  ) {}

  ngOnInit(): void {
    if (this.action === ROUTER_ACTIONS.detail) {
      this.getDetail();
      this.initForm(this.data);
      this.formCreate.disable();
    }
    if (this.action === ROUTER_ACTIONS.update) {
      this.getDetail();
      this.initForm(this.data);
      this.formCreate.enable();
    }
  }

  initForm(data?: IDebitAccount) {
    this.formCreate = this.fb.group({
      accountNumber: [
        data?.accountNumber,
        [
          Validators.required,
          Validators.pattern(VALIDATORS.PATTERN.MERCHANT_NUMBER),
          Validators.maxLength(
            VALIDATORS.LENGTH.CASHOUT_ACCOUNT_NUMBER_MAX_LENGTH
          ),
        ],
      ],
      transactionLimit: [
        data?.transactionLimit
          ? CommonUtils.formatWithThousandSeparator(
              data?.transactionLimit?.toString()
            )
          : '0',
        [Validators.required],
      ],
      accountName: [data?.accountName, [Validators.required]],
      cif: [
        data?.cif,
        [Validators.required, Validators.pattern(VALIDATORS.PATTERN.NUMBER)],
      ],
    });
  }

  onUpdate() {
    const body = {
      ...this.formCreate.value,
      transactionLimit: this.formCreate.value.transactionLimit
        ?.split(',')
        .join(''),
    };
    if (this.formCreate.invalid) {
      CommonUtils.markFormGroupTouched(this.formCreate);
      return;
    }
    this.paymentService.update(body, this.id).subscribe((res: any) => {
      this.toastService.success('common.action.updateSuccess');
      this.activeModal.close(this.actionConfirm.code);
    });
  }

  getDetail() {
    if (this.id) {
      this.paymentService.detail(this.id).subscribe((res: any) => {
        const data = res.body as IDebitAccount;
        this.initForm(data);
      });
    }
  }
}
