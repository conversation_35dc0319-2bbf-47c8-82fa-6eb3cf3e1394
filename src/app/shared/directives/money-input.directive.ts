import { Directive, HostListener } from '@angular/core';
import { NgControl } from '@angular/forms';

@Directive({
  selector: '[appMoneyInput]',
})
export class MoneyInputDirective {
  constructor(private ngControl: NgControl) {}

  @HostListener('input', ['$event'])
  onInputChange(event: Event): void {
    const input = event.target as HTMLInputElement;

    // Loại bỏ mọi ký tự không phải số (chặn cả dấu chấm)
    let rawValue = input.value.replace(/[^0-9]/g, '');

    // Format lại có dấu phẩy
    const formatted = this.formatWithCommas(rawValue);

    // Gán lại giá trị cho control (không trigger emit event để tránh loop)
    this.ngControl.control?.setValue(formatted, { emitEvent: false });
  }

  private formatWithCommas(value: string): string {
    if (!value) return '';
    return value.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
}
