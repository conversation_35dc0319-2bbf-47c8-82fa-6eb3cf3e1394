export interface IPosition {
  positionId?: number;
  positionName?: string;
  shortName?: string;
  positionCode?: string;
  description?: string;
  status?: number;
  userCreated?: string;
  userLastModified?: string;
  createdDate?: string;
  lastModifiedDate?: string;
}

export class Position implements IPosition {
  constructor(
    public positionId?: number,
    public positionName?: string,
    public shortName?: string,
    public positionCode?: string,
    public description?: string,
    public status?: number,
    public userCreated?: string,
    public userLastModified?: string,
    public createdDate?: string,
    public lastModifiedDate?: string
  ) {
    this.positionId = positionId;
    this.positionName = positionName;
    this.shortName = shortName;
    this.positionCode = positionCode;
    this.description = description;
    this.status = status;
    this.userCreated = userCreated;
    this.userLastModified = userLastModified;
    this.createdDate = createdDate;
    this.lastModifiedDate = lastModifiedDate;
  }
}
