import { ICustomer } from './customer.model';
import { INumberStructure } from './premium-account-number.modal';
import { IReferral } from './referral.model';
import { SearchWithPagination } from './request/base.request.model';

export interface IPremiumAccNumber {
  premiumAccNumberId?: number;
  premiumAccNumber?: string;
  discount?: number;
  originalPrice?: number;
  totalPrice?: number;
  referralId?: number;
  customerId?: number;
  customer?: ICustomer;
  referral?: IReferral;
  phoneNumber?: string;
  cif?: string;
  fullName?: string;
  rmCode?: string;
  statusStr?: string;
  createdDateStr?: string;
  numberGroup?: number;
  numberStructure?: string;
  price?: number;
  createdDate?: string;
  createdBy?: string;
  lastModifiedBy?: string;
  lastModifiedDate?: string;
  status?: number;
  isSpecial?: boolean;
  referralCode?: string;
  premiumAccountNumberStructure?: INumberStructure;
}

export class PremiumAccNumber implements IPremiumAccNumber {
  constructor(data: IPremiumAccNumber) {
    Object.assign(this, data);
  }
}

export interface IPremiumAccNumberSearch extends SearchWithPagination {
  referralIds?: number[];
  startDate?: string;
  endDate?: string;
  status?: number;
  timeZoneStr?: string;
  premiumAccNumberId?: number;

  expectedString?: string;
  accountLength?: string;
  hideAccountNumber?: string[];
  numberTypes?: number[];
  numberRecord?: number;
  accountLengths?: string[];
}

export interface IQueryPremiumAccNumber {
  premiumAccNumber?: string;
  numberGroup?: string;
  originalPrice?: number;
  totalPrice?: number;
  discount?: number;
  numberStructure?: string;
  numberType?: number;
  numberGroupStr?: string;

  createdDate?: string;
  createdBy?: string;
  lastModifiedBy?: string;
  lastModifiedDate?: string;
  status?: number;
}

export interface IConfirmOtp {
  transactionId?: string;
  customerId?: string;
}
