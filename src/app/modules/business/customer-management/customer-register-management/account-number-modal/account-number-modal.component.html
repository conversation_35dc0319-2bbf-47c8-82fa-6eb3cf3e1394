<div class="modal-content">
  <div class="modal-header" [ngClass]="isHiddenBtnClose ? 'border-0' : ''">
    <h5 class="modal-title">
      {{ "model.customer.title" | translate }}
    </h5>
    <button
      type="button"
      class="close"
      data-dismiss="modal"
      aria-label="Close"
      (click)="activeModal.close(MODAL_ACTION.CLOSE.code)"
    >
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <!-- "customerRegisterManagement.customerApproval.detail.accountNumber"
              | translate -->
  <div class="modal-body">
    <div class="row">
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th class="text-center" scope="col">
                {{ "customerRegisterManagement.no" | translate }}
              </th>
              <th scope="col">
                {{
                  "customerRegisterManagement.customerApproval.detail.accountNumber"
                    | translate
                }}
              </th>
              <th scope="col">
                {{ "customerRegisterManagement.currency" | translate }}
              </th>
              <th scope="col">
                {{
                  "customerRegisterManagement.customerApproval.detail.accountType"
                    | translate
                }}
              </th>
              <th scope="col" class="text-center">
                {{ "model.customer.default" | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let dataItem of accountCustomers; let i = index">
              <td class="text-center">{{ i + 1 }}</td>
              <td>{{ dataItem.accountNumber }}</td>
              <td>{{ dataItem.currency }}</td>
              <td>
                <span>{{
                  ACCOUNT_TYPE_MAP[dataItem.accountType || 0].label | translate
                }}</span>
              </td>
              <td class="text-center" *ngIf="dataItem.default">
                <span class="bi bi-check-lg button"></span>
              </td>
              <td class="text-center" *ngIf="!dataItem.default"></td>
            </tr>
          </tbody>
        </table>
        <div
          class="row d-block text-center m-0"
          *ngIf="accountCustomers?.length === 0"
        >
          <img
            src="/assets/dist/img/icon/empty.svg"
            height="120"
            alt="no_search_result"
          />
          <p class="text-center mb-5">
            {{ "common.no_search_result" | translate }}
          </p>
        </div>
      </div>
    </div>
  </div>
  <div class="text-center pb-4">
    <button
      type="button"
      class="btn btn-secondary mr-3"
      (click)="activeModal.close(MODAL_ACTION.CANCEL.code)"
    >
      {{ "common.action.close" | translate }}
    </button>
  </div>
</div>
